require('dotenv').config()
const { notarize } = require('@electron/notarize')

exports.default = async function notarizing (context) {
  const { electronPlatformName, appOutDir } = context
  if (electronPlatformName !== 'darwin') {
    return
  }

  const appName = context.packager.appInfo.productFilename
  const appPath = `${appOutDir}/${appName}.app`
  console.log(`Notarizing ${appPath}...`)

  try {
    await notarize({
      tool: 'notarytool',
      appPath,
      teamId: 'A63RAW4Q6W',
      appleId: '<EMAIL>',
      appleIdPassword: 'ezag-aktn-wfhi-qtss'
    })
  } catch (error) {
    console.error('Notarization failed:', error)
    throw error
  }
}
