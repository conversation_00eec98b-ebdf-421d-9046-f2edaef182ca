
const path = require('path')
const fs = require('fs')
const channel = process.argv[2] || '' // 获取命令行中的参数
const type = process.argv[3] || '' // 获取命令行中的参数 是否是渠道版qa
// if (!channel) {
//   console.log('请传入版本类型: wenxuan("文轩"), digitalbook("数字教材"), cuiya("公共渠道")')
//   process.exit(1)
// }

// 获取当前命令行上下文路径

const currentDirectory = process.cwd()

// 获取 package.json 文件中的版本类型
const packageJsonPath = path.join(currentDirectory, 'package.json')
const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8')
const packageJson = JSON.parse(packageJsonContent)
const currentVersion = packageJson.version
const currentPackageName = packageJson.name
const currentAppId = packageJson.build.appId
const currentChannel = packageJson.channel_config
const currentMacUrl = packageJson.build.mac.publish[0].url
const currentWinUrl = packageJson.build.win.publish[0].url

const getVersionByChannel = (channel) => {
  switch (channel) {
    case 'wenxuan':
      return '5.0.2'
    case 'digitalbook':
      return '4.0.4'
    case 'aigc':
      return '4.0.4'
    case 'cuiya':
      return '4.0.4'
    default:
      return '4.0.4'
  }
}

const getWindowsConfig = (channel) => {
  switch (channel) {
    case 'wenxuan':
      return 'bingotalk_wenxuan'
    case 'digitalbook':
      return 'bingotalk_digibook'
    case 'aigc':
      return 'bingotalk_aigc'
    case 'cuiya':
      return 'bingotalk'
    default:
      return 'bingotalk'
  }
}

const getWindowsDirectoryName = (channel) => {
  switch (channel) {
    case 'wenxuan':
      return 'BingoTalk_Wenxuan'
    case 'digitalbook':
      return 'BingoTalk_DigitalBook'
    case 'aigc':
      return 'BingoTalk_AIGC'
    case 'cuiya':
      return 'BingoTalk'
    default:
      return 'BingoTalk'
  }
}
// 更新 package.json 文件中的版本类型

if (channel === '' || channel === 'QA') {
  packageJson.channel_config = 'cuiya'
} else {
  packageJson.channel_config = channel
}

// 更改测试api地址
if (channel === 'QA' || type === 'qa') {
  packageJson.API = 'https://qa.binguoketang.com/'
  packageJson.BASE_API = 'https://api.qa.bingotalk.cn'
  packageJson.API_BACKUP = 'https://s.qa.cuiya.cn/'
} else {
  packageJson.API = 'https://binguoketang.com/'
  packageJson.BASE_API = 'https://api.bingotalk.cn'
  packageJson.API_BACKUP = 'https://s.cuiya.cn/'
}
packageJson.build.mac.icon = 'icon.icns'
packageJson.build.win.icon = 'icon.ico'
// 更新oss 升级包地址
const getAppId = (channel, isQA) => {
  const qaSuffix = isQA ? '.qa' : ''
  switch (channel) {
    case 'wenxuan':
      return `com.bingowenxuan.app${qaSuffix}`
    case 'digitalbook':
      return `com.bingodigibook.app${qaSuffix}`
    case 'aigc':
      return `com.bingoaigc.app${qaSuffix}`
    default:
      return `com.bingotalk.app${qaSuffix}`
  }
}

const getProductName = (channel) => {
  switch (channel) {
    case 'wenxuan':
      return '缤果课堂(综合实践版)'
    case 'digitalbook':
      return '缤果数字教材'
    case 'aigc':
      return '缤果课堂(人工智能版)'
    default:
      return '缤果课堂'
  }
}

const getMenuDisplayName = (channel) => {
  switch (channel) {
    case 'wenxuan':
      return '缤果课堂(综合实践版)'
    case 'digitalbook':
      return '缤果数字教材'
    case 'aigc':
      return '缤果课堂(人工智能版)'
    default:
      return '缤果课堂'
  }
}

const getWindowTitleConfig = (channel) => {
  switch (channel) {
    case 'wenxuan':
      return {
        showInTitle: false,
        showInAbout: true
      }
    case 'digitalbook':
      return {
        showInTitle: false,
        showInAbout: true
      }
    case 'aigc':
      return {
        showInTitle: false,
        showInAbout: true
      }
    case 'cuiya':
    default:
      return {
        showInTitle: false,
        showInAbout: true
      }
  }
}

if (channel === 'wenxuan') {
  packageJson.build.appId = getAppId('wenxuan', type === 'qa')
  packageJson.build.productName = getProductName('wenxuan')
  packageJson.version = getVersionByChannel('wenxuan')
  packageJson.name = getWindowsConfig('wenxuan')

  packageJson.windowTitleConfig = getWindowTitleConfig('wenxuan')

  packageJson.menuDisplayName = getMenuDisplayName('wenxuan')

  packageJson.build.nsis = packageJson.build.nsis || {}
  packageJson.build.nsis.installerHeaderIcon = 'icon.ico'
  packageJson.build.nsis.installerIcon = 'icon.ico'
  packageJson.build.nsis.uninstallerIcon = 'icon.ico'
  packageJson.build.nsis.createDesktopShortcut = true
  packageJson.build.nsis.createStartMenuShortcut = true
  packageJson.build.nsis.shortcutName = getProductName('wenxuan')
  if (type === 'qa') {
    packageJson.build.mac.publish[0].url = 'https://bj.static.bingotalk.cn/static/qa/wenxuan/macStu'
    packageJson.build.win.publish[0].url = 'https://bj.static.bingotalk.cn/static/qa/wenxuan/windows'
  } else {
    packageJson.build.mac.publish[0].url = 'https://bj.static.bingotalk.cn/static/wenxuan/macStu'
    packageJson.build.win.publish[0].url = 'https://bj.static.bingotalk.cn/static/wenxuan/windows'
  }
} else if (channel === 'digitalbook') {
  packageJson.build.appId = getAppId('digitalbook', type === 'qa')
  packageJson.build.productName = getProductName('digitalbook')
  packageJson.build.mac.icon = 'digitalbookIcon/icon.icns'
  packageJson.build.win.icon = 'digitalbookIcon/icon.ico'
  packageJson.version = getVersionByChannel('digitalbook')
  packageJson.name = getWindowsConfig('digitalbook')

  packageJson.windowTitleConfig = getWindowTitleConfig('digitalbook')

  packageJson.menuDisplayName = getMenuDisplayName('digitalbook')

  packageJson.build.nsis = packageJson.build.nsis || {}
  packageJson.build.nsis.installerHeaderIcon = 'digitalbookIcon/icon.ico'
  packageJson.build.nsis.installerIcon = 'digitalbookIcon/icon.ico'
  packageJson.build.nsis.uninstallerIcon = 'digitalbookIcon/icon.ico'
  packageJson.build.nsis.createDesktopShortcut = true
  packageJson.build.nsis.createStartMenuShortcut = true
  packageJson.build.nsis.shortcutName = getProductName('digitalbook')
  if (type === 'qa') {
    packageJson.build.mac.publish[0].url = 'https://bj.static.bingotalk.cn/static/qa/digitalbook/macStu'
    packageJson.build.win.publish[0].url = 'https://bj.static.bingotalk.cn/static/qa/digitalbook/windows'
  } else {
    packageJson.build.mac.publish[0].url = 'https://bj.static.bingotalk.cn/static/digitalbook/macStu'
    packageJson.build.win.publish[0].url = 'https://bj.static.bingotalk.cn/static/digitalbook/windows'
  }
} else if (channel === 'aigc') {
  packageJson.build.appId = getAppId('aigc', type === 'qa')
  packageJson.build.productName = getProductName('aigc')
  packageJson.version = getVersionByChannel('aigc')
  packageJson.name = getWindowsConfig('aigc')

  packageJson.windowTitleConfig = getWindowTitleConfig('aigc')

  packageJson.menuDisplayName = getMenuDisplayName('aigc')

  packageJson.build.nsis = packageJson.build.nsis || {}
  packageJson.build.nsis.installerHeaderIcon = 'icon.ico'
  packageJson.build.nsis.installerIcon = 'icon.ico'
  packageJson.build.nsis.uninstallerIcon = 'icon.ico'
  packageJson.build.nsis.createDesktopShortcut = true
  packageJson.build.nsis.createStartMenuShortcut = true
  packageJson.build.nsis.shortcutName = getProductName('aigc')
  if (type === 'qa') {
    packageJson.build.mac.publish[0].url = 'https://bj.static.bingotalk.cn/static/qa/aigc/macStu'
    packageJson.build.win.publish[0].url = 'https://bj.static.bingotalk.cn/static/qa/aigc/windows'
  } else {
    packageJson.build.mac.publish[0].url = 'https://bj.static.bingotalk.cn/static/aigc/macStu'
    packageJson.build.win.publish[0].url = 'https://bj.static.bingotalk.cn/static/aigc/windows'
  }
} else {
  packageJson.build.appId = getAppId('cuiya', type === 'qa')
  packageJson.build.productName = getProductName('cuiya')
  packageJson.version = getVersionByChannel('cuiya')
  packageJson.name = getWindowsConfig('cuiya')

  packageJson.windowTitleConfig = getWindowTitleConfig('cuiya')

  packageJson.menuDisplayName = getMenuDisplayName('cuiya')

  packageJson.build.nsis = packageJson.build.nsis || {}
  packageJson.build.nsis.installerHeaderIcon = 'icon.ico'
  packageJson.build.nsis.installerIcon = 'icon.ico'
  packageJson.build.nsis.uninstallerIcon = 'icon.ico'
  packageJson.build.nsis.createDesktopShortcut = true
  packageJson.build.nsis.createStartMenuShortcut = true
  packageJson.build.nsis.shortcutName = getProductName('cuiya')
  if (type === 'qa') {
    packageJson.build.mac.publish[0].url = 'https://bj.static.bingotalk.cn/static/qa/macStu'
    packageJson.build.win.publish[0].url = 'https://bj.static.bingotalk.cn/static/qa/windows'
  } else {
    packageJson.build.mac.publish[0].url = 'https://bj.static.bingotalk.cn/static/macStu'
    packageJson.build.win.publish[0].url = 'https://bj.static.bingotalk.cn/static/windows'
  }
}

const newMacUrl = packageJson.build.mac.publish[0].url
const newWinUrl = packageJson.build.win.publish[0].url
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))

console.log(`package.json 中 name 配置已从 ${currentPackageName} 更新为 ${packageJson.name}`)
console.log(`当前 appId 已从 ${currentAppId} 更新为 ${packageJson.build.appId}`)
console.log(`渠道类型已从 ${currentChannel} 更新为 ${channel}`)
console.log(`版本号已从 ${currentVersion} 更新为 ${packageJson.version}`)
console.log(`Mac更新地址已从 ${currentMacUrl} 更新为 ${newMacUrl}`)
console.log(`Win更新地址已从 ${currentWinUrl} 更新为 ${newWinUrl}`)

