# just a flag
ENV = 'development'
NODE_ENV = 'development'

# base api
VUE_APP_PUBLIC_PATH = './'
VUE_APP_BASE_API = 'https://api.qa.bingotalk.cn'
# VUE_APP_BASE_API = 'http://192.168.110.39:8080'
VUE_APP_ADMIN_API = 'https://admin.qa.bingotalk.cn'
VUE_APP_AGORA_WHITE_BOARD_APPID='7MLmUNY4EeqFYGXzZMW71A/v1eTvxtPCMuBKA'
VUE_APP_AGORA_APPID= '2e7850b5ad954593869f87e0fa90add0'
VUE_APP_DOWNLOAD_IMG_URL= 'https://static.bingotalk.cn/courses/covers/620c97658d455.jpeg'
VUE_APP_DOWNLOAD_IMG_SIZE= 352646
VUE_APP_DOWNLOAD_PC_URL= 'https://cuiya.cn/#/download/student'
VUE_APP_DOWNLOAD_WEB_URL= 'https://qa.binguoketang.com/#/classpro/login'
VUE_APP_DOC_URL = 'https://classroom-v2-qa.bingotalk.cn'
VUE_APP_WEB_URL = 'https://qa.binguoketang.com/'
VUE_APP_AI_URL = 'https://qa.cuiya.cn/'
VUE_APP_QINGGUO_URL = 'http://localhost:9521'
VUE_APP_WEB_URL_BACKUP = 'https://s.qa.cuiya.cn/'
VUE_APP_DOWNLOAD_WEB_URL_BACKUP = 'https://s.qa.cuiya.cn/#/classpro/login'
VUE_APP_SCRATCH_URL = 'http://localhost:8601/'
