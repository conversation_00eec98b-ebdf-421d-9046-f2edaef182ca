'use strict'
const path = require('path')
const os = require('os')
function resolve (dir) {
  return path.join(__dirname, dir)
}

// const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV)

const name = '缤果课后服务平台' // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 6666 npm run dev OR npm run dev --port = 6666
const port = process.env.port || process.env.npm_config_port || 8889 // dev port
class InjectStylesInBody {
  apply (compiler) {
    compiler.hooks.compilation.tap('inject-styles-in-body', (compilation) => {
      if (!compilation.hooks.htmlWebpackPluginAlterAssetTags) return
      compilation.hooks.htmlWebpackPluginAlterAssetTags.tap('inject-styles-in-body', function (pluginArgs) {
        const { head, body } = pluginArgs
        head
          .filter(asset => asset.tagName === 'link' && asset.attributes && asset.attributes.rel === 'stylesheet')
          .forEach(asset => {
            head.splice(head.indexOf(asset), 1)
            body.push(asset)
          })
      })
    })
  }
}
// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  transpileDependencies: [
    'pdfjs-dist',
    'screenfull'
  ],
  publicPath: process.env.VUE_APP_PUBLIC_PATH,
  outputDir: 'build',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
        utils: resolve('src/utils'),
        'rootpath': resolve('./'),
        sdk: resolve('src/sdk')
      }
    },
    devtool: 'source-map'
  },
  css: {
    loaderOptions: {
      sass: {
        // 注意：在 sass-loader v7 中，这个选项名是 "data"
        prependData: '@import "@/styles/global.scss";',
        sassOptions: {
          outputStyle: 'expanded',
          unicode: false
        }
      }
    }
  },
  chainWebpack (config) {
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')
    config.module
      .rule('js')
      .use('thread-loader')
      .before('babel-loader')
      .loader('thread-loader')
      .options({ workers: os.cpus().length - 1 })
    config.module
      .rule('js')
      .use('cache-loader')
      .before('babel-loader')
      .loader('cache-loader')
      .end()
    // 处理 markdown 文件
    config.module
      .rule('markdown')
      .test(/\.md$/)
      .use('raw-loader')
      .loader('raw-loader')
      .end()
    // 处理 marked 库，使用 babel 转译
    config.module
      .rule('babel')
      .test(/\.js$/)
      .include
      .add(/node_modules\/marked\//) // 仅处理 marked 库
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .options({
        presets: ['@babel/preset-env']
      })
    // set svg-sprite-loader
    config.module.rule('mjs').test(/\.mjs$/).include.add(resolve('node_modules')).end().type('javascript/auto')
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
            // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .plugin('html')
            .tap(args => {
              args[0].inject = 'body'
              return args
            }).end()
            .plugin('inject-styles-in-body')
            .use(InjectStylesInBody)
            .end()
          // config
          //   .optimization.splitChunks({
          //     // chunks: 'all',
          //     cacheGroups: {
          //       common: {
          //         name: 'chunk-common',
          //         chunks: 'initial',
          //         minChunks: 2,
          //         maxInitialRequests: 5,
          //         minSize: 0,
          //         priority: 1,
          //         reuseExistingChunk: true,
          //         enforce: true
          //       },
          //       vendors: {
          //         name: 'chunk-vendors',
          //         test: /[\\/]node_modules[\\/]/,
          //         chunks: 'initial',
          //         priority: 2,
          //         reuseExistingChunk: true,
          //         enforce: true
          //       },
          //       elementUI: {
          //         name: 'chunk-elementui',
          //         test: /[\\/]node_modules[\\/]element-ui[\\/]/,
          //         chunks: 'all',
          //         priority: 3,
          //         reuseExistingChunk: true,
          //         enforce: true
          //       },
          //       pdfjs: {
          //         name: 'chunk-pdfjs',
          //         test: /[\\/]node_modules[\\/](vue-)?pdfjs-dist[\\/]/,
          //         chunks: 'all',
          //         priority: 4,
          //         reuseExistingChunk: true,
          //         enforce: true
          //       },
          //       whitewebsdk: {
          //         name: 'chunk-white-web-sdk',
          //         test: /[\\/]node_modules[\\/](vue-)?white-web-sdk[\\/]/,
          //         chunks: 'all',
          //         priority: 4,
          //         reuseExistingChunk: true,
          //         enforce: true
          //       },
          //       agorartcsdkng: {
          //         name: 'chunk-agora-rtc-sdk-ng',
          //         test: /[\\/]node_modules[\\/](vue-)?agora-rtc-sdk-ng[\\/]/,
          //         chunks: 'all',
          //         priority: 5,
          //         reuseExistingChunk: true,
          //         enforce: true
          //       }
          //       // libs: {
          //       //   name: 'chunk-libs',
          //       //   test: /[\\/]node_modules[\\/]/,
          //       //   priority: 10,
          //       //   chunks: 'initial' // only package third parties that are initially dependent
          //       // },
          //       // elementUI: {
          //       //   name: 'chunk-elementUI', // split elementUI into a single package
          //       //   priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          //       //   test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
          //       // },
          //       // pdfjsDist: {
          //       //   name: 'chunk-pdfjs', // split white-web-sdk into a single package
          //       //   priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          //       //   test: /[\\/]node_modules[\\/]_?pdfjs-dist(.*)/ // in order to adapt to cnpm
          //       // },
          //       // agoraRtcSdkNg: {
          //       //   name: 'chunk-agoraRtcSdkNg', // split white-web-sdk into a single package
          //       //   priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          //       //   test: /[\\/]node_modules[\\/]_?agora-rtc-sdk-ng(.*)/ // in order to adapt to cnpm
          //       // },
          //       // whiteWebSdk: {
          //       //   name: 'chunk-whiteWebSdk', // split white-web-sdk into a single package
          //       //   priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          //       //   test: /[\\/]node_modules[\\/]_?white-web-sdk(.*)/ // in order to adapt to cnpm
          //       // },
          //       // videojs: {
          //       //   name: 'chunk-videojs', // split white-web-sdk into a single package
          //       //   priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          //       //   test: /[\\/]node_modules[\\/]_?videojs(.*)/ // in order to adapt to cnpm
          //       // },
          //       // commons: {
          //       //   name: 'chunk-commons',
          //       //   test: resolve('src/components'), // can customize your rules
          //       //   minChunks: 3, //  minimum common number
          //       //   priority: 5,
          //       //   reuseExistingChunk: true
          //       // }
          //     }
          //   })
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial' // only package third parties that are initially dependent
                },
                elementUI: {
                  name: 'chunk-elementUI', // split elementUI into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
                },
                agoraRtcSdkNg: {
                  name: 'chunk-agoraRtcSdkNg', // split white-web-sdk into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?agora-rtc-sdk-ng(.*)/ // in order to adapt to cnpm
                },
                pdfjs: {
                  name: 'chunk-pdfjs', // split white-web-sdk into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?pdfjs-dist(.*)/ // in order to adapt to cnpm
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'), // can customize your rules
                  minChunks: 3, //  minimum common number
                  priority: 5,
                  reuseExistingChunk: true
                },
                styles: {
                  name: 'styles',
                  test: /\.css$/,
                  chunks: 'all',
                  enforce: true // 忽略splitChunks的其他配置，强制将匹配到的缓存组中的文件合并为一个styles.css文件
                }
              }
            })
          // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
          config.optimization.runtimeChunk('single')
        }
      )
  }
}
