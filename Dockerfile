# FROM node:12-alpine3.12
# RUN rm -rf /var/cache/apk/* && \
#     rm -rf /tmp/*

# RUN apk update

# RUN apk add nginx && rm -rf /var/cache/apk/*
# COPY default.conf /etc/nginx/conf.d/default.conf
# RUN mkdir -p /var/app
# RUN mkdir -p /run/nginx

# WORKDIR /var/app

# COPY ./build/ /var/app

# # RUN npm install
# # ARG BUILD_ENVIRONMENT
# # RUN npm rebuild node-sass
# # RUN npm run build:$BUILD_ENVIRONMENT
# # nginx site conf
# COPY ./default.conf /etc/nginx/conf.d/


# EXPOSE 80
# EXPOSE 443
# STOPSIGNAL SIGTERM

# CMD ["nginx", "-g", "daemon off;"]

FROM nginx:1.20.2
COPY build/ /usr/share/nginx/html/
COPY default.conf /etc/nginx/conf.d/default.conf