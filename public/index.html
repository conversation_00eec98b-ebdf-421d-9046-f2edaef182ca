<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="target-densitydpi=device-dpi, width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"  /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <meta http-equiv="Cache-Control" content="no-store, must-revalidate">
<!--    <link rel="stylesheet" href="//at.alicdn.com/t/font_3383678_48fzlx7q0do.css">-->
    <link rel="preload" href="//at.alicdn.com/t/font_3383678_48fzlx7q0do.css" onload="this.rel='stylesheet'">
    <script defer src="https://g.alicdn.com/IMM/office-js/1.1.15/aliyun-web-office-sdk.min.js"></script>
    <title>缤果课堂</title>
  </head>
  <style lang="scss" scoped>
  .loading {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0px;
    top: 0px;
    bottom: 0px;
    right: 0px;
    z-index: 9999;
    display: grid;
    background: rgba(202,224,249, 0.4);
    opacity: 1;
    pointer-events:none;
    align-items: center;
  }
.loadingImg{
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

  </style>
  <body>
    <noscript>
      <strong>We're sorry but 缤果课堂 doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div class="loading">
        <img class="loadingImg" src="./loading.gif" alt="">
    </div>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script>
      // window.onload = function () {
      //   document.querySelector(".loading").style.opacity = "0";
      // };
      // 横竖屏切换
      function detectOrient() {
        var width = document.documentElement.clientWidth,
          height = document.documentElement.clientHeight,
          wrapper = document.body,
          style = ''

        if (width >= height) {
          // 竖屏
          style += 'width:100%'
          style += 'height:100%;'
          style += '-webkit-transform: rotate(0); transform: rotate(0);'
          style += '-webkit-transform-origin: 0 0;'
          style += 'transform-origin: 0 0;'
        } else {
          // 横屏
          style += 'width:' + height + 'px;' // 注意旋转后的宽高切换
          style += 'height:' + width + 'px;'
          style += '-webkit-transform: rotate(90deg); transform: rotate(90deg);'
          // 注意旋转中点的处理
          style +=
            '-webkit-transform-origin: ' + width / 2 + 'px ' + width / 2 + 'px;'
          style += 'transform-origin: ' + width / 2 + 'px ' + width / 2 + 'px;'
        }
        wrapper.style.cssText = style
      }
      // window.addEventListener('resize', detectOrient)
      // detectOrient()
    </script>
  </body>
</html>
