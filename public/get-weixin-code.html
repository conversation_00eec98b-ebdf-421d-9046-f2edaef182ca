<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>微信登录</title>
</head>

<body>
  <script>
    var GWC = {
      version: '1.2.0',
      urlParams: {},
      appendParams: function (url, params) {
        if (params) {
          var baseWithSearch = url.split('#')[0];
          var hash = url.split('#')[1];
          for (var key in params) {
            var attrValue = params[key];
            if (attrValue !== undefined) {
              var newParam = key + "=" + attrValue;
              if (baseWithSearch.indexOf('?') > 0) {
                var oldParamReg = new RegExp('^' + key + '=[-%.!~*\'\(\)\\w]*', 'g');
                if (oldParamReg.test(baseWithSearch)) {
                  baseWithSearch = baseWithSearch.replace(oldParamReg, newParam);
                } else {
                  baseWithSearch += "&" + newParam;
                }
              } else {
                baseWithSearch += "?" + newParam;
              }
            }
          }

          if (hash) {
            url = baseWithSearch + '#' + hash;
          } else {
            url = baseWithSearch;
          }
        }
        return url;
      },
      appendParamsRedirect: function (url, params) {
        if (params) {
          let baseWithSearch = url
          for (let key in params) {
            let attrValue = params[key];
            if (attrValue !== undefined) {
              let newParam = key + "=" + attrValue;
              if (baseWithSearch.indexOf('?') > 0) {
                let oldParamReg = new RegExp('^' + key + '=[-%.!~*\'\(\)\\w]*', 'g');
                if (oldParamReg.test(baseWithSearch)) {
                  baseWithSearch = baseWithSearch.replace(oldParamReg, newParam);
                } else {
                  baseWithSearch += "&" + newParam;
                }
              } else {
                baseWithSearch += "?" + newParam;
              }
            }
          }
          url = baseWithSearch;
        }
        return url;
      },
      getUrlParams: function () {
        var pairs = location.search.substring(1).split('&');
        for (var i = 0; i < pairs.length; i++) {
          var pos = pairs[i].indexOf('=');
          if (pos === -1) {
            continue;
          }
          GWC.urlParams[pairs[i].substring(0, pos)] = decodeURIComponent(pairs[i].substring(pos + 1));
        }
      },
      doRedirect: function () {
        var code = GWC.urlParams['code'];
        var appId = GWC.urlParams['appid'];
        var scope = GWC.urlParams['scope'] || 'snsapi_base';
        var state = GWC.urlParams['state'];
        var isMp = GWC.urlParams['isMp']; //isMp为true时使用开放平台作授权登录，false为网页扫码登录
        var baseUrl;
        var redirectUri;

        if (!code) {
          baseUrl = "https://open.weixin.qq.com/connect/oauth2/authorize#wechat_redirect";
          if (scope == 'snsapi_login' && !isMp) {
            baseUrl = "https://open.weixin.qq.com/connect/qrconnect";
          }
          //第一步，没有拿到code，跳转至微信授权页面获取code
          redirectUri = GWC.appendParams(baseUrl, {
            'appid': appId,
            'redirect_uri': encodeURIComponent(location.href),
            'response_type': 'code',
            'scope': scope,
            'state': encodeURIComponent(state),
          });
        } else {
          console.log(GWC.urlParams)
          //第二步，从微信授权页面跳转回来，已经获取到了code，再次跳转到实际所需页面
          redirectUri = GWC.appendParamsRedirect(GWC.urlParams['redirect_uri'], {
            'code': code,
            'state': encodeURIComponent(state)
          });
        }
        location.href = redirectUri;
      }
    };

    GWC.getUrlParams();
    GWC.doRedirect();

  </script>
</body>

</html>
