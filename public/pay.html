<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>支付</title>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
  </head>
  <body>
    <script>
      if (typeof WeixinJSBridge == 'undefined') {
        if (document.addEventListener) {
          document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false)
        } else if (document.attachEvent) {
          document.attachEvent('WeixinJSBridgeReady', onBridgeReady)
          document.attachEvent('onWeixinJSBridgeReady', onBridgeReady)
        }
      } else {
        onBridgeReady()
      }
      function getCurrentPageQueryParams() {
        const params = new URLSearchParams(window.location.search)
        const queryParams = {}
        for (const [key, value] of params.entries()) {
          queryParams[key] = value
        }
        return queryParams
      }
      function onBridgeReady() {
        let data = getCurrentPageQueryParams()
        if (window.WeixinJSBridge) {
          window.WeixinJSBridge.invoke(
            'getBrandWCPayRequest',
            {
              appId: data.appId, // 公众号名称，由商户传入
              timeStamp: data.timeStamp, // 时间戳，自1970年以来的秒数
              nonceStr: data.nonceStr, // 随机串
              package: data.package,
              signType: data.signType, // 微信签名方式：
              paySign: data.paySign
            },
            function (res) {
              if (res.err_msg === 'get_brand_wcpay_request:ok') {
                window.location.href = data.redirect_uri
              } else if (res.err_msg === 'get_brand_wcpay_request:cancel') {
                window.location.href = data.redirect_uri
              } else {
                window.location.href = data.redirect_uri
              }
            }
          )
        } else {
          alert('无WeixinJSBridge')
        }
      }
    </script>
  </body>
</html>
