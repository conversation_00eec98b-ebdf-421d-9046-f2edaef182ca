<!DOCTYPE html>
<html lang="en">
<head>
	<title>three.js webgl - GLTFloader + variants</title>
	<meta charset="utf-8">
<!--	<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">-->
<!--	<link type="text/css" rel="stylesheet" href="main.css">-->
	<meta http-equiv="Cache-Control" content="no-store, must-revalidate">
</head>
<body style="margin: 0px">
<script type="importmap">
			{
				"imports": {
					"three": "https://static.bingotalk.cn/resource/three.module.js"
				}
			}
		</script>
<script type="module">
import * as THREE from 'https://static.bingotalk.cn/resource/three.module.js';
import { OrbitControls } from 'https://static.bingotalk.cn/resource/OrbitControls.js';
import { GLTFLoader } from 'https://static.bingotalk.cn/resource/GLTFLoader.js';
import { RGBELoader } from 'https://static.bingotalk.cn/resource/RGBELoader.js';

import { MeshoptDecoder } from 'https://static.bingotalk.cn/resource/meshopt_decoder.module.js';

	/**
	 1，创建场景 const scene = new THREE.Scene();
	 2，创建相机  const camera = new THREE.PerspectiveCamera( 45, window.innerWidth / window.innerHeight, 0.25, 20 );
	 3，加载环境  const rgbeLoader = new RGBELoader();
	            rgbeLoader.load( 'models/test/CAMERA.hdr',
	 4，加载3d模型
		 const loader = new GLTFLoader();
		loader.setMeshoptDecoder(MeshoptDecoder);
	 	loader.load( 'models/test/CAMERA.gltf',
	 5，添加渲染器
		 const rgbeLoader = new RGBELoader();
		rgbeLoader.load( 'models/test/CAMERA.hdr',
	 6，渲染
		 const renderer = new THREE.WebGLRenderer( { antialias: true } );
		document.body.appendChild(renderer.domElement);
	    把场景和相机添加到渲染器去渲染显示
	 	renderer.render( scene, camera );
	 7，创建控制器,鼠标控制
	   const controls = new OrbitControls( camera, renderer.domElement );
	 */


	// 创建场景
	const scene = new THREE.Scene();
	// // 设置光照，可不设置，使用默认
	// const ambientLight = new THREE.AmbientLight(0xffffff, 0.01); // 环境光
	// scene.add(ambientLight);
	// const directionalLight = new THREE.DirectionalLight(0xffffff, 0.01); // 平行光
	// directionalLight.position.set(1, 1, 1).normalize(); // 设置光源方向
	// scene.add(directionalLight);

	// 创建相机
	const camera = new THREE.PerspectiveCamera( 45, window.innerWidth / window.innerHeight, 0.25, 20 );
	// 设置相机位置
	camera.position.set( 3,3,3);
	// camera.position.set(20, 20, 20);
	// camera.lookAt(scene.position);


	//环境渲染器
	const rgbeLoader = new RGBELoader();
	// rgbeLoader.setPath( 'models/test/' )
	// rgbeLoader.load( 'models/test/CAMERA.hdr', function ( texture ) {
	rgbeLoader.load( 'https://static.bingotalk.cn/resource/CAMERA.hdr', function ( texture ) {
			texture.mapping = THREE.EquirectangularReflectionMapping;
			scene.background = texture;
			scene.environment = texture;
		})

	//gltf加载器
	const loader = new GLTFLoader();
	loader.setMeshoptDecoder(MeshoptDecoder);
	// loader.setPath( 'models/test/' );
	// loader.load( 'models/test/CAMERA.gltf', function ( gltf ) {
	loader.load( 'https://static.bingotalk.cn/resource/CAMERA.gltf', function ( gltf ) {
		gltf.scene.scale.set( 4.0, 4.0, 4.0 );
		// gltf.scene.scale.set( 3.0, 3.0, 3.0 );
		scene.add( gltf.scene );
		//重绘，渲染
		// render();
		renderer.render( scene, camera );
	} );

	// 创建渲染器
	const renderer = new THREE.WebGLRenderer( { antialias: true } );
	renderer.setPixelRatio( window.devicePixelRatio );
	renderer.setSize( window.innerWidth, window.innerHeight );
	renderer.toneMapping = THREE.ACESFilmicToneMapping;
	renderer.toneMappingExposure = 1;
	renderer.outputEncoding = THREE.sRGBEncoding;
	// container.appendChild( renderer.domElement );
	document.body.appendChild(renderer.domElement);
	// renderer.render( scene, camera );

	//创建控制器,鼠标控制
	const controls = new OrbitControls( camera, renderer.domElement );
	// controls.addEventListener( 'change', render ); // use if there is no animation loop
	controls.addEventListener( 'change', function(){
		renderer.render( scene, camera )
	} ); // use if there is no animation loop
	controls.minDistance = 2;
	controls.maxDistance = 10;
	controls.target.set( 0, 0.5, - 0.2 );
	controls.update();

	// window.addEventListener( 'resize', onWindowResize );
	// window.addEventListener( 'resize', function onWindowResize() {
	window.addEventListener( 'resize', function () {
		camera.aspect = window.innerWidth / window.innerHeight;
		camera.updateProjectionMatrix();
		renderer.setSize( window.innerWidth, window.innerHeight );

		//重绘，渲染
		// render();
		renderer.render( scene, camera );
	});


</script>

</body>
</html>
