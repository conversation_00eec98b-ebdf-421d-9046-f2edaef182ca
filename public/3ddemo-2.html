<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
<!--    <title>learn51(导入FBX格式骨骼绑定模型)</title>-->
    <meta http-equiv="Cache-Control" content="no-store, must-revalidate">
    <script src="https://static.bingotalk.cn/bingoprd/resource/three.min.js"></script>
    <script src="https://static.bingotalk.cn/bingoprd/resource/OrbitControls.js"></script>
    <script src="https://static.bingotalk.cn/bingoprd/resource/FBXLoader.js"></script>
    <script src="https://static.bingotalk.cn/bingoprd/resource/fflate.min.js"></script>
    <script src="https://static.bingotalk.cn/bingoprd/resource/stats.min.js"></script>
    <script src="https://static.bingotalk.cn/bingoprd/resource/dat.gui.js"></script>
</head>
<style type="text/css">
    html, body {
        margin: 0;
        height: 100%;
    }
    canvas {
        display: block;
    }
</style>
<body onload="draw()">
</body>
<script>
    var renderer, camera, scene, gui, light, stats, controls, meshHelper, mixer, action
    var clock = new THREE.Clock()
    var initRender = () => {
        renderer = new THREE.WebGLRenderer({antialias: true})
        renderer.setPixelRatio(window.devicePixelRatio)
        renderer.setSize(window.innerWidth, window.innerHeight)
        renderer.setClearColor(0xeeeeee)
        renderer.shadowMap.type = THREE.PCFSoftShadowMap
        renderer.shadowMap.enabled = true
        document.body.appendChild(renderer.domElement)
    }
    var initScene = () => {
        scene = new THREE.Scene()
        scene.background = new THREE.Color( 0xa0a0a0 )
        scene.fog = new THREE.Fog( 0xa0a0a0, 200, 1000 )
    }
    var initCamera = () => {
        camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 2000)
        camera.position.set(100, 200, 300)
    }
    var initGui = () => {
        // gui = {
        //     animation: true,
        //     helper: true
        // }
        // var datGui = new dat.GUI()
        // datGui.add(gui, 'animation').onChange(e => {
        //     if (e) {
        //         action.play()
        //     } else {
        //         action.stop()
        //     }
        // })
        // datGui.add(gui, 'helper').onChange(e => {
        //     meshHelper.visible = e
        // })
    }
    var initLight = () => {
        scene.add(new THREE.AmbientLight(0x444444))
        light = new THREE.DirectionalLight(0xffffff)
        light.position.set(0, 200, 100)
        light.castShadow = true
        light.shadow.camera.top = 180
        light.shadow.camera.bottom = -100
        light.shadow.camera.left = -120
        light.shadow.camera.right = 120
        // light.shadow.camera.near = 20
        // light.shadow.camera.far = 200
        // light.shadow.mapSize.width = 1024
        // light.shadow.mapSize.height = 1024
        light.castShadow = true
        var debug = new THREE.CameraHelper(light.shadow.camera)
        debug.name = 'debug'
        // scene.add(debug)
        scene.add(light)
    }
    var initModel = () => {
        var helper = new THREE.AxesHelper(50)
        scene.add(helper)

        // 地板
        // var mesh = new THREE.Mesh(new THREE.PlaneBufferGeometry(2000, 2000), new THREE.MeshPhongMaterial({
        //   color: 0xffffff,
        //   depthWrite: false
        // }))
        // mesh.rotation.x = Math.PI / 2
        // mesh.position.y = -0
        // mesh.receiveShadow = true
        // scene.add(mesh)
        // 底面
        var planeGeometry = new THREE.PlaneBufferGeometry(2000, 2000)
        // var planeMatarial = new THREE.MeshStandardMaterial({color: 0xaaaaaa})
        var planeMatarial = new THREE.MeshPhongMaterial({
            color: 0xffffff,
            depthWrite: false
        })
        var plane = new THREE.Mesh(planeGeometry, planeMatarial)
        plane.rotation.x = -0.5 * Math.PI
        plane.position.y = -0
        plane.receiveShadow = true
        scene.add(plane)

        // 加载地板割线
        var grid = new THREE.GridHelper(2000, 20, 0x000000, 0x000000)
        grid.material.opacity = 0.2
        grid.material.transparent = true
        scene.add(grid)

        // 加载模型
        var loader = new THREE.FBXLoader()
        // loader.load('data/model/SambaDancing/Samba Dancing.fbx', mesh => {
        // loader.load('https://huohua-component.huohuaschool.com/widget/3a74bc5bd707764f6a3a566bfcd0b783_unzip/media/01.FBX', mesh => {
        loader.load('https://static.bingotalk.cn/bingoprd/resource/01.fbx', mesh => {
        // loader.load('https://huohua-component.huohuaschool.com/widget/3a74bc5bd707764f6a3a566bfcd0b783_unzip/media/03.FBX', mesh => {
            meshHelper = new THREE.SkeletonHelper(mesh)
            scene.add(meshHelper)
            mesh.traverse(function (child) {
                if (child.isMesh) {
                    child.castShadow = true
                    child.receiveShadow = true
                }
            })
            // mesh.castShadow = true
            mixer = mesh.mixer = new THREE.AnimationMixer(mesh)
            // action = mixer.clipAction(mesh.animations[0])
            // action.play()
            scene.add(mesh)
        })
        // 加载模型
        var loader = new THREE.FBXLoader()
        // loader.load('https://huohua-component.huohuaschool.com/widget/3a74bc5bd707764f6a3a566bfcd0b783_unzip/media/02.FBX', mesh => {
        loader.load('https://static.bingotalk.cn/bingoprd/resource/02.fbx', mesh => {
            meshHelper = new THREE.SkeletonHelper(mesh)
            scene.add(meshHelper)
            mesh.traverse(function (child) {
                if (child.isMesh) {
                    child.castShadow = true
                    child.receiveShadow = true
                }
            })
            // mesh.castShadow = true
            mixer = mesh.mixer = new THREE.AnimationMixer(mesh)
            // action = mixer.clipAction(mesh.animations[0])
            // action.play()
            scene.add(mesh)
        })
    }
    var initStats = () => {
        stats = new Stats()
        document.body.appendChild(stats.dom)
        stats.domElement.remove(); // 从 DOM 中移除 FPS 面板完全删除 FPS 监视器，不再占用内存。
        // stats.domElement.style.display = 'none'; // 隐藏 FPS 面板
    }
    var initControls = () => {
        controls = new THREE.OrbitControls(camera, renderer.domElement)
        controls.enableDamping = true
        // controls.target.set(0, 100, 0)
        controls.target.set(0, 10, 0)
        //设置相机距离原点的最远距离
        controls.minDistance = 1
        //设置相机距离原点的最远距离
        controls.maxDistance = 2000
    }
    var render = () => {
        var time = clock.getDelta()
        if (mixer) {
            mixer.update(time)
        }
        controls.update()
    }
    var onWindowResize = () => {
        camera.aspect = window.innerWidth / window.innerHeight
        camera.updateProjectionMatrix()
        renderer.setSize(window.innerWidth, window.innerHeight)
    }
    var animate = () => {
        render()
        stats.update()
        renderer.render(scene, camera)
        requestAnimationFrame(animate)
    }
    var draw = () => {
        // if (!Detector.webgl) Detector.addGetWebGLMessage()
        initGui()
        initRender()
        initScene()
        initCamera()
        initLight()
        initModel()
        initControls()
        initStats()
        animate()
        window.onresize = onWindowResize
    }
</script>
</html>
