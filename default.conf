# server {
#   listen 80;
#   server_name  _;
#   root /var/app/build;

#   location / {
#     index  index.html index.htm;
#   }

#   location = /index.html {
#     add_header Cache-Control  no-store;
#     # add_header Pragma no-cache;
#   }

#   location ~ /\.ht {
#     deny all;
#   }
# }

server {
    listen       80;
    server_name  _;
    root   /usr/share/nginx/html;

    #charset koi8-r;
    access_log  /var/log/nginx/host.access.log  main;
    error_log  /var/log/nginx/error.log  debug;

    location / {
        # index  index.html index.htm;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        if ($request_filename ~* .*\.(js|css|woff|png|jpg|jpeg)$){
            expires    365d;  #js、css、图片缓存100天
            #add_header Cache-Control "max-age = 8640000"; #或者设置max-age
        }
        if ($request_filename ~* .*\.(?:htm|html)$){
            add_header Cache-Control "no-cache, no-store";  #html不缓存
        }
    }

    # JupyterHub 代理配置
    location ^~ /jupyterhub {

        proxy_pass https://classroom-v2-qa.bingotalk.cn;  # 注意：去掉末尾的 `/jupyterhub/`

#         proxy_set_header Host $host;  # 强制覆盖为目标 Host
        proxy_set_header X-Original-Host $host;  # 保留原始 Host 供调试

        # 关键：添加 Ingress 需要的特定头
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 基本代理设置
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Ssl on;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-Server $host;
        proxy_set_header Accept-Encoding "";
        proxy_set_header real-host $host;

        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 移除所有可能的安全头
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        proxy_hide_header Access-Control-Allow-Origin;

        add_header X-Upstream-Addr $upstream_addr;
        add_header X-Upstream-Status $upstream_status;
        add_header X-Request-Time $request_time;

        proxy_buffering off;
        proxy_redirect off;

    }

    # # 显式处理 WebSocket 路径（增强兼容性）
    # location ~* /jupyterhub/user/[^/]+/(terminals/websocket|api/events|api/kernels) {
    #     proxy_pass http://jupyterhub:8000;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "upgrade";
    # }

    location ~ /\.ht {
        deny all;
    }

    # Managing requests to verify letsencrypt host
    location ~ /.well-known {
        allow all;
    }
}