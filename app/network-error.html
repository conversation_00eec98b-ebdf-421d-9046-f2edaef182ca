<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
            background: rgba(202, 224, 249, 0.4);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #324157;
            margin: 0;
            padding: 0;
        }

        .error-container {
            background: white;
            border-radius: 12px;
            padding: 50px 40px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(50, 65, 87, 0.1);
            max-width: 520px;
            width: 90%;
            animation: fadeInUp 0.6s ease-out;
            border: 1px solid rgba(50, 65, 87, 0.08);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-icon {
            width: 100px;
            height: 100px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, #3A71A8 0%, #324157 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 50px;
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        .error-title {
            font-size: 24px;
            font-weight: 600;
            color: #324157;
            margin-bottom: 12px;
        }

        .error-message {
            font-size: 15px;
            color: #3A71A8;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .error-suggestions {
            text-align: left;
            background: rgba(202, 224, 249, 0.3);
            border-radius: 8px;
            padding: 18px;
            margin-bottom: 25px;
            border: 1px solid rgba(58, 113, 168, 0.2);
        }

        .error-suggestions h3 {
            font-size: 16px;
            color: #324157;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            font-weight: 600;
        }

        .error-suggestions h3::before {
            content: "💡";
            margin-right: 8px;
        }

        .error-suggestions ul {
            list-style: none;
            padding: 0;
        }

        .error-suggestions li {
            padding: 6px 0;
            color: #3A71A8;
            position: relative;
            padding-left: 18px;
            font-size: 14px;
        }

        .error-suggestions li::before {
            content: "•";
            color: #3A71A8;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .retry-button {
            background: linear-gradient(135deg, #16D9E3 0%, #30C7EC 50%, #46AEF7 100%);
            color: white;
            border: none;
            padding: 12px 32px;
            border-radius: 25px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 12px;
            box-shadow: 0 2px 8px rgba(70, 174, 247, 0.3);
        }

        .retry-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(70, 174, 247, 0.4);
        }

        .retry-button:active {
            transform: translateY(0);
        }

        .quit-button {
            background: transparent;
            color: #3A71A8;
            border: 1px solid #3A71A8;
            padding: 11px 24px;
            border-radius: 25px;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quit-button:hover {
            background: #3A71A8;
            color: white;
        }

        .network-status {
            margin-top: 18px;
            padding: 12px;
            background: rgba(202, 224, 249, 0.5);
            border: 1px solid rgba(58, 113, 168, 0.3);
            border-radius: 6px;
            color: #324157;
            font-size: 13px;
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner {
            border: 3px solid rgba(202, 224, 249, 0.3);
            border-top: 3px solid #46AEF7;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            📡
        </div>
        <h1 class="error-title">网络连接异常</h1>
        <p class="error-message">
            很抱歉，无法连接到服务器。请检查您的网络连接后重试。
        </p>

        <div class="error-suggestions">
            <h3>解决建议</h3>
            <ul>
                <li>检查网络连接是否正常</li>
                <li>确认WiFi或有线网络已连接</li>
                <li>尝试访问其他网站验证网络</li>
                <li>重启路由器或网络设备</li>
                <li>联系网络管理员或技术支持</li>
            </ul>
        </div>

        <div>
            <button class="retry-button" onclick="retryConnection()">
                重新连接
            </button>
            <button class="quit-button" onclick="quitApp()">
                退出应用
            </button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p style="margin-top: 10px; color: #667eea;">正在重新连接...</p>
        </div>

        <div class="network-status" id="networkStatus">
            <strong>网络状态：</strong><span id="statusText">检测中...</span>
        </div>
    </div>

    <script>
        function updateNetworkStatus() {
            const statusText = document.getElementById('statusText');
            if (navigator.onLine) {
                statusText.textContent = '已连接到网络';
                statusText.parentElement.style.background = 'rgba(48, 176, 143, 0.1)';
                statusText.parentElement.style.borderColor = 'rgba(48, 176, 143, 0.3)';
                statusText.parentElement.style.color = '#30B08F';
            } else {
                statusText.textContent = '网络连接断开';
                statusText.parentElement.style.background = 'rgba(192, 54, 57, 0.1)';
                statusText.parentElement.style.borderColor = 'rgba(192, 54, 57, 0.3)';
                statusText.parentElement.style.color = '#C03639';
            }
        }

        function retryConnection() {
            const loading = document.getElementById('loading');
            const retryButton = document.querySelector('.retry-button');

            loading.style.display = 'block';
            retryButton.disabled = true;
            retryButton.textContent = '连接中...';
            retryButton.style.opacity = '0.6';

            if (window.ipc) {
                window.ipc.send('retry-connection');
            } else {
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        }

        function quitApp() {
            if (window.ipc) {
                window.ipc.send('quit-app');
            } else {
                window.close();
            }
        }

        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);

        updateNetworkStatus();
        setInterval(updateNetworkStatus, 5000);

        let autoRetryCount = 0;
        const maxAutoRetry = 3;

        function autoRetry() {
            if (navigator.onLine && autoRetryCount < maxAutoRetry) {
                autoRetryCount++;
                console.log(`自动重试第 ${autoRetryCount} 次`);
                setTimeout(retryConnection, 3000);
            }
        }

        window.addEventListener('online', () => {
            setTimeout(autoRetry, 1000);
        });
        function resetRetryState() {
            const loading = document.getElementById('loading')
            const retryButton = document.querySelector('.retry-button')

            loading.style.display = 'none'
            retryButton.disabled = false
            retryButton.textContent = '重新连接'
            retryButton.style.opacity = '1'
        }
    </script>
</body>
</html>
