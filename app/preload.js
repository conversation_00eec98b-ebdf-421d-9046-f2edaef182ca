const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')
const info = require('../package.json')

contextBridge.exposeInMainWorld(
  '_platform',
  process.platform
)

// 同步暴露渠道配置，避免异步获取的时序问题
contextBridge.exposeInMainWorld(
  '_channelConfig',
  info.channel_config
)

const domainConfig = {
  primaryUrl: info.API,
  backupUrl: info.API_BACKUP,
  downloadUrl: info.API + '#/classpro/login',
  backupDownloadUrl: info.API_BACKUP + '#/classpro/login'
}

contextBridge.exposeInMainWorld(
  '_domainConfig',
  domainConfig
)

/**
* 通信方法挂载到window对象上
* 在渲染进程中使用:
* <script setup lang="ts">
* window.ipc.on('WindowID', (e, f) => console.log(e, f))
* window.ipc.send('navBar', val)
* </script>
*/
contextBridge.exposeInMainWorld('ipc', {
  send: (channel, ...args) => ipcRenderer.send(channel, ...args),
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
  on: (channel, listener) => {
    ipcRenderer.on(channel, listener)
  },
  once: (channel, listener) => {
    ipcRenderer.once(channel, listener)
  },
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel)
  }
})
