const { autoUpdater } = require('electron-differential-updater')
const { ipcMain, app, Menu, globalShortcut, BrowserWindow, net, dialog, shell } = require('electron')
const path = require('path')
const fs = require('fs')
const Store = require('electron-store')
const info = require('../package.json')

let fullPackageInfo = info
try {
  const packageJsonPath = path.join(__dirname, '../package.json')
  const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8')
  fullPackageInfo = JSON.parse(packageJsonContent)
} catch (error) {
  console.log('read package.json error:', error.message)
}
const process = require('process')
const Download = require(path.join(__dirname, './download'))

const logDir = path.join(app.getPath('userData'), 'logs')
const logFile = path.join(logDir, 'network-check.log')

function ensureLogDir() {
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true })
  }
}

function writeLog(message) {
  try {
    ensureLogDir()
    const now = new Date()
    const timestamp = now.getFullYear() + '-' +
                     String(now.getMonth() + 1).padStart(2, '0') + '-' +
                     String(now.getDate()).padStart(2, '0') + ' ' +
                     String(now.getHours()).padStart(2, '0') + ':' +
                     String(now.getMinutes()).padStart(2, '0') + ':' +
                     String(now.getSeconds()).padStart(2, '0') + '.' +
                     String(now.getMilliseconds()).padStart(3, '0')
    const logEntry = `[${timestamp}] ${message}\n`

    console.log(message)
    fs.appendFileSync(logFile, logEntry)
  } catch (error) {
    console.log('log error:', error.message)
  }
}
// 获取url里的参数
function getAllUrlParams (url) {
  // 用JS拿到URL，如果函数接收了URL，那就用函数的参数。如果没传参，就使用当前页面的URL
  var queryString = url ? url.split('?')[1] : window.location.search.slice(1)
  // 用来存储我们所有的参数
  var obj = {}
  // 如果没有传参，返回一个空对象
  if (!queryString) {
    return obj
  }
  // stuff after # is not part of query string, so get rid of it
  queryString = queryString.split('#')[0]
  // 将参数分成数组
  var arr = queryString.split('&')
  for (var i = 0; i < arr.length; i++) {
    // 分离成key:value的形式
    var a = arr[i].split('=')
    // 将undefined标记为true
    var paramName = a[0]
    var paramValue = typeof (a[1]) === 'undefined' ? true : a[1]
    // 如果调用对象时要求大小写区分，可删除这两行代码
    paramName = paramName.toLowerCase()
    if (typeof paramValue === 'string') paramValue = paramValue.toLowerCase()
    // 如果paramName以方括号结束, e.g. colors[] or colors[2]
    if (paramName.match(/\[(\d+)?\]$/)) {
      // 如果paramName不存在，则创建key
      var key = paramName.replace(/\[(\d+)?\]/, '')
      if (!obj[key]) obj[key] = []
      // 如果是索引数组 e.g. colors[2]
      if (paramName.match(/\[\d+\]$/)) {
        // 获取索引值并在对应的位置添加值
        var index = /\[(\d+)\]/.exec(paramName)[1]
        obj[key][index] = paramValue
      } else {
        // 如果是其它的类型，也放到数组中
        obj[key].push(paramValue)
      }
    } else {
      // 处理字符串类型
      if (!obj[paramName]) {
        // 如果如果paramName不存在，则创建对象的属性
        obj[paramName] = paramValue
      } else if (obj[paramName] && typeof obj[paramName] === 'string') {
        // 如果属性存在，并且是个字符串，那么就转换为数组
        obj[paramName] = [obj[paramName]]
        obj[paramName].push(paramValue)
      } else {
        // 如果是其它的类型，还是往数组里丢
        obj[paramName].push(paramValue)
      }
    }
  }
  return obj
}
// autoUpdater.logger = require('electron-log')
// autoUpdater.logger.transports.file.level = 'info'

// 签名验证
app.commandLine.appendSwitch('ignore-certificate-errors', true)

// 平台判定
const platform = require('os').platform()
const isMac = platform === 'darwin'

const store = new Store()

// 通过环境变量判断是否启用测试地址
let API = process.env.NODE_ENV === 'development' ? 'http://localhost:8889' : info.API
let BASE_API = process.env.NODE_ENV === 'development' ? 'https://api.qa.bingotalk.cn' : info.BASE_API

if (store.get('API')) {
  API = store.get('API')
}

let Hardware = store.get('Hardware') || true

const winURL = API

// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
let mainWindow
let fromMenu = false
autoUpdater.autoDownload = false

function sendRequest (url, callback) {
  const request2 = net.request(url)
  request2.on('response', (response) => {
    response.on('data', callback)
  })
  request2.on('error', err => {
    console.log(err.toString())
    // const str = err.toString()
    // if (str.indexOf('ERR_INTERNET_DISCONNECTED') > -1) {

    // }
  })
  // 结束请求，不然没有响应数据
  request2.end()
}

ipcMain.on('open-url', (event, url) => {
  shell.openExternal(url)
})

ipcMain.handle('getVersion', async (event, videoList) => {
  return info.version
})
ipcMain.handle('getChannelConfig', async (event, videoList) => {
  return info.channel_config
})

ipcMain.handle('check-domain-availability', async (event, url) => {
  return new Promise((resolve) => {
    console.log('🔍 主进程检测域名:', url)

    const request = net.request({
      method: 'HEAD',
      url: url
    })

    const timeout = setTimeout(() => {
      request.abort()
      console.log('⏰ 主进程检测超时')
      resolve(false)
    }, 25000)

    request.on('response', (response) => {
      clearTimeout(timeout)
      const isAvailable = response.statusCode >= 200 && response.statusCode < 500
      resolve(isAvailable)
    })

    request.on('error', (error) => {
      clearTimeout(timeout)
      const certErrors = [
        'CERT_HAS_EXPIRED',
        'CERT_AUTHORITY_INVALID',
        'CERT_COMMON_NAME_INVALID',
        'CERT_UNTRUSTED',
        'CERT_REVOKED'
      ]
      const isCertError = certErrors.some(certError =>
        error.message.includes(certError) || error.code === certError
      )
      if (isCertError) {
        console.log('⚠️ 证书问题，但域名可用:', error.message)
        resolve(true)
      } else {
        resolve(false)
      }
    })

    request.end()
  })
})
async function checkBasicNetworkConnection() {
  const testUrls = [
    'https://www.baidu.com',
    'https://www.qq.com',
    'https://www.163.com'
  ]

  const checkSingleUrl = (url, timeout = 8000) => {
    return new Promise((resolve) => {
      const startTime = Date.now()
      writeLog(`    📡 发起请求: (超时时间设置为: ${timeout}ms, 开始时间: ${new Date().toLocaleTimeString()})`)

      const request = net.request({
        method: 'HEAD',
        url: url
      })

      const timeoutId = setTimeout(() => {
        const endTime = Date.now()
        const duration = endTime - startTime
        request.abort()
        writeLog(`    ⏰ 网络检测超时: (耗时: ${duration}ms, 超时设置: ${timeout}ms)`)
        resolve(false)
      }, timeout)

      request.on('response', () => {
        const endTime = Date.now()
        const duration = endTime - startTime
        clearTimeout(timeoutId)
        writeLog(`    ✅ 网络检测成功: (耗时: ${duration}ms)`)
        resolve(true)
      })

      request.on('error', (error) => {
        const endTime = Date.now()
        const duration = endTime - startTime
        clearTimeout(timeoutId)
        writeLog(`    ❌ 网络检测失败: (耗时: ${duration}ms, 错误: ${error.message})`)
        resolve(false)
      })

      request.end()
    })
  }

  const totalStartTime = Date.now()
  writeLog(`-------------- 开始检测网络 -------------------- \n `)
  writeLog(`🚀 开始网络检测，总共${testUrls.length}个网址，时间: ${new Date().toLocaleTimeString()}`)

  const delays = [0, 1500, 3000, 6000]
  const maxAttempts = 4

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const urlIndex = attempt < testUrls.length ? attempt : 0
    const url = testUrls[urlIndex]
    const requestStartTime = Date.now()

    try {
      writeLog(`🔍 [${attempt + 1}/${maxAttempts}] 开始第 ${attempt + 1} 次检测 (时间: ${new Date().toLocaleTimeString()})`)
      const isConnected = await checkSingleUrl(url)
      const requestEndTime = Date.now()
      const requestDuration = requestEndTime - requestStartTime

      if (isConnected) {
        writeLog(`🎉 [${attempt + 1}/${maxAttempts}] 网络检测成功 (耗时: ${requestDuration}ms)`)
        const totalDuration = requestEndTime - totalStartTime
        writeLog(`✅ 总检测完成，总耗时: ${totalDuration}ms \n `)
        writeLog(`-------------- 网络检测结束 -------------------- \n `)
        return true
      } else {
        writeLog(`❌ [${attempt + 1}/${maxAttempts}] 网络检测失败 (耗时: ${requestDuration}ms)`)
      }

      if (attempt < maxAttempts - 1) {
        const delay = delays[attempt + 1]
        const delayStartTime = Date.now()
        writeLog(`⏳ [${attempt + 1}/${maxAttempts}] 等待${delay}ms后进行下次检测 (开始时间: ${new Date().toLocaleTimeString()})`)
        await new Promise(resolve => setTimeout(resolve, delay))
        const delayEndTime = Date.now()
        const actualDelay = delayEndTime - delayStartTime
        writeLog(`⏰ [${attempt + 1}/${maxAttempts}] 等待完成，实际延迟: ${actualDelay}ms (预期: ${delay}ms)`)
      }
    } catch (error) {
      const requestEndTime = Date.now()
      const requestDuration = requestEndTime - requestStartTime
      writeLog(`🔍 [${attempt + 1}/${maxAttempts}] 检测网络 ${url} 时发生异常 (耗时: ${requestDuration}ms): ${error.message}`)
    }
  }

  const totalEndTime = Date.now()
  const totalDuration = totalEndTime - totalStartTime
  writeLog(`❌ 所有网络检测均失败，网络不可用！总耗时: ${totalDuration}ms (结束时间: ${new Date().toLocaleTimeString()})`)
  writeLog(`-------------- 网络检测结束 -------------------- \n `)
  return false
}

Download.download()
function createWindow () {
  // 打印当前应用的包信息
  console.log('🔍 ===== 当前应用包信息 =====')
  console.log('应用名称:', app.getName())
  console.log('应用版本:', app.getVersion())
  console.log('包配置 appId:', info.build?.appId || 'unknown')
  console.log('产品名称:', info.build?.productName || 'unknown')
  console.log('渠道配置:', info.channel_config || 'unknown')
  console.log('包名称:', info.name || 'unknown')
  console.log('================================')

  const titleConfig = info.windowTitleConfig || { showInTitle: false, showInAbout: true }
  const windowTitle = titleConfig.showInTitle ? (info.build?.productName || '缤果课堂') : ''
  const clearObj = {
    storages: ['appcache', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'serviceworkers', 'cachestorage']
  }
  const menuDisplayName = info.menuDisplayName || info.build?.productName || '缤果课堂'
  const template = [
    ...(isMac ? [{
      label: menuDisplayName,
      submenu: [
        {
          label: `关于 ${menuDisplayName}`, role: 'about'
        },
        { type: 'separator' },
        { label: '服务', role: 'services' },
        { type: 'separator' },
        { label: '隐藏', role: 'hide' },
        { label: '隐藏其他', role: 'hideothers' },
        { role: 'unhide' },
        { type: 'separator' },
        { label: '退出', role: 'quit' }
      ]
    }] : []),
    {
      label: '编辑',
      submenu: [
        { label: '撤销', role: 'undo' },
        { label: '重做', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', role: 'cut' },
        { label: '复制', role: 'copy' },
        { label: '粘贴', role: 'paste' },
        ...(isMac ? [
          { label: '删除', role: 'delete' },
          { label: '全选', role: 'selectall' },
          { type: 'separator' }
        ] : [
          { label: '删除', role: 'delete' },
          { type: 'separator' },
          { label: '全选', role: 'selectall' }
        ])
      ]
    },
    {
      label: '视图',
      submenu: [
        { label: '重新加载', role: 'reload' },
        { label: '强制重新加载', role: 'forcereload' },
        { label: '开发者工具', role: 'toggledevtools' },
        {
          label: '清除缓存数据',
          accelerator: 'CmdOrCtrl+Shift+Delete',
          click: (item, focusedWindow) => {
            if (focusedWindow) {
              focusedWindow.webContents.session.clearStorageData(clearObj)
              store.delete('API')
              store.delete('BASE_API_ARR')
            }
          }
        }
      ]
    },
    {
      label: '其他',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox({
              title: '学校端',
              message: '学校端',
              detail: `Version: ${info.version}`,
              type: 'info'
            })
          }
        },
        // {
        //   label: '清除本地地址',
        //   click: (item, focusedWindow) => {
        //     if (focusedWindow) {
        //       store.delete('API')
        //       store.delete('BASE_API_ARR')
        //     }
        //   }
        // },
        {
          label: '清除缓存视频',
          click: (item, focusedWindow) => {
            Download.deleteALLVideo(Download.downloadRootPath)
          }
        },
        // {
        //   label: '查看网络日志',
        //   click: () => {
        //     shell.openPath(logFile)
        //   }
        // },
        ...(isMac ? [] : [
          {
            label: !Hardware ? '开启硬件加速' : '禁用硬件加速',
            click: (item, focusedWindow) => {
              if (focusedWindow) {
                store.set('Hardware', !Hardware)
                Hardware = !Hardware
              }
            }
          }
        ])
      ]
    }
  ]

  app.on('web-contents-created', (createEvent, contents) => {
    contents.setWindowOpenHandler(({ url }) => {
      return {
        action: 'allow',
        overrideBrowserWindowOptions: {
          title: windowTitle,
          width: 1150,
          height: 800,
          minWidth: 1150,
          minHeight: 800,
          show: false,
          webPreferences: {
            nodeIntegrationInSubFrames: true,
            autoplayPolicy: 'no-user-gesture-required',
            nodeIntegration: true,
            contextIsolation: true,
            preload: path.join(__dirname, './preload')
          }
        }
      }
    })

    contents.on('did-create-window', (window, { url }) => {
      // 1：全屏，2：最大化，3：最小化
      const opt = getAllUrlParams(url).opt
      if (opt) {
        switch (opt) {
          case '1':
            window.setFullScreen(true)
            break
          case '2':
            window.maximize()
            break
          case '3':
            window.minimize()
            break
        }
      }
    })
  })

  app.on('browser-window-created', (createEvent, window) => {
    window.once('ready-to-show', () => {
      // setTimeout(() => {
      window.show()
      // }, 100)
    })
  })

  mainWindow = new BrowserWindow({
    title: windowTitle,
    frame: true,
    // titleBarStyle: 'hiddenInset', // macOS: 隐藏标题栏但保留窗口控制按钮
    // titleBarOverlay: true, // Windows: 自定义标题栏样式
    width: 1150,
    height: 800,
    minWidth: 1150,
    minHeight: 800,
    center: true,
    resizable: true,
    show: false,
    backgroundColor: '#cae0f9',
    webPreferences: {
      nodeIntegrationInSubFrames: true,
      autoplayPolicy: 'no-user-gesture-required',
      nodeIntegration: true,
      contextIsolation: true,
      preload: path.join(__dirname, './preload')
    }
  })
  // mainWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
  //   const allowedPermissions = [
  //     'camera',
  //     'microphone',
  //     'geolocation',
  //     'notifications',
  //     'media'
  //   ]

  //   if (allowedPermissions.includes(permission)) {
  //     callback(true)
  //   } else {
  //     callback(false)
  //   }
  // })
  // const startUrl = process.env.ELECTRON_START_URL ||
  // `file://${path.resolve(
  //   __dirname,
  //   '../../app.asar/build/'
  // )}/index.html`

  mainWindow.center()

  if (store.get('BASE_API_ARR')) {
    const arr = store.get('BASE_API_ARR')
    const arrUrl = []
    arr.map(val => {
      arrUrl.push(val.keyValue)
    })
    if (arrUrl.indexOf(BASE_API) !== -1) {
      // 后台接口获取
      const strUrl2 = `${BASE_API}/api/v1/dictionary/getConfig?configType=API_HOST`
      sendRequest(strUrl2, (chunk) => {
        const { data } = JSON.parse(chunk.toString())
        if (data && data.length > 0) {
          store.set('BASE_API_ARR', data)
        }
      })
    } else {
      arr.map(val => {
        const strUrl2 = `${val.keyValue}/api/v1/dictionary/getConfig?configType=API_HOST`
        sendRequest(strUrl2, (chunk) => {
          const { data } = JSON.parse(chunk.toString())
          BASE_API = val.keyValue
          if (data && data.length > 0) {
            store.set('BASE_API_ARR', data)
          }
          // 网页地址获取
          const strUrl = `${BASE_API}/api/v1/dictionary/getConfig?configType=WEB_STUDENT_URL`
          sendRequest(strUrl, (chunk) => {
            const { data } = JSON.parse(chunk.toString())
            if (data && data.length > 0) {
              const url = data[0].keyValue
              if (url !== API) {
                store.set('API', url)
                mainWindow.loadURL(url)
              }
            }
          })
        })
      })
    }
  } else {
    // 后台接口获取
    const strUrl2 = `${BASE_API}/api/v1/dictionary/getConfig?configType=API_HOST`
    sendRequest(strUrl2, (chunk) => {
      const { data } = JSON.parse(chunk.toString())
      if (data && data.length > 0) {
        store.set('BASE_API_ARR', data)
      }
    })
  }

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL, isMainFrame) => {
    if (isMainFrame) {
      // loadNetworkErrorPage()
    }
  })

  // let loadTimeout = setTimeout(() => {
  // loadNetworkErrorPage()
  // }, 25000)

  mainWindow.webContents.on('did-finish-load', () => {
    // clearTimeout(loadTimeout)
  })

  function loadNetworkErrorPage() {
    const errorPagePath = path.join(__dirname, 'network-error.html')

    mainWindow.loadFile(errorPagePath).catch(error => {
      console.log('❌ 加载错误页面失败:', error.message)
      // 降级方案：使用简单的内联HTML
      const simpleErrorHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title></title>
          <style>
            body {
              font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
              background: rgba(202, 224, 249, 0.4);
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              margin: 0;
              color: #324157;
            }
            .container {
              text-align: center;
              background: white;
              padding: 40px;
              border-radius: 12px;
              box-shadow: 0 4px 20px rgba(50, 65, 87, 0.1);
              max-width: 400px;
            }
            button {
              padding: 12px 24px;
              margin: 10px;
              border: none;
              border-radius: 25px;
              cursor: pointer;
              font-size: 15px;
              font-weight: 600;
            }
            .retry {
              background: linear-gradient(135deg, #16D9E3 0%, #30C7EC 50%, #46AEF7 100%);
              color: white;
            }
            .quit {
              background: transparent;
              color: #3A71A8;
              border: 1px solid #3A71A8;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>网络连接异常</h2>
            <p>无法连接到服务器，请检查网络连接后重试。</p>
            <button class="retry" onclick="if(window.ipc) window.ipc.send('retry-connection'); else location.reload();">重新连接</button>
            <button class="quit" onclick="if(window.ipc) window.ipc.send('quit-app'); else window.close();">退出应用</button>
          </div>
        </body>
        </html>
      `

      mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(simpleErrorHtml)}`)
    })
  }

  async function loadMainPage() {
    mainWindow.loadURL(winURL)
    // const isNetworkAvailable = await checkBasicNetworkConnection()
    //
    // if (!isNetworkAvailable) {
    //   loadNetworkErrorPage()
    //   return
    // }
    // clearTimeout(loadTimeout)
    // mainWindow.loadURL(winURL)
  }
  setTimeout(loadMainPage, 2000)

  // mainWindow.webContents.on('new-window', (event, url, frameName, disposition, options, additionalFeatures, referrer, postBody) => {
  //   event.preventDefault()
  //   let win = new BrowserWindow({
  //     webContents: options.webContents, // use existing webContents if provided
  //     show: false,
  //     width: 1150,
  //     height: 800,
  //     minWidth: 1150,
  //     minHeight: 800,
  //     parent: mainWindow,
  //     webPreferences: {
  //       preload: path.join(__dirname, './preload')
  //     }
  //   })
  //   if (platform === 'darwin') {
  //     // 修复黑屏问题
  //     if (mainWindow.isFullScreen()) {
  //       mainWindow.once('leave-full-screen', () => mainWindow.hide())
  //       mainWindow.setFullScreen(false)
  //     }
  //   }
  //   win.once('ready-to-show', () => win.show())
  //   win.once('close', () => {
  //     if (win.isFullScreen()) {
  //       win.setFullScreen(false)
  //     }
  //     win = null
  //     mainWindow.show()
  //   })
  //   win.once('closed', () => {
  //     win = null
  //     mainWindow.show()
  //   })
  //   if (!options.webContents) {
  //     const loadOptions = {
  //       httpReferrer: referrer
  //     }
  //     if (postBody != null) {
  //       const { data, contentType, boundary } = postBody
  //       loadOptions.postData = data
  //       loadOptions.extraHeaders = `content-type: ${contentType}; boundary=${boundary}`
  //     }

  //     win.loadURL(url, loadOptions) // existing webContents will be navigated automatically
  //   }
  //   event.newGuest = win
  // })

  // Emitted when the window is closed.

  // mainWindow.webContents.setWindowOpenHandler(({ url }) => {
  //   console.log('setWindowOpenHandler', url)
  //   return {
  //     action: 'allow',
  //     overrideBrowserWindowOptions: {
  //       width: 1150,
  //       height: 800,
  //       minWidth: 1150,
  //       minHeight: 800,
  //       webPreferences: {
  //         nodeIntegrationInSubFrames: true,
  //         autoplayPolicy: 'no-user-gesture-required',
  //         nodeIntegration: true,
  //         contextIsolation: true,
  //         preload: path.join(__dirname, './preload')
  //       }
  //     }
  //   }
  // })

  mainWindow.on('closed', function () {
    // Dereference the window object, usually you would store windows
    // in an array if your app supports multi windows, this is the time
    // when you should delete the corresponding element.
    const currentWindow = BrowserWindow.getFocusedWindow()
    if (currentWindow === mainWindow) {
      mainWindow = null
    }
    mainWindow = null
  })

  // mainWindow.once('ready-to-show', () => {
  //   mainWindow.show()
  // })

  const menu = Menu.buildFromTemplate(template)

  if (platform === 'darwin') {
    mainWindow.excludedFromShownWindowsMenu = true
    Menu.setApplicationMenu(menu)
  }

  if (platform === 'win32') {
    // const menu = Menu.buildFromTemplate(template)
    Menu.setApplicationMenu(menu)
    // mainWindow.setMenu(menu)
  }

  ipcMain.on('resize-window', (event, reply) => {
    const currentWindow = BrowserWindow.getFocusedWindow() || mainWindow

    if (platform === 'darwin') {
      if (reply.width === 700) {
        currentWindow.setResizable(true)
        currentWindow.setFullScreen(false)
        currentWindow.setContentSize(reply.width, reply.height, false)
        currentWindow.center()
        currentWindow.setResizable(false)
        return
      }
    }

    if (platform === 'win32') {
      if (reply.width === 700) {
        if (currentWindow.isFullScreen()) {
          currentWindow.setResizable(true)
          currentWindow.setFullScreen(false)
          currentWindow.setResizable(false)
        }
      }
    }

    currentWindow.setContentSize(reply.width, reply.height, false)
    currentWindow.center()
  })

  ipcMain.on('minimum', (event) => {
    const currentWindow = BrowserWindow.getFocusedWindow()
    currentWindow.minimize()
  })

  ipcMain.on('maximum', () => {
    const currentWindow = BrowserWindow.getFocusedWindow()

    if (platform === 'win32') {
      const fullscreen = currentWindow.isFullScreen()
      if (fullscreen) {
        currentWindow.setResizable(true)
        currentWindow.setFullScreen(false)
        currentWindow.setResizable(false)
      } else {
        currentWindow.setResizable(true)
        currentWindow.setFullScreen(true)
        currentWindow.setResizable(false)
      }
    }

    if (platform === 'darwin') {
      const fullscreen = currentWindow.isFullScreen()
      currentWindow.setFullScreen(!fullscreen)
    }
  })

  ipcMain.on('close', () => {
    const currentWindow = BrowserWindow.getFocusedWindow() || mainWindow
    if (currentWindow === mainWindow) {
      app.quit()
      return
    }
    currentWindow.close()
  })

  ipcMain.on('retry-connection', async () => {
    mainWindow.loadURL(winURL)
    // // const isNetworkAvailable = await checkBasicNetworkConnection()
    // if (isNetworkAvailable) {
    //   mainWindow.loadURL(winURL)
    // } else {
    //   mainWindow.webContents.executeJavaScript(`
    //     if (typeof resetRetryState === 'function') {
    //       resetRetryState();
    //     }
    //   `)
    // }
  })

  ipcMain.on('quit-app', () => {
    app.quit()
  })

  // if (process.env.NODE_ENV === 'development') {
  //   autoUpdater.updateConfigPath = path.join(__dirname, 'dev-app-update.yml')
  // }

  const message = {
    error: 'updateCheckError',
    checking: 'updateChecking',
    updateAva: 'updateAva',
    updateNotAva: 'updateNotAva'
  }

  function sendUpdateMessage (text) {
    mainWindow.webContents.send('message', text)
  }

  autoUpdater.on('error', function (error) {
    if (fromMenu) {
      sendUpdateMessage({
        fromMenu: fromMenu,
        message: 'updateError'
      })
      fromMenu = false
    } else {
      sendUpdateMessage(error.message)
    }
  })
  autoUpdater.on('checking-for-update', function () {
    sendUpdateMessage(message.checking)
  })
  autoUpdater.on('update-available', function (info) {
    sendUpdateMessage({ update: true, info })
  })
  autoUpdater.on('update-not-available', function (info) {
    if (fromMenu) {
      sendUpdateMessage({
        fromMenu: fromMenu,
        message: message.updateNotAva
      })
      fromMenu = false
    } else {
      sendUpdateMessage(message.updateNotAva)
    }
  })

  // 更新下载进度事件
  autoUpdater.on('download-progress', function (progressObj) {
    mainWindow.webContents.send('downloadProgress', progressObj)
  })
  ipcMain.on('startDownload', (e, arg) => {
    sendUpdateMessage('开始下载更新')
    // some code here to handle event
    mainWindow.webContents.send('downloadProgress', { percent: 1 })
    autoUpdater.downloadUpdate()
  })
  autoUpdater.on('update-downloaded', (info) => {
    sendUpdateMessage('下载完成')
    mainWindow.webContents.send('isUpdateNow')
  })
  ipcMain.on('isUpdateNow', (e, arg) => {
    sendUpdateMessage('开始退出更新')
    // some code here to handle event
    autoUpdater.quitAndInstall()
  })

  ipcMain.on('checkForUpdate', () => {
    // 执行自动更新检查
    autoUpdater.checkForUpdates()
  })

  // 添加获取应用信息的方法
  ipcMain.handle('getAppInfo', () => {
    console.log('🔍 调试 getAppInfo 被调用')

    // 使用更可靠的方式获取应用信息
    const fs = require('fs')
    const path = require('path')

    let packageInfo = {}
    try {
      // 直接读取文件，避免 require 缓存问题
      const packagePath = path.join(__dirname, '../package.json')
      const packageContent = fs.readFileSync(packagePath, 'utf8')
      packageInfo = JSON.parse(packageContent)
      console.log('🔍 直接读取的 package.json 关键信息:', {
        name: packageInfo.name,
        version: packageInfo.version,
        channel_config: packageInfo.channel_config,
        appId: packageInfo.build?.appId,
        productName: packageInfo.build?.productName
      })
    } catch (error) {
      console.log('🔍 直接读取 package.json 失败:', error)
      // 降级到原始的 info 对象
      packageInfo = info
    }

    // 优先使用 Electron API，其次使用文件读取结果
    const result = {
      name: app.getName() || packageInfo.name || 'unknown',
      version: app.getVersion() || packageInfo.version || 'unknown',
      appId: packageInfo.build?.appId || 'unknown',
      productName: packageInfo.build?.productName || 'unknown',
      channel: packageInfo.channel_config || 'unknown',
      packageName: packageInfo.name || 'unknown'
    }

    console.log('🔍 返回的应用信息:', result)
    return result
  })
}

if (!Hardware) {
  app.disableHardwareAcceleration()
}
// autoUpdater.checkForUpdates();

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', createWindow)

app.whenReady().then(() => {
  // more details: https://www.electronjs.org/docs/tutorial/keyboard-shortcuts
  globalShortcut.register('Alt+Shift+X', () => {
    // Open the DevTools.
    const currentWindow = BrowserWindow.getFocusedWindow()
    currentWindow.webContents.openDevTools()
  })
})

// Quit when all windows are closed.
app.on('window-all-closed', function () {
  // On OS X it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', function () {
  console.log('main process activate')
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (mainWindow === null) {
    createWindow()
  }

  if (mainWindow) {
    mainWindow.show()
  }
})

// Disable error dialogs by overriding
dialog.showErrorBox = function (title, content) {
  console.log(`${title}\n${content}`)
}
// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
