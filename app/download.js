const { app, ipcMain } = require('electron')
const fetch = require('node-fetch')
const AbortController = require('abort-controller')
const path = require('path')
const {
  access,
  writeFile,
  readFileSync,
  constants,
  mkdir,
  rmSync,
  readdir,
  rename
} = require('fs')
const { Promise } = require('core-js')
const downloadRootPath = path.join(app.getPath('userData'), '/download')

const blockSize = 1024 * 1024 * 10 // 每一块的大小

// fetch
// 获取响应头信息, 为了判断是否可以分段下载
const getResHeaders = (url) => {
  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'GET', // 请求方式
      // mode: 'cors',
      headers: {
        // 请求头
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        Pragma: 'no-cache',
        Range: 'bytes=0-1'
      }
    })
      .then((r) => {
        console.log(url, r)
        const h = {}
        r.headers.forEach(function (v, i, a) {
          h[i.toLowerCase()] = v
        })
        return resolve(h)
      })
      .catch(reject)
  })
}

// 下载块
const downloadBlock = (u, o, controller) => {
  let option = {
    'Content-Type': 'application/octet-stream',
    'Cache-Control': 'no-cache',
    Connection: 'keep-alive',
    Pragma: 'no-cache'
  }
  if (typeof o === 'string') {
    option['Range'] = 'bytes=' + o
  } else if (typeof o === 'object') {
    option = Object.assign(option, o)
  }
  return fetch(u, {
    method: 'GET',
    headers: option,
    signal: controller.signal
  }).then((res) => res.buffer())
}

// 文件操作
// 文件路径是否存在
const fileAccess = (path, constants) => {
  return new Promise((resolve, reject) => {
    access(path, constants, (err) => {
      if (err) {
        reject(err)
      }
      resolve(1)
    })
  })
}

// 创建文件目录
const mkdirFile = (path) => {
  return new Promise((resolve, reject) => {
    mkdir(path, (err) => {
      if (err) {
        reject(err)
      }
      resolve(1)
    })
  })
}

// 创建文件目录
const reNameFile = (path, newPath) => {
  return new Promise((resolve, reject) => {
    rename(path, newPath, (err) => {
      if (err) {
        reject(err)
      }
      resolve(1)
    })
  })
}

// read file name
const fileDir = (dir) => {
  return new Promise((resolve, reject) => {
    readdir(dir, (err, files) => {
      if (err) {
        reject(err)
      }
      files = files.filter(item => !(/(^|\/)\.[^\/\.]/g).test(item))
      resolve(files)
    })
  })
}

const creatAccess = async (path) => {
  try {
    await fileAccess(path, constants.F_OK)
  } catch (error) {
    await mkdirFile(path)
  }
}

const writeFiles = (path, data) => {
  return new Promise((resolve, reject) => {
    writeFile(path, data, (err) => {
      if (err) {
        reject(err)
      }
      resolve(1)
    })
  })
}

const saveHeadFile = async (range, h, url, controller, savePath) => {
  try {
    console.log('saveHeadFileURL', url, range)
    const p = await downloadBlock(url, {
      etag: h.etag,
      'Content-Type': h['content-type'],
      Range: range
    }, controller)
    if (savePath) {
      // console.log('savePath', savePath)
      await writeFiles(savePath, p)
      // console.log('savePathDone', savePath)
      return 1
    } else {
      return p
    }
  } catch (error) {
    console.log('saveHeadFile', error)
    console.log('saveHeadFileName', error.name)
    return Promise.reject(error)
  }
}

// 视频文件是否存在
const isExist = async (id_list) => {
  const videoFiles = await fileDir(downloadRootPath)
  const videoDoneLists = videoFiles.filter(val => { return val.split('-').length === 1 })
  const videoLists = videoFiles.filter(val => { return val.split('-').length > 1 })
  const videoListsObj = {}
  const videoListsKey = []
  videoLists.map((val) => {
    const obj = val.split('-')
    videoListsObj[obj[0]] = Number(obj[1])
    videoListsKey.push(obj[0])
  })
  for (const key in id_list) {
    // 已经下载完成的直接标记为存储完毕
    if (videoDoneLists && videoDoneLists.indexOf(key) > -1) {
      id_list[key].isCache = true
    }

    // 已经缓存了部分的写入缓存了多少
    if (videoListsKey && videoListsKey.indexOf(key) > -1) {
      id_list[key].lastBlock = videoListsObj[key]
    }
  }
  return { 'list': id_list, blockSize: blockSize }
}

// 读取本地视频文件
const loadLocalVideo = async (dirPath, name) => {
  const bList = []
  const filePathList = []
  const videoPath = path.join(dirPath, name)
  const videoFiles = await fileDir(videoPath)
  videoFiles.forEach((val) => {
    filePathList.push(path.join(videoPath, val))
  })
  for (let i = 0; i < filePathList.length; i++) {
    const data = readFileSync(filePathList[i]) // 读取文件，并将缓存区进行转换
    bList.push(data)
  }
  return Buffer.concat(bList)
}

// 移除本地视频文件
// eslint-disable-next-line no-unused-vars
const deleteVideo = (downloadRootPath, videoInfo) => {
  let filePath = path.join(downloadRootPath, '/head/', videoInfo['name'])
  rmSync(filePath, { recursive: true, force: true })
  filePath = path.join(downloadRootPath, '/mp4/', videoInfo['name'])
  rmSync(filePath, { recursive: true, force: true })
  return 0
}

// 移除本所有地视频文件
const deleteALLVideo = (downloadRootPath) => {
  const filePath = path.join(downloadRootPath)
  rmSync(filePath, { recursive: true, force: true })
  return 0
}

const BlockDownload = async (downloadRootPath, videoList, mainWindow) => {
  const controller = new AbortController()
  let pauseDownload = false
  let cancelDownload = false
  // let delVideoInfo = null

  // 监听暂停和取消操作
  ipcMain.once('pauseDownload', (event) => {
    controller.abort()
    pauseDownload = true
  })
  ipcMain.once('cancelDownload', (event, videoInfo) => {
    controller.abort()
    cancelDownload = true
    // delVideoInfo = videoInfo
  })
  const list = Object.values(videoList)

  for (let listIndex = 0; listIndex < list.length; listIndex++) {
    const videoInfo = list[listIndex]
    // 获取url中文件名
    const url = videoInfo['url']
    const fileName = url.split('/').reverse()[0].split('.')[0]
    console.log(fileName)

    if (videoInfo['isCache']) {
      continue
    } else if (videoInfo['lastBlock']) {
      await creatAccess(path.join(downloadRootPath, `${fileName}-${videoInfo['lastBlock']}`))
    } else {
      await creatAccess(path.join(downloadRootPath, `${fileName}-0`))
    }

    // 初始化下载
    // 获取请求头信息
    const h = await getResHeaders(url)
    const contentRange = h['content-range']

    // 判断是否支持分段下载
    if (contentRange) {
    // 获取文件大小
      const contentLength = Number(contentRange.split('/').reverse()[0])
      // 获取分段数目
      const totalBlock = Math.ceil((contentLength - 1) / blockSize)

      // 确认下载进度
      const lastBlock = videoInfo['lastBlock'] // 只记录中间段的分块

      // 判断是否需要分块下载
      if (totalBlock >= 1) {
      // 分段下载
        for (let i = lastBlock; i < totalBlock; i++) {
          const filePath = path.join(
            downloadRootPath,
            `${fileName}-${videoInfo['lastBlock']}`,
            `${fileName}-${i}`
          )
          // 存储的文件段开始和结尾
          const start = blockSize * i
          let end = blockSize * (i + 1) - 1
          if (i === totalBlock - 1) {
            end = ''
          } else {
            end = end > contentLength ? contentLength - 1 : end
          }
          try {
            await saveHeadFile(`bytes=${start}-${end}`, h, url, controller, filePath)
            // 改名更新文件进度
            const oldPath = path.join(downloadRootPath, `${fileName}-${videoInfo['lastBlock']}`)
            const newPath = path.join(downloadRootPath, `${fileName}-${i}`)
            console.log(oldPath, newPath)
            await reNameFile(oldPath, newPath)
            videoInfo['lastBlock'] = i
            const progress = (((i + 1) * 100) / totalBlock).toFixed(2)
            let size = 0
            if (end) {
              size = end - start
            } else {
              size = contentLength - 1 - start
            }
            mainWindow.sender.send('saveResult', { progress, index: i, name: videoInfo['fileName'], size })
          } catch (error) {
            console.log('for', error.name)
            // if (error.name === 'AbortError') {
            //   break
            // }
            break
          }
        }
        if (+videoInfo['lastBlock'] === +totalBlock - 1) {
          videoInfo['isCache'] = true
          const dirs = await fileDir(path.join(downloadRootPath, `${fileName}-${videoInfo['lastBlock']}`))
          console.log('dirs', dirs)
          if (dirs.length === videoInfo['lastBlock'] + 1) {
            const oldPath = path.join(downloadRootPath, `${fileName}-${videoInfo['lastBlock']}`)
            const newPath = path.join(downloadRootPath, `${fileName}`)
            await reNameFile(oldPath, newPath)
          }
        }
      }
    }
    list[listIndex] = videoInfo
  }

  if (pauseDownload) {
    return { status: 'pause', list }
  } else if (cancelDownload) {
    // deleteVideo(downloadRootPath, delVideoInfo)
    deleteALLVideo(downloadRootPath)
    return { status: 'cancel', list }
  } else {
    return { status: 'done', list }
  }
}

const Download = {
  download: async () => {
    try {
      await creatAccess(downloadRootPath)
    } catch (error) {
      console.log(error)
    }
    ipcMain.handle('isExist', async (event, id_list) => {
      try {
        await creatAccess(downloadRootPath)
      } catch (error) {
        console.log(error)
      }
      return isExist(id_list)
    })

    ipcMain.handle('loadLocalVideo', (event, name) => {
      return loadLocalVideo(downloadRootPath, name)
    })

    ipcMain.handle('deleteVideo', (event, videoInfo) => {
      // return deleteVideo(downloadRootPath, videoInfo)
      return deleteALLVideo(downloadRootPath)
    })

    ipcMain.handle('blockSave', async (event, videoList) => {
      try {
        await creatAccess(downloadRootPath)
      } catch (error) {
        console.log(error)
      }
      return await BlockDownload(downloadRootPath, videoList, event)
    })
  },
  downloadRootPath: downloadRootPath,
  deleteALLVideo: deleteALLVideo
}
module.exports = Download
