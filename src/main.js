import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import 'normalize.css/normalize.css'
// import './assets/iconfont/iconfont.css'
import 'element-ui/lib/theme-chalk/index.css'
import '@/styles/element-variables.scss'
import '@/styles/index.scss' // global css
// import './assets/iconfont/iconfont'
import './permission' // permission control
import './icons'
import 'viewerjs/dist/viewer.css'
import { message } from '@/utils/singeMessage.js'
import { i18n } from './lang'
import ElementUI from 'element-ui'
import VueClipboard from 'vue-clipboard2'
import { ImagePreview } from 'vant'
import JwChat from 'jwchat'
import VueViewer from 'v-viewer'
import '@/utils/mathjax'
import 'mathjax/es5/tex-mml-svg'
Vue.use(JwChat)
Vue.use(ImagePreview)
Vue.use(ElementUI)
Vue.use(VueClipboard)
Vue.use(VueViewer)

Vue.config.productionTip = false
Vue.prototype.$message = message
// 全局事件总线
Vue.prototype.$bus = new Vue()

import Vant from 'vant'
import 'vant/lib/index.css'

Vue.use(Vant)

// import Vconsole from 'vconsole'
// const vConsole = new Vconsole()
// Vue.use(vConsole)

// Vue.use(Button)
// Vue.use(Input)
// Vue.use(Popover)
// Vue.use(Select)
// Vue.use(Option)
// Vue.use(Loading.directive)
// Vue.use(Dialog)
// Vue.use(Form)
// Vue.use(FormItem)
// Vue.use(Loading)
// Vue.use(Checkbox)
// Vue.use(CheckboxButton)
// Vue.use(CheckboxGroup)
// Vue.use(Upload)
// Vue.use(Progress)

new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')
