import AgoraRTC from 'agora-rtc-sdk-ng'
import { EventEmitter } from 'events'

class RtcClient extends EventEmitter {
  /**
   * @param {Object} options {
   *   appId: string,
   *   webConfig: { mode: string, codec: string, role: string }
   * }
   * @return {null}
   */
  constructor (options) {
    super()
    this.localTracks = {
      videoTrack: null,
      audioTrack: null
    }
    this.agoraRtc = AgoraRTC
    this.appId = process.env.VUE_APP_AGORA_APPID
    this.clientConfig = options.webConfig
    // this._client = AgoraRTC.createClient(this.clientConfig)
    this.mics = [] // all microphones devices you can use
    this.cams = [] // all cameras devices you can use
    this.playbacks = [] // all cameras devices you can use
    this.currentMic = null // the microphone you are using
    this.currentCam = null // the camera you are using
    this.currentPlayback = null // the camera you are using
  }

  get client () {
    return this._client
  }

  async mediaDeviceTest (domId, camData) {
    this.cams = await AgoraRTC.getCameras()
    if (camData) {
      const customCam = this.cams.findIndex((cam) => {
        if (camData && camData.length > 0) {
          for (const item of camData) {
            const re = cam.label.includes(item.keyValue)
            if (re) return re
          }
        }
      })
      if (customCam > -1) {
        this.currentCam = this.cams[customCam]
      } else {
        this.currentCam = this.cams[0]
      }
    } else {
      this.currentCam = this.cams[0]
    }
    if (this.currentCam) {
      this.localTracks.videoTrack = await AgoraRTC.createCameraVideoTrack({
        cameraId: this.currentCam.deviceId
      })
    } else {
      this.localTracks.videoTrack = await AgoraRTC.createCameraVideoTrack()
    }

    this.localTracks.videoTrack.play(domId)
  }

  async micDeviceTest (micDta) {
    // this.localTracks.audioTrack.play()
    this.mics = await AgoraRTC.getMicrophones()
    if (micDta) {
      const customMic = this.mics.findIndex((mic) => {
        if (micDta && micDta.length > 0) {
          for (const item of micDta) {
            const re = mic.label.includes(item.keyValue)
            if (re) return re
          }
        }
      })
      if (customMic > -1) {
        this.currentMic = this.mics[customMic]
      } else {
        this.currentMic = this.mics[0]
      }
    } else {
      this.currentMic = this.mics[0]
    }
    if (this.currentMic) {
      this.localTracks.audioTrack = await AgoraRTC.createMicrophoneAudioTrack({
        microphoneId: this.currentMic.deviceId
      })
    } else {
      this.localTracks.audioTrack = await AgoraRTC.createMicrophoneAudioTrack()
    }
    // this.currentMic = this.mics[0]
  }

  async playbackDeviceTest () {
    this.localTracks.audioTrack = await AgoraRTC.createMicrophoneAudioTrack()
    this.playbacks = await AgoraRTC.getPlaybackDevices()
    this.currentPlayback = this.playbacks[0]
  }

  async leave () {
    for (const trackName in this.localTracks) {
      console.log(trackName)
      var track = this.localTracks[trackName]
      if (track) {
        track.stop()
        track.close()
        this.localTracks[trackName] = undefined
      }
    }
    await this._client.leave()
  }

  async join (option) {
    this._client = AgoraRTC.createClient(this.clientConfig)
    this._client.on('network-quality', (evt) => {
      this.fire('network-quality', {
        downlinkNetworkQuality: evt.downlinkNetworkQuality,
        uplinkNetworkQuality: evt.uplinkNetworkQuality
      })
    })
    this._client.on('user-published', async (user, mediaType) => {
      if (mediaType === 'audio') {
        await this._client.subscribe(user, 'audio')
        // if (user.audioTrack) {
        //   !user.audioTrack.isPlaying && user.audioTrack.play()
        // }
      }

      if (mediaType === 'video') {
        await this._client.subscribe(user, 'video')
        this.fire('user-published', {
          user,
          mediaType
        })
      }
    })
    this.localTracks.videoTrack = await AgoraRTC.createCameraVideoTrack()
    // this.localTracks.audioTrack = await AgoraRTC.createMicrophoneAudioTrack()

    this.localUid = await this._client.join(this.appId, option.channel, option.token, option.uid)
  }

  async publish () {
    await this.client.publish([this.localTracks.videoTrack])
  }

  fire (...eventArgs) {
    const [eventName, ...args] = eventArgs
    this.emit(eventName, ...args)
  }
}

export default RtcClient
