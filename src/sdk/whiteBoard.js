import { WhiteWebSdk, DeviceType, LoggerReportMode } from 'white-web-sdk'
// import 'video.js/dist/video-js.css'
// import { videoJsPlugin } from '@netless/video-js-plugin'
export default class WhiteClient {
  constructor (option) {
    this.whiteWebSdk = WhiteWebSdk
    this._room = null
    // const plugins = createPlugins({
    //   'video.js': videoJsPlugin({
    //     currentTimeMaxError: 1,
    //     syncInterval: 500,
    //     retryInterval: 15000,
    //     onPlayer: (params) => {
    //       console.log('player', params)
    //     }
    //   })
    // })
    // plugins.setPluginContext('video.js', { enable: true, verbose: true })
    this._client = new WhiteWebSdk({
      appIdentifier: process.env.VUE_APP_AGORA_WHITE_BOARD_APPID,
      // plugins,
      region: 'cn-hz',
      deviceType: DeviceType.Surface,
      preloadDynamicPPT: true,
      // pptParams: {
      //   useServerWrap: false
      // },
      useMobXState: true,
      loggerOptions: {
        reportDebugLogMode: LoggerReportMode.AlwaysReport,
        reportLevelMask: 'debug',
        printLevelMask: 'debug'
      },
      ...option
    })
  }

  get client () {
    return this._client
  }

  get room () {
    return this._room
  }

  async joinRoom (params, callbck) {
    return this._client.joinRoom(params, callbck)
  }

  async replayRoom (params, callbck) {
    return this._client.replayRoom(params, callbck)
  }
}
