
import { message } from '@/utils/singeMessage.js'

class NetworkMonitor {
  constructor() {
    this.isOnline = true
    this.listeners = []
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 3000
    this.isReconnecting = false
    this.lastOnlineTime = Date.now()
    this.offlineStartTime = null
    this.lastQuality = 'good'
    this.initialCheckDone = false
    this.init()
  }

  init() {
    if (!this.isElectronEnvironment()) {
      return
    }

    window.addEventListener('online', this.handleOnline.bind(this))
    window.addEventListener('offline', this.handleOffline.bind(this))
    setTimeout(() => {
      this.performInitialCheck()
    }, 5000)

    this.startPeriodicCheck()
  }

  async performInitialCheck() {
    try {
      await this.checkConnection()
      this.initialCheckDone = true
    } catch (error) {
      this.initialCheckDone = true
    }
  }
  isElectronEnvironment() {
    return !!(window.ipc || window.require || window.process?.versions?.electron)
  }

  addListener(callback) {
    this.listeners.push(callback)
  }

  removeListener(callback) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  notifyListeners(status) {
    this.listeners.forEach(callback => {
      try {
        callback(status)
      } catch (error) {
        console.error('网络状态监听器、错误:', error)
      }
    })
  }

  handleOnline() {
    this.isOnline = true
    this.reconnectAttempts = 0
    this.isReconnecting = false
    this.lastOnlineTime = Date.now()
    if (this.offlineStartTime) {
      const offlineDuration = Date.now() - this.offlineStartTime
      console.log(`⏲️ 网络恢复，持续时间: ${offlineDuration}ms`)
      this.offlineStartTime = null
    }

    this.notifyListeners({
      online: true,
      reconnecting: false,
      attempts: this.reconnectAttempts
    })

    this.handleNetworkRecovery()
  }

  handleOffline() {
    this.isOnline = false
    this.offlineStartTime = Date.now()

    message({
      message: '网络连接已断开，正在尝试重连...',
      type: 'warning',
      duration: 5000
    })

    this.notifyListeners({
      online: false,
      reconnecting: false,
      attempts: this.reconnectAttempts
    })

    this.startReconnect()
  }

  startReconnect() {
    if (this.isReconnecting || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return
    }

    this.isReconnecting = true
    this.reconnectAttempts++

    this.notifyListeners({
      online: false,
      reconnecting: true,
      attempts: this.reconnectAttempts
    })

    setTimeout(() => {
      this.checkConnection()
    }, this.reconnectDelay * this.reconnectAttempts)
  }

  async checkConnection() {
    try {
      const results = await Promise.allSettled([
        this.pingTest('https://www.baidu.com'),
        this.pingTest('https://www.qq.com'),
        navigator.onLine ? Promise.resolve(true) : Promise.reject(new Error('Navigator offline'))
      ])

      const successCount = results.filter(result => result.status === 'fulfilled').length
      const isConnected = successCount > 0

      if (isConnected && !this.isOnline) {
        this.handleOnline()
      } else if (!isConnected && this.isOnline) {
        this.handleOffline()
      } else if (!isConnected) {
        this.isReconnecting = false
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          setTimeout(() => this.startReconnect(), 1000)
        } else {
          message({
            message: '网络连接失败，请检查网络设置',
            type: 'error',
            duration: 3000
          })
        }
      }
    } catch (error) {
      console.error('网络检测失败:', error)
      this.isReconnecting = false
    }
  }

  async pingTest(url, timeout = 3000) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'no-cors',
        signal: controller.signal,
        cache: 'no-cache'
      })
      clearTimeout(timeoutId)
      return true
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  startPeriodicCheck() {
    setInterval(() => {
      if (this.isOnline && !this.isReconnecting) {
        this.checkNetworkQuality()
      }
    }, 30000)
  }

  async checkNetworkQuality() {
    try {
      const startTime = Date.now()
      await this.pingTest('https://www.baidu.com', 5000)
      const latency = Date.now() - startTime

      let quality = 'good'
      if (latency > 2000) {
        quality = 'poor'
      } else if (latency > 1000) {
        quality = 'fair'
      }

      if (quality !== this.lastQuality) {
        console.log(`📊 网络质量变化: ${this.lastQuality} -> ${quality}, 延迟: ${latency}ms`)
        this.lastQuality = quality

        this.notifyListeners({
          online: true,
          quality: quality,
          latency: latency
        })
      }

      if (quality === 'poor') {
        console.log(`⚠️ 网络质量较差，延迟: ${latency}ms`)
      }
    } catch (error) {
      this.checkConnection()
    }
  }

  handleNetworkRecovery() {
    if (window.location.pathname.includes('network-error') ||
        document.title.includes('网络连接异常')) {
      if (window.ipc) {
        window.ipc.send('retry-connection')
      } else {
        window.location.reload()
      }
    }
  }

  getStatus() {
    return {
      online: this.isOnline,
      reconnecting: this.isReconnecting,
      attempts: this.reconnectAttempts,
      lastOnlineTime: this.lastOnlineTime,
      offlineStartTime: this.offlineStartTime
    }
  }

  manualReconnect() {
    this.reconnectAttempts = 0
    this.isReconnecting = false
    this.startReconnect()
  }

  destroy() {
    window.removeEventListener('online', this.handleOnline.bind(this))
    window.removeEventListener('offline', this.handleOffline.bind(this))
    this.listeners = []
  }
}

const networkMonitor = new NetworkMonitor()

export default networkMonitor
export { NetworkMonitor }
