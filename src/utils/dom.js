export function hasClass (el, className) {
  return el.classList.contains(className)
}

export function addClass (el, className) {
  el.classList.add(className)
}

export function removeClass (el, cls) {
  if (hasClass(el, cls)) {
    let newClass = ' ' + el.className.replace(/[\t\r\n]/g, '') + ' '
    while (newClass.indexOf(' ' + cls + ' ') >= 0) {
      newClass = newClass.replace(' ' + cls + ' ', ' ')
    }
    el.className = newClass.replace(/^\s+|\s+$/g, '')
  }
}

export function offset (ele) {
  const result = {
    top: 0,
    left: 0
  }
  const getOffset = (node, init) => {
    if (node.nodeType !== 1) {
      return
    }
    position = window.getComputedStyle(node).position

    if (typeof (init) === 'undefined' && position === 'static') {
      getOffset(node.parentNode)
      return
    }

    result.top = node.offsetTop + result.top - node.scrollTop
    result.left = node.offsetLeft + result.left - node.scrollLeft

    if (position === 'fixed') {
      return
    }

    getOffset(node.parentNode)
  }

  if (window.getComputedStyle(ele).display === 'none') {
    return result
  }

  let position

  getOffset(ele, true)

  return result
}

export function getScrollHeight () {
  var scrollHeight = 0
  var bodyScrollHeight = 0
  var documentScrollHeight = 0
  if (document.body) {
    bodyScrollHeight = document.body.scrollHeight
  }

  if (document.documentElement) {
    documentScrollHeight = document.documentElement.scrollHeight
  }
  scrollHeight =
    bodyScrollHeight - documentScrollHeight > 0
      ? bodyScrollHeight
      : documentScrollHeight
  return scrollHeight
}

export function getWindowHeight () {
  var windowHeight = 0
  if (document.compatMode === 'CSS1Compat') {
    windowHeight = document.documentElement.clientHeight
  } else {
    windowHeight = document.body.clientHeight
  }
  return windowHeight
}

export function getDocumentTop () {
  var scrollTop = 0
  var bodyScrollTop = 0
  var documentScrollTop = 0
  if (document.body) {
    bodyScrollTop = document.body.scrollTop
  }
  if (document.documentElement) {
    documentScrollTop = document.documentElement.scrollTop
  }
  scrollTop =
    bodyScrollTop - documentScrollTop > 0
      ? bodyScrollTop
      : documentScrollTop
  return scrollTop
}

export function findParentByClass(el, className) {
  // 处理Vue组件根元素
  if (el.__vue__) {
    el = el.__vue__.$el
  }

  // 向上递归查找，直到body元素
  while (el && el !== document.body) {
    if (el.classList.contains(className)) {
      return el // 找到匹配元素
    }
    el = el.parentElement // 继续向上查找
  }

  return false // 未找到匹配元素
}
