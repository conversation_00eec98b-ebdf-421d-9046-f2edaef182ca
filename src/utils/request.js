import axios from 'axios'
import { message } from '@/utils/singeMessage.js'
import store from '@/store'
import { getToken, getPartentToken, getPublishToken, getExpertToken, getAuthorToken } from '@/utils/auth'
import { isElectronWeb } from '@/api/config'
import { version } from 'rootpath/package.json'
import { Toast } from 'vant'
import { isApp } from '@/utils/index'
const platform = window.clientInformation['platform']
var patt1 = new RegExp('Mac')
const isMac = patt1.test(platform)
let v = version
if (window.ipc) {
  window.ipc.invoke('getVersion').then(res => {
    v = res
  }).catch(() => {
    v = version
  })
}

// 获取渠道版本信息
if (window.ipc) {
  window.ipc.invoke('getChannelConfig').then(res => {
    window.localStorage.setItem('channel', res)
  }).catch(() => {
    // 获取失败时不做处理，使用默认逻辑
  })
}
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 600000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    const href = window.location.href
    if (!config.headers.authorization && href.indexOf('/expert') > -1 && href.indexOf('activity') === -1) {
      config.headers.authorization = getExpertToken()
    } else if (!config.headers.authorization && (href.indexOf('/author') > -1 || href.indexOf('/editor') > -1)) {
      config.headers.authorization = getAuthorToken()
    } else if (!config.headers.authorization && href.indexOf('/publisher') > -1) {
      config.headers.authorization = getPublishToken()
    } else if (!config.headers.authorization && store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers.authorization = getToken()
    }
    if (config.params && config.params.userType === 'PARENT') {
      config.headers.authorization = getPartentToken()
    }
    if (href.indexOf('parent') > -1 || href.indexOf('/h5/') > -1) {
      config.headers.authorization = getPartentToken()
    }
    // if (!config.url.includes('getLastAppVersion')) {
    //   config.headers.app = 'ClassPro'
    //   config.headers.appType = 'WEB'
    //   config.headers.appVersion = '3.0.0'
    // }
    const getChannelConfig = () => {
      const channel = window.localStorage.getItem('channel')
      if (channel) {
        return channel
      }

      // 如果是 Electron 环境，直接从 preload 暴露的同步配置获取
      if (window._channelConfig && !channel) {
        console.log('🔍 使用 Electron 同步渠道配置:', window._channelConfig)
        // 只在第一次时设置到 localStorage
        window.localStorage.setItem('channel', window._channelConfig)
        return window._channelConfig
      }

      // 保持原有逻辑：直接使用 headerChannel（包括 cuiya）
      return window.localStorage.getItem('headerChannel') || 'cuiya'
    }

    config.headers['channel'] = getChannelConfig()
    if (href.indexOf('/chuanEdu/detail') > -1) {
      config.headers['channel'] = 'cjt'
    }
    window.localStorage.setItem('headerChannel', config.headers['channel'])
    if (isElectronWeb) {
      if (config.url.includes('getLastAppVersion')) {
        config.headers['app'] = 'BingoClass'
        if (isMac) {
          config.headers['appType'] = 'APP_MAC'
        } else {
          config.headers['appType'] = 'APP_WIN'
        }
      } else {
        config.headers['app'] = 'ClassPro'
        config.headers['appType'] = 'WEB'
      }
      config.headers['appVersion'] = v
      if (config.urlType === 'admin') {
        delete config.headers['app']
        delete config.headers['appType']
        delete config.headers['appVersion']
      }
    } else if (href.indexOf('parent') > -1 || href.indexOf('/h5/') > -1) {
      config.headers['app'] = 'Parent'
      config.headers['appType'] = 'H5'
      config.headers['appVersion'] = version
    } else if (href.indexOf('publisher') > -1) {
      config.headers['app'] = 'PUBLISHER'
    } else if (href.indexOf('expert') > -1 && href.indexOf('activity') === -1) {
      config.headers['app'] = 'EXPERT'
    } else if (href.indexOf('editor') > -1 || href.indexOf('author') > -1) {
      config.headers['app'] = 'AUTHOR'
    } else if (href.indexOf('community') > -1) {
      config.headers['app'] = 'BingoMate'
    } else if (href.indexOf('bingoBook') > -1) {
      config.headers['app'] = 'BingoBook'
      config.headers['appType'] = 'H5'
    } else if (config.urlType === 'admin') {
      delete config.headers['authorization']
    } else {
      config.headers['app'] = 'ClassPro'
      config.headers['appType'] = 'WEB'
      config.headers['appVersion'] = version
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data
    // if the custom code is not 20000, it is judged as an error.
    if (response.config.url.indexOf('api.agora.io') > -1) {
      // 声网接口过滤
      return res
    } else if (+res.code !== 200) {
      if (res.code === 500 && (response.config.url === 'https://admin.qa.bingotalk.cn/aicenter/user/login' || response.config.url === 'https://admin.bingotalk.cn/aicenter/user/login')) {
        return res
      }
      if (+res.code === 602 || +res.code === 911) {
        // 602: 用户不存在 911: 无效的激活码
        const href = window.location.href
        if (href.indexOf('parent') === -1 && href.indexOf('h5') === -1 && href.indexOf('community') === -1) {
          // 不在家长端
          message({
            message: res.message || 'Error',
            type: 'error',
            duration: 5 * 1000
          })
        }
      } else {
        if (+res.code !== 910 && +res.code !== 635) {
          const href = window.location.href
          // 910:授权失败 635:邮箱已注册
          if (href.indexOf('bingoBook') !== -1) {
            Toast(`${res && res.message}`)
          } else {
            message({
              message: (res && res.message) || 'Error',
              type: 'error',
              duration: 5 * 1000
            })
          }
        }
      }

      if (+res.code === 901) {
        // 登录过期
        store.dispatch('user/FedLogOut')
        if (window.location.href.indexOf('/parent') !== -1) {
          location.href = '/#/parent/checkLogin'
        } else if (window.location.href.indexOf('/h5') !== -1) {
          if (isApp()) {
            if (window.webkit && window.webkit.messageHandlers) {
              window.webkit.messageHandlers.bingo_action.postMessage('event_no_login')
            } else if (window.bingo_action) {
              window.bingo_action.postMessage('event_no_login')
            }
          } else {
            location.href = '/#/h5/checkLogin'
          }
        } else if (window.location.href.indexOf('/datacenter') !== -1) {
          location.href = '/#/datacenter/login'
        } else if (window.location.href.indexOf('/publisher') !== -1) {
          location.href = '/#/publisher/login'
          store.dispatch('user/PublishLogout')
        } else if (window.location.href.indexOf('/expert') !== -1 && window.location.href.indexOf('activity') === -1) {
          location.href = '/#/expert/login'
          store.dispatch('user/ExpertLogout')
        } else if (window.location.href.indexOf('/author') !== -1) {
          location.href = '/#/author/login'
          store.dispatch('user/AuthorLogout')
        } else if (window.location.href.indexOf('/bingoBook') !== -1) {
          if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.bingo_download) {
            window.webkit.messageHandlers.bingo_action.postMessage('event_no_login')
          } else if (window.bingo_action) {
            window.bingo_action.postMessage('event_no_login')
          } else {
            location.href = '/#/bingoBook/login'
            store.dispatch('user/FedLogOut')
          }
        } else {
          location.href = '/#/classpro/login'
        }
        return false
      }

      return Promise.reject(res)
    } else {
      return res
    }
  },
  error => {
    console.log('err:' + error) // for debug
    if (['Network Error'].indexOf(error.message) > -1) {
      message({
        message: '网络故障，请检测网络',
        type: 'error',
        duration: 5 * 1000
      })
    } else {
      message({
        message: error.message,
        type: 'error',
        duration: 5 * 1000
      })
    }

    return Promise.reject(error)
  }
)

export default service
