
import {
  getCompatibleWebUrl,
  getAvailableWebUrl
} from './domainHelper'

export function testDomainManager() {
  const isElectron = !!window.ipc
  const domainConfig = window._domainConfig || {}
  const isQA = window.location.hostname.includes('qa')

  console.log('🧪 域名切换测试')
  console.log('环境:', isQA ? 'qa' : 'production', '| Electron:', isElectron ? '✅' : '❌')
  console.log('主域名:', domainConfig.primaryUrl || '未配置')
  console.log('备用域名:', domainConfig.backupUrl || '未配置')
  console.log('手动选择 - 主:', getCompatibleWebUrl(false))
  console.log('手动选择 - 备:', getCompatibleWebUrl(true))
}

export async function testSmartDomainSelection() {
  if (!window.ipc) {
    console.log('⚠️ 非Electron环境，无法进行网络检测')
    return
  }

  try {
    const selectedUrl = await getAvailableWebUrl()
    const domainConfig = window._domainConfig || {}
    const isBackup = selectedUrl === domainConfig.backupUrl
    console.log('🚀 智能选择:', isBackup ? '备用域名' : '主域名', '|', selectedUrl)
  } catch (error) {
    console.log('❌ 智能选择失败:', error.message)
  }
}

export function addDebugTools() {
  if (typeof window !== 'undefined') {
    window.domainDebug = {
      test: testDomainManager,
      smartTest: testSmartDomainSelection,
      getCompatibleWebUrl,
      getAvailableWebUrl,
      getConfig() {
        return window._domainConfig || null
      },
      async testFailover() {
        if (!window.ipc) {
          console.log('⚠️ 非Electron环境，无法测试')
          return { error: '需要Electron环境' }
        }

        const original = window._domainConfig
        if (!original || !original.backupUrl) {
          console.log('❌ 域名配置不完整')
          return { error: '域名配置不完整' }
        }

        const normalUrl = await getAvailableWebUrl()
        const isMainDomainSelected = normalUrl === original.primaryUrl
        const hasBackupDomain = !!original.backupUrl

        console.log('🎯 故障转移测试:', {
          选择域名: isMainDomainSelected ? '主域名' : '备用域名',
          备用域名: hasBackupDomain ? '已配置' : '未配置',
          机制状态: '就绪'
        })

        return {
          normalUrl,
          isMainDomainSelected,
          hasBackupDomain,
          backupUrl: original.backupUrl
        }
      }
    }
  }
}

function initDebugTools() {
  if (typeof window !== 'undefined') {
    setTimeout(() => {
      try {
        addDebugTools()
      } catch (error) {
        console.log('❌ 调试工具初始化失败:', error.message)
      }
    }, 500)
  }
}

if (typeof window !== 'undefined') {
  if (process.env.NODE_ENV === 'development') {
    initDebugTools()
  }

  if (window.require) {
    initDebugTools()
  }

  if (document.readyState === 'complete') {
    initDebugTools()
  } else {
    window.addEventListener('load', initDebugTools)
  }
}
