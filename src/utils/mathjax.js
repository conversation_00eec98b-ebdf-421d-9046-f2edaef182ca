window.MathJax = {
  loader: { load: ['input/tex', 'output/svg'] },
  svg: {
    fontCache: 'global'// 可以设置为'local'或'global'
  },
  tex: {
    inlineMath: [['$', '$'], ['\\(', '\\)']],
    displayMath: [['$$', '$$'], ['\\[', '\\]']],
    processEscapes: true,
    processEnvironments: true
  },
  options: {
    enableMenu: false
  },
  startup: {
    ready: () => {
      window.MathJax.startup.defaultReady()
    }
  }
}
