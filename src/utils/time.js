import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import 'dayjs/locale/en'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear'
import isLeapYear from 'dayjs/plugin/isLeapYear'
dayjs.extend(customParseFormat)
dayjs.extend(weekOfYear)
dayjs.extend(isoWeeksInYear)
dayjs.extend(isLeapYear)

const formatYYYYMMDDHHmm = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

const formatYYYYMMDD = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).format('YYYY-MM-DD')
}

const formatYYYYMM = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).format('YYYY-MM')
}

const formatD = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).format('D')
}

const formatYYYYMMDDDot = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).format('YYYY.MM.DD')
}

const formatMMDDDot = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).format('MM.DD')
}

const formatWeek = (date) => {
  if (!date) {
    return ''
  }
  dayjs.locale('zh-cn')
  return dayjs(date).format('ddd')
}

const formatHHmm = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).format('HH:mm')
}

const formatWeeklyTime = (date) => {
  if (!date) {
    return ''
  }
  const weeklyTime = date.split(',')
  let day
  switch (weeklyTime[0]) {
    case 'MONDAY':
      day = '周一'
      break
    case 'TUESDAY':
      day = '周二'
      break
    case 'WEDNESDAY':
      day = '周三'
      break
    case 'THURSDAY':
      day = '周四'
      break
    case 'FRIDAY':
      day = '周五'
      break
    case 'SATURDAY':
      day = '周六'
      break
    case 'SUNDAY':
      day = '周日'
      break
    default :
      return 'Invalid Time'
  }
  const startTime = dayjs(weeklyTime[1], 'HH:mm:ss')
  const endTime = startTime.add(weeklyTime[2] * 0.5, 'hour')
  return `${day} (每周) ${startTime.format('H:mm')}-${endTime.format('H:mm')}`
}

const addMonth = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).add(1, 'months').startOf('month').format('YYYY-MM-DD')
}

const subMonth = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).subtract(1, 'months').startOf('month').format('YYYY-MM-DD')
}

const addMin = (date, min) => {
  if (!date) {
    return ''
  }
  return dayjs(date).add(min, 'minute').format('HH:mm')
}

const subMin = (date, min) => {
  if (!date) {
    return ''
  }
  return dayjs(date).subtract(min, 'minute').format('HH:mm')
}

const formatHHmmHalfHour = (date) => {
  if (!date) {
    return ''
  }
  const endTime = dayjs(date).add(30, 'minutes')
  const formatStartTime = dayjs(date).format('HH:mm')
  const formatEndTime = dayjs(endTime).format('HH:mm')
  return `${formatStartTime}-${formatEndTime}`
}

const getWeeksInMonth = (date) => {
  if (!date) {
    return ''
  }
  const firstDay = dayjs(date).startOf('month')
  const lastDay = dayjs(date).endOf('month')
  const fristWeek = dayjs(firstDay).week()
  const lastWeek = dayjs(lastDay).week()
  const weeksInYear = dayjs(date).isoWeeksInYear()
  console.log(firstDay, lastDay)
  console.log(fristWeek, lastWeek)
  return (lastWeek - fristWeek + weeksInYear) % weeksInYear + 1
}

const getWeekOfMonth = (date) => {
  if (!date) {
    return ''
  }
  const firstDay = dayjs(date).startOf('month')
  const fristWeek = dayjs(firstDay).week()
  const curWeek = dayjs(date).week()
  const weeksInYear = dayjs(date).isoWeeksInYear()
  return (curWeek - fristWeek + weeksInYear) % weeksInYear + 1
}

const getStartWeek = (date) => {
  if (!date) {
    return ''
  }
  dayjs.locale('zh-cn')
  return dayjs(date).startOf('week').format('YYYY-MM-DD')
}

const addDays = (date, count) => {
  if (!date) {
    return ''
  }
  dayjs.locale('zh-cn')
  const startWeek = dayjs(date).startOf('week')
  return startWeek.add(count - 1, 'day').startOf('day').format('YYYY-MM-DD')
}

const getTimestamp = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).valueOf()
}

const isBefore = (date) => {
  return dayjs(date).isBefore(dayjs())
}

const getStartTimeOfDay = (date) => {
  let startTime = dayjs()
  if (dayjs().isBefore(dayjs(date))) {
    startTime = date
  }
  return `${dayjs(startTime).format('YYYY/MM/DD HH:mm:ss')}`
}

const getEndTimeOfDay = (date) => {
  return `${dayjs(date).format('YYYY/MM/DD')} 23:59:59`
}

const formatA = (date) => {
  if (!date) {
    return ''
  }
  dayjs.locale('en')
  return dayjs(date).format('A')
}

const getClassTimePeriod = (date) => {
  if (!date) {
    return ''
  }
  const endTime = dayjs(date).add(30, 'm')
  return `${dayjs(date).format('HH:mm')}-${endTime.format('HH:mm')}`
}

const formatM = (date) => {
  if (!date) {
    return ''
  }
  return dayjs(date).format('M')
}

export {
  formatYYYYMMDD,
  formatYYYYMM,
  formatYYYYMMDDDot,
  formatWeek,
  formatWeeklyTime,
  formatHHmm,
  addMonth,
  subMonth,
  formatHHmmHalfHour,
  getWeeksInMonth,
  getWeekOfMonth,
  addDays,
  getStartWeek,
  formatD,
  getTimestamp,
  isBefore,
  getEndTimeOfDay,
  getStartTimeOfDay,
  formatA,
  getClassTimePeriod,
  formatMMDDDot,
  formatM,
  addMin,
  subMin,
  formatYYYYMMDDHHmm
}
