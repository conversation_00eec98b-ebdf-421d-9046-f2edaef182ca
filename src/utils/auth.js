import Cookies from 'js-cookie'
import localStorage from '@/utils/local-storage.js'

const AdminTokenKey = 'admin-token'

const TokenKey = 'Admin-Token'

const PartentTokenKey = 'Partent-Token'

const ExpiresInKey = 'Admin-Expires-In'

const ChildTokenKey = 'Admin-Child-Token'

const ChildIdKey = 'Admin-Child-Id'

const ChildNameKey = 'Admin-Child-Name'

const ExpertToken = 'Expert-Token'

const PublishToken = 'Publish-Token'

const AuthorToken = 'Author-Token'
export function getToken () {
  return Cookies.get(TokenKey)
}

export function setToken (token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken () {
  return Cookies.remove(TokenKey)
}
export function getExpertToken () {
  return Cookies.get(ExpertToken)
}

export function setExpertToken (token) {
  return Cookies.set(ExpertToken, token)
}

export function removeExpertToken () {
  return Cookies.remove(ExpertToken)
}
export function getPublishToken () {
  return Cookies.get(PublishToken)
}

export function setPublishToken (token) {
  return Cookies.set(PublishToken, token)
}

export function removePublishToken () {
  return Cookies.remove(PublishToken)
}
export function getAuthorToken () {
  return Cookies.get(AuthorToken)
}

export function setAuthorToken (token) {
  return Cookies.set(AuthorToken, token)
}

export function removeAuthorToken () {
  return Cookies.remove(AuthorToken)
}
export function getPartentToken () {
  return Cookies.get(PartentTokenKey)
}

export function setPartentToken (token) {
  return Cookies.set(PartentTokenKey, token)
}

export function removePartentToken () {
  return Cookies.remove(PartentTokenKey)
}

export function getExpiresIn () {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn (time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn () {
  return Cookies.remove(ExpiresInKey)
}

export function getChildId () {
  return Cookies.get(ChildIdKey)
}

export function setChildId (childId) {
  return Cookies.set(ChildIdKey, childId)
}

export function removeChildId () {
  return Cookies.remove(ChildIdKey)
}

export function getChildName () {
  return Cookies.get(ChildNameKey)
}

export function setChildName (childName) {
  return Cookies.set(ChildNameKey, childName)
}

export function removeChildName () {
  return Cookies.remove(ChildNameKey)
}

export function getChildToken () {
  return Cookies.get(ChildTokenKey)
}

export function setChildToken (childToken) {
  return Cookies.set(ChildTokenKey, childToken)
}

export function removeChildToken () {
  return Cookies.remove(ChildTokenKey)
}

export function getAdminToken () {
  if (window.adminToken) {
    return window.adminToken
  } else {
    return localStorage.read(AdminTokenKey)
  }
}
//
export function setAdminToken (token) {
  return localStorage.save(AdminTokenKey, token)
}
//
export function removeAdminToken () {
  return localStorage.clear(AdminTokenKey)
}
