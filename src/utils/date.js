import moment from 'moment-timezone'

export function addMonth (date) {
  if (!date) {
    return ''
  }
  return moment(date).add(1, 'months').startOf('month').format('YYYY-MM-DD')
}

export function subMonth (date) {
  if (!date) {
    return ''
  }
  return moment(date).subtract(1, 'months').startOf('month').format('YYYY-MM-DD')
}

export function formatDot (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('YYYY.MM.DD')
}

export function formatMMDDDot (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('MM.DD')
}

export function formatMMDDSlash (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('MM/DD')
}

export function formatMMDDSlash2 (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('MM.DD')
}

export function formatMMDDSlash3 (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('MM-DD')
}

export function formatYYYYMMSlash (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('YYYY/MM')
}

export function formatYYYYMMDot (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('YYYY.MM')
}

export function formatYYYYMMDDSlash (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('YYYY/MM/DD')
}

export function formatWeek (date, locale = 'zh-cn') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('ddd')
}

export function formatWeekddd (date) {
  if (!date) {
    return ''
  }
  moment.locale('zh-cn')
  return moment(date).format('ddd')
}

export function formatHHmm (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('HH:mm')
}

export function formatHHmmHalfHour (date) {
  if (!date) {
    return ''
  }
  const endTime = moment(date).add(30, 'minutes')
  const formatStartTime = moment(date).format('HH:mm')
  const formatEndTime = moment(endTime).format('HH:mm')
  return `${formatStartTime}-${formatEndTime}`
}

export function formatYYYYMM (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('YYYY-MM')
}

export function formatYYYYMMDD (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('YYYY-MM-DD')
}

export function formatYYYYMMDD2 (date) {
  if (!date) {
    return ''
  }
  return moment(date).format('YYYY/MM/DD')
}

export function getTimeZone () {
  return moment.tz.guess()
}

export function formatMMDDddd (date, locale = 'zh-cn') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('MM.DD ddd')
}

export function formatHHmmaa (date, locale = 'en') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('HH:mm A')
}

export function getAge (date) {
  if (!date) {
    return ''
  }
  return moment().diff(date, 'years')
}

export function formatMM (date, locale = 'zh-cn') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('MM')
}

export function formatYYYYMMCN (date, locale = 'zh-cn') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('YYYY年MM月')
}

export function formatYYYYMMDDdddHHmm (date, locale = 'zh-cn') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('YYYY-MM-DD ddd HH:mm')
}

export function formatYYYYMMDDHHmm (date, locale = 'zh-cn') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('YYYY/MM/DD HH:mm:')
}

export function formatHH (date, locale = 'zh-cn') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('HH')
}

export function formatmm (date, locale = 'zh-cn') {
  if (!date) {
    return ''
  }
  moment.locale(locale)
  return moment(date).format('mm')
}

export function formatdiffer (date) {
  const data = new Date()
  const now = data.getTime()
  const time = (date - now) / 1000 / 60
  let timeStr = ''
  timeStr = `${Math.round(time)}`
  return timeStr
}

export function formatDifferUp (date) {
  const data = new Date()
  const now = data.getTime()
  const time = (now - date) / 1000 / 60
  let timeStr = ''
  timeStr = `${Math.round(time)}`
  return timeStr
}

export function formatCustom (date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) {
    return ''
  }
  return moment(date).format(format)
}

export function formatDateTime(dateTimeString) {
  const dateTime = new Date(dateTimeString)
  const year = dateTime.getFullYear()
  const month = String(dateTime.getMonth() + 1).padStart(2, '0')
  const day = String(dateTime.getDate()).padStart(2, '0')
  const hours = String(dateTime.getHours()).padStart(2, '0')
  const minutes = String(dateTime.getMinutes()).padStart(2, '0')
  const seconds = String(dateTime.getSeconds()).padStart(2, '0')

  const formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`

  return formattedDateTime
}
