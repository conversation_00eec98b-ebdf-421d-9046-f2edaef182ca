
class TitleManager {
  constructor() {
    this.titleConfig = { showInTitle: false, showInAbout: true }
    this.productName = '缤果课堂'
    this.customTitle = ''
    this.isInitialized = false
  }

  /**
   * 初始化标题管理器
   * @param {Object} config - 渠道配置
   * @param {boolean} config.showInTitle - 是否在窗口标题显示
   * @param {boolean} config.showInAbout - 是否在关于菜单显示
   * @param {string} productName - 产品名称
   */
  async init(config = null, productName = null) {
    try {
      if (window.ipc && !config) {
        // 从 Electron 获取配置
        const appInfo = await window.ipc.invoke('getAppInfo')
        this.titleConfig = appInfo.windowTitleConfig || { showInTitle: false, showInAbout: true }
        this.productName = appInfo.productName || '缤果课堂'
      } else if (config && productName) {
        // 手动设置配置
        this.titleConfig = config
        this.productName = productName
      } else {
        // 使用默认配置
      }

      this.isInitialized = true
      this.updateTitle()
    } catch (error) {
      this.isInitialized = true
      this.updateTitle()
    }
  }

  /**
   * 设置自定义标题
   * @param {string} title - 自定义标题，传入空字符串则使用产品名称
   */
  setCustomTitle(title = '') {
    this.customTitle = title
    this.updateTitle()
  }

  /**
   * 获取当前应该显示的标题
   * @returns {string} 标题文本
   */
  getCurrentTitle() {
    if (!this.titleConfig.showInTitle) {
      return ''
    }
    return this.customTitle || this.productName
  }

  /**
   * 更新 document.title
   */
  updateTitle() {
    if (!this.isInitialized) {
      return
    }

    const newTitle = this.getCurrentTitle()
    document.title = newTitle
  }

  /**
   * 临时显示标题（不受渠道配置限制）
   * @param {string} title - 临时标题
   * @param {number} duration - 显示时长（毫秒），默认3秒
   */
  showTemporaryTitle(title, duration = 3000) {
    const originalTitle = document.title
    document.title = title
    setTimeout(() => {
      this.updateTitle() // 恢复正常标题
    }, duration)
  }

  /**
   * 强制设置标题（忽略渠道配置）
   * @param {string} title - 强制设置的标题
   */
  forceSetTitle(title) {
    document.title = title
  }

  /**
   * 获取产品名称（用于关于菜单等）
   * @returns {string} 产品名称
   */
  getProductName() {
    return this.productName
  }

  /**
   * 获取标题配置
   * @returns {Object} 标题配置
   */
  getTitleConfig() {
    return this.titleConfig
  }

  /**
   * 重置为默认标题
   */
  reset() {
    this.customTitle = ''
    this.updateTitle()
  }
}

// 创建全局实例
const titleManager = new TitleManager()

export default titleManager
