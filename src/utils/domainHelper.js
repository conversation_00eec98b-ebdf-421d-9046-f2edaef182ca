
const CHECK_TIMEOUT = 3000

function getDomainConfig() {
  const domainConfig = (typeof window !== 'undefined' && window._domainConfig) || {}
  const isQA = (typeof window !== 'undefined' && window.location.hostname.includes('qa'))

  return {
    environment: isQA ? 'qa' : 'production',
    primaryUrl: domainConfig.primaryUrl,
    backupUrl: domainConfig.backupUrl
  }
}

async function testDomainWithFetch(url) {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), CHECK_TIMEOUT)

    await fetch(url, {
      method: 'HEAD',
      mode: 'no-cors',
      signal: controller.signal,
      cache: 'no-cache'
    })

    clearTimeout(timeoutId)
    return true
  } catch (error) {
    return false
  }
}

async function testDomain(url) {
  if (typeof window !== 'undefined' && window.ipc) {
    try {
      return await window.ipc.invoke('check-domain-availability', url)
    } catch (error) {
      console.log('IPC 检测失败，降级到 fetch:', error.message)
    }
  }

  return await testDomainWithFetch(url)
}

export function getCompatibleWebUrl(useBackup = false) {
  const config = getDomainConfig()
  return useBackup ? config.backupUrl : config.primaryUrl
}

export async function getAvailableWebUrl() {
  const config = getDomainConfig()

  const isMainAvailable = await testDomain(config.primaryUrl)

  return isMainAvailable ? config.primaryUrl : config.backupUrl
}
