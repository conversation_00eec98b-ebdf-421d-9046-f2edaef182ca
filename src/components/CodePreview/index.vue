<template>
  <div class="code-preview-component">
    <el-dialog
      title="代码预览"
      :visible="visible"
      width="90%"
      custom-class="code-preview-dialog"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :show-close="false"
      :append-to-body="true"
      top="3vh"
      @close="closeDialog"
      @closed="closeDialog"
    >
      <div class="preview-container">
        <iframe
          ref="previewFrame"
          sandbox="allow-scripts"
          frameborder="0"
          width="100%"
          height="65vh"
        ></iframe>
      </div>

      <div slot="footer">
        <el-button @click="closeDialog" size="small">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CodePreview',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    initialCode: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'html'
    }
  },

  data() {
    return {
      executing: false
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        setTimeout(() => {
          this.executeCode()
        }, 100)
      }
    }
  },

  methods: {
    closeDialog() {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        const iframe = this.$refs.previewFrame
        if (iframe) {
          iframe.srcdoc = `
            <html>
            <body style="text-align: center; color: #999; padding: 50px;">
              <p>准备加载代码预览...</p>
            </body>
            </html>
          `
        }
      })
    },

    executeCode() {
      if (this.executing || !this.initialCode.trim()) {
        return
      }

      this.executing = true

      try {
        const previewHtml = this.generatePreviewHtml(this.initialCode)
        const iframe = this.$refs.previewFrame
        if (iframe) {
          iframe.srcdoc = ''
          setTimeout(() => {
            iframe.srcdoc = previewHtml
          }, 50)
        }
      } catch (error) {
        console.error('代码预览失败:', error)
      } finally {
        setTimeout(() => {
          this.executing = false
        }, 500)
      }
    },

    generatePreviewHtml(code) {
      let htmlContent = ''

      switch (this.language.toLowerCase()) {
        case 'html':
        case 'markup':
          htmlContent = code
          break
        case 'css':
          htmlContent = `
            <style>${code}</style>
            <div class="css-demo">
              <h3>CSS样式演示</h3>
              <p>这是一个段落文本</p>
              <div class="demo-box">示例盒子</div>
              <button class="demo-button">示例按钮</button>
            </div>
          `
          break
        case 'javascript':
        case 'js':
          htmlContent = `
            <div id="js-output"></div>
            <script>${code}<\/script>
          `
          break
        default:
          htmlContent = `<pre><code>${this.escapeHtml(code)}</code></pre>`
      }

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>代码预览</title>
          <style>
            html, body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 15px;
              background: #fff;
              line-height: 1.6;
              overflow-x: auto;
              overflow-y: auto;
              height: auto;
              min-height: 100%;
            }
            .css-demo { padding: 20px; }
            .demo-box {
              width: 100px; height: 100px;
              background: #4299e1; margin: 10px 0;
              border-radius: 4px;
            }
            .demo-button {
              padding: 8px 16px; background: #48bb78;
              color: white; border: none; border-radius: 4px;
            }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
        </html>
      `
    },

    escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      }
      return text.replace(/[&<>"']/g, m => map[m])
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-container {
  height: 65vh;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

iframe {
  width: 100%;
  height: 100%;
}
</style>
