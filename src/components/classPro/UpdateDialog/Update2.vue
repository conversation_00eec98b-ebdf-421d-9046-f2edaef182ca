<template>
  <div class="update-wrapper">
    <el-dialog
      custom-class="update-dialog"
      title=""
      :show-close="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :visible.sync="dialogVisible"
      top="36px"
    >
      <template v-if="status===1">
        <div class="update-dialog-body">
          <h3>新版本就绪</h3>
          <p>最新版本：{{ latestversion }}</p>
          <p class="note">{{ note }}</p>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="!force" @click="_cancelUpdate">稍后</el-button>
          <el-button type="primary" @click="_updateNow">立即升级</el-button>
        </div>
      </template>
      <template v-else>
        <div class="updating-body">
          <h2 v-if="status === 2">正在更新，请耐心等待...</h2>
          <h2 v-else-if="status===3">版本已完成更新！</h2>
          <el-progress :percentage="progress" :stroke-width="15" color="#AAD023" :text-inside="true" />
        </div>
        <div v-if="status === 2" slot="footer" class="updating-footer">
          <el-button @click="_cancelUpdate">取消</el-button>
        </div>
        <div v-else-if="status===3" slot="footer" class="updated-footer">
          <el-button type="primary" @click="_quitAndInstall">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getLastAppVersion, isElectronWeb } from '@/api/config'
import { version } from 'rootpath/package.json'
const ipc = window.ipc
export default {
  data () {
    return {
      dialogVisible: false,
      status: 0,
      progress: 0,
      force: false,
      latestversion: '',
      note: '',
      isAutoCheck: false
    }
  },
  computed: {
    updateCount () {
      return this.$store.getters.update
    }
  },
  watch: {
    updateCount () {
      console.log(this.updateCount)
      this._getLastAppVersion(true)
    }
  },
  mounted() {
    if (navigator.userAgent.includes('Electron') && window.location.hash.includes('/classpro')) {
      if (!this._hasAutoUpdateChecked()) {
        setTimeout(() => {
          this._getLastAppVersion(false)
        }, 1000)
      }
    }
    if (window) {
      window.resetAutoUpdateFlag = this.resetAutoUpdateFlag
    }
  },
  methods: {
    _hasAutoUpdateChecked() {
      const AUTO_UPDATE_CHECK_KEY = 'hasAutoUpdateChecked'
      try {
        const value = localStorage.getItem(AUTO_UPDATE_CHECK_KEY)
        const result = value === 'true'
        return result
      } catch (error) {
        return false
      }
    },

    _setAutoUpdateChecked() {
      const AUTO_UPDATE_CHECK_KEY = 'hasAutoUpdateChecked'
      try {
        localStorage.setItem(AUTO_UPDATE_CHECK_KEY, 'true')
      } catch (error) {
        console.error('🔍 error:', error)
      }
    },

    _resetAutoUpdateChecked() {
      const AUTO_UPDATE_CHECK_KEY = 'hasAutoUpdateChecked'
      try {
        localStorage.removeItem(AUTO_UPDATE_CHECK_KEY)
      } catch (error) {
        console.error('🔍 error:', error)
      }
    },

    resetAutoUpdateFlag() {
      this._resetAutoUpdateChecked()
    },

    async _getLastAppVersion (type) {
      try {
        const { data } = await getLastAppVersion()

        if (!data) {
          if (type) {
            let v = ''
            if (window.ipc) {
              window.ipc.invoke('getVersion').then(res => {
                v = res
                this.$message.success(`当前版本${v},已经是最新的版本!`)
              }).catch(() => {
                v = version
                this.$message.success(`当前版本${v},已经是最新的版本!`)
              })
            }
          }
          return
        }

        let currentVersion = version
        if (window.ipc) {
          currentVersion = await window.ipc.invoke('getVersion')
        }

        if (data.version !== currentVersion) {
          this.note = data.releaseNotes
          this._updateDialog(type)
          if (data.isForceUpdate) {
            this.force = true
          }
        } else {
          if (!type) {
            this._resetAutoUpdateChecked()
          } else {
            this.$message.success(`当前版本${currentVersion},已经是最新的版本!`)
          }
        }
      } catch (error) {
        console.log('🔍 check error:', error)
        return error
      }
    },
    _updateDialog (isManualCheck = true) {
      if (!isElectronWeb) return
      if (!ipc) return
      ipc.removeAllListeners('message')
      ipc.removeAllListeners('downloadProgress')
      ipc.removeAllListeners('isUpdateNow')
      ipc.on('message', (event, text) => {
        if (text.update) {
          this.status = 1
          this.dialogVisible = true
          this.latestversion = text.info.version

          this.$nextTick(() => {
            this.isAutoCheck = !isManualCheck
          })
        } else {
          if (typeof text === 'string' && text.includes('Could not locate update bundle')) {
            this.$message.error('更新失败：请尝试卸载后重新安装应用')
            this.dialogVisible = false
          }
        }
      })
      ipc.on('downloadProgress', (evt, progressObj) => {
        const newProgress = Math.ceil(progressObj.percent)
        if (newProgress >= this.progress) {
          this.progress = newProgress
        }
      })
      ipc.on('isUpdateNow', () => {
        this.status = 3
        this.progress = 100
      })
      ipc.send('checkForUpdate')
    },
    _updateNow () {
      if (this.isAutoCheck) {
        this._setAutoUpdateChecked()
        this.isAutoCheck = false
      }
      this.status = 2
      ipc.send('startDownload')
    },
    _cancelUpdate () {
      if (this.isAutoCheck) {
        this._setAutoUpdateChecked()
        this.isAutoCheck = false
      }
      this.dialogVisible = false
      ipc.removeAllListeners()
    },
    _quitAndInstall () {
      ipc.send('isUpdateNow')
    }
  }
}
</script>
