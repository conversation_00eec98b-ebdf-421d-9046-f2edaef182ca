<template>
  <div class="update-wrapper">
    <el-dialog
      custom-class="update-dialog"
      title=""
      :show-close="false"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      top="36px"
    >
      <template v-if="status===1">
        <div class="update-dialog-body">
          <h3>新版本就绪</h3>
          <p>最新版本：{{ latestversion }}</p>
          <p class="note">{{ note }}</p>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="!force" @click="cancelUpdate">稍后</el-button>
          <el-button type="primary" @click="updateNow">立即升级</el-button>
        </div>
      </template>
      <template v-else>
        <div class="updating-body">
          <h2 v-if="status === 2">正在更新，请耐心等待...</h2>
          <h2 v-else-if="status===3">版本已完成更新！</h2>
          <el-progress :percentage="progress" :stroke-width="15" color="#AAD023" :text-inside="true" />
        </div>
        <div v-if="status === 2" slot="footer" class="updating-footer">
          <el-button @click="cancelUpdate">取消</el-button>
        </div>
        <div v-else-if="status===3" slot="footer" class="updated-footer">
          <el-button type="primary" @click="quitAndInstall">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getLastAppVersion, isElectronWeb } from '@/api/config'
import { version } from 'rootpath/package.json'
const ipc = window.ipc
export default {
  data () {
    return {
      dialogVisible: false,
      flag: true,
      status: 0,
      progress: 0,
      force: false,
      latestversion: '',
      note: ''
    }
  },
  mounted () {
    if (navigator.userAgent.includes('Electron') && window.location.hash === '#/classpro') {
      // 当前环境是Electron
      this.getLastAppVersion()
    } else {
      // 当前环境是浏览器
    }
  },
  methods: {
    async getLastAppVersion () {
      try {
        const { data } = await getLastAppVersion()

        let currentVersion = version
        if (window.ipc) {
          currentVersion = await window.ipc.invoke('getVersion')
        }

        if (data.version !== currentVersion) {
          this.note = data.releaseNotes
          this.updateDialog()
          if (data.isForceUpdate) {
            this.force = true
          }
        }
      } catch (error) {
        return error
      }
    },
    updateDialog () {
      if (!isElectronWeb) return
      if (!ipc) return
      ipc.on('message', (event, text) => {
        console.log('flag', this.flag)
        if (!this.flag) {
          return
        }
        console.log('text', text)
        if (text.update) {
          console.log('text.update')
          this.status = 1
          this.dialogVisible = true
          this.latestversion = text.info.version
        }
      })
      ipc.on('downloadProgress', (evt, progressObj) => {
        this.progress = Math.ceil(progressObj.percent)
      })
      ipc.on('isUpdateNow', () => {
        this.status = 3
        this.progress = 100
      })
      ipc.send('checkForUpdate')
    },
    updateNow () {
      this.status = 2
      ipc.send('startDownload')
    },
    cancelUpdate () {
      this.dialogVisible = false
      this.flag = false
      ipc.removeAllListeners()
    },
    quitAndInstall () {
      ipc.send('isUpdateNow')
    }
  }
}
</script>
