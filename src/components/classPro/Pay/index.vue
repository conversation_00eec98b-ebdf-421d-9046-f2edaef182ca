<template>
  <NormalDialog
    :title="'付费'"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    width="25vw"
    @closeDialog="closePay()"
  >
    <div class="pay_main">
      <p class="p1">{{ goodInfo&&goodInfo.price }}元</p>
      <p class="p2">教材付费</p>
      <div ref="qrCodeUrl" class="qrcode mb-10">
      </div>
      <p class="p3">微信扫码支付</p>
      <p class="p4" @click="bindBookShow=true;closePay()">兑换码兑换</p>
    </div>
    <NormalDialog
      :title="'支付成功'"
      :dialog-visible="dialogShow1"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      width="25vw"
      @closeDialog="closeSure"
    >
      <div style="width: 100%;">
        <img class="success" src="@/assets/pay/success.png" alt="" />
        <p class="p5">付款成功</p>
        <div class="sure" @click="closeSure">确定</div>
      </div>
    </NormalDialog>
    <BindBook
      v-if="bindBookShow"
      :show="bindBookShow"
      :append-to-body="true"
      @close="bindBookShow = false"
    />
  </NormalDialog>
</template>
<script>
import QRCode from 'qrcodejs2'
import { createOrders, prepay, getOrders } from '@/api/community-api.js'
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import BindBook from '@/views/digitalbooks/myDigitalbooks/components/Dialog/bindBook.vue'
export default {
  components: { NormalDialog, BindBook },
  props: {
    goodInfo: {
      type: Object,
      require: false,
      default: null
    }
  },
  data() {
    return {
      dialogShow: false,
      dialogShow1: false,
      bindBookShow: false,
      timer: null
    }
  },
  methods: {
    closeSure() {
      this.dialogShow1 = false
      setTimeout(() => {
        location.reload()
      }, 1000)
    },
    closePay() {
      this.dialogShow = false
      clearInterval(this.timer)
      this.timer = null
      this.$refs.qrCodeUrl.innerHTML = ''
    },
    show() {
      this.showTips(this.goodInfo)
    },
    close() {
      this.dialogShow = false
    },
    _getOrders (id) {
      getOrders({ orderId: id }).then(res => {
        if (res.data.orderStatus === 'TRADE_SUCCESS') {
          this.$message.success('支付成功')
          this.closePay()
          this.dialogShow1 = true
          setTimeout(() => {
          }, 1000)
        } else if (res.data.orderStatus !== 'WAIT_BUYER_PAY') {
          this.$message.warning('订单过期')
          this.closePay()
        } else if (res.data.orderStatus === 'TRADE_CANCELLED') {
          this.$message.warning('取消支付')
          this.closePay()
        }
      })
    },
    creatQrCode (data) {
      const url = data
      console.log(data)
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl, {
            width: 200, // 二维码宽度
            height: 200, // 二维码高度
            text: url, // 浏览器地址url
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
          const img = document.createElement('img')
          img.setAttribute('src', require('@/assets/pay/wechatpay.jpg'))
          img.classList.add('wchatpay')
          this.$refs.qrCodeUrl.appendChild(img)
        }
      )
    },
    showTips (item) {
      this.price = item.price
      if (this.price === null) {
        return
      }
      this.dialogShow2 = true
      this.$nextTick(() => {
        createOrders({ goodsId: item.id }).then(res => {
          if (res.data.orderStatus === 'TRADE_SUCCESS') {
            this.$message.success('购买成功')
            this.dialogShow = false
            setTimeout(() => {
              location.reload()
            }, 1000)
            return
          }
          this.dialogShow = true
          prepay({ ordersId: res.data.id, payMethod: 'WX' }).then(res1 => {
            this.creatQrCode(res1.data.qrCode)
            this.timer = setInterval(() => {
              this._getOrders(res.data.id)
            }, 3000)
          })
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.success{
    width: 50px;
    display: block;
    margin: 0 auto;
}
.sure{
    background: #2F80ED;
    color: #fff;
    width: 68px;
    height: 27px;
    text-align: center;
    line-height: 27px;
    font-size: var(--font-size-M);
    font-weight: 600;
    border-radius: 5px;
    cursor: pointer;
    margin: 0 auto;
    margin-top: 20px;
}
.p5{
    font-size: var(--font-size-M);
        color: #4F4F4F;
        text-align: center;
}
    .pay_main{
    text-align: center;
    .p1{
        font-size: var(--font-size-XXL);
        font-weight: 700;
        color: #000;
    }
    .p2{
        font-size: var(--font-size-S);
        color: #4F4F4F;
        height: 10px;
    }
    .p3{
        font-size: var(--font-size-S);
        font-weight: 700;
        color:#4F4F4F
    }
    .p4{
        font-size: var(--font-size-M);
        color: #2F80ED;
        margin-top: 20px;
        text-decoration: underline;
        cursor: pointer;
    }
    .subMit{
            width: 100px;
            height: 30px;
            line-height: 30px;
            margin: 0 auto;
            margin-top: 20px;
            background: linear-gradient(90deg, #84FAB0 0%, #8FD3F4 100%);
            text-align: center;
            border-radius: 3px;
            cursor: pointer;
        }
    }
    .qrcode{
    width: 120px;
    height: 120px;
    position: relative;
    ::v-deep img{
        width: 100%;
        height: 100%;
    }
    ::v-deep .wchatpay{
        width: 20px;
        height: 20px;
        position: absolute;
        left: 50px;
        top:50px;
    }
    }
</style>
