<template>
  <NormalDialog
    v-if="addCourseShow"
    width="30vw"
    :title="'添加教材'"
    :dialog-visible="addCourseShow"
    :is-center="true"
    :append-to-body="appendToBody"
    @closeDialog="close"
  >
    <div class="w flex-col">
      <div class="flex items-center mb10">
        <div>选择班级：</div>
        <el-select
          v-model="selectClassUserId"
          style="flex: 1"
          class="w h40"
          placeholder="请选择"
          :disabled="!!disableSelectClassUserId"
          @change="getCourseList"
          @focus="setOptionWidth"
        >
          <el-option
            v-for="item in tableData"
            :key="item.id"
            :style="{ width: selectOptionWidth }"
            :label="item.name"
            :value="item.userId"
          />
        </el-select>
      </div>
      <div class="flex items-center">
        <div>选择教材：</div>
        <el-select
          v-model="selectCrousUserId"
          style="flex: 1"
          class="w h40"
          :disabled="!selectClassUserId"
          placeholder="请选择"
          @focus="setOptionWidth"
        >
          <el-option
            v-for="item in crouseList"
            :key="item.id"
            :style="{ width: selectOptionWidth }"
            :label="item.title"
            :value="item.id"
            :disabled="!!item.studentCourseId"
          >
            <div class="flex w justify-between">
              <div class="select-box">
                <div class="item-scope w" style="display: inline-block">
                  {{ item.title }}
                </div>
              </div>
              <div class="tr select-add" :style="item.studentCourseId ? 'color: #636363;' : 'color: #3479FF;'">
                {{ item.studentCourseId ? '已添加' : '添加' }}
              </div>
            </div>
          </el-option>
        </el-select>
      </div>
      <div class="flex pt10">
        <div class="w70"></div>
        <div class="f12" style="color:#EB5757;">*如果没有教材，请先通过兑换码兑换教材再添加到班级</div>
      </div>
      <!-- <div class="flex items-start pt10">
        <div>计划授课：</div>
        <div class="flex flex-col mh-40 " style="flex: 1">
          <el-switch v-model="hasWeeks" class="w70" active-color="#3479FF" inactive-color="#e9eef5" />
          <div v-if="hasWeeks" class="week-box">
            <el-checkbox-group v-model="weeksList">
              <el-checkbox label="每周一" />
              <el-checkbox label="每周二" />
              <el-checkbox label="每周三" />
              <el-checkbox label="每周四" />
              <el-checkbox label="每周五" />
            </el-checkbox-group>
          </div>
        </div>
      </div> -->
    </div>
    <template #footer>
      <div class="edu-btn" @click="addCourse">确定</div>
    </template>

    <NormalDialog
      v-if="tipsShow"
      width="40vw"
      :title="'提示'"
      :dialog-visible="tipsShow"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="tipsShow = false"
    >
      <div class="w mb10 flex flex-col items-center">
        <div class="mb10 f20" style="color: #000;">该教材添加次数已用完</div>
        <img width="200" src="../../../assets/images/kefuCode.png" />
        <div class="f14 mt10">扫码联系客服增加教材使用额度</div>
      </div>
    </NormalDialog>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import { debounce } from '@/utils/index'
import { addCourseToClass, getUserClassList } from '@/api/educational-api.js'
import { assistantDigitalBooks } from '@/api/digital-api.js'
export default {
  components: { NormalDialog },
  props: {
    row: {
      type: Object,
      default: () => {
        return null
      }
    },
    show: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      selectOptionWidth: 200,
      hasWeeks: false,
      weeksList: [],
      crouseList: [],
      tableData: [],
      tableDataMap: null,
      selectCrousUserId: '',
      selectClassUserId: '',
      disableSelectClassUserId: false,
      tipsShow: false,
      addCourseShow: false,
      nowEditRow: null
    }
  },
  mounted () {
    this.addCourseShow = this.show
    this._getUserClassList()
  },
  methods: {
    close () {
      this.addCourseShow = false
      this.$emit('close')
    },
    handleAddCourse (row) {
      this.nowEditRow = row
      this.selectClassUserId = row.userId
      this.getCourseList(row.userId)
      this.disableSelectClassUserId = true
    },
    async _getUserClassList () {
      const { data } = await getUserClassList()
      this.tableData = data
      const map = new Map()
      for (let i = 0; i < data.length; i++) {
        map.set(data[i].userId, data[i])
      }
      this.tableDataMap = map
      if (this.row) {
        this.handleAddCourse(this.row)
      }
    },
    addCourse: debounce(async function () {
      if (!(this.nowEditRow && this.nowEditRow.userId && this.selectCrousUserId)) {
        this.$message.error('有内容未填写')
        return
      }
      const obj = {
        studentId: this.nowEditRow.userId,
        courseId: this.selectCrousUserId,
        // courseType: this.nowCourseList.get(this.selectCrousUserId)
        courseType: 'DIGITAL_BOOK'
        // weekdays: ''
      }
      if (this.hasWeeks) {
        if (this.weeksList.length === 0) {
          this.$message.error('有内容未填写')
          return
        }
        const srtObj = {
          '每周一': 1,
          '每周二': 2,
          '每周三': 3,
          '每周四': 4,
          '每周五': 5,
          '每周六': 6,
          '每周日': 7
        }
        const arr = []
        for (let i = 0; i < this.weeksList.length; i++) {
          arr.push(srtObj[this.weeksList[i]])
        }
        obj.weekdays = arr.join(',')
      }
      try {
        await addCourseToClass(obj)
        this.addCourseShow = false
        this.$emit('added')
        this.$message.success('添加成功')
      } catch (error) {
        if (error.code === 910) {
          this.tipsShow = true
        }
      }
    }, 3000, true),
    async getCourseList (val) {
      this.nowEditRow = this.tableDataMap.get(val)
      const { data } = await assistantDigitalBooks({
        classUserId: val
      })
      const map = new Map()
      data.forEach(element => {
        map.set(element.courseId, element.courseType)
      })
      this.nowCourseList = map
      this.crouseList = data
    },
    setOptionWidth (event) {
      // 下拉框弹出时，设置弹框的宽度
      this.$nextTick(() => {
        // this.selectOptionWidth = event.srcElement.offsetWidth + 'px'
        this.selectOptionWidth = 35 + 'vw'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}

.item-scope {
  width: 100%;
  @include ellipses(1);
}

.select-box {
  width: calc(100% - 40px);
}

.select-add {
  width: 40px;
}

.tr {
  text-align: right;
}

.pt10 {
  padding-top: 10px;
}

.w70 {
  width: 70px;

  ::v-deep .el-switch__core {
    width: 40px !important;
  }
}

.f12 {
  font-size: 12px;
}

.mh-40 {
  min-height: 40px;
}

.week-box {
  padding-top: 20px;
  max-width: 50px;
}

.h40 {
  height: 40px;
  ::v-deep .el-input__inner {
    height: 40px !important;
  }
}
</style>
