<template>
  <NormalDialog
    v-if="guidShow"
    width="30vw"
    :title="'双师AI课开课指引'"
    :dialog-visible="guidShow"
    :is-center="true"
    @closeDialog="close"
  >
    <div class="guide-box">
      <el-timeline style="padding-inline-start:0px">
        <el-timeline-item>
          <div class="flex justify-between items-center">
            <div class="guide-intro">激活学校课程</div>
            <div
              :class="{ 'classpro-btn': !school, 'classpro-btn-disable': school }"
              @click="school ? '' : handleClick('1')"
            >{{ school ? '已激活' : '激活课程' }}</div>
          </div>
        </el-timeline-item>
        <el-timeline-item>
          <div class="flex justify-between items-center">
            <div class="guide-intro">创建班级</div>
            <div class="classpro-btn" @click="handleClick('2')">添加班级</div>
          </div>
        </el-timeline-item>
        <el-timeline-item>
          <div class="flex justify-between items-center">
            <div class="guide-intro">添加班级课程</div>
            <div class="classpro-btn" @click="handleClick('3')">添加课程</div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <bindSchool v-if="bindSchoolShow" :show="bindSchoolShow" :append-to-body="true" @close="bindSchoolShow = false" />
    <jionClass v-if="addClassShow" :show="addClassShow" :append-to-body="true" @close="addClassShow = false" />
    <addCourse v-if="addCourseShow" :show="addCourseShow" :append-to-body="true" @close="addCourseShow = false" />
  </NormalDialog>
</template>

<script>
import bindSchool from './bindSchool.vue'
import jionClass from './jionClass.vue'
import addCourse from './addCourse.vue'
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import { debounce } from '@/utils/index'
import { mapGetters } from 'vuex'
export default {
  components: {
    NormalDialog,
    bindSchool,
    jionClass,
    addCourse
  },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      guidShow: false,
      addClassShow: false,
      bindSchoolShow: false,
      addCourseShow: false
    }
  },
  computed: {
    ...mapGetters([
      'school',
      'id'
    ])
  },
  mounted () {
    this.$nextTick(() => {
      this.guidShow = this.show
    })
  },
  methods: {
    close () {
      this.guidShow = false
      this.$emit('close')
    },
    handleClick: debounce(async function (type) {
      switch (type) {
        case '1':
          this.bindSchoolShow = true
          break
        case '2':
          if (this.school) {
            this.addClassShow = true
          } else {
            this.$message('请先绑定学校')
          }
          break
        case '3':
          if (this.school) {
            this.addCourseShow = true
          } else {
            this.$message('请先绑定学校')
          }
          break

        default:
          break
      }
      // window.localStorage.setItem('eduType', type)
    }, 1000, true)
  }
}
</script>

<style lang="scss" scoped>
.guide-box {
  width: 285px;
  // height: 390px;
  background-size: contain;
  position: relative;
  padding: 30px 10px 0;

  .classpro-btn,
  .classpro-btn-disable {
    width: 70px;
    height: 30px;
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF;
    letter-spacing: 0;
  }
}
</style>
