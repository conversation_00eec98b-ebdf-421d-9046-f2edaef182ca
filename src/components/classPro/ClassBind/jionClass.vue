<template>
  <div>
    <NormalDialog
      v-if="addClassShow"
      width="30vw"
      :title="'添加班级'"
      :dialog-visible="addClassShow"
      :append-to-body="appendToBody"
      :is-center="true"
      @closeDialog="close"
    >
      <div class="w flex justify-around">
        <div class="edu-btn-opacity" @click="enterAdminClassShow = true">
          加入行政班
        </div>
        <div class="edu-btn-opacity" @click="enterInterestClassShow = true">
          添加兴趣班
        </div>
      </div>
    </NormalDialog>

    <NormalDialog
      v-if="enterAdminClassShow"
      width="30vw"
      :title="'加入行政班'"
      :dialog-visible="enterAdminClassShow"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="
        enterAdminClassShow = false
        clearItem()
      "
    >
      <div class="w flex-col">
        <div class="flex items-center mb10">
          <div>选择年级：</div>
          <el-select
            v-model="gradeValue"
            style="flex: 1"
            class="w"
            placeholder="请选择"
            @focus="setOptionWidth"
          >
            <el-option
              v-for="item in gradeOption"
              :key="item.gradName"
              :style="{ width: selectOptionWidth }"
              :label="item.gradName"
              :value="`${item.year},${item.level}`"
            />
          </el-select>
        </div>
        <div class="flex items-center">
          <div>选择班级：</div>
          <el-select
            v-model="teamValue"
            style="flex: 1"
            class="w"
            placeholder="请选择"
            @focus="setOptionWidth"
          >
            <el-option
              v-for="item in classNameOption"
              :key="item.value"
              :style="{ width: selectOptionWidth }"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="addClass('ORGANIZATION')">确定</div>
      </template>
    </NormalDialog>

    <NormalDialog
      v-if="enterInterestClassShow"
      width="30vw"
      :title="'添加兴趣班'"
      :dialog-visible="enterInterestClassShow"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="
        enterInterestClassShow = false
        clearItem()
      "
    >
      <div class="w flex-col">
        <div class="flex items-center mb10">
          <div>班级名称：</div>
          <el-input v-model="nameValue" style="flex: 1" class="w" placeholder="请输入班级名称" />
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="addClass('INTEREST')">确定</div>
      </template>
    </NormalDialog>
    <NormalDialog
      v-if="codeShow"
      width="30vw"
      :title="codeInfo && codeInfo.name"
      :dialog-visible="codeShow"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="codeShow = false; $emit('close')"
    >
      <div class="w flex flex-col items-center">
        <div ref="bill" class="w flex flex-col items-center">
          <div class="code-share-title flex items-center">{{ codeInfo && codeInfo.name }}</div>
          <div ref="qrCodeUrl" class="qrcode mb-10"></div>
          <div class="code-share flex items-center">
            班级二维码，保存或截图分享
          </div>
        </div>
        <div class="mt20 flex justify-center">
          <el-button type="text" class="f16" @click="saveImg">保存</el-button>
        </div>
      </div>
    </NormalDialog>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { debounce } from '@/utils/index'
import { assistantAddClass, getUserClassList } from '@/api/educational-api.js'
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'

export default {
  components: { NormalDialog },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: false
    },
    needShowCode: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      selectOptionWidth: 200,
      gradeValue: '',
      enterAdminClassShow: false,
      enterInterestClassShow: false,
      addClassShow: false,
      teamValue: '',
      nameValue: '',
      codeInfo: null,
      codeShow: false
    }
  },
  computed: {
    ...mapGetters([
      'school',
      'id'
    ]),
    gradeOption () {
      const mouth = moment().month() + 1
      let year = moment().year()
      if (mouth < 9) {
        year = year - 1
      }
      const arr = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']
      // 初中
      const arr2 = ['七年级', '八年级', '九年级']
      const obj = []
      arr.forEach((val, index) => {
        obj.push({
          gradName: val + (year - index),
          year: year - index,
          grad: val,
          level: index + 1
        })
      })
      arr2.forEach((val, index) => {
        obj.push({
          gradName: val + (year - index),
          year: year - index,
          grad: val,
          level: 6 + index + 1
        })
      })
      return obj
    },
    classNameOption () {
      const obj = []
      for (let i = 1; i < 51; i++) {
        obj.push({
          value: i,
          label: i + '班'
        })
      }
      return obj
    }
  },
  mounted () {
    this.addClassShow = this.show
  },
  methods: {
    close () {
      this.addClassShow = false
      this.$emit('close')
    },
    addClass: debounce(async function (type) {
      const obj = {
        type: type
      }
      if (type === 'INTEREST') {
        if (!this.nameValue) {
          this.$message.error('请填写内容')
          return
        }
        obj.name = this.nameValue
      } else if (type === 'ORGANIZATION') {
        if (!(this.teamValue && this.gradeValue)) {
          this.$message.error('请填写内容')
          return
        }
        obj.grade = this.gradeValue.split(',')[0]
        obj.level = this.gradeValue.split(',')[1]
        obj.team = this.teamValue
      }
      const { data } = await assistantAddClass(obj)
      this.enterInterestClassShow = false
      this.enterAdminClassShow = false
      this.clearItem()
      this._getUserClassList()

      if (this.needShowCode) {
        // 显示二维码
        this.$nextTick(() => {
          this.creatQrCode(data)
        })
      } else {
        this.$emit('close')
      }
    }, 3000, true),
    async _getUserClassList () {
      const { data } = await getUserClassList()
      this.tableData = data
      const map = new Map()
      for (let i = 0; i < data.length; i++) {
        map.set(data[i].userId, data[i])
      }
      this.tableDataMap = map
    },
    clearItem () {
      this.value = ''
      this.gradeValue = ''
      this.teamValue = ''
      this.nameValue = ''
    },
    creatQrCode (row) {
      console.log(1)
      const origin = window.location.origin
      const url = `${origin}/#/parent/hassClassInfo?isScan=1&classId=${row.userId}&className=${row.name}&schoolId=${row.school && row.school.id}&schoolName=${row.school && row.school.name}`
      this.codeShow = true
      this.codeInfo = row
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl, {
            width: 200, // 二维码宽度
            height: 200, // 二维码高度
            text: url, // 浏览器地址url
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
        }
      )
    },
    saveImg () {
      this.saveImage()
    },
    saveImage () {
      const canvasID = this.$refs.bill
      const that = this
      const a = document.createElement('a')
      html2canvas(canvasID).then((canvas) => {
        const dom = document.body.appendChild(canvas)
        dom.style.display = 'none'
        a.style.display = 'none'
        document.body.removeChild(dom)
        const blob = that.dataURLToBlob(dom.toDataURL('image/png'))
        a.setAttribute('href', URL.createObjectURL(blob))
        // 这块是保存图片操作  可以设置保存的图片的信息
        a.setAttribute('download', that.codeInfo.name + '.png')
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(blob)
        document.body.removeChild(a)
      })
    },
    dataURLToBlob (dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    setOptionWidth (event) {
      // 下拉框弹出时，设置弹框的宽度
      this.$nextTick(() => {
        this.selectOptionWidth = event.srcElement.offsetWidth + 'px'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}
</style>
