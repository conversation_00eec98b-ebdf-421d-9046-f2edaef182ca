<template>
  <div>
    <NormalDialog
      v-if="bindSchoolShow"
      width="30vw"
      :title="'完善学校信息'"
      :dialog-visible="bindSchoolShow"
      :is-center="true"
      :append-to-body="appendToBody"
      @closeDialog="close"
    >
      <div class="flex flex-col">
        <div class="mb10">
          <el-input v-model="schoolCode" class="w" placeholder="请输入学校课程激活码" />
        </div>
        <div style="color: #828282">
          备注：
          <div class="school-disc flex items-center">
            <div class="school-disc2"></div>
            仅支持已合作的学校
          </div>
          <div class="school-disc flex items-center">
            <div class="school-disc2"></div>
            如没有激活码，请联系学校管理员获取
          </div>
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="bindSchool">确定</div>
      </template>
    </NormalDialog>

    <ComponentDialog
      :width="'30vw'"
      :title="'提示'"
      :dialog-visible="confirmShow"
      :is-center="true"
      @closeDialog="confirmShow = false"
    >
      <div class="flex flex-col w items-center">
        <div class="f14" style="margin-bottom: 30px; line-height: 40px">
          确认加入{{ schoolName }}?
        </div>
        <div class="flex justify-around w">
          <div class="edu-btn-opacity f14" @click="confirmShow = false">取消</div>
          <div class="edu-btn f14" @click="confirmBind">确定</div>
        </div>
      </div>
    </ComponentDialog>
  </div>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import ComponentDialog from '@/components/classPro/ComponentDialog'
import { debounce } from '@/utils/index'
import { assistantBindSchool } from '@/api/educational-api.js'
import { getSchoolInfo } from '@/api/digital-api.js'

export default {
  components: { NormalDialog, ComponentDialog },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      schoolCode: '',
      bindSchoolShow: false,
      confirmShow: false,
      schoolName: ''
    }
  },
  mounted () {
    this.bindSchoolShow = this.show
  },
  methods: {
    close () {
      this.bindSchoolShow = false
      this.$emit('close')
    },
    bindSchool: debounce(async function () {
      if (this.schoolCode) {
        try {
          const res = await getSchoolInfo({
            code: this.schoolCode
          })
          if (res.data) {
            this.schoolName = res.data.name
            this.confirmShow = true
          } else {
            this.$message.error('激活失败')
          }
        } catch (error) {
          if (error !== 'cancel') {
            console.log(error)
          }
        }
      }
    }, 3000, true),
    async confirmBind () {
      try {
        await assistantBindSchool({
          schoolCode: this.schoolCode
        })
        this.schoolCode = ''
        this.$store.dispatch('user/GetInfo')
        this.$emit('close')
        this.bindSchoolShow = false
        this.confirmShow = false
        this.$message.success('激活成功')
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.school-disc {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.school-disc2 {
  width: 5px;
  height: 5px;
  background: #828282;
  border-radius: 50%;
  margin-right: 5px;
}
</style>
