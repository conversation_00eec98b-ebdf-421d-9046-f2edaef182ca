<template>
  <NormalDialog
    v-if="bindShow"
    width="35vw"
    :title="'填写申请信息'"
    :dialog-visible="bindShow"
    :is-center="true"
    :append-to-body="appendToBody"
    @closeDialog="close"
  >
    <div class="flex flex-col">
      <div class="mb10">
        <span class="mr10">所在地区</span>
        <el-cascader
          v-model="selectedOptions"
          class="input-box"
          size="large"
          :options="pcaTextArr"
        />
      </div>
      <div class="mb10">
        <span class="mr10">学校名称</span>
        <el-input v-model="schoolName" class="input-box" placeholder="填写学校全名" />
      </div>
      <div class="mb10">
        <span class="mr10">选择年级</span>
        <el-select
          v-model="gradeValue"
          class="input-box"
          style="flex: 1"
          placeholder="请选择"
          @focus="setOptionWidth"
        >
          <el-option
            v-for="item in gradeOption"
            :key="item.gradName"
            :style="{ width: selectOptionWidth }"
            :label="item.gradName"
            :value="`${item.year},${item.level}`"
          />
        </el-select>
      </div>
      <div class="mb10">
        <span class="mr10">选择班级</span>
        <el-select
          v-model="teamValue"
          style="flex: 1"
          class="input-box"
          placeholder="请选择"
          @focus="setOptionWidth"
        >
          <el-option
            v-for="item in classNameOption"
            :key="item.value"
            :style="{ width: selectOptionWidth }"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="mb10">
        <span class="mr10">老师姓名</span>
        <el-input v-model="userName" class="input-box" placeholder="填写老师姓名" />
      </div>
      <div class="mb10">
        <span class="mr10">手机号码</span>
        <el-input v-model="phone" :disabled="!!phone" class="input-box" placeholder="请输入手机号" />
      </div>

      <div style="color: #828282">
        说明：提交申请后等待审核，通过后会发送短信通知
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="bind">提交申请</div>
    </template>
  </NormalDialog>
</template>

<script>
import moment from 'moment'
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import { debounce } from '@/utils/index'
import { pcaTextArr } from 'element-china-area-data'
import { mapGetters } from 'vuex'
import { courseApply } from '@/api/course-api.js'
export default {
  components: { NormalDialog },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: true
    },
    courseId: {
      type: [String, Array],
      default: ''
    }
  },
  data () {
    return {
      schoolCode: '',
      bindShow: false,
      selectOptionWidth: 200,
      gradeValue: '',
      teamValue: '',
      pcaTextArr,
      selectedOptions: [],
      schoolName: '',
      userName: '',
      phone: ''
    }
  },
  computed: {
    ...mapGetters([
      'mobile',
      'name'
    ]),
    gradeOption () {
      const mouth = moment().month() + 1
      let year = moment().year()
      if (mouth < 9) {
        year = year - 1
      }
      const arr = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']
      // 初中
      const arr2 = ['七年级', '八年级', '九年级']
      const obj = []
      arr.forEach((val, index) => {
        obj.push({
          gradName: val + (year - index),
          year: year - index,
          grad: val,
          level: index + 1
        })
      })
      arr2.forEach((val, index) => {
        obj.push({
          gradName: val + (year - index),
          year: year - index,
          grad: val,
          level: 6 + index + 1
        })
      })
      return obj
    },
    classNameOption () {
      const obj = []
      for (let i = 1; i < 51; i++) {
        obj.push({
          value: i,
          label: i + '班'
        })
      }
      return obj
    }
  },
  mounted () {
    this.bindShow = this.show
    this.userName = this.name
    this.phone = this.mobile
    this.getApplyInfo()
  },
  methods: {
    close () {
      this.bindShow = false
      this.$emit('close')
    },
    async getApplyInfo () {
      try {
        const obj = {
          apiType: 'get'
        }
        if (this.courseId) {
          obj.courseIds = this.courseId
        }
        await courseApply(obj)
      } catch (error) {
        this.close()
      }
    },
    bind: debounce(async function () {
      if (this.selectedOptions.length > 0 && this.schoolName && this.gradeValue && this.teamValue && this.userName && this.phone) {
        const obj = {
          apiType: 'create',
          mobile: this.phone,
          userName: this.userName,
          schoolName: this.schoolName
        }
        if (this.courseId) {
          obj.courseIds = this.courseId
        }
        if (this.selectedOptions.length > 0) {
          const province = this.selectedOptions[0]
          let city = this.selectedOptions[1]
          const district = this.selectedOptions[2]
          if (city === '市辖区') {
            // 和手机端选择一致
            city = province
          }
          obj.province = province
          obj.city = city
          obj.district = district
        }

        obj.grade = this.gradeValue.split(',')[0]
        obj.level = this.gradeValue.split(',')[1]
        obj.team = this.teamValue
        this.$message.success('已提交')
        await courseApply(obj)
        this.close()
      } else {
        this.$message.error('有内容未填写')
      }
      // this.$alert('您已经提交申请，请耐心等待！', '', {
      //   confirmButtonText: '确定',
      //   callback: action => {

      //   }
      // })
    }, 2000, true),
    setOptionWidth (event) {
      // 下拉框弹出时，设置弹框的宽度
      this.$nextTick(() => {
        this.selectOptionWidth = event.srcElement.offsetWidth + 'px'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.school-disc {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.mr10 {
  margin-right: 10px;
}

.input-box {
  width: calc(100% - 90px);
}

.school-disc2 {
  width: 5px;
  height: 5px;
  background: #828282;
  border-radius: 50%;
  margin-right: 5px;
}
</style>
