<template>
  <div class="w h h5-video">
    <video
      ref="videoPlayer"
      playsinline
      webkit-playsinline
      x5-video-player-type="h5"
      x5-playsinline="true"
      x5-video-orientation="portraint"
      class="video-js vjs-big-play-centered"
      style="width: 100%; height: 100%; objectFit:contain;"
    ></video>
  </div>
</template>

<script>
import videojs from 'video.js'
// import 'videojs-contrib-hls'
import 'video.js/dist/video-js.css'
export default {
  props: {
    options: {
      type: Object,
      default () {
        return {
          preload: 'auto',
          controls: true,
          autoplay: true,
          loop: false
        }
      }
    }
  },
  data () {
    return {
      player: null
    }
  },
  mounted () {
    this.player = videojs(this.$refs.videoPlayer, this.options, () => {
      this.player.playsinline(true)
      this.$emit('player', this.player)
    })
  },
  beforeDestroy () {
    if (this.player) {
      this.player.dispose()
    }
  },
  methods: {
    play () {
      this.player.play()
    },
    pause () {
      this.player.pause()
    },
    setTime (time) {
      this.player.currentTime(time)
    }
  }
}
</script>

<style lang="scss" scoped>
.video-js {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
<style lang="scss">
.h5-video {
  .video-js .vjs-time-control{display:block;}
  .video-js .vjs-remaining-time{display: none;}
  .vjs-paused .vjs-big-play-button,
  .vjs-paused.vjs-has-started .vjs-big-play-button {
      display: block;
  }
}
</style>
