<template>
  <div class="game-main" v-if="showGame">
    <iframe
      id="scratch-iframe"
      ref="scratchFrame"
      :src="iframeUrl"
      style="border: none"
      width="100%"
      height="100%"
      allowfullscreen
      allow="microphone *; camera *"
      sandbox="allow-same-origin allow-scripts allow-popups allow-modals"
    ></iframe>
  </div>
</template>

<script>
export default {
  props: {
    studentCourseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      iframeUrl: '',
      showGame: false
    }
  },
  mounted () {
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy () {
    window.removeEventListener('message', this.handleMessage)
  },
  methods: {
    handleMessage (e) {
      if (e.data.type === 'gameBack') {
        this.showGame = false
        this.iframeUrl = ''
        this.$emit('close')
      }
    },
    open(trainingInfo) {
      this.showGame = true
      const basicUrl = trainingInfo.practice_url
      // let basicUrl = "http://localhost:8881/#/game/faceIdentification"
      switch (trainingInfo.trainingType) {
        case 'TRAFFIC_VIOLATION_DETECTION_PRACTICE':
        case 'FACE_RECOGNITION_PRACTICE':
        case 'OBJECT_DETECTION_PRACTICE':
        case 'IMAGE_RECOGNITION_PRACTICE':
        case 'GESTURE_SNAKE_PRACTICE':
        case 'GESTURE_RPS_PRACTICE':
          this.iframeUrl = `${basicUrl}?trainingId=${trainingInfo.trainingId}&studentCourseId=${this.studentCourseId}&back=1&trainingName=${trainingInfo.trainingName}`
          break
        case 'LICENSE_PLATE_RECOGNITION_PRACTICE':
          this.iframeUrl = `${basicUrl}?type=car&trainingId=${trainingInfo.trainingId}&studentCourseId=${this.studentCourseId}&back=1&trainingName=${trainingInfo.trainingName}`
          break
        default:
          this.iframeUrl = `${basicUrl}?trainingId=${trainingInfo.trainingId}&studentCourseId=${this.studentCourseId}&back=1&trainingName=${trainingInfo.trainingName}`
          break
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.game-main{
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: white;
}
</style>
