<template>
  <div class="python-main" v-if="showPython">
    <div class="header_view">
      {{ experimentTitle }}
      <div v-if="backShow" class='back' @click='back'>
        <i class="el-icon-arrow-left"></i>
        返回
      </div>
      <div class='option'>
        <div class='option_btn' @click='openDialog'>
          <i class="el-icon-document svgIcon"></i>
          实验说明
        </div>
      </div>
    </div>
    <div class='content_view'>
      <iframe
        id="scratch-iframe"
        ref="scratchFrame"
        :src="iframeUrl"
        style="border: none"
        width="100%"
        height="100%"
        allowfullscreen
        allow="microphone *; camera *"
        sandbox="allow-same-origin allow-scripts allow-popups allow-modals"
      ></iframe>
    </div>
    <NormalDialog
      v-if="dialogShow"
      :title="experimentTitle"
      :dialog-visible="dialogShow"
      width='60vw'
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog="closeDialog"
    >
      <div class="explanation_main" v-loading="loading">
        <div class="explanation_left ">
          <video v-if="videoUrl && videoUrl !== ''" :src="videoUrl" style="width: 100%;height: 50%;border-radius: 20px" controls :poster="videoPoster"></video>
          <el-image v-else style="width: 100%;height: 50%;border-radius: 20px" :src="DefaultCover" fit="cover"/>
          <div class='know_btn' @click='dialogShow = false'>知道了</div>
        </div>
        <div class="explanation_right">
          <div class="title">实验说明:</div>
          <div class="des" v-html="trainingData ? trainingData.description : ''">
          </div>
        </div>
      </div>
    </NormalDialog>
  </div>
</template>

<script>
import DefaultCover from '@/assets/scratch/explanationDefault.png'
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import { getToken } from 'utils/auth'
import { getTraining } from '@/api/training-api'

export default {
  name: 'PythonView',
  components: {
    NormalDialog
  },
  props: {
    studentCourseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      DefaultCover,
      iframeUrl: '/jupyterhub/hub/logout',
      showPython: false,
      dialogShow: false,
      token: '',
      loading: false,
      trainingData: null,
      experimentTitle: '',
      backShow: true,
      trainingId: 0
    }
  },
  mounted() {
    this.token = this.$route.query && this.$route.query.token ? `Bearer ${this.$route.query.token}` : getToken()
    if (this.$route.query && this.$route.query.back === '0') {
      this.backShow = false
    }
    if (this.$route.query && this.$route.query.trainingId) {
      this.trainingId = this.$route.query.trainingId
      this.showPython = true
      this.getTrainingInfo()
    }
  },
  methods: {
    open(id) {
      this.showPython = true
      this.trainingId = id
      this.getTrainingInfo()
    },
    back() {
      this.showPython = false
      this.trainingData = null
      this.$emit('close')
    },
    async getTrainingInfo() {
      this.loading = true
      try {
        const { data } = await getTraining({
          trainingId: this.trainingId,
          studentCourseId: this.studentCourseId
        }, { authorization: this.token })
        if (!data) {
          this.$message.warning('该实验已被删除')
          this.back()
          return
        }
        this.trainingData = data
        this.videoUrl = data.descriptionVideo
        this.scratchFile = data.scratchFile
        this.experimentTitle = data.trainingName
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    closeDialog() {
      this.dialogShow = false
    },
    openDialog() {
      this.dialogShow = true
    }
  }
}
</script>

<style scoped lang='scss'>
.python-main{
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: white;
  .header_view{
    width: 100%;
    height: 40px;
    font-size: var(--font-size-XXL);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    //background-color: #ebebeb;
    .back{
      position: absolute;
      left: 15px;
      font-size: var(--font-size-XL);
      cursor: pointer;
    }
    .option{
      position: absolute;
      right: 15px;
      font-size: var(--font-size-XL);
      display: flex;
      .option_btn{
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        font-size: var(--font-size-M);
        margin-left: 10px;
      }
      .svgIcon{
        font-size: 20px;
      }
    }
  }
  .content_view{
    width: 100%;
    height: calc(100% - 40px);
  }
}
.explanation_main{
  width: 100%;
  height: 50vh;
  display: flex;
  .explanation_left{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .know_btn{
      height: 40px;
      width: 50%;
      color: white;
      background: linear-gradient(90deg, #36D1DC 0%, #5B86E5 100%);
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 5px;
      margin-top: 10vh;
    }
  }
  .explanation_right{
    width: 100%;
    height: 100%;
    font-size: var(--font-size-L);
    padding-left: 20px;
    .title{
      font-weight: 600;
      margin-bottom: 5px;
      height: 30px;
    }
    .des{
      padding-left: 5px;
      overflow-y: auto;
      width: 100%;
      height: calc(100% - 35px);
    }
  }
}
</style>
