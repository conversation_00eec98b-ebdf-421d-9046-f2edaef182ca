<template>
  <div class="setting-pop">
    <el-popover
      ref="settingPopOver"
      v-model="isShow"
      :popper-class="isElectron ? 'e-settingPopOver' : 'settingPopOver'"
      placement="bottom"
      trigger="click"
      :visible-arrow="false"
      :popper-options="{
        boundariesElement: 'body'
      }"
    >
      <div slot="reference" class="bloc-setting">
        <!-- <img class="setting-icon" src="@/assets/images/Navbar/icon-setting-blue.png" /> -->
        <!-- <div class="top"><div class="setting-icon"></div></div> -->
<!--        <i class="iconfont icon-a-******************************************"></i>-->
        <svg-icon
          class="icon-a-******************************************"
          icon-class="set"
          class-name="set"
        />
        <p>设置</p>
      </div>
      <div class="setting">
        <p @click="goToNetworkCheck">设备检测</p>
        <template v-if="isElectron">
          <div class="e-line"></div>
          <p @click="handleUpdateClick">检查更新</p>
          <!-- <div class="e-line"></div>
          <p @click="handleWebClick">访问网页</p> -->
        </template>
        <template v-else>
          <div class="line"></div>
          <p @click="downLoadPc">下载PC版</p>
        </template>
      </div>
    </el-popover>
    <update-dialog-2 />
  </div>
</template>
<script>
const ipc = window.ipc
import UpdateDialog2 from '@/components/classPro/UpdateDialog/Update2.vue'
export default {
  components: { UpdateDialog2 },
  data () {
    return {
      isShow: false,
      isElectron: !!ipc
    }
  },
  methods: {
    goToNetworkCheck () {
      this.isShow = false
      this.$router.push({ path: '/network/network-check' })
    },
    handleUpdateClick () {
      this.$store.dispatch('app/setUpdate')
    },
    handleWebClick () {
      console.log(1)
      const ipc = window.ipc
      if (ipc) {
        ipc.send('open-url', process.env.VUE_APP_WEB_URL)
      }
    },
    downLoadPc () {
      window.open(process.env.VUE_APP_DOWNLOAD_PC_URL, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.setting-pop {
  .bloc-setting {
    //height: 40px;
    margin-right: 10px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;

    p {
      font-size: var(--font-size-L);
      font-weight: 400;
      color: #0B0B0B;
      margin: 0;
    }

    .icon-a-****************************************** {
      width: 20px;
      height: 20px;
      font-size: 16px;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #909399;
    }

    &:hover {
      .setting-icon {
        height: 24px;
        //background: url('../../../assets/images/Navbar/icon-setting-blue.png') center center no-repeat;
        background-size: contain;
      }

      .icon-a-****************************************** {
        color: #1F66FF;
        background: #E9F0FF;
      }

      p {
        color: #1F66FF;
      }
    }
  }
}
.setting {
  p {
    font-size: 14px;
    font-weight: 400;
    color: #0B0B0B;
    line-height: 20px;
    margin: 0px;
    padding-left: 3px;
    cursor: pointer;
  }

  .line {
    width: 100%;
    height: 1px;
    opacity: 0.16;
    border: 1px solid #979797;
    margin: 11px 0;
  }
  .e-line {
    width: 100%;
    height: 1px;
    opacity: 0.16;
    border: 1px solid #979797;
    margin: 3px 0;
  }

  .quit-btt {
    cursor: pointer;
    width: 100%;
    height: 30px;
    background: #1F66FF;
    box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
    border-radius: 16px;
    margin-top: 18px;
    .blur {
      position: absolute;
      width: 58px;
      height: 30px;
      background: linear-gradient(124deg, rgba(255, 255, 255, 0.74) 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0.51;
      filter: blur(1px);
    }
    .quit {
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 28px;
      text-align: center;
      z-index: 10;
    }
    &:hover {
      background: #6193FF;
      box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41);
    }
  }
}
</style>

<style lang="scss">
.el-popover.settingPopOver {
  width: 196px;
  height: 106px ;
  background: url('../../../assets/images/Navbar/bg-setting.png') no-repeat;
  background-size: cover;
  padding: 25px 13px 0 !important;
  border: 0;
  box-shadow: 0 0 0 0;
  top: -10px !important;
  left: 7px !important;
}

.el-popover.e-settingPopOver {
  width: 196px;
  height: 106px ;
  background: url('../../../assets/images/Navbar/bg-setting.png') no-repeat;
  background-size: cover;
  padding: 18px 13px 0 !important;
  border: 0;
  box-shadow: 0 0 0 0;
  top: -10px !important;
  left: 7px !important;
}
</style>
