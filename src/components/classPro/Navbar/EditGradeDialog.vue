<template>
  <comp-dialog
    :dialog-visible="dialogVisible"
    title="修改班级名称"
    :is-center="false"
    :append-to-body="true"
    @closeDialog="closeEditGradeDialog"
  >
    <div class="edit-grade-box">
      <el-form
        ref="editInfoForm"
        :model="editInfoForm"
        :rules="editInfoRules"
        class="edit-name-form"
      >
        <el-form-item prop="childName">
          <el-input v-model="editInfoForm.childName" type="text" auto-complete="off" maxlength="25" placeholder="请输入新昵称" />
        </el-form-item>
        <el-form-item>
          <div
            :class="{'submit-btt': valid, 'not-submit-btt': !valid}"
            @click="handleEdit"
          >
            <span>确认修改</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </comp-dialog>
</template>

<script>
import { changeChildName } from '@/api/user-api'
import CompDialog from '@/components/classPro/ComponentDialog'
export default {
  components: {
    CompDialog
  },
  props: {
    childUserId: {
      type: Number,
      require: true,
      default: 0
    },
    childName: {
      type: String,
      require: true,
      default: ''
    },
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    }
  },
  data () {
    const validateDisplayname = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('请输入昵称'))
      } else {
        callback()
      }
    }
    return {
      editInfoForm: {
        childName: this.childName
      },
      editInfoRules: {
        childName: [{
          required: true,
          trigger: 'blur',
          validator: validateDisplayname
        }]
      },
      loading: false
    }
  },
  computed: {
    valid () {
      return (this.editInfoForm.childName && this.editInfoForm.childName.length > 0)
    }
  },
  watch: {
    dialogVisible: {
      handler: function (val) {
        if (val) this.editInfoForm.childName = this.childName
      },
      immediate: true
    }
  },
  methods: {
    handleEdit () {
      if (!this.loading && this.valid) {
        this.$refs.editInfoForm.validate(async valid => {
          if (valid) {
            this.loading = true
            var param = {
              'childUserId': this.childUserId,
              'childName': this.editInfoForm.childName
            }
            await changeChildName(param)
              .then(response => {
                this.$emit('editSuccess')
                this.$emit('closeEditGradeDialog')
              })
            this.loading = false
          }
        })
      }
    },
    closeEditGradeDialog () {
      this.$emit('closeEditGradeDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-grade-box {
    width: 100%;
    background: white;
    position: relative;

    .button {
        background: url('../../../assets/images/login/login-btn-blue.png') center center no-repeat;
        background-size: cover;
        width: 100%;
        height: 30px;
        font-size: 16px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 30px;
        text-align: center;
        margin: 31px auto 0;
        cursor: pointer;
        border-radius: 240px;
    }

    .submit-btt {
        width: 100%;
        height: 30px;
        background: #1F66FF;
        box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
        border-radius: 16px;
        font-size: 16px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 30px;
        margin: 0 auto;
        text-align: center;
        cursor: pointer;
    }
    .not-submit-btt {
        width: 100%;
        height: 30px;
        background: #BFBFBF;
        box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), 1px 1px 1px 0px #E7E7E7, -1px -1px 1px 0px rgba(129, 129, 134, 0.32);
        border-radius: 16px;
        font-size: 16px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 32px;
        margin: 0 auto;
        text-align: center;
        cursor: pointer;
    }

    .edit-name-form {
        border-radius: 6px;
        width: 100%;

        ::v-deep .el-input {
        height: 38px;

        input {
            margin-top: 30px;
            height: 38px;
            border: none;
            border-bottom: 1px solid rgba(151, 151, 151, 0.15);
        }

        ::v-deep .el-input__prefix {
            height: 39px;
            line-height: 39px;
        }
    }

        .input-icon {
            height: 14px;
            width: 14px;
            margin-left: 2px;
        }
    }

        ::v-deep .el-input__inner {
            background: transparent;
        }}

.close {
    width: 34px;
    height: 34px;
    margin: 27px auto;
    display: flex;
    justify-content: center;
    cursor: pointer;
    }

.edit-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
    }

.edit-code {
    width: 33%;
    height: 38px;
    float: right;

    img {
        cursor: pointer;
        vertical-align: middle;
    }
}

.el-edit-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
}

.edit-code-img {
    height: 38px;
}

</style>
