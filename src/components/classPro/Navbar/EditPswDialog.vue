<template>
  <el-dialog
    :show-close="false"
    width="30%"
    :close-on-click-modal="false"
    :visible.sync="editPswDialogVisible"
  >
    <div class="forget-dialog">
      <img class="top" src="@/assets/images/bg-top.png" alt="" />
      <div class="title">修改密码</div>
      <div class="forget">
        <div class="forget-form">
          <el-form
            ref="forgetForm"
            :model="forgetForm"
            :rules="forgetRules"
            class="forget-form"
          >
            <el-form-item prop="mobileOrEmail">
              <el-input
                v-model.trim="forgetForm.mobileOrEmail"
                type="text"
                auto-complete="off"
                placeholder="请输入手机号码"
                @input="mobileChange"
              >
                <img
                  slot="prefix"
                  class="el-input__icon input-icon"
                  src="@/assets/images/register/mobile.png"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="code">
              <el-input
                v-model="forgetForm.code"
                type="text"
                auto-complete="off"
                placeholder="请输入短信验证码"
              >
                <img
                  slot="prefix"
                  class="el-input__icon input-icon"
                  src="@/assets/images/register/message.png"
                />
                <el-button
                  slot="suffix"
                  type="info"
                  :disabled="smsDisabled"
                  class="sms-btn"
                  @click="getCode"
                >
                  {{
                    smsDisabled
                      ? countdown > 0
                        ? countdown + '后重新获取'
                        : '发送验证码'
                      : '发送验证码'
                  }}
                </el-button>
              </el-input>
            </el-form-item>
            <el-form-item prop="passwordNew">
              <el-input
                v-model="forgetForm.passwordNew"
                type="password"
                auto-complete="new-password"
                placeholder="请重新设置登录密码"
              >
                <img
                  slot="prefix"
                  class="el-input__icon input-icon"
                  src="@/assets/images/register/password.png"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="passwordCheck" style="margin-top: 10px;">
              <el-input
                v-model="forgetForm.passwordCheck"
                type="password"
                auto-complete="off"
                placeholder="请再次输入密码"
              >
                <img
                  slot="prefix"
                  class="el-input__icon input-icon"
                  src="@/assets/images/register/code.png"
                />
              </el-input>
            </el-form-item>
            <el-form-item style="width:100%;">
              <div
                class="submit"
                :class="{'submit-btt': valid, 'not-submit-btt': !valid}"
                @click="handleForget"
              >
                <span>确  定</span>
              </div>

            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <img
      class="close"
      src="@/assets/images/close.png"
      alt="关闭按钮"
      @click="closeForgetDialog"
    />

    <el-dialog
      title="获取验证码"
      :show-close="false"
      :center="true"
      :width="'350px'"
      :top="'35vh'"
      custom-class="nocaptcha-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      :visible.sync="nocaptchaVisible"
      :append-to-body="true"
    >
      <i class="el-icon-error close-dialog" @click="nocaptchaVisible = false"></i>
      <div v-if="nocaptchaVisible" class="nocaptcha flex">
        <nocaptcha @callback="slideToGetCode" />
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>

import Nocaptcha from '@/components/classPro/Nocaptcha'
import { validMobile } from '@/utils/validate'
import { forgetPassword, slideToGetSmsCode, verifyCode } from '@/api/user-api'

export default {
  components: {
    Nocaptcha
  },
  props: {
    editPswDialogVisible: {
      type: Boolean,
      require: true,
      default: false
    }
  },
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('请输入手机号'))
      }
      if (!validMobile(value)) {
        callback(new Error('手机号错误'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      const passwordreg = /(?![A-Z]*$)(?![a-z]*$)(?![0-9]*$)(?![^a-zA-Z0-9]*$)/
      if (!value || value.length === 0) {
        callback(new Error('密码不能为空'))
      }
      if (!passwordreg.test(value)) {
        callback(new Error('密码长度在8-20之间,且必须包含数字、大写字母、小写字母、特殊字符中的两种'))
      } else {
        callback()
      }
    }
    const validateCode = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('验证码不能为空'))
      } else {
        callback()
      }
    }
    const validateCheckPass = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('确认密码不能为空'))
      }

      if (value !== this.forgetForm.passwordNew) {
        callback(new Error('确认密码与密码不匹配'))
      } else {
        callback()
      }
    }
    return {
      nocaptchaVisible: false,
      countdown: 0,
      smsDisabled: true,
      forgetForm: {
        mobileOrEmail: '',
        passwordNew: '',
        passwordCheck: '',
        code: ''
      },
      forgetRules: {
        mobileOrEmail: [{
          required: true,
          trigger: 'blur',
          validator: validateUsername
        }],
        passwordNew: [{
          required: true,
          trigger: 'blur',
          validator: validatePassword
        }],
        passwordCheck: [{
          required: true,
          trigger: 'blur',
          validator: validateCheckPass
        }],
        code: [{
          required: true,
          trigger: 'blur',
          validator: validateCode
        }]
      },
      loading: false,
      mobile: ''
    }
  },
  computed: {
    valid () {
      return (this.forgetForm.mobileOrEmail.length > 0) &&
        (validMobile(this.forgetForm.mobileOrEmail)) &&
        (this.forgetForm.passwordNew.length >= 6) &&
        (this.forgetForm.passwordNew.length <= 20) &&
        (this.forgetForm.passwordCheck.length > 0) &&
        (this.forgetForm.passwordCheck === this.forgetForm.passwordNew) &&
        (this.forgetForm.code.length > 0)
    }
  },
  methods: {
    getCode () {
      if (this.smsDisabled) return
      const mobile = this.forgetForm.mobileOrEmail
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this.nocaptchaVisible = true
      console.log('nocaptchaVisible')
    },
    tick () {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    },
    mobileChange (val) {
      if (validMobile(val)) {
        this.smsDisabled = false
      } else {
        this.smsDisabled = true
      }
    },

    slideToGetCode (data) {
      console.log(data)
      if (data.codeKey) {
        verifyCode(
          {
            mobile: this.forgetForm.mobileOrEmail,
            codeKey: data.codeKey
          }
        ).then(response => {
          if (+response.code === 200) {
            this.countdown = 60
            this.smsDisabled = true
            setTimeout(this.tick, 1000)
            this.nocaptchaVisible = false
            data.nc.reset()
          } else {
            data.nc.reset()
            this.$message.error(response.data.message)
          }
        })
      } else {
        slideToGetSmsCode({
          smsCodeType: 'FORGET_PW',
          sessionId: data.sessionId,
          token: data.token,
          scene: data.scene,
          sig: data.sig,
          mobile: this.forgetForm.mobileOrEmail
        }).then(response => {
          if (+response.code === 200) {
            this.countdown = 60
            this.smsDisabled = true
            setTimeout(this.tick, 1000)
            data.nc.reset()
            this.nocaptchaVisible = false
          } else {
            data.nc.reset()
            this.$message.error(response.data.message)
          }
        }).catch((e) => {
          data.nc.reset()
          console.log(e)
        })
      }
    },
    handleForget () {
      if (!this.loading) {
        this.loading = true
        this.$refs.forgetForm.validate(async valid => {
          if (valid) {
            forgetPassword(this.forgetForm).then(res => {
              if (res.code === '200') {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                this.$router.push({ path: this.redirect || '/classpro/login' })
                this.$emit('closePswDialog')
                this.$emit('updatePswDone')
              }
            })
          }
        })
        this.loading = false
      }
    },
    closeForgetDialog () {
      this.$emit('closePswDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
$vw_design_width: 965;
$vw_design_height: 650;
@function ui_w($px) {
  @return $px / $vw_design_width * 100vw;
}

@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}

.forget-dialog {
  background: white;
  padding: 26px 32px 50px;
  border-radius: 40px;

  .top {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #0B0B0B;
    line-height: 25px;
    margin-top: 26px;
    text-align: left;
  }

  .button {
    background: url('../../../assets/images/login/login-btn-blue.png') center center no-repeat;
    background-size: cover;
    width: 238px;
    height: 30px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 30px;
    text-align: center;
    margin: 31px auto 0;
    cursor: pointer;
    border-radius: 240px;
  }

  .sms-btn {
    position: absolute;
    top: 0;
    right: -20px;
    margin: 3px;
    padding: 0;
    width: 92px;
    height: 30px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    background: #1F66FF;
    color: #FFFFFF;
    box-sizing: border-box !important;
    box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.16);

    &.is-disabled {
      border: #FFFFFF;
      background: #A1A1A1;
      color: #FFFFFF;
    }

    &.is-disabled:hover {
      border: #FFFFFF;
      background: #A1A1A1;
      color: #FFFFFF;
    }

    .sms-btn:hover {
      background: #1F66FF;
      box-shadow: 1px 1px 1px #fff;
    }
  }
}

.close {
  width: 34px;
  height: 34px;
  margin: 27px auto;
  display: flex;
  justify-content: center;
  cursor: pointer;
}

.forget {
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.forget-form {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  background-size: cover;
  border-radius: 6px;
  height: ui_h(286);
  width: 100%;
  padding-left: ui_h(10);
  padding-right: ui_h(10);

  ::v-deep .el-input {
    height: 38px;

    input {
      height: 38px;
      border: none;
      border-bottom: 1px solid rgba(151, 151, 151, 0.15);
    }

    ::v-deep .el-input__prefix {
      height: 39px;
      line-height: 39px;
    }
  }

  .input-icon {
    height: 14px;
    width: 14px;
    margin-left: 2px;
  }
}

.forget-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.forget-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-forget-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.forget-code-img {
  height: 38px;
}

.tab {
  margin-top: ui_h(33);
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  padding-left: ui_w(38);

  img {
    width: 34px;
    height: 34px;
  }

  .account {
    display: flex;
    width: ui_w(700);
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .tab-title {
      text-align: left;
      width: ui_w(72);
      height: ui_h(25);
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #0B0B0B;
      line-height: ui_h(25);
    }

    .indicator {
      margin-top: ui_h(4);
      width: ui_w(21);
      height: ui_h(4);
      background: #1F66FF;
      border-radius: ui_w(3);
    }
  }
}

.button {
  width: ui_w(238);
  height: 40px;
  background-size: contain;
  line-height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
  cursor: pointer;
  border-radius: 40px;
  margin: 0 auto;
}

.register-bottom {
  display: flex;
  margin-bottom: ui_h(20);
  flex-direction: row;
  width: 100%;
  justify-content: space-between;

  .register-p {
    width: 98px;
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #1F66FF;
    line-height: 20px;
  }
}

.code-button {
  background: #1F66FF;
  background-size: cover;
  width: 108px;
  height: 30px;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  border-radius: 240px;
}

.submit {
  width: 100%;
  height: 30px;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
}

.submit-btt {
  background: #1F66FF;
  box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
}

.not-submit-btt {
  background: #BFBFBF;
  box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), 1px 1px 1px 0px #E7E7E7, -1px -1px 1px 0px rgba(129, 129, 134, 0.32);
}
</style>
