<template>
  <!-- <el-dialog
    :visible.sync="dialogVisible"
    :custom-class="'grade-dialog'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :width="width"
  > -->
  <!-- </el-dialog> -->

  <comp-dialog title="班级切换" :dialog-visible="dialogVisible" :width="'25%'" :is-center="false" @closeDialog="closeDialog">
    <div class="grade-box">
      <div class="grade-content">
        <div v-for="(item, index) in gradeList" :key="item.toUserId" class="grade-input">
          <div class="grade-item" :class="{'grade-item-select' : selectIndex === index}" @click="select(index)">
            {{ item.toUserDisplayName }}
            <!-- <img v-if="item.isRelation" slot="suffix" class="suffix-icon" :src="iconEdit" alt="编辑" @click="openEditGradeDialog(index)" /> -->
          </div>
        </div>
      </div>
      <div class="classpro-btn grade-btn" @click="confirm">确定</div>
      <edit-grade-dialog
        :dialog-visible="showEditGradeDialog"
        :child-user-id="editChildId"
        :child-name="editChildName"
        @closeEditGradeDialog="showEditGradeDialog = false"
        @editSuccess="editSuccess"
      />
    </div>
  </comp-dialog>
</template>

<script>
import iconEdit from '@/assets/images/Navbar/icon-edit.png'
import CompDialog from '@/components/classPro/ComponentDialog'
import EditGradeDialog from '@/components/classPro/Navbar/EditGradeDialog'
import { getToken, getChildId, setChildName, setChildId, setChildToken, removeChildName, removeChildId } from '@/utils/auth'
import { switchAccount } from '@/api/user-api'
import store from '@/store'
export default {
  components: {
    CompDialog,
    EditGradeDialog
  },
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    },
    width: {
      type: String,
      require: false,
      default: '25%'
    }
  },
  data () {
    return {
      gradeInput: '',
      selectIndex: 0,
      iconEdit,
      gradeList: [],
      showEditGradeDialog: false,
      editChildId: -1,
      editChildName: ''
    }
  },
  watch: {
    dialogVisible: {
      handler: function (val) {
        if (val) this.createGradeList()
      },
      immediate: true
    }
  },
  methods: {
    createGradeList () {
      this.gradeList = this.$store.getters.userRelations
      var selectId = getChildId() || this.$store.getters.id
      for (var i = 0; i < this.gradeList.length; i++) {
        if (+selectId === +this.gradeList[i].toUserId) {
          this.selectIndex = i
          return
        }
      }
    },
    closeDialog () {
      this.$emit('closeDialog')
    },
    select (index) {
      this.selectIndex = index
    },
    confirm () {
      if (this.gradeList[this.selectIndex].toUserId === this.$store.getters.id &&
          this.gradeList[this.selectIndex].toUserDisplayName === this.$store.getters.name) {
        removeChildName()
        removeChildId()
        setChildToken(getToken())
        this.$emit('closeDialog')
        this.$bus.$emit('getStuCourseList')
        return
      }
      var param = {
        'touserId': this.gradeList[this.selectIndex].toUserId,
        'parentUserId': this.$store.getters.id
      }
      switchAccount(param)
        .then(response => {
          setChildName(this.gradeList[this.selectIndex].toUserDisplayName)
          setChildId(this.gradeList[this.selectIndex].toUserId)
          setChildToken('Bearer ' + response.data.access_token)
          this.$emit('closeDialog')
          this.$bus.$emit('getStuCourseList')
        })
    },
    openEditGradeDialog (index) {
      this.editChildId = this.gradeList[index].toUserId
      this.editChildName = this.gradeList[index].toUserDisplayName
      this.showEditGradeDialog = true
    },
    async editSuccess () {
      await store.dispatch('user/GetUserRelation')
      await this.createGradeList()
      if (getChildId()) {
        setChildName(this.gradeList[this.selectIndex].toUserDisplayName)
      }
    }
  }
}
</script>

<style lang="scss">
.grade-box {
  display: flex;
  flex-direction: column;
  width: 100%;

  .grade-content {
    justify-content: center;
    align-items: center;
    background-size: cover;
    min-height: 100px;
    max-height: 120px;
    overflow: scroll;
    @include noScrollBar;

    .suffix-icon {
      width: 12px;
      height: 14px;
      object-fit: contain;
      cursor: pointer;
    }

    .el-input__suffix {
      top: 30%;
      right: 9px;
    }

    .grade-input {
      margin-bottom: 10px;
    }

    .grade-item {
      width: 100%;
      height: 43px;
      line-height: 43px;
      border-radius: 5px;
      border: 1px solid rgba(140, 147, 153, 0.19);
      cursor: pointer;
      position: relative;
      padding: 0 1vh;
      @include ellipses(1);

      .suffix-icon {
        position: absolute;
        right: 1vh;
        top: 30%;
      }
    }

    .grade-item-select {
      border: 1px solid rgba(31, 102, 255, 0.33);
    }
  }

  .grade-btn {
    width: 80%;
    height: 30px;
    margin-top: 19px;
    margin: 19px auto 0;
  }

  .close {
    width: 34px;
    height: 34px;
    margin: 27px auto;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }
}
</style>

