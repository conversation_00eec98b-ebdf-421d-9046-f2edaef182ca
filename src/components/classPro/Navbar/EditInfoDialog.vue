<template>
  <el-dialog
    width="30%"
    :show-close="false"
    :close-on-click-modal="false"
    :visible.sync="editInfoDialogVisible"
  >
    <div class="info-dialog">
      <img class="info-dialog-top" src="@/assets/images/bg-top.png" alt="" />
      <div class="title">修改昵称</div>
      <el-form
        ref="editInfoForm"
        :model="editInfoForm"
        :rules="editInfoRules"
        class="edit-name-form"
      >
        <el-form-item prop="displayName">
          <el-input v-model="editInfoForm.displayName" type="text" auto-complete="off" placeholder="请输入新昵称" />
        </el-form-item>
        <el-form-item>
          <div
            :class="{'submit-btt': valid, 'not-submit-btt': !valid}"
            @click="handleEdit"
          >
            <span>确认修改</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <img
      class="close"
      src="@/assets/images/close.png"
      alt="关闭按钮"
      @click="closeInfoDialog"
    />
  </el-dialog>
</template>

<script>
import { updateUserInfo } from '@/api/user-api'
import store from '@/store'

export default {
  props: {
    displayName: {
      type: String,
      require: true,
      default: ''
    },
    schoolName: {
      type: String,
      require: true,
      default: ''
    },
    editInfoDialogVisible: {
      type: Boolean,
      require: true,
      default: false
    }
  },
  data () {
    const validateDisplayname = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('请输入昵称'))
      } else {
        callback()
      }
    }
    return {
      editInfoForm: {
        displayName: ''
      },
      editInfoRules: {
        displayName: [{
          required: true,
          trigger: 'blur',
          validator: validateDisplayname
        }]
      },
      loading: false
    }
  },
  computed: {
    valid () {
      return (this.editInfoForm.displayName && this.editInfoForm.displayName.length > 0)
    }
  },
  methods: {
    handleEdit () {
      if (!this.loading && this.valid) {
        this.loading = true
        this.$refs.editInfoForm.validate(async valid => {
          if (valid) {
            const params = {
              userType: 'STUDENT',
              ...this.editInfoForm
            }
            updateUserInfo(params).then(res => {
              if (res.code === 200) {
                store.dispatch('user/EditName', this.editInfoForm.displayName)
                this.$emit('closeInfoDialog')
              }
            })
          }
          this.loading = false
        })
      }
    },
    closeInfoDialog () {
      this.$emit('closeInfoDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
$vw_design_width: 965;
$vw_design_height: 650;
@function ui_w($px) {
  @return $px / $vw_design_width * 100vw;
}

@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}

.info-dialog {
  background: white;
  padding: 26px 32px 40px;
  border-radius: 40px;

  .info-dialog-top {
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #0B0B0B;
    line-height: 25px;
    margin-top: 26px;
    text-align: left;
  }

  .button {
    background: url('../../../assets/images/login/login-btn-blue.png') center center no-repeat;
    background-size: cover;
    width: 238px;
    height: 30px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 30px;
    text-align: center;
    margin: 31px auto 0;
    cursor: pointer;
    border-radius: 240px;
  }

  .submit-btt {
    width: 80%;
    height: 30px;
    background: #1F66FF;
    box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 30px;
    margin: 0 auto;
    text-align: center;
    cursor: pointer;
  }
  .not-submit-btt {
    width: 80%;
    height: 30px;
    background: #BFBFBF;
    box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), 1px 1px 1px 0px #E7E7E7, -1px -1px 1px 0px rgba(129, 129, 134, 0.32);
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 32px;
    margin: 0 auto;
    text-align: center;
    cursor: pointer;
  }

  .edit-name-form {
    border-radius: 6px;
    width: 100%;

    ::v-deep .el-input {
      height: 38px;

      input {
        margin-top: 30px;
        height: 38px;
        border: none;
        border-bottom: 1px solid rgba(151, 151, 151, 0.15);
      }

      ::v-deep .el-input__prefix {
        height: 39px;
        line-height: 39px;
      }
    }

      .input-icon {
        height: 14px;
        width: 14px;
        margin-left: 2px;
      }
  }

  ::v-deep .el-input__inner {
    background: transparent;
  }
}

.close {
  width: 34px;
  height: 34px;
  margin: 27px auto;
  display: flex;
  justify-content: center;
  cursor: pointer;
}

.edit-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.edit-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-edit-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.edit-code-img {
  height: 38px;
}

</style>
