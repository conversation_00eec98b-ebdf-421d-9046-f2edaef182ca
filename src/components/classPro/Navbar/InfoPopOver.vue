<template>
  <div>
    <el-popover
      ref="loginPopOver"
      popper-class="loginPopOver"
      placement="bottom"
      trigger="click"
      title=""
      class="info-pop"
      :visible-arrow="false"
      :popper-options="{
        boundariesElement: 'body'
      }"
    >
      <div slot="reference" class="header">
        <img class="avatar" :src="avatar" alt="" />
      </div>
      <div class="info">
        <div class="row1">
          <el-upload
            class="avatar-container"
            :action="uploadUrl"
            :show-file-list="false"
            :headers="handleHeader"
            :accept="handleAccept"
            :on-error="handleError"
            :on-progress="handleProgress"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img :src="avatar" class="avatar" />
            <img class="edit-avatar" src="../../../assets/images/dashboard/editAvatar.png" alt="" />
          </el-upload>
          <div class="t-info">
            <div class="info-name">{{ name || mobile }}</div>
            <div v-show="schoolName" class="flex items-center">
              <div class="school" :title="schoolName">
                {{ schoolName }}
              </div>
              <div class="change" @click="bindSchoolShow = true">
                <img src="../../../assets/images/school-change.svg" />
              </div>
            </div>
          </div>
          <div class="edit">
            <img src="../../../assets/images/dashboard/edit.png" alt="" @click="showInfoDialog()" />
          </div>
        </div>
        <div class="row2">
          <li>
            <img class="mobile-svg" src="../../../assets/images/Navbar/mobile.png" alt="" />
            <div class="phone-num">{{ mobile }}</div>
            <div class="edit-num" @click="showMobileDialog()">修改手机号</div>
          </li>
          <li>
            <img class="psw-svg" src="../../../assets/images/login/password.png" alt="" />
            <div class="password">******</div>
            <div class="edit-password" @click="showPswDialog()">修改密码</div>
          </li>
        </div>
        <div class="row3">
          <div class="quit-btt" @click="logout">
            <div class="blur"></div>
            <div class="quit">退出登录</div>
          </div>
        </div>
      </div>
    </el-popover>
    <!--  弹窗-->
    <edit-info-dialog
      :edit-info-dialog-visible="editInfoDialogVisible"
      :display-name="name"
      :school-name="schoolName"
      class="classpro-dialog"
      @closeInfoDialog="closeInfoDialog"
    />
    <edit-mobile-dialog
      :edit-mobile-dialog-visible="editMobileDialogVisible"
      class="classpro-dialog"
      @closeMobileDialog="closeMobileDialog"
      @updateMobileDone="logout"
    />
    <edit-psw-dialog
      :edit-psw-dialog-visible="editPswDialogVisible"
      class="classpro-dialog"
      @closePswDialog="closePswDialog"
      @updatePswDone="logout"
    />
    <NormalDialog
      v-if="bindSchoolShow"
      width="30vw"
      :title="'学校激活码'"
      :dialog-visible="bindSchoolShow"
      :is-center="true"
      @closeDialog="
        bindSchoolShow = false;
        schoolCode=''
      "
    >
      <div class="flex flex-col">
        <div class="mb10">
          <el-input v-model="schoolCode" class="w" placeholder="请输入激活码" />
        </div>
        <div style="color: #828282">
          备注：
          <div class="school-disc flex items-center">
            <div class="school-disc2"></div>
            支持已合作的学校绑定，请输入所在学校激活码
          </div>
          <div class="school-disc flex items-center">
            <div class="school-disc2"></div>
            如没有请向学校管理员索取
          </div>
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="bindSchool">确定</div>
      </template>
    </NormalDialog>
  </div>
</template>

<script>
import store from '@/store'
import EditPswDialog from './EditPswDialog'
import EditInfoDialog from './EditInfoDialog'
import EditMobileDialog from './EditMobileDialog'
import { getToken } from '@/utils/auth'
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import { debounce } from '@/utils/index'
import { assistantBindSchool } from '@/api/educational-api.js'
export default {
  name: 'InfoPopOver',
  components: {
    NormalDialog,
    EditMobileDialog,
    EditInfoDialog,
    EditPswDialog
  },
  props: {
    name: {
      type: String,
      require: false,
      default: ''
    },
    schoolName: {
      type: String,
      require: false,
      default: ''
    },
    mobile: {
      type: String,
      require: false,
      default: ''
    },
    avatar: {
      type: String,
      require: false,
      default: ''
    }
  },
  data () {
    return {
      editMobileDialogVisible: false,
      editInfoDialogVisible: false,
      editPswDialogVisible: false,
      uploading: false,
      handleAccept: 'image/*',
      handleHeader: {
        Authorization: getToken()
      },
      schoolCode: '',
      bindSchoolShow: false
    }
  },
  computed: {
    uploadUrl: function () {
      return `${process.env.VUE_APP_BASE_API}/api/v1/students/avatar`
    }
  },
  methods: {
    showMobileDialog () {
      this.editMobileDialogVisible = true
    },
    closeMobileDialog () {
      this.editMobileDialogVisible = false
    },
    showInfoDialog () {
      this.editInfoDialogVisible = true
    },
    closeInfoDialog () {
      this.editInfoDialogVisible = false
    },
    showPswDialog () {
      this.editPswDialogVisible = true
    },
    closePswDialog () {
      this.editPswDialogVisible = false
    },
    logout () {
      if (window.resetAutoUpdateFlag) {
        window.resetAutoUpdateFlag()
      }
      store.dispatch('user/FedLogOut')
      location.reload()
    },
    handleProgress () {
      this.uploading = true
    },
    handleError (e) {
      this.$message.error(e)
      this.uploading = false
    },
    handleAvatarSuccess (response, file) {
      this.uploading = false
      if (response.code === '200') {
        this.$store.dispatch('user/GetInfo')
      } else {
        this.$message.error(response.message)
      }
    },
    beforeAvatarUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 10MB!')
        this.uploading = false
      }
      return isLt2M
    },
    bindSchool: debounce(async function () {
      if (this.schoolCode) {
        try {
          await assistantBindSchool({
            schoolCode: this.schoolCode
          })
          this.schoolCode = ''
          this.$store.dispatch('user/GetInfo')
          this.bindSchoolShow = false
          this.$message.success('激活成功')
        } catch (error) {
          this.bindSchoolShow = false
        }
      }
    }, 3000, true)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';

.mb10 {
  margin-bottom: 10px;
}
.header {
  position: relative;
  cursor: pointer;
  background: url('../../../assets/images/bg-header.png') center center no-repeat;
  background-size: contain;
  width: 50px;
  height: 50px;
  margin: 0 6px 0 10px;
  .avatar {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    transform: translateX(-50%) translateY(-50%);
    width: 28px;
    height: 28px;
  }
}

.info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 312px;
  height: 283px;
  .row1 {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 97px;
    padding: 20px 20px 13px 20px;
    border-radius: 14px;

    .avatar-container {
      position: relative;
      width: 60px;
      height: 60px;
      cursor: pointer;

      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }

      .edit-avatar {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 22px;
        height: 22px;
      }
    }

    .t-info {
      margin-left: 15px;
      padding-top: 10px;
      max-width: 170px;
      height: 100%;
      .info-name {
        font-size: 16px;
        font-weight: 500;
        color: #0B0B0B;
        line-height: 22px;
        @include ellipses(2);
        margin-bottom: 6px;
        text-align: start;
      }
      .school {
        max-width: calc(100% - 25px);
        padding: 1px 9px;
        border-radius: 11px;
        border: 1px solid #1F66FF;
        font-size: 14px;
        font-weight: 400;
        color: #1F66FF;
        line-height: 22px;
        text-align: start;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .change {
        background: #F7C466;
        border-radius: 5px;
        cursor: pointer;
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 5px;
        img {
          width: 10px;
          height: 20px;
        }
      }
    }
    .edit {
      position: absolute;
      cursor: pointer;
      top: 31px;
      right: 21px;
      width: 12px;
      height: 14px;
    }
  }
  .row2 {
    width: 280px;
    border-bottom: 1px solid rgba(151, 151, 151, 0.15);
    li {
      position: relative;
      list-style: none;
      display: flex;
      align-items: center;
      height: 52px;
      border-top: 1px solid rgba(151, 151, 151, 0.15);
      .mobile-svg,
      .psw-svg {
        margin-left: 20px;
        width: 13px;
      }
      .phone-num,
      .password {
        font-size: 16px;
        font-weight: 400;
        color: #0B0B0B;
        margin-left: 19px;
        line-height: 22px;
      }
      .edit-num,
      .edit-password {
        position: absolute;
        cursor: pointer;
        right: 10px;
        width: 76px;
        height: 23px;
        background: #F7C466;
        border-radius: 5px;
        font-size: 12px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 23px;
        text-align: center;
        &:hover {
          background: #1F66FF;
        }
      }
    }
  }
  .row3 {
    position: relative;
    width: 100%;
    height: 77px;
    .quit-btt {
      cursor: pointer;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);
      width: 169px;
      height: 30px;
      background: #1F66FF;
      box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
      border-radius: 16px;
      .blur {
        position: absolute;
        width: 58px;
        height: 30px;
        background: linear-gradient(124deg, rgba(255, 255, 255, 0.74) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0.51;
        filter: blur(1px);
      }
      .quit {
        font-size: 14px;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 28px;
        text-align: center;
        z-index: 10;
      }
      &:hover {
        background: #6193FF;
        box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41);
      }
    }
  }
}

#file {
  display: none;
}
.school-disc {
  margin-top: 10px;
}
.school-disc2 {
  width: 5px;
  height: 5px;
  background: #828282;
  border-radius: 50%;
  margin-right: 5px;
}
</style>

<style lang="scss">

.el-popover.loginPopOver {
  width: 312px;
  height: 283px ;
  background: url('../../../assets/images/Navbar/bg-info-popover.png') no-repeat;
  background-size: cover;
  padding: 0;
  border: 0;
  box-shadow: 0 0 0 0;
  top: -15px !important;
  left: -120px !important;
}
</style>
