<template>
  <div>
    <div v-show="showNc">
      <div id="nc" class="nc-container"></div>
    </div>
    <div v-show="!showNc">
      <div class="captcha-wrapper">
        <div class="captcha-container">
          <el-input
            v-model="codeKey"
            type="text"
            placeholder="请输入验证码"
          />
          <img :src="imgSrc" alt="" @click="refreshCaptcha" />
        </div>
        <button class="confirm-btn" @click="sendSms">确定</button>
      </div>
    </div>
  </div>
</template>

<script>
import VS2 from 'vue-script2'
import { getVerifyCodeImage } from '@/api/user-api'

export default {
  data () {
    return {
      appKey: 'FFFF0N0000000000662A',
      lang: 'cn',
      scene: 'bingo_teacher',
      ncOption: {},
      nc: null,
      showNc: true,
      codeKey: '',
      imgSrc: ''
    }
  },

  created () {
    this.init()
  },
  methods: {
    init () {
      const that = this
      const myLang = {
        cn: {
          // 加载状态提示。
          LOADING: '加载中...',
          // 等待滑动状态提示。
          SLIDE: '请向右滑动验证',
          // 验证通过状态提示。
          SUCCESS: '验证通过',
          // 验证失败触发拦截状态提示。
          ERROR: '非常抱歉，网络出错了...',
          // 验证失败触发拦截状态提示。
          FAIL: '非常抱歉，请尝试用验证码获取</a>'
        }
      }
      const ncOption = {
        renderTo: 'nc',
        appkey: this.appKey,
        scene: this.scene,
        language: 'cn',
        upLang: myLang,
        success (data) {
          that.$emit('callback', {
            sessionId: data.sessionId,
            sig: data.sig,
            token: data.token,
            scene: that.scene,
            nc: that.nc
          })
          that.dragStatus = true //  拖动状态，判断滑块是否拖动完成
        },
        fail (failCode) {
          console.log('failCode:' + failCode)
          that.showNc = false
          that.refreshCaptcha()
        },
        error (errorCode) {
          console.log('errorCode:' + errorCode)
          that.showNc = false
          that.refreshCaptcha()
        }
      }
      VS2.load('https://g.alicdn.com/AWSC/AWSC/awsc.js').then(() => {
        // 实例化nc
        // eslint-disable-next-line no-undef
        AWSC.use('nc', function (state, module) {
          that.nc = module.init(ncOption)
        })
      })
    },
    refreshCaptcha () {
      getVerifyCodeImage({}).then(response => {
        if (+response.code === 200) {
          this.imgSrc = 'data:image/jpeg;charset=utf-8;base64,' + response.data
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    showCaptcha () {
      console.log('nc hide ')
      this.nc.hide()
    },
    sendSms () {
      const that = this
      if (!this.codeKey) {
        return false
      }
      that.$emit('callback', {
        codeKey: that.codeKey
      })
      that.refreshCaptcha()
    }
  }
}
</script>

<style lang="scss">
.sm-pop-inner.nc-container {
  width: 300px;
}

.nc_iconfont.btn_slide,
.nc_iconfont.btn_ok {
  height: 34px !important;
  line-height: 34px !important;
}

.captcha-wrapper h3 {
  font-size: 22px;
  color: #000;
  text-align: center;
  margin-bottom: 10px;
  margin-top: -20px;
}

.captcha-wrapper .captcha-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.captcha-wrapper .captcha-container input {
  border-radius: 0px;
  border: 1px solid #eee;
  padding: 10px;
  width: 140px;
  height: 26px;
  font-size: 14px;
  margin-right: 5px;
}

.captcha-wrapper .captcha-container img {
  width: 120px;
  height: 32px;
}

.captcha-wrapper .confirm-btn {
  width: 80px;
  height: 32px;
  display: block;
  margin: 5px auto;
  font-size: 14px;
  border-radius: 20px;
  border: none;
  line-height: 22px;
  color: #fff;
  text-align: center;
  background: #1F66FF;
  cursor: pointer;
}

</style>
