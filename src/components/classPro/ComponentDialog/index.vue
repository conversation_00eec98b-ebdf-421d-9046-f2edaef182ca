<template>
  <!-- 使用方法
      <ComponentDialog
      width="40%"
      :title="'标题'"
      :dialog-visible="dialogTableVisible"
      :is-center="true"
      @closeDialog="closeDialog"
    >
      内容
    </ComponentDialog> -->
  <el-dialog
    :visible.sync="dialogVisible"
    :custom-class="!isCenter ? 'bingo-dialog' : 'bingo-dialog center'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :width="width"
    :append-to-body="appendToBody"
  >
    <div class="dialog-box">
      <img class="dialog-box-top" src="@/assets/images/bg-top.png" alt="" />
      <div class="title">
        {{ title }}
      </div>
      <div class="content">
        <slot></slot>
      </div>
    </div>
    <img
      class="close"
      src="@/assets/images/pop-close.png"
      alt="关闭按钮"
      @click="closeDialog"
    />
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    },
    title: {
      type: String,
      require: false,
      default: ''
    },
    width: {
      type: String,
      require: false,
      default: '30%'
    },
    isCenter: {
      type: Boolean,
      require: true,
      default: false
    },
    appendToBody: {
      type: Boolean,
      require: true,
      default: false
    }
  },
  data () {
    return {

    }
  },
  methods: {
    closeDialog () {
      this.$emit('closeDialog')
    }
  }
}
</script>

<style lang="scss">
$title-top: 108px;
.center {
  margin:0 !important;
  position:absolute !important;
  top:50% !important;
  left:50% !important;
  transform:translate(-50%,-50%) !important;
}
.bingo-dialog {
  display: flex;
  flex-direction: column;
  max-height:calc(100% - 30px);
  max-width:calc(100% - 30px);
  background: transparent !important;
  box-shadow: none !important;

  .dialog-box {
    background: white;
    padding: 26px 32px 40px;
    border-radius: 15px;
    position: relative;
  }

  .el-dialog__body {
    flex:1;
    overflow: hidden;
    padding: 0px;
  }

  .el-dialog__header {
    display: none;
  }

  .el-edit-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .dialog-box-top {
    position: absolute;
    width: 100%;
    height: $title-top;
    left: 0;
    top: 0;
  }

  .title {
    position: absolute;
    width: 100%;
    height: $title-top;
    left: 0;
    top: 0;
    font-size: 18px;
    font-weight: 500;
    color: #0B0B0B;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 38px;
  }

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    min-height: 100px;
    padding-top: $title-top;
  }

  .close {
    width: 34px;
    height: 34px;
    margin: 27px auto;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }
}
</style>

