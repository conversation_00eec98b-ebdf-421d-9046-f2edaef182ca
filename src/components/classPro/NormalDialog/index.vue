<template>
  <!-- 使用方法
      <NormalDialog
      width="40%"
      :title="'标题'"
      :dialog-visible="dialogTableVisible"
      :is-center="true"
      @closeDialog="closeDialog"
    >
      内容
    </NormalDialog> -->
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :custom-class="!isCenter ? 'bingo-normal-dialog' : 'bingo-normal-dialog normal-center'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    :width="width"
    :append-to-body="appendToBody"
  >
    <div class="dialog-box">
      <div class="content">
        <slot></slot>
      </div>
    </div>
    <div v-if="hasFooter" slot="footer" class="dialog-footer">
      <slot name="footer"></slot>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    },
    title: {
      type: String,
      require: false,
      default: ''
    },
    width: {
      type: String,
      require: false,
      default: '30%'
    },
    isCenter: {
      type: Boolean,
      require: true,
      default: false
    },
    appendToBody: {
      type: Boolean,
      require: true,
      default: false
    },
    hasFooter: {
      type: Boolean,
      require: false,
      default: true
    }
  },
  data () {
    return {

    }
  },
  methods: {
    closeDialog () {
      this.$emit('closeDialog')
    }
  }
}
</script>

<style lang="scss">
$title-top: 54px;
.normal-center {
  margin:0 !important;
  position:absolute !important;
  top:50% !important;
  left:50% !important;
  transform:translate(-50%,-50%) !important;
}
.bingo-normal-dialog {
  display: flex;
  flex-direction: column;
  max-height:calc(100% - 30px);
  max-width:calc(100% - 30px);
  border-radius: 15px !important;

  .dialog-box {
    background: white;
    padding: 20px 20px 0 20px;
    position: relative;
  }

  .el-dialog__body {
    flex:1;
    overflow: auto;
    padding: 5px;
  }

  .el-dialog__header {
    border-bottom: 1px solid #DADFEA;
    padding: 10px 20px;
    .el-dialog__title {
      font-weight: 500;
      font-size: 18px !important;
    }

    .el-dialog__headerbtn {
      top: 12px;
      right: 20px;
      font-size: 16px !important;
    }
  }

  .el-dialog__footer {
    width: 100%;
    color: #fff;
    font-size: 12px;
    padding: 20px;
  }

  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    min-height: 85px;
  }

  // ::v-deep .el-dialog__header .el-dialog__title, .el-dialog__header .el-dialog__headerbtn {
  //   font-size: 16px;
  // }
}
</style>

