<template>
  <!-- 使用方法
      <NormalDialog
      width="40%"
      :title="'标题'"
      :dialog-visible="dialogTableVisible"
      :is-center="true"
      @closeDialog="closeDialog"
    >
      内容
    </NormalDialog> -->
  <el-dialog
    :title="title"
    v-bind='$attrs'
    :visible.sync="dialogVisible"
    :custom-class="`${!isCenter ? 'bingo-normal-dialog2' : 'bingo-normal-dialog2 normal-center'} ${digClass ? 'editor-dig' : ''}`"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="closeDialog"
    :width="width"
    :append-to-body="appendToBody"
  >
    <div slot="title" v-if='!defaultHeader'>
      <slot name='header'></slot>
    </div>
    <div class="dialog-box">
      <div class="content">
        <slot></slot>
      </div>
    </div>
    <div v-if="hasFooter" slot="footer" class="dialog-footer">
      <slot name="footer"></slot>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    },
    title: {
      type: String,
      require: false,
      default: ''
    },
    width: {
      type: String,
      require: false,
      default: '30%'
    },
    isCenter: {
      type: Boolean,
      require: true,
      default: false
    },
    appendToBody: {
      type: Boolean,
      require: true,
      default: false
    },
    hasFooter: {
      type: Boolean,
      require: false,
      default: true
    },
    defaultHeader: {
      type: Boolean,
      default: true
    },
    digClass: {
      type: Boolean,
      require: false,
      default: false
    }
  },
  data () {
    return {

    }
  },
  mounted () {
    console.log(this.digClass)
  },
  methods: {
    closeDialog () {
      this.$emit('closeDialog')
    }
  }
}
</script>

<style lang="scss">
$title-top: 54PX;
.normal-center {
  margin:0 !important;
  position:absolute !important;
  top:50% !important;
  left:50% !important;
  transform:translate(-50%,-50%) !important;
}
.bingo-normal-dialog2 {
  display: flex;
  flex-direction: column;
  max-height:calc(100% - 30PX);
  max-width:calc(100% - 30PX);
  border-radius: 15PX !important;

  .dialog-box {
    background: white;
    padding: 20PX 20PX 0 20PX;
    position: relative;
  }

  .el-dialog__body {
    flex:1;
    overflow: auto;
    padding: 5PX;
  }

  .el-dialog__header {
    border-bottom: 1PX solid #DADFEA;
    padding: 10PX 20PX;
    .el-dialog__title {
      font-weight: 500;
      font-size: 18PX !important;
    }

    .el-dialog__headerbtn {
      top: 12PX;
      right: 20PX;
      font-size: 16PX !important;
    }
  }

  .el-dialog__footer {
    width: 100%;
    color: #fff;
    font-size: 12PX;
    padding: 20PX;
  }

  .dialog-footer {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    min-height: 85PX;
  }

  .el-input__inner {
    height: 40PX !important;
    line-height: 40PX !important;
    outline: 0 !important;
    padding: 0 15PX !important;
    border-radius: 4PX !important;
    border: 1PX solid #DCDFE6 !important;
  }

  .edu-btn {
    border-radius: 50PX !important;
    width: 110PX !important;
    height: 40PX !important;
    border: 1PX solid #1F66FF !important;
    font-size: 14PX !important;
    margin-left: 2PX !important;
  }

  // ::v-deep .el-dialog__header .el-dialog__title, .el-dialog__header .el-dialog__headerbtn {
  //   font-size: 16px;
  // }
}
</style>

