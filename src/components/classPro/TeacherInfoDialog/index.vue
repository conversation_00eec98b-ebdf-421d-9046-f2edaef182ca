<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :custom-class="'teacher-info-dialog center'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :append-to-body="false"
  >
    <div class="dialog-box">
      <div class="info-box mb18">
        <div class="flex items-center mb18">
          <div class="line"></div>
          <div class="name" style="flex: 1">{{ teacherInfo && teacherInfo.displayName || '' }}</div>
          <i
            class="iconfont"
            :class="[teacherInfo && teacherInfo.teacher && teacherInfo.teacher.collection ? 'icon-xinshixin' : 'icon-xinxiankuang']"
            @click="_followTeacher"
          ></i>
          <div
            class="collect"
            @click="_followTeacher"
          >{{ teacherInfo && teacherInfo.teacher && teacherInfo.teacher.collection ? '已收藏' : '收藏' }}</div>
        </div>

        <div v-if="teacherInfo" class="info flex items-center">
          <div class="avatar">
            <img
              :src="teacherInfo.avatar || dfAvatar"
              alt="teacher-avatar"
              class="h-full w-full object-cover"
            />
          </div>

          <div class="flex flex-col justify-center" style="flex: 1">
            <!-- <div class="flex items-center">
              <svg-icon
                icon-class="location"
                class-name="location"
              />
              <div class="country">{{ country(teacherInfo.nationality) }}</div>
            </div> -->

            <div v-if="teacherInfo.teacher && teacherInfo.teacher.tags" class="label-list mb12">
              <div v-for="tag in teacherInfo.teacher.tags" :key="tag.id" class="label">{{ tag.name || '' }}</div>
            </div>

            <el-rate
              v-if="teacherInfo.teacher && teacherInfo.teacher.avgStar"
              v-model="teacherInfo.teacher.avgStar"
              disabled
              allow-half
              show-text
              :texts="[]"
              class="score"
              disabled-void-color="#CDCACA"
            />

          </div>
        </div>
      </div>

      <div class="subtitle" v-html="teacherInfo && teacherInfo.teacher && teacherInfo.teacher.introduction || '暂无简介'"></div>
    </div>
    <img
      class="close"
      src="@/assets/images/pop-close.png"
      alt="关闭按钮"
      @click="closeDialog"
    />
  </el-dialog>
</template>

<script>
import dfAvatar from '@/assets/images/dashboard/defaultAvatar.png'
import { getVtUserInfo, followTeacher, cancelFollowedTeacher } from '@/api/user-api.js'
import { debounce } from '@/utils/index'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    },
    teacherId: {
      type: Number,
      require: true,
      default: undefined
    }
  },
  data () {
    return {
      dfAvatar,
      teacherInfo: undefined
    }
  },
  watch: {
    dialogVisible: {
      handler: function (val) {
        if (val) {
          this.teacherInfo = undefined
          this._getVtUserInfo()
        }
      },
      immediate: true
    }
  },
  methods: {
    closeDialog () {
      this.$emit('closeDialog')
      this.$bus.$emit('updateData')
    },
    country (countryName) {
      switch (countryName) {
        case 'China':
          return '中国'
        default:
          return countryName
      }
    },
    _getVtUserInfo () {
      if (!this.teacherId) return
      const params = {
        'userType': 'TEACHER',
        'userId': this.teacherId
      }
      getVtUserInfo(params).then(
        response => {
          const data = response.data
          const teacher = data.teacher
          if (teacher.teachLv) {
            let teachLvName
            if (teacher.teachLv.indexOf('1') > -1 && teacher.teachLv.indexOf('4') > -1) {
              teachLvName = '全龄段'
            } else if (teacher.teachLv.indexOf('1') > -1) {
              teachLvName = '低龄段'
            } else if (teacher.teachLv.indexOf('4') > -1) {
              teachLvName = '高龄段'
            }
            const teachLvJson = { 'id': 0, 'name': teachLvName }
            let newTeacherTags = [teachLvJson]
            if (teacher.tags && teacher.tags.length > 0) {
              newTeacherTags = newTeacherTags.concat(teacher.tags)
            }
            teacher.tags = newTeacherTags
          }

          this.teacherInfo = data
        }
      )
    },
    //  关注/取关老师
    _followTeacher: debounce(async function () {
      if (!this.teacherInfo || !this.teacherInfo.teacher) {
        this.$message.error('请稍后')
        return
      }
      const params = {
        'teacher': this.teacherInfo.id
      }
      if (this.teacherInfo.teacher.collection) {
        cancelFollowedTeacher(params).then(
          this.teacherInfo.teacher.collection = false
        )
      } else {
        followTeacher(params).then(
          this.teacherInfo.teacher.collection = true
        )
      }
    }, 500, true)
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog{
  background: none;
  box-shadow: none;
}
::v-deep .el-dialog__body{
 padding: 0;
}
$title-top: 108px;
.center {
  margin:0 !important;
  position:absolute !important;
  top:50% !important;
  left:50% !important;
  transform:translate(-50%,-50%) !important;
}

.teacher-info-dialog {
  display: flex;
  flex-direction: column;
  max-height:calc(100% - 30px);
  max-width:calc(100% - 30px);
  background: transparent !important;
  box-shadow: none !important;
  overflow: hidden;
  .dialog-box {
    background: white;
    padding: 26px 32px 40px;
    border-radius: 15px;
    position: relative;
    margin: 0 auto;
    width: 564px;
    min-height: 468px;
    padding: 46px 45px 28px;
    display: flex;
    flex-direction: column;
  }

  .info-box {
    width: 100%;
    min-height: 145px;

    .line {
        height: 22px;
        width: 6px;
        background: #1F66FF;
        margin-right: 18px;
    }

    .name {
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
        color: #131B1F;
    }

    .iconfont {
      font-size: 30px;
      margin-right: 6px;
      cursor: pointer;
    }

    .icon-xinshixin:before {
      color: red
    }

    .collect {
      font-size: 17px;
      color: #000000;
      cursor: pointer;
    }

    .info {
        padding: 0 24px;
    }

    .avatar {
      margin-right: 15px;
      height: 74px;
      width: 74px;
      border-radius: 100px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .location {
      font-size: 19px;
    }

    .country {
      padding-left: 8px;
      font-size: 18px;
      color: #0B0B0B;
      line-height: 25px;
    }
  }

  .subtitle {
    flex: 1;
    border: 1px solid rgba(31, 102, 255, 0.3);
    border-radius: 19px;
    padding: 13px 24px;
    overflow: scroll;
    overflow-x: hidden;
    white-space: pre-line;
    word-break: break-all;
    @include scrollBar;
  }

  .el-dialog__body {
    flex:1;
    overflow: auto;
    padding: 0px;
  }

  .el-dialog__header {
    display: none;
  }

  .el-edit-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .top {
    position: absolute;
    width: 100%;
    height: $title-top;
    left: 0;
    top: 0;
  }

  .label-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .label {
    height: 26px;
    background: #D6E3FF;
    padding: 0 17px;
    line-height: 26px;
    border-radius: 8px;
    color: #1F66FF;
    font-size: 14px;
  }

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    min-height: 100px;
    padding-top: $title-top;
  }

  .close {
    width: 34px;
    height: 34px;
    margin: 27px auto;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }

  .mb12 {
    margin-bottom: 12px;
  }

  .mb18 {
    margin-bottom: 18px;
  }
}
</style>

