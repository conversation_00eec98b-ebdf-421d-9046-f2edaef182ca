<template>
  <div class="w h flex flex-col justify-center items-center no-conversion">
    <img class="empty mb-10" :class="{'empty-h5' : source === 'h5'}" src="@/assets/images/empty5.png" />
    <div class="empty-text" :style="{ color: strColor }">{{ msg }}</div>
  </div>
</template>

<script>
export default {
  props: {
    msg: {
      type: String,
      default: '暂无数据'
    },
    strColor: {
      type: String,
      default: '#8C8C8C'
    },
    source: {
      type: String,
      default: 'default'
    }
  }
}
</script>

<style lang="scss" scoped>
.empty {
  width: 426px;
  height: 331px;
}
.empty-h5 {
  width: 600px;
  height: 600px;
}
.mb-10 {
  margin-bottom: -40px;
}
.no-conversion{
  .empty-text {
    font-weight: 400;
    font-size: 14px;
  }
}

</style>
