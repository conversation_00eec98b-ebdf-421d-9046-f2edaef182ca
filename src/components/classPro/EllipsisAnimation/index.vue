<template>
  <div class="ellipsis-animation">
    <span>{{ text }}</span>
    <span v-for="(dot, index) in dots" :key="index" class="dot">{{ dot }}</span>
  </div>
</template>

<script>
export default {
  name: 'EllipsisAnimation',
  props: {
    // 前面显示的文本
    text: {
      type: String,
      default: '加载中'
    },
    // 最大点数
    maxDots: {
      type: Number,
      default: 3
    },
    // 动画间隔(毫秒)
    interval: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      dots: [],
      timer: null,
      dotCount: 0
    }
  },
  mounted() {
    this.startAnimation()
  },
  methods: {
    // 开始动画
    startAnimation() {
      this.timer = setInterval(() => {
        this.dotCount = (this.dotCount + 1) % (this.maxDots + 1)
        this.dots = Array(this.dotCount).fill('.')
      }, this.interval)
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer)
    }
  }
}
</script>

<style scoped lang='scss'>
.dot {
  display: inline-block;
  width: 0.5em;
  text-align: center;
  transition: opacity 0.3s;
}
</style>
