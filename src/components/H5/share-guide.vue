<template>
  <div v-if="show" class="share-guide" @click="close">
    <img :src="iconShareGuide" alt="" />
    <span class="f1">{{ title }}</span>
    <span class="f2">{{ subtitle }}</span>
  </div>
</template>

<script>
import iconShareGuide from '@/assets/H5/icon-share-guide.svg'
export default {
  props: {
    title: {
      type: String,
      default: '立即分享给好友'
    },
    subtitle: {
      type: String,
      default: '点击屏幕右上角将本页分享给好友'
    }
  },
  data () {
    return {
      iconShareGuide,
      show: false
    }
  },
  methods: {
    open () {
      this.show = true
    },
    close () {
      this.show = false
    }
  }
}
</script>

<style lang="scss" scoped>
.share-guide {
    width: 100%;
    height: 100%;
    position: fixed;
    background: rgba($color: #000000, $alpha: 0.7);
    z-index: 110;
    left: 0;
    top: 0;

    img {
        width: 110px;
        height: 150px;
        object-fit: contain;
        position: absolute;
        right: 37px;
        top: 23px;
    }

    .f1 {
        width: 100%;
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
        color: #FFFFFF;
        position: absolute;
        top: 177px;
        text-align: center;
    }

    .f2 {
        width: 100%;
        font-weight: 400;
        font-size: 14px;
        line-height: 28px;
        color: #FFFFFF;
        position: absolute;
        top: 214px;
        text-align: center;
    }
}
</style>
