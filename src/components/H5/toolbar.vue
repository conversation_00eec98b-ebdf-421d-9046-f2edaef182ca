<template>
  <div class="toolbar">
    <div class="left" @click="$emit('prev')">
      <van-icon name="arrow-left" />
      <span>返回</span>
    </div>
    <div class="center">
      <slot name="center"></slot>
    </div>
    <div class="right">
      <img v-if="needHome" class="home" :src="iconHome" alt="" @click="goHome" />
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
import iconHome from '@/assets/H5/icon-home.svg'
import { isApp } from '@/utils/index.js'
export default {
  props: {
    needHome: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      iconHome,
      isApp: isApp()
    }
  },
  methods: {
    goHome () {
      this.$router.push({ 'path': '/h5/index' })
      sessionStorage.removeItem('from')
    }
  }
}
</script>

<style lang="scss" scoped>
.toolbar {
    width: 100%;
    height: 50px;
    display: flex;
    position:absolute;
    top: 0;
    background: #FFFFFF;
    z-index: 99;

    .left {
        height: 100%;
        display: flex;
        align-items: center;
        position: absolute;
        left: 6px;
        z-index: 11;

        .van-icon {
            font-size: 22px;
            width: 22px;
            height: 22px;
        }

        span {
            color: #000000;
            font-size: 16px;
        }
    }

    .center {
        position: absolute;
        transform: translate(-50%, 0);
        left: 50%;
    }

    .right {
        position: absolute;
        height: 100%;
        right: 12px;
        display: flex;
        align-items: center;

        .home {
            width: 30px;
            height: 30px;
            object-fit: contain;
        }
    }
}
</style>
