<template>
  <div
    class="draggable-button"
    :style="{ top: position.y + 'px', left: position.x + 'px' }"
    @mousedown="startDrag"
    @touchstart="startDrag"
  >
    <!-- 圆形按钮的内容 -->
    <slot>+</slot>
  </div>
</template>

<script>
export default {
  name: 'DraggableButton',
  data() {
    return {
      position: { x: 100, y: 100 }, // 初始位置
      isDragging: false,
      startX: 0,
      startY: 0
    }
  },
  methods: {
    startDrag(event) {
      this.isDragging = true
      const e = event.type === 'touchstart' ? event.touches[0] : event
      this.startX = e.clientX - this.position.x
      this.startY = e.clientY - this.position.y

      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('touchmove', this.onDrag, { passive: false })
      document.addEventListener('mouseup', this.stopDrag)
      document.addEventListener('touchend', this.stopDrag)
    },
    onDrag(event) {
      if (!this.isDragging) return
      const e = event.type === 'touchmove' ? event.touches[0] : event
      this.position.x = e.clientX - this.startX
      this.position.y = e.clientY - this.startY

      // 限制按钮在窗口范围内
      this.position.x = Math.max(0, Math.min(window.innerWidth - 50, this.position.x))
      this.position.y = Math.max(0, Math.min(window.innerHeight - 50, this.position.y))
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('touchmove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
      document.removeEventListener('touchend', this.stopDrag)
    }
  }
}
</script>

  <style scoped>
  .draggable-button {
    position: fixed;
    width: 50px;
    height: 50px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: grab;
    user-select: none;
    z-index: 1000;
  }
  .draggable-button:active {
    cursor: grabbing;
  }
  </style>
