<template>
  <div v-if="showStatus" class="network-status" :class="statusClass">
    <div class="status-content">
      <i class="status-icon" :class="iconClass"></i>
      <span class="status-text">{{ statusText }}</span>
      <button v-if="showRetryButton" class="retry-btn" @click="handleRetry">
        重试
      </button>
    </div>
  </div>
</template>

<script>
import networkMonitor from '@/utils/networkMonitor'

export default {
  name: 'NetworkStatus',
  data() {
    return {
      networkStatus: {
        online: true,
        reconnecting: false,
        attempts: 0,
        quality: 'good'
      },
      showStatus: false,
      autoHideTimer: null,
      lastStatusChange: null
    }
  },
  computed: {
    statusClass() {
      if (!this.networkStatus.online) {
        return 'status-offline'
      }
      if (this.networkStatus.reconnecting) {
        return 'status-reconnecting'
      }
      if (this.networkStatus.quality === 'poor') {
        return 'status-poor'
      }
      return 'status-online'
    },
    iconClass() {
      if (!this.networkStatus.online) {
        return 'icon-offline'
      }
      if (this.networkStatus.reconnecting) {
        return 'icon-reconnecting'
      }
      if (this.networkStatus.quality === 'poor') {
        return 'icon-poor'
      }
      return 'icon-online'
    },
    statusText() {
      if (!this.networkStatus.online) {
        if (this.networkStatus.attempts > 0) {
          return `网络断开 (重试 ${this.networkStatus.attempts}/${5})`
        }
        return '网络连接断开'
      }
      if (this.networkStatus.reconnecting) {
        return '正在重新连接...'
      }
      if (this.networkStatus.quality === 'poor') {
        return '网络信号较弱'
      }
      return '网络连接正常'
    },
    showRetryButton() {
      return !this.networkStatus.online && !this.networkStatus.reconnecting
    }
  },
  mounted() {
    if (this.isElectronEnvironment()) {
      this.initNetworkMonitor()
    }
  },
  beforeDestroy() {
    if (this.networkStatusListener) {
      networkMonitor.removeListener(this.networkStatusListener)
    }
    if (this.autoHideTimer) {
      clearTimeout(this.autoHideTimer)
    }
  },
  methods: {
    initNetworkMonitor() {
      this.networkStatus = { ...networkMonitor.getStatus(), quality: 'good' }
      this.networkStatusListener = (status) => {
        const oldStatus = { ...this.networkStatus }
        this.networkStatus = { ...this.networkStatus, ...status }
        if (this.hasStatusChanged(oldStatus, this.networkStatus)) {
          this.updateVisibility()
        }
      }

      networkMonitor.addListener(this.networkStatusListener)
      setTimeout(() => {
        this.updateVisibility()
      }, 8000)
    },

    updateVisibility() {
      if (this.autoHideTimer) {
        clearTimeout(this.autoHideTimer)
        this.autoHideTimer = null
      }
      if (!this.networkStatus.online || this.networkStatus.reconnecting || this.networkStatus.quality === 'poor') {
        this.showStatus = true
      } else {
        this.showStatus = false
      }
    },

    handleRetry() {
      networkMonitor.manualReconnect()
    },
    isElectronEnvironment() {
      return !!(window.ipc || window.require || window.process?.versions?.electron)
    },
    hasStatusChanged(oldStatus, newStatus) {
      const keyFields = ['online', 'reconnecting', 'quality']

      for (const field of keyFields) {
        if (oldStatus[field] !== newStatus[field]) {
          if (field === 'quality') {
            const wasPoor = oldStatus.quality === 'poor'
            const isPoor = newStatus.quality === 'poor'
            if (wasPoor !== isPoor) {
              return true
            }
          } else {
            return true
          }
        }
      }

      return false
    }
  }
}
</script>

<style scoped>
.network-status {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 200px;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

.status-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-text {
  flex: 1;
  font-weight: 500;
}

.retry-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s ease;
}

.retry-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.status-online {
  background: rgba(76, 175, 80, 0.9);
  color: white;
}

.status-online .icon-online {
  background: #4caf50;
}

.status-offline {
  background: rgba(244, 67, 54, 0.9);
  color: white;
}

.status-offline .icon-offline {
  background: #f44336;
}

.status-reconnecting {
  background: rgba(255, 152, 0, 0.9);
  color: white;
}

.status-reconnecting .icon-reconnecting {
  background: #ff9800;
  animation: pulse 1.5s infinite;
}

.status-poor {
  background: rgba(255, 193, 7, 0.9);
  color: #333;
}

.status-poor .icon-poor {
  background: #ffc107;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@media (max-width: 768px) {
  .network-status {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
  }
}
</style>
