<template>
  <div class="layout">
    <navbar @showExchange="showExchange" />
    <div class="layout-nav-box">
      <div class="new-nav">
        <div class="w flex justify-center">
          <img class="avatar" :src="avatar || DefaultAvatar" alt="" />
        </div>
        <div class="speech">
          {{ welcomeWord }}
          <template v-if="name && name !== ''">，{{ name }}</template>
          <template v-else-if="mobile && mobile !== ''">，{{ mobile }}</template>
        </div>
        <div>
          <div v-if="menuList.recommend && +menuList.recommend === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 1}" @click="handleClick(1)">
            <img v-if="index === 1" class="nav-icon" src="../../assets/images/dashboard/new/icon-1.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-1-base.svg" />
            <div class="nav-text">推荐</div>
          </div>
          <div v-if="menuList.digitalBook && +menuList.digitalBook === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 4}" @click="handleClick(4)">
            <img v-if="index === 4" class="nav-icon" src="../../assets/images/dashboard/new/icon-4.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-4-base.svg" />
            <div class="nav-text">数字教材</div>
          </div>
          <div v-if="menuList.qingguoketang && +menuList.qingguoketang === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 11}" @click="handleClick(11)">
            <img v-if="index === 11" class="nav-icon" src="../../assets/images/dashboard/new/icon-11.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-11-base.svg" />
            <div class="nav-text">精品课程</div>
          </div>
          <div v-if="menuList.aiCourse && +menuList.aiCourse === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 2}" @click="handleClick(2)">
            <img v-if="index === 2" class="nav-icon" src="../../assets/images/dashboard/new/icon-2.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-2-base.svg" />
            <div class="nav-text">双师AI课</div>
          </div>
          <div v-if="menuList.aiTraining && +menuList.aiTraining === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 16}" @click="handleClick(16)">
            <img v-if="index === 16" class="nav-icon" src="../../assets/images/dashboard/new/icon-16.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-16-base.svg" />
            <div class="nav-text">AI实验室</div>
          </div>
          <div v-if="menuList.liveCourse && +menuList.liveCourse === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 3}" @click="handleClick(3)">
            <img v-if="index === 3" class="nav-icon" src="../../assets/images/dashboard/new/icon-3.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-3-base.svg" />
            <div class="nav-text">空中课堂</div>
          </div>
          <div v-if="menuList.activity && +menuList.activity === 1" class="nav-card flex items-center" :class="{'nav-card-active': index === 5}" @click="handleClick(5)">
            <img v-if="index === 5" class="nav-icon" src="../../assets/images/dashboard/new/icon-5.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-5-base.svg" />
            <div class="nav-text">活动</div>
          </div>
        </div>
        <div class="nav-my">我的</div>
        <div class="nav-my-menu">
          <div v-if="menuList.myDigitalBook && +menuList.myDigitalBook === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 7}" @click="handleClick(7)">
            <img v-if="index === 7" class="nav-icon" src="../../assets/images/dashboard/new/icon-7.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-7-base.svg" />
            <div class="nav-text">我的教材</div>
            <div v-show="unReadDigitalHomeworkCount" class="tips-dots"></div>
          </div>
          <div v-if="menuList.myQingguoketang && +menuList.myQingguoketang === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 12}" @click="handleClick(12)">
            <img v-if="index === 12" class="nav-icon" src="../../assets/images/dashboard/new/icon-12.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-12-base.svg" />
            <div class="nav-text">我的精品课</div>
          </div>
          <div v-if="menuList.myCourse && +menuList.myCourse === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 6}" @click="handleClick(6)">
            <img v-if="index === 6" class="nav-icon" src="../../assets/images/dashboard/new/icon-6.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-6-base.svg" />
            <div class="nav-text">我的课程</div>
            <!-- <div v-if="!school" class="nav-tips-btn" @click.stop="guidShow = true">激活课程</div> -->
          </div>
          <div v-if="menuList.myAiTraining && +menuList.myAiTraining === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 15}" @click="handleClick(15)">
            <img v-if="index === 15" class="nav-icon" src="../../assets/images/dashboard/new/icon-15.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-15-base.svg" />
            <div class="nav-text">我的AI实验</div>
          </div>
          <div v-if="menuList.mySubject && +menuList.mySubject === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 13}" @click="handleClick(13)">
            <img v-if="index === 13" class="nav-icon" src="../../assets/images/dashboard/new/icon-13.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-13-base.svg" />
            <div class="nav-text">学科教研</div>
          </div>
          <div v-if="menuList.myResource && +menuList.myResource === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 14}" @click="handleClick(14)">
            <img v-if="index === 14" class="nav-icon" src="../../assets/images/dashboard/new/icon-14.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-14-base.svg" />
            <div class="nav-text">数字资源</div>
          </div>
          <div v-if="menuList.myActivity && +menuList.myActivity === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 8}" @click="handleClick(8)">
            <img v-if="index === 8" class="nav-icon" src="../../assets/images/dashboard/new/icon-8.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-8-base.svg" />
            <div class="nav-text">我的活动</div>
          </div>
          <div v-if="menuList.myData && +menuList.myData === 1" class="nav-card flex items-center mb-7" :class="{'nav-card-active': index === 9}" @click="handleClick(9)">
            <img v-if="index === 9" class="nav-icon" src="../../assets/images/dashboard/new/icon-9.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-9-base.svg" />
            <div class="nav-text">我的数据</div>
          </div>
          <div v-if="menuList.myClass && +menuList.myClass === 1" class="nav-card flex items-center" :class="{'nav-card-active': index === 10}" @click="handleClick(10)">
            <img v-if="index === 10" class="nav-icon" src="../../assets/images/dashboard/new/icon-10.svg" />
            <img v-else class="nav-icon" src="../../assets/images/dashboard/new/icon-10-base.svg" />
            <div class="nav-text">班级管理</div>
          </div>
        </div>
        <!--        <div class="help-box flex items-center" @click="handleHelp">-->
        <!--          <img class="nav-icon" src="../../assets/images/dashboard/new/help.svg" />-->
        <!--          <div class="nav-text">帮助指南</div>-->
        <!--        </div>-->
      </div>
      <!--      <div class="help-box flex items-center" @click="handleHelp">-->
      <!-- <div class="help-box1" @click="handleHelp">
        <img class="nav-icon" src="../../assets/images/dashboard/new/help.svg" />
        <div class="nav-text">帮助指南</div>
      </div> -->

      <div class="new-body">
        <router-view class="router" @showTeacherInfo="showTeacherInfo" @changeIndex="setIndex" />
      </div>

    </div>
    <exchange-dialog :dialog-visible="dialogExchange" class="classpro-dialog" @closeExchange="closeExchange" />
    <teacher-info-dialog :dialog-visible="dialogTeacherInfo" :teacher-id="teacherId" @closeDialog="closeTeacherInfo" />
    <guidSchool v-if="guidShow" :show="guidShow" @close="guidShow = false" />
    <div class="ai-iframe" v-show='false'>
      <iframe
        id="scratch-iframe"
        ref="scratchFrame"
        :src="iframeUrl"
        style="border: none"
        width="100%"
        height="100%"
        allowfullscreen
        allow="microphone *; camera *"
        sandbox="allow-same-origin allow-scripts allow-popups allow-modals"
        @load="handleLoad"
      ></iframe>
    </div>
  </div>
</template>

<script>
import ExchangeDialog from './components/exchangeDialog.vue'
import TeacherInfoDialog from '@/components/classPro/TeacherInfoDialog/index.vue'
import guidSchool from '@/components/classPro/ClassBind/guidSchool.vue'
import Navbar from './components/Navbar.vue'
import DefaultAvatar from '@/assets/images/profile.png'
import { mapGetters } from 'vuex'
import { getConfig } from '@/api/config.js'
import { getStuCourseList } from '@/api/lesson-api.js'
import { getUserDigitalHomeworkNotice } from '@/api/digital-api.js'

export default {
  components: {
    Navbar,
    ExchangeDialog,
    TeacherInfoDialog,
    guidSchool
  },
  data () {
    return {
      DefaultAvatar,
      unReadDigitalHomeworkCount: 0,
      tableData: [],
      guidShow: false,
      dialogExchange: false,
      dialogTeacherInfo: false,
      teacherId: undefined,
      index: 0,
      menuList: {
        activity: '1',
        aiCourse: '1',
        digitalBook: '1',
        liveCourse: '1',
        myActivity: '1',
        myClass: '1',
        myCourse: '1',
        myData: '1',
        myDigitalBook: '1',
        recommend: '1',
        qingguoketang: '1',
        myQingguoketang: '1',
        mySubject: '1',
        myResource: '1',
        aiTraining: '1',
        myAiTraining: '1'
      },
      iframeUrl: ''
    }
  },
  computed: {
    schoolName () {
      return this.school ? this.school.name : ''
    },
    ...mapGetters([
      'id',
      'name',
      'avatar',
      'mobile',
      'school',
      'childName',
      'userRelations',
      'showCourseCenter'
    ]),
    welcomeWord () {
      const h = new Date().getHours()
      const word = `${
        h <= 12
          ? '上午好'
          : (h > 12) && (h < 18)
            ? '下午好'
            : '晚上好'
      }`
      return word
    }
  },
  // watch: {
  //   '$route.path': {
  //     handler: function (val) {
  //       // 高亮标签
  //       if (val === '/classpro') {
  //         this.index = 1
  //       } else if (val.indexOf('aiCourse') !== -1) {
  //         this.index = 2
  //       } else if (val.indexOf('skyCourse') !== -1) {
  //         this.index = 3
  //       } else if (val.indexOf('digitalbooks') !== -1 && val.indexOf('digitalbooks/detail') === -1) {
  //         this.index = 4
  //       } else if (val.indexOf('activity') !== -1) {
  //         this.index = 5
  //       } else if (val.indexOf('myCourse') !== -1) {
  //         this.index = 6
  //       } else if (val.indexOf('myDigitalbooks') !== -1) {
  //         this.index = 7
  //       } else if (val.indexOf('myActivity') !== -1) {
  //         this.index = 8
  //       } else if (val.indexOf('myData') !== -1) {
  //         this.index = 9
  //       } else if (val.indexOf('myClass') !== -1) {
  //         this.index = 10
  //       } else {
  //         this.index = ''
  //       }
  //     },
  //     immediate: true
  //   }
  // },
  mounted () {
    const menuList = window.localStorage.getItem('menuList')
    if (menuList) {
      this.menuList = JSON.parse(menuList)
    }
    this._getStuCourseList()
    this._getUserDigitalHomeworkNotice()
    const timer = setTimeout(() => {
      this.iframeUrl = 'https://chatpub.com.cn/chat/share?shareId=52c0mrcsmrrrpyl6jx7yymxs'
      clearTimeout(timer)
    }, 1000)
  },
  methods: {
    handleLoad() {
      const timer = setTimeout(() => {
        const aiEl = document.querySelector('.ai-iframe')
        if (aiEl) {
          aiEl.remove()
        }
        clearTimeout(timer)
      }, 5000)
    },
    setIndex(index) {
      this.index = index
    },
    async _getStuCourseList () {
      const params = { 'studentCourseListType': 'ASSISTANT' }
      const { data } = await getStuCourseList(params, false)
      if (data && data.length > 0) {
        this.tableData = data.filter(v => {
          return +v.id !== 0
        })
      } else {
        this.tableData = []
      }
      this.indexPageSelect()
      this._getConfig()
    },
    async _getUserDigitalHomeworkNotice () {
      // 获取教材的任务红点提示
      const { data } = await getUserDigitalHomeworkNotice()
      this.unReadDigitalHomeworkCount = data.unReadDigitalHomeworkCount
    },
    showExchange () {
      this.dialogExchange = true
    },
    closeExchange () {
      this.dialogExchange = false
    },
    showTeacherInfo (teacherId) {
      this.teacherId = teacherId
      this.dialogTeacherInfo = true
    },
    closeTeacherInfo () {
      this.teacherId = undefined
      this.dialogTeacherInfo = false
    },
    handleClick (type) {
      this.index = type
      window.sessionStorage.setItem('menuSelectIndex', this.index)
      let path = '/classpro'
      switch (type) {
        case 2:
          path = '/classpro/aiCourse'
          break
        case 3:
          path = '/classpro/skyCourse'
          break
        case 4:
          path = '/classpro/digitalbooks'
          break
        case 5:
          path = '/classpro/activity'
          break
        case 6:
          path = '/classpro/myCourse'
          break
        case 7:
          path = '/classpro/myDigitalbooks'
          break
        case 8:
          path = '/classpro/myActivity'
          break
        case 9:
          path = '/classpro/myData'
          break
        case 10:
          path = '/classpro/myClass'
          break
        case 11:
          path = '/classpro/onlineClass'
          break
        case 12:
          path = '/classpro/myOnlineClass'
          break
        case 13:
          path = '/classpro/mySubject'
          break
        case 14:
          path = '/classpro/myResource'
          break
        case 15:
          path = '/classpro/myAitraining'
          break
        case 16:
          path = '/classpro/aitraining'
          break
        default:
          path = '/classpro'
          break
      }
      if (this.$route.path !== path) {
        this.$router.push({ path })
      }
    },
    handleHelp () {
      const url = `${window.location.origin}/#/help`
      window.open(url, '_blank', 'width=600,height=800,left=200,top=100,menubar=0,scrollbars=1,resizable=1,status=1,titlebar=0,toolbar=0,location=1')
    },
    // 获取菜单配置
    async _getConfig () {
      // this.menuList = {
      //   activity: '1',
      //   aiCourse: '1',
      //   digitalBook: '1',
      //   liveCourse: '1',
      //   myActivity: '1',
      //   myClass: '1',
      //   myCourse: '1',
      //   myData: '1',
      //   myDigitalBook: '1',
      //   recommend: '1',
      //   onlineClass: '1'
      // }
      // return
      const param = {
        'configType': 'CHANNEL_CONFIG'
      }
      const { data } = await getConfig(param)
      if (data && data[0].keyValue) {
        const menu = JSON.parse(data[0].keyValue)
        // 渠道包配置 request.js 也需要修改
        let channelConfig = ''
        // 0 不显示 1 显示
        try {
          if (window.ipc) {
            const res = await window.ipc.invoke('getChannelConfig')
            channelConfig = res
            window.localStorage.setItem('channel', res)
          } else {
            const channel = window.localStorage.getItem('channel')
            if (channel) {
              channelConfig = channel
            } else {
              channelConfig = window.localStorage.getItem('headerChannel') || 'cuiya'
            }
          }
        } catch (error) {
          console.log(error)
          channelConfig = window.localStorage.getItem('channel') ||
                         window.localStorage.getItem('headerChannel') ||
                         'cuiya'
        }
        if (channelConfig === 'wenxuan') {
          if (menu['wenxuan']) {
            this.menuList = menu['wenxuan'].menu
          }
        } else if (channelConfig === 'digitalbook') {
          if (menu['digitalbook']) {
            this.menuList = menu['digitalbook'].menu
          }
        } else if (channelConfig === 'aigc') {
          if (menu['aigc']) {
            this.menuList = menu['aigc'].menu
          }
        } else if (channelConfig === 'cuiya') {
          if (menu['cuiya']) {
            this.menuList = menu['cuiya'].menu
          }
        }
        // else {
        //   this.menuList = {
        //     activity: '1',
        //     aiCourse: '1',
        //     digitalBook: '1',
        //     liveCourse: '1',
        //     myActivity: '1',
        //     myClass: '1',
        //     myCourse: '1',
        //     myData: '1',
        //     myDigitalBook: '1',
        //     recommend: '1',
        //     qingguoketang: '1',
        //     myQingguoketang: '1',
        //     mySubject: '1',
        //     myResource: '1',
        //     aigc: '0',
        //     myAiTraining: '0',
        //     aiTraining: '1'
        //   }
        // }
        this.indexPageSelect()
        window.localStorage.setItem('currentChannel', channelConfig)
        window.localStorage.setItem('menuList', JSON.stringify(this.menuList))
      }
    },
    // 根据菜单列表 显示首页内容
    indexPageSelect () {
      this.index = window.sessionStorage.getItem('menuSelectIndex')
      if (this.index) {
        this.index = parseInt(this.index)
        // this.handleClick(this.index)
        // return
      }
      if (this.$route.path !== '/classpro') return
      if (+this.menuList.recommend === 1) {
        const flag = window.sessionStorage.getItem('autoMy')
        if (!flag && this.tableData.length > 0) {
          this.handleClick(6)
        } else {
          this.handleClick(1)
        }
        window.sessionStorage.setItem('autoMy', true)
      } else if ((+this.menuList.aiCourse === 1 || +this.menuList.liveCourse === 1) && +this.menuList.digitalBook === 1) {
        this.handleClick(6)
      } else if (+this.menuList.aiCourse === 1 || +this.menuList.liveCourse === 1) {
        this.handleClick(6)
      } else if (+this.menuList.digitalBook === 1) {
        this.handleClick(4)
      } else {
        this.handleClick(1)
      }
    }
  }
}
</script>

<style lang="scss">
//:root {
//  /** 定义公共变量 */
//  --primary-color: #007bff;
//  --larger-font-size: 16px;
//  --mid-font-size: 14px;
//  --font-size: 12px;
//  --min-font-size: 8px;
//}
.layout {
  width: 100%;
  height: 100%;
  background: #F1F7FF;
  .ai-iframe{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
  }
  .layout-nav-box {
    width: 100%;
    height: calc(100% - 40px);
    padding: 10px;
    box-sizing: border-box;
    display: flex;

    .new-nav {
      width: 180px;
      //height: 100%;
      height: calc(100% - 0px);
      background: #FFFFFF;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px;
      position: relative;
      /*增加滚动条*/
      overflow-y:auto;
      overflow-x: hidden;
      &::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 5px;
        height: 1px;
      }
      //&::-webkit-scrollbar-thumb {
      //  /*滚动条里面的滑块*/
      //  border-radius: 5px;
      //  background: red;
      //}
      //&::-webkit-scrollbar-track {
      //  /*滚动条里面轨道背景*/
      //  border-radius: 5px;
      //  background: #fff;
      //}
        .avatar {
        width: 42px;
        height: 42px;
        border-radius: 50%;
      }

      .speech {
        margin-top: 10px;
        margin-bottom: 20px;
        //font-size: 14px;
        font-size: var(--font-size-XL);
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; //需要显示的行数
        //overflow: hidden;
        text-overflow: ellipsis;
        line-height: 20px;
      }

      .nav-card {
        width: 130px;
        height: 20px;
        //padding: 5px 15px;
        padding: 5px 10px 5px 10px;
        cursor: pointer;
        position: relative;

        .tips-dots {
          position: absolute;
          right: 8px;
          top: 35%;
          width: 7px;
          height: 7px;
          border-radius: 50%;
          background: #EB5757;
        }
      }

      .nav-tips-btn {
        position: absolute;
        right: -20px;
        top: -18px;
        width: 60px;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 5px 6px 6px 0px;
        background: linear-gradient(90deg, #FF512F 0%, #DD2476 100%);
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
        color: #FFF;
        font-size: 12px;
      }

      .mb-7 {
        margin-bottom: 7px;
      }

      .nav-card-active {
        border-radius: 5px;
        background: linear-gradient(270deg, #00D2FF 0%, #3A7BD5 111.54%);
        .nav-text {
          color: #FFF;
        }
      }

      .nav-icon {
        width: 15px;
        height: 15px;
      }

      .nav-text {
        color: #000;
        //font-size: 12px;
        font-size: var(--font-size-L);
        margin-left: 7px;
      }

      .nav-my {
        color: #4F4F4F;
        font-size: 14px;
        padding-left: 15px;
        margin-top: 20px;
        margin-bottom: 10px;
        width: 130px;
        //padding: 5px 15px;
      }

      //.help-box {
      //  width: 130px;
      //  //padding: 40px 15px;
      //  cursor: pointer;
      //  margin-top: 40px;
      //  margin-left: 20px;
      //  //position: absolute;
      //  //bottom: 10px;
      //}

    }
    .help-box1 {
      width: 160px;
      height: 40px;
      //padding: 5px 15px;
      cursor: pointer;
      //position: absolute;
      padding-top: 15px;
      position: fixed;
      bottom: 0px;
      //margin-left: 32px;
      padding-left: 32px;
      //margin-top: 80vh;
      margin-bottom: 10px;
      background-color: white;
      .nav-icon {
        width: 15px;
        height: 15px;
        position: fixed;
      }

      .nav-text {
        color: #000;
        //font-size: 12px;
        font-size: var(--font-size-L);
        margin-left: 22px;
        //position: relative;
      }
    }

    .new-body {
      width: calc(100% - 180px);
      padding-left: 10px;
      box-sizing: border-box;
    }
    .nav-my-menu{
      margin-bottom: 50px;
    }
  }
}
</style>
