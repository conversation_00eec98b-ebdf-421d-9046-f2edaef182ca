<template>
  <div class="layout">
    <navbar @showExchange="showExchange" />
    <router-view class="router" @showTeacherInfo="showTeacherInfo" />
    <exchange-dialog :dialog-visible="dialogExchange" class="classpro-dialog" @closeExchange="closeExchange" />
    <teacher-info-dialog :dialog-visible="dialogTeacherInfo" :teacher-id="teacherId" @closeDialog="closeTeacherInfo" />
  </div>
</template>

<script>
import ExchangeDialog from './components/exchangeDialog.vue'
import TeacherInfoDialog from '@/components/classPro/TeacherInfoDialog/index.vue'
import Navbar from './components/Navbar.vue'
export default {
  components: {
    Navbar,
    ExchangeDialog,
    TeacherInfoDialog
  },
  data () {
    return {
      dialogExchange: false,
      dialogTeacherInfo: false,
      teacherId: undefined
    }
  },
  methods: {
    showExchange () {
      this.dialogExchange = true
    },
    closeExchange () {
      this.dialogExchange = false
    },
    showTeacherInfo (teacherId) {
      this.teacherId = teacherId
      this.dialogTeacherInfo = true
    },
    closeTeacherInfo () {
      this.teacherId = undefined
      this.dialogTeacherInfo = false
    }
  }
}
</script>

<style lang="scss">
.layout {
  height: 100%;
  width: 100%;
  background: #F8FAFF;
}
</style>
