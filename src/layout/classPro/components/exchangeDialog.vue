<template>
  <el-dialog
    :show-close="false"
    width="30%"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
  >
    <div class="exchange-dialog">
      <img class="top" src="../../../assets/images/bg-top.png" alt="" />
      <div class="title">课程兑换</div>
      <el-input
        v-model="exchangeCode"
        type="text"
        auto-complete="off"
        placeholder="请输入兑换码"
      >
        <img
          slot="prefix"
          class="el-input__icon input-icon"
          src="@/assets/images/exchange.png"
        />
      </el-input>
      <div class="button" :class="[ validInput ? 'button-blue' : 'button-grey' ]" @click="_exchangeCourse">使用兑换码</div>
    </div>
    <img class="close" src="../../../assets/images/close.png" alt="关闭按钮" @click="closeExchange" />
  </el-dialog>
</template>

<script>
import { exchangeCourse } from '@/api/course-api.js'
import { message } from '@/utils/singeMessage.js'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    }
  },
  data () {
    return {
      exchangeCode: ''
    }
  },
  computed: {
    validInput () {
      return this.exchangeCode !== ''
    }
  },
  methods: {
    closeExchange () {
      this.$emit('closeExchange')
    },
    // 兑换课程
    _exchangeCourse () {
      if (!this.validInput) return
      exchangeCourse({ exchangeCode: this.exchangeCode })
        .then(response => {
          message({
            message: '兑换成功',
            duration: 5 * 1000
          })
          this.$router.go(0)
        })
        .catch(error => {
          // if (error.message !== 'Error') {
          if (error.code !== 200) {
            this.exchangeCode = ''
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
$vw_design_width: 965;
$vw_design_height: 650;
@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}

.exchange-dialog {
    background:white;
    padding: ui_h(26) ui_h(32) ui_h(40);
    border-radius: 20px;

    .top {
        position: absolute;
        width: 100%;
        left: 0;
        top: 0;
    }

    .title {
        font-size: 18px;
        font-weight: 500;
        color: #0B0B0B;
        line-height: 25px;
        margin-bottom: ui_h(60);
    }

    .button {
        width: 80%;
        height: 30px;
        border-radius: 16px;
        font-size: 16px;
        font-weight: 500;
        line-height: 30px;
        text-align: center;
        margin: ui_h(31) auto 0;
        cursor: pointer;
    }

    .button-blue {
        background: #1F66FF;
        box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
        color: #FFFFFF;
        &:hover {
          background: #6193FF;
          box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41);
        }
    }

    .button-grey {
        background: #BFBFBF;
        box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), 1px 1px 1px 0px #E7E7E7, -1px -1px 1px 0px rgba(129, 129, 134, 0.32);
        color: #FFFFFF;
    }

    ::v-deep .el-input__inner {
        border-radius: 0px;
        border: 0px solid transparent;
        border-bottom: 1px solid rgba(151, 151, 151, 0.15);
    }

    ::v-deep .el-input__prefix {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .input-icon {
      width: 15px;
      height: 13px;
    }
}

.close {
    width: 34px;
    height: 34px;
    margin: ui_h(27) auto;
    display: flex;
    justify-content: center;
    cursor: pointer;
}
</style>
