<template>
  <div class="w h">
    <router-view />
    <PopWechat v-if="show" :show="show" :code="code" @close="popClose" />
  </div>
</template>
<script>
import PopWechat from '@/views/parent/components/PopWechat.vue'
import localStore from '@/utils/local-storage.js'
import { getPartentToken } from '@/utils/auth'
import { getGzhUserInfo } from '@/api/partent-api'
export default {
  components: {
    PopWechat
  },
  data () {
    return {
      appid: 'wxfa4a06f2648b3c54',
      code: '',
      show: false
    }
  },
  async mounted () {
    const isScan = this.$route.query && this.$route.query.isScan
    const token = getPartentToken()
    if (isScan) {
      // localStore.save('scanPath', this.$route.fullPath)
      if (token && this.$route.path.indexOf('/parent/hassClassInfo') === -1) {
        const { data } = await getGzhUserInfo()
        if (!data.subscribe) {
          this.show = true
        }
      }
    }
    if (!token) {
      if (!(this.$route.path.indexOf('/parent/checkLogin') !== -1 || isScan)) {
        this.handleGetWxCode()
      }
    }
    this.$bus.$on('popWechatOpen', () => {
      this.show = true
    })
  },
  beforeDestroy () {
    this.$bus.$off('popWechatOpen')
  },
  methods: {
    handleGetWxCode (state = 1) {
      const baseUrl = window.location.origin
      const url = `${baseUrl}/#/parent/checkLogin`
      window.location.href = `${process.env.VUE_APP_WEB_URL}get-weixin-code.html?appid=${this.appid}&scope=snsapi_userinfo&state=${state}&redirect_uri=${encodeURIComponent(url)}`
    },
    popClose () {
      this.show = false
    }
  }
}
</script>
