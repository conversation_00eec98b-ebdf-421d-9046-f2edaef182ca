<template>
  <div class="course-layout">
    <div class="header">
      <svg-icon icon-class="arrow-round" class-name="arrow-round" @click="back" />
    </div>
    <router-view class="router" @moveTo="moveTo" />
  </div>
</template>

<script>
import { getPartentToken } from '@/utils/auth'
import localStore from '@/utils/local-storage.js'
export default {
  methods: {
    moveTo (path) {
      this.$router.push({
        'path': path
      })
    },
    async back () {
      const hash = window.location.hash
      const aicourseId = this.$route.params.aicourseId
      const studentCourseId = this.$route.params.studentCourseId
      const unitId = this.$route.params.unitId
      const classId = this.$route.params.classId
      if (hash.indexOf('report') > -1) {
        this.moveTo(`/parent/course/${aicourseId}/${studentCourseId}`)
      } else if (hash.indexOf('homework') > -1) {
        // const isScan = this.$route.query.isScan
        // const token = getPartentToken()
        // const childInfo = JSON.parse(localStore.read('currChild'))
        // if (isScan && !token) {
        //   localStore.save('scanPath', '/parent/home')
        //   this.$router.push({
        //     'path': '/parent/checkLogin'
        //   })
        // } else if (+childInfo.belongClassId === +classId) {
        //   this.moveTo(`/parent/course/report/${aicourseId}/${studentCourseId}/${unitId}`)
        // } else {
        //   this.moveTo('/parent/home')
        // }
        const token = getPartentToken()
        if (token) {
          const childInfo = JSON.parse(localStore.read('currChild'))
          if (!childInfo || +childInfo.belongClassId !== +classId) {
            this.moveTo('/parent/home')
          } else {
            const homeback = localStore.read('homeworkback')
            if (homeback) {
              localStore.clear('homeworkback')
              this.moveTo(homeback)
            } else {
              this.moveTo(`/parent/course/report/${aicourseId}/${studentCourseId}/${unitId}`)
            }
          }
        } else {
          localStore.save('scanPath', '/parent/home')
          this.$router.push({
            'path': '/parent/checkLogin'
          })
        }
      } else {
        this.moveTo('/parent/home')
      }
    }
  }
}
</script>

<style lang="scss">
.course-layout {
    width: 100%;
    height: 100%;
    background: linear-gradient(359.86deg, #56CCF2 0.08%, #2F80ED 98.53%);
    display: flex;
    flex-direction: column;
    padding: 0 12px;
    padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
    overflow: hidden;

    .arrow-round {
        width: 30px;
        height: 30px;
        margin: 26px 0 23px;
        object-fit: contain;
    }

    .course-title {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        color: #FFFFFF;
    }

    .course-content {
        background: #FFFFFF;
        border: 1px solid #E0E0E0;
        border-radius: 8px;
    }

    .router {
      height: calc(100% - 79px);
    }
}
</style>
