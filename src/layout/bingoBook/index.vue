<template>
  <div class="w h">
    <keep-alive>
      <!-- 需要缓存的视图组件 -->
      <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" />
  </div>
</template>
<script>

export default {
  components: {
  },
  data () {
    return {
      appid: 'wxfa4a06f2648b3c54',
      code: '',
      show: false
    }
  },
  async mounted () {
  },
  beforeDestroy () {
  },
  methods: {

  }
}
</script>
