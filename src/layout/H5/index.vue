<template>
  <div class="w h">
    <router-view />
    <PopWechat v-if="show" :show="show" :code="code" @close="popClose" />
    <!-- <DraggableButton v-if="isApp">状态</DraggableButton> -->
  </div>
</template>
<script>
import PopWechat from '@/views/parent/components/PopWechat.vue'
import { getPartentToken } from '@/utils/auth'
import { getGzhUserInfo } from '@/api/partent-api'
import localStore from '@/utils/local-storage.js'
import { isWeChatBrowser, isApp } from '@/utils/index'
// import DraggableButton from '@/components/H5/DraggableButton.vue'
export default {
  components: {
    PopWechat
    // DraggableButton
  },
  data () {
    return {
      appid: 'wxfa4a06f2648b3c54',
      code: '',
      show: false,
      isApp: isApp()
    }
  },
  watch: {
    '$route.path': {
      handler: function (val) {
        this.isLogin()
      },
      immediate: true
    }
  },
  async mounted () {
    const token = getPartentToken()
    if (token && this.$route.path.indexOf('/h5/hassClassInfo') === -1) {
      const { data } = await getGzhUserInfo()
      if (!data.subscribe && isWeChatBrowser && !isApp) {
        this.show = true
      }
    }
    this.isLogin()
    this.$bus.$on('popWechatOpen', () => {
      this.show = true
    })
  },
  beforeDestroy () {
    this.$bus.$off('popWechatOpen')
  },
  methods: {
    isLogin () {
      const token = getPartentToken()
      if (!token) {
        // 需要登录的页面
        const whiteList = ['/h5/my', '/h5/my-info', '/h5/my-jion', '/h5/apply', '/h5/submit-work']
        if (whiteList.indexOf(this.$route.path) !== -1) {
          localStore.save('h5ScanPath', this.$route.fullPath)
          if (isWeChatBrowser()) {
            this.handleGetWxCode()
          } else if (isApp) {
            if (window.webkit && window.webkit.messageHandlers) {
              window.webkit.messageHandlers.bingo_action.postMessage('event_no_login')
            } else if (window.bingo_action) {
              window.bingo_action.postMessage('event_no_login')
            }
          }
        }
      // if (!(this.$route.path.indexOf('/h5/checkLogin') !== -1 || this.$route.path.indexOf('/h5/checkPraise') !== -1)) {
      //   this.handleGetWxCode()
      // }
      }
    },
    handleGetWxCode (state = 1) {
      const baseUrl = window.location.origin
      const url = `${baseUrl}/#/h5/checkLogin`
      window.location.href = `${process.env.VUE_APP_WEB_URL}get-weixin-code.html?appid=${this.appid}&scope=snsapi_userinfo&state=${state}&redirect_uri=${encodeURIComponent(url)}`
    },
    popClose () {
      this.show = false
    }
  }
}
</script>
