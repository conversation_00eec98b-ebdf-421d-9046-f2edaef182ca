<svg width="182px" height="121px" viewBox="0 0 182 121" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="60.6389567%" y1="100%" x2="60.6389567%" y2="0%" id="linearGradient-1">
            <stop stop-color="#FCE965" stop-opacity="0.186178568" offset="0%"></stop>
            <stop stop-color="#FFCE00" stop-opacity="0.432128906" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="53.8981691%" y1="61.8486745%" x2="59.4403202%" y2="38.1513255%" id="linearGradient-2">
            <stop stop-color="#FCE965" stop-opacity="0.158059714" offset="0%"></stop>
            <stop stop-color="#FFCE00" stop-opacity="0.553772399" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-11.0229431%" y1="50%" x2="62.5893673%" y2="85.4054736%" id="linearGradient-3">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="27.1138134%" y1="50%" x2="54.7215456%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="11.0097459%" y1="50%" x2="58.0439029%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-11.0229431%" y1="50%" x2="62.5893673%" y2="57.5517074%" id="linearGradient-6">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="40.1790756%" y1="50%" x2="52.0261105%" y2="100%" id="linearGradient-7">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="40.0236246%" y1="50%" x2="52.0581809%" y2="100%" id="linearGradient-8">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-11.0229431%" y1="50%" x2="62.5893673%" y2="56.3029258%" id="linearGradient-9">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-11.0229431%" y1="50%" x2="62.5893673%" y2="53.7220434%" id="linearGradient-10">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-11.0229431%" y1="50%" x2="62.5893673%" y2="52.6896061%" id="linearGradient-11">
            <stop stop-color="#FCE965" offset="0%"></stop>
            <stop stop-color="#FFCE00" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="68.3855412%" y1="24.7802538%" x2="41.1245728%" y2="65.6738582%" id="linearGradient-12">
            <stop stop-color="#F9E460" offset="0%"></stop>
            <stop stop-color="#FFC028" offset="100%"></stop>
        </linearGradient>
        <path d="M5.42753882,58.5698403 C-5.57187255,29.7191568 0.147212408,10.9716337 22.5847937,2.32727121 C33.0970034,-0.471775032 42.4328395,-0.745978069 50.5923018,1.5046621 C61.9568568,5.16348012 67.3705487,12.2890114 66.8333775,22.8812559 C62.8482403,46.413065 33.256888,22.8734191 19.8287604,38.5842297 C14.9495371,48.1660888 10.14913,54.827959 5.42753882,58.5698403 Z" id="path-13"></path>
        <linearGradient x1="52.7350679%" y1="35.7545776%" x2="57.3029842%" y2="84.0922296%" id="linearGradient-15">
            <stop stop-color="#946E13" offset="0%"></stop>
            <stop stop-color="#8F600C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.9101501%" y1="12.3522769%" x2="57.917244%" y2="66.4826417%" id="linearGradient-16">
            <stop stop-color="#946E13" offset="0%"></stop>
            <stop stop-color="#3D2700" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="54.0752589%" y1="38.2063706%" x2="57.0055997%" y2="79.0051221%" id="linearGradient-17">
            <stop stop-color="#946E13" offset="0%"></stop>
            <stop stop-color="#3D2700" offset="100%"></stop>
        </linearGradient>
        <filter x="-12.9%" y="-13.8%" width="125.8%" height="127.5%" filterUnits="objectBoundingBox" id="filter-18">
            <feGaussianBlur stdDeviation="2.74595465" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-15.8%" y="-20.4%" width="131.5%" height="140.8%" filterUnits="objectBoundingBox" id="filter-19">
            <feGaussianBlur stdDeviation="4.34776154" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-32.1%" y="-40.7%" width="164.1%" height="181.3%" filterUnits="objectBoundingBox" id="filter-20">
            <feGaussianBlur stdDeviation="4.34776154" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="20.2335603%" y1="24.3306085%" x2="62.6551265%" y2="73.0598304%" id="linearGradient-21">
            <stop stop-color="#FDE771" offset="0%"></stop>
            <stop stop-color="#FF920F" offset="100%"></stop>
        </linearGradient>
        <path d="M35.6988304,25.1374967 C36.0948598,22.7867761 33.4132558,19.9865333 27.6540184,16.7367685 L22.5256164,17.2529687 C20.8037181,13.7373391 18.6790963,12.5908862 16.1517511,13.81361 C13.2711745,15.9105875 15.5636747,26.6892949 18.5043359,28.1264564 C18.7982275,28.3760384 19.220731,28.3183285 19.5249159,28.3132917 C23.7184594,28.243853 27.8237557,27.6526752 35.6988304,25.1374967 Z" id="path-22"></path>
        <filter x="-4.8%" y="-6.7%" width="109.6%" height="113.3%" filterUnits="objectBoundingBox" id="filter-23">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="-1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.564705882   0 0 0 0 0.0470588235  0 0 0 0.621666541 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.511184372 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="33.6737594%" y1="71.5801066%" x2="50%" y2="89.5663025%" id="linearGradient-24">
            <stop stop-color="#FFCF4E" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FEA226" offset="100%"></stop>
        </linearGradient>
        <filter x="-14.2%" y="-12.3%" width="128.4%" height="124.7%" filterUnits="objectBoundingBox" id="filter-25">
            <feGaussianBlur stdDeviation="0.255847518" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="20.2335603%" y1="24.4159076%" x2="62.6551265%" y2="72.9832028%" id="linearGradient-26">
            <stop stop-color="#FDE771" offset="0%"></stop>
            <stop stop-color="#FF920F" offset="100%"></stop>
        </linearGradient>
        <path d="M22.1598857,31.7834571 C22.556493,29.43468 19.8729127,26.6370803 14.1091448,23.390658 L8.97635583,23.9069557 C7.25336619,20.3944733 5.12706769,19.2492093 2.59746032,20.4711636 C-0.285767291,22.5666702 2.00755517,33.3360388 4.95055266,34.7716812 C5.24466676,35.021022 5.66753327,34.9633168 5.97197566,34.9582526 C10.16907,34.8884365 14.2778965,34.297331 22.1598857,31.7834571 Z" id="path-27"></path>
        <filter x="-4.8%" y="-6.7%" width="109.6%" height="113.4%" filterUnits="objectBoundingBox" id="filter-28">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="-1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.564705882   0 0 0 0 0.0470588235  0 0 0 0.621666541 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.511184372 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="56.7260831%" y1="11.1913899%" x2="50%" y2="94.2633876%" id="linearGradient-29">
            <stop stop-color="#F9E669" offset="0%"></stop>
            <stop stop-color="#FFD73B" offset="100%"></stop>
        </linearGradient>
        <path d="M2.9738879,0 C-1.44084229,12.1451246 -0.940329376,20.7310207 4.47542664,25.7576881 C10.7423101,25.5100122 14.988862,23.5167157 18.9911866,21.9083029 C24.519754,19.6865394 26.8254009,15.5287521 26.7081588,12.8860682 C26.6391435,11.3304352 25.5591825,10.9490115 24.2041773,9.44569198 C23.5072552,8.71492477 22.8305485,7.17790799 22.1740572,4.83464164 L2.9738879,0 Z" id="path-30"></path>
        <filter x="-13.1%" y="-13.6%" width="126.2%" height="127.2%" filterUnits="objectBoundingBox" id="filter-32">
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="2" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.560784314   0 0 0 0 0.0431372549  0 0 0 0.398797735 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <filter x="-39.7%" y="-45.0%" width="179.5%" height="189.9%" filterUnits="objectBoundingBox" id="filter-33">
            <feGaussianBlur stdDeviation="2.05946599" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-5.6%" y="-11.8%" width="111.1%" height="123.5%" filterUnits="objectBoundingBox" id="filter-34">
            <feGaussianBlur stdDeviation="0.457659109" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="41.3403538%" y1="20.8855699%" x2="67.471718%" y2="84.9887878%" id="linearGradient-35">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#CFCAC1" offset="100%"></stop>
        </linearGradient>
        <path d="M15.1162147,20.6406467 C14.6516089,20.3218256 14.6106891,20.4539117 14.9934554,21.0369049 C15.5621084,22.0915901 15.8464349,22.8826934 15.8464349,23.4102147 C15.5105651,24.5802235 13.740022,25.4980447 10.5348056,26.1636782 C7.87026608,26.642946 5.5200927,26.6710137 3.48428543,26.2478815 C2.9475909,26.0271425 2.1618471,25.7362524 1.71789493,25.0799436 C1.42192681,24.6424043 1.4858275,24.1214201 1.909597,23.516991 C2.09607084,20.660471 2.15038119,19.0184924 2.07252803,18.5910552 C-0.124458453,16.1567486 -0.574755795,11.4698448 0.721636006,4.53034394 C0.858815942,3.79602867 1.50050641,3.26420423 2.24752375,3.26572944 L2.24752375,3.26572944 L3.46954694,3.26819043 C4.11538219,3.26949105 4.71935384,2.94871788 5.07992618,2.4129078 L5.07992618,2.4129078 L6.70368776,-3.26605328e-13 C8.07145486,0.331348192 9.26054979,0.55418077 10.2709726,0.668497736 C10.2710447,0.674194682 10.2711191,0.679889965 10.2711957,0.685583591 C11.4472366,0.845957176 14.3353365,1.45617518 18.9337081,2.51619661 L19.4458033,3.68400719 C19.7042337,4.2733409 20.2385517,4.69578951 20.8716078,4.81129059 C21.6413473,4.95172955 22.7715506,6.47956361 24.2622177,9.39479275 C24.3754848,9.80537859 24.4988003,10.1960588 24.632164,10.5668335 C24.834902,10.8923201 24.788108,11.273833 24.491782,11.7113723 C23.926632,12.1807725 29.2824373,15.1709326 28.678565,15.8091062 C27.4947828,18.5341369 26.1841979,21.2260848 23.3513271,24.332682 C22.9228864,25.064117 22.489447,25.2683134 22.0510086,24.9452713 L15.2082934,20.7057097 Z" id="path-36"></path>
        <filter x="-8.7%" y="-9.4%" width="117.4%" height="118.8%" filterUnits="objectBoundingBox" id="filter-38">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.815312925   0 0 0 0 0.815312925   0 0 0 0 0.815312925  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="-1" dy="-0" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.836038783   0 0 0 0 0.822877591   0 0 0 0 0.752079453  0 0 0 0.775747446 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-65.2%" y="-64.9%" width="230.3%" height="229.8%" filterUnits="objectBoundingBox" id="filter-39">
            <feGaussianBlur stdDeviation="1.93636392" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-99.9%" y="-99.5%" width="299.8%" height="299.0%" filterUnits="objectBoundingBox" id="filter-40">
            <feGaussianBlur stdDeviation="1.93636392" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-99.9%" y="-99.5%" width="299.8%" height="299.0%" filterUnits="objectBoundingBox" id="filter-41">
            <feGaussianBlur stdDeviation="1.93636392" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-11.6%" y="-44.7%" width="123.2%" height="189.5%" filterUnits="objectBoundingBox" id="filter-42">
            <feGaussianBlur stdDeviation="0.193636392" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-6.0%" y="-44.7%" width="112.0%" height="189.5%" filterUnits="objectBoundingBox" id="filter-43">
            <feGaussianBlur stdDeviation="0.193636392" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-35.7%" y="-35.5%" width="171.4%" height="171.1%" filterUnits="objectBoundingBox" id="filter-44">
            <feGaussianBlur stdDeviation="1.93636392" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="10.9198326%" x2="50%" y2="100%" id="linearGradient-45">
            <stop stop-color="#EFEEEA" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#D0CEC6" offset="100%"></stop>
        </linearGradient>
        <path d="M6.79503536,17.2074278 C8.46583321,20.06401 11.2566741,21.6014021 15.1675579,21.8196042 C18.902974,21.8009859 21.8129216,20.2619836 23.8974005,17.2025971 L6.79503536,17.2074278 Z" id="path-46"></path>
        <filter x="-3.4%" y="-12.6%" width="106.8%" height="125.2%" filterUnits="objectBoundingBox" id="filter-47">
            <feGaussianBlur stdDeviation="0.193636392" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-9.2%" y="-34.2%" width="118.5%" height="168.5%" filterUnits="objectBoundingBox" id="filter-48">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.00533694821 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="31.0600788%" y1="47.6107768%" x2="78.5215085%" y2="62.6914048%" id="linearGradient-49">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#E4E3E1" offset="100%"></stop>
        </linearGradient>
        <path d="M0.854680643,0.588340387 C3.80740942,0.196113462 6.27369791,1.00997706e-13 8.25354611,1.00997706e-13 C10.2318471,1.00997706e-13 12.6412881,0.195807065 15.4818691,0.587421195 L15.4818662,0.587442328 C15.9305825,0.649304191 16.2764295,1.01401518 16.314396,1.4653817 C16.6563035,5.53017352 14.1392307,7.78666766 8.76317749,8.23486412 C2.67380992,8.06601064 -0.242017219,5.8178909 0.0156960671,1.49050491 L0.0157260529,1.49050669 C0.0432882419,1.02769691 0.395087946,0.649390569 0.854680643,0.588340387 Z" id="path-50"></path>
        <filter x="-9.2%" y="-18.2%" width="118.4%" height="136.4%" filterUnits="objectBoundingBox" id="filter-51">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.858823529   0 0 0 0 0.854901961   0 0 0 0 0.819607843  0 0 0 0.805743451 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-9.2%" y="-18.2%" width="118.4%" height="136.4%" filterUnits="objectBoundingBox" id="filter-52">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="-0" dy="-0" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="-0" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 0.862467006   0 0 0 0 0.862467006   0 0 0 0 0.862467006  0 0 0 0.436213567 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M1.62916908,1.13472978 C4.24261622,0.808249062 6.42866706,0.645008704 8.18732158,0.645008704 C9.94461589,0.645008704 12.0807247,0.807996646 14.5956479,1.13397253 L14.595648,1.13397156 C15.0527503,1.19321962 15.4047095,1.56650115 15.4370006,2.02629471 C15.6739184,5.39977818 13.4099154,7.27468964 8.64499134,7.65102909 C3.24631698,7.50920701 0.625294821,5.64328338 0.781924858,2.05325822 L0.781910024,2.05325757 C0.802496193,1.58141407 1.1605194,1.19327509 1.62916908,1.13472978 Z" id="path-53"></path>
        <filter x="-10.2%" y="-21.4%" width="118.5%" height="142.8%" filterUnits="objectBoundingBox" id="filter-54">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowInner" result="shadowInner"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.845665973   0 0 0 0 0.845665973   0 0 0 0 0.845665973  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M1.62916908,1.13472978 C4.24261622,0.808249062 6.42866706,0.645008704 8.18732158,0.645008704 C9.94461589,0.645008704 12.0807247,0.807996646 14.5956479,1.13397253 L14.595648,1.13397156 C15.0527503,1.19321962 15.4047095,1.56650115 15.4370006,2.02629471 C15.6739184,5.39977818 13.4099154,7.27468964 8.64499134,7.65102909 C3.24631698,7.50920701 0.625294821,5.64328338 0.781924858,2.05325822 L0.781910024,2.05325757 C0.802496193,1.58141407 1.1605194,1.19327509 1.62916908,1.13472978 Z" id="path-55"></path>
        <mask id="mask-56" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14.6785327" height="7.00602038" fill="white">
            <use xlink:href="#path-55"></use>
        </mask>
        <filter x="-81.4%" y="-22.0%" width="261.2%" height="151.2%" filterUnits="objectBoundingBox" id="filter-57">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.241200694 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="45.923547%" y1="75.2708219%" x2="55.7548771%" y2="24.8341641%" id="linearGradient-58">
            <stop stop-color="#3AA1E9" offset="0%"></stop>
            <stop stop-color="#3693F7" offset="100%"></stop>
        </linearGradient>
        <path d="M2.81831231,-7.13183676e-13 C3.63730055,-7.13334122e-13 4.3012217,0.66392115 4.3012217,1.48290939 C4.3012217,1.49445385 4.30108689,1.50599791 4.3008173,1.51753921 L4.11671244,9.39908114 C4.0909219,10.5031761 3.18848097,11.3847831 2.0840848,11.3847831 L2.03516422,11.3847831 C1.03224713,11.3847831 0.219222178,10.5717582 0.219222178,9.56884108 C0.219222178,9.49998246 0.223138746,9.43117957 0.230952906,9.36276577 L1.12809997,1.50814741 C1.22624445,0.64888206 1.95346014,-7.13024805e-13 2.81831231,-7.13183676e-13 Z" id="path-59"></path>
        <filter x="-6.5%" y="-4.4%" width="117.2%" height="108.8%" filterUnits="objectBoundingBox" id="filter-60">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-0" dy="-0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.323654175   0 0 0 0 0.4315389  0 0 0 0.686191372 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.578362312 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="45.7565831%" y1="80.2058908%" x2="55.7206556%" y2="41.1574774%" id="linearGradient-61">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#EBEBEB" offset="100%"></stop>
        </linearGradient>
        <path d="M2.65519536,0.583835032 C3.31585792,0.583835032 3.85143074,1.11940786 3.85143074,1.78007042 C3.85143074,1.78834103 3.85134497,1.79661142 3.85117344,1.80488026 L3.69649733,9.26116287 C3.67762862,10.1707441 2.93487055,10.8982539 2.0250936,10.8982539 C1.20784892,10.8982539 0.545341215,10.2357462 0.545341215,9.41850155 C0.545341215,9.36862887 0.547862532,9.31878807 0.552895513,9.26916999 L1.3106372,1.79890805 C1.38063079,1.10887017 1.96161668,0.583835032 2.65519536,0.583835032 Z" id="path-62"></path>
        <mask id="mask-63" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="3.48885296" height="10.3144189" fill="white">
            <use xlink:href="#path-62"></use>
        </mask>
        <filter x="-107.5%" y="-107.1%" width="315.0%" height="314.1%" filterUnits="objectBoundingBox" id="filter-64">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.062745098   0 0 0 0 0   0 0 0 0 0  0 0 0 0.377910054 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="27.7271658%" y1="50%" x2="78.2305803%" y2="100%" id="linearGradient-65">
            <stop stop-color="#FFE364" offset="0%"></stop>
            <stop stop-color="#FEBD27" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-66" cx="1.16295099" cy="1.16767006" rx="1.16295099" ry="1.16767006"></ellipse>
        <filter x="-43.0%" y="-42.8%" width="186.0%" height="185.6%" filterUnits="objectBoundingBox" id="filter-68">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-0" dy="-1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.588235294   0 0 0 0 0.0588235294  0 0 0 0.912775213 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <filter x="-171.3%" y="-170.6%" width="442.5%" height="441.1%" filterUnits="objectBoundingBox" id="filter-69">
            <feGaussianBlur stdDeviation="0.774545567" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="62.1220381%" y1="6.66844653%" x2="57.3498362%" y2="50%" id="linearGradient-70">
            <stop stop-color="#FFCF38" offset="0%"></stop>
            <stop stop-color="#FFB703" offset="100%"></stop>
        </linearGradient>
        <path d="M0,8.41578759 C8.5414122,0.85492861 13.9183209,-1.61134583 16.1307261,1.01696428 C18.3431313,3.64527439 17.4332958,11.865411 13.4012196,25.6773742 L0,8.41578759 Z" id="path-71"></path>
        <filter x="-8.7%" y="-5.8%" width="117.3%" height="111.7%" filterUnits="objectBoundingBox" id="filter-72">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.996078431   0 0 0 0 0.945098039   0 0 0 0 0.701960784  0 0 0 0.897131433 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.952590957   0 0 0 0 0.683939715  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <radialGradient cx="77.0152612%" cy="17.0478592%" fx="77.0152612%" fy="17.0478592%" r="79.1230415%" gradientTransform="translate(0.770153,0.170479),scale(1.000000,0.673998),rotate(84.454009),scale(1.000000,0.595230),translate(-0.770153,-0.170479)" id="radialGradient-73">
            <stop stop-color="#FFC102" offset="0%"></stop>
            <stop stop-color="#FF8D01" offset="100%"></stop>
        </radialGradient>
        <path d="M1.12379877,11.5255029 C7.79209427,5.62272701 11.9898563,3.69730223 13.7170849,5.74922854 C15.4443136,7.80115486 14.7340034,14.21863 11.5861544,25.0016538 L1.12379877,11.5255029 Z" id="path-74"></path>
        <filter x="-22.2%" y="-15.0%" width="144.4%" height="129.9%" filterUnits="objectBoundingBox" id="filter-75">
            <feOffset dx="0" dy="-0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.90463876   0 0 0 0 0.347982774   0 0 0 0 0  0 0 0 0.719445715 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-22.2%" y="-15.0%" width="144.4%" height="129.9%" filterUnits="objectBoundingBox" id="filter-76">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.529411765   0 0 0 0 0.00392156863  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M0,8.41578759 C8.5414122,0.85492861 13.9183209,-1.61134583 16.1307261,1.01696428 C18.3431313,3.64527439 17.4332958,11.865411 13.4012196,25.6773742 L0,8.41578759 Z" id="path-77"></path>
        <filter x="-8.7%" y="-5.8%" width="117.3%" height="111.7%" filterUnits="objectBoundingBox" id="filter-78">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.996078431   0 0 0 0 0.945098039   0 0 0 0 0.701960784  0 0 0 0.897131433 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.952590957   0 0 0 0 0.683939715  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M1.12379877,11.5255029 C7.79209427,5.62272701 11.9898563,3.69730223 13.7170849,5.74922854 C15.4443136,7.80115486 14.7340034,14.21863 11.5861544,25.0016538 L1.12379877,11.5255029 Z" id="path-79"></path>
        <filter x="-22.2%" y="-15.0%" width="144.4%" height="129.9%" filterUnits="objectBoundingBox" id="filter-80">
            <feOffset dx="0" dy="-0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.90463876   0 0 0 0 0.347982774   0 0 0 0 0  0 0 0 0.719445715 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-22.2%" y="-15.0%" width="144.4%" height="129.9%" filterUnits="objectBoundingBox" id="filter-81">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="-0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.529411765   0 0 0 0 0.00392156863  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="58.8288283%" y1="16.0482323%" x2="50%" y2="88.1073194%" id="linearGradient-82">
            <stop stop-color="#F9E669" offset="0%"></stop>
            <stop stop-color="#FFD73B" offset="100%"></stop>
        </linearGradient>
        <path d="M4.16253091,27.8137932 C8.12790325,10.0630619 17.9368284,2.21442011 33.5893064,4.26786764 C52.8970519,6.87630552 60.5855178,18.57091 56.6547042,39.3516809 C59.5140806,49.8128669 49.989534,54.0037051 28.0810644,51.9241955 C8.87183079,49.1352755 0.898986283,41.0984747 4.16253091,27.8137932 Z" id="path-83"></path>
        <filter x="-6.5%" y="-7.2%" width="112.9%" height="114.4%" filterUnits="objectBoundingBox" id="filter-85">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.980598267   0 0 0 0 0.782269436  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="-3" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.62745098   0 0 0 0 0.0392156863  0 0 0 0.641780827 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-22.5%" y="-22.5%" width="145.1%" height="145.0%" filterUnits="objectBoundingBox" id="filter-86">
            <feGaussianBlur stdDeviation="2.02651471" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="52.64642%" y1="41.9751666%" x2="50%" y2="57.050769%" id="linearGradient-87">
            <stop stop-color="#AF8800" offset="0%"></stop>
            <stop stop-color="#604000" offset="100%"></stop>
        </linearGradient>
        <path d="M19.622999,35.1492121 C27.2952429,31.5760554 37.8205316,31.7007587 51.1988652,35.523322 C67.344809,39.1925051 58.510638,18.5438342 35.2906925,18.391569 C19.6180906,18.7165475 15.3108537,26.7892017 13.3017988,30.2015236 C11.4304624,34.8054696 13.5375292,36.4546991 19.622999,35.1492121 Z" id="path-88"></path>
        <filter x="-3.2%" y="-8.5%" width="106.4%" height="117.1%" filterUnits="objectBoundingBox" id="filter-89">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.568627451   0 0 0 0 0.0509803922  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.961983943   0 0 0 0 0.524493908   0 0 0 0 0.00423548809  0 0 0 1 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-6.4%" y="-17.1%" width="112.8%" height="134.2%" filterUnits="objectBoundingBox" id="filter-90">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.771442614   0 0 0 0 0.0546035394  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="-2" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 0.384313725   0 0 0 0 0.250980392   0 0 0 0 0  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="59.2584112%" y1="36.7963956%" x2="50%" y2="100%" id="linearGradient-91">
            <stop stop-color="#FFD048" offset="0%"></stop>
            <stop stop-color="#FF8C03" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-92" cx="55.3478788" cy="37.4738043" rx="4.08482564" ry="4.09355597"></ellipse>
        <filter x="-42.8%" y="-30.5%" width="185.7%" height="185.5%" filterUnits="objectBoundingBox" id="filter-93">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.576470588   0 0 0 0 0.0470588235  0 0 0 0.656997787 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.560784314   0 0 0 0 0.0431372549  0 0 0 0.451621743 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-49.0%" y="-36.6%" width="197.9%" height="197.7%" filterUnits="objectBoundingBox" id="filter-94">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="2" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.935122287   0 0 0 0 0.669713462  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="-0" dy="-1" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.545098039   0 0 0 0 0.0392156863  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.950004162   0 0 0 0 0.723552425  0 0 0 0.500596693 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-28.5%" y="-28.4%" width="156.9%" height="156.8%" filterUnits="objectBoundingBox" id="filter-95">
            <feGaussianBlur stdDeviation="2.02651471" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="59.2584112%" y1="36.7963956%" x2="50%" y2="100%" id="linearGradient-96">
            <stop stop-color="#FFD048" offset="0%"></stop>
            <stop stop-color="#FF8C03" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-97" cx="20.1405299" cy="33.2423332" rx="5.61899387" ry="5.63100312"></ellipse>
        <filter x="-26.7%" y="-26.6%" width="153.4%" height="153.3%" filterUnits="objectBoundingBox" id="filter-98">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.576470588   0 0 0 0 0.0470588235  0 0 0 0.656997787 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-31.1%" y="-31.1%" width="162.3%" height="162.2%" filterUnits="objectBoundingBox" id="filter-99">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.935122287   0 0 0 0 0.669713462  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="-1" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.545098039   0 0 0 0 0.0392156863  0 0 0 1 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner3"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner3" result="shadowOffsetInner3"></feOffset>
            <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.950004162   0 0 0 0 0.723552425  0 0 0 0.500596693 0" type="matrix" in="shadowInnerInner3" result="shadowMatrixInner3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
                <feMergeNode in="shadowMatrixInner3"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="44.223551%" y1="22.9284078%" x2="50%" y2="88.7251156%" id="linearGradient-100">
            <stop stop-color="#FFD435" offset="0%"></stop>
            <stop stop-color="#FFA718" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-101" cx="37.849745" cy="39.3243867" rx="11.6286522" ry="10.2338899"></ellipse>
        <filter x="-21.5%" y="-19.5%" width="143.0%" height="148.9%" filterUnits="objectBoundingBox" id="filter-102">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.576131687   0 0 0 0 0  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.584313725   0 0 0 0 0.0549019608  0 0 0 0.825472745 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-19.3%" y="-17.1%" width="138.7%" height="144.0%" filterUnits="objectBoundingBox" id="filter-103">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.943556502   0 0 0 0 0.725479349  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="-2" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.638853649   0 0 0 0 0.0197456174  0 0 0 0.110039984 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-34.9%" y="-34.9%" width="169.9%" height="169.7%" filterUnits="objectBoundingBox" id="filter-104">
            <feGaussianBlur stdDeviation="1.57617811" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-29.6%" y="-42.5%" width="159.3%" height="185.0%" filterUnits="objectBoundingBox" id="filter-105">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.769065968   0 0 0 0 0.621383531   0 0 0 0 0.00850141758  0 0 0 0.345284842 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="20.4743013%" x2="50%" y2="100%" id="linearGradient-106">
            <stop stop-color="#815E00" offset="0%"></stop>
            <stop stop-color="#4A3000" offset="100%"></stop>
        </linearGradient>
        <path d="M1.66950551,0.337456994 C2.31052679,0.112485665 3.12757556,-1.56092297e-13 4.12065181,-1.56092297e-13 C5.13477228,-1.56092297e-13 5.97668116,0.11730353 6.64637845,0.35191059 L6.64637233,0.351928038 C7.7026366,0.721956496 8.25894065,1.8781952 7.88891219,2.93445947 C7.83905586,3.0767768 7.77347008,3.2130846 7.69337296,3.34085129 C6.63068356,5.03599705 5.47188716,5.88356993 4.21698377,5.88356993 C2.73839208,5.91726468 1.51600568,5.02216031 0.54982455,3.19825681 L0.549825885,3.19825611 C0.0259123523,2.20924101 0.402951667,0.982770025 1.39196677,0.458856492 C1.48132136,0.411522448 1.57409341,0.370942603 1.66950551,0.337456994 Z" id="path-107"></path>
        <filter x="-8.1%" y="-17.0%" width="114.9%" height="134.0%" filterUnits="objectBoundingBox" id="filter-109">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.297765345 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <filter x="-58.7%" y="-96.0%" width="217.5%" height="292.0%" filterUnits="objectBoundingBox" id="filter-110">
            <feGaussianBlur stdDeviation="0.900673206" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="52.9166019%" x2="62.1372842%" y2="79.0543888%" id="linearGradient-111">
            <stop stop-color="#FEF5B5" offset="0%"></stop>
            <stop stop-color="#FBEB76" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="38.6634904%" y1="27.152206%" x2="50%" y2="72.847794%" id="linearGradient-112">
            <stop stop-color="#B18104" offset="0%"></stop>
            <stop stop-color="#5E3B00" offset="100%"></stop>
        </linearGradient>
        <path d="M51.3756544,19.1093361 C49.1278832,18.9470272 48.2518466,18.416752 48.7475446,17.5185104 C49.4910916,16.171148 51.4060969,18.4783963 51.5609093,18.7938662 C51.6261668,18.9543413 51.5644152,19.0594979 51.3756544,19.1093361 Z" id="path-113"></path>
        <filter x="-50.5%" y="-74.6%" width="200.9%" height="249.3%" filterUnits="objectBoundingBox" id="filter-114">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.850980392   0 0 0 0 0.411764706   0 0 0 0 0.0235294118  0 0 0 0.69552591 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-50.5%" y="-74.6%" width="200.9%" height="249.3%" filterUnits="objectBoundingBox" id="filter-115">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.262548487 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M27.992273,17.0015679 C25.7445018,16.839259 24.8684652,16.3089838 25.3641632,15.4107422 C26.1077102,14.0633798 28.0227155,16.3706281 28.1775279,16.686098 C28.2427854,16.8465731 28.1810338,16.9517298 27.992273,17.0015679 Z" id="path-116"></path>
        <filter x="-50.5%" y="-74.6%" width="200.9%" height="249.3%" filterUnits="objectBoundingBox" id="filter-117">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.850980392   0 0 0 0 0.411764706   0 0 0 0 0.0235294118  0 0 0 0.69552591 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-50.5%" y="-74.6%" width="200.9%" height="249.3%" filterUnits="objectBoundingBox" id="filter-118">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.262548487 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-119">
            <stop stop-color="#FFFFF9" offset="0%"></stop>
            <stop stop-color="#FFFCD3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="37.3805851%" y1="14.7496027%" x2="41.9368187%" y2="83.9980091%" id="linearGradient-120">
            <stop stop-color="#D27CFF" offset="0%"></stop>
            <stop stop-color="#9B37FF" offset="100%"></stop>
        </linearGradient>
        <path d="M0.401634344,6.19971359 L5.41299582,19.2173339 C5.50271442,19.4503889 5.7002052,19.6252261 5.94241828,19.686028 L9.72075776,20.6344908 L9.72075776,20.6344908 L14.6137951,22.817458 L10.5735101,9.04684409 C8.53786496,6.45322775 6.4135351,5.15641958 4.20052053,5.15641958 C3.1729911,5.15641958 2.14546168,5.15641958 1.11793226,5.15641958 C0.694030213,5.15641958 0.350389707,5.50006009 0.350389707,5.92396213 C0.350389707,6.01824653 0.367761224,6.11172404 0.401634344,6.19971359 Z" id="path-121"></path>
        <filter x="2.4%" y="0.0%" width="97.6%" height="100.0%" filterUnits="objectBoundingBox" id="filter-122">
            <feOffset dx="0" dy="-0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.875524009   0 0 0 0 0.751048018   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-1.0%" y="-2.8%" width="104.4%" height="105.7%" filterUnits="objectBoundingBox" id="filter-123">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.142615685 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="13.6421243%" x2="50%" y2="93.3741682%" id="linearGradient-124">
            <stop stop-color="#CB68FF" offset="0%"></stop>
            <stop stop-color="#9B37FF" offset="100%"></stop>
        </linearGradient>
        <path d="M13.9245769,7.7823815 C14.424273,6.14236481 16.1638521,4.83459162 19.1433141,3.85906195 C21.6601913,3.03499091 24.0322561,2.26116196 26.2595084,1.5375751 C26.930131,1.31970509 27.6505961,1.68558344 27.8703158,2.35560226 L32.0431913,15.0804785 C32.263299,15.7517988 31.8975566,16.4744631 31.2262477,16.6946057 C31.1390429,16.7232028 31.0490139,16.7423325 30.9577156,16.7516642 C28.1390772,17.0397617 25.9450722,17.5474887 24.3757006,18.2748452 C22.5227139,19.1336489 20.6553212,20.4891126 18.7735228,22.3412363 C18.4369782,21.8018124 18.0716131,20.9905227 17.6774273,19.9073672 C17.2832416,18.8242117 16.756338,17.1836981 16.0967164,14.9858262 L13.9245769,7.7823815 Z" id="path-125"></path>
        <filter x="0.0%" y="1.6%" width="97.6%" height="98.4%" filterUnits="objectBoundingBox" id="filter-126">
            <feOffset dx="-0" dy="-0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.877439158   0 0 0 0 0.757147961   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-5.4%" y="-3.2%" width="108.4%" height="107.9%" filterUnits="objectBoundingBox" id="filter-127">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.161430732 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="58.5150277%" y1="62.5973163%" x2="50%" y2="69.7404007%" id="linearGradient-128">
            <stop stop-color="#D79DFC" offset="0%"></stop>
            <stop stop-color="#A94DFF" offset="100%"></stop>
        </linearGradient>
        <path d="M14.6137951,22.817458 C16.4933931,23.2045728 17.879969,23.0458323 18.7735228,22.3412363 C18.3525086,21.6012989 16.7361934,16.7483473 13.9245769,7.7823815 C13.218136,8.47825118 12.5248922,8.88460761 11.8448455,9.00145081 C11.2877472,9.05215715 10.8639687,9.06728824 10.5735101,9.04684409 L14.6137951,22.817458 Z" id="path-129"></path>
        <filter x="0.0%" y="0.0%" width="100.0%" height="100.0%" filterUnits="objectBoundingBox" id="filter-130">
            <feOffset dx="-0" dy="-0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.878431373   0 0 0 0 0.756862745   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="33.2500071%" y1="34.6275749%" x2="50%" y2="76.6120926%" id="linearGradient-131">
            <stop stop-color="#FEEC86" offset="0%"></stop>
            <stop stop-color="#FCD44E" offset="100%"></stop>
        </linearGradient>
        <path d="M21.3315209,13.0851852 L20.0671116,13.7587142 C19.9424009,13.8251455 19.7874496,13.7779007 19.7210184,13.6531899 C19.6941327,13.6027177 19.6849316,13.5446931 19.6948839,13.4883794 L19.9443662,12.0767264 L19.9443662,12.0767264 L18.9138126,11.0811718 C18.8121871,10.9829976 18.8093893,10.8210281 18.9075634,10.7194027 C18.9472023,10.6783702 18.9994187,10.6517639 19.0559137,10.6438123 L20.4748737,10.4440953 L20.4748737,10.4440953 L21.102852,9.15538592 C21.164749,9.02836368 21.3178983,8.97556923 21.4449205,9.0374662 C21.4963255,9.06251547 21.537857,9.10406377 21.5628855,9.15547889 L22.1899002,10.4435325 L22.1899002,10.4435325 L23.6082349,10.642249 C23.7481689,10.6618545 23.8457143,10.7911868 23.8261088,10.9311207 C23.8181855,10.9876735 23.7915701,11.0399499 23.7505002,11.0796268 L22.7193372,12.0758158 L22.7193372,12.0758158 L22.967987,13.4878343 C22.9924923,13.6269939 22.8995467,13.7596704 22.7603872,13.7841758 C22.7041522,13.7940785 22.6462207,13.7848847 22.5958147,13.758058 L21.3315209,13.0851852 L21.3315209,13.0851852 Z" id="path-132"></path>
        <filter x="-21.5%" y="-22.0%" width="143.0%" height="146.1%" filterUnits="objectBoundingBox" id="filter-133">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.187979745 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-21.5%" y="-22.0%" width="143.0%" height="146.1%" filterUnits="objectBoundingBox" id="filter-134">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.6   0 0 0 0 0.0901960784  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <filter x="-39.0%" y="-35.5%" width="177.9%" height="171.0%" filterUnits="objectBoundingBox" id="filter-135">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.568627451   0 0 0 0 0.0509803922  0 0 0 0.551709155 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-136">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#DAD5C8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="92.7756841%" y1="25.3054057%" x2="28.7520971%" y2="83.26214%" id="linearGradient-137">
            <stop stop-color="#A37800" offset="0%"></stop>
            <stop stop-color="#6A4300" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-138" cx="3.19468468" cy="3.45393785" rx="3.19468468" ry="3.45393785"></ellipse>
        <filter x="-7.8%" y="-7.2%" width="115.7%" height="114.5%" filterUnits="objectBoundingBox" id="filter-140">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.232983569 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <filter x="-39.0%" y="-35.5%" width="177.9%" height="171.0%" filterUnits="objectBoundingBox" id="filter-141">
            <feOffset dx="-0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.568627451   0 0 0 0 0.0509803922  0 0 0 0.551709155 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <ellipse id="path-142" cx="3.19468468" cy="3.45393785" rx="3.19468468" ry="3.45393785"></ellipse>
        <filter x="-7.8%" y="-7.2%" width="115.7%" height="114.5%" filterUnits="objectBoundingBox" id="filter-144">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.232983569 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="28.2792052%" y1="19.2267367%" x2="94.8815184%" y2="100%" id="linearGradient-145">
            <stop stop-color="#FDE97C" offset="0%"></stop>
            <stop stop-color="#FBE167" offset="100%"></stop>
        </linearGradient>
        <path d="M52.0503601,70.0056087 C44.7491675,68.2224346 40.9783853,64.6482005 40.7380135,59.2829066 C40.9359321,56.1438251 43.8283577,55.0783099 45.9544404,56.146392 C48.7587429,57.15646 49.7711859,61.7720967 51.0161656,63.6806818 C52.1819811,65.588126 53.1247316,66.7916735 53.8444172,67.2913242 C54.5642545,68.214385 54.4282861,69.084695 53.4365121,69.9022542 C53.1305699,70.0653255 52.6685192,70.099777 52.0503601,70.0056087 Z" id="path-146"></path>
        <filter x="-22.1%" y="-21.0%" width="129.5%" height="127.9%" filterUnits="objectBoundingBox" id="filter-147">
            <feOffset dx="-1" dy="-1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.535798888   0 0 0 0 0.323550053   0 0 0 0 0  0 0 0 0.217434543 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-29.5%" y="-27.9%" width="144.2%" height="141.9%" filterUnits="objectBoundingBox" id="filter-148">
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="1" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.588235294   0 0 0 0 0.0784313725  0 0 0 1 0" type="matrix" in="shadowInnerInner1" result="shadowMatrixInner1"></feColorMatrix>
            <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner2"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner2" result="shadowOffsetInner2"></feOffset>
            <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.881320183   0 0 0 0 0.64223871  0 0 0 0.5 0" type="matrix" in="shadowInnerInner2" result="shadowMatrixInner2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixInner1"></feMergeNode>
                <feMergeNode in="shadowMatrixInner2"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="33.6944005%" y1="71.5801066%" x2="50%" y2="89.5663025%" id="linearGradient-149">
            <stop stop-color="#FFCF4E" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FEA226" offset="100%"></stop>
        </linearGradient>
        <filter x="-14.2%" y="-12.3%" width="128.4%" height="124.7%" filterUnits="objectBoundingBox" id="filter-150">
            <feGaussianBlur stdDeviation="0.255847518" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <path d="M54.9125205,47.3095416 C58.546924,48.7572637 61.1876681,48.14929 62.8347528,45.4856203" id="path-151"></path>
        <filter x="-26.4%" y="-82.3%" width="153.3%" height="257.9%" filterUnits="objectBoundingBox" id="filter-152">
            <feMorphology radius="0.457659109" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowInner" result="shadowInner"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.588235294   0 0 0 0 0.0666666667  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="36.194588%" x2="70.8950935%" y2="82.0517484%" id="linearGradient-153">
            <stop stop-color="#FDE773" offset="0%"></stop>
            <stop stop-color="#FF9210" offset="100%"></stop>
            <stop stop-color="#FF9210" offset="100%"></stop>
        </linearGradient>
        <path d="M71.7410492,64.8554616 C72.9616243,60.9720585 74.8129993,59.4964532 77.2951744,60.4286456 C79.1107747,61.393737 78.9727038,63.2543893 76.8809619,66.0106026 C75.6592249,67.743247 74.3375503,68.2599972 72.9159382,67.5608532 C71.8602063,66.8967056 71.4685766,65.9949084 71.7410492,64.8554616 Z" id="path-154"></path>
        <filter x="-21.7%" y="-19.5%" width="143.5%" height="138.9%" filterUnits="objectBoundingBox" id="filter-155">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.6345865   0 0 0 0 0.375422052   0 0 0 0 0.00827241786  0 0 0 0.422484498 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="约课-时间-无" transform="translate(-392.000000, -329.000000)">
            <g id="缺省图-没课" transform="translate(392.000000, 329.000000)">
                <g id="空页面" fill-rule="nonzero">
                    <path d="M127.716671,11.9746153 L123.2506,11.9746153 C123.2506,11.9746153 118.337311,12.2226175 118.644265,8.74956553 C118.929874,5.55207162 122.201672,6.26852254 122.201672,6.26852254 C122.201672,6.26852254 122.506593,-0.228523827 130.288168,0.00621080489 C137.858332,0.234821924 138.374664,6.95231472 138.374664,6.95231472 C139.704995,6.94589511 140.837085,7.92379572 141.029507,9.24557002 C141.213476,10.9805651 139.547593,11.9746153 138.004694,11.9746153 L127.716671,11.9746153 L127.716671,11.9746153 Z" id="路径_5901" fill="url(#linearGradient-1)" opacity="0.569411505"></path>
                    <path d="M41.88557,19.4993911 C41.3811252,20.0427068 40.9351673,20.63794 40.5550996,21.2752096 C39.3415558,23.4833413 38.7640332,25.987051 38.8871835,28.5060568 C37.1461331,27.8527119 35.1943778,28.1247874 33.6964175,29.2296518 C31.6148257,30.9044324 32.386275,35.0235149 37.0983157,35.0235149 L68.040648,35.0235149 C68.040648,35.0235149 71.1650684,35.5338076 71.2656922,32.7782271 C71.3917261,29.3082369 67.1706078,29.9491645 67.1706078,29.9491645 C67.874908,28.1769386 67.1750339,26.154345 65.5281018,25.202422 C63.834931,24.2187372 61.6861338,24.5677486 60.3881558,26.0372608 C60.3881558,26.0372608 59.1786372,14.4668847 47.8345722,16.4733555 C47.0548249,16.6098703 46.2890383,16.8172206 45.5466508,17.0928508 C44.1889072,17.65661 42.9479533,18.4700111 41.8876029,19.4912264 L41.88557,19.4993911 Z" id="路径_5902" fill="url(#linearGradient-2)" opacity="0.569411505"></path>
                    <g id="组_5827" opacity="0.736" transform="translate(0.000000, 55.294407)">
                        <g id="plant_11_" opacity="0.575561523" transform="translate(22.024614, 30.459260) rotate(-1.000000) translate(-22.024614, -30.459260) translate(0.439350, 5.058220)">
                            <path d="M6.54817934,13.0822331 C6.50937393,13.0841167 6.47187209,13.0679245 6.44653912,13.0383479 C5.460309,12.0192196 4.964627,10.6195626 5.08862577,9.20400876 C5.15477989,8.662324 5.36392247,8.14823865 5.69440148,7.71497473 C5.73848943,7.66573188 5.81229678,7.65739986 5.86617346,7.69558361 C5.8910246,7.71166646 5.90766991,7.7378176 5.91176911,7.7672181 C5.91586831,7.79661861 5.90701508,7.82635424 5.8875179,7.84867141 C5.58342874,8.25578589 5.39070725,8.73570225 5.32849669,9.24074983 C5.21922183,10.5969318 5.70128215,11.9340111 6.64981956,12.9056718 C6.6690338,12.9283949 6.67739394,12.9584173 6.67270372,12.9878518 C6.66801349,13.0172863 6.65074177,13.0431907 6.62542591,13.0587596 C6.60286954,13.0747393 6.57577476,13.0829728 6.54817934,13.0822331 Z" id="Path" fill="#D0E7FE"></path>
                            <path d="M26.2613001,50.4519864 C17.173648,39.6276582 7.94065036,41.5963673 7.94776107,38.1743446 C7.94776107,37.1262035 10.8739871,34.0491386 15.1296632,35.3616114 C19.3853392,36.6740841 25.1981434,40.9605426 26.2613001,50.4519864 Z" id="Path-2" fill="url(#linearGradient-3)"></path>
                            <path d="M1.26898627,5.48805749 C9.80676481,-9.06651025 43.0675106,5.11656442 26.3253335,50.5703743 C26.3253335,50.5703743 24.7387296,32.2569907 15.3400584,25.2853722 C5.94138722,18.3137536 -3.47557923,13.5741552 1.26898627,5.48805749 Z" id="Path-3" fill="url(#linearGradient-4)"></path>
                            <path d="M26.7420584,50.6183418 C34.7411438,45.9450815 42.8174757,44.9030639 43.1447572,38.065142 C43.3673493,33.4143345 42.1629127,30.065794 38.9510817,30.065794 C37.1571318,30.065794 29.7536581,35.3279321 26.7420584,50.6183418 Z" id="Path-4" fill="url(#linearGradient-5)"></path>
                            <path d="M25.1208969,32.2447575 C25.0711944,32.2455578 25.0281977,32.2101553 25.0192566,32.1610557 C25.0162074,32.1365617 24.4714159,29.7800301 22.9020908,29.7585978 C22.8736644,29.7586893 22.8464987,29.7468234 22.8271854,29.7258792 C22.8078722,29.704935 22.7981763,29.6768267 22.8004506,29.6483746 C22.7987677,29.6202625 22.8087286,29.5927017 22.8279753,29.5722162 C22.8472219,29.5517308 22.8740435,29.5401418 22.9020908,29.5401924 C24.6299746,29.563666 25.1961106,32.0059267 25.2194879,32.1100264 C25.2337714,32.167398 25.1999781,32.2257616 25.1432577,32.241682 C25.1359404,32.2435328 25.1284406,32.2445598 25.1208969,32.2447575 Z" id="Path-5" fill="#FFFFFF"></path>
                            <path d="M26.2795954,37.8824572 L26.2602837,37.8824572 C26.2307133,37.87701 26.2045311,37.8599372 26.1875477,37.8350278 C26.1705642,37.8101184 26.1641844,37.7794329 26.1698239,37.7497811 C26.1840536,37.6742578 26.4991383,35.8984393 27.6385251,35.4555052 C27.6658574,35.4450826 27.6962344,35.4462613 27.7226841,35.4587708 C27.7491338,35.4712803 27.7693847,35.4940462 27.7787886,35.5218433 C27.8003498,35.5798767 27.7719544,35.644595 27.7147553,35.667787 C26.6932711,36.0607124 26.3893668,37.772234 26.3863176,37.7895839 C26.3779304,37.8424604 26.3329126,37.8816364 26.2795954,37.8824572 L26.2795954,37.8824572 Z" id="Path-6" fill="#FFFFFF"></path>
                            <path d="M22.141822,21.3173363 C22.0949701,21.316729 22.053881,21.2857853 22.0401818,21.2407924 C21.2065128,18.4913527 18.4644068,16.795477 15.6439627,17.2850036 C15.6151006,17.2900182 15.5854396,17.2832424 15.5615788,17.2661836 C15.537718,17.2491248 15.5216366,17.223198 15.5169124,17.1941715 C15.5106233,17.1645116 15.5164394,17.133558 15.5330591,17.1082382 C15.5496788,17.0829183 15.5757145,17.0653461 15.6053394,17.0594542 C18.532663,16.5493547 21.3796286,18.3098235 22.2434622,21.1642485 C22.2637778,21.2225213 22.2352254,21.2865806 22.1784125,21.3101922 C22.1667585,21.3148182 22.1543531,21.3172403 22.141822,21.3173363 L22.141822,21.3173363 Z" id="Path-7" fill="#FFFFFF"></path>
                            <path d="M23.4489152,25.2027048 C23.4047734,25.1983069 23.3685704,25.1656726 23.3594718,25.1220785 C22.9359176,23.5751151 23.0727823,21.9274254 23.7457047,20.4722917 C24.0135488,19.9058083 24.3997237,19.403873 24.8779767,19.0006076 C24.8987039,18.9853999 24.9251457,18.9803642 24.9499791,18.9868949 C24.9748124,18.9934256 24.9954012,19.0108296 25.0060434,19.0342869 C25.035344,19.0852001 25.0237429,19.1499163 24.9786006,19.1873747 C24.9542069,19.2026835 22.6134326,21.0244283 23.5424242,25.0659463 C23.5573634,25.1208872 23.5289654,25.1783555 23.4763581,25.199643 C23.4674266,25.2021002 23.4581661,25.2031334 23.4489152,25.2027048 Z" id="Path-8" fill="#FFFFFF"></path>
                            <path d="M26.5001547,50.802079 C26.4580152,50.8031005 26.4196013,50.7779273 26.4035964,50.7387709 C22.0767723,41.240183 11.4970417,38.1988387 11.3913359,38.1692417 C11.3341059,38.1515252 11.300942,38.0916744 11.3161221,38.0335038 C11.3214372,38.0046394 11.3388722,37.9794705 11.3639739,37.9644257 C11.3890755,37.9493808 11.4194132,37.945917 11.447238,37.9549188 C11.5488782,37.9845157 21.9375252,40.9687073 26.4137605,50.2478694 C26.5943427,44.2672213 26.173796,38.2835318 25.1585037,32.3876257 C23.5779983,23.5646653 19.7654736,12.1320681 10.5731321,7.74150995 C10.5192696,7.71452174 10.4963559,7.64955782 10.5212956,7.59454566 C10.5314568,7.56734081 10.5526843,7.54577442 10.5796566,7.53525286 C10.6066288,7.5247313 10.6367887,7.52625211 10.6625755,7.53943405 C15.7832098,9.9888389 22.4914644,16.2746241 25.368899,32.3457817 C26.4161023,38.4019129 26.8310089,44.5512472 26.6068769,50.6938652 C26.6046778,50.7437069 26.5691175,50.7857147 26.5204827,50.7959237 L26.5001547,50.802079 Z" id="Path-9" fill="#FFFFFF"></path>
                            <path d="M26.1891356,50.4448423 C26.1560632,50.4450251 26.1249724,50.4290306 26.1057906,50.4019777 C26.0723611,50.3525481 26.0820655,50.2856663 26.1281515,50.2478694 C36.4974868,41.6524995 38.5160615,33.5735458 38.5353732,33.4929196 C38.5407172,33.464179 38.5575241,33.4388788 38.5818979,33.4228839 C38.6062716,33.406889 38.6360957,33.4015885 38.6644563,33.408211 C38.7228648,33.4246007 38.757294,33.4850997 38.7417028,33.5439489 C38.7234076,33.6276369 36.6916196,41.7770109 26.2602837,50.4213689 C26.2404076,50.4380011 26.2149626,50.446396 26.1891356,50.4448423 L26.1891356,50.4448423 Z" id="Path-10" fill="#FFFFFF"></path>
                            <path d="M16.9845972,40.6727376 C16.9676633,40.6725172 16.9512575,40.6667874 16.9378427,40.6564082 C16.1440696,40.0419011 15.8074383,39.0011013 16.0901633,38.035545 C16.0958054,38.0125059 16.1107038,37.9928448 16.131313,37.9812408 C16.1519221,37.9696367 16.1763989,37.9671273 16.1989183,37.9743099 C16.2468671,37.9992432 16.2690287,38.055966 16.2507548,38.106986 C15.9856918,38.9839165 16.2956879,39.9336429 17.0262697,40.4829087 C17.0688917,40.5169293 17.0809092,40.576833 17.054729,40.6247701 C17.0413208,40.6519796 17.0147228,40.6701717 16.9845972,40.6727376 Z" id="Path-11" fill="#FFFFFF"></path>
                            <path d="M21.0308944,43.8273669 C21.0026632,43.8271234 20.9756552,43.8157627 20.9556806,43.7957288 C19.860943,42.7775835 18.4336515,42.1958249 16.9419083,42.1597304 L16.9419083,42.1597304 C16.8811166,42.159733 16.8316782,42.1105465 16.8311205,42.0495072 C16.8287423,41.989925 16.8735739,41.9390568 16.9327607,41.9341811 C18.484701,41.956883 19.9717177,42.5632421 21.1000097,43.6334557 C21.1422157,43.6776707 21.1422157,43.7474314 21.1000097,43.7916464 C21.0825949,43.8122783 21.0577363,43.8251258 21.0308944,43.8273669 Z" id="Path-12" fill="#FFFFFF"></path>
                            <path d="M35.0714745,40.9319662 C35.0322311,40.9325768 34.9961444,40.9104453 34.9787661,40.8751095 C34.9613878,40.8397736 34.9658238,40.7975486 34.9901623,40.7666314 C36.0787005,39.2495049 36.0787005,37.203284 34.9901623,35.6861575 C34.9740958,35.6643778 34.9673171,35.6370753 34.9713213,35.6102713 C34.9753254,35.5834674 34.9897834,35.5593635 35.0115067,35.5432756 C35.0333909,35.5261563 35.0612928,35.5187627 35.0887399,35.5228099 C35.1161869,35.5268572 35.1407938,35.5419936 35.1568523,35.5647079 C35.1771803,35.5922637 37.1520498,38.3264118 35.1568523,40.8911428 C35.1368139,40.9180249 35.104889,40.9332898 35.0714745,40.9319662 Z" id="Path-13" fill="#FFFFFF"></path>
                            <path d="M31.1136043,46.2053356 C31.0835772,46.2056081 31.0546788,46.1938542 31.0333085,46.172672 C31.0140816,46.1527509 31.0037592,46.125829 31.0047179,46.0981045 C31.0056767,46.07038 31.0178338,46.0442418 31.0383905,46.0257077 C33.9513992,43.4589356 35.9333835,43.5007796 36.0146957,43.5007796 C36.0425564,43.5007379 36.0692101,43.5121948 36.0884244,43.5324528 C36.1076387,43.5527109 36.117729,43.5800006 36.1163359,43.607941 C36.1150342,43.6367486 36.1022247,43.6638137 36.0808021,43.6830197 C36.0593795,43.7022257 36.031152,43.7119517 36.0024989,43.7099996 C35.9892857,43.7048967 34.0296622,43.6763203 31.188818,46.1767544 C31.1679483,46.1949905 31.1412691,46.2051269 31.1136043,46.2053356 L31.1136043,46.2053356 Z" id="Path-14" fill="#FFFFFF"></path>
                        </g>
                        <path d="M15.2548158,57.0904985 L15.2548158,57.0904985 C15.2548158,54.8003049 17.4624414,52.9438602 20.1853829,52.9438602 C20.2280718,52.9438602 20.2666951,52.9530455 20.3083676,52.954066 C20.8745036,50.5832463 23.3626562,48.7972219 26.3488459,48.7972219 C27.4487482,48.7968573 28.5333159,49.0562671 29.5149388,49.5544962 C30.7285598,47.7313163 32.7899151,46.6620922 34.9730186,46.7233925 C37.9592083,46.7233925 40.4473609,48.5094168 41.013497,50.8802366 C41.0551694,50.8802366 41.0937927,50.8700308 41.1364816,50.8700308 C44.5404126,50.8700308 47.2999446,53.1908418 47.2999446,56.0535838 C47.1200268,57.9302058 45.4737811,59.3146904 43.6022734,59.1633073 L17.7206076,59.1633073 C16.4728621,59.2644409 15.3751051,58.3416376 15.2548158,57.0904985 Z" id="Path备份-3" fill="url(#linearGradient-6)" opacity="0.529212588"></path>
                        <g id="plant_25_" opacity="0.588702974" transform="translate(140.146115, 0.000000)">
                            <g id="Group" transform="translate(0.000000, 13.748748)">
                                <path d="M1.34178952,27.6859711 C0.27660001,25.0446962 -1.31305304,21.3032303 1.93333561,19.093663 C5.17972426,16.8840957 2.21589542,15.5757053 2.11120599,12.3731084 C2.00651657,9.17051159 5.32303697,8.34689921 5.22342955,6.8282682 C5.03299989,5.04923684 5.04732396,3.25409885 5.26611844,1.47835978 C5.77431955,-0.439320102 9.61123788,-1.30987941 10.0025527,4.08901711 C10.2485221,7.48144283 10.5788528,9.52975764 12.2864085,11.9475243 C13.9939642,14.365291 10.233276,16.9259397 10.3023914,18.8742371 C10.3715067,20.8225346 12.2254244,20.5337089 12.0221439,24.6619767 C11.8188635,28.7902444 9.25854631,31.9081327 6.82324662,31.7285097 C4.44220581,31.375431 2.38964714,29.8616824 1.34178952,27.6859711 Z" id="Path-15" fill="url(#linearGradient-7)"></path>
                                <path d="M9.55431935,13.7274252 C9.51847369,13.7079626 9.47617012,13.7043869 9.43758864,13.7175586 C9.39900716,13.7307303 9.36764128,13.7594567 9.35103891,13.796825 C9.32257965,13.8570395 8.64565578,15.2389121 7.54285939,14.7867928 C7.67813398,13.9456016 7.72074088,13.0920102 7.66990966,12.2414529 C7.67225185,12.2364798 7.67428846,12.2313673 7.67600808,12.2261441 C7.68219749,12.1913662 7.67684105,12.1555096 7.66076204,12.1240856 C7.64754881,11.9873272 7.63128638,11.8526099 7.60892553,11.7158515 C7.3808208,9.66322203 7.389006,7.59103746 7.63331918,5.54028949 C7.70852023,5.03029448 7.73167871,4.51392346 7.70243453,3.9992056 C7.69449046,3.95941896 7.67062418,3.9246533 7.63641974,3.90304283 C7.6022153,3.88143236 7.56067386,3.87487321 7.52151494,3.88490004 C7.4398726,3.9091224 7.39216222,3.99408907 7.41377631,4.07677009 C7.42326423,4.55634204 7.39744506,5.03596239 7.33652974,5.5117131 C7.08909577,7.5903248 7.08193336,9.69069868 7.31518529,11.7709631 C7.32636572,11.8362805 7.33348053,11.9046598 7.34262815,11.9750801 C6.41784258,11.7003392 5.67920822,10.9988578 5.35454544,10.0869972 C5.34400168,10.0478262 5.31790221,10.0147207 5.28235305,9.99542588 C5.2468039,9.97613103 5.20492391,9.97233964 5.16651103,9.9849387 C5.08654899,10.0146867 5.04433894,10.1026104 5.07096922,10.1839528 C5.43787529,11.2380629 6.30392876,12.0391677 7.38023503,12.320038 C7.37044201,14.320548 7.09702781,16.3109592 6.56711327,18.239433 C6.14789674,19.9605388 5.84667437,21.7084764 5.66556451,23.4709536 C5.61608613,24.3213701 5.63137665,25.1743393 5.71130261,26.022417 C4.89818084,25.5998946 2.68750604,24.2874219 2.62957112,22.5187474 C2.62494396,22.4359818 2.55857512,22.3702225 2.47609439,22.3666802 C2.39166571,22.3721445 2.32756386,22.4451835 2.33278167,22.5299739 C2.40189702,24.6374826 5.06995282,26.0622198 5.74281108,26.3888071 C5.96235396,28.7300299 6.47665347,31.3896753 6.86085351,33.3563433 C7.08365645,34.2917046 7.23654241,35.2424753 7.3182345,36.2007147 C7.30027778,36.2838526 7.3510181,36.3663632 7.43308795,36.3874818 C7.44321683,36.3883819 7.45345113,36.3883819 7.46358001,36.3874818 C7.53559622,36.3856748 7.5963775,36.3331792 7.60892553,36.2619498 C7.55351028,35.2578685 7.39962786,34.2617205 7.14951173,33.2879641 C6.77445932,31.3488519 6.26829102,28.7279887 6.04671534,26.4224864 C6.06444515,26.408209 6.07869756,26.3900584 6.08838783,26.369416 C6.11616792,26.2977249 6.08928878,26.2163272 6.02435449,26.1755048 C5.94072255,25.3308273 5.91830272,24.4811658 5.95727194,23.6332266 C7.16623852,23.6213206 8.23575439,22.8437126 8.62431134,21.6941145 C8.64842242,21.6119797 8.60351713,21.5254067 8.52267111,21.4981621 C8.48465918,21.4868242 8.44370927,21.491234 8.40895614,21.5104077 C8.37420302,21.5295814 8.34853789,21.5619239 8.33768591,21.6002206 C7.99826939,22.6192661 7.04974775,23.3077219 5.97963279,23.3117423 C6.16633013,21.6247262 6.45964547,19.9513338 6.8578043,18.3016888 C7.09970802,17.1943536 7.33551334,16.1145743 7.49610489,15.0990919 C7.64922106,15.1569113 7.81125227,15.1873093 7.97483032,15.1889034 C8.70737386,15.0970554 9.33466029,14.617935 9.61835269,13.9335834 C9.65176829,13.8584784 9.62433033,13.7701407 9.55431935,13.7274252 Z" id="Path-16" fill="#FFFFFF"></path>
                            </g>
                            <g id="Group-2" transform="translate(13.781694, 0.000000)">
                                <path d="M16.0528983,38.7107768 C17.5541243,35.018299 19.7942748,29.7857579 15.2163993,26.6964461 C10.6385237,23.6071342 14.8179696,21.7772246 14.9663643,17.2999167 C15.1147591,12.8226088 10.4413417,11.6713885 10.5816052,9.54346801 C10.7218687,7.41554754 10.9536084,3.66591695 10.5216375,2.06359795 C9.79999191,-0.613397436 4.39679778,-1.83095576 3.84590778,5.7183141 C3.49931463,10.4619948 3.03278602,13.3257573 0.626961994,16.7089978 C-1.77886203,20.0922382 3.52167548,23.6693899 3.42308447,26.3933322 C3.32449345,29.1172745 0.713356182,28.7141433 0.998965202,34.4865741 C1.28457422,40.2590048 4.89483486,44.6240484 8.32722511,44.3668609 C10.6578354,44.1923408 14.6817717,42.0817703 16.0528983,38.7107768 Z" id="Path-17" fill="url(#linearGradient-8)"></path>
                                <path d="M14.5913119,31.4921766 C14.5959475,31.4072102 14.5315525,31.3343539 14.4469828,31.328883 C14.364866,31.3327847 14.2985634,31.3976082 14.2924897,31.4799296 C14.2060955,34.0671135 10.8722962,35.951114 9.81422154,36.4807978 C9.93809166,35.2622139 9.9642635,34.0356069 9.89248451,32.8128141 C9.6373397,30.3512524 9.21316809,27.9103266 8.62299816,25.5074641 C7.94200868,22.4232552 7.30167529,19.4951958 7.48259488,17.1703024 C8.98982461,16.7952762 10.2104746,15.6883396 10.7350819,14.2208107 C10.761074,14.1397784 10.718983,14.0525522 10.6395401,14.0228171 C10.6011254,14.0105235 10.5593826,14.0144544 10.5239141,14.0337054 C10.4884455,14.0529565 10.462305,14.0858704 10.4515057,14.1248757 C9.96632293,15.4499898 8.87454341,16.4588649 7.51918536,16.8345298 C7.53443139,16.705936 7.55069383,16.5783629 7.57102187,16.4538514 C7.89883109,13.5511595 7.88859803,10.6199949 7.54052981,7.71968198 C7.45184493,7.04758937 7.41615991,6.3695136 7.43380757,5.69177888 C7.45536158,5.60935401 7.40757093,5.52469711 7.32606894,5.50092942 C7.28692967,5.49086855 7.24539708,5.49726656 7.21106152,5.51864608 C7.17672595,5.54002559 7.15255606,5.57453815 7.14413295,5.61421439 C7.1083009,6.3271881 7.14234462,7.04197208 7.24577317,7.74825837 C7.58891207,10.6222967 7.59812151,13.5266481 7.27321603,16.4028222 C7.24272396,16.5865275 7.21934671,16.7753358 7.20105147,16.9672059 C7.17944829,17.0022246 7.17182412,17.0441479 7.17970702,17.0845732 C7.18226797,17.0937157 7.18567065,17.1025991 7.18987104,17.1111084 C7.11681209,18.3175749 7.17920526,19.5284651 7.37587265,20.7209188 L7.37587265,20.7209188 C5.74962912,21.4230815 4.75152215,19.4064049 4.70984966,19.3206757 C4.6932473,19.2833074 4.66188141,19.254581 4.62329993,19.2414093 C4.58471845,19.2282376 4.54241488,19.2318133 4.50656922,19.2512759 C4.43226475,19.2929839 4.40328137,19.3856647 4.44050307,19.4625371 C4.4506671,19.4829488 5.28716611,21.1842646 6.69894878,21.1842646 C6.94842653,21.1814771 7.19508197,21.1309039 7.42567636,21.0352591 C7.65131765,22.475305 7.98774678,24.0092448 8.34043834,25.584008 C8.90893475,27.9145144 9.32519091,30.2798253 9.58654745,32.6648292 C8.9655257,32.6240058 6.72944084,32.3443654 6.13891116,30.2276714 C6.12783313,30.1892987 6.10196982,30.1569479 6.06705414,30.1377898 C6.03213845,30.1186316 5.9910547,30.1142488 5.95290956,30.1256128 C5.87220345,30.152415 5.82722658,30.2386744 5.85126934,30.3205446 C6.50989797,32.6791174 8.99398496,32.955696 9.6119575,32.9873342 C9.67252374,34.2060361 9.63854106,35.4276229 9.51031728,36.6410297 L9.50421887,36.6410297 C9.42796719,36.6770973 9.39280096,36.7664921 9.4239231,36.8451468 C9.43561981,36.8748516 9.45622,36.9001729 9.48287442,36.9176084 C9.16778974,40.0457025 8.45630819,43.5258985 7.92777905,46.1110412 C7.57897678,47.3950549 7.36303393,48.7117767 7.28338005,50.0402949 C7.29643354,50.1114887 7.35765881,50.1635736 7.42974197,50.1648063 C7.44020777,50.1657454 7.45078463,50.1657454 7.46125043,50.1648063 C7.54358885,50.144605 7.59495355,50.0622642 7.57712029,49.9790597 C7.68613267,48.6963849 7.89995822,47.4248773 8.21643727,46.1773793 C8.75208124,43.5585573 9.47474321,40.0232496 9.78677868,36.8512703 C10.661901,36.4328303 14.4967865,34.4426889 14.5913119,31.4921766 Z" id="Path-18" fill="#FFFFFF"></path>
                            </g>
                        </g>
                        <path d="M181.700841,54.8605195 C181.962695,54.274393 182.055317,53.626196 181.968155,52.9897865 C181.660048,51.2925109 180.308015,49.9817274 178.607929,49.7320781 C177.88225,49.5990412 177.135519,49.6403084 176.428763,49.8525072 C175.42435,47.7366164 173.273761,46.4125594 170.940191,46.473349 C170.783665,46.473349 170.628156,46.4804931 170.474679,46.4917196 C169.059214,45.2308323 167.22785,44.5426992 165.335749,44.5607721 C162.64693,44.5002452 160.141715,45.9255398 158.811464,48.272641 C156.932264,48.2723438 155.219145,49.3534005 154.404344,51.0537361 C154.342343,51.0537361 154.280342,51.0455714 154.217326,51.0455714 C152.408991,50.9498643 150.863374,52.3390283 150.758509,54.1542744 C150.758509,54.4694826 150.811294,54.7824468 150.915035,55.0799453 C151.050282,55.4317993 151.391299,55.660216 151.76678,55.6504525 L180.375453,55.6504525 C180.930816,55.6674325 181.44584,55.3603284 181.696776,54.8625607" id="Fill-1" fill="url(#linearGradient-9)" opacity="0.528714135"></path>
                        <path d="M129.750492,48.3461321 L169.397293,48.3461321 C169.336986,51.1187223 167.531178,52.2519122 163.979869,51.7457019 C164.673055,56.1811659 158.592937,58.5581091 155.668748,56.1811659 C153.159251,61.4933126 147.029329,58.5591297 147.029329,56.1811659 C144.851179,58.9765491 140.079171,57.4354653 139.837267,54.329824 C136.220908,56.350583 133.637214,53.7817697 133.637214,51.6262934 C131.046066,51.7058991 129.750492,50.612512 129.750492,48.3461321 Z" id="路径-19" fill="url(#linearGradient-10)" transform="translate(149.573892, 53.754720) rotate(180.000000) translate(-149.573892, -53.754720) "></path>
                        <path d="M64.8664239,60.2451278 L25.2196231,60.2451278 C25.2799296,57.8889364 27.0857375,56.9255039 30.6370468,57.3548301 C29.9438605,53.5847878 36.0239785,51.5640288 38.9481677,53.5847878 C41.4576647,49.0686976 47.5875864,51.5640288 47.5875864,53.5847878 C49.7657364,51.2088651 54.5377447,52.5192967 54.7796484,55.1585304 C58.3960075,53.4408853 60.9797019,55.6249379 60.9797019,57.455868 C63.5708499,57.3885094 64.8664239,58.3182627 64.8664239,60.2451278 Z" id="路径-19-2" fill="url(#linearGradient-11)"></path>
                    </g>
                    <g id="组_5835" transform="translate(78.847194, 26.540956)" fill="#FFC9C9">
                        <path d="M27.0930594,0.00863830266 C24.7419951,3.03138176 21.8133822,5.55220042 18.4790507,7.4231909 C14.8617954,9.43296434 11.0237596,11.0129257 7.04249308,12.1311511 C4.71899764,12.8343344 2.36704293,13.4415827 0.0191538335,14.0590369 C-0.0123546349,14.0692427 -0.00219061284,14.1233337 0.0293178556,14.1121073 C4.29539532,13.0899889 8.49688612,11.8130054 12.6113608,10.287974 C16.2430158,8.91831138 19.6451443,6.99996455 22.7001691,4.59923123 C24.3605145,3.26966575 25.8500891,1.7383078 27.1347319,0.0402764486 C27.1560764,0.00863830266 27.1133875,-0.0127939898 27.092043,0.00863830266 L27.0930594,0.00863830266 Z" id="路径_8351"></path>
                    </g>
                    <g id="组_5837" transform="translate(96.954646, 31.662929)" fill="#FFC6C6">
                        <path d="M9.85564814,0.0100045108 C7.62083582,2.60904117 5.14077395,4.98481104 2.45014166,7.10409331 C1.65429873,7.73277389 0.848291781,8.32879573 0.0107763627,8.91461173 C-0.00101184953,8.92334836 -0.00351467276,8.94002636 0.00518615054,8.95186309 C0.0138869738,8.96369982 0.0304966189,8.96621294 0.0422848311,8.95747631 C2.87382505,6.97335763 5.50649966,4.71745326 7.9031395,2.22161297 C8.58209617,1.50720323 9.2397084,0.783608209 9.88715661,0.0375603154 C9.91866508,0.0273544619 9.87699259,-0.015510123 9.85564814,0.00592216943 L9.85564814,0.0100045108 Z" id="路径_8352"></path>
                    </g>
                </g>
                <g id="编组-9备份" transform="translate(94.329951, 74.143298) scale(-1, 1) translate(-94.329951, -74.143298) translate(48.694650, 27.286595)">
                    <g id="尾巴备份" transform="translate(30.950315, 46.279055) rotate(-93.000000) translate(-30.950315, -46.279055) translate(-2.484558, 16.994135)">
                        <mask id="mask-14" fill="white">
                            <use xlink:href="#path-13"></use>
                        </mask>
                        <use id="蒙版" fill="url(#linearGradient-12)" xlink:href="#path-13"></use>
                        <path d="M59.0078501,-18.7410101 C75.9065549,-5.15994776 81.5394565,12.3379952 75.9065549,33.7528187 C69.3570409,32.0732605 61.0091699,33.4574842 50.8629421,37.9054899 C51.9337753,14.3373997 45.816912,-0.440971545 32.5123521,-6.42962394 C38.3889896,-10.1322836 47.2208223,-14.236079 59.0078501,-18.7410101 Z" id="路径-18" fill="url(#linearGradient-15)" mask="url(#mask-14)"></path>
                        <path d="M28.2809875,-1.97524985 C40.5909017,9.59904939 44.6942065,24.5114636 40.5909017,42.7619928 C35.8198878,41.3306093 33.7740482,42.3891475 34.453383,45.9376074 C28.317342,22.349672 21.443842,9.12436446 13.8328829,6.26168486 C18.1137379,3.10613713 22.9297727,0.360492228 28.2809875,-1.97524985 Z" id="路径-18备份" fill="url(#linearGradient-16)" mask="url(#mask-14)"></path>
                        <path d="M8.84311776,10.3214216 C18.6879332,20.7053625 21.9595452,34.0935868 18.6579537,50.4860944 C14.8395889,49.2045401 13.2007392,50.1567695 13.7414047,53.3427827 C8.8502332,32.1654719 3.35928641,20.2945232 -2.73143566,17.7299365 C0.698656035,14.8927651 4.55684051,12.4232601 8.84311776,10.3214216 Z" id="路径-18备份-2" fill="url(#linearGradient-17)" mask="url(#mask-14)" transform="translate(8.585131, 31.832102) rotate(-43.000000) translate(-8.585131, -31.832102) "></path>
                        <path d="M40.9791366,73.2670699 C58.5925971,73.2670699 72.8711281,60.1391514 72.8711281,43.9450431 C72.8711281,33.4926822 70.4156339,19.923569 61.4614941,14.7314128 C56.5427576,11.8792295 47.2241264,14.6230162 40.9791366,14.6230162 C23.365676,14.6230162 9.08714507,27.7509348 9.08714507,43.9450431 C9.08714507,60.1391514 23.365676,73.2670699 40.9791366,73.2670699 Z" id="椭圆形" fill="#563600" opacity="0.446166992" filter="url(#filter-18)" mask="url(#mask-14)"></path>
                        <ellipse id="椭圆形备份-8" fill="#FDCE49" opacity="0.299277169" filter="url(#filter-19)" mask="url(#mask-14)" cx="3.60035083" cy="-10.0029048" rx="41.3795732" ry="31.9564277"></ellipse>
                        <ellipse id="椭圆形备份-11" fill="#FDCE49" opacity="0.27515593" filter="url(#filter-20)" mask="url(#mask-14)" cx="65.2407476" cy="3.62725614" rx="20.346862" ry="16.0354834"></ellipse>
                    </g>
                    <g id="穿衣" transform="translate(18.358759, 0.000000)">
                        <g id="身" transform="translate(22.778678, 53.719282)">
                            <g id="路径-44" transform="translate(25.282062, 20.831441) scale(-1, 1) rotate(-15.000000) translate(-25.282062, -20.831441) ">
                                <use fill="url(#linearGradient-21)" fill-rule="evenodd" xlink:href="#path-22"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-23)" xlink:href="#path-22"></use>
                            </g>
                            <path d="M25.3995695,22.6013098 C27.4553592,22.9129706 28.9315357,21.9299187 29.828099,19.6521539 C30.7246624,17.3743892 31.0040874,16.3044851 30.6663742,16.4424417 L25.3995695,22.6013098 Z" id="路径-46备份" fill="url(#linearGradient-24)" opacity="0.713535854" filter="url(#filter-25)" transform="translate(28.102274, 19.544100) rotate(-30.000000) translate(-28.102274, -19.544100) "></path>
                            <g id="路径-44备份" transform="translate(11.734709, 27.482814) scale(-1, 1) rotate(-68.000000) translate(-11.734709, -27.482814) ">
                                <use fill="url(#linearGradient-26)" fill-rule="evenodd" xlink:href="#path-27"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-28)" xlink:href="#path-27"></use>
                            </g>
                            <g id="椭圆形">
                                <mask id="mask-31" fill="white">
                                    <use xlink:href="#path-30"></use>
                                </mask>
                                <g id="蒙版">
                                    <use fill="url(#linearGradient-29)" fill-rule="evenodd" xlink:href="#path-30"></use>
                                    <use fill="black" fill-opacity="1" filter="url(#filter-32)" xlink:href="#path-30"></use>
                                </g>
                                <ellipse fill="#FDF2A4" opacity="0.753339495" filter="url(#filter-33)" mask="url(#mask-31)" cx="11.960972" cy="6.87235005" rx="7.77295851" ry="6.87235005"></ellipse>
                                <ellipse fill="#FF9913" opacity="0.502425421" filter="url(#filter-34)" mask="url(#mask-31)" cx="15.3902184" cy="-0.572695837" rx="12.345287" ry="5.84149754"></ellipse>
                            </g>
                        </g>
                        <g id="编组-9备份" transform="translate(37.024193, 68.127543) scale(-1, 1) translate(-37.024193, -68.127543) translate(21.153098, 50.574301)">
                            <g id="衣服" transform="translate(15.871095, 17.553242) scale(-1, 1) translate(-15.871095, -17.553242) ">
                                <g id="编组-6" transform="translate(0.000000, 5.252712)">
                                    <g id="编组-5">
                                        <g id="椭圆形备份-3" transform="translate(15.871095, 14.926886) rotate(-7.000000) translate(-15.871095, -14.926886) translate(1.508029, 1.654141)">
                                            <mask id="mask-37" fill="white">
                                                <use xlink:href="#path-36"></use>
                                            </mask>
                                            <g id="蒙版">
                                                <use fill="url(#linearGradient-35)" fill-rule="evenodd" xlink:href="#path-36"></use>
                                                <use fill="black" fill-opacity="1" filter="url(#filter-38)" xlink:href="#path-36"></use>
                                            </g>
                                            <ellipse fill="#FFFFFF" filter="url(#filter-39)" mask="url(#mask-37)" cx="19.4539898" cy="12.7395337" rx="4.45797878" ry="4.47606858"></ellipse>
                                            <ellipse id="椭圆形备份-6" fill="#CECECA" filter="url(#filter-40)" mask="url(#mask-37)" cx="23.1513611" cy="11.0620651" rx="2.90737746" ry="2.91917516"></ellipse>
                                            <ellipse id="椭圆形备份-7" fill="#CECECA" filter="url(#filter-41)" mask="url(#mask-37)" cx="14.0146181" cy="20.6809378" rx="2.90737746" ry="2.91917516"></ellipse>
                                            <path d="M-0.740783847,18.5389575 C1.03615874,19.1720772 2.70660791,19.6035724 4.27056369,19.833443 C2.25335809,19.866168 0.675518966,19.6923879 -0.462953685,19.3121029 L-0.740783847,18.5389575 Z" id="路径-16" fill="#DCDAD4" opacity="0.563804263" filter="url(#filter-42)" mask="url(#mask-37)"></path>
                                            <path d="M1.29756611,22.9138002 C4.74122761,23.5469198 7.97850808,23.978415 11.0094075,24.2082856 C7.1001235,24.2410106 4.04231854,24.0672306 1.83599264,23.6869455 L1.29756611,22.9138002 Z" id="路径-16备份-2" fill="#DCDAD4" filter="url(#filter-43)" mask="url(#mask-37)"></path>
                                        </g>
                                        <ellipse id="椭圆形" fill="#FFFFFF" filter="url(#filter-44)" cx="18.5888214" cy="8.68569634" rx="8.1406569" ry="8.17369045"></ellipse>
                                        <g id="路径-14" filter="url(#filter-47)" transform="translate(15.346218, 19.511101) rotate(-16.000000) translate(-15.346218, -19.511101) ">
                                            <use fill="url(#linearGradient-45)" fill-rule="evenodd" xlink:href="#path-46"></use>
                                            <use fill="black" fill-opacity="1" filter="url(#filter-48)" xlink:href="#path-46"></use>
                                        </g>
                                    </g>
                                    <g id="编组-4" transform="translate(14.594654, 14.366763) rotate(-8.000000) translate(-14.594654, -14.366763) translate(6.422123, 10.249331)">
                                        <g id="路径-13">
                                            <use fill="black" fill-opacity="1" filter="url(#filter-51)" xlink:href="#path-50"></use>
                                            <use fill="url(#linearGradient-49)" fill-rule="evenodd" xlink:href="#path-50"></use>
                                            <use fill="black" fill-opacity="1" filter="url(#filter-52)" xlink:href="#path-50"></use>
                                        </g>
                                        <g id="路径-13">
                                            <use fill="black" fill-opacity="1" filter="url(#filter-54)" xlink:href="#path-53"></use>
                                            <path stroke="#E4E4E4" stroke-width="0.5" d="M8.18732158,0.895008704 C9.80100014,0.895008704 11.7357528,1.03347841 13.9907305,1.30967664 L13.9907305,1.30967664 L14.5635127,1.3818976 C15.2995341,3.63743284 14.8290064,4.88246503 13.7572315,5.77004471 C12.6574577,6.6808113 10.9473546,7.21693712 8.63786045,7.40075166 C6.06429544,7.33169444 4.1335303,6.87057793 2.85404776,5.99653707 C1.61238983,5.14833498 1.00680591,3.90680777 1.02556678,2.28501705 C1.0360942,2.01184241 1.10384659,1.79378139 1.22046791,1.63790765 C1.33076821,1.49048245 1.4847641,1.40471252 1.66015905,1.38280159 C4.26125658,1.05786363 6.43696462,0.895008704 8.18732158,0.895008704 Z" stroke-linejoin="square"></path>
                                        </g>
                                        <use id="路径-13" stroke="#FFFFFF" mask="url(#mask-56)" stroke-dasharray="0.3872727924271634" xlink:href="#path-55"></use>
                                    </g>
                                </g>
                                <g id="编组-3" filter="url(#filter-57)" transform="translate(7.971355, 5.939197) rotate(8.000000) translate(-7.971355, -5.939197) translate(5.803222, 0.246806)">
                                    <g id="矩形">
                                        <use fill="url(#linearGradient-58)" fill-rule="evenodd" xlink:href="#path-59"></use>
                                        <use fill="black" fill-opacity="1" filter="url(#filter-60)" xlink:href="#path-59"></use>
                                    </g>
                                    <use id="矩形" stroke="url(#linearGradient-61)" mask="url(#mask-63)" stroke-dasharray="0.3872727924271634" xlink:href="#path-62"></use>
                                    <g id="椭圆形" filter="url(#filter-64)" transform="translate(0.969126, 7.881773)">
                                        <mask id="mask-67" fill="white">
                                            <use xlink:href="#path-66"></use>
                                        </mask>
                                        <g id="蒙版">
                                            <use fill="url(#linearGradient-65)" fill-rule="evenodd" xlink:href="#path-66"></use>
                                            <use fill="black" fill-opacity="1" filter="url(#filter-68)" xlink:href="#path-66"></use>
                                        </g>
                                        <ellipse fill="#FFF1BC" filter="url(#filter-69)" mask="url(#mask-67)" cx="0.872213239" cy="0.973058387" rx="1" ry="1"></ellipse>
                                    </g>
                                </g>
                            </g>
                        </g>
                        <g id="编组-10备份" transform="translate(36.455921, 34.760568) rotate(14.000000) translate(-36.455921, -34.760568) translate(5.903230, 6.565056)">
                            <g id="编组-9备份-3" transform="translate(-0.000000, 0.000000)">
                                <g id="编组-8备份-2" transform="translate(-0.000000, 0.000000)">
                                    <g id="编组-7备份-2" transform="translate(0.000000, 0.000000)">
                                        <g id="头-默认" transform="translate(-0.000000, -0.000000)">
                                            <g id="编组-21" transform="translate(40.487310, 2.150468)">
                                                <g id="路径-6">
                                                    <use fill="url(#linearGradient-70)" fill-rule="evenodd" xlink:href="#path-71"></use>
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-72)" xlink:href="#path-71"></use>
                                                </g>
                                                <g id="路径-6">
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-75)" xlink:href="#path-74"></use>
                                                    <use fill="url(#radialGradient-73)" fill-rule="evenodd" xlink:href="#path-74"></use>
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-76)" xlink:href="#path-74"></use>
                                                </g>
                                            </g>
                                            <g id="编组-21备份" transform="translate(12.055201, 14.989155) scale(-1, 1) translate(-12.055201, -14.989155) translate(3.401950, 2.150468)">
                                                <g id="路径-6">
                                                    <use fill="url(#linearGradient-70)" fill-rule="evenodd" xlink:href="#path-77"></use>
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-78)" xlink:href="#path-77"></use>
                                                </g>
                                                <g id="路径-6">
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-80)" xlink:href="#path-79"></use>
                                                    <use fill="url(#radialGradient-73)" fill-rule="evenodd" xlink:href="#path-79"></use>
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-81)" xlink:href="#path-79"></use>
                                                </g>
                                            </g>
                                            <g id="路径-9-+-椭圆形备份-10-蒙版" transform="translate(-0.000000, -0.000000)">
                                                <mask id="mask-84" fill="white">
                                                    <use xlink:href="#path-83"></use>
                                                </mask>
                                                <g id="蒙版" transform="translate(30.551838, 28.194963) rotate(-9.000000) translate(-30.551838, -28.194963) ">
                                                    <use fill="url(#linearGradient-82)" fill-rule="evenodd" xlink:href="#path-83"></use>
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-85)" xlink:href="#path-83"></use>
                                                </g>
                                                <ellipse id="椭圆形" fill="#FFF172" filter="url(#filter-86)" mask="url(#mask-84)" cx="30.3849834" cy="21.5076148" rx="13.4855853" ry="13.5144075"></ellipse>
                                                <g id="路径-9" mask="url(#mask-84)">
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-89)" xlink:href="#path-88"></use>
                                                    <use fill="url(#linearGradient-87)" fill-rule="evenodd" xlink:href="#path-88"></use>
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-90)" xlink:href="#path-88"></use>
                                                </g>
                                                <g id="椭圆形备份-10" mask="url(#mask-84)">
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-93)" xlink:href="#path-92"></use>
                                                    <use fill="url(#linearGradient-91)" fill-rule="evenodd" xlink:href="#path-92"></use>
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-94)" xlink:href="#path-92"></use>
                                                </g>
                                            </g>
                                            <ellipse id="椭圆形" fill="#C59B23" opacity="0.377893357" filter="url(#filter-95)" cx="36.8199327" cy="27.0692353" rx="10.6760883" ry="10.6989059"></ellipse>
                                            <g id="椭圆形备份-9">
                                                <use fill="black" fill-opacity="1" filter="url(#filter-98)" xlink:href="#path-97"></use>
                                                <use fill="url(#linearGradient-96)" fill-rule="evenodd" xlink:href="#path-97"></use>
                                                <use fill="black" fill-opacity="1" filter="url(#filter-99)" xlink:href="#path-97"></use>
                                            </g>
                                            <g id="椭圆形">
                                                <use fill="black" fill-opacity="1" filter="url(#filter-102)" xlink:href="#path-101"></use>
                                                <use fill="url(#linearGradient-100)" fill-rule="evenodd" xlink:href="#path-101"></use>
                                                <use fill="black" fill-opacity="1" filter="url(#filter-103)" xlink:href="#path-101"></use>
                                            </g>
                                            <ellipse id="椭圆形" fill="#FFF372" opacity="0.546138945" filter="url(#filter-104)" cx="39.1630265" cy="35.3177901" rx="6.76549247" ry="6.77995208"></ellipse>
                                            <g id="椭圆形" filter="url(#filter-105)" transform="translate(34.580936, 27.902195)">
                                                <mask id="mask-108" fill="white">
                                                    <use xlink:href="#path-107"></use>
                                                </mask>
                                                <g id="蒙版">
                                                    <use fill="url(#linearGradient-106)" fill-rule="evenodd" xlink:href="#path-107"></use>
                                                    <use fill="black" fill-opacity="1" filter="url(#filter-109)" xlink:href="#path-107"></use>
                                                </g>
                                                <ellipse fill="#BC9324" opacity="0.865592593" filter="url(#filter-110)" mask="url(#mask-108)" cx="3.78384655" cy="1.73195342" rx="2.30017297" ry="1.40715987"></ellipse>
                                            </g>
                                            <path d="M33.6349392,4.95924754 C36.3479939,3.92470869 38.0429818,2.6329578 38.7199029,1.08399488 C39.8800228,-2.43958651 43.4697372,4.49787462 39.0375148,6.32070064 C37.1756667,6.73120213 35.3631945,6.41206228 33.6000979,5.36328107 L33.6000301,5.36339495 C33.49309,5.29978141 33.4579669,5.16152035 33.5215804,5.05458021 C33.5475427,5.01093516 33.5874888,4.97734131 33.6349392,4.95924754 Z" id="路径-11" fill="url(#linearGradient-111)"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                            <g id="路径-4">
                                <use fill="black" fill-opacity="1" filter="url(#filter-114)" xlink:href="#path-113"></use>
                                <use fill="url(#linearGradient-112)" fill-rule="evenodd" xlink:href="#path-113"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-115)" xlink:href="#path-113"></use>
                            </g>
                            <g id="路径-4备份" transform="translate(26.716246, 15.996764) scale(-1, 1) translate(-26.716246, -15.996764) ">
                                <use fill="black" fill-opacity="1" filter="url(#filter-117)" xlink:href="#path-116"></use>
                                <use fill="url(#linearGradient-112)" fill-rule="evenodd" xlink:href="#path-116"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-118)" xlink:href="#path-116"></use>
                            </g>
                        </g>
                        <g id="书" transform="translate(44.461399, 63.279081) scale(-1, 1) rotate(3.000000) translate(-44.461399, -63.279081) translate(28.187869, 51.767401)">
                            <path d="M2.46572158,5.04559117 C3.95476127,4.74953675 4.69928111,4.00304048 4.69928111,2.80610234 C7.8257101,3.08811472 10.2094158,3.95683621 11.8503981,5.41226683 C14.2417568,3.03931606 17.8665137,1.23522711 22.7246689,7.56412096e-13 C23.5337387,1.12856763 24.6378505,1.59508192 26.0370043,1.39954287 L13.0496133,7.72082652 C11.9417168,8.69171021 11.0379088,8.90071801 10.3381894,8.34784993 C8.36090934,6.2553239 5.73675341,5.15457098 2.46572158,5.04559117 Z" id="路径-67" fill="url(#linearGradient-119)"></path>
                            <g id="路径-64">
                                <use fill="black" fill-opacity="1" filter="url(#filter-122)" xlink:href="#path-121"></use>
                                <use fill="url(#linearGradient-120)" fill-rule="evenodd" xlink:href="#path-121"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-123)" xlink:href="#path-121"></use>
                            </g>
                            <g id="路径-66">
                                <use fill="black" fill-opacity="1" filter="url(#filter-126)" xlink:href="#path-125"></use>
                                <use fill="url(#linearGradient-124)" fill-rule="evenodd" xlink:href="#path-125"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-127)" xlink:href="#path-125"></use>
                            </g>
                            <path d="M14.7625125,9.57745533 C19.2354609,6.16900741 22.8334217,4.69532883 25.5563947,5.15641958 C26.9534303,5.84615413 28.0881896,8.78966054 28.9606726,13.9869388 C22.1903723,16.6677066 18.5456345,18.7302814 18.0264591,20.1746634" id="路径-68" stroke="#FFFFFF" stroke-width="0.511695035" opacity="0.276958647" stroke-linecap="round" stroke-dasharray="2.046780129124347"></path>
                            <path d="M10.5322161,10.3620643 C7.5588608,7.96894949 4.82904556,6.99975321 2.3427704,7.45447543 C2.96253815,8.33186881 4.32744577,11.9317334 6.43749326,18.2540693 C11.2197166,19.2263727 13.7154111,20.150643 13.9245769,21.0268803" id="路径-69" stroke="#FFFFFF" stroke-width="0.511695035" opacity="0.276958647" stroke-linecap="round" stroke-dasharray="2.046780129124347"></path>
                            <g id="路径-65">
                                <use fill="black" fill-opacity="1" filter="url(#filter-130)" xlink:href="#path-129"></use>
                                <use fill="url(#linearGradient-128)" fill-rule="evenodd" xlink:href="#path-129"></use>
                            </g>
                            <g id="星形" transform="translate(21.332298, 11.344427) rotate(-19.000000) translate(-21.332298, -11.344427) ">
                                <use fill="black" fill-opacity="1" filter="url(#filter-133)" xlink:href="#path-132"></use>
                                <use fill="url(#linearGradient-131)" fill-rule="evenodd" xlink:href="#path-132"></use>
                                <use fill="black" fill-opacity="1" filter="url(#filter-134)" xlink:href="#path-132"></use>
                            </g>
                        </g>
                    </g>
                    <g id="编组" transform="translate(47.482159, 24.999834)">
                        <g id="编组-18备份" filter="url(#filter-135)" transform="translate(5.231321, 5.721935) scale(-1, 1) rotate(-1.000000) translate(-5.231321, -5.721935) translate(0.097380, 0.088878)">
                            <ellipse id="椭圆形" fill="url(#linearGradient-136)" transform="translate(5.133941, 5.633058) rotate(-12.000000) translate(-5.133941, -5.633058) " cx="5.13394144" cy="5.63305772" rx="4.21713307" ry="4.86083502"></ellipse>
                            <g id="椭圆形" transform="translate(5.111806, 6.473932) rotate(-12.000000) translate(-5.111806, -6.473932) translate(1.917121, 3.019994)">
                                <mask id="mask-139" fill="white">
                                    <use xlink:href="#path-138"></use>
                                </mask>
                                <g id="蒙版">
                                    <use fill="url(#linearGradient-137)" fill-rule="evenodd" xlink:href="#path-138"></use>
                                    <use fill="black" fill-opacity="1" filter="url(#filter-140)" xlink:href="#path-138"></use>
                                </g>
                                <ellipse fill="#FFFFFF" mask="url(#mask-139)" cx="1.48878112" cy="2.14211554" rx="1.71665428" ry="1.55840886"></ellipse>
                                <ellipse id="椭圆形备份-4" fill="#FFFFFF" mask="url(#mask-139)" cx="4.51257518" cy="4.04616375" rx="1" ry="1"></ellipse>
                            </g>
                        </g>
                        <g id="编组-18备份-4" filter="url(#filter-141)" transform="translate(25.166153, 13.141506) rotate(29.000000) translate(-25.166153, -13.141506) translate(20.032212, 7.508448)">
                            <ellipse id="椭圆形" fill="url(#linearGradient-136)" transform="translate(5.133941, 5.633058) rotate(-12.000000) translate(-5.133941, -5.633058) " cx="5.13394144" cy="5.63305772" rx="4.21713307" ry="4.86083502"></ellipse>
                            <g id="椭圆形" transform="translate(5.111806, 6.473932) rotate(-12.000000) translate(-5.111806, -6.473932) translate(1.917121, 3.019994)">
                                <mask id="mask-143" fill="white">
                                    <use xlink:href="#path-142"></use>
                                </mask>
                                <g id="蒙版">
                                    <use fill="url(#linearGradient-137)" fill-rule="evenodd" xlink:href="#path-142"></use>
                                    <use fill="black" fill-opacity="1" filter="url(#filter-144)" xlink:href="#path-142"></use>
                                </g>
                                <ellipse fill="#FFFFFF" mask="url(#mask-143)" cx="1.48878112" cy="2.14211554" rx="1.71665428" ry="1.55840886"></ellipse>
                                <ellipse id="椭圆形备份-4" fill="#FFFFFF" mask="url(#mask-143)" cx="4.51257518" cy="4.04616375" rx="1" ry="1"></ellipse>
                            </g>
                        </g>
                    </g>
                    <g id="路径-61" transform="translate(47.518267, 62.900875) scale(-1, 1) rotate(-94.000000) translate(-47.518267, -62.900875) ">
                        <use fill="black" fill-opacity="1" filter="url(#filter-147)" xlink:href="#path-146"></use>
                        <use fill="url(#linearGradient-145)" fill-rule="evenodd" xlink:href="#path-146"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-148)" xlink:href="#path-146"></use>
                    </g>
                    <path d="M52.1087819,85.111247 C54.1644484,85.4245242 55.6396204,84.4424209 56.5342979,82.1649372 C57.4289754,79.8874535 57.7075395,78.8175738 57.3699901,78.955298 L52.1087819,85.111247 Z" id="路径-46" fill="url(#linearGradient-149)" opacity="0.713535854" filter="url(#filter-150)" transform="translate(54.808736, 82.055794) rotate(17.000000) translate(-54.808736, -82.055794) "></path>
                    <g id="路径-12" stroke-linecap="round" transform="translate(58.873637, 46.779915) scale(-1, 1) rotate(-18.000000) translate(-58.873637, -46.779915) ">
                        <use fill="black" fill-opacity="1" filter="url(#filter-152)" xlink:href="#path-151"></use>
                        <use stroke="#775000" stroke-width="0.915318218" xlink:href="#path-151"></use>
                    </g>
                    <g id="路径-56" transform="translate(75.108923, 64.010165) scale(-1, 1) rotate(-54.000000) translate(-75.108923, -64.010165) ">
                        <use fill="black" fill-opacity="1" filter="url(#filter-155)" xlink:href="#path-154"></use>
                        <use fill="url(#linearGradient-153)" fill-rule="evenodd" xlink:href="#path-154"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>