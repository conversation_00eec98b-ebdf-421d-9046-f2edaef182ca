// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.popper {
  min-width: 10px !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  border: none !important;
  color: #fff !important;
  padding: 0 !important;
  .popper__arrow::after{
    border-right-color: rgba(0, 0, 0, 0.5) !important;
    left: 0 !important;
  }
}

.top50 {
  top: 25% !important;
  z-index: 9999999 !important;
}

.message-override {
  z-index: 9999999 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  border-color: rgba(0, 0, 0, 0.5) !important;
}

.nocaptcha-dialog {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  height: 140px;

  .el-dialog__header {
    padding: 16px;
  }

  .el-dialog__body {
    padding: 0 16px;

    .close-dialog {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 20px;
      color: #DFDFDF;
      cursor: pointer;

      &:hover {
        color: #FF963D;
      }
    }
  }
}

.classpro-dialog {
  .el-dialog {
    background: transparent;
    box-shadow: none;
  }

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0px;
  }

  .el-edit-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
}



.el-dialog.classpro-book-dialog {
  position: relative;
  background: #FFFFFF;
  border-radius: 15px;
  border: none;
  box-shadow: none;
  user-select: none;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 30px 24px;
  }

  .dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: #313131;
    line-height: 24px;
    text-align: center;
  }
}

.search-input.el-input {
  width: 210px;
  height: 43px;
  border: none;
  outline: none;

  input {
    padding: 0 12px 0 36px;
    height: 43px;
    line-height: 43px;
    border: none;
    border-radius: 18px;
    background: rgba($color: #FFFFFF, $alpha: 0.3);
    font-size: 16px;

    &::placeholder {
      color: #8C9399;
      font-size: 16px;
      text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.15);
    }
  }

  .el-input__prefix {
    display: flex;
    justify-content: center;
    align-items: center;
    left: 6px;

    .svg-icon {
      font-size: 20px;
    }
  }
}

.shadow-box {
  padding: 16px;
  background: #FFFFFF;
  box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
}

.el-drawer {
  background: transparent;
  box-shadow: none;
}

.course-detail {
  .el-timeline-item__timestamp {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #1C1B1A !important;
    letter-spacing: 0.22px;
    white-space: pre-wrap;
  }
}

.el-popover{
  min-width: 80px !important;
  padding: 8px !important;
}



