@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './btn.scss';
@import './utils.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

@mixin sizeScale($scale: 1) {
  // .layout-tool {
  //   transform-origin: right bottom;
  //   transform: scale($scale);
  // }
  // .controls-container {
  //   transform-origin: center center;
  //   transform: scale($scale);
  // }
  // .bingo-tools-container {
  //   transform-origin: left center;
  //   transform: scale($scale);
  // }
  // .nav-container {
  //   .left {
  //     transform-origin: left top;
  //   }
  //   .right {
  //     transform-origin: right top;
  //   }
  //   .class-room-name-wrap {
  //     transform-origin: center top;
  //   }
  //   .left, .right, .class-room-name-wrap {
  //     transform: scale($scale);
  //   }
  // }
}


@media screen and (max-width: 768px) {
  @include sizeScale(0.4);
}

@media screen and (max-width: 768px) {
  @include sizeScale(0.4);
}

@media screen and (max-width: 960px) {
  @include sizeScale(0.5);
}

@media screen and (min-width: 960px) {
  @include sizeScale(0.5);
}

@media screen and (min-width: 960px) {
  @include sizeScale(0.5);
}

@media screen and (min-width: 1280px) {
  @include sizeScale(0.65);
}

@media screen and (min-width: 1440px) {
  @include sizeScale(0.75);
}

@media screen and (min-width: 1536px) {
  @include sizeScale(0.8);
}

@media screen and (min-width: 1632px) {
  @include sizeScale(0.85);
}

@media screen and (min-width: 1728px) {
  @include sizeScale(0.9);
}

@media screen and (min-width: 1824px) {
  @include sizeScale(0.95);
}

@media screen and (min-width: 1920px) {
  @include sizeScale();
}

@font-face {
  font-family: 'DOUYUFont';
  src: url('../assets/font/douyuFont.otf');
}

.vjs-has-started .vjs-control-bar {
  padding: 0 40px 0 20px;
}

.content-left-show-full-screen {
  .vjs-has-started .vjs-control-bar {
    padding: 0 65px 0 30px;
  }
}

.vjs-paused .vjs-big-play-button,
.vjs-paused.vjs-has-started .vjs-big-play-button {
   display: none;
}

.classpro-loadding {
  .el-loading-spinner {
    /*这个是自己想设置的 gif 加载动图*/
    background-image:url('../assets/images/loading.gif');
    background-repeat: no-repeat;
    background-size: 200px 120px;
    height:100px;
    width:100%;
    background-position:center;
    /*覆盖 element-ui  默认的 50%    因为此处设置了height:100%,所以不设置的话，会只显示一半，因为被top顶下去了*/
    top:40%;
  }
  .el-loading-spinner .circular {
    /*隐藏 之前  element-ui  默认的 loading 动画*/
    display: none;
  }

  .el-loading-spinner .el-loading-text{
    /*为了使得文字在loading图下面*/
    margin:85px 0px;
  }
}

*|*:fullscreen:not(:root) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  min-width: 0 !important;
  max-width: none !important;
  min-height: 0 !important;
  max-height: none !important;
  box-sizing: border-box !important;
  object-fit: contain;
  transform: none !important;
}

.classpro-btn {
  position: relative;
  background: #3479FF;
  // box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  padding: 5px 0;
  color: #FFFFFF;
  text-align: center;
  cursor: pointer;
  // &::before {
  //   content: ' ';
  //   position: absolute;
  //   width: 50%;
  //   left: 0;
  //   height: 100%;
  //   background: linear-gradient(124deg, rgba(255, 255, 255, 0.74) 0%, rgba(255, 255, 255, 0) 100%);
  //   opacity: 0.51;
  //   filter: blur(1px);
  //   border-radius: 16px 0 0 16px;
  // }
  &:hover {
    background: #6193FF;
    box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41);
  }
}


.classpro-btn-opacity {
  position: relative;
  border-radius: 16px;
  border: 1px solid #1F66FF;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  padding: 5px 0;
  color: #1F66FF;
  text-align: center;
  cursor: pointer;
  &:hover {
    border: none;
    color: #FFFFFF;
    background: #1F66FF;
    box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
    &::before {
      content: ' ';
      position: absolute;
      width: 50%;
      left: 0;
      height: 100%;
      background: linear-gradient(124deg, rgba(255, 255, 255, 0.74) 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0.51;
      filter: blur(1px);
    }
  }
}

.classpro-btn-disable {
  position: relative;
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  padding: 5px 0;
  color: #FFFFFF;
  text-align: center;
  cursor: not-allowed;
  background: #BFBFBF;
  box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), 1px 1px 1px 0px #cfcccc, -1px -1px 1px 0px rgba(225, 226, 228, 0.32);
  &:hover {
    background: #BFBFBF;
    box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), 1px 1px 1px 0px #cfcccc, -1px -1px 1px 0px rgba(225, 226, 228, 0.32);
  }
}

.check-popper {
  .el-select-dropdown__item {
    color: #0B0B0B;
    font-size: 18px;
  }
}

.check-popover-tip {
  background: rgba(0, 0, 0, 0.76);
  border-color: rgba(0, 0, 0, 0.76);
  .popper__arrow {
    border-top-color: rgba(0, 0, 0, 0.76) !important;
  }
  .popper__arrow::after {
    border-top-color: rgba(0, 0, 0, 0.76) !important;
  }
}

.empty-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  img {
    width: 90px;
    height: 90px;
  }

  .hint {
    display: flex;
    font-size: 12px;
    font-weight: 400;
    color: #8C9399;
    line-height: 17px;
    padding-top: 8px;

    .hint-blue {
      color: rgba(31, 102, 255, 1);
      cursor: pointer;
    }
  }
}

.update-dialog {
  width: 517px;
  height: 548px;
  background: url('../assets/images/home/<USER>') no-repeat;
  background-size: cover;
  border: none;
  box-shadow: none;
  .el-dialog__body{
    padding-bottom: 25px;
  }
  .update-dialog-body{
    margin-top: 172px;
    text-align: center;
    h3{
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #392E30;
      line-height: 25px;
      margin: 0px;
    }
    p{
      margin-top: 8px;
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #1F66FF;
      line-height: 20px;
      margin-bottom: 0px;
    }
    .note {
      padding: 0 60px;
      text-align: left;
      width: 387px;
      height: 130px;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      white-space: pre-wrap;
      word-wrap: break-word;
      overflow: auto;
    }
  }
  .updating-body{
    margin: 0 auto;
    margin-top: 230px;
    text-align: center;
    width: 310px;
    h2{
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #1F66FF;
      line-height: 25px;
      margin-top: 0px;
      margin-bottom: 22px;
    }
    .el-progress.el-progress--line{
      .el-progress-bar__outer{
        background-color: #F7F7F7;
        .el-progress-bar__innerText{
          color: #717171;
        }
      }
    }
  }
  .el-dialog__footer{
    .updating-footer{
      width: 215px;
      margin: 0 auto;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      .el-button.el-button--default{
        background: rgba(62, 89, 253, 0.1);
        border-radius: 10px;
        border: 2px solid #1F66FF;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        background: #fff;
        color: #1F66FF;
        line-height: 20px;
        min-width: 98px;
        padding: 8px 21px;
      }
    }
    .el-button{
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      line-height: 20px;
      padding: 8px 21px;
    }
    .updated-footer{
      width: 215px;
      margin: 0 auto;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      .el-button.el-button--primary {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 20px;
        padding: 8px 21px;
        border-radius: 10px;
        background: linear-gradient(180deg, rgba(62, 89, 253, 0.41) 0%, #1F66FF 100%);
        border: none;
        color: #FFFFFF;
        min-width: 98px;
        font-weight: 500;
      }
    }
    .dialog-footer{
      width: 215px;
      margin: 0 auto;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      .el-button.el-button--default {
        background: rgba(62, 89, 253, 0.1);
        border-radius: 10px;
        border: 2px solid #1F66FF;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #1F66FF;
        background: white;
        line-height: 20px;
        padding: 8px 21px;
      }
      .el-button.el-button--primary {
        margin-left: 15px;
        background: linear-gradient(180deg, rgba(62, 89, 253, 0.41) 0%, #1F66FF 100%);
        border-radius: 10px;
        border: none;
        color: #FFFFFF;
        font-weight: 500;
      }
    }
  }
}

.el-scrollbar__wrap {
  overflow-x: hidden;
}

.el-select-dropdown__wrap {
  margin-bottom: 5px !important;
}

.van-toast {
  max-width: 85% !important;
}

.el-table th.el-table__cell > .cell {
  font-size: 12px !important;
}

.tox-tinymce-aux {
  z-index: 98 !important;
}
