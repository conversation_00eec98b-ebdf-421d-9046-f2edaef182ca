@import './mixin.scss';
@import './variables.scss';

.edu-btn-opacity {
  border-radius: 50px;
  width: 110px;
  height: 30px;
  border: 1px solid #1F66FF;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #1F66FF;
  text-align: center;
  cursor: pointer;
  &:hover {
    color: #FFFFFF;
    background: #1F66FF;
  }
}

.edu-btn {
  border-radius: 50px;
  width: 110px;
  height: 30px;
  border: 1px solid #1F66FF;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #fff;
  background: #1F66FF;
  text-align: center;
  cursor: pointer;
  margin-left: 2px;
}

.edu-btn-op {
  border-radius: 50px;
  width: 110px;
  height: 30px;
  border: 1px solid #1F66FF;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #1F66FF;
  text-align: center;
  cursor: pointer;
}