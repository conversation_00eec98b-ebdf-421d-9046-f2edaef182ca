<svg width="139" height="133" viewBox="0 0 139 133" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M90.3864 106.78C90.3864 106.78 28.3518 134.257 6.80253 85.9684C-0.548863 63.201 3.44999 45.0206 3.44999 45.0206C3.44999 45.0206 10.1696 19.6385 33.4717 19.6004C68.198 23.6319 36.0368 68.7664 60.0479 87.2246C81.6329 96.8397 90.3864 106.78 90.3864 106.78Z" fill="url(#paint0_linear_1213_2038)"/>
<mask id="mask0_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="2" y="19" width="89" height="96">
<path fill-rule="evenodd" clip-rule="evenodd" d="M90.3864 106.78C90.3864 106.78 28.3518 134.257 6.80253 85.9684C-0.548863 63.201 3.44999 45.0206 3.44999 45.0206C3.44999 45.0206 10.1696 19.6385 33.4717 19.6004C68.198 23.6319 36.0368 68.7664 60.0479 87.2246C81.6329 96.8397 90.3864 106.78 90.3864 106.78Z" fill="white"/>
</mask>
<g mask="url(#mask0_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-26.8097 34.2498C-26.8097 34.2498 1.04301 -4.42854 48.6773 5.48126C45.7461 20.0599 56.6733 41.8338 56.6733 41.8338C56.6733 41.8338 4.83975 42.1918 -6.76836 72.1029C-15.5688 59.6211 -26.8097 34.2498 -26.8097 34.2498Z" fill="url(#paint1_linear_1213_2038)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.0723479 77.9572C0.0723479 77.9572 24.0503 49.5885 64.5658 56.5026C61.9744 67.1463 69.6818 65.2462 69.6818 65.2462C69.6818 65.2462 18.6343 81.4358 13.2294 98.4819C5.81313 89.4421 0.0723479 77.9572 0.0723479 77.9572Z" fill="url(#paint2_linear_1213_2038)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.8772 126.631C28.8772 126.631 29.1644 95.1603 59.4001 74.2711C63.4793 82.1328 67.4871 76.2663 67.4871 76.2663C67.4871 76.2663 42.8982 117.243 48.6109 130.607C38.8596 129.854 28.8772 126.631 28.8772 126.631Z" fill="url(#paint3_linear_1213_2038)"/>
<g opacity="0.446167" filter="url(#filter0_f_1213_2038)">
<ellipse cx="65.3975" cy="55.889" rx="46.7629" ry="43.8188" transform="rotate(-93 65.3975 55.889)" fill="#563600"/>
</g>
<g opacity="0.299277" filter="url(#filter1_f_1213_2038)">
<ellipse cx="-9.77585" cy="114.712" rx="60.6745" ry="46.8075" transform="rotate(-93 -9.77585 114.712)" fill="#FDCE49"/>
</g>
<g opacity="0.275156" filter="url(#filter2_f_1213_2038)">
<ellipse cx="5.43065" cy="23.4081" rx="29.8344" ry="23.4876" transform="rotate(-93 5.43065 23.4081)" fill="#FDCE49"/>
</g>
</g>
<g filter="url(#filter3_i_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M98.5238 91.9922C98.5238 91.9922 114.477 94.0875 119.876 84.5408C125.275 74.994 116.066 71.8842 115.102 72.6566C114.139 73.4289 99.0952 87.927 99.0952 87.927L98.5238 91.9922Z" fill="url(#paint4_linear_1213_2038)"/>
</g>
<g filter="url(#filter4_ii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M73.2719 116.64C73.2719 116.64 82.4032 130.376 90.8831 132.532C99.363 134.687 105.725 129.148 98.6333 120.975C91.5417 112.802 91.7246 111.676 91.7246 111.676L73.2719 116.64Z" fill="url(#paint5_linear_1213_2038)"/>
</g>
<g filter="url(#filter5_iii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M94.9808 110.702C94.9808 110.702 98.0401 113.549 104.324 112.6C110.607 111.651 110.713 102.699 106.64 98.3704C105.443 97.6151 104.324 97.7866 104.324 97.7866L94.9808 110.702Z" fill="url(#paint6_linear_1213_2038)"/>
</g>
<g filter="url(#filter6_i_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M71.0702 78.9119C71.0702 78.9119 61.3603 105.596 73.2719 116.64C82.4609 116.277 89.1122 115.193 94.9808 112.837C103.087 109.583 106.043 101.657 105.872 97.7865C105.77 95.5079 104.187 94.9492 102.2 92.7472C100.667 91.1417 99.2232 85.9933 99.2232 85.9933L71.0702 78.9119Z" fill="url(#paint7_linear_1213_2038)"/>
</g>
<mask id="mask1_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="67" y="78" width="39" height="39">
<path fill-rule="evenodd" clip-rule="evenodd" d="M71.0702 78.9119C71.0702 78.9119 61.3603 105.596 73.2719 116.64C82.4609 116.277 89.1122 115.193 94.9808 112.837C103.087 109.583 106.043 101.657 105.872 97.7865C105.77 95.5079 104.187 94.9492 102.2 92.7472C100.667 91.1417 99.2232 85.9933 99.2232 85.9933L71.0702 78.9119Z" fill="white"/>
</mask>
<g mask="url(#mask1_1213_2038)">
<g opacity="0.753339" filter="url(#filter7_f_1213_2038)">
<ellipse cx="84.2481" cy="88.978" rx="11.3974" ry="10.0661" fill="#FDF2A4"/>
</g>
<g opacity="0.502425" filter="url(#filter8_f_1213_2038)">
<ellipse cx="89.2763" cy="78.0731" rx="18.1018" ry="8.55621" fill="#FF9913"/>
</g>
</g>
<g opacity="0.311393" filter="url(#filter9_f_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M66.4555 96.9798C66.4555 96.9798 66.0574 95.2892 66.9896 94.5848C67.9218 93.8803 70.3716 93.1759 70.3716 93.1759L74.1331 87.6111L97.8582 87.6815L100.704 93.1759L104.605 95.1483L104.935 96.6314L66.4555 96.9798Z" fill="#FF9313"/>
</g>
<g filter="url(#filter10_iii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M73.1611 88.4757L73.1926 88.3767L73.2965 88.377L73.2962 88.5188C73.2965 88.377 73.2965 88.377 73.2966 88.377L73.2969 88.377L73.2983 88.377L73.3039 88.377L73.326 88.377L73.4124 88.3772L73.7425 88.3778L74.9374 88.38C75.936 88.3817 77.2907 88.3837 78.7153 88.385C81.565 88.3877 84.6929 88.3877 85.8102 88.377L85.9352 88.3758L85.9392 88.4052C87.3409 88.4411 90.3506 88.6985 93.0955 88.9557C94.5876 89.0955 96.0047 89.2356 97.0486 89.3407C97.5706 89.3932 97.9994 89.437 98.2977 89.4677L98.6426 89.5033L98.7329 89.5127L98.756 89.5151L98.7619 89.5157L98.7633 89.5159L98.7637 89.5159C98.7638 89.5159 98.7638 89.5159 98.7491 89.657L98.871 89.5846C99.0867 89.9477 99.4752 90.3608 99.9673 90.8009C100.457 91.2392 101.041 91.6959 101.637 92.1473C101.885 92.3348 102.136 92.5219 102.383 92.7062C102.728 92.9638 103.066 93.2159 103.378 93.4557C103.913 93.8672 104.383 94.2517 104.699 94.5812C105.551 95.4696 106.589 96.7644 107.849 98.6255L107.857 98.6375L107.863 98.6509C108.04 99.0805 108.226 99.4877 108.368 99.7877C108.439 99.9377 108.498 100.061 108.54 100.146C108.561 100.189 108.578 100.222 108.589 100.245L108.6 100.267C108.605 100.274 108.611 100.283 108.619 100.294C108.635 100.317 108.656 100.351 108.679 100.395C108.725 100.482 108.78 100.608 108.816 100.768C108.89 101.093 108.886 101.549 108.606 102.097L108.596 102.114L108.583 102.129C107.847 102.909 107.066 103.795 106.306 104.823C104.707 107.347 102.452 109.218 100.602 110.457C99.6757 111.078 98.849 111.541 98.2535 111.849C97.9556 112.003 97.7155 112.118 97.5495 112.195C97.4665 112.234 97.402 112.263 97.3581 112.282L97.3079 112.304L97.2948 112.309L97.2914 112.311L97.2905 112.311L97.2902 112.311C97.2901 112.311 97.2901 112.311 97.2351 112.181L97.2901 112.311L97.2867 112.313L97.2867 112.313L94.2285 113.506L94.2561 113.569L94.0664 113.569L93.9672 113.608L93.9492 113.569L93.4919 113.569L93.5167 113.602C93.644 113.769 93.826 114.008 94.0448 114.299C94.4824 114.88 95.0675 115.668 95.6572 116.492C96.2466 117.316 96.8418 118.177 97.2989 118.906C97.5273 119.27 97.7226 119.603 97.8656 119.882C98.0058 120.156 98.1051 120.396 98.1261 120.567C98.1397 120.678 98.1068 120.79 98.0537 120.894C98.0001 121 97.9199 121.109 97.8213 121.221C97.6242 121.445 97.3393 121.695 97.0005 121.956C96.3218 122.479 95.4068 123.066 94.4918 123.614C93.5758 124.162 92.6558 124.674 91.9651 125.049C91.6197 125.237 91.3314 125.39 91.1295 125.497C91.0285 125.55 90.9491 125.591 90.8949 125.62L90.8329 125.652L90.817 125.66L90.8129 125.662L90.8118 125.663L90.8116 125.663L90.8057 125.652L90.8115 125.663L90.8004 125.669L90.7884 125.672L90.7465 125.537C90.7884 125.672 90.7884 125.672 90.7883 125.672L90.7881 125.672L90.7872 125.673L90.784 125.674L90.7714 125.677L90.7222 125.692C90.6789 125.705 90.6152 125.724 90.5325 125.748C90.3671 125.796 90.126 125.863 89.8226 125.943C89.2158 126.103 88.3594 126.311 87.36 126.508C85.3637 126.902 82.787 127.251 80.4888 127.06L80.4757 127.059L80.463 127.056C80.4134 127.042 80.3621 127.028 80.3094 127.014C79.5041 126.796 78.3625 126.487 77.6144 125.63C77.1892 125.143 77.1975 124.51 77.2961 124.027C77.346 123.783 77.4206 123.568 77.4824 123.415C77.5106 123.345 77.5364 123.287 77.5562 123.245L77.4271 123.068C77.3126 122.911 77.1487 122.685 76.9511 122.412C76.5559 121.865 76.0259 121.126 75.487 120.358C74.9482 119.591 74.3998 118.794 73.968 118.132C73.7522 117.8 73.5647 117.502 73.4218 117.256C73.2965 117.041 73.1999 116.857 73.1515 116.727C70.576 114.757 68.8682 111.013 67.7396 107.203C66.6037 103.369 66.0453 99.4343 65.7898 97.0896C65.65 95.8064 66.5925 94.6921 67.8683 94.5534L69.5999 94.3653C70.6597 94.2502 71.552 93.521 71.8759 92.5053L73.1611 88.4757ZM85.4872 88.6631C85.9975 93.1046 88.0924 99.2839 90.0609 104.358C91.0538 106.917 92.0162 109.199 92.7304 110.841C93.0875 111.662 93.3826 112.324 93.5884 112.78C93.6861 112.996 93.7637 113.167 93.8183 113.286L93.2043 113.286L92.9156 113.286L93.0921 113.514L93.2043 113.428L93.0921 113.514L93.0923 113.514L93.0929 113.515L93.0954 113.519L93.1053 113.531L93.144 113.582C93.1779 113.626 93.2278 113.691 93.2913 113.774C93.4182 113.94 93.5998 114.179 93.8182 114.469C94.2549 115.05 94.8386 115.836 95.4265 116.657C96.0147 117.479 96.606 118.335 97.0586 119.056C97.285 119.417 97.4754 119.742 97.6131 120.011C97.7537 120.286 97.8303 120.486 97.8446 120.602C97.8487 120.635 97.8405 120.688 97.8009 120.766C97.762 120.842 97.6981 120.932 97.6084 121.034C97.4289 121.238 97.1605 121.474 96.8273 121.731C96.1619 122.244 95.2583 122.824 94.3461 123.37C93.4347 123.916 92.5185 124.426 91.8298 124.8C91.4855 124.987 91.1983 125.14 90.9972 125.246C90.8967 125.299 90.8177 125.34 90.7639 125.368L90.7025 125.4L90.6928 125.405L90.6888 125.406L90.6409 125.421C90.5986 125.433 90.5357 125.452 90.4539 125.475C90.2905 125.522 90.0515 125.589 89.7504 125.669C89.1481 125.827 88.2976 126.035 87.3052 126.23C85.3217 126.621 82.7813 126.963 80.5253 126.779L80.3968 126.744C79.5791 126.522 78.5173 126.233 77.8281 125.444C77.4901 125.056 77.4818 124.536 77.574 124.084C77.6195 123.861 77.6881 123.663 77.7455 123.521C77.7741 123.45 77.7998 123.393 77.8181 123.354C77.8273 123.335 77.8346 123.32 77.8395 123.311L77.845 123.3L77.8462 123.297L77.8464 123.297L77.8465 123.297L77.8465 123.297L77.8884 123.218L77.8356 123.146L77.7213 123.23L77.8356 123.146L77.8355 123.146L77.8349 123.145L77.8326 123.142L77.8237 123.13L77.7889 123.083L77.6564 122.901C77.542 122.744 77.3783 122.519 77.181 122.246C76.7862 121.699 76.2571 120.962 75.7191 120.195C75.181 119.429 74.6348 118.635 74.2056 117.977C73.991 117.647 73.8064 117.353 73.667 117.113C73.5252 116.87 73.4371 116.694 73.4069 116.598L73.3934 116.554L73.3571 116.527C70.8401 114.623 69.1428 110.941 68.0115 107.122C66.8825 103.311 66.3264 99.3958 66.0718 97.0589C65.9498 95.9388 66.7713 94.9579 67.8989 94.8354L69.6305 94.6473C70.8019 94.52 71.7881 93.7141 72.1461 92.5915L73.3997 88.6608L73.4119 88.6608L73.7419 88.6615L74.937 88.6636C75.9356 88.6653 77.2904 88.6673 78.7151 88.6687C81.323 88.6711 84.1664 88.6713 85.4872 88.6631ZM94.1151 113.246L97.181 112.05L97.1817 112.049L97.1838 112.048L97.1957 112.043L97.2436 112.022C97.286 112.004 97.3488 111.976 97.4301 111.938C97.5928 111.862 97.8292 111.749 98.1231 111.597C98.711 111.293 99.5283 110.835 100.444 110.222C102.277 108.994 104.498 107.148 106.069 104.667L106.075 104.658L106.075 104.658C106.84 103.623 107.625 102.732 108.362 101.949C108.602 101.472 108.598 101.09 108.54 100.831C108.51 100.699 108.465 100.596 108.429 100.528C108.411 100.494 108.395 100.469 108.384 100.452C108.378 100.444 108.374 100.438 108.371 100.435L108.369 100.432L108.369 100.432L108.369 100.432L108.369 100.432L108.369 100.432L108.369 100.431L108.36 100.42L108.353 100.407L108.479 100.343L108.353 100.407L108.353 100.407L108.353 100.407L108.353 100.406L108.352 100.404L108.348 100.398L108.335 100.371C108.324 100.348 108.307 100.314 108.286 100.271C108.243 100.184 108.183 100.06 108.111 99.9088C107.97 99.6093 107.784 99.2029 107.606 98.7725C106.355 96.9261 105.33 95.6488 104.494 94.7775C104.195 94.465 103.739 94.0919 103.205 93.6805C102.893 93.4412 102.559 93.192 102.216 92.936L102.216 92.9356C101.969 92.7514 101.717 92.5636 101.466 92.3735C100.868 91.9211 100.277 91.4586 99.7783 91.0124C99.3065 90.5905 98.9081 90.1756 98.6648 89.7908L98.6133 89.7854L98.2687 89.7499C97.9706 89.7192 97.542 89.6754 97.0202 89.6229C95.9767 89.5178 94.5603 89.3779 93.0691 89.2381C90.3454 88.9829 87.3825 88.7296 85.9786 88.6899C86.6074 93.0747 88.6276 99.1841 90.5075 104.234C91.4604 106.794 92.376 109.079 93.0533 110.723C93.392 111.545 93.671 112.207 93.8654 112.664C93.9626 112.892 94.0386 113.069 94.0903 113.189L94.1151 113.246Z" fill="url(#paint8_linear_1213_2038)"/>
</g>
<mask id="mask2_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="65" y="88" width="44" height="40">
<path fill-rule="evenodd" clip-rule="evenodd" d="M73.1611 88.4757L73.1926 88.3767L73.2965 88.377L73.2962 88.5188C73.2965 88.377 73.2965 88.377 73.2966 88.377L73.2969 88.377L73.2983 88.377L73.3039 88.377L73.326 88.377L73.4124 88.3772L73.7425 88.3778L74.9374 88.38C75.936 88.3817 77.2907 88.3837 78.7153 88.385C81.565 88.3877 84.6929 88.3877 85.8102 88.377L85.9352 88.3758L85.9392 88.4052C87.3409 88.4411 90.3506 88.6985 93.0955 88.9557C94.5876 89.0955 96.0047 89.2356 97.0486 89.3407C97.5706 89.3932 97.9994 89.437 98.2977 89.4677L98.6426 89.5033L98.7329 89.5127L98.756 89.5151L98.7619 89.5157L98.7633 89.5159L98.7637 89.5159C98.7638 89.5159 98.7638 89.5159 98.7491 89.657L98.871 89.5846C99.0867 89.9477 99.4752 90.3608 99.9673 90.8009C100.457 91.2392 101.041 91.6959 101.637 92.1473C101.885 92.3348 102.136 92.5219 102.383 92.7062C102.728 92.9638 103.066 93.2159 103.378 93.4557C103.913 93.8672 104.383 94.2517 104.699 94.5812C105.551 95.4696 106.589 96.7644 107.849 98.6255L107.857 98.6375L107.863 98.6509C108.04 99.0805 108.226 99.4877 108.368 99.7877C108.439 99.9377 108.498 100.061 108.54 100.146C108.561 100.189 108.578 100.222 108.589 100.245L108.6 100.267C108.605 100.274 108.611 100.283 108.619 100.294C108.635 100.317 108.656 100.351 108.679 100.395C108.725 100.482 108.78 100.608 108.816 100.768C108.89 101.093 108.886 101.549 108.606 102.097L108.596 102.114L108.583 102.129C107.847 102.909 107.066 103.795 106.306 104.823C104.707 107.347 102.452 109.218 100.602 110.457C99.6757 111.078 98.849 111.541 98.2535 111.849C97.9556 112.003 97.7155 112.118 97.5495 112.195C97.4665 112.234 97.402 112.263 97.3581 112.282L97.3079 112.304L97.2948 112.309L97.2914 112.311L97.2905 112.311L97.2902 112.311C97.2901 112.311 97.2901 112.311 97.2351 112.181L97.2901 112.311L97.2867 112.313L97.2867 112.313L94.2285 113.506L94.2561 113.569L94.0664 113.569L93.9672 113.608L93.9492 113.569L93.4919 113.569L93.5167 113.602C93.644 113.769 93.826 114.008 94.0448 114.299C94.4824 114.88 95.0675 115.668 95.6572 116.492C96.2466 117.316 96.8418 118.177 97.2989 118.906C97.5273 119.27 97.7226 119.603 97.8656 119.882C98.0058 120.156 98.1051 120.396 98.1261 120.567C98.1397 120.678 98.1068 120.79 98.0537 120.894C98.0001 121 97.9199 121.109 97.8213 121.221C97.6242 121.445 97.3393 121.695 97.0005 121.956C96.3218 122.479 95.4068 123.066 94.4918 123.614C93.5758 124.162 92.6558 124.674 91.9651 125.049C91.6197 125.237 91.3314 125.39 91.1295 125.497C91.0285 125.55 90.9491 125.591 90.8949 125.62L90.8329 125.652L90.817 125.66L90.8129 125.662L90.8118 125.663L90.8116 125.663L90.8057 125.652L90.8115 125.663L90.8004 125.669L90.7884 125.672L90.7465 125.537C90.7884 125.672 90.7884 125.672 90.7883 125.672L90.7881 125.672L90.7872 125.673L90.784 125.674L90.7714 125.677L90.7222 125.692C90.6789 125.705 90.6152 125.724 90.5325 125.748C90.3671 125.796 90.126 125.863 89.8226 125.943C89.2158 126.103 88.3594 126.311 87.36 126.508C85.3637 126.902 82.787 127.251 80.4888 127.06L80.4757 127.059L80.463 127.056C80.4134 127.042 80.3621 127.028 80.3094 127.014C79.5041 126.796 78.3625 126.487 77.6144 125.63C77.1892 125.143 77.1975 124.51 77.2961 124.027C77.346 123.783 77.4206 123.568 77.4824 123.415C77.5106 123.345 77.5364 123.287 77.5562 123.245L77.4271 123.068C77.3126 122.911 77.1487 122.685 76.9511 122.412C76.5559 121.865 76.0259 121.126 75.487 120.358C74.9482 119.591 74.3998 118.794 73.968 118.132C73.7522 117.8 73.5647 117.502 73.4218 117.256C73.2965 117.041 73.1999 116.857 73.1515 116.727C70.576 114.757 68.8682 111.013 67.7396 107.203C66.6037 103.369 66.0453 99.4343 65.7898 97.0896C65.65 95.8064 66.5925 94.6921 67.8683 94.5534L69.5999 94.3653C70.6597 94.2502 71.552 93.521 71.8759 92.5053L73.1611 88.4757ZM85.4872 88.6631C85.9975 93.1046 88.0924 99.2839 90.0609 104.358C91.0538 106.917 92.0162 109.199 92.7304 110.841C93.0875 111.662 93.3826 112.324 93.5884 112.78C93.6861 112.996 93.7637 113.167 93.8183 113.286L93.2043 113.286L92.9156 113.286L93.0921 113.514L93.2043 113.428L93.0921 113.514L93.0923 113.514L93.0929 113.515L93.0954 113.519L93.1053 113.531L93.144 113.582C93.1779 113.626 93.2278 113.691 93.2913 113.774C93.4182 113.94 93.5998 114.179 93.8182 114.469C94.2549 115.05 94.8386 115.836 95.4265 116.657C96.0147 117.479 96.606 118.335 97.0586 119.056C97.285 119.417 97.4754 119.742 97.6131 120.011C97.7537 120.286 97.8303 120.486 97.8446 120.602C97.8487 120.635 97.8405 120.688 97.8009 120.766C97.762 120.842 97.6981 120.932 97.6084 121.034C97.4289 121.238 97.1605 121.474 96.8273 121.731C96.1619 122.244 95.2583 122.824 94.3461 123.37C93.4347 123.916 92.5185 124.426 91.8298 124.8C91.4855 124.987 91.1983 125.14 90.9972 125.246C90.8967 125.299 90.8177 125.34 90.7639 125.368L90.7025 125.4L90.6928 125.405L90.6888 125.406L90.6409 125.421C90.5986 125.433 90.5357 125.452 90.4539 125.475C90.2905 125.522 90.0515 125.589 89.7504 125.669C89.1481 125.827 88.2976 126.035 87.3052 126.23C85.3217 126.621 82.7813 126.963 80.5253 126.779L80.3968 126.744C79.5791 126.522 78.5173 126.233 77.8281 125.444C77.4901 125.056 77.4818 124.536 77.574 124.084C77.6195 123.861 77.6881 123.663 77.7455 123.521C77.7741 123.45 77.7998 123.393 77.8181 123.354C77.8273 123.335 77.8346 123.32 77.8395 123.311L77.845 123.3L77.8462 123.297L77.8464 123.297L77.8465 123.297L77.8465 123.297L77.8884 123.218L77.8356 123.146L77.7213 123.23L77.8356 123.146L77.8355 123.146L77.8349 123.145L77.8326 123.142L77.8237 123.13L77.7889 123.083L77.6564 122.901C77.542 122.744 77.3783 122.519 77.181 122.246C76.7862 121.699 76.2571 120.962 75.7191 120.195C75.181 119.429 74.6348 118.635 74.2056 117.977C73.991 117.647 73.8064 117.353 73.667 117.113C73.5252 116.87 73.4371 116.694 73.4069 116.598L73.3934 116.554L73.3571 116.527C70.8401 114.623 69.1428 110.941 68.0115 107.122C66.8825 103.311 66.3264 99.3958 66.0718 97.0589C65.9498 95.9388 66.7713 94.9579 67.8989 94.8354L69.6305 94.6473C70.8019 94.52 71.7881 93.7141 72.1461 92.5915L73.3997 88.6608L73.4119 88.6608L73.7419 88.6615L74.937 88.6636C75.9356 88.6653 77.2904 88.6673 78.7151 88.6687C81.323 88.6711 84.1664 88.6713 85.4872 88.6631ZM94.1151 113.246L97.181 112.05L97.1817 112.049L97.1838 112.048L97.1957 112.043L97.2436 112.022C97.286 112.004 97.3488 111.976 97.4301 111.938C97.5928 111.862 97.8292 111.749 98.1231 111.597C98.711 111.293 99.5283 110.835 100.444 110.222C102.277 108.994 104.498 107.148 106.069 104.667L106.075 104.658L106.075 104.658C106.84 103.623 107.625 102.732 108.362 101.949C108.602 101.472 108.598 101.09 108.54 100.831C108.51 100.699 108.465 100.596 108.429 100.528C108.411 100.494 108.395 100.469 108.384 100.452C108.378 100.444 108.374 100.438 108.371 100.435L108.369 100.432L108.369 100.432L108.369 100.432L108.369 100.432L108.369 100.432L108.369 100.431L108.36 100.42L108.353 100.407L108.479 100.343L108.353 100.407L108.353 100.407L108.353 100.407L108.353 100.406L108.352 100.404L108.348 100.398L108.335 100.371C108.324 100.348 108.307 100.314 108.286 100.271C108.243 100.184 108.183 100.06 108.111 99.9088C107.97 99.6093 107.784 99.2029 107.606 98.7725C106.355 96.9261 105.33 95.6488 104.494 94.7775C104.195 94.465 103.739 94.0919 103.205 93.6805C102.893 93.4412 102.559 93.192 102.216 92.936L102.216 92.9356C101.969 92.7514 101.717 92.5636 101.466 92.3735C100.868 91.9211 100.277 91.4586 99.7783 91.0124C99.3065 90.5905 98.9081 90.1756 98.6648 89.7908L98.6133 89.7854L98.2687 89.7499C97.9706 89.7192 97.542 89.6754 97.0202 89.6229C95.9767 89.5178 94.5603 89.3779 93.0691 89.2381C90.3454 88.9829 87.3825 88.7296 85.9786 88.6899C86.6074 93.0747 88.6276 99.1841 90.5075 104.234C91.4604 106.794 92.376 109.079 93.0533 110.723C93.392 111.545 93.671 112.207 93.8654 112.664C93.9626 112.892 94.0386 113.069 94.0903 113.189L94.1151 113.246Z" fill="white"/>
</mask>
<g mask="url(#mask2_1213_2038)">
<g filter="url(#filter11_f_1213_2038)">
<ellipse cx="75.8484" cy="114.545" rx="6.53669" ry="6.55623" transform="rotate(-7 75.8484 114.545)" fill="white"/>
</g>
<g filter="url(#filter12_f_1213_2038)">
<ellipse cx="106.412" cy="101.327" rx="4.26306" ry="4.2758" transform="rotate(-7 106.412 101.327)" fill="#CECECA"/>
</g>
<g filter="url(#filter13_f_1213_2038)">
<ellipse cx="94.8325" cy="116.944" rx="4.26306" ry="4.2758" transform="rotate(-7 94.8325 116.944)" fill="#CECECA"/>
</g>
<g opacity="0.563804" filter="url(#filter14_f_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M72.9757 116.467C72.9757 116.467 77.0244 117.371 80.5001 117.453C76.1052 118.065 73.5181 117.541 73.5181 117.541L72.9757 116.467Z" fill="#DCDAD4"/>
</g>
<g filter="url(#filter15_f_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M76.7227 122.463C76.7227 122.463 84.4099 122.92 91.088 122.609C82.5627 123.728 77.6443 123.49 77.6443 123.49L76.7227 122.463Z" fill="#DCDAD4"/>
</g>
</g>
<g filter="url(#filter16_f_1213_2038)">
<ellipse cx="83.3867" cy="100.343" rx="11.9366" ry="11.9722" fill="white"/>
</g>
<g filter="url(#filter17_if_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M79.514 110.787C79.514 110.787 84.7782 115.808 93.179 113.9C101.065 111.599 103.618 103.875 103.618 103.875L79.514 110.787Z" fill="url(#paint9_linear_1213_2038)"/>
</g>
<g filter="url(#filter18_diii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M76.1652 99.6051C76.085 98.9767 76.5163 98.4372 77.133 98.2919C79.2743 97.7873 84.2845 96.6616 87.8857 96.2831C91.4823 95.9051 96.444 95.9812 98.5707 96.0376C99.1894 96.054 99.7133 96.4751 99.7822 97.0901C100.07 99.6572 99.811 106.315 89.8896 108.201C78.4997 109.08 76.5085 102.292 76.1652 99.6051Z" fill="url(#paint10_linear_1213_2038)"/>
</g>
<g filter="url(#filter19_d_1213_2038)">
<path d="M77.5139 100.256C77.4332 99.7153 77.8091 99.2362 78.3671 99.1091C80.3484 98.6579 84.7372 97.7065 87.9021 97.3739C91.0628 97.0417 95.4035 97.0743 97.3679 97.1111C97.926 97.1216 98.3859 97.4977 98.4355 98.0298C98.5403 99.1557 98.4805 101.054 97.3284 102.868C96.1796 104.676 93.9298 106.424 89.6088 107.228C84.641 107.617 81.7105 106.445 79.9787 104.905C78.2433 103.362 77.6889 101.429 77.5139 100.256Z" stroke="#E4E4E4" stroke-width="0.283625"/>
</g>
<path d="M77.5139 100.256C77.5006 100.167 77.4996 100.08 77.5095 99.9966L77.3686 99.9799C77.3926 99.7782 77.472 99.5926 77.5928 99.4355L77.7052 99.522C77.8093 99.3866 77.9496 99.274 78.115 99.1959L78.0545 99.0677C78.1426 99.0261 78.2368 98.9934 78.3356 98.9709L78.3671 99.1091C78.4548 99.0892 78.5471 99.0682 78.6439 99.0464L78.6127 98.9081C78.7855 98.8691 78.9724 98.8273 79.1716 98.7832L79.2022 98.9217C79.3787 98.8826 79.5648 98.8418 79.7594 98.7996L79.7293 98.661C79.9086 98.622 80.0952 98.5819 80.2878 98.5409L80.3173 98.6796C80.4984 98.641 80.685 98.6017 80.8761 98.5618L80.8471 98.423C81.0298 98.3849 81.2167 98.3463 81.4071 98.3074L81.4355 98.4464C81.6189 98.409 81.8055 98.3713 81.9948 98.3336L81.967 98.1945C82.1516 98.1577 82.3386 98.1209 82.5276 98.0842L82.5546 98.2234C82.7398 98.1874 82.9269 98.1516 83.1152 98.116L83.0889 97.9767C83.2752 97.9415 83.4627 97.9066 83.6509 97.8722L83.6764 98.0117C83.863 97.9776 84.0504 97.9439 84.2379 97.9109L84.2133 97.7712C84.401 97.7381 84.5889 97.7056 84.7764 97.6739L84.8001 97.8137C84.9881 97.7819 85.1758 97.7509 85.3626 97.7208L85.3401 97.5808C85.5294 97.5503 85.7178 97.5208 85.9049 97.4924L85.9261 97.6326C86.1159 97.6038 86.3041 97.5761 86.4902 97.5498L86.4704 97.4094C86.6615 97.3824 86.8504 97.3568 87.0366 97.3329L87.0547 97.4735C87.2462 97.4488 87.4347 97.4259 87.6195 97.4049L87.6035 97.264C87.6991 97.2531 87.7937 97.2427 87.8873 97.2329L87.9021 97.3739C87.9932 97.3643 88.0854 97.355 88.1784 97.3461L88.1648 97.2049C88.3469 97.1873 88.5324 97.1709 88.7207 97.1555L88.7322 97.2968C88.9148 97.2819 89.1 97.268 89.2873 97.255L89.2775 97.1135C89.4615 97.1007 89.6476 97.0889 89.835 97.0779L89.8433 97.2194C90.0275 97.2086 90.2132 97.1986 90.3996 97.1894L90.3926 97.0478C90.5779 97.0386 90.764 97.0302 90.9504 97.0225L90.9563 97.1642C91.1418 97.1566 91.3277 97.1496 91.5133 97.1433L91.5085 97.0016C91.6949 96.9952 91.8811 96.9896 92.0666 96.9845L92.0705 97.1263C92.2573 97.1212 92.4433 97.1167 92.6281 97.1127L92.6251 96.971C92.8127 96.967 92.999 96.9636 93.1835 96.9607L93.1857 97.1025C93.3735 97.0995 93.5595 97.0971 93.743 97.0952L93.7415 96.9534C93.9305 96.9514 94.1169 96.9499 94.3 96.9489L94.3008 97.0907C94.4906 97.0896 94.6768 97.089 94.8589 97.0889L94.8588 96.9471C95.0498 96.9469 95.2363 96.9471 95.4173 96.9478L95.4168 97.0896C95.6092 97.0902 95.7954 97.0913 95.9745 97.0927L95.9756 96.9509C96.1703 96.9524 96.3566 96.9543 96.5332 96.9564L96.5315 97.0983C96.7308 97.1007 96.9178 97.1034 97.0906 97.1062L97.093 96.9644C97.1901 96.966 97.2827 96.9677 97.3705 96.9693L97.3679 97.1111C97.457 97.1128 97.5438 97.1239 97.627 97.1436L97.6597 97.0056C97.8543 97.0516 98.0321 97.1398 98.1785 97.262L98.0876 97.3709C98.2186 97.4802 98.32 97.6196 98.3797 97.7816L98.5127 97.7326C98.5455 97.8215 98.5673 97.9165 98.5767 98.0167L98.4355 98.0298C98.4437 98.1186 98.451 98.2123 98.4568 98.3104L98.5983 98.3021C98.6088 98.4803 98.6145 98.6728 98.6127 98.8773L98.4709 98.876C98.4693 99.0558 98.4618 99.2449 98.4466 99.4416L98.588 99.4525C98.5738 99.6371 98.5528 99.8283 98.5236 100.025L98.3834 100.004C98.3564 100.185 98.3224 100.371 98.28 100.56L98.4184 100.591C98.3775 100.774 98.329 100.96 98.2718 101.148L98.1361 101.106C98.0825 101.282 98.0212 101.46 97.9512 101.639L98.0832 101.691C98.0147 101.866 97.938 102.042 97.8524 102.219L97.7248 102.157C97.6444 102.323 97.5561 102.488 97.459 102.654L97.5813 102.726C97.4861 102.888 97.3827 103.05 97.2705 103.21L97.1542 103.129C97.0487 103.28 96.9351 103.431 96.813 103.58L96.9227 103.669C96.804 103.814 96.6773 103.958 96.5423 104.1L96.4396 104.002C96.3126 104.135 96.178 104.267 96.0354 104.397L96.1309 104.502C95.9922 104.628 95.8461 104.753 95.6923 104.875L95.604 104.765C95.4595 104.879 95.3079 104.993 95.149 105.104L95.2302 105.22C95.0764 105.327 94.9158 105.433 94.7483 105.536L94.6739 105.415C94.5165 105.512 94.3526 105.607 94.1821 105.699L94.2498 105.824C94.0852 105.913 93.9146 106.001 93.7377 106.086L93.6763 105.958C93.5095 106.038 93.337 106.116 93.1587 106.192L93.2143 106.323C93.0421 106.396 92.8645 106.468 92.6814 106.537L92.6313 106.404C92.4576 106.47 92.279 106.533 92.0951 106.595L92.1401 106.729C91.9619 106.789 91.779 106.847 91.5912 106.902L91.551 106.766C91.3717 106.819 91.1878 106.87 90.9993 106.919L91.035 107.057C90.8524 107.104 90.6655 107.15 90.4742 107.194L90.4426 107.055C90.2615 107.097 90.0763 107.136 89.887 107.174L89.9147 107.313C89.8199 107.332 89.7241 107.35 89.6273 107.368L89.6162 107.227C89.5206 107.234 89.4258 107.241 89.3317 107.248L89.3413 107.389C89.1521 107.402 88.9659 107.413 88.7827 107.421L88.7762 107.279C88.5853 107.288 88.3976 107.294 88.213 107.298L88.216 107.44C88.0256 107.444 87.8386 107.445 87.6548 107.444L87.6555 107.303C87.4663 107.302 87.2807 107.298 87.0986 107.292L87.094 107.434C86.9017 107.428 86.7133 107.419 86.5285 107.407L86.5374 107.266C86.3479 107.254 86.1625 107.239 85.9809 107.222L85.9675 107.363C85.7776 107.345 85.5919 107.324 85.4103 107.3L85.4287 107.16C85.2408 107.135 85.0574 107.107 84.8783 107.077L84.8546 107.217C84.6649 107.185 84.48 107.15 84.2997 107.111L84.3291 106.973C84.1432 106.933 83.9624 106.891 83.7865 106.845L83.751 106.982C83.5651 106.934 83.3847 106.883 83.2094 106.828L83.2515 106.693C83.0705 106.637 82.8952 106.577 82.7254 106.515L82.6764 106.648C82.4963 106.581 82.3222 106.511 82.1539 106.439L82.2103 106.308C82.0368 106.233 81.8696 106.155 81.7085 106.073L81.6445 106.2C81.4733 106.113 81.3089 106.023 81.1509 105.93L81.2229 105.808C81.0604 105.712 80.905 105.613 80.7563 105.512L80.6763 105.629C80.5182 105.521 80.3675 105.409 80.2238 105.296L80.3119 105.184C80.1639 105.067 80.0235 104.947 79.8904 104.825L79.7945 104.93C79.6532 104.8 79.5199 104.668 79.3941 104.534L79.4975 104.437C79.3691 104.3 79.2488 104.161 79.1358 104.021L79.0254 104.11C78.9049 103.96 78.7927 103.809 78.6883 103.658L78.8051 103.577C78.6984 103.422 78.6 103.267 78.5093 103.111L78.3868 103.183C78.2897 103.017 78.2011 102.851 78.1204 102.686L78.2477 102.624C78.1644 102.454 78.0896 102.286 78.0224 102.12L77.891 102.173C77.8179 101.993 77.7536 101.816 77.6972 101.644L77.832 101.6C77.7719 101.417 77.7209 101.239 77.6775 101.068L77.5401 101.103C77.4909 100.909 77.4514 100.725 77.4197 100.553L77.5592 100.528C77.5418 100.433 77.5268 100.343 77.5139 100.256Z" stroke="white" stroke-width="0.283625" stroke-dasharray="0.57"/>
<g filter="url(#filter20_d_1213_2038)">
<g filter="url(#filter21_ii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.0689 78.8263C75.3651 77.6829 76.472 76.9437 77.6416 77.1081C78.9334 77.2896 79.8227 78.4988 79.611 79.7861L77.7411 91.1557C77.4903 92.6806 76.0681 93.7264 74.5377 93.5113C72.8549 93.2748 71.7534 91.6255 72.1795 89.9805L75.0689 78.8263Z" fill="url(#paint11_linear_1213_2038)"/>
</g>
<path d="M75.0558 80.1668L75.1933 79.6068L75.331 79.6406L75.3998 79.3606L75.2621 79.3268C75.2841 79.237 75.3124 79.1502 75.3464 79.0668L75.4777 79.1203C75.5424 78.9616 75.6294 78.8162 75.7342 78.6872L75.6241 78.5978C75.7398 78.4555 75.8754 78.3316 76.026 78.2296L76.1055 78.3471C76.2446 78.2529 76.3975 78.179 76.5592 78.1286L76.5171 77.9932C76.6889 77.9397 76.87 77.9107 77.0557 77.9095L77.0566 78.0513C77.1397 78.0508 77.2239 78.0563 77.3086 78.0682L77.3283 77.9278C77.4278 77.9417 77.5243 77.9633 77.6173 77.9918L77.5757 78.1274C77.7537 78.182 77.9179 78.2641 78.0643 78.3681L78.1465 78.2525C78.3085 78.3677 78.4503 78.5077 78.5671 78.6661L78.453 78.7503C78.561 78.8968 78.6459 79.0602 78.7032 79.2346L78.838 79.1902C78.8988 79.375 78.9311 79.5712 78.9306 79.7729L78.7888 79.7725C78.7886 79.8623 78.7813 79.9534 78.7664 80.0452L78.9064 80.0678L78.8604 80.3524L78.7204 80.3298L78.6283 80.8991L78.7683 80.9217L78.6762 81.491L78.5362 81.4683L78.4441 82.0376L78.5841 82.0602L78.492 82.6295L78.352 82.6068L78.2599 83.1761L78.3999 83.1987L78.3078 83.768L78.1678 83.7453L78.0757 84.3146L78.2157 84.3373L78.1236 84.9065L77.9836 84.8839L77.8915 85.4531L78.0315 85.4758L77.9394 86.045L77.7994 86.0224L77.7074 86.5916L77.8473 86.6143L77.7553 87.1835L77.6153 87.1609L77.5232 87.7302L77.6632 87.7528L77.5711 88.3221L77.4311 88.2994L77.339 88.8687L77.479 88.8913L77.3869 89.4606L77.2469 89.4379L77.1548 90.0072L77.2948 90.0298L77.2027 90.5991L77.0627 90.5765L77.0166 90.8611C77.0016 90.9543 76.9807 91.0451 76.9546 91.1332L77.0905 91.1736C77.0331 91.3671 76.9515 91.5487 76.8497 91.7154L76.7287 91.6415C76.631 91.8015 76.5133 91.947 76.3797 92.075L76.4778 92.1774C76.3345 92.3147 76.174 92.433 76.0005 92.5294L75.9316 92.4054C75.7699 92.4952 75.5961 92.5646 75.4144 92.6107L75.4493 92.7481C75.2599 92.7961 75.0623 92.8204 74.8604 92.8181L74.8621 92.6763C74.7702 92.6752 74.6772 92.6683 74.5837 92.6551L74.564 92.7956C74.4719 92.7826 74.382 92.7644 74.2945 92.7414L74.3306 92.6043C74.1609 92.5596 74.0008 92.4954 73.8522 92.4146L73.7845 92.5392C73.6218 92.4508 73.472 92.3437 73.3375 92.2213L73.4329 92.1164C73.3052 92.0003 73.1921 91.8694 73.0957 91.7271L72.9783 91.8066C72.8757 91.6551 72.791 91.4915 72.7267 91.3192L72.8595 91.2695C72.7998 91.1096 72.7587 90.9419 72.7383 90.7694L72.5975 90.7861C72.5763 90.607 72.5762 90.4231 72.5991 90.2375L72.7399 90.2549C72.7503 90.1706 72.7658 90.0858 72.7867 90.001L72.6489 89.9671L72.7177 89.6871L72.8554 89.7209L72.993 89.1609L72.8552 89.1271L72.9928 88.5671L73.1305 88.6009L73.268 88.0409L73.1303 88.0071L73.2678 87.447L73.4056 87.4809L73.5431 86.9208L73.4054 86.887L73.5429 86.327L73.6806 86.3608L73.8182 85.8008L73.6804 85.767L73.818 85.207L73.9557 85.2408L74.0932 84.6808L73.9555 84.6469L74.093 84.0869L74.2308 84.1208L74.3683 83.5607L74.2306 83.5269L74.3681 82.9669L74.5058 83.0007L74.6434 82.4407L74.5056 82.4069L74.6432 81.8469L74.7809 81.8807L74.9184 81.3207L74.7807 81.2868L74.9182 80.7268L75.056 80.7606L75.1935 80.2006L75.0558 80.1668Z" stroke="url(#paint12_linear_1213_2038)" stroke-width="0.283625" stroke-dasharray="0.57"/>
<g filter="url(#filter22_d_1213_2038)">
<g filter="url(#filter23_i_1213_2038)">
<ellipse cx="74.9513" cy="90.1151" rx="1.70522" ry="1.71032" transform="rotate(8 74.9513 90.1151)" fill="url(#paint13_linear_1213_2038)"/>
</g>
<mask id="mask3_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="73" y="88" width="4" height="4">
<ellipse cx="74.9513" cy="90.1151" rx="1.70522" ry="1.71032" transform="rotate(8 74.9513 90.1151)" fill="white"/>
</mask>
<g mask="url(#mask3_1213_2038)">
<g filter="url(#filter24_f_1213_2038)">
<ellipse cx="74.5687" cy="89.7736" rx="0.994714" ry="0.997687" transform="rotate(8 74.5687 89.7736)" fill="#FFF1BC"/>
</g>
</g>
</g>
</g>
<g filter="url(#filter25_d_1213_2038)">
<g filter="url(#filter26_ii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M96.5365 79.9271C96.1809 78.8007 95.0369 78.1204 93.8775 78.3458C92.5969 78.5947 91.7722 79.8488 92.051 81.1232L94.5133 92.3793C94.8436 93.889 96.3186 94.859 97.8356 94.5641C99.5037 94.2399 100.517 92.5352 100.006 90.9147L96.5365 79.9271Z" fill="url(#paint14_linear_1213_2038)"/>
</g>
<path d="M96.6193 81.2648L96.4527 80.7127L96.3169 80.7537L96.2336 80.4777L96.3694 80.4367C96.3426 80.3482 96.3098 80.263 96.2715 80.1815L96.1432 80.2418C96.0703 80.0867 95.9758 79.9461 95.8644 79.8228L95.9697 79.7277C95.8467 79.5916 95.7047 79.475 95.5491 79.3811L95.4758 79.5025C95.3319 79.4157 95.1754 79.3499 95.0113 79.3081L95.0463 79.1707C94.8719 79.1262 94.6895 79.1068 94.5041 79.1153L94.5106 79.2569C94.4275 79.2607 94.3437 79.2706 94.2598 79.287L94.2327 79.1478C94.1341 79.1669 94.0389 79.1935 93.9475 79.2269L93.9961 79.3601C93.8212 79.4239 93.6615 79.5145 93.5208 79.626L93.4327 79.5149C93.2769 79.6384 93.1426 79.7856 93.0343 79.95L93.1526 80.028C93.0525 80.1799 92.9763 80.3476 92.9281 80.5247L92.7912 80.4875C92.7402 80.6752 92.7181 80.8728 92.7292 81.0742L92.8708 81.0664C92.8757 81.1561 92.8878 81.2467 92.9074 81.3375L92.7688 81.3675L92.8297 81.6493L92.9683 81.6194L93.09 82.183L92.9514 82.213L93.0732 82.7766L93.2118 82.7467L93.3336 83.3103L93.1949 83.3403L93.3167 83.9039L93.4553 83.874L93.5771 84.4376L93.4385 84.4676L93.5602 85.0313L93.6988 85.0013L93.8206 85.565L93.682 85.5949L93.8038 86.1586L93.9424 86.1286L94.0641 86.6923L93.9255 86.7222L94.0473 87.2859L94.1859 87.2559L94.3076 87.8196L94.169 87.8495L94.2908 88.4132L94.4294 88.3833L94.5512 88.9469L94.4126 88.9769L94.5343 89.5405L94.6729 89.5106L94.7947 90.0742L94.6561 90.1042L94.7778 90.6678L94.9165 90.6379L95.0382 91.2016L94.8996 91.2315L95.0214 91.7952L95.16 91.7652L95.2209 92.047C95.2408 92.1393 95.2663 92.2289 95.2971 92.3156L95.1634 92.363C95.2309 92.5533 95.3219 92.7303 95.4323 92.8915L95.5493 92.8113C95.6552 92.966 95.7803 93.1051 95.9204 93.2259L95.8278 93.3333C95.9781 93.4629 96.1446 93.5727 96.3229 93.6599L96.3852 93.5324C96.5515 93.6137 96.7286 93.6739 96.9125 93.7104L96.8849 93.8495C97.0765 93.8875 97.2751 93.9014 97.4765 93.8885L97.4675 93.747C97.5592 93.7411 97.6516 93.7293 97.7443 93.7113L97.7714 93.8505C97.8626 93.8328 97.9515 93.8099 98.0377 93.7823L97.9945 93.6473C98.1616 93.5938 98.3181 93.5213 98.4622 93.4329L98.5364 93.5537C98.6942 93.4569 98.8382 93.3421 98.9662 93.2129L98.8654 93.1131C98.9868 92.9904 99.093 92.8538 99.1817 92.7067L99.3031 92.78C99.3977 92.6233 99.4737 92.4554 99.5289 92.28L99.3937 92.2374C99.445 92.0746 99.4772 91.9049 99.4885 91.7316L99.63 91.7408C99.6418 91.5609 99.6323 91.3772 99.5997 91.1931L99.46 91.2178C99.4452 91.1342 99.4253 91.0504 99.4 90.9667L99.5358 90.9257L99.4525 90.6497L99.3167 90.6907L99.15 90.1386L99.2858 90.0976L99.1192 89.5456L98.9834 89.5866L98.8167 89.0345L98.9525 88.9935L98.7858 88.4415L98.6501 88.4825L98.4834 87.9304L98.6192 87.8894L98.4525 87.3374L98.3168 87.3784L98.1501 86.8263L98.2859 86.7853L98.1192 86.2333L97.9835 86.2743L97.8168 85.7222L97.9526 85.6812L97.7859 85.1292L97.6502 85.1701L97.4835 84.6181L97.6193 84.5771L97.4526 84.0251L97.3168 84.066L97.1502 83.514L97.286 83.473L97.1193 82.921L96.9835 82.9619L96.8169 82.4099L96.9526 82.3689L96.786 81.8168L96.6502 81.8578L96.4836 81.3058L96.6193 81.2648Z" stroke="url(#paint15_linear_1213_2038)" stroke-width="0.283625" stroke-dasharray="0.57"/>
<g filter="url(#filter27_d_1213_2038)">
<g filter="url(#filter28_i_1213_2038)">
<ellipse cx="1.70522" cy="1.71032" rx="1.70522" ry="1.71032" transform="matrix(-0.981627 0.190809 0.190809 0.981627 98.5917 89.1902)" fill="url(#paint16_linear_1213_2038)"/>
</g>
<mask id="mask4_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="95" y="89" width="4" height="4">
<ellipse cx="1.70522" cy="1.71032" rx="1.70522" ry="1.71032" transform="matrix(-0.981627 0.190809 0.190809 0.981627 98.5917 89.1902)" fill="white"/>
</mask>
<g mask="url(#mask4_1213_2038)">
<g filter="url(#filter29_f_1213_2038)">
<ellipse cx="0.994714" cy="0.997687" rx="0.994714" ry="0.997687" transform="matrix(-0.981627 0.190809 0.190809 0.981627 98.3943 89.6638)" fill="#FFF1BC"/>
</g>
</g>
</g>
</g>
<g opacity="0.674878" filter="url(#filter30_f_1213_2038)">
<ellipse cx="88.2252" cy="83.0693" rx="14.4278" ry="5.05908" fill="#FF9913"/>
</g>
<g filter="url(#filter31_ii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M106.396 29.2371C106.396 29.2371 128.643 17.6634 131.967 24.4437C135.292 31.224 119.345 58.5234 119.345 58.5234L106.396 29.2371Z" fill="url(#paint17_linear_1213_2038)"/>
</g>
<g filter="url(#filter32_di_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M106.893 34.055C106.893 34.055 124.261 25.0194 126.857 30.3128C129.452 35.6062 117.003 56.9189 117.003 56.9189L106.893 34.055Z" fill="url(#paint18_radial_1213_2038)"/>
</g>
<g filter="url(#filter33_ii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M78.2559 22.2209C78.2559 22.2209 64.0464 1.55766 57.9279 5.98357C51.8094 10.4095 53.0729 41.9997 53.0729 41.9997L78.2559 22.2209Z" fill="url(#paint19_linear_1213_2038)"/>
</g>
<g filter="url(#filter34_di_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.555 26.242C75.555 26.242 64.4617 10.1102 59.6849 13.5655C54.9082 17.0208 55.8946 41.6833 55.8946 41.6833L75.555 26.242Z" fill="url(#paint20_radial_1213_2038)"/>
</g>
<g filter="url(#filter35_ii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M47.5163 46.8322C47.5163 46.8322 59.5971 8.74058 93.4988 16.2413C132.938 26.0337 122.084 65.3062 122.084 65.3062C122.084 65.3062 125.197 95.3434 79.3807 85.0768C37.8268 75.2833 47.5163 46.8322 47.5163 46.8322Z" fill="url(#paint21_linear_1213_2038)"/>
</g>
<mask id="mask5_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="46" y="15" width="78" height="73">
<path fill-rule="evenodd" clip-rule="evenodd" d="M47.5163 46.8322C47.5163 46.8322 59.5971 8.74058 93.4988 16.2413C132.938 26.0337 122.084 65.3062 122.084 65.3062C122.084 65.3062 125.197 95.3434 79.3807 85.0768C37.8268 75.2833 47.5163 46.8322 47.5163 46.8322Z" fill="white"/>
</mask>
<g mask="url(#mask5_1213_2038)">
<g filter="url(#filter36_f_1213_2038)">
<ellipse cx="88.1462" cy="41.2036" rx="19.7738" ry="19.7949" transform="rotate(14 88.1462 41.2036)" fill="#FFF172"/>
</g>
<g filter="url(#filter37_ddii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M68.3751 51.9022C68.3751 51.9022 86.6476 48.3671 113.167 63.6347C134.838 74.5768 129.586 42.0968 96.6041 33.6436C74.191 28.546 65.2024 38.4911 61.1349 42.6281C54.6941 51.4472 68.3751 51.9022 68.3751 51.9022Z" fill="url(#paint22_linear_1213_2038)"/>
</g>
<g filter="url(#filter38_ddiii_1213_2038)">
<ellipse cx="118.004" cy="70.1272" rx="5.98954" ry="5.99595" transform="rotate(14 118.004 70.1272)" fill="url(#paint23_linear_1213_2038)"/>
</g>
</g>
<g opacity="0.377893" filter="url(#filter39_f_1213_2038)">
<ellipse cx="95.33" cy="49.1418" rx="15.6542" ry="15.671" transform="rotate(14 95.33 49.1418)" fill="#C59B23"/>
</g>
<g filter="url(#filter40_diii_1213_2038)">
<ellipse cx="69.4123" cy="52.7481" rx="8.23908" ry="8.24789" transform="rotate(14 69.4123 52.7481)" fill="url(#paint24_linear_1213_2038)"/>
</g>
<g filter="url(#filter41_ddii_1213_2038)">
<ellipse cx="92.4528" cy="66.9247" rx="17.051" ry="14.9899" transform="rotate(14 92.4528 66.9247)" fill="url(#paint25_linear_1213_2038)"/>
</g>
<g opacity="0.546139" filter="url(#filter42_f_1213_2038)">
<ellipse cx="91.2441" cy="61.3216" rx="9.92018" ry="9.93079" transform="rotate(14 91.2441 61.3216)" fill="#FFF372"/>
</g>
<g filter="url(#filter43_d_1213_2038)">
<g filter="url(#filter44_i_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M91.3214 53.2972C91.1844 52.6912 91.2987 52.0472 91.7811 51.6557C92.6176 50.9766 94.3876 50.1643 97.7122 50.9932C100.813 51.7664 102.149 53.1903 102.721 54.2C103.111 54.8878 102.862 55.6957 102.303 56.2546C100.939 57.6185 98.1559 59.9855 95.7644 59.3892C92.8857 58.7407 91.7193 55.0576 91.3214 53.2972Z" fill="url(#paint26_linear_1213_2038)"/>
</g>
<mask id="mask6_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="91" y="50" width="12" height="10">
<path fill-rule="evenodd" clip-rule="evenodd" d="M91.3214 53.2972C91.1844 52.6912 91.2987 52.0472 91.7811 51.6557C92.6176 50.9766 94.3876 50.1643 97.7122 50.9932C100.813 51.7664 102.149 53.1903 102.721 54.2C103.111 54.8878 102.862 55.6957 102.303 56.2546C100.939 57.6185 98.1559 59.9855 95.7644 59.3892C92.8857 58.7407 91.7193 55.0576 91.3214 53.2972Z" fill="white"/>
</mask>
<g mask="url(#mask6_1213_2038)">
<g opacity="0.865593" filter="url(#filter45_f_1213_2038)">
<ellipse cx="96.6189" cy="53.3354" rx="3.37272" ry="2.06111" transform="rotate(14 96.6189 53.3354)" fill="#BC9324"/>
</g>
</g>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M98.4418 19.3711C98.2413 19.1873 98.3557 18.8652 98.6249 18.8261C100.401 18.5678 105.305 17.6451 107.241 15.1336C110.14 10.5373 112.789 21.6703 105.838 22.6887C102.333 22.5921 99.5182 20.3577 98.4418 19.3711Z" fill="url(#paint27_linear_1213_2038)"/>
<g filter="url(#filter46_di_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M118.485 39.62C118.485 39.62 113.774 38.0779 115.31 36.4268C116.845 34.7756 118.752 38.734 118.861 39.2373C118.914 39.6141 118.485 39.62 118.485 39.62Z" fill="url(#paint28_linear_1213_2038)"/>
</g>
<g filter="url(#filter47_di_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8341 28.1735C80.8341 28.1735 85.7173 29.0235 85.1369 26.8449C84.5565 24.6662 81.0144 27.266 80.6823 27.6595C80.4577 27.9669 80.8341 28.1735 80.8341 28.1735Z" fill="url(#paint29_linear_1213_2038)"/>
</g>
<g filter="url(#filter48_d_1213_2038)">
<ellipse cx="6.18332" cy="7.86174" rx="6.18332" ry="7.86174" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 91.6438 35.9866)" fill="url(#paint30_linear_1213_2038)"/>
<g filter="url(#filter49_i_1213_2038)">
<ellipse cx="4.68434" cy="5.6212" rx="4.68434" ry="5.6212" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 88.5255 38.8645)" fill="url(#paint31_linear_1213_2038)"/>
</g>
<mask id="mask7_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="77" y="37" width="11" height="12">
<ellipse cx="4.68434" cy="5.6212" rx="4.68434" ry="5.6212" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 88.5255 38.8645)" fill="white"/>
</mask>
<g mask="url(#mask7_1213_2038)">
<ellipse cx="2.51711" cy="2.28265" rx="2.51711" ry="2.28265" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 88.406 39.7441)" fill="white"/>
<ellipse cx="0.719175" cy="0.652185" rx="0.719175" ry="0.652185" transform="matrix(-0.97437 -0.224951 -0.224951 0.97437 82.0891 43.0232)" fill="white"/>
</g>
</g>
<g filter="url(#filter50_d_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M117.595 52.5143C113.42 48.7895 109.392 47.7148 105.609 49.406C105.336 49.5283 105.172 49.7942 105.166 50.075C105.149 50.1142 105.135 50.1555 105.124 50.1985C105.026 50.6007 105.273 51.006 105.675 51.1038C109.715 52.0857 112.304 53.7898 113.506 56.1901C113.692 56.5602 114.142 56.71 114.512 56.5246C114.882 56.3392 115.032 55.8889 114.847 55.5188C113.647 53.1233 111.36 51.3637 108.023 50.2243C110.687 49.7791 113.527 50.8944 116.597 53.6328C116.906 53.9084 117.379 53.8814 117.655 53.5725C117.93 53.2637 117.903 52.7899 117.595 52.5143Z" fill="#A47603"/>
</g>
<g filter="url(#filter51_di_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M87.4588 64.5033C87.4588 64.5033 89.0549 59.7014 95.7054 62.1441C100.953 64.4551 99.5341 67.1736 99.5341 67.1736C99.5341 67.1736 92.735 79.9104 87.6759 78.4193C83.9493 76.6524 87.4588 64.5033 87.4588 64.5033Z" fill="url(#paint32_linear_1213_2038)"/>
</g>
<mask id="mask8_1213_2038" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="85" y="61" width="15" height="18">
<path fill-rule="evenodd" clip-rule="evenodd" d="M87.4588 64.5033C87.4588 64.5033 89.0549 59.7014 95.7054 62.1441C100.953 64.4551 99.5341 67.1736 99.5341 67.1736C99.5341 67.1736 92.735 79.9104 87.6759 78.4193C83.9493 76.6524 87.4588 64.5033 87.4588 64.5033Z" fill="white"/>
</mask>
<g mask="url(#mask8_1213_2038)">
<g filter="url(#filter52_i_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M88.2309 58.8635C88.2309 58.8635 89.4306 63.4341 94.3397 65.2934C99.3587 66.4968 103.367 63.2036 103.367 63.2036L88.2309 58.8635Z" fill="white"/>
</g>
<g filter="url(#filter53_di_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M82.8926 75.7324C82.8926 75.7324 85.8801 73.7841 89.6506 75.6201C93.4211 77.4562 93.6916 80.7833 93.6916 80.7833C93.6916 80.7833 86.8855 79.8596 86.3814 78.6599C84.8353 77.7196 82.8926 75.7324 82.8926 75.7324Z" fill="url(#paint33_linear_1213_2038)"/>
</g>
</g>
<g opacity="0.479592" filter="url(#filter54_f_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M61.0185 67.5265L69.5543 67.9974C70.5209 68.0508 71.0985 66.9388 70.499 66.1787L55.0968 46.6511H47.5167L61.0185 67.5265Z" fill="#E69415"/>
</g>
<g filter="url(#filter55_dii_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M66.7095 91.1943C66.7095 91.1943 41.886 83.719 41.0701 61.2201C41.5193 52.4444 48.0756 49.465 52.8945 52.4505C60.0097 54.5523 61.5448 68.177 64.3664 73.5124C68.3297 81.5108 70.7765 83.6058 70.7765 83.6058C70.7765 83.6058 73.2237 87.4764 69.8514 90.9051C68.8112 91.589 66.7095 91.1943 66.7095 91.1943Z" fill="url(#paint34_linear_1213_2038)"/>
</g>
<g opacity="0.600254" filter="url(#filter56_f_1213_2038)">
<ellipse cx="51.0875" cy="60.4556" rx="9.93079" ry="4.68434" fill="#E59015"/>
</g>
<g filter="url(#filter57_i_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M43.4233 46.6511L42.5375 53.6659L61.1934 61.9821L63.6485 62.9109L61.7963 60.6352L43.4233 46.6511Z" fill="url(#paint35_linear_1213_2038)"/>
</g>
<g filter="url(#filter58_i_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.2767 58.9881C28.2767 58.9881 32.4912 51.6947 34.7053 50.7743C45.3265 52.4583 63.2716 62.5039 63.2716 62.5039L43.5089 47.0488C43.2203 46.823 43.3938 46.3598 43.7598 46.3794L53.3549 46.892C53.4684 46.898 53.5729 46.9552 53.6392 47.0475L66.2306 64.5717C66.4296 64.8486 66.1971 65.2292 65.8627 65.1622C60.4624 64.0803 33.1167 58.7053 28.2767 59.8137C28.1862 59.4396 28.2767 58.9881 28.2767 58.9881Z" fill="url(#paint36_linear_1213_2038)"/>
</g>
<g opacity="0.713536" filter="url(#filter59_f_1213_2038)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M88.45 127.614C88.45 127.614 92.8532 128.847 95.4176 124.12C97.982 119.392 97.2082 119.602 97.2082 119.602L88.45 127.614Z" fill="url(#paint37_linear_1213_2038)"/>
</g>
<defs>
<filter id="filter0_f_1213_2038" x="10.636" y="-1.80032" width="109.523" height="115.378" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.46657" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter1_f_1213_2038" x="-73.9396" y="36.7595" width="128.327" height="155.905" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.6554" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter2_f_1213_2038" x="-35.3884" y="-23.7219" width="81.6381" height="94.2603" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.6554" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter3_i_1213_2038" x="98.5239" y="72.5437" width="23.0382" height="20.0183" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.97137 0 0 0 0 0.814862 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter4_ii_1213_2038" x="73.2719" y="110.926" width="28.4177" height="23.1977" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.12424"/>
<feGaussianBlur stdDeviation="1.12424"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.737255 0 0 0 0 0.152941 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.749494"/>
<feGaussianBlur stdDeviation="1.31161"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.564706 0 0 0 0 0.0470588 0 0 0 0.729268 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1213_2038" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter5_iii_1213_2038" x="94.9808" y="97.395" width="15.9373" height="16.1479" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.980392 0 0 0 0 0.588235 0 0 0 0 0.0666667 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3.74747" dy="-0.374747"/>
<feGaussianBlur stdDeviation="0.749494"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.564706 0 0 0 0 0.0666667 0 0 0 0.581312 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1213_2038" result="effect2_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="0.749494"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1213_2038" result="effect3_innerShadow_1213_2038"/>
</filter>
<filter id="filter6_i_1213_2038" x="67.3398" y="76.2886" width="38.5387" height="40.351" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.62323"/>
<feGaussianBlur stdDeviation="2.8106"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.560784 0 0 0 0 0.0431373 0 0 0 0.398798 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter7_f_1213_2038" x="64.6508" y="70.712" width="39.1946" height="36.532" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.09992" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter8_f_1213_2038" x="69.3523" y="67.6947" width="39.8479" height="20.7567" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.911094" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter9_f_1213_2038" x="64.8291" y="86.0691" width="41.6482" height="12.4525" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.770971" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter10_iii_1213_2038" x="64.2771" y="88.001" width="44.5797" height="39.8623" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.187373"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.815313 0 0 0 0 0.815313 0 0 0 0 0.815313 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.49899" dy="-0.374747"/>
<feGaussianBlur stdDeviation="3.18535"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.836039 0 0 0 0 0.822878 0 0 0 0 0.752079 0 0 0 0.775747 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1213_2038" result="effect2_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1213_2038" result="effect3_innerShadow_1213_2038"/>
</filter>
<filter id="filter11_f_1213_2038" x="61.6011" y="100.279" width="28.4947" height="28.5327" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.85486" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter12_f_1213_2038" x="94.439" y="89.3413" width="23.9468" height="23.9717" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.85486" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter13_f_1213_2038" x="82.8591" y="104.958" width="23.9468" height="23.9717" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.85486" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter14_f_1213_2038" x="72.2047" y="115.696" width="9.06636" height="2.82515" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.385486" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter15_f_1213_2038" x="75.9517" y="121.692" width="15.9072" height="2.59102" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.385486" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter16_f_1213_2038" x="63.7404" y="80.6611" width="39.2925" height="39.364" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.85486" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter17_if_1213_2038" x="78.743" y="102.751" width="25.6456" height="12.353" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.49899"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feGaussianBlur stdDeviation="0.385486" result="effect2_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter18_diii_1213_2038" x="75.0314" y="95.616" width="25.9395" height="14.5363" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.858824 0 0 0 0 0.854902 0 0 0 0 0.819608 0 0 0 0.805743 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.374747" dy="-0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1213_2038" result="effect3_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.374747" dy="-0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.862467 0 0 0 0 0.862467 0 0 0 0 0.862467 0 0 0 0.436214 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_1213_2038" result="effect4_innerShadow_1213_2038"/>
</filter>
<filter id="filter19_d_1213_2038" x="76.9863" y="96.947" width="22.0015" height="11.2471" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.187373"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.845666 0 0 0 0 0.845666 0 0 0 0 0.845666 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
</filter>
<filter id="filter20_d_1213_2038" x="71.3381" y="76.7105" width="9.05355" height="17.9531" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.241201 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
</filter>
<filter id="filter21_ii_1213_2038" x="71.7128" y="76.7105" width="8.30406" height="16.8288" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.374747" dy="-0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.323654 0 0 0 0 0.431539 0 0 0 0.686191 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.578362 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1213_2038" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter22_d_1213_2038" x="72.871" y="88.4045" width="4.16057" height="4.17039" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.187373"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0 0 0 0 0 0 0 0 0 0.37791 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
</filter>
<filter id="filter23_i_1213_2038" x="72.4963" y="87.2803" width="4.16057" height="4.54514" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.749494" dy="-1.12424"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.588235 0 0 0 0 0.0588235 0 0 0 0.912775 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter24_f_1213_2038" x="70.4899" y="85.692" width="8.15758" height="8.16338" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.54194" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter25_d_1213_2038" x="91.2473" y="77.928" width="9.64114" height="17.8132" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.241201 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
</filter>
<filter id="filter26_ii_1213_2038" x="91.622" y="77.928" width="8.89164" height="16.689" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.374747" dy="-0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.323654 0 0 0 0 0.431539 0 0 0 0.686191 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.578362 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1213_2038" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter27_d_1213_2038" x="95.1636" y="89.4839" width="4.16093" height="4.17064" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.187373"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0 0 0 0 0 0 0 0 0 0.37791 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
</filter>
<filter id="filter28_i_1213_2038" x="94.7889" y="88.3596" width="4.16093" height="4.54538" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.749494" dy="-1.12424"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.588235 0 0 0 0 0.0588235 0 0 0 0.912775 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter29_f_1213_2038" x="93.5294" y="86.7513" width="8.15776" height="8.16338" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.54194" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter30_f_1213_2038" x="71.9752" y="76.1881" width="32.4999" height="13.7625" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.911094" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter31_ii_1213_2038" x="105.646" y="22.3135" width="26.777" height="38.4584" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.749494" dy="2.24848"/>
<feGaussianBlur stdDeviation="1.31161"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.945098 0 0 0 0 0.701961 0 0 0 0.897131 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.952591 0 0 0 0 0.68394 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1213_2038" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter32_di_1213_2038" x="103.895" y="24.9024" width="26.3157" height="34.265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.749494"/>
<feGaussianBlur stdDeviation="1.49899"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.904639 0 0 0 0 0.347983 0 0 0 0 0 0 0 0 0.719446 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.749494" dy="2.24848"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.529412 0 0 0 0 0.00392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter33_ii_1213_2038" x="52.2154" y="5.36182" width="26.0405" height="38.8864" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.749494" dy="2.24848"/>
<feGaussianBlur stdDeviation="1.31161"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.996078 0 0 0 0 0.945098 0 0 0 0 0.701961 0 0 0 0.897131 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.952591 0 0 0 0 0.68394 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1213_2038" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter34_di_1213_2038" x="52.8123" y="9.33261" width="25.7406" height="34.5992" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.749494"/>
<feGaussianBlur stdDeviation="1.49899"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.904639 0 0 0 0 0.347983 0 0 0 0 0 0 0 0 0.719446 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.749494" dy="2.24848"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.529412 0 0 0 0 0.00392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter35_ii_1213_2038" x="46.5362" y="10.3915" width="76.9155" height="78.3226" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.49899"/>
<feGaussianBlur stdDeviation="1.49899"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980598 0 0 0 0 0.782269 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4.87171"/>
<feGaussianBlur stdDeviation="2.8106"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.627451 0 0 0 0 0.0392157 0 0 0 0.641781 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1213_2038" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter36_f_1213_2038" x="60.2979" y="13.3366" width="55.6967" height="55.734" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.03433" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter37_ddii_1213_2038" x="57.1625" y="29.5997" width="71.2833" height="39.2609" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="1.12424"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.568627 0 0 0 0 0.0509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.961984 0 0 0 0 0.524494 0 0 0 0 0.00423549 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1213_2038" result="effect2_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="1.12424"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.771443 0 0 0 0 0.0546035 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.37272"/>
<feGaussianBlur stdDeviation="1.31161"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.384314 0 0 0 0 0.25098 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_1213_2038" result="effect4_innerShadow_1213_2038"/>
</filter>
<filter id="filter38_ddiii_1213_2038" x="109.389" y="62.2564" width="18.7281" height="18.7396" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.374747" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1213_2038"/>
<feOffset dx="0.749494" dy="1.49899"/>
<feGaussianBlur stdDeviation="1.49899"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576471 0 0 0 0 0.0470588 0 0 0 0.656998 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.374747" dy="0.374747"/>
<feGaussianBlur stdDeviation="0.187373"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.560784 0 0 0 0 0.0431373 0 0 0 0.451622 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1213_2038" result="effect2_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2.62323" dy="2.24848"/>
<feGaussianBlur stdDeviation="1.68636"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.935122 0 0 0 0 0.669713 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.749494" dy="-1.49899"/>
<feGaussianBlur stdDeviation="1.68636"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.545098 0 0 0 0 0.0392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_1213_2038" result="effect4_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.749494" dy="1.87373"/>
<feGaussianBlur stdDeviation="0.749494"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.950004 0 0 0 0 0.723552 0 0 0 0.500597 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_1213_2038" result="effect5_innerShadow_1213_2038"/>
</filter>
<filter id="filter39_f_1213_2038" x="71.6024" y="25.3994" width="47.4552" height="47.4847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.03433" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter40_diii_1213_2038" x="57.798" y="41.8756" width="23.2285" height="23.244" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.374747" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1213_2038"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="1.49899"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576471 0 0 0 0 0.0470588 0 0 0 0.656998 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.24848"/>
<feGaussianBlur stdDeviation="1.68636"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.935122 0 0 0 0 0.669713 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.49899"/>
<feGaussianBlur stdDeviation="1.12424"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.545098 0 0 0 0 0.0392157 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1213_2038" result="effect3_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.87373"/>
<feGaussianBlur stdDeviation="0.749494"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.950004 0 0 0 0 0.723552 0 0 0 0.500597 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_1213_2038" result="effect4_innerShadow_1213_2038"/>
</filter>
<filter id="filter41_ddii_1213_2038" x="71.015" y="49.5543" width="42.8756" height="39.2381" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.24848"/>
<feGaussianBlur stdDeviation="2.24848"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.576132 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="0.749494"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.584314 0 0 0 0 0.054902 0 0 0 0.825473 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1213_2038" result="effect2_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="0.374747" operator="erode" in="SourceAlpha" result="effect3_innerShadow_1213_2038"/>
<feOffset dy="1.87373"/>
<feGaussianBlur stdDeviation="1.31161"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.943556 0 0 0 0 0.725479 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.99798"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.638854 0 0 0 0 0.0197456 0 0 0 0.11004 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_1213_2038" result="effect4_innerShadow_1213_2038"/>
</filter>
<filter id="filter42_f_1213_2038" x="75.0453" y="45.1135" width="32.3976" height="32.4162" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.13781" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter43_d_1213_2038" x="90.518" y="50.6423" width="13.1417" height="10.3403" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.769066 0 0 0 0 0.621384 0 0 0 0 0.00850142 0 0 0 0.345285 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
</filter>
<filter id="filter44_i_1213_2038" x="91.2675" y="49.8928" width="11.6427" height="9.5908" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.749494"/>
<feGaussianBlur stdDeviation="1.49899"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.297765 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter45_f_1213_2038" x="89.722" y="47.5887" width="13.7937" height="11.4932" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.79303" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter46_di_1213_2038" x="114.253" y="35.2771" width="6.11158" height="5.84174" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.374747" dy="0.374747"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.411765 0 0 0 0 0.0235294 0 0 0 0.695526 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.749494"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.262548 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter47_di_1213_2038" x="79.8613" y="25.2354" width="6.82246" height="4.60053" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.374747" dy="0.374747"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85098 0 0 0 0 0.411765 0 0 0 0 0.0235294 0 0 0 0.695526 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.749494"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.262548 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter48_d_1213_2038" x="75.3213" y="34.0942" width="15.5593" height="18.5717" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.749494" dy="1.12424"/>
<feGaussianBlur stdDeviation="0.749494"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.568627 0 0 0 0 0.0509804 0 0 0 0.551709 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
</filter>
<filter id="filter49_i_1213_2038" x="77.9594" y="36.9597" width="9.84954" height="11.9067" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.374747" dy="-0.749494"/>
<feGaussianBlur stdDeviation="0.749494"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.232984 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter50_d_1213_2038" x="103.979" y="47.8777" width="14.9908" height="10.2253" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.374747"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.175182 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
</filter>
<filter id="filter51_di_1213_2038" x="84.8372" y="59.2029" width="15.9961" height="25.3338" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.12424"/>
<feGaussianBlur stdDeviation="0.56212"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.929412 0 0 0 0 0.447059 0 0 0 0 0.113725 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.12424" dy="5.99595"/>
<feGaussianBlur stdDeviation="3.18535"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter52_i_1213_2038" x="88.2309" y="58.8635" width="15.1357" height="8.56807" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.99798"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.21264 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter53_di_1213_2038" x="78.7704" y="69.2111" width="19.0435" height="14.1954" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.49899"/>
<feGaussianBlur stdDeviation="2.06111"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.190816 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.49899" dy="-1.87373"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.0969255 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
</filter>
<filter id="filter54_f_1213_2038" x="45.4793" y="44.6138" width="27.301" height="25.4228" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.01867" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter55_dii_1213_2038" x="39.9459" y="46.8302" width="33.5622" height="45.2269" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.374747" dy="-2.99798"/>
<feGaussianBlur stdDeviation="0.749494"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.894118 0 0 0 0 0.572549 0 0 0 0 0.0823529 0 0 0 0.500458 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1213_2038"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1213_2038" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.12424" dy="-2.24848"/>
<feGaussianBlur stdDeviation="2.06111"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.584314 0 0 0 0 0.0862745 0 0 0 0.820524 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1213_2038"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.749494"/>
<feGaussianBlur stdDeviation="1.31161"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1213_2038" result="effect3_innerShadow_1213_2038"/>
</filter>
<filter id="filter56_f_1213_2038" x="37.0821" y="51.6966" width="28.0109" height="17.518" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.03733" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<filter id="filter57_i_1213_2038" x="42.5375" y="45.1521" width="21.111" height="17.7588" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.49899"/>
<feGaussianBlur stdDeviation="0.936867"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.151848 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter58_i_1213_2038" x="28.2365" y="46.0039" width="38.0676" height="19.166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.374747"/>
<feGaussianBlur stdDeviation="0.374747"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.796078 0 0 0 0 0.760784 0 0 0 0 0.607843 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1213_2038"/>
</filter>
<filter id="filter59_f_1213_2038" x="87.4313" y="118.582" width="10.9051" height="10.196" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.509334" result="effect1_foregroundBlur_1213_2038"/>
</filter>
<linearGradient id="paint0_linear_1213_2038" x1="5.98466" y1="78.1969" x2="55.1106" y2="97.1596" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E460"/>
<stop offset="1" stop-color="#FFC028"/>
</linearGradient>
<linearGradient id="paint1_linear_1213_2038" x1="6.73138" y1="51.3039" x2="45.9511" y2="42.0187" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#8F600C"/>
</linearGradient>
<linearGradient id="paint2_linear_1213_2038" x1="22.7244" y1="94.5182" x2="45.0012" y2="65.6281" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#3D2700"/>
</linearGradient>
<linearGradient id="paint3_linear_1213_2038" x1="49.5461" y1="108.463" x2="57.0558" y2="85.0101" gradientUnits="userSpaceOnUse">
<stop stop-color="#946E13"/>
<stop offset="1" stop-color="#3D2700"/>
</linearGradient>
<linearGradient id="paint4_linear_1213_2038" x1="117.711" y1="74.4587" x2="98.5312" y2="80.7649" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD56A"/>
<stop offset="1" stop-color="#FF9E26"/>
</linearGradient>
<linearGradient id="paint5_linear_1213_2038" x1="111.008" y1="127.529" x2="101.765" y2="106.51" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF2A7"/>
<stop offset="1" stop-color="#FDA61C"/>
</linearGradient>
<linearGradient id="paint6_linear_1213_2038" x1="112.04" y1="108.034" x2="109.924" y2="91.5547" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF4D"/>
<stop offset="1" stop-color="#FF9312"/>
</linearGradient>
<linearGradient id="paint7_linear_1213_2038" x1="71.431" y1="80.7598" x2="68.9857" y2="114.481" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E669"/>
<stop offset="1" stop-color="#FFD73B"/>
</linearGradient>
<linearGradient id="paint8_linear_1213_2038" x1="67.8376" y1="100.614" x2="81.2966" y2="129.357" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#CFCAC1"/>
</linearGradient>
<linearGradient id="paint9_linear_1213_2038" x1="81.0335" y1="111.119" x2="82.6932" y2="116.907" gradientUnits="userSpaceOnUse">
<stop stop-color="#EFEEEA" stop-opacity="0.01"/>
<stop offset="1" stop-color="#D0CEC6"/>
</linearGradient>
<linearGradient id="paint10_linear_1213_2038" x1="76.9834" y1="105.23" x2="82.0805" y2="114.856" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E4E3E1"/>
</linearGradient>
<linearGradient id="paint11_linear_1213_2038" x1="74.1341" y1="94.9963" x2="80.5151" y2="94.1174" gradientUnits="userSpaceOnUse">
<stop stop-color="#3AA1E9"/>
<stop offset="1" stop-color="#3693F7"/>
</linearGradient>
<linearGradient id="paint12_linear_1213_2038" x1="73.1852" y1="96.2252" x2="78.475" y2="96.1667" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EBEBEB"/>
</linearGradient>
<linearGradient id="paint13_linear_1213_2038" x1="73.3329" y1="90.9859" x2="75.0744" y2="92.691" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE364"/>
<stop offset="1" stop-color="#FEBD27"/>
</linearGradient>
<linearGradient id="paint14_linear_1213_2038" x1="98.3164" y1="96.026" x2="91.8981" y2="95.4822" gradientUnits="userSpaceOnUse">
<stop stop-color="#3AA1E9"/>
<stop offset="1" stop-color="#3693F7"/>
</linearGradient>
<linearGradient id="paint15_linear_1213_2038" x1="99.3278" y1="97.2033" x2="94.0421" y2="97.4217" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EBEBEB"/>
</linearGradient>
<linearGradient id="paint16_linear_1213_2038" x1="0.0868314" y1="2.58111" x2="1.82832" y2="4.28624" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE364"/>
<stop offset="1" stop-color="#FEBD27"/>
</linearGradient>
<linearGradient id="paint17_linear_1213_2038" x1="122.796" y1="21.1707" x2="113.771" y2="34.6704" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF38"/>
<stop offset="1" stop-color="#FFB703"/>
</linearGradient>
<radialGradient id="paint18_radial_1213_2038" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(122.815 33.2655) rotate(98.4481) scale(15.6588 6.3592)">
<stop stop-color="#FFC102"/>
<stop offset="1" stop-color="#FF8D01"/>
</radialGradient>
<linearGradient id="paint19_linear_1213_2038" x1="67.5622" y1="7.3993" x2="69.1931" y2="23.5558" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF38"/>
<stop offset="1" stop-color="#FFB703"/>
</linearGradient>
<radialGradient id="paint20_radial_1213_2038" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(61.8676 18.0701) rotate(109.552) scale(15.6588 6.3592)">
<stop stop-color="#FFC102"/>
<stop offset="1" stop-color="#FF8D01"/>
</radialGradient>
<linearGradient id="paint21_linear_1213_2038" x1="60.0775" y1="15.1182" x2="48.7261" y2="78.7064" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9E669"/>
<stop offset="1" stop-color="#FFD73B"/>
</linearGradient>
<linearGradient id="paint22_linear_1213_2038" x1="64.3555" y1="23.4203" x2="57.4486" y2="50.066" gradientUnits="userSpaceOnUse">
<stop stop-color="#AF8800"/>
<stop offset="1" stop-color="#604000"/>
</linearGradient>
<linearGradient id="paint23_linear_1213_2038" x1="115.332" y1="67.9863" x2="114.216" y2="75.5653" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD048"/>
<stop offset="1" stop-color="#FF8C03"/>
</linearGradient>
<linearGradient id="paint24_linear_1213_2038" x1="65.737" y1="49.8031" x2="64.2017" y2="60.2286" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD048"/>
<stop offset="1" stop-color="#FF8C03"/>
</linearGradient>
<linearGradient id="paint25_linear_1213_2038" x1="75.9975" y1="57.3116" x2="77.5215" y2="82.8071" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD435"/>
<stop offset="1" stop-color="#FFA718"/>
</linearGradient>
<linearGradient id="paint26_linear_1213_2038" x1="92.6511" y1="51.5501" x2="90.9928" y2="58.2009" gradientUnits="userSpaceOnUse">
<stop stop-color="#815E00"/>
<stop offset="1" stop-color="#4A3000"/>
</linearGradient>
<linearGradient id="paint27_linear_1213_2038" x1="101.738" y1="18.1272" x2="101.695" y2="22.1709" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEF5B5"/>
<stop offset="1" stop-color="#FBEB76"/>
</linearGradient>
<linearGradient id="paint28_linear_1213_2038" x1="114.747" y1="35.8263" x2="114.25" y2="38.7571" gradientUnits="userSpaceOnUse">
<stop stop-color="#B18104"/>
<stop offset="1" stop-color="#5E3B00"/>
</linearGradient>
<linearGradient id="paint29_linear_1213_2038" x1="85.9156" y1="26.5788" x2="84.9784" y2="29.3999" gradientUnits="userSpaceOnUse">
<stop stop-color="#B18104"/>
<stop offset="1" stop-color="#5E3B00"/>
</linearGradient>
<linearGradient id="paint30_linear_1213_2038" x1="0" y1="0" x2="0" y2="15.7235" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DAD5C8"/>
</linearGradient>
<linearGradient id="paint31_linear_1213_2038" x1="6.65378" y1="-1.36177" x2="-1.26447" y2="3.74844" gradientUnits="userSpaceOnUse">
<stop stop-color="#A37800"/>
<stop offset="1" stop-color="#6A4300"/>
</linearGradient>
<linearGradient id="paint32_linear_1213_2038" x1="87.9161" y1="68.0992" x2="90.0972" y2="79.9808" gradientUnits="userSpaceOnUse">
<stop stop-color="#B34E51"/>
<stop offset="1" stop-color="#7C2628"/>
</linearGradient>
<linearGradient id="paint33_linear_1213_2038" x1="85.0668" y1="74.8789" x2="84.2613" y2="78.2354" gradientUnits="userSpaceOnUse">
<stop stop-color="#F68080"/>
<stop offset="1" stop-color="#E53636"/>
</linearGradient>
<linearGradient id="paint34_linear_1213_2038" x1="36.5875" y1="73.8639" x2="65.9121" y2="98.3863" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDE97C"/>
<stop offset="1" stop-color="#FBE167"/>
</linearGradient>
<linearGradient id="paint35_linear_1213_2038" x1="46.6176" y1="52.0185" x2="45.886" y2="58.1272" gradientUnits="userSpaceOnUse">
<stop stop-color="#D5CEB4"/>
<stop offset="1" stop-color="#AAA07D"/>
</linearGradient>
<linearGradient id="paint36_linear_1213_2038" x1="22.2006" y1="61.559" x2="38.6751" y2="83.355" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEFEFF"/>
<stop offset="1" stop-color="#F2EDDD"/>
</linearGradient>
<linearGradient id="paint37_linear_1213_2038" x1="90.1557" y1="126.291" x2="91.8811" y2="127.888" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCF4E" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FEA226"/>
</linearGradient>
</defs>
</svg>
