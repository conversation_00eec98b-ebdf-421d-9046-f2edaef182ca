((typeof self !== 'undefined' ? self : this)["webpackJsonp"] = (typeof self !== 'undefined' ? self : this)["webpackJsonp"] || []).push([[5],{

/***/ "./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=script&lang=js&":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/cache-loader/dist/cjs.js??ref--13-0!./node_modules/babel-loader/lib!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/ai/index.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _utils_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/index.js */ "./src/utils/index.js");
/* harmony import */ var _assets_video_v_2_mp4__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../assets/video/v-2.mp4 */ "./src/assets/video/v-2.mp4");
/* harmony import */ var _assets_video_v_2_mp4__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_assets_video_v_2_mp4__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _assets_images_ai_6_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../assets/images/ai/6.png */ "./src/assets/images/ai/6.png");
/* harmony import */ var _assets_images_ai_6_png__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_assets_images_ai_6_png__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _components_videoJs_vue__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/videoJs.vue */ "./src/components/videoJs.vue");




/* harmony default export */ __webpack_exports__["default"] = ({
  components: {
    videoJs: _components_videoJs_vue__WEBPACK_IMPORTED_MODULE_3__["default"]
  },
  inject: ['setFooterState', 'setNavFontState', 'setNavState'],
  data() {
    return {
      V2: (_assets_video_v_2_mp4__WEBPACK_IMPORTED_MODULE_1___default()),
      coverImg: (_assets_images_ai_6_png__WEBPACK_IMPORTED_MODULE_2___default()),
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false,
        loop: false,
        poster: _assets_images_ai_6_png__WEBPACK_IMPORTED_MODULE_2___default.a,
        sources: [{
          src: _assets_video_v_2_mp4__WEBPACK_IMPORTED_MODULE_1___default.a,
          type: 'video/mp4'
        }]
      },
      hoverItem: '',
      clickItem: '传统文化类',
      tagsList: {
        '传统文化类': ['中华优秀传统文化', '神话知多少', '故事里的古代生活史', '体验非遗多彩', '探索非遗匠心', '传承非遗智慧', '向世界讲中国故事', '二十四节气诗画课', '趣味国学——花修', '趣味国学——汉服', '博物馆里的中国故事', '国宝博览课', '大中国奇妙游'],
        '综合艺术类': ['综合艺术类', '玩转节奏', '硬笔书法', '艺术大师的画笔', '戏剧梦工厂', '乐器百科', '漫画小世界', '口风琴入门', '国画趣味多', '音乐素养', '趣味美育', '表演与表达', '音乐基础'],
        '学科素养类': ['自然拼读', '英文脱口秀', '经典名著导读', '英文写作', '基础数学', '新概念英语', '英文绘本阅读', '经典名著赏析', '中文绘本阅读', '读懂古诗词中的浩然正气', '古人怎么说再见', '诗词中的三餐四季', '诗人的家国情怀', '诗人和 Ta 的家人们', '诗人眼中的那个 Ta', '诗人眼中的战场', '寻找古诗中的智慧', '英文歌谣', '英文演讲'],
        '科学百科类': ['探秘人工智能', '小小科学家', '加油科学课', '世界百科', '物质与材料的秘密', '我与时间', '遇见恐龙', '动物探秘', '人体奥秘', '电影鉴赏', '给少年看的演化论', '哇！建筑'],
        '民族发展类': ['趣味表达', '卫生与健康'],
        '思维训练类': ['玩转思维—八大思维图示 ', '青少年辩论 ', '围棋对弈', '中国象棋入门 ', '玩转魔方 ', '数字思维', '我是大侦探：推理思维训练营'],
        '探究实践类': ['神奇的传动装置', '简单机械', '折纸奥秘', '体适能'],
        '安全健康类': ['趣味心理效应与实验', '识情绪，善社交', '自我探索之相信自己', '社交力养成记', '画知我心', '青少年心理健康', '入学啦！初入小学', '校园安全', '中国精神', '热血诗人的峥嵘岁月', '小学生法律常识课', '多多安全课']
      }
    };
  },
  mounted() {
    this.setFooterState(true);
    this.setNavState(1);
    this.setNavFontState(0);
    window.addEventListener('scroll', this.handleNavbarState);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleNavbarState);
    this.setNavState(1);
  },
  methods: {
    handleNavbarState() {
      const documentTop = Object(_utils_index_js__WEBPACK_IMPORTED_MODULE_0__["getDocumentTop"])();
      if (documentTop < 10) {
        this.setNavState(1);
      } else {
        this.setNavState(0);
      }
    },
    tagHover(key) {
      this.hoverItem = key;
      this.clickItem = key;
    }
  }
});

/***/ }),

/***/ "./node_modules/cache-loader/dist/cjs.js?{\"cacheDirectory\":\"node_modules/.cache/vue-loader\",\"cacheIdentifier\":\"1df0c205-vue-loader-template\"}!./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=template&id=60c32676&scoped=true&":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/cache-loader/dist/cjs.js?{"cacheDirectory":"node_modules/.cache/vue-loader","cacheIdentifier":"1df0c205-vue-loader-template"}!./node_modules/cache-loader/dist/cjs.js??ref--13-0!./node_modules/babel-loader/lib!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/ai/index.vue?vue&type=template&id=60c32676&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w"
  }, [_vm._m(0), _c("div", {
    staticClass: "computer content"
  }, [_c("div", {
    staticClass: "pt100 pb100 flex justify-center animate__animated animate__fadeInLeft"
  }, [_c("div", {
    staticClass: "w1000 tl"
  }, [_c("svg-icon", {
    staticClass: "w510 h90",
    attrs: {
      "icon-class": "ai-text-01"
    }
  })], 1)]), _c("div", {
    staticClass: "flex justify-center mb60"
  }, [_c("div", {
    staticClass: "w1000 flex justify-between"
  }, [_c("div", {
    staticClass: "flex flex-col w450"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeInUp",
      expression: "'animate__animated animate__fadeInUp'"
    }],
    staticClass: "f35 fb color333 w tl mb30"
  }, [_vm._v(" 双翼驱动，构建高质量课堂 ")]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeInUp animate__delay-300",
      expression: "'animate__animated animate__fadeInUp animate__delay-300'"
    }],
    staticClass: "color-text f20 w tl lh30 mb60"
  }, [_vm._v(" 线上素质领域专家、专业教师沉浸式主导授课。"), _c("br"), _vm._v(" 线下带班教师补位助教，轻松组织管理班级。 ")]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-600",
      expression: "'animate__animated animate__fadeIn animate__delay-600'"
    }],
    staticClass: "f35 fb color333 w tl mb10"
  }, [_vm._v(" 低门槛落地，轻课减负减压 ")]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-700",
      expression: "'animate__animated animate__fadeIn animate__delay-700'"
    }],
    staticClass: "color-text f20 w tl lh30"
  }, [_vm._v(" 减轻教师备课负担，"), _c("br"), _vm._v(" 降低学校开课门槛。 ")])]), _c("div", {
    staticClass: "w450 flex flex-col"
  }, [_c("img", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-500",
      expression: "'animate__animated animate__fadeIn animate__delay-500'"
    }],
    staticClass: "w450 mb80",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/1.png */ "./src/assets/images/ai/1.png")
    }
  })])])]), _c("div", {
    staticClass: "ai-bg-box flex justify-center align-center"
  }, [_c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-between"
  }, [_c("div", {
    staticClass: "flex flex-col tl"
  }, [_c("svg-icon", {
    staticClass: "w270 h120",
    attrs: {
      "icon-class": "ai-text-02"
    }
  }), _c("div", {
    staticClass: "color-white f20 mt20"
  }, [_vm._v(" AI赋能教学全环节 ")])], 1), _c("div", {
    staticClass: "w660"
  }, [_c("div", {
    staticClass: "flex align-center justify-between mb30"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn",
      expression: "'animate__animated animate__fadeIn'"
    }],
    staticClass: "flex flex-col align-center"
  }, [_c("img", {
    attrs: {
      width: "320",
      src: __webpack_require__(/*! ../../assets/images/ai/2.png */ "./src/assets/images/ai/2.png")
    }
  }), _c("div", {
    staticClass: "w320 flex align-center pl10"
  }, [_c("svg-icon", {
    staticClass: "w40 h60 mr20",
    attrs: {
      "icon-class": "ai-num-1"
    }
  }), _c("span", {
    staticClass: "color-white f16"
  }, [_vm._v("智能排课、选课、备课")])], 1)]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-300",
      expression: "'animate__animated animate__fadeIn animate__delay-300'"
    }],
    staticClass: "flex flex-col align-center"
  }, [_c("img", {
    attrs: {
      width: "320",
      src: __webpack_require__(/*! ../../assets/images/ai/3.png */ "./src/assets/images/ai/3.png")
    }
  }), _c("div", {
    staticClass: "w320 flex align-center pl10"
  }, [_c("svg-icon", {
    staticClass: "w40 h60 mr20",
    attrs: {
      "icon-class": "ai-num-2"
    }
  }), _c("span", {
    staticClass: "color-white f16"
  }, [_vm._v("AI教师多感官情景授课")])], 1)])]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-500",
      expression: "'animate__animated animate__fadeIn animate__delay-500'"
    }],
    staticClass: "flex align-center justify-between"
  }, [_c("div", {
    staticClass: "flex flex-col align-center"
  }, [_c("img", {
    attrs: {
      width: "320",
      src: __webpack_require__(/*! ../../assets/images/ai/4.png */ "./src/assets/images/ai/4.png")
    }
  }), _c("div", {
    staticClass: "w320 flex align-center pl10"
  }, [_c("svg-icon", {
    staticClass: "w40 h60 mr20",
    attrs: {
      "icon-class": "ai-num-3"
    }
  }), _c("span", {
    staticClass: "color-white f16"
  }, [_vm._v("多元化考评练习激励机制")])], 1)]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-700",
      expression: "'animate__animated animate__fadeIn animate__delay-700'"
    }],
    staticClass: "flex flex-col align-center"
  }, [_c("img", {
    attrs: {
      width: "320",
      src: __webpack_require__(/*! ../../assets/images/ai/5.png */ "./src/assets/images/ai/5.png")
    }
  }), _c("div", {
    staticClass: "w320 flex align-center pl10"
  }, [_c("svg-icon", {
    staticClass: "w40 h60 mr20",
    attrs: {
      "icon-class": "ai-num-4"
    }
  }), _c("span", {
    staticClass: "color-white f16"
  }, [_vm._v("学情报告数据多维统计分析")])], 1)])])])])])]), _c("div", {
    staticClass: "pt100 pb30 flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 tl"
  }, [_c("svg-icon", {
    staticClass: "w510 h90",
    attrs: {
      "icon-class": "ai-text-03"
    }
  })], 1)]), _vm._m(1), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex flex-col align-end"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn",
      expression: "'animate__animated animate__fadeIn'"
    }],
    staticClass: "ai-tag1"
  }, [_vm._v(" 唤醒 ")]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-200",
      expression: "'animate__animated animate__fadeIn animate__delay-200'"
    }],
    staticClass: "w160 tc pt10 pb10"
  }, [_c("svg-icon", {
    staticClass: "h32 w15",
    attrs: {
      "icon-class": "ai-arrow-down"
    }
  })], 1)])]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-end"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-800",
      expression: "'animate__animated animate__fadeIn animate__delay-800'"
    }],
    staticClass: "ai-tag3"
  }, [_vm._v(" 授新 ")]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-600",
      expression: "'animate__animated animate__fadeIn animate__delay-600'"
    }]
  }, [_c("svg-icon", {
    staticClass: "w40 h15 mr10 ml10",
    attrs: {
      "icon-class": "ai-arrow-left"
    }
  })], 1), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-400",
      expression: "'animate__animated animate__fadeIn animate__delay-400'"
    }],
    staticClass: "ai-tag2"
  }, [_vm._v(" 复习 ")])])]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-1000",
      expression: "'animate__animated animate__fadeIn animate__delay-1000'"
    }],
    staticClass: "w1000 flex justify-end box-border",
    staticStyle: {
      padding: "10px 290px 10px 0"
    }
  }, [_c("svg-icon", {
    staticClass: "h32 w15",
    attrs: {
      "icon-class": "ai-arrow-down"
    }
  })], 1)]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-end",
    staticStyle: {
      padding: "0 220px 10px 0"
    }
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-1600",
      expression: "'animate__animated animate__fadeIn animate__delay-1600'"
    }],
    staticClass: "ai-tag5"
  }, [_vm._v(" 总结 ")]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-1400",
      expression: "'animate__animated animate__fadeIn animate__delay-1400'"
    }]
  }, [_c("svg-icon", {
    staticClass: "w40 h15 mr10 ml10",
    attrs: {
      "icon-class": "ai-arrow-left"
    }
  })], 1), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-1200",
      expression: "'animate__animated animate__fadeIn animate__delay-1200'"
    }],
    staticClass: "ai-tag4"
  }, [_vm._v(" 拓展 ")])])]), _c("div", {
    staticClass: "mt80 mb100 flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-center"
  }, [_c("div", {
    staticClass: "w h580"
  }, [_c("video-js", {
    ref: "video",
    attrs: {
      options: _vm.videoOptions
    }
  })], 1)])]), _c("div", {
    staticClass: "pb100 flex justify-center"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn",
      expression: "'animate__animated animate__fadeIn'"
    }],
    staticClass: "w1000 tl"
  }, [_c("svg-icon", {
    staticClass: "w700 h90",
    attrs: {
      "icon-class": "ai-text-04"
    }
  })], 1)]), _c("div", {
    staticClass: "flex justify-center mb200"
  }, [_c("div", {
    staticClass: "w1000 flex justify-between"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-200",
      expression: "'animate__animated animate__fadeIn animate__delay-200'"
    }],
    staticClass: "flex flex-col w450 tl"
  }, [_c("svg-icon", {
    staticClass: "w240 h50 mb10",
    attrs: {
      "icon-class": "ai-text-05"
    }
  }), _vm._m(2)], 1), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeInRight animate__delay-200",
      expression: "'animate__animated animate__fadeInRight animate__delay-200'"
    }],
    staticClass: "w600 flex flex-col"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/7.png */ "./src/assets/images/ai/7.png")
    }
  })])])]), _c("div", {
    staticClass: "pb50 flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 tl"
  }, [_c("svg-icon", {
    staticClass: "w580 h90",
    attrs: {
      "icon-class": "ai-text-06"
    }
  })], 1)]), _c("div", {
    staticClass: "flex justify-center mb100"
  }, [_c("div", {
    staticClass: "w1000 flex flex-col"
  }, [_c("div", {
    staticClass: "tag-title-box flex justify-between"
  }, _vm._l(_vm.tagsList, function (item, key) {
    return _c("div", {
      key: key,
      staticClass: "tag-item",
      class: {
        "tag-item-active": key === _vm.clickItem
      },
      on: {
        mouseover: function ($event) {
          return _vm.tagHover(key);
        }
      }
    }, [_vm._v(" " + _vm._s(key) + " ")]);
  }), 0), _c("div", {
    staticClass: "tags-box"
  }, [_c("div", {
    staticClass: "tag-bg flex justify-between"
  }, [_c("div", {
    class: _vm.clickItem === "传统文化类" || _vm.hoverItem === "传统文化类" ? "active-div" : "tag-div"
  }), _c("div", {
    class: _vm.clickItem === "安全健康类" || _vm.hoverItem === "安全健康类" ? "active-div" : "tag-div"
  })]), _c("div", {
    staticClass: "tag-box-content flex flex-wrap"
  }, _vm._l(_vm.tagsList[_vm.clickItem], function (item) {
    return _c("div", {
      key: item,
      staticClass: "tag-box-item"
    }, [_vm._v(_vm._s(item))]);
  }), 0)])])]), _c("div", {
    staticClass: "flex justify-center mb50"
  }, [_c("div", {
    staticClass: "w1000 flex justify-between"
  }, [_c("div", {
    staticClass: "flex flex-col w450 pt40 tl"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn",
      expression: "'animate__animated animate__fadeIn'"
    }]
  }, [_c("svg-icon", {
    staticClass: "w390 h50 mb10",
    attrs: {
      "icon-class": "ai-text-07"
    }
  })], 1), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-400",
      expression: "'animate__animated animate__fadeIn animate__delay-400'"
    }],
    staticClass: "color-333 f24 fb w tl lh40"
  }, [_vm._v(" 随材丰富，手册教具一应俱全。"), _c("br"), _vm._v(" 实践探究，感统调动。 ")])]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeInRight animate__delay-400",
      expression: "'animate__animated animate__fadeInRight animate__delay-400'"
    }],
    staticClass: "flex flex-col"
  }, [_vm._m(3), _c("div", {
    staticClass: "w tc mt30 f14"
  }, [_vm._v(" 学生实践 ")])])])]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeInLeft animate__delay-400",
      expression: "'animate__animated animate__fadeInLeft animate__delay-400'"
    }],
    staticClass: "flex flex-col align-center mb200"
  }, [_vm._m(4), _vm._m(5)])]), _c("div", {
    staticClass: "h-5 content"
  }, [_c("div", {
    staticClass: "pt30 pb20 flex justify-center"
  }, [_c("div", {
    staticClass: "w tc"
  }, [_c("svg-icon", {
    staticClass: "w250 h40",
    attrs: {
      "icon-class": "ai-text-01"
    }
  })], 1)]), _vm._m(6), _c("div", {
    staticClass: "ai-bg-box flex justify-center align-center"
  }, [_c("div", {
    staticClass: "w pl10 pr10 box-border"
  }, [_c("svg-icon", {
    staticClass: "w290 h46",
    attrs: {
      "icon-class": "ai-m-text-02"
    }
  }), _c("div", {
    staticClass: "color-white f22 mt10 mb30"
  }, [_vm._v(" AI赋能教学全环节 ")]), _c("el-row", {
    staticClass: "mb20",
    attrs: {
      gutter: 10
    }
  }, [_c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("div", {
    staticClass: "w flex flex-col align-center"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/2.png */ "./src/assets/images/ai/2.png")
    }
  }), _c("div", {
    staticClass: "w flex align-center pl5"
  }, [_c("svg-icon", {
    staticClass: "w20 h36 mr5",
    attrs: {
      "icon-class": "ai-num-1"
    }
  }), _c("span", {
    staticClass: "color-white f12"
  }, [_vm._v("智能排课、选课、备课")])], 1)])]), _c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("div", {
    staticClass: "w flex flex-col align-center"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/3.png */ "./src/assets/images/ai/3.png")
    }
  }), _c("div", {
    staticClass: "w flex align-center pl5"
  }, [_c("svg-icon", {
    staticClass: "w24 h36 mr5",
    attrs: {
      "icon-class": "ai-num-2"
    }
  }), _c("span", {
    staticClass: "color-white f12"
  }, [_vm._v("AI教师多感官情景授课")])], 1)])])], 1), _c("el-row", {
    staticClass: "mb20",
    attrs: {
      gutter: 10
    }
  }, [_c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("div", {
    staticClass: "w flex flex-col align-center"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/4.png */ "./src/assets/images/ai/4.png")
    }
  }), _c("div", {
    staticClass: "w flex align-center pl5"
  }, [_c("svg-icon", {
    staticClass: "w24 h36 mr5",
    attrs: {
      "icon-class": "ai-num-3"
    }
  }), _c("span", {
    staticClass: "color-white f12"
  }, [_vm._v("多元化考评练习激励机制")])], 1)])]), _c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("div", {
    staticClass: "w flex flex-col align-center"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/5.png */ "./src/assets/images/ai/5.png")
    }
  }), _c("div", {
    staticClass: "w flex align-center pl5"
  }, [_c("svg-icon", {
    staticClass: "w24 h36 mr5",
    attrs: {
      "icon-class": "ai-num-4"
    }
  }), _c("span", {
    staticClass: "color-white f12"
  }, [_vm._v("学情报告数据多维统计分析")])], 1)])])], 1)], 1)]), _c("div", {
    staticClass: "pt30 pb20 flex justify-center"
  }, [_c("div", {
    staticClass: "w tc"
  }, [_c("svg-icon", {
    staticClass: "w250 h46",
    attrs: {
      "icon-class": "ai-text-03"
    }
  })], 1)]), _vm._m(7), _c("div", {
    staticClass: "w pl10 pr10 box-border mt40"
  }, [_c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w flex flex-col align-end"
  }, [_c("div", {
    staticClass: "ai-tag1"
  }, [_vm._v(" 唤醒 ")]), _c("div", {
    staticClass: "w80 tc pt10 pb10"
  }, [_c("svg-icon", {
    staticClass: "h28 w15",
    attrs: {
      "icon-class": "ai-arrow-down"
    }
  })], 1)])]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w flex align-center justify-end"
  }, [_c("div", {
    staticClass: "ai-tag3"
  }, [_vm._v(" 授新 ")]), _c("div", [_c("svg-icon", {
    staticClass: "w40 h16 mr10 ml10",
    attrs: {
      "icon-class": "ai-arrow-left"
    }
  })], 1), _c("div", {
    staticClass: "ai-tag2"
  }, [_vm._v(" 复习 ")])])]), _c("div", {
    staticClass: "flex justify-end"
  }, [_c("div", {
    staticClass: "w flex justify-start box-border w195 mt10 mb10"
  }, [_c("svg-icon", {
    staticClass: "h32 w15",
    attrs: {
      "icon-class": "ai-arrow-down"
    }
  })], 1)]), _c("div", {
    staticClass: "flex justify-end"
  }, [_c("div", {
    staticClass: "w flex align-center justify-end pr155"
  }, [_c("div", {
    staticClass: "ai-tag5"
  }, [_vm._v(" 总结 ")]), _c("div", [_c("svg-icon", {
    staticClass: "w40 h16 mr10 ml10",
    attrs: {
      "icon-class": "ai-arrow-left"
    }
  })], 1), _c("div", {
    staticClass: "ai-tag4"
  }, [_vm._v(" 拓展 ")])])])]), _c("div", {
    staticClass: "mt30 mb60 flex justify-center"
  }, [_c("div", {
    staticClass: "w pr10 pl10 box-border flex justify-center"
  }, [_c("div", {
    staticClass: "w h220"
  }, [_c("video-js", {
    ref: "video",
    attrs: {
      options: _vm.videoOptions
    }
  })], 1)])]), _c("div", {
    staticClass: "mb20 flex justify-center"
  }, [_c("div", {
    staticClass: "w tc"
  }, [_c("svg-icon", {
    staticClass: "w350 h46",
    attrs: {
      "icon-class": "ai-text-04"
    }
  })], 1)]), _c("div", {
    staticClass: "w flex flex-col align-center justify-center pl10 pr10 box-border"
  }, [_c("svg-icon", {
    staticClass: "w140 h40 tc mb10",
    attrs: {
      "icon-class": "ai-text-05"
    }
  }), _vm._m(8), _c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/7.png */ "./src/assets/images/ai/7.png")
    }
  })], 1), _c("div", {
    staticClass: "mt70 mb20 flex justify-center"
  }, [_c("div", {
    staticClass: "w tc"
  }, [_c("svg-icon", {
    staticClass: "w340 h36",
    attrs: {
      "icon-class": "ai-text-06"
    }
  })], 1)]), _c("div", {
    staticClass: "flex justify-center pl10 pr10 box-border mb70"
  }, [_c("div", {
    staticClass: "w flex"
  }, [_c("div", {
    staticClass: "tag-title-box"
  }, _vm._l(_vm.tagsList, function (item, key) {
    return _c("div", {
      key: key,
      staticClass: "tag-item w",
      class: {
        "tag-item-active": key === _vm.clickItem
      },
      on: {
        click: function ($event) {
          return _vm.tagHover(key);
        }
      }
    }, [_vm._v(" " + _vm._s(key) + " ")]);
  }), 0), _c("div", {
    staticClass: "tags-box"
  }, [_c("div", {
    staticClass: "tag-box-content flex flex-wrap"
  }, _vm._l(_vm.tagsList[_vm.clickItem], function (item) {
    return _c("div", {
      key: item,
      staticClass: "tag-box-item"
    }, [_vm._v(_vm._s(item))]);
  }), 0)])])]), _c("div", {
    staticClass: "flex justify-center mb50"
  }, [_c("div", {
    staticClass: "w"
  }, [_c("div", {
    staticClass: "w tc"
  }, [_c("svg-icon", {
    staticClass: "w230 h34 mb20",
    attrs: {
      "icon-class": "ai-text-07"
    }
  })], 1), _vm._m(9), _vm._m(10)])]), _vm._m(11)])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "bg-color-box"
  }, [_c("div", {
    staticClass: "banner-title-box"
  }, [_c("div", {
    staticClass: "banner-title"
  }, [_c("div", {
    staticClass: "banner-title-weight animate__animated animate__fadeIn"
  }, [_vm._v("科技让教育更有趣")]), _c("div", {
    staticClass: "banner-title-item animate__animated animate__fadeIn animate__delay-100"
  }, [_vm._v("让每个孩子的个性得到完美绽放")])]), _c("img", {
    staticClass: "sky-img animate__animated animate__fadeIn animate__delay-100",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/ai-banner-1.png */ "./src/assets/images/ai/ai-banner-1.png")
    }
  })]), _c("div", {
    staticClass: "computer banner-nav animate__animated animate__fadeIn animate__delay-200"
  }, [_c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 趣味性 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 课前互动 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 故事化 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 情境授课 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 游戏化 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 闯关式学习 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 激励式 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 教学互动 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 阶段性 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 学习成果展现 ")])])]), _c("div", {
    staticClass: "h-5 banner-nav mb5"
  }, [_c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 趣味性 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 课前互动 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 故事化 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 情境授课 ")])])]), _c("div", {
    staticClass: "h-5 banner-nav"
  }, [_c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 游戏化 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 闯关式学习 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 激励式 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 教学互动 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 阶段性 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 学习成果展现 ")])])])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 tl f27 color-text fb"
  }, [_vm._v(" 符合课堂教学内容结构 "), _c("span", {
    staticClass: "f35 color-d9 fb"
  }, [_vm._v("5步")]), _vm._v(" 教学法。 ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "color-333 f24 fb w tl lh40"
  }, [_vm._v(" 围绕学生核心素养，"), _c("br"), _vm._v(" 以培养“全面发展的人”为核心，"), _c("br"), _vm._v(" 侧重启蒙与实践。 ")]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w500 flex"
  }, [_c("img", {
    attrs: {
      width: "250",
      src: __webpack_require__(/*! ../../assets/images/ai/8.png */ "./src/assets/images/ai/8.png")
    }
  }), _c("img", {
    attrs: {
      width: "250",
      src: __webpack_require__(/*! ../../assets/images/ai/9.png */ "./src/assets/images/ai/9.png")
    }
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w1000 flex"
  }, [_c("img", {
    attrs: {
      width: "600",
      src: __webpack_require__(/*! ../../assets/images/ai/10.png */ "./src/assets/images/ai/10.png")
    }
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w1000 flex mt20"
  }, [_c("div", {
    staticClass: "w600 tc f14"
  }, [_vm._v(" 素材包 ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w flex justify-center mb20"
  }, [_c("div", {
    staticClass: "w flex flex-col"
  }, [_c("div", {
    staticClass: "flex flex-col w mb30"
  }, [_c("div", {
    staticClass: "f20 fb color333 w tc mb20"
  }, [_vm._v(" 双翼驱动，构建高质量课堂 ")]), _c("div", {
    staticClass: "color-text f14 w tc lh20"
  }, [_vm._v(" 线上素质领域专家、专业教师沉浸式主导授课。"), _c("br"), _vm._v(" 线下带班教师补位助教，轻松组织管理班级。 ")])]), _c("div", {
    staticClass: "w flex flex-col"
  }, [_c("img", {
    staticClass: "w pl10 pr10 box-border mb20",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/1.png */ "./src/assets/images/ai/1.png")
    }
  }), _c("div", {
    staticClass: "f20 fb color333 w tc mb20"
  }, [_vm._v(" 低门槛落地，轻课减负减压 ")]), _c("div", {
    staticClass: "color-text f14 w tc lh20"
  }, [_vm._v(" 减轻教师备课负担，"), _c("br"), _vm._v(" 降低学校开课门槛。 ")])])])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w tc f21 color-text fb"
  }, [_vm._v(" 符合课堂教学内容结构 "), _c("span", {
    staticClass: "f30 color-d9"
  }, [_vm._v("5步")]), _vm._v(" 教学法。 ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "color-333 f16 fb w tc lh20 mb20"
  }, [_vm._v(" 围绕学生核心素养，"), _c("br"), _vm._v(" 以培养“全面发展的人”为核心，"), _c("br"), _vm._v(" 侧重启蒙与实践。 ")]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w color-333 f16 fb w tc lh30 mb20"
  }, [_vm._v(" 随材丰富，手册教具一应俱全。"), _c("br"), _vm._v(" 实践探究，感统调动。 ")]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w flex flex-col"
  }, [_c("div", {
    staticClass: "w flex pl10 pr10 box-border"
  }, [_c("div", {
    staticClass: "w"
  }, [_c("img", {
    staticClass: "border-r15-1",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/8.png */ "./src/assets/images/ai/8.png")
    }
  }), _c("img", {
    staticClass: "border-r15-2",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/9.png */ "./src/assets/images/ai/9.png")
    }
  })])]), _c("div", {
    staticClass: "w tc mt20 f14",
    staticStyle: {
      "font-weight": "300"
    }
  }, [_vm._v(" 学生实践 ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "flex flex-col align-center pr10 pl10 box-border mb60"
  }, [_c("div", {
    staticClass: "w flex"
  }, [_c("img", {
    staticClass: "w h200",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/ai/10.png */ "./src/assets/images/ai/10.png")
    }
  })]), _c("div", {
    staticClass: "w flex mt20"
  }, [_c("div", {
    staticClass: "w tc f14",
    staticStyle: {
      "font-weight": "300"
    }
  }, [_vm._v(" 素材包 ")])])]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true&":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--9-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/getUrl.js */ "./node_modules/css-loader/dist/runtime/getUrl.js");
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(/*! ../../assets/images/ai/ai-bg.png */ "./src/assets/images/ai/ai-bg.png");
exports = ___CSS_LOADER_API_IMPORT___(true);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
exports.push([module.i, "@media screen and (min-width: 769px) {\n.bg-color-box[data-v-60c32676] {\n    width: 100%;\n    height: 620px;\n    padding-top: 80px;\n    padding-bottom: 30px;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n}\n.bg-color-box .banner-title-box[data-v-60c32676] {\n      width: 1000px;\n      height: calc(100% - 56px);\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n}\n.bg-color-box .banner-title-box .banner-title[data-v-60c32676] {\n        display: flex;\n        flex-direction: column;\n        align-items: flex-start;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-weight[data-v-60c32676] {\n          font-family: 'PingFang SC';\n          font-weight: 500;\n          font-size: 50px;\n          margin-bottom: 16px;\n          color: #041128;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-item[data-v-60c32676] {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 28px;\n          color: #333333;\n}\n.bg-color-box .banner-nav[data-v-60c32676] {\n      height: 56px;\n      width: 1000px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-nav .item-box[data-v-60c32676] {\n        width: 175px;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n}\n.bg-color-box .banner-nav .item-box .item-title[data-v-60c32676] {\n          color: #333333;\n          font-weight: 500;\n          font-size: 23px;\n          margin-bottom: 3px;\n}\n.bg-color-box .banner-nav .item-box .item-desc[data-v-60c32676] {\n          color: #4F4F4F;\n          font-size: 15px;\n}\n.bg-color-box .banner-nav .item-line[data-v-60c32676] {\n        width: 1px;\n        height: 60%;\n        background: rgba(0, 0, 0, 0.3);\n}\n}\n@media screen and (max-width: 768px) {\n.bg-color-box[data-v-60c32676] {\n    width: 100%;\n    height: 90.69767vw;\n    padding-bottom: 4.65116vw;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n}\n.bg-color-box .banner-title-box[data-v-60c32676] {\n      width: 100%;\n      height: calc(100% - 9.30233vw);\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-title-box .banner-title[data-v-60c32676] {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        padding-top: 6.97674vw;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-weight[data-v-60c32676] {\n          font-family: 'PingFang SC';\n          font-weight: 600;\n          font-size: 5.5814vw;\n          margin-bottom: 3.72093vw;\n          color: #000000;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-item[data-v-60c32676] {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 3.72093vw;\n          color: #000000;\n}\n.bg-color-box .banner-nav[data-v-60c32676] {\n      height: 9.30233vw;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-nav .item-box[data-v-60c32676] {\n        width: 25.34884vw;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n}\n.bg-color-box .banner-nav .item-box .item-title[data-v-60c32676] {\n          color: #333333;\n          font-weight: 500;\n          font-size: 3.72093vw;\n          margin-bottom: 0.69767vw;\n}\n.bg-color-box .banner-nav .item-box .item-desc[data-v-60c32676] {\n          color: #4F4F4F;\n          font-size: 2.32558vw;\n}\n.bg-color-box .banner-nav .item-line[data-v-60c32676] {\n        width: 1px;\n        height: 60%;\n        background: rgba(0, 0, 0, 0.3);\n}\n}\n.vjs-poster[data-v-60c32676] {\n  background-size: cover !important;\n}\n.bg-color-box[data-v-60c32676] {\n  background: linear-gradient(90deg, #FCCCF1 0%, #FFF5CA 50%, #8FC0FD 100%);\n}\n@media screen and (min-width: 769px) {\n.h-5[data-v-60c32676] {\n    display: none !important;\n}\n.sky-img[data-v-60c32676] {\n    width: 500px;\n    height: 190px;\n}\n.content[data-v-60c32676] {\n    width: 100%;\n}\n.content .color333[data-v-60c32676] {\n      color: #333333;\n}\n.content .color-text[data-v-60c32676] {\n      color: #828282;\n}\n.content .color-white[data-v-60c32676] {\n      color: #fff;\n}\n.content .color-d9[data-v-60c32676] {\n      color: #BB6BD9;\n}\n.content .f35[data-v-60c32676] {\n      font-size: 35px;\n}\n.content .mb250[data-v-60c32676] {\n      margin-bottom: 250px;\n}\n.content .mb200[data-v-60c32676] {\n      margin-bottom: 200px;\n}\n.content .h580[data-v-60c32676] {\n      height: 562.5px;\n}\n.content .ai-bg-box[data-v-60c32676] {\n      width: 100%;\n      height: 800px;\n      background: url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ") no-repeat;\n      background-size: cover;\n}\n.content .ai-tag1[data-v-60c32676], .content .ai-tag2[data-v-60c32676], .content .ai-tag3[data-v-60c32676], .content .ai-tag4[data-v-60c32676], .content .ai-tag5[data-v-60c32676] {\n      width: 160px;\n      height: 160px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border-radius: 60px;\n      font-size: 40px;\n      font-weight: 600;\n}\n.content .ai-tag1[data-v-60c32676] {\n      color: #A44747;\n      background: #FF8C8C;\n}\n.content .ai-tag2[data-v-60c32676] {\n      color: #825024;\n      background: #F2994A;\n}\n.content .ai-tag3[data-v-60c32676] {\n      color: #725D1F;\n      background: #F2C94C;\n}\n.content .ai-tag4[data-v-60c32676] {\n      color: #346046;\n      background: #6FCF97;\n}\n.content .ai-tag5[data-v-60c32676] {\n      color: #245C6D;\n      background: #56CCF2;\n}\n.content .tags-box[data-v-60c32676] {\n      background: linear-gradient(90deg, #E9DEFA 0%, #FBFCDB 100%);\n      border-radius: 33px;\n      width: 100%;\n      min-height: 550px;\n      position: relative;\n}\n.content .tags-box .tag-bg[data-v-60c32676] {\n        position: absolute;\n        top: -40px;\n        left: 0;\n        width: 100%;\n        height: 80px;\n        z-index: -1;\n        box-sizing: border-box;\n}\n.content .tags-box .tag-bg .tag-div[data-v-60c32676] {\n          border-radius: 15px 15px 0px 0px;\n          width: 80px;\n          height: 100%;\n          background: #F2C94C;\n}\n.content .tags-box .tag-bg .active-div[data-v-60c32676] {\n          border-radius: 15px 15px 0px 0px;\n          width: 80px;\n          height: 100%;\n          background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n}\n.content .tags-box .tag-box-content[data-v-60c32676] {\n        padding: 60px 40px 0 40px;\n}\n.content .tags-box .tag-box-item[data-v-60c32676] {\n        width: 23%;\n        height: 44px;\n        border-radius: 8px;\n        margin-bottom: 40px;\n        margin-right: 18px;\n        background: #FFFFFF;\n        font-size: 18px;\n        color: #40405A;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n}\n.content .tag-title-box[data-v-60c32676] {\n      width: 100%;\n      height: 40px;\n}\n.content .tag-title-box .tag-item[data-v-60c32676] {\n        padding: 10px 15px;\n        box-sizing: border-box;\n        height: 100%;\n        font-size: 18px;\n        font-weight: 500;\n        border-radius: 15px 15px 0px 0px;\n        background: #F2C94C;\n        color: #40405A;\n        cursor: pointer;\n}\n.content .tag-title-box .tag-item[data-v-60c32676]:hover {\n          color: #fff;\n          background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n}\n.content .tag-title-box .tag-item-active[data-v-60c32676] {\n        color: #fff;\n        background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n}\n}\n@media screen and (max-width: 768px) {\n.computer[data-v-60c32676] {\n    display: none !important;\n}\n.bg-color-box[data-v-60c32676] {\n    height: 100vw !important;\n}\n.bg-color-box .banner-title[data-v-60c32676] {\n      padding: 0 !important;\n}\n.sky-img[data-v-60c32676] {\n    width: 96%;\n    height: 36.04651vw;\n    margin-top: 4.65116vw;\n}\n.content[data-v-60c32676] {\n    width: 100%;\n}\n.content .w24[data-v-60c32676] {\n      width: 5.5814vw;\n}\n.content .color333[data-v-60c32676] {\n      color: #333333;\n}\n.content .color-text[data-v-60c32676] {\n      color: #828282;\n}\n.content .color-white[data-v-60c32676] {\n      color: #fff;\n}\n.content .color-d9[data-v-60c32676] {\n      color: #BB6BD9;\n}\n.content .f30[data-v-60c32676] {\n      font-size: 6.97674vw;\n}\n.content .w195[data-v-60c32676] {\n      width: 45.34884vw;\n}\n.content .pr155[data-v-60c32676] {\n      padding-right: 33.72093vw;\n}\n.content .h220[data-v-60c32676] {\n      height: 55.81395vw;\n}\n.content .ai-bg-box[data-v-60c32676] {\n      width: 100%;\n      height: 139.53488vw;\n      background: url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ") no-repeat;\n      background-size: cover;\n}\n.content .ai-tag1[data-v-60c32676], .content .ai-tag2[data-v-60c32676], .content .ai-tag3[data-v-60c32676], .content .ai-tag4[data-v-60c32676], .content .ai-tag5[data-v-60c32676] {\n      width: 19.76744vw;\n      height: 19.76744vw;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border-radius: 6.97674vw;\n      font-size: 5.5814vw;\n      font-weight: 600;\n}\n.content .ai-tag1[data-v-60c32676] {\n      color: #A44747;\n      background: #FF8C8C;\n}\n.content .ai-tag2[data-v-60c32676] {\n      color: #825024;\n      background: #F2994A;\n}\n.content .ai-tag3[data-v-60c32676] {\n      color: #725D1F;\n      background: #F2C94C;\n}\n.content .ai-tag4[data-v-60c32676] {\n      color: #346046;\n      background: #6FCF97;\n}\n.content .ai-tag5[data-v-60c32676] {\n      color: #245C6D;\n      background: #56CCF2;\n}\n.content .tags-box[data-v-60c32676] {\n      background: linear-gradient(90deg, #E9DEFA 0%, #FBFCDB 100%);\n      border-radius: 1.16279vw;\n      width: 100%;\n      min-height: 76.74419vw;\n      position: relative;\n}\n.content .tags-box .tag-box-content[data-v-60c32676] {\n        padding: 2.32558vw 3.48837vw;\n}\n.content .tags-box .tag-box-item[data-v-60c32676] {\n        width: 47%;\n        padding: 2.32558vw 1.86047vw;\n        border-radius: 1.86047vw;\n        margin-bottom: 3.48837vw;\n        background: #FFFFFF;\n        font-size: 2.7907vw;\n        color: #40405A;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n}\n.content .tags-box .tag-box-item[data-v-60c32676]:nth-child(2n+1) {\n          margin-right: 3.48837vw;\n}\n.content .border-r15-1[data-v-60c32676] {\n      border-radius: 3.48837vw 0 0 3.48837vw;\n      width: 50%;\n}\n.content .border-r15-2[data-v-60c32676] {\n      border-radius: 0 3.48837vw 3.48837vw 0;\n      width: 50%;\n}\n.content .tag-title-box[data-v-60c32676] {\n      width: 27.90698vw;\n      padding-right: 10px;\n      box-sizing: border-box;\n}\n.content .tag-title-box .tag-item[data-v-60c32676] {\n        padding: 2.32558vw;\n        margin-bottom: 2.32558vw;\n        box-sizing: border-box;\n        font-size: 2.7907vw;\n        font-weight: 600;\n        border-radius: 1.16279vw;\n        background: #F2C94C;\n        color: #40405A;\n        cursor: pointer;\n}\n.content .tag-title-box .tag-item[data-v-60c32676]:hover {\n          color: #fff;\n          background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n}\n.content .tag-title-box .tag-item-active[data-v-60c32676] {\n        color: #fff;\n        background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n}\n}\n", "",{"version":3,"sources":["/Users/<USER>/Documents/cuiya官网/cuiya_official_website/src/styles/global.scss","/Users/<USER>/Documents/cuiya官网/cuiya_official_website/src/views/ai/index.vue"],"names":[],"mappings":"AA6BA;AAEE;IACE,WAAW;IACX,aAAa;IACb,iBATgB;IAUhB,oBAAoB;IACpB,sBAAsB;IACtB,aAAa;IACb,sBAAsB;IACtB,mBAAmB;IACnB,uBAAuB;AAAA;AATzB;MAYI,aAAa;MACb,yBAAwC;MACxC,aAAa;MACb,8BAA8B;MAC9B,mBAAmB;AAAA;AAhBvB;QAmBM,aAAa;QACb,sBAAsB;QACtB,uBAAuB;AAAA;AArB7B;UAwBQ,0BAA0B;UAC1B,gBAAgB;UAChB,eAAe;UACf,mBAAmB;UACnB,cAAc;AAAA;AA5BtB;UA+BQ,0BAA0B;UAC1B,gBAAgB;UAChB,eAAe;UACf,cAAc;AAAA;AAlCtB;MAwCI,YA7CgB;MA8ChB,aAAa;MACb,aAAa;MACb,uBAAuB;MACvB,mBAAmB;AAAA;AA5CvB;QA+CM,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;AAAA;AApDzB;UAsDQ,cAAc;UACd,gBAAgB;UAChB,eAAe;UACf,kBAAkB;AAAA;AAzD1B;UA4DQ,cAAc;UACd,eAAe;AAAA;AA7DvB;QAiEM,UAAU;QACV,WAAW;QACX,8BAAgC;AAAA;AACjC;AAIP;AAEE;IACE,WAAW;IACX,kBArFqC;IAsFrC,yBAtFqC;IAuFrC,sBAAsB;IACtB,aAAa;IACb,sBAAsB;IACtB,mBAAmB;IACnB,uBAAuB;AAAA;AARzB;MAWI,WAAW;MACX,8BAAyC;MACzC,aAAa;MACb,sBAAsB;MACtB,uBAAuB;MACvB,mBAAmB;AAAA;AAhBvB;QAmBM,aAAa;QACb,sBAAsB;QACtB,mBAAmB;QACnB,sBAzGiC;AAAA;AAmFvC;UAyBQ,0BAA0B;UAC1B,gBAAgB;UAChB,mBA9G+B;UA+G/B,wBA/G+B;UAgH/B,cAAc;AAAA;AA7BtB;UAgCQ,0BAA0B;UAC1B,gBAAgB;UAChB,oBArH+B;UAsH/B,cAAc;AAAA;AAnCtB;MAyCI,iBA5HmC;MA6HnC,WAAW;MACX,aAAa;MACb,uBAAuB;MACvB,mBAAmB;AAAA;AA7CvB;QAgDM,iBAnIiC;QAoIjC,YAAY;QACZ,aAAa;QACb,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;AAAA;AArDzB;UAuDQ,cAAc;UACd,gBAAgB;UAChB,oBA5I+B;UA6I/B,wBA7I+B;AAAA;AAmFvC;UA6DQ,cAAc;UACd,oBAjJ+B;AAAA;AAmFvC;QAkEM,UAAU;QACV,WAAW;QACX,8BAAgC;AAAA;AACjC;AAKP;EACE,iCAAiC;AAAA;AC/KnC;EACE,yEAAyE;AAAA;AAE3E;AACE;IACE,wBAAwB;AAAA;AAG1B;IACE,YAAY;IACZ,aAAa;AAAA;AAGf;IACE,WAAW;AAAA;AADb;MAII,cAAc;AAAA;AAJlB;MAOI,cAAc;AAAA;AAPlB;MAUI,WAAW;AAAA;AAVf;MAcI,cAAc;AAAA;AAdlB;MAiBI,eAAe;AAAA;AAjBnB;MAqBI,oBAAoB;AAAA;AArBxB;MAyBI,oBAAoB;AAAA;AAzBxB;MA6BI,eAAe;AAAA;AA7BnB;MAiCI,WAAW;MACX,aAAa;MACb,6DAA6D;MAC7D,sBAAsB;AAAA;AApC1B;MAwCI,YAAY;MACZ,aAAa;MACb,aAAa;MACb,uBAAuB;MACvB,mBAAmB;MACnB,mBAAmB;MACnB,eAAe;MACf,gBAAgB;AAAA;AA/CpB;MAmDI,cAAc;MACd,mBAAmB;AAAA;AApDvB;MAwDI,cAAc;MACd,mBAAmB;AAAA;AAzDvB;MA4DI,cAAc;MACd,mBAAmB;AAAA;AA7DvB;MAgEI,cAAc;MACd,mBAAmB;AAAA;AAjEvB;MAoEI,cAAc;MACd,mBAAmB;AAAA;AArEvB;MAyEI,4DAA4D;MAC5D,mBAAmB;MACnB,WAAW;MACX,iBAAiB;MACjB,kBAAkB;AAAA;AA7EtB;QAgFM,kBAAkB;QAClB,UAAU;QACV,OAAO;QACP,WAAW;QACX,YAAY;QACZ,WAAW;QACX,sBAAsB;AAAA;AAtF5B;UAyFQ,gCAAgC;UAChC,WAAW;UACX,YAAY;UACZ,mBAAmB;AAAA;AA5F3B;UAgGQ,gCAAgC;UAChC,WAAW;UACX,YAAY;UACZ,yEAAyE;AAAA;AAnGjF;QAwGM,yBAAyB;AAAA;AAxG/B;QA4GM,UAAU;QACV,YAAY;QACZ,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,eAAe;QACf,cAAc;QACd,aAAa;QACb,uBAAuB;QACvB,mBAAmB;AAAA;AAtHzB;MA0HI,WAAW;MACX,YAAY;AAAA;AA3HhB;QA8HM,kBAAkB;QAClB,sBAAsB;QACtB,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,gCAAgC;QAChC,mBAAmB;QACnB,cAAc;QACd,eAAe;AAAA;AAtIrB;UAwIQ,WAAW;UACX,yEAAyE;AAAA;AAzIjF;QA8IM,WAAW;QACX,yEAAyE;AAAA;AAC1E;AAKP;AACE;IACE,wBAAwB;AAAA;AAG1B;IACE,wBAA0B;AAAA;AAD5B;MAGI,qBAAqB;AAAA;AAIzB;IACE,UAAU;IACV,kBD/JqC;ICgKrC,qBDhKqC;AAAA;ACmKvC;IACE,WAAW;AAAA;AADb;MAII,eDvKmC;AAAA;ACmKvC;MAQI,cAAc;AAAA;AARlB;MAWI,cAAc;AAAA;AAXlB;MAcI,WAAW;AAAA;AAdf;MAkBI,cAAc;AAAA;AAlBlB;MAsBI,oBDzLmC;AAAA;ACmKvC;MA0BI,iBD7LmC;AAAA;ACmKvC;MA8BI,yBDjMmC;AAAA;ACmKvC;MAkCI,kBDrMmC;AAAA;ACmKvC;MAsCI,WAAW;MACX,mBD1MmC;MC2MnC,6DAA6D;MAC7D,sBAAsB;AAAA;AAzC1B;MA6CI,iBDhNmC;MCiNnC,kBDjNmC;MCkNnC,aAAa;MACb,uBAAuB;MACvB,mBAAmB;MACnB,wBDrNmC;MCsNnC,mBDtNmC;MCuNnC,gBAAgB;AAAA;AApDpB;MAwDI,cAAc;MACd,mBAAmB;AAAA;AAzDvB;MA6DI,cAAc;MACd,mBAAmB;AAAA;AA9DvB;MAiEI,cAAc;MACd,mBAAmB;AAAA;AAlEvB;MAqEI,cAAc;MACd,mBAAmB;AAAA;AAtEvB;MAyEI,cAAc;MACd,mBAAmB;AAAA;AA1EvB;MA8EI,4DAA4D;MAC5D,wBDlPmC;MCmPnC,WAAW;MACX,sBDpPmC;MCqPnC,kBAAkB;AAAA;AAlFtB;QAqFM,4BDxPiC;AAAA;ACmKvC;QAyFM,UAAU;QACV,4BD7PiC;QC+PjC,wBD/PiC;QCgQjC,wBDhQiC;QCiQjC,mBAAmB;QACnB,mBDlQiC;QCmQjC,cAAc;QACd,aAAa;QACb,uBAAuB;QACvB,mBAAmB;AAAA;AAnGzB;UAqGQ,uBDxQ+B;AAAA;ACmKvC;MA2GI,sCD9QmC;MC+QnC,UAAU;AAAA;AA5Gd;MA+GI,sCAAgC;MAChC,UAAU;AAAA;AAhHd;MAmHI,iBDtRmC;MCuRnC,mBAAmB;MACnB,sBAAsB;AAAA;AArH1B;QAwHM,kBD3RiC;QC4RjC,wBD5RiC;QC6RjC,sBAAsB;QACtB,mBD9RiC;QC+RjC,gBAAgB;QAChB,wBDhSiC;QCiSjC,mBAAmB;QACnB,cAAc;QACd,eAAe;AAAA;AAhIrB;UAkIQ,WAAW;UACX,yEAAyE;AAAA;AAnIjF;QAwIM,WAAW;QACX,yEAAyE;AAAA;AAC1E","file":"index.vue","sourcesContent":["@import './mixin.scss';\n\n//默认设计稿的宽度\n$designWidth: 1440;\n//默认设计稿的高度\n$designHeight: 810;\n\n//默认设计稿的宽度\n$designWidthH5: 430;\n\n//px转为vw的函数\n@function vw($px) {\n  @return $px * 100vw / #{$designWidth};\n}\n\n//px转为vh的函数\n@function vh($px) {\n  @return $px * 100vh / #{$designHeight};\n}\n\n//H5中px转为vw的函数\n@function pw($px) {\n  @return $px * 100vw / #{$designWidthH5};\n}\n\n$navTopPadding: 80px;\n$bannerNavHeight: 56px;\n$MbannerNavHeight: pw(40);\n\n@media screen and (min-width: 769px) {\n  // 通用首页banner样式\n  .bg-color-box {\n    width: 100%;\n    height: 620px;\n    padding-top: $navTopPadding;\n    padding-bottom: 30px;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n\n    .banner-title-box {\n      width: 1000px;\n      height: calc(100% - #{$bannerNavHeight});\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .banner-title {\n        display: flex;\n        flex-direction: column;\n        align-items: flex-start;\n\n        .banner-title-weight {\n          font-family: 'PingFang SC';\n          font-weight: 500;\n          font-size: 50px;\n          margin-bottom: 16px;\n          color: #041128;\n        }\n        .banner-title-item {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 28px;\n          color: #333333;\n        }\n      }\n    }\n\n    .banner-nav {\n      height: $bannerNavHeight;\n      width: 1000px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .item-box {\n        width: 175px;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        .item-title {\n          color: #333333;\n          font-weight: 500;\n          font-size: 23px;\n          margin-bottom: 3px;\n        }\n        .item-desc {\n          color: #4F4F4F;\n          font-size: 15px;\n        }\n      }\n      .item-line {\n        width: 1px;\n        height: 60%;\n        background: rgba($color: #000000, $alpha: 0.3);\n      }\n    }\n  }\n}\n@media screen and (max-width: 768px) {\n  // 通用首页banner样式\n  .bg-color-box {\n    width: 100%;\n    height: pw(390);\n    padding-bottom: pw(20);\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n\n    .banner-title-box {\n      width: 100%;\n      height: calc(100% - #{$MbannerNavHeight});\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n\n      .banner-title {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        padding-top: pw(30);\n\n        .banner-title-weight {\n          font-family: 'PingFang SC';\n          font-weight: 600;\n          font-size: pw(24);\n          margin-bottom: pw(16);\n          color: #000000;\n        }\n        .banner-title-item {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: pw(16);\n          color: #000000;\n        }\n      }\n    }\n\n    .banner-nav {\n      height: $MbannerNavHeight;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .item-box {\n        width: pw(109);\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        .item-title {\n          color: #333333;\n          font-weight: 500;\n          font-size: pw(16);\n          margin-bottom: pw(3);\n        }\n        .item-desc {\n          color: #4F4F4F;\n          font-size: pw(10);\n        }\n      }\n      .item-line {\n        width: 1px;\n        height: 60%;\n        background: rgba($color: #000000, $alpha: 0.3);\n      }\n    }\n  }\n}\n\n.vjs-poster {\n  background-size: cover !important;\n}","\n        @import \"@/styles/global.scss\";\n        $src: \"./src/assets\";\n        \n\n.bg-color-box {\n  background: linear-gradient(90deg, #FCCCF1 0%, #FFF5CA 50%, #8FC0FD 100%);\n}\n@media screen and (min-width: 769px) {\n  .h-5 {\n    display: none !important;\n  }\n\n  .sky-img {\n    width: 500px;\n    height: 190px;\n  }\n\n  .content {\n    width: 100%;\n\n    .color333 {\n      color: #333333;\n    }\n    .color-text {\n      color: #828282;\n    }\n    .color-white {\n      color: #fff;\n    }\n\n    .color-d9 {\n      color: #BB6BD9;\n    }\n    .f35 {\n      font-size: 35px;\n    }\n\n    .mb250 {\n      margin-bottom: 250px;\n    }\n\n    .mb200 {\n      margin-bottom: 200px;\n    }\n\n    .h580 {\n      height: 562.5px;\n    }\n\n    .ai-bg-box {\n      width: 100%;\n      height: 800px;\n      background: url('../../assets/images/ai/ai-bg.png') no-repeat;\n      background-size: cover;\n    }\n\n    .ai-tag1, .ai-tag2, .ai-tag3, .ai-tag4, .ai-tag5 {\n      width: 160px;\n      height: 160px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border-radius: 60px;\n      font-size: 40px;\n      font-weight: 600;\n    }\n\n    .ai-tag1 {\n      color: #A44747;\n      background: #FF8C8C;\n    }\n\n    .ai-tag2 {\n      color: #825024;\n      background: #F2994A;\n    }\n    .ai-tag3 {\n      color: #725D1F;\n      background: #F2C94C;\n    }\n    .ai-tag4 {\n      color: #346046;\n      background: #6FCF97;\n    }\n    .ai-tag5 {\n      color: #245C6D;\n      background: #56CCF2;\n    }\n\n    .tags-box {\n      background: linear-gradient(90deg, #E9DEFA 0%, #FBFCDB 100%);\n      border-radius: 33px;\n      width: 100%;\n      min-height: 550px;\n      position: relative;\n\n      .tag-bg {\n        position: absolute;\n        top: -40px;\n        left: 0;\n        width: 100%;\n        height: 80px;\n        z-index: -1;\n        box-sizing: border-box;\n\n        .tag-div {\n          border-radius: 15px 15px 0px 0px;\n          width: 80px;\n          height: 100%;\n          background: #F2C94C;\n        }\n\n        .active-div {\n          border-radius: 15px 15px 0px 0px;\n          width: 80px;\n          height: 100%;\n          background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n        }\n      }\n\n      .tag-box-content {\n        padding: 60px 40px 0 40px;\n      }\n\n      .tag-box-item {\n        width: 23%;\n        height: 44px;\n        border-radius: 8px;\n        margin-bottom: 40px;\n        margin-right: 18px;\n        background: #FFFFFF;\n        font-size: 18px;\n        color: #40405A;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n      }\n    }\n    .tag-title-box {\n      width: 100%;\n      height: 40px;\n\n      .tag-item {\n        padding: 10px 15px;\n        box-sizing: border-box;\n        height: 100%;\n        font-size: 18px;\n        font-weight: 500;\n        border-radius: 15px 15px 0px 0px;\n        background: #F2C94C;\n        color: #40405A;\n        cursor: pointer;\n        &:hover {\n          color: #fff;\n          background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n        }\n      }\n\n      .tag-item-active {\n        color: #fff;\n        background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n      }\n    }\n  }\n}\n\n@media screen and (max-width: 768px) {\n  .computer {\n    display: none !important;\n  }\n\n  .bg-color-box {\n    height: pw(430) !important;\n    .banner-title {\n      padding: 0 !important;\n    }\n  }\n\n  .sky-img {\n    width: 96%;\n    height: pw(155);\n    margin-top: pw(20);\n  }\n\n  .content {\n    width: 100%;\n\n    .w24 {\n      width: pw(24);\n    }\n\n    .color333 {\n      color: #333333;\n    }\n    .color-text {\n      color: #828282;\n    }\n    .color-white {\n      color: #fff;\n    }\n\n    .color-d9 {\n      color: #BB6BD9;\n    }\n\n    .f30 {\n      font-size: pw(30);\n    }\n\n    .w195 {\n      width: pw(195);\n    }\n\n    .pr155 {\n      padding-right: pw(145);\n    }\n\n    .h220 {\n      height: pw(240);\n    }\n\n    .ai-bg-box {\n      width: 100%;\n      height: pw(600);\n      background: url('../../assets/images/ai/ai-bg.png') no-repeat;\n      background-size: cover;\n    }\n\n    .ai-tag1, .ai-tag2, .ai-tag3, .ai-tag4, .ai-tag5 {\n      width: pw(85);\n      height: pw(85);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border-radius: pw(30);\n      font-size: pw(24);\n      font-weight: 600;\n    }\n\n    .ai-tag1 {\n      color: #A44747;\n      background: #FF8C8C;\n    }\n\n    .ai-tag2 {\n      color: #825024;\n      background: #F2994A;\n    }\n    .ai-tag3 {\n      color: #725D1F;\n      background: #F2C94C;\n    }\n    .ai-tag4 {\n      color: #346046;\n      background: #6FCF97;\n    }\n    .ai-tag5 {\n      color: #245C6D;\n      background: #56CCF2;\n    }\n\n    .tags-box {\n      background: linear-gradient(90deg, #E9DEFA 0%, #FBFCDB 100%);\n      border-radius: pw(5);\n      width: 100%;\n      min-height: pw(330);\n      position: relative;\n\n      .tag-box-content {\n        padding: pw(10) pw(15);\n      }\n\n      .tag-box-item {\n        width: 47%;\n        padding: pw(10) pw(8);\n        // height: pw(40);\n        border-radius: pw(8);\n        margin-bottom: pw(15);\n        background: #FFFFFF;\n        font-size: pw(12);\n        color: #40405A;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        &:nth-child(2n+1) {\n          margin-right: pw(15);\n        }\n      }\n    }\n\n    .border-r15-1 {\n      border-radius: pw(15) 0 0 pw(15);\n      width: 50%;\n    }\n    .border-r15-2 {\n      border-radius: 0 pw(15) pw(15) 0;\n      width: 50%;\n    }\n    .tag-title-box {\n      width: pw(120);\n      padding-right: 10px;\n      box-sizing: border-box;\n\n      .tag-item {\n        padding: pw(10);\n        margin-bottom: pw(10);\n        box-sizing: border-box;\n        font-size: pw(12);\n        font-weight: 600;\n        border-radius: pw(5);\n        background: #F2C94C;\n        color: #40405A;\n        cursor: pointer;\n        &:hover {\n          color: #fff;\n          background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n        }\n      }\n\n      .tag-item-active {\n        color: #fff;\n        background: linear-gradient(90deg, #FEAC5E 0%, #C779D0 50%, #4BC0C8 100%);\n      }\n    }\n  }\n}\n"]}]);
// Exports
module.exports = exports;


/***/ }),

/***/ "./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true&":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader??ref--9-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--9-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true&");
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(/*! ../../../node_modules/vue-style-loader/lib/addStylesClient.js */ "./node_modules/vue-style-loader/lib/addStylesClient.js").default
var update = add("095cf72e", content, false, {"sourceMap":true,"shadowMode":false});
// Hot Module Replacement
if(true) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true&", function() {
     var newContent = __webpack_require__(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true&");
     if(newContent.__esModule) newContent = newContent.default;
     if(typeof newContent === 'string') newContent = [[module.i, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ "./src/assets/images/ai/1.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/1.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/1.0e33ed5b.png";

/***/ }),

/***/ "./src/assets/images/ai/10.png":
/*!*************************************!*\
  !*** ./src/assets/images/ai/10.png ***!
  \*************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/10.ff8aacdd.png";

/***/ }),

/***/ "./src/assets/images/ai/2.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/2.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/2.8b067fe7.png";

/***/ }),

/***/ "./src/assets/images/ai/3.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/3.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/3.6b2c176d.png";

/***/ }),

/***/ "./src/assets/images/ai/4.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/4.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/4.0fde2139.png";

/***/ }),

/***/ "./src/assets/images/ai/5.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/5.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/5.14d7ae8d.png";

/***/ }),

/***/ "./src/assets/images/ai/6.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/6.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/6.dd1c6e4b.png";

/***/ }),

/***/ "./src/assets/images/ai/7.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/7.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/7.95e13193.png";

/***/ }),

/***/ "./src/assets/images/ai/8.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/8.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/8.89f4632e.png";

/***/ }),

/***/ "./src/assets/images/ai/9.png":
/*!************************************!*\
  !*** ./src/assets/images/ai/9.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/9.3baa05de.png";

/***/ }),

/***/ "./src/assets/images/ai/ai-banner-1.png":
/*!**********************************************!*\
  !*** ./src/assets/images/ai/ai-banner-1.png ***!
  \**********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/ai-banner-1.f7ec30a0.png";

/***/ }),

/***/ "./src/assets/images/ai/ai-bg.png":
/*!****************************************!*\
  !*** ./src/assets/images/ai/ai-bg.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/ai-bg.0b2740a1.png";

/***/ }),

/***/ "./src/assets/video/v-2.mp4":
/*!**********************************!*\
  !*** ./src/assets/video/v-2.mp4 ***!
  \**********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/media/v-2.0febd2f9.mp4";

/***/ }),

/***/ "./src/views/ai/index.vue":
/*!********************************!*\
  !*** ./src/views/ai/index.vue ***!
  \********************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=60c32676&scoped=true& */ "./src/views/ai/index.vue?vue&type=template&id=60c32676&scoped=true&");
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ "./src/views/ai/index.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _index_vue_vue_type_style_index_0_id_60c32676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true& */ "./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true&");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");






/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "60c32676",
  null
  
)

/* hot reload */
if (true) {
  var api = __webpack_require__(/*! ./node_modules/vue-hot-reload-api/dist/index.js */ "./node_modules/vue-hot-reload-api/dist/index.js")
  api.install(__webpack_require__(/*! vue */ "./node_modules/vue/dist/vue.runtime.esm.js"))
  if (api.compatible) {
    module.hot.accept()
    if (!api.isRecorded('60c32676')) {
      api.createRecord('60c32676', component.options)
    } else {
      api.reload('60c32676', component.options)
    }
    module.hot.accept(/*! ./index.vue?vue&type=template&id=60c32676&scoped=true& */ "./src/views/ai/index.vue?vue&type=template&id=60c32676&scoped=true&", function(__WEBPACK_OUTDATED_DEPENDENCIES__) { /* harmony import */ _index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=60c32676&scoped=true& */ "./src/views/ai/index.vue?vue&type=template&id=60c32676&scoped=true&");
(function () {
      api.rerender('60c32676', {
        render: _index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
        staticRenderFns: _index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]
      })
    })(__WEBPACK_OUTDATED_DEPENDENCIES__); }.bind(this))
  }
}
component.options.__file = "src/views/ai/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/views/ai/index.vue?vue&type=script&lang=js&":
/*!*********************************************************!*\
  !*** ./src/views/ai/index.vue?vue&type=script&lang=js& ***!
  \*********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/babel-loader/lib!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=script&lang=js& */ "./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true&":
/*!******************************************************************************************!*\
  !*** ./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true& ***!
  \******************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_60c32676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/vue-style-loader??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true& */ "./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=style&index=0&id=60c32676&lang=scss&scoped=true&");
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_60c32676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_60c32676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_60c32676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_60c32676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ "./src/views/ai/index.vue?vue&type=template&id=60c32676&scoped=true&":
/*!***************************************************************************!*\
  !*** ./src/views/ai/index.vue?vue&type=template&id=60c32676&scoped=true& ***!
  \***************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/cache-loader/dist/cjs.js?{"cacheDirectory":"node_modules/.cache/vue-loader","cacheIdentifier":"1df0c205-vue-loader-template"}!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/babel-loader/lib!../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=template&id=60c32676&scoped=true& */ "./node_modules/cache-loader/dist/cjs.js?{\"cacheDirectory\":\"node_modules/.cache/vue-loader\",\"cacheIdentifier\":\"1df0c205-vue-loader-template\"}!./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/ai/index.vue?vue&type=template&id=60c32676&scoped=true&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_60c32676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);
//# sourceMappingURL=5.js.map