((typeof self !== 'undefined' ? self : this)["webpackJsonp"] = (typeof self !== 'undefined' ? self : this)["webpackJsonp"] || []).push([[8],{

/***/ "./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=script&lang=js&":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/cache-loader/dist/cjs.js??ref--13-0!./node_modules/babel-loader/lib!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/shuzi/index.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _utils_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/index.js */ "./src/utils/index.js");
/* harmony import */ var _components_videoJs_vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/videoJs.vue */ "./src/components/videoJs.vue");
/* harmony import */ var _assets_video_v_1_mp4__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../assets/video/v-1.mp4 */ "./src/assets/video/v-1.mp4");
/* harmony import */ var _assets_video_v_1_mp4__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_assets_video_v_1_mp4__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _assets_images_sz_6_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../assets/images/sz/6.png */ "./src/assets/images/sz/6.png");
/* harmony import */ var _assets_images_sz_6_png__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sz_6_png__WEBPACK_IMPORTED_MODULE_3__);




/* harmony default export */ __webpack_exports__["default"] = ({
  components: {
    videoJs: _components_videoJs_vue__WEBPACK_IMPORTED_MODULE_1__["default"]
  },
  inject: ['setFooterState', 'setNavFontState', 'setNavState'],
  data() {
    return {
      V1: (_assets_video_v_1_mp4__WEBPACK_IMPORTED_MODULE_2___default()),
      coverImg: (_assets_images_sz_6_png__WEBPACK_IMPORTED_MODULE_3___default()),
      time: null,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false,
        loop: false,
        poster: _assets_images_sz_6_png__WEBPACK_IMPORTED_MODULE_3___default.a,
        sources: [{
          src: _assets_video_v_1_mp4__WEBPACK_IMPORTED_MODULE_2___default.a,
          type: 'video/mp4'
        }]
      }
    };
  },
  mounted() {
    this.setFooterState(true);
    this.setNavState(1);
    this.setNavFontState(0);
    window.addEventListener('scroll', this.handleNavbarState);
    this.gifMove();
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleNavbarState);
    this.setNavState(1);
    if (this.time) {
      clearInterval(this.time);
    }
  },
  methods: {
    handleNavbarState() {
      const documentTop = Object(_utils_index_js__WEBPACK_IMPORTED_MODULE_0__["getDocumentTop"])();
      if (documentTop < 10) {
        this.setNavState(1);
      } else {
        this.setNavState(0);
      }
    },
    gifMove() {
      this.time = setInterval(() => {
        document.getElementById('gif').src = document.getElementById('gif').src;
      }, 2000);
    }
  }
});

/***/ }),

/***/ "./node_modules/cache-loader/dist/cjs.js?{\"cacheDirectory\":\"node_modules/.cache/vue-loader\",\"cacheIdentifier\":\"1df0c205-vue-loader-template\"}!./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=template&id=5d035cf0&scoped=true&":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/cache-loader/dist/cjs.js?{"cacheDirectory":"node_modules/.cache/vue-loader","cacheIdentifier":"1df0c205-vue-loader-template"}!./node_modules/cache-loader/dist/cjs.js??ref--13-0!./node_modules/babel-loader/lib!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/shuzi/index.vue?vue&type=template&id=5d035cf0&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w"
  }, [_c("div", {
    staticClass: "bg-color-box"
  }, [_c("div", {
    staticClass: "banner-title-box"
  }, [_vm._m(0), _c("svg-icon", {
    staticClass: "sky-img animate__animated animate__fadeIn animate__delay-100",
    attrs: {
      "icon-class": "sz-1"
    }
  })], 1), _vm._m(1)]), _c("div", {
    staticClass: "computer content"
  }, [_c("div", {
    staticClass: "pt50 pb50 flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 tc"
  }, [_c("svg-icon", {
    staticClass: "w600 h90",
    attrs: {
      "icon-class": "sz-text-1"
    }
  })], 1)]), _c("div", {
    staticClass: "pb100 flex justify-center"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeInLeft",
      expression: "'animate__animated animate__fadeInLeft'"
    }],
    staticClass: "w1000 flex align-center"
  }, [_c("img", {
    staticClass: "mr20",
    attrs: {
      width: "378",
      src: __webpack_require__(/*! ../../assets/images/sz/1.png */ "./src/assets/images/sz/1.png")
    }
  }), _c("div", {
    staticClass: "flex flex-col mr20"
  }, [_c("svg-icon", {
    staticClass: "w130 h60 mb20",
    attrs: {
      "icon-class": "ai-t-1"
    }
  }), _vm._m(2)], 1)])]), _c("div", {
    staticClass: "pb100 flex justify-center"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeInRight",
      expression: "'animate__animated animate__fadeInRight'"
    }],
    staticClass: "w1000 flex justify-end align-center"
  }, [_c("div", {
    staticClass: "flex flex-col mr20"
  }, [_c("svg-icon", {
    staticClass: "w300 h60 mb20",
    attrs: {
      "icon-class": "ai-t-2"
    }
  }), _vm._m(3)], 1), _c("img", {
    attrs: {
      width: "355",
      src: __webpack_require__(/*! ../../assets/images/sz/2.png */ "./src/assets/images/sz/2.png")
    }
  })])]), _c("div", {
    staticClass: "pb100 flex justify-center"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeInLeft",
      expression: "'animate__animated animate__fadeInLeft'"
    }],
    staticClass: "w1000 flex align-center"
  }, [_c("img", {
    staticClass: "mr20",
    attrs: {
      width: "355",
      src: __webpack_require__(/*! ../../assets/images/sz/3.png */ "./src/assets/images/sz/3.png")
    }
  }), _c("div", {
    staticClass: "flex flex-col ml20"
  }, [_c("svg-icon", {
    staticClass: "w300 h60 mb20",
    attrs: {
      "icon-class": "ai-t-3"
    }
  }), _vm._m(4)], 1)])]), _c("div", {
    staticClass: "pt50 pb100 flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex flex-col align-center bg-1"
  }, [_c("div", {
    staticClass: "w flex justify-around align-center"
  }, [_vm._m(5), _c("img", {
    staticClass: "h50",
    attrs: {
      id: "gif",
      src: __webpack_require__(/*! ../../assets/images/arrow.gif */ "./src/assets/images/arrow.gif")
    }
  }), _c("div", {
    staticClass: "flex flex-col align-center align-self-star"
  }, [_c("div", {
    staticClass: "w mb20 flex justify-between"
  }, [_c("div", {
    staticClass: "w310 h200"
  }, [_c("video-js", {
    ref: "video",
    attrs: {
      options: _vm.videoOptions
    }
  })], 1)])])]), _vm._m(6)])]), _c("div", {
    staticClass: "sz-bg-box flex justify-center align-center"
  }, [_c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex flex-col justify-between"
  }, [_c("div", {
    staticClass: "flex flex-col tl"
  }, [_c("svg-icon", {
    staticClass: "w270 h120",
    attrs: {
      "icon-class": "sz-text-2"
    }
  })], 1), _c("div", {
    staticClass: "w flex justify-between mb40"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-100",
      expression: "'animate__animated animate__fadeIn animate__delay-100'"
    }],
    staticClass: "tag-box flex flex-col justify-between align-start"
  }, [_c("svg-icon", {
    staticClass: "w100 h100 mb80",
    attrs: {
      "icon-class": "sz-icon-1"
    }
  }), _vm._m(7)], 1), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-300",
      expression: "'animate__animated animate__fadeIn animate__delay-300'"
    }],
    staticClass: "tag-box flex flex-col justify-between align-start"
  }, [_c("svg-icon", {
    staticClass: "w100 h100 mb80",
    attrs: {
      "icon-class": "sz-icon-2"
    }
  }), _vm._m(8)], 1)]), _c("div", {
    staticClass: "w flex justify-between"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-500",
      expression: "'animate__animated animate__fadeIn animate__delay-500'"
    }],
    staticClass: "tag-box flex flex-col justify-between align-start"
  }, [_c("svg-icon", {
    staticClass: "w100 h100 mb80",
    attrs: {
      "icon-class": "sz-icon-3"
    }
  }), _vm._m(9)], 1), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-700",
      expression: "'animate__animated animate__fadeIn animate__delay-700'"
    }],
    staticClass: "tag-box flex flex-col justify-between align-start"
  }, [_c("svg-icon", {
    staticClass: "w100 h100 mb80",
    attrs: {
      "icon-class": "sz-icon-4"
    }
  }), _vm._m(10)], 1)])])])])]), _c("div", {
    staticClass: "h-5 content"
  }, [_c("div", {
    staticClass: "pt30 pb30 flex justify-center"
  }, [_c("div", {
    staticClass: "w tc"
  }, [_c("svg-icon", {
    staticClass: "w320 h46",
    attrs: {
      "icon-class": "sz-text-1"
    }
  })], 1)]), _c("div", {
    staticClass: "mb30 flex justify-center"
  }, [_c("div", {
    staticClass: "w flex flex-col justify-center align-center"
  }, [_c("svg-icon", {
    staticClass: "w80 h36 mb20",
    attrs: {
      "icon-class": "ai-t-1"
    }
  }), _vm._m(11), _c("img", {
    staticClass: "w380 h220",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sz/1.png */ "./src/assets/images/sz/1.png")
    }
  })], 1)]), _c("div", {
    staticClass: "mb30 flex justify-center"
  }, [_c("div", {
    staticClass: "w flex flex-col justify-center align-center"
  }, [_c("div", {
    staticClass: "flex flex-col align-center"
  }, [_c("svg-icon", {
    staticClass: "w180 h36 mb20",
    attrs: {
      "icon-class": "ai-t-2"
    }
  }), _vm._m(12)], 1), _c("img", {
    staticClass: "w350 h220 mt10",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sz/2.png */ "./src/assets/images/sz/2.png")
    }
  })])]), _c("div", {
    staticClass: "mb30 flex justify-center"
  }, [_c("div", {
    staticClass: "w flex flex-col justify-center align-center"
  }, [_c("div", {
    staticClass: "flex flex-col align-center"
  }, [_c("svg-icon", {
    staticClass: "w180 h36 mb20",
    attrs: {
      "icon-class": "ai-t-3"
    }
  }), _vm._m(13)], 1), _c("img", {
    staticClass: "w350 h220 mt10 b-r-17",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sz/3.png */ "./src/assets/images/sz/3.png")
    }
  })])]), _c("div", {
    staticClass: "pt50 flex justify-center pr10 pl10 box-border"
  }, [_c("div", {
    staticClass: "w flex flex-col align-center bg-1"
  }, [_c("div", {
    staticClass: "w flex flex-col align-center"
  }, [_vm._m(14), _c("svg-icon", {
    staticClass: "w80 h54 mt20 mb20",
    attrs: {
      "icon-class": "ai-m-arrow-down"
    }
  }), _c("div", {
    staticClass: "w flex flex-col align-center align-self-star"
  }, [_c("div", {
    staticClass: "w mb20 flex justify-between"
  }, [_c("div", {
    staticClass: "w h200"
  }, [_c("video-js", {
    ref: "video",
    attrs: {
      options: _vm.videoOptions
    }
  })], 1)])]), _c("div", {
    staticClass: "w color333 f15 fb mb20"
  }, [_vm._v("AI数字化内容")])], 1)])]), _c("div", {
    staticClass: "sz-bg-box flex justify-center align-center"
  }, [_c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w flex flex-col justify-between"
  }, [_c("div", {
    staticClass: "flex flex-col tl mb10"
  }, [_c("svg-icon", {
    staticClass: "w110 h40",
    attrs: {
      "icon-class": "sz-text-2"
    }
  })], 1), _c("div", {
    staticClass: "w flex flex-col"
  }, [_c("div", {
    staticClass: "tag-box flex flex-col justify-between align-start"
  }, [_c("svg-icon", {
    staticClass: "w70 h70 mb40",
    attrs: {
      "icon-class": "sz-icon-1"
    }
  }), _vm._m(15)], 1), _c("div", {
    staticClass: "tag-box flex flex-col justify-between align-start"
  }, [_c("svg-icon", {
    staticClass: "w70 h70 mb40",
    attrs: {
      "icon-class": "sz-icon-2"
    }
  }), _vm._m(16)], 1)]), _c("div", {
    staticClass: "w flex flex-col"
  }, [_c("div", {
    staticClass: "tag-box flex flex-col justify-between align-start"
  }, [_c("svg-icon", {
    staticClass: "w70 h70 mb40",
    attrs: {
      "icon-class": "sz-icon-3"
    }
  }), _vm._m(17)], 1), _c("div", {
    staticClass: "tag-box flex flex-col justify-between align-start"
  }, [_c("svg-icon", {
    staticClass: "w70 h70 mb40",
    attrs: {
      "icon-class": "sz-icon-4"
    }
  }), _vm._m(18)], 1)])])])])])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "banner-title"
  }, [_c("div", {
    staticClass: "banner-title-weight animate__animated animate__fadeIn"
  }, [_vm._v("科技让教育更鲜活")]), _c("div", {
    staticClass: "banner-title-item animate__animated animate__fadeIn animate__delay-100"
  }, [_vm._v("为孩子打造更立体有趣的学习体验")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "banner-nav animate__animated animate__fadeIn animate__delay-200"
  }, [_c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 优质输出 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 数字化高质量供给 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 创新呈现 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 技术重塑传统出版物 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 双效俱佳 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 纸数一体融合出版 ")])])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("ul", [_c("li", {
    staticClass: "li-style f18 fb"
  }, [_vm._v("依托"), _c("span", {
    staticClass: "color1 f27"
  }, [_vm._v("出版社")]), _vm._v("的纸质出版物。")]), _c("li", {
    staticClass: "li-style f18 fb"
  }, [_vm._v("保障"), _c("span", {
    staticClass: "color1 f27"
  }, [_vm._v("数字化课程")]), _vm._v("内容科学权威。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("ul", [_c("li", {
    staticClass: "li-style f18 fb"
  }, [_c("span", {
    staticClass: "color2 f27"
  }, [_vm._v("双师")]), _vm._v("轻课模式，赋能教学全环节。")]), _c("li", {
    staticClass: "li-style f18 fb"
  }, [_c("span", {
    staticClass: "color2 f27"
  }, [_vm._v("一站式")]), _vm._v("云平台，统筹管理。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("ul", [_c("li", {
    staticClass: "li-style f18 fb"
  }, [_c("span", {
    staticClass: "color3 f27"
  }, [_vm._v("动画")]), _vm._v("贯穿演绎、故事化情景呈现，亲近学生。")]), _c("li", {
    staticClass: "li-style f18 fb"
  }, [_vm._v("采用电影级制作，规模化供给高品质输出。")]), _c("li", {
    staticClass: "li-style f18 fb"
  }, [_vm._v("构建知识体系，丰富教学内涵。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w450 flex flex-col align-center align-self-end"
  }, [_c("div", {
    staticClass: "w mb20 flex justify-around"
  }, [_c("img", {
    attrs: {
      width: "140",
      src: __webpack_require__(/*! ../../assets/images/sz/4.png */ "./src/assets/images/sz/4.png")
    }
  }), _c("div", {
    staticClass: "ml20 flex flex-col align-start justify-around"
  }, [_c("div", {
    staticClass: "f12 color000"
  }, [_vm._v("《中华优秀传统文化》读本")]), _c("div", {
    staticClass: "f24 fb color000"
  }, [_vm._v("黑与白的和谐之道")]), _c("img", {
    attrs: {
      width: "250",
      src: __webpack_require__(/*! ../../assets/images/sz/5.png */ "./src/assets/images/sz/5.png")
    }
  })])])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w flex justify-around align-center mt10"
  }, [_c("div", {
    staticClass: "w500"
  }, [_vm._v("纸质出版物")]), _c("div", {
    staticClass: "w100"
  }), _c("div", {
    staticClass: "w350"
  }, [_vm._v("AI数字化内容")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "tl"
  }, [_c("div", {
    staticClass: "t-title"
  }, [_vm._v("增加学生学习兴趣")]), _c("div", {
    staticClass: "t-desc"
  }, [_vm._v("AI教师赋能学校教师授课、游戏化教学、沉浸式体验，增强学习兴趣。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "tl"
  }, [_c("div", {
    staticClass: "t-title"
  }, [_vm._v("降低教师教学难度")]), _c("div", {
    staticClass: "t-desc"
  }, [_vm._v("一键备课、一键上课，大幅减少教师教学工作量，最大限度为老师减负。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "tl"
  }, [_c("div", {
    staticClass: "t-title"
  }, [_vm._v("降低学校开课难度")]), _c("div", {
    staticClass: "t-desc"
  }, [_vm._v("赋能学校开设高质量传统文化等素质教育课程，补充学校思政教育。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "tl"
  }, [_c("div", {
    staticClass: "t-title"
  }, [_vm._v("快速在课堂上普及")]), _c("div", {
    staticClass: "t-desc"
  }, [_vm._v("纸数融合，快速高效开课。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("ul", [_c("li", {
    staticClass: "li-style f18 fb"
  }, [_vm._v("依托"), _c("span", {
    staticClass: "color1 f23"
  }, [_vm._v("出版社")]), _vm._v("的纸质出版物。")]), _c("li", {
    staticClass: "li-style f18 fb"
  }, [_vm._v("保障"), _c("span", {
    staticClass: "color1 f23"
  }, [_vm._v("数字化课程")]), _vm._v("内容科学权威。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("ul", [_c("li", {
    staticClass: "li-style f18 fb"
  }, [_c("span", {
    staticClass: "color2 f23"
  }, [_vm._v("双师")]), _vm._v("轻课模式，赋能教学全环节。")]), _c("li", {
    staticClass: "li-style f18 fb"
  }, [_c("span", {
    staticClass: "color2 f23"
  }, [_vm._v("一站式")]), _vm._v("云平台，统筹管理。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("ul", [_c("li", {
    staticClass: "li-style f16 fb"
  }, [_c("span", {
    staticClass: "color3 f23"
  }, [_vm._v("动画")]), _vm._v("贯穿演绎、故事化情景呈现，亲近学生。")]), _c("li", {
    staticClass: "li-style f16 fb"
  }, [_vm._v("采用电影级制作，规模化供给高品质输出。")]), _c("li", {
    staticClass: "li-style f16 fb"
  }, [_vm._v("构建知识体系，丰富教学内涵。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w flex flex-col align-center align-self-end"
  }, [_c("div", {
    staticClass: "w mb20 flex justify-around"
  }, [_c("img", {
    staticClass: "w140 h200",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sz/4.png */ "./src/assets/images/sz/4.png")
    }
  }), _c("div", {
    staticClass: "ml20 flex flex-col align-start justify-around"
  }, [_c("div", {
    staticClass: "f12 color000"
  }, [_vm._v("《中华优秀传统文化》读本")]), _c("div", {
    staticClass: "f24 fb color000"
  }, [_vm._v("黑与白的和谐之道")]), _c("img", {
    staticClass: "w200 h120",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sz/5.png */ "./src/assets/images/sz/5.png")
    }
  })])]), _c("div", {
    staticClass: "w color333 f15 fb"
  }, [_vm._v("纸质出版物")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "tl"
  }, [_c("div", {
    staticClass: "t-title"
  }, [_vm._v("增加学生学习兴趣")]), _c("div", {
    staticClass: "t-desc"
  }, [_vm._v("AI教师赋能学校教师授课、游戏化教学、沉浸式体验，增强学习兴趣。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "tl"
  }, [_c("div", {
    staticClass: "t-title"
  }, [_vm._v("降低教师教学难度")]), _c("div", {
    staticClass: "t-desc"
  }, [_vm._v("一键备课、一键上课，大幅减少教师教学工作量，最大限度为老师减负。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "tl"
  }, [_c("div", {
    staticClass: "t-title"
  }, [_vm._v("降低学校开课难度")]), _c("div", {
    staticClass: "t-desc"
  }, [_vm._v("赋能学校开设高质量传统文化等素质教育课程，补充学校思政教育。")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "tl"
  }, [_c("div", {
    staticClass: "t-title"
  }, [_vm._v("快速在课堂上普及")]), _c("div", {
    staticClass: "t-desc"
  }, [_vm._v("纸数融合，快速高效开课。")])]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true&":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--9-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/getUrl.js */ "./node_modules/css-loader/dist/runtime/getUrl.js");
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(/*! ../../assets/images/sz/bg-1.png */ "./src/assets/images/sz/bg-1.png");
var ___CSS_LOADER_URL_IMPORT_1___ = __webpack_require__(/*! ../../assets/images/sz/bg-2.jpg */ "./src/assets/images/sz/bg-2.jpg");
exports = ___CSS_LOADER_API_IMPORT___(true);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
var ___CSS_LOADER_URL_REPLACEMENT_1___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_1___);
// Module
exports.push([module.i, "@media screen and (min-width: 769px) {\n.bg-color-box[data-v-5d035cf0] {\n    width: 100%;\n    height: 620px;\n    padding-top: 80px;\n    padding-bottom: 30px;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n}\n.bg-color-box .banner-title-box[data-v-5d035cf0] {\n      width: 1000px;\n      height: calc(100% - 56px);\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n}\n.bg-color-box .banner-title-box .banner-title[data-v-5d035cf0] {\n        display: flex;\n        flex-direction: column;\n        align-items: flex-start;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-weight[data-v-5d035cf0] {\n          font-family: 'PingFang SC';\n          font-weight: 500;\n          font-size: 50px;\n          margin-bottom: 16px;\n          color: #041128;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-item[data-v-5d035cf0] {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 28px;\n          color: #333333;\n}\n.bg-color-box .banner-nav[data-v-5d035cf0] {\n      height: 56px;\n      width: 1000px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-nav .item-box[data-v-5d035cf0] {\n        width: 175px;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n}\n.bg-color-box .banner-nav .item-box .item-title[data-v-5d035cf0] {\n          color: #333333;\n          font-weight: 500;\n          font-size: 23px;\n          margin-bottom: 3px;\n}\n.bg-color-box .banner-nav .item-box .item-desc[data-v-5d035cf0] {\n          color: #4F4F4F;\n          font-size: 15px;\n}\n.bg-color-box .banner-nav .item-line[data-v-5d035cf0] {\n        width: 1px;\n        height: 60%;\n        background: rgba(0, 0, 0, 0.3);\n}\n}\n@media screen and (max-width: 768px) {\n.bg-color-box[data-v-5d035cf0] {\n    width: 100%;\n    height: 90.69767vw;\n    padding-bottom: 4.65116vw;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n}\n.bg-color-box .banner-title-box[data-v-5d035cf0] {\n      width: 100%;\n      height: calc(100% - 9.30233vw);\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-title-box .banner-title[data-v-5d035cf0] {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        padding-top: 6.97674vw;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-weight[data-v-5d035cf0] {\n          font-family: 'PingFang SC';\n          font-weight: 600;\n          font-size: 5.5814vw;\n          margin-bottom: 3.72093vw;\n          color: #000000;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-item[data-v-5d035cf0] {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 3.72093vw;\n          color: #000000;\n}\n.bg-color-box .banner-nav[data-v-5d035cf0] {\n      height: 9.30233vw;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-nav .item-box[data-v-5d035cf0] {\n        width: 25.34884vw;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n}\n.bg-color-box .banner-nav .item-box .item-title[data-v-5d035cf0] {\n          color: #333333;\n          font-weight: 500;\n          font-size: 3.72093vw;\n          margin-bottom: 0.69767vw;\n}\n.bg-color-box .banner-nav .item-box .item-desc[data-v-5d035cf0] {\n          color: #4F4F4F;\n          font-size: 2.32558vw;\n}\n.bg-color-box .banner-nav .item-line[data-v-5d035cf0] {\n        width: 1px;\n        height: 60%;\n        background: rgba(0, 0, 0, 0.3);\n}\n}\n.vjs-poster[data-v-5d035cf0] {\n  background-size: cover !important;\n}\n.bg-color-box[data-v-5d035cf0] {\n  background: linear-gradient(90deg, #529DFF 0%, #30FAD2 100%);\n}\n@media screen and (min-width: 769px) {\n.h-5[data-v-5d035cf0] {\n    display: none;\n}\n.sky-img[data-v-5d035cf0] {\n    width: 480px;\n    height: 300px;\n}\n.content[data-v-5d035cf0] {\n    width: 100%;\n}\n.content .color333[data-v-5d035cf0] {\n      color: #333333;\n}\n.content .color000[data-v-5d035cf0] {\n      color: #000000;\n}\n.content .color-text[data-v-5d035cf0] {\n      color: #828282;\n}\n.content .color-white[data-v-5d035cf0] {\n      color: #fff;\n}\n.content .color-d9[data-v-5d035cf0] {\n      color: #BB6BD9;\n}\n.content .f35[data-v-5d035cf0] {\n      font-size: 35px;\n}\n.content .mb250[data-v-5d035cf0] {\n      margin-bottom: 250px;\n}\n.content .mb200[data-v-5d035cf0] {\n      margin-bottom: 200px;\n}\n.content .sz-tag1[data-v-5d035cf0], .content .sz-tag2[data-v-5d035cf0], .content .sz-tag3[data-v-5d035cf0] {\n      padding: 5px;\n      border-radius: 5px;\n      font-size: 26px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n}\n.content .sz-tag1[data-v-5d035cf0] {\n      color: #56CCF2;\n      border: 2px solid #56CCF2;\n}\n.content .sz-tag2[data-v-5d035cf0] {\n      color: #EB5757;\n      border: 2px solid #EB5757;\n}\n.content .sz-tag3[data-v-5d035cf0] {\n      color: #27AE60;\n      border: 2px solid #27AE60;\n}\n.content .color1[data-v-5d035cf0] {\n      color: #2D9CDB;\n}\n.content .color2[data-v-5d035cf0] {\n      color: #EB5757;\n}\n.content .color3[data-v-5d035cf0] {\n      color: #27AE60;\n}\n.content .li-style[data-v-5d035cf0] {\n      list-style-type: disc;\n      margin: 10px 0;\n      text-align: left;\n}\n.content .bg-1[data-v-5d035cf0] {\n      background: url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ") no-repeat;\n      background-size: contain;\n      width: 1000px;\n      height: 480px;\n      box-sizing: border-box;\n      padding: 120px 20px 20px 10px;\n}\n.content .sz-bg-box[data-v-5d035cf0] {\n      width: 100%;\n      height: 1200px;\n      background: url(" + ___CSS_LOADER_URL_REPLACEMENT_1___ + ") no-repeat;\n      background-size: cover;\n}\n.content .tag-box[data-v-5d035cf0] {\n      width: 49%;\n      height: 367.5px;\n      background: rgba(0, 0, 0, 0.82);\n      border-radius: 22.5px;\n      box-sizing: border-box;\n      padding: 50px 15px;\n}\n.content .tag-box .t-title[data-v-5d035cf0] {\n        font-weight: 500;\n        font-size: 36px;\n        color: #fff;\n        margin-bottom: 10px;\n}\n.content .tag-box .t-desc[data-v-5d035cf0] {\n        font-weight: 500;\n        font-size: 14px;\n        color: #BDBDBD;\n}\n}\n@media screen and (max-width: 768px) {\n.computer[data-v-5d035cf0] {\n    display: none;\n}\n.bg-color-box .banner-title[data-v-5d035cf0] {\n    padding-top: 0 !important;\n}\n.bg-color-box .banner-nav .item-box[data-v-5d035cf0] {\n    width: 34.88372vw !important;\n}\n.h220[data-v-5d035cf0] {\n    height: 53.48837vw;\n}\n.sky-img[data-v-5d035cf0] {\n    width: 65.11628vw;\n    height: 41.86047vw;\n}\n.content[data-v-5d035cf0] {\n    width: 100%;\n}\n.content .color333[data-v-5d035cf0] {\n      color: #333333;\n}\n.content .color000[data-v-5d035cf0] {\n      color: #000000;\n}\n.content .color-text[data-v-5d035cf0] {\n      color: #828282;\n}\n.content .color-white[data-v-5d035cf0] {\n      color: #fff;\n}\n.content .color-d9[data-v-5d035cf0] {\n      color: #BB6BD9;\n}\n.content .sz-tag1[data-v-5d035cf0], .content .sz-tag2[data-v-5d035cf0], .content .sz-tag3[data-v-5d035cf0] {\n      padding: 5px;\n      border-radius: 5px;\n      font-size: 26px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n}\n.content .sz-tag1[data-v-5d035cf0] {\n      color: #56CCF2;\n      border: 2px solid #56CCF2;\n}\n.content .sz-tag2[data-v-5d035cf0] {\n      color: #EB5757;\n      border: 2px solid #EB5757;\n}\n.content .sz-tag3[data-v-5d035cf0] {\n      color: #27AE60;\n      border: 2px solid #27AE60;\n}\n.content .color1[data-v-5d035cf0] {\n      color: #56CCF2;\n}\n.content .color2[data-v-5d035cf0] {\n      color: #EB5757;\n}\n.content .color3[data-v-5d035cf0] {\n      color: #27AE60;\n}\n.content .li-style[data-v-5d035cf0] {\n      list-style-type: disc;\n      margin: 10px 0;\n      text-align: left;\n}\n.content .b-r-17[data-v-5d035cf0] {\n      border-radius: 3.95349vw;\n}\n.content .bg-1[data-v-5d035cf0] {\n      background: linear-gradient(180deg, #D9F6FF 20.96%, rgba(255, 255, 255, 0) 100%);\n      width: 100%;\n      box-sizing: border-box;\n      padding: 6.97674vw;\n      padding-bottom: 2.32558vw;\n      border-radius: 3.95349vw;\n}\n.content .sz-bg-box[data-v-5d035cf0] {\n      width: 100%;\n      padding: 4.65116vw 2.32558vw;\n      box-sizing: border-box;\n      background: url(" + ___CSS_LOADER_URL_REPLACEMENT_1___ + ") no-repeat;\n      background-size: cover;\n}\n.content .tag-box[data-v-5d035cf0] {\n      width: 100%;\n      background: rgba(0, 0, 0, 0.82);\n      border-radius: 3.48837vw;\n      box-sizing: border-box;\n      padding: 6.97674vw 3.48837vw;\n      margin-bottom: 4.65116vw;\n}\n.content .tag-box .t-title[data-v-5d035cf0] {\n        font-weight: 500;\n        font-size: 4.65116vw;\n        color: #fff;\n        margin-bottom: 4.65116vw;\n}\n.content .tag-box .t-desc[data-v-5d035cf0] {\n        font-weight: 500;\n        font-size: 3.25581vw;\n        color: #BDBDBD;\n}\n}\n", "",{"version":3,"sources":["/Users/<USER>/Documents/cuiya官网/cuiya_official_website/src/styles/global.scss","/Users/<USER>/Documents/cuiya官网/cuiya_official_website/src/views/shuzi/index.vue"],"names":[],"mappings":"AA6BA;AAEE;IACE,WAAW;IACX,aAAa;IACb,iBATgB;IAUhB,oBAAoB;IACpB,sBAAsB;IACtB,aAAa;IACb,sBAAsB;IACtB,mBAAmB;IACnB,uBAAuB;AAAA;AATzB;MAYI,aAAa;MACb,yBAAwC;MACxC,aAAa;MACb,8BAA8B;MAC9B,mBAAmB;AAAA;AAhBvB;QAmBM,aAAa;QACb,sBAAsB;QACtB,uBAAuB;AAAA;AArB7B;UAwBQ,0BAA0B;UAC1B,gBAAgB;UAChB,eAAe;UACf,mBAAmB;UACnB,cAAc;AAAA;AA5BtB;UA+BQ,0BAA0B;UAC1B,gBAAgB;UAChB,eAAe;UACf,cAAc;AAAA;AAlCtB;MAwCI,YA7CgB;MA8ChB,aAAa;MACb,aAAa;MACb,uBAAuB;MACvB,mBAAmB;AAAA;AA5CvB;QA+CM,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;AAAA;AApDzB;UAsDQ,cAAc;UACd,gBAAgB;UAChB,eAAe;UACf,kBAAkB;AAAA;AAzD1B;UA4DQ,cAAc;UACd,eAAe;AAAA;AA7DvB;QAiEM,UAAU;QACV,WAAW;QACX,8BAAgC;AAAA;AACjC;AAIP;AAEE;IACE,WAAW;IACX,kBArFqC;IAsFrC,yBAtFqC;IAuFrC,sBAAsB;IACtB,aAAa;IACb,sBAAsB;IACtB,mBAAmB;IACnB,uBAAuB;AAAA;AARzB;MAWI,WAAW;MACX,8BAAyC;MACzC,aAAa;MACb,sBAAsB;MACtB,uBAAuB;MACvB,mBAAmB;AAAA;AAhBvB;QAmBM,aAAa;QACb,sBAAsB;QACtB,mBAAmB;QACnB,sBAzGiC;AAAA;AAmFvC;UAyBQ,0BAA0B;UAC1B,gBAAgB;UAChB,mBA9G+B;UA+G/B,wBA/G+B;UAgH/B,cAAc;AAAA;AA7BtB;UAgCQ,0BAA0B;UAC1B,gBAAgB;UAChB,oBArH+B;UAsH/B,cAAc;AAAA;AAnCtB;MAyCI,iBA5HmC;MA6HnC,WAAW;MACX,aAAa;MACb,uBAAuB;MACvB,mBAAmB;AAAA;AA7CvB;QAgDM,iBAnIiC;QAoIjC,YAAY;QACZ,aAAa;QACb,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;AAAA;AArDzB;UAuDQ,cAAc;UACd,gBAAgB;UAChB,oBA5I+B;UA6I/B,wBA7I+B;AAAA;AAmFvC;UA6DQ,cAAc;UACd,oBAjJ+B;AAAA;AAmFvC;QAkEM,UAAU;QACV,WAAW;QACX,8BAAgC;AAAA;AACjC;AAKP;EACE,iCAAiC;AAAA;AC/KnC;EACE,4DAA4D;AAAA;AAE9D;AACE;IACE,aAAa;AAAA;AAGf;IACE,YAAY;IACZ,aAAa;AAAA;AAGf;IACE,WAAW;AAAA;AADb;MAII,cAAc;AAAA;AAJlB;MAQI,cAAc;AAAA;AARlB;MAYI,cAAc;AAAA;AAZlB;MAeI,WAAW;AAAA;AAff;MAmBI,cAAc;AAAA;AAnBlB;MAsBI,eAAe;AAAA;AAtBnB;MA0BI,oBAAoB;AAAA;AA1BxB;MA8BI,oBAAoB;AAAA;AA9BxB;MAkCI,YAAY;MACZ,kBAAkB;MAClB,eAAe;MACf,aAAa;MACb,uBAAuB;MACvB,mBAAmB;AAAA;AAvCvB;MA2CI,cAAc;MACd,yBAAyB;AAAA;AA5C7B;MAgDI,cAAc;MACd,yBAAyB;AAAA;AAjD7B;MAqDI,cAAc;MACd,yBAAyB;AAAA;AAtD7B;MA0DI,cAAc;AAAA;AA1DlB;MA6DI,cAAc;AAAA;AA7DlB;MAgEI,cAAc;AAAA;AAhElB;MAoEI,qBAAqB;MACrB,cAAc;MACd,gBAAgB;AAAA;AAtEpB;MA0EI,6DAA4D;MAC5D,wBAAwB;MACxB,aAAa;MACb,aAAa;MACb,sBAAsB;MACtB,6BAA6B;AAAA;AA/EjC;MAmFI,WAAW;MACX,cAAc;MACd,6DAA4D;MAC5D,sBAAsB;AAAA;AAtF1B;MA0FI,UAAU;MACV,eAAe;MACf,+BAA+B;MAC/B,qBAAqB;MACrB,sBAAsB;MACtB,kBAAkB;AAAA;AA/FtB;QAkGM,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX,mBAAmB;AAAA;AArGzB;QAwGM,gBAAgB;QAChB,eAAe;QACf,cAAc;AAAA;AACf;AAIP;AACE;IACE,aAAa;AAAA;AAEf;IAEI,yBAAyB;AAAA;AAF7B;IAMM,4BAAyB;AAAA;AAK/B;IACE,kBD3HqC;AAAA;AC8HvC;IACE,iBD/HqC;ICgIrC,kBDhIqC;AAAA;ACmIvC;IACE,WAAW;AAAA;AADb;MAII,cAAc;AAAA;AAJlB;MAQI,cAAc;AAAA;AARlB;MAYI,cAAc;AAAA;AAZlB;MAeI,WAAW;AAAA;AAff;MAmBI,cAAc;AAAA;AAnBlB;MAuBI,YAAY;MACZ,kBAAkB;MAClB,eAAe;MACf,aAAa;MACb,uBAAuB;MACvB,mBAAmB;AAAA;AA5BvB;MAgCI,cAAc;MACd,yBAAyB;AAAA;AAjC7B;MAqCI,cAAc;MACd,yBAAyB;AAAA;AAtC7B;MA0CI,cAAc;MACd,yBAAyB;AAAA;AA3C7B;MA+CI,cAAc;AAAA;AA/ClB;MAkDI,cAAc;AAAA;AAlDlB;MAqDI,cAAc;AAAA;AArDlB;MAyDI,qBAAqB;MACrB,cAAc;MACd,gBAAgB;AAAA;AA3DpB;MA+DI,wBDlMmC;AAAA;ACmIvC;MAmEI,gFAAgF;MAChF,WAAW;MAEX,sBAAsB;MACtB,kBD1MmC;MC2MnC,yBD3MmC;MC4MnC,wBD5MmC;AAAA;ACmIvC;MA6EI,WAAW;MAEX,4BDlNmC;MCmNnC,sBAAsB;MACtB,6DAA4D;MAC5D,sBAAsB;AAAA;AAlF1B;MAsFI,WAAW;MAEX,+BAA+B;MAC/B,wBD5NmC;MC6NnC,sBAAsB;MACtB,4BD9NmC;MC+NnC,wBD/NmC;AAAA;ACmIvC;QA+FM,gBAAgB;QAChB,oBDnOiC;QCoOjC,WAAW;QACX,wBDrOiC;AAAA;ACmIvC;QAqGM,gBAAgB;QAChB,oBDzOiC;QC0OjC,cAAc;AAAA;AACf","file":"index.vue","sourcesContent":["@import './mixin.scss';\n\n//默认设计稿的宽度\n$designWidth: 1440;\n//默认设计稿的高度\n$designHeight: 810;\n\n//默认设计稿的宽度\n$designWidthH5: 430;\n\n//px转为vw的函数\n@function vw($px) {\n  @return $px * 100vw / #{$designWidth};\n}\n\n//px转为vh的函数\n@function vh($px) {\n  @return $px * 100vh / #{$designHeight};\n}\n\n//H5中px转为vw的函数\n@function pw($px) {\n  @return $px * 100vw / #{$designWidthH5};\n}\n\n$navTopPadding: 80px;\n$bannerNavHeight: 56px;\n$MbannerNavHeight: pw(40);\n\n@media screen and (min-width: 769px) {\n  // 通用首页banner样式\n  .bg-color-box {\n    width: 100%;\n    height: 620px;\n    padding-top: $navTopPadding;\n    padding-bottom: 30px;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n\n    .banner-title-box {\n      width: 1000px;\n      height: calc(100% - #{$bannerNavHeight});\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .banner-title {\n        display: flex;\n        flex-direction: column;\n        align-items: flex-start;\n\n        .banner-title-weight {\n          font-family: 'PingFang SC';\n          font-weight: 500;\n          font-size: 50px;\n          margin-bottom: 16px;\n          color: #041128;\n        }\n        .banner-title-item {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 28px;\n          color: #333333;\n        }\n      }\n    }\n\n    .banner-nav {\n      height: $bannerNavHeight;\n      width: 1000px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .item-box {\n        width: 175px;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        .item-title {\n          color: #333333;\n          font-weight: 500;\n          font-size: 23px;\n          margin-bottom: 3px;\n        }\n        .item-desc {\n          color: #4F4F4F;\n          font-size: 15px;\n        }\n      }\n      .item-line {\n        width: 1px;\n        height: 60%;\n        background: rgba($color: #000000, $alpha: 0.3);\n      }\n    }\n  }\n}\n@media screen and (max-width: 768px) {\n  // 通用首页banner样式\n  .bg-color-box {\n    width: 100%;\n    height: pw(390);\n    padding-bottom: pw(20);\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n\n    .banner-title-box {\n      width: 100%;\n      height: calc(100% - #{$MbannerNavHeight});\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n\n      .banner-title {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        padding-top: pw(30);\n\n        .banner-title-weight {\n          font-family: 'PingFang SC';\n          font-weight: 600;\n          font-size: pw(24);\n          margin-bottom: pw(16);\n          color: #000000;\n        }\n        .banner-title-item {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: pw(16);\n          color: #000000;\n        }\n      }\n    }\n\n    .banner-nav {\n      height: $MbannerNavHeight;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .item-box {\n        width: pw(109);\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        .item-title {\n          color: #333333;\n          font-weight: 500;\n          font-size: pw(16);\n          margin-bottom: pw(3);\n        }\n        .item-desc {\n          color: #4F4F4F;\n          font-size: pw(10);\n        }\n      }\n      .item-line {\n        width: 1px;\n        height: 60%;\n        background: rgba($color: #000000, $alpha: 0.3);\n      }\n    }\n  }\n}\n\n.vjs-poster {\n  background-size: cover !important;\n}","\n        @import \"@/styles/global.scss\";\n        $src: \"./src/assets\";\n        \n\n.bg-color-box {\n  background: linear-gradient(90deg, #529DFF 0%, #30FAD2 100%);\n}\n@media screen and (min-width: 769px) {\n  .h-5 {\n    display: none;\n  }\n\n  .sky-img {\n    width: 480px;\n    height: 300px;\n  }\n\n  .content {\n    width: 100%;\n\n    .color333 {\n      color: #333333;\n    }\n\n    .color000 {\n      color: #000000;\n    }\n\n    .color-text {\n      color: #828282;\n    }\n    .color-white {\n      color: #fff;\n    }\n\n    .color-d9 {\n      color: #BB6BD9;\n    }\n    .f35 {\n      font-size: 35px;\n    }\n\n    .mb250 {\n      margin-bottom: 250px;\n    }\n\n    .mb200 {\n      margin-bottom: 200px;\n    }\n\n    .sz-tag1, .sz-tag2, .sz-tag3 {\n      padding: 5px;\n      border-radius: 5px;\n      font-size: 26px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n\n    .sz-tag1 {\n      color: #56CCF2;\n      border: 2px solid #56CCF2;\n    }\n\n    .sz-tag2 {\n      color: #EB5757;\n      border: 2px solid #EB5757;\n    }\n\n    .sz-tag3 {\n      color: #27AE60;\n      border: 2px solid #27AE60;\n    }\n\n    .color1 {\n      color: #2D9CDB;\n    }\n    .color2 {\n      color: #EB5757;\n    }\n    .color3 {\n      color: #27AE60;\n    }\n\n    .li-style {\n      list-style-type: disc;\n      margin: 10px 0;\n      text-align: left;\n    }\n\n    .bg-1 {\n      background: url('../../assets/images/sz/bg-1.png') no-repeat;\n      background-size: contain;\n      width: 1000px;\n      height: 480px;\n      box-sizing: border-box;\n      padding: 120px 20px 20px 10px;\n    }\n\n    .sz-bg-box {\n      width: 100%;\n      height: 1200px;\n      background: url('../../assets/images/sz/bg-2.jpg') no-repeat;\n      background-size: cover;\n    }\n\n    .tag-box {\n      width: 49%;\n      height: 367.5px;\n      background: rgba(0, 0, 0, 0.82);\n      border-radius: 22.5px;\n      box-sizing: border-box;\n      padding: 50px 15px;\n\n      .t-title {\n        font-weight: 500;\n        font-size: 36px;\n        color: #fff;\n        margin-bottom: 10px;\n      }\n      .t-desc {\n        font-weight: 500;\n        font-size: 14px;\n        color: #BDBDBD;\n      }\n    }\n  }\n}\n@media screen and (max-width: 768px) {\n  .computer {\n    display: none;\n  }\n  .bg-color-box {\n    .banner-title {\n      padding-top: 0 !important;\n    }\n    .banner-nav {\n      .item-box {\n        width: pw(150) !important;\n      }\n    }\n  }\n\n  .h220 {\n    height: pw(230);\n  }\n\n  .sky-img {\n    width: pw(280);\n    height: pw(180);\n  }\n\n  .content {\n    width: 100%;\n\n    .color333 {\n      color: #333333;\n    }\n\n    .color000 {\n      color: #000000;\n    }\n\n    .color-text {\n      color: #828282;\n    }\n    .color-white {\n      color: #fff;\n    }\n\n    .color-d9 {\n      color: #BB6BD9;\n    }\n\n    .sz-tag1, .sz-tag2, .sz-tag3 {\n      padding: 5px;\n      border-radius: 5px;\n      font-size: 26px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n\n    .sz-tag1 {\n      color: #56CCF2;\n      border: 2px solid #56CCF2;\n    }\n\n    .sz-tag2 {\n      color: #EB5757;\n      border: 2px solid #EB5757;\n    }\n\n    .sz-tag3 {\n      color: #27AE60;\n      border: 2px solid #27AE60;\n    }\n\n    .color1 {\n      color: #56CCF2;\n    }\n    .color2 {\n      color: #EB5757;\n    }\n    .color3 {\n      color: #27AE60;\n    }\n\n    .li-style {\n      list-style-type: disc;\n      margin: 10px 0;\n      text-align: left;\n    }\n\n    .b-r-17 {\n      border-radius: pw(17);\n    }\n\n    .bg-1 {\n      background: linear-gradient(180deg, #D9F6FF 20.96%, rgba(255, 255, 255, 0) 100%);\n      width: 100%;\n      // height: pw(633);\n      box-sizing: border-box;\n      padding: pw(30);\n      padding-bottom: pw(10);\n      border-radius: pw(17);\n    }\n\n    .sz-bg-box {\n      width: 100%;\n      // height: pw(1200);\n      padding: pw(20) pw(10);\n      box-sizing: border-box;\n      background: url('../../assets/images/sz/bg-2.jpg') no-repeat;\n      background-size: cover;\n    }\n\n    .tag-box {\n      width: 100%;\n      // height: pw(360);\n      background: rgba(0, 0, 0, 0.82);\n      border-radius: pw(15);\n      box-sizing: border-box;\n      padding: pw(30) pw(15);\n      margin-bottom: pw(20);\n\n      .t-title {\n        font-weight: 500;\n        font-size: pw(20);\n        color: #fff;\n        margin-bottom: pw(20);\n      }\n      .t-desc {\n        font-weight: 500;\n        font-size: pw(14);\n        color: #BDBDBD;\n      }\n    }\n  }\n}\n\n"]}]);
// Exports
module.exports = exports;


/***/ }),

/***/ "./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true&":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader??ref--9-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--9-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true&");
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(/*! ../../../node_modules/vue-style-loader/lib/addStylesClient.js */ "./node_modules/vue-style-loader/lib/addStylesClient.js").default
var update = add("0a10c267", content, false, {"sourceMap":true,"shadowMode":false});
// Hot Module Replacement
if(true) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true&", function() {
     var newContent = __webpack_require__(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true&");
     if(newContent.__esModule) newContent = newContent.default;
     if(typeof newContent === 'string') newContent = [[module.i, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ "./src/assets/images/arrow.gif":
/*!*************************************!*\
  !*** ./src/assets/images/arrow.gif ***!
  \*************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/arrow.e88968ea.gif";

/***/ }),

/***/ "./src/assets/images/sz/1.png":
/*!************************************!*\
  !*** ./src/assets/images/sz/1.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/1.961af6fc.png";

/***/ }),

/***/ "./src/assets/images/sz/2.png":
/*!************************************!*\
  !*** ./src/assets/images/sz/2.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/2.45ac8f3c.png";

/***/ }),

/***/ "./src/assets/images/sz/3.png":
/*!************************************!*\
  !*** ./src/assets/images/sz/3.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/3.804cf246.png";

/***/ }),

/***/ "./src/assets/images/sz/4.png":
/*!************************************!*\
  !*** ./src/assets/images/sz/4.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/4.1cdcb742.png";

/***/ }),

/***/ "./src/assets/images/sz/5.png":
/*!************************************!*\
  !*** ./src/assets/images/sz/5.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/5.0a16c52c.png";

/***/ }),

/***/ "./src/assets/images/sz/6.png":
/*!************************************!*\
  !*** ./src/assets/images/sz/6.png ***!
  \************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/6.9f101eec.png";

/***/ }),

/***/ "./src/assets/images/sz/bg-1.png":
/*!***************************************!*\
  !*** ./src/assets/images/sz/bg-1.png ***!
  \***************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/bg-1.0318d428.png";

/***/ }),

/***/ "./src/assets/images/sz/bg-2.jpg":
/*!***************************************!*\
  !*** ./src/assets/images/sz/bg-2.jpg ***!
  \***************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/bg-2.3e7e872a.jpg";

/***/ }),

/***/ "./src/assets/video/v-1.mp4":
/*!**********************************!*\
  !*** ./src/assets/video/v-1.mp4 ***!
  \**********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/media/v-1.45268372.mp4";

/***/ }),

/***/ "./src/views/shuzi/index.vue":
/*!***********************************!*\
  !*** ./src/views/shuzi/index.vue ***!
  \***********************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=5d035cf0&scoped=true& */ "./src/views/shuzi/index.vue?vue&type=template&id=5d035cf0&scoped=true&");
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ "./src/views/shuzi/index.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _index_vue_vue_type_style_index_0_id_5d035cf0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true& */ "./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true&");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");






/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "5d035cf0",
  null
  
)

/* hot reload */
if (true) {
  var api = __webpack_require__(/*! ./node_modules/vue-hot-reload-api/dist/index.js */ "./node_modules/vue-hot-reload-api/dist/index.js")
  api.install(__webpack_require__(/*! vue */ "./node_modules/vue/dist/vue.runtime.esm.js"))
  if (api.compatible) {
    module.hot.accept()
    if (!api.isRecorded('5d035cf0')) {
      api.createRecord('5d035cf0', component.options)
    } else {
      api.reload('5d035cf0', component.options)
    }
    module.hot.accept(/*! ./index.vue?vue&type=template&id=5d035cf0&scoped=true& */ "./src/views/shuzi/index.vue?vue&type=template&id=5d035cf0&scoped=true&", function(__WEBPACK_OUTDATED_DEPENDENCIES__) { /* harmony import */ _index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=5d035cf0&scoped=true& */ "./src/views/shuzi/index.vue?vue&type=template&id=5d035cf0&scoped=true&");
(function () {
      api.rerender('5d035cf0', {
        render: _index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
        staticRenderFns: _index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]
      })
    })(__WEBPACK_OUTDATED_DEPENDENCIES__); }.bind(this))
  }
}
component.options.__file = "src/views/shuzi/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/views/shuzi/index.vue?vue&type=script&lang=js&":
/*!************************************************************!*\
  !*** ./src/views/shuzi/index.vue?vue&type=script&lang=js& ***!
  \************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/babel-loader/lib!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=script&lang=js& */ "./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true&":
/*!*********************************************************************************************!*\
  !*** ./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true& ***!
  \*********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5d035cf0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/vue-style-loader??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true& */ "./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=style&index=0&id=5d035cf0&lang=scss&scoped=true&");
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5d035cf0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5d035cf0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5d035cf0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5d035cf0_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ "./src/views/shuzi/index.vue?vue&type=template&id=5d035cf0&scoped=true&":
/*!******************************************************************************!*\
  !*** ./src/views/shuzi/index.vue?vue&type=template&id=5d035cf0&scoped=true& ***!
  \******************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/cache-loader/dist/cjs.js?{"cacheDirectory":"node_modules/.cache/vue-loader","cacheIdentifier":"1df0c205-vue-loader-template"}!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/babel-loader/lib!../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=template&id=5d035cf0&scoped=true& */ "./node_modules/cache-loader/dist/cjs.js?{\"cacheDirectory\":\"node_modules/.cache/vue-loader\",\"cacheIdentifier\":\"1df0c205-vue-loader-template\"}!./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/shuzi/index.vue?vue&type=template&id=5d035cf0&scoped=true&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_5d035cf0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);
//# sourceMappingURL=8.js.map