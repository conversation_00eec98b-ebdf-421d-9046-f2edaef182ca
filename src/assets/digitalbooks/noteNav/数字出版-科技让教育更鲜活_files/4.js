((typeof self !== 'undefined' ? self : this)["webpackJsonp"] = (typeof self !== 'undefined' ? self : this)["webpackJsonp"] || []).push([[4],{

/***/ "./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=script&lang=js&":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/cache-loader/dist/cjs.js??ref--13-0!./node_modules/babel-loader/lib!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/skyclass/index.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _assets_images_sky_ctwh_png__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/assets/images/sky/ctwh.png */ "./src/assets/images/sky/ctwh.png");
/* harmony import */ var _assets_images_sky_ctwh_png__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_ctwh_png__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _assets_images_sky_zhys_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/assets/images/sky/zhys.png */ "./src/assets/images/sky/zhys.png");
/* harmony import */ var _assets_images_sky_zhys_png__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_zhys_png__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _assets_images_sky_kxsy_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/assets/images/sky/kxsy.png */ "./src/assets/images/sky/kxsy.png");
/* harmony import */ var _assets_images_sky_kxsy_png__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_kxsy_png__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _assets_images_sky_aqjy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/sky/aqjy.png */ "./src/assets/images/sky/aqjy.png");
/* harmony import */ var _assets_images_sky_aqjy_png__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_aqjy_png__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _assets_images_sky_mzfz_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/sky/mzfz.png */ "./src/assets/images/sky/mzfz.png");
/* harmony import */ var _assets_images_sky_mzfz_png__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_mzfz_png__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _assets_images_sky_fnkx_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/sky/fnkx.png */ "./src/assets/images/sky/fnkx.png");
/* harmony import */ var _assets_images_sky_fnkx_png__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_fnkx_png__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _assets_images_sky_whcc_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/sky/whcc.png */ "./src/assets/images/sky/whcc.png");
/* harmony import */ var _assets_images_sky_whcc_png__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_whcc_png__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _assets_images_sky_ldsj_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/sky/ldsj.png */ "./src/assets/images/sky/ldsj.png");
/* harmony import */ var _assets_images_sky_ldsj_png__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_ldsj_png__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _assets_images_sky_Group1_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/sky/Group1.png */ "./src/assets/images/sky/Group1.png");
/* harmony import */ var _assets_images_sky_Group1_png__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_Group1_png__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _assets_images_sky_Group2_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/assets/images/sky/Group2.png */ "./src/assets/images/sky/Group2.png");
/* harmony import */ var _assets_images_sky_Group2_png__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_Group2_png__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _assets_images_sky_Group3_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/assets/images/sky/Group3.png */ "./src/assets/images/sky/Group3.png");
/* harmony import */ var _assets_images_sky_Group3_png__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_Group3_png__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _assets_images_sky_Group4_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/assets/images/sky/Group4.png */ "./src/assets/images/sky/Group4.png");
/* harmony import */ var _assets_images_sky_Group4_png__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_Group4_png__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var _assets_images_sky_Group5_png__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/assets/images/sky/Group5.png */ "./src/assets/images/sky/Group5.png");
/* harmony import */ var _assets_images_sky_Group5_png__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_Group5_png__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var _assets_images_sky_Group6_png__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/assets/images/sky/Group6.png */ "./src/assets/images/sky/Group6.png");
/* harmony import */ var _assets_images_sky_Group6_png__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_Group6_png__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var _assets_images_sky_Group7_png__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/assets/images/sky/Group7.png */ "./src/assets/images/sky/Group7.png");
/* harmony import */ var _assets_images_sky_Group7_png__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_Group7_png__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var _assets_images_sky_Group8_png__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/assets/images/sky/Group8.png */ "./src/assets/images/sky/Group8.png");
/* harmony import */ var _assets_images_sky_Group8_png__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_assets_images_sky_Group8_png__WEBPACK_IMPORTED_MODULE_15__);
/* harmony import */ var _utils_index_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/index.js */ "./src/utils/index.js");

















/* harmony default export */ __webpack_exports__["default"] = ({
  inject: ['setFooterState', 'setNavFontState', 'setNavState'],
  data() {
    return {
      tagActiveIndex: 0,
      hoverImg: '',
      ctwh: (_assets_images_sky_ctwh_png__WEBPACK_IMPORTED_MODULE_0___default()),
      zhys: (_assets_images_sky_zhys_png__WEBPACK_IMPORTED_MODULE_1___default()),
      kxsy: (_assets_images_sky_kxsy_png__WEBPACK_IMPORTED_MODULE_2___default()),
      aqjy: (_assets_images_sky_aqjy_png__WEBPACK_IMPORTED_MODULE_3___default()),
      mzfz: (_assets_images_sky_mzfz_png__WEBPACK_IMPORTED_MODULE_4___default()),
      fnkx: (_assets_images_sky_fnkx_png__WEBPACK_IMPORTED_MODULE_5___default()),
      whcc: (_assets_images_sky_whcc_png__WEBPACK_IMPORTED_MODULE_6___default()),
      ldsj: (_assets_images_sky_ldsj_png__WEBPACK_IMPORTED_MODULE_7___default()),
      Group1: (_assets_images_sky_Group1_png__WEBPACK_IMPORTED_MODULE_8___default()),
      Group2: (_assets_images_sky_Group2_png__WEBPACK_IMPORTED_MODULE_9___default()),
      Group3: (_assets_images_sky_Group3_png__WEBPACK_IMPORTED_MODULE_10___default()),
      Group4: (_assets_images_sky_Group4_png__WEBPACK_IMPORTED_MODULE_11___default()),
      Group5: (_assets_images_sky_Group5_png__WEBPACK_IMPORTED_MODULE_12___default()),
      Group6: (_assets_images_sky_Group6_png__WEBPACK_IMPORTED_MODULE_13___default()),
      Group7: (_assets_images_sky_Group7_png__WEBPACK_IMPORTED_MODULE_14___default()),
      Group8: (_assets_images_sky_Group8_png__WEBPACK_IMPORTED_MODULE_15___default()),
      // 走马灯效果
      swiperOption: {
        loop: true,
        freeMode: true,
        autoplay: {
          delay: 0,
          disableOnInteraction: false
        },
        speed: 6000,
        spaceBetween: 35,
        // slidesPerGroup: 1,
        slidesPerView: 3
      },
      // 走马灯效果
      swiperOptionM: {
        loop: true,
        freeMode: true,
        autoplay: {
          delay: 0,
          disableOnInteraction: false
        },
        speed: 6000,
        spaceBetween: 10,
        slidesPerView: 2
      }
    };
  },
  mounted() {
    this.setFooterState(true);
    this.setNavState(1);
    this.setNavFontState(0);
    window.addEventListener('scroll', this.handleNavbarState);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleNavbarState);
    this.setNavState(1);
  },
  methods: {
    imgHover(val) {
      this.hoverImg = val;
    },
    handleNavbarState() {
      const documentTop = Object(_utils_index_js__WEBPACK_IMPORTED_MODULE_16__["getDocumentTop"])();
      if (documentTop < 10) {
        this.setNavState(1);
      } else {
        this.setNavState(0);
      }
    }
  }
});

/***/ }),

/***/ "./node_modules/cache-loader/dist/cjs.js?{\"cacheDirectory\":\"node_modules/.cache/vue-loader\",\"cacheIdentifier\":\"1df0c205-vue-loader-template\"}!./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=template&id=e8a88d58&scoped=true&":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/cache-loader/dist/cjs.js?{"cacheDirectory":"node_modules/.cache/vue-loader","cacheIdentifier":"1df0c205-vue-loader-template"}!./node_modules/cache-loader/dist/cjs.js??ref--13-0!./node_modules/babel-loader/lib!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/skyclass/index.vue?vue&type=template&id=e8a88d58&scoped=true& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w"
  }, [_c("div", {
    staticClass: "bg-color-box"
  }, [_c("div", {
    staticClass: "banner-title-box"
  }, [_vm._m(0), _c("svg-icon", {
    staticClass: "sky-img animate__animated animate__fadeIn animate__delay-100",
    attrs: {
      "icon-class": "sky-ip"
    }
  })], 1), _vm._m(1)]), _c("div", {
    staticClass: "content computer"
  }, [_c("div", {
    staticClass: "pt100 pb100 flex justify-center"
  }, [_c("div", {
    staticClass: "flex flex-col tl align-start w400 animate__animated animate__fadeInLeft"
  }, [_c("svg-icon", {
    staticClass: "w230 h80 mb40",
    attrs: {
      "icon-class": "sky-2"
    }
  }), _c("svg-icon", {
    staticClass: "w360 h120",
    attrs: {
      "icon-class": "sky-1"
    }
  }), _vm._m(2)], 1), _vm._m(3)]), _c("div", {
    staticClass: "flex flex-col align-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-end"
  }, [_c("div", {
    staticClass: "relative inline-block"
  }, [_c("img", {
    attrs: {
      width: "350",
      height: "180",
      src: __webpack_require__(/*! ../../assets/images/sky/sky-bg1.png */ "./src/assets/images/sky/sky-bg1.png")
    }
  }), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn",
      expression: "'animate__animated animate__fadeIn'"
    }],
    staticClass: "absolute flex justify-center align-center items-center"
  }, [_c("svg-icon", {
    staticClass: "h40 w330",
    attrs: {
      "icon-class": "sky-duoduan"
    }
  })], 1)])]), _c("div", {
    staticClass: "w1000 flex justify-between mb100"
  }, [_c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-300",
      expression: "'animate__animated animate__fadeIn animate__delay-300'"
    }],
    staticClass: "w30p relative img-hover"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-4.png */ "./src/assets/images/sky/sky-4.png")
    }
  }), _c("div", {
    staticClass: "absolute flex justify-center align-center items-center color-white f35 fb"
  }, [_vm._v(" 教学内容 ")])]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-400",
      expression: "'animate__animated animate__fadeIn animate__delay-400'"
    }],
    staticClass: "w30p relative img-hover"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-5.png */ "./src/assets/images/sky/sky-5.png")
    }
  }), _c("div", {
    staticClass: "absolute flex justify-center align-center items-center color-white f35 fb"
  }, [_vm._v(" 教学师资 ")])]), _c("div", {
    directives: [{
      name: "animate-onscroll",
      rawName: "v-animate-onscroll",
      value: "animate__animated animate__fadeIn animate__delay-500",
      expression: "'animate__animated animate__fadeIn animate__delay-500'"
    }],
    staticClass: "w30p relative img-hover"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-6.png */ "./src/assets/images/sky/sky-6.png")
    }
  }), _c("div", {
    staticClass: "absolute flex justify-center align-center items-center color-white f35 fb"
  }, [_vm._v(" 教学方法 ")])])]), _c("div", {
    staticClass: "w1000 flex justify-center"
  }, [_c("svg-icon", {
    staticClass: "w500 h60",
    attrs: {
      "icon-class": "sky-more"
    }
  })], 1), _c("div", {
    staticClass: "w1000 mb1 flex justify-around"
  }, [_c("div", {
    staticClass: "tag-item",
    class: _vm.tagActiveIndex === 0 ? "tag-item-active" : "",
    on: {
      mouseover: function ($event) {
        _vm.tagActiveIndex = 0;
      }
    }
  }, [_vm._v(" 学校端 ")]), _c("div", {
    staticClass: "tag-item",
    class: _vm.tagActiveIndex === 1 ? "tag-item-active" : "",
    on: {
      mouseover: function ($event) {
        _vm.tagActiveIndex = 1;
      }
    }
  }, [_vm._v(" 教师端 ")]), _c("div", {
    staticClass: "tag-item",
    class: _vm.tagActiveIndex === 2 ? "tag-item-active" : "",
    on: {
      mouseover: function ($event) {
        _vm.tagActiveIndex = 2;
      }
    }
  }, [_vm._v(" 助教端 ")])]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-center"
  }, [_c("img", {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: _vm.tagActiveIndex === 0,
      expression: "tagActiveIndex === 0"
    }],
    staticClass: "w h500 object-contain",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-7.png */ "./src/assets/images/sky/sky-7.png")
    }
  }), _c("img", {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: _vm.tagActiveIndex === 1,
      expression: "tagActiveIndex === 1"
    }],
    staticClass: "w h500 object-contain",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-8.png */ "./src/assets/images/sky/sky-8.png")
    }
  }), _c("img", {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: _vm.tagActiveIndex === 2,
      expression: "tagActiveIndex === 2"
    }],
    staticClass: "w h500 object-contain",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-9.png */ "./src/assets/images/sky/sky-9.png")
    }
  })])]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-center mt100 mb60"
  }, [_c("svg-icon", {
    staticClass: "w630 h120",
    attrs: {
      "icon-class": "sky-3"
    }
  })], 1)]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-center mb30",
    on: {
      mouseleave: function ($event) {
        _vm.hoverImg = "";
      }
    }
  }, [_c("img", {
    staticClass: "mr20 pointer",
    attrs: {
      width: _vm.hoverImg === "ctwh" ? 284 : 216,
      height: "216",
      src: _vm.hoverImg === "ctwh" ? _vm.Group1 : _vm.ctwh
    },
    on: {
      mouseover: function ($event) {
        return _vm.imgHover("ctwh");
      }
    }
  }), _c("img", {
    staticClass: "mr20 pointer",
    attrs: {
      width: _vm.hoverImg === "zhys" ? 284 : 216,
      height: "216",
      src: _vm.hoverImg === "zhys" ? _vm.Group2 : _vm.zhys
    },
    on: {
      mouseover: function ($event) {
        return _vm.imgHover("zhys");
      }
    }
  }), _c("img", {
    staticClass: "mr20 pointer",
    attrs: {
      width: _vm.hoverImg === "kxsy" ? 284 : 216,
      height: "216",
      src: _vm.hoverImg === "kxsy" ? _vm.Group3 : _vm.kxsy
    },
    on: {
      mouseover: function ($event) {
        return _vm.imgHover("kxsy");
      }
    }
  }), _c("img", {
    staticClass: "mr20 pointer",
    attrs: {
      width: _vm.hoverImg === "aqjy" ? 284 : 216,
      height: "216",
      src: _vm.hoverImg === "aqjy" ? _vm.Group4 : _vm.aqjy
    },
    on: {
      mouseover: function ($event) {
        return _vm.imgHover("aqjy");
      }
    }
  })])]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-center mb100",
    on: {
      mouseleave: function ($event) {
        _vm.hoverImg = "";
      }
    }
  }, [_c("img", {
    staticClass: "mr20 pointer",
    attrs: {
      width: _vm.hoverImg === "mzfz" ? 284 : 216,
      height: "216",
      src: _vm.hoverImg === "mzfz" ? _vm.Group5 : _vm.mzfz
    },
    on: {
      mouseover: function ($event) {
        return _vm.imgHover("mzfz");
      }
    }
  }), _c("img", {
    staticClass: "mr20 pointer",
    attrs: {
      width: _vm.hoverImg === "fnkx" ? 284 : 216,
      height: "216",
      src: _vm.hoverImg === "fnkx" ? _vm.Group6 : _vm.fnkx
    },
    on: {
      mouseover: function ($event) {
        return _vm.imgHover("fnkx");
      }
    }
  }), _c("img", {
    staticClass: "mr20 pointer",
    attrs: {
      width: _vm.hoverImg === "whcc" ? 284 : 216,
      height: "216",
      src: _vm.hoverImg === "whcc" ? _vm.Group7 : _vm.whcc
    },
    on: {
      mouseover: function ($event) {
        return _vm.imgHover("whcc");
      }
    }
  }), _c("img", {
    staticClass: "mr20 pointer",
    attrs: {
      width: _vm.hoverImg === "ldsj" ? 284 : 216,
      height: "216",
      src: _vm.hoverImg === "ldsj" ? _vm.Group8 : _vm.ldsj
    },
    on: {
      mouseover: function ($event) {
        return _vm.imgHover("ldsj");
      }
    }
  })])]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w1000 flex justify-center align-center mb50"
  }, [_c("svg-icon", {
    staticClass: "w40 h14",
    attrs: {
      "icon-class": "sky-right"
    }
  }), _c("div", {
    staticClass: "ml20 mr20 f35"
  }, [_vm._v("助力教育资源均衡发展")]), _c("svg-icon", {
    staticClass: "w40 h14",
    attrs: {
      "icon-class": "sky-left"
    }
  })], 1)]), _c("div", {
    staticClass: "w mb100"
  }, [_c("swiper", {
    staticClass: "swiper slinter",
    attrs: {
      options: _vm.swiperOption
    }
  }, [_c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-1.png */ "./src/assets/images/sky/sky-s-1.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-2.png */ "./src/assets/images/sky/sky-s-2.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-3.png */ "./src/assets/images/sky/sky-s-3.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-4.png */ "./src/assets/images/sky/sky-s-4.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-5.png */ "./src/assets/images/sky/sky-s-5.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-6.png */ "./src/assets/images/sky/sky-s-6.png")
    }
  })])], 1)], 1)])]), _c("div", {
    staticClass: "content h-5"
  }, [_c("div", {
    staticClass: "pt20 pb30 flex flex-col align-center w"
  }, [_c("div", {
    staticClass: "flex flex-col tc align-center w"
  }, [_c("svg-icon", {
    staticClass: "w130 h44 mb20",
    attrs: {
      "icon-class": "sky-2"
    }
  }), _c("svg-icon", {
    staticClass: "w200 h56",
    attrs: {
      "icon-class": "sky-m-1"
    }
  }), _c("img", {
    staticClass: "h240 mt20 mb20",
    staticStyle: {
      width: "95%"
    },
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-3.png */ "./src/assets/images/sky/sky-3.png")
    }
  }), _vm._m(4)], 1)]), _c("div", {
    staticClass: "w flex flex-col align-center"
  }, [_c("div", {
    staticClass: "w tc pb10"
  }, [_c("svg-icon", {
    staticClass: "h36 w170",
    attrs: {
      "icon-class": "sky-duoduan"
    }
  })], 1), _c("div", {
    staticClass: "w pl10 pr10 box-border flex justify-between mb40"
  }, [_c("el-row", {
    attrs: {
      gutter: 10
    }
  }, [_c("el-col", {
    attrs: {
      span: 8
    }
  }, [_c("div", {
    staticClass: "w relative"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-4.png */ "./src/assets/images/sky/sky-4.png")
    }
  }), _c("div", {
    staticClass: "absolute flex justify-center align-center items-center color-white f16 fb"
  }, [_vm._v(" 教学内容 ")])])]), _c("el-col", {
    attrs: {
      span: 8
    }
  }, [_c("div", {
    staticClass: "w relative"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-5.png */ "./src/assets/images/sky/sky-5.png")
    }
  }), _c("div", {
    staticClass: "absolute flex justify-center align-center items-center color-white f16 fb"
  }, [_vm._v(" 教学师资 ")])])]), _c("el-col", {
    attrs: {
      span: 8
    }
  }, [_c("div", {
    staticClass: "w relative"
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-6.png */ "./src/assets/images/sky/sky-6.png")
    }
  }), _c("div", {
    staticClass: "absolute flex justify-center align-center items-center color-white f16 fb"
  }, [_vm._v(" 教学方法 ")])])])], 1)], 1), _c("div", {
    staticClass: "w flex justify-center pb20"
  }, [_c("svg-icon", {
    staticClass: "w220 h30",
    attrs: {
      "icon-class": "sky-more"
    }
  })], 1), _c("div", {
    staticClass: "w pl10 pr10 mb10 box-border flex justify-around"
  }, [_c("div", {
    staticClass: "tag-item",
    class: _vm.tagActiveIndex === 0 ? "tag-item-active" : "",
    on: {
      click: function ($event) {
        _vm.tagActiveIndex = 0;
      },
      mouseover: function ($event) {
        _vm.tagActiveIndex = 0;
      }
    }
  }, [_vm._v(" 学校端 ")]), _c("div", {
    staticClass: "tag-item",
    class: _vm.tagActiveIndex === 1 ? "tag-item-active" : "",
    on: {
      click: function ($event) {
        _vm.tagActiveIndex = 1;
      },
      mouseover: function ($event) {
        _vm.tagActiveIndex = 1;
      }
    }
  }, [_vm._v(" 教师端 ")]), _c("div", {
    staticClass: "tag-item",
    class: _vm.tagActiveIndex === 2 ? "tag-item-active" : "",
    on: {
      click: function ($event) {
        _vm.tagActiveIndex = 2;
      },
      mouseover: function ($event) {
        _vm.tagActiveIndex = 2;
      }
    }
  }, [_vm._v(" 助教端 ")])]), _c("div", {
    staticClass: "w flex justify-center pl10 pr10 box-border"
  }, [_c("div", {
    staticClass: "w flex justify-center"
  }, [_c("img", {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: _vm.tagActiveIndex === 0,
      expression: "tagActiveIndex === 0"
    }],
    staticClass: "w h200",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-m-7.jpg */ "./src/assets/images/sky/sky-m-7.jpg")
    }
  }), _c("img", {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: _vm.tagActiveIndex === 1,
      expression: "tagActiveIndex === 1"
    }],
    staticClass: "w h200",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-m-8.jpg */ "./src/assets/images/sky/sky-m-8.jpg")
    }
  }), _c("img", {
    directives: [{
      name: "show",
      rawName: "v-show",
      value: _vm.tagActiveIndex === 2,
      expression: "tagActiveIndex === 2"
    }],
    staticClass: "w h200",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-m-9.jpg */ "./src/assets/images/sky/sky-m-9.jpg")
    }
  })])]), _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w flex justify-center mt50 mb20"
  }, [_c("svg-icon", {
    staticClass: "w280 h60",
    attrs: {
      "icon-class": "sky-3"
    }
  })], 1)]), _c("div", {
    staticClass: "w pl10 pr10 box-border"
  }, [_c("el-row", {
    attrs: {
      gutter: 10
    }
  }, [_c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("img", {
    staticClass: "w mb10",
    attrs: {
      src: _vm.Group1
    }
  })]), _c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("img", {
    staticClass: "w mb10",
    attrs: {
      src: _vm.Group2
    }
  })])], 1), _c("el-row", {
    attrs: {
      gutter: 10
    }
  }, [_c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("img", {
    staticClass: "w mb10",
    attrs: {
      src: _vm.Group3
    }
  })]), _c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("img", {
    staticClass: "w mb10",
    attrs: {
      src: _vm.Group4
    }
  })])], 1), _c("el-row", {
    attrs: {
      gutter: 10
    }
  }, [_c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("img", {
    staticClass: "w mb10",
    attrs: {
      src: _vm.Group5
    }
  })]), _c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("img", {
    staticClass: "w mb10",
    attrs: {
      src: _vm.Group6
    }
  })])], 1), _c("el-row", {
    attrs: {
      gutter: 10
    }
  }, [_c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: _vm.Group7
    }
  })]), _c("el-col", {
    attrs: {
      span: 12
    }
  }, [_c("img", {
    staticClass: "w",
    attrs: {
      src: _vm.Group8
    }
  })])], 1)], 1), _vm._m(5), _c("div", {
    staticClass: "w mb100"
  }, [_c("swiper", {
    staticClass: "swiper slinter",
    attrs: {
      options: _vm.swiperOptionM
    }
  }, [_c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-1.png */ "./src/assets/images/sky/sky-s-1.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-2.png */ "./src/assets/images/sky/sky-s-2.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-3.png */ "./src/assets/images/sky/sky-s-3.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-4.png */ "./src/assets/images/sky/sky-s-4.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-5.png */ "./src/assets/images/sky/sky-s-5.png")
    }
  })]), _c("swiper-slide", [_c("img", {
    staticClass: "w h",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-s-6.png */ "./src/assets/images/sky/sky-s-6.png")
    }
  })])], 1)], 1)])])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "banner-title"
  }, [_c("div", {
    staticClass: "banner-title-weight animate__animated animate__fadeIn"
  }, [_vm._v("科技让教育更平等")]), _c("div", {
    staticClass: "banner-title-item animate__animated animate__fadeIn animate__delay-100"
  }, [_vm._v("为每个孩子提供创造和实现他们美好梦想的机会")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "banner-nav animate__animated animate__fadeIn animate__delay-200"
  }, [_c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 严选 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 优质师资 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 互动式 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 教学内容 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 浸入式 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 教学模式 ")])]), _c("div", {
    staticClass: "item-line"
  }), _c("div", {
    staticClass: "item-box"
  }, [_c("div", {
    staticClass: "item-title"
  }, [_vm._v(" 云保护 ")]), _c("div", {
    staticClass: "item-desc"
  }, [_vm._v(" 数据安全 ")])])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "f16 lh30 fb color333 tl mt30"
  }, [_vm._v(" 严选优质师资线上实景实时授课。"), _c("br"), _vm._v(" 助教线下同步协作，减负备课授课。 ")]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "ml100 animate__animated animate__fadeIn animate__delay-300"
  }, [_c("img", {
    staticClass: "w500 h300",
    attrs: {
      src: __webpack_require__(/*! ../../assets/images/sky/sky-3.png */ "./src/assets/images/sky/sky-3.png")
    }
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "f14 lh25 color333 tc"
  }, [_vm._v(" 严选优质师资线上实景实时授课。"), _c("br"), _vm._v(" 助教线下同步协作，减负备课授课。 ")]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "flex justify-center"
  }, [_c("div", {
    staticClass: "w flex justify-center align-center mb30 mt60"
  }, [_c("div", {
    staticClass: "tc f20"
  }, [_vm._v("助力教育资源均衡发展")])])]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true&":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--9-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
exports = ___CSS_LOADER_API_IMPORT___(true);
// Module
exports.push([module.i, "@media screen and (min-width: 769px) {\n.bg-color-box[data-v-e8a88d58] {\n    width: 100%;\n    height: 620px;\n    padding-top: 80px;\n    padding-bottom: 30px;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n}\n.bg-color-box .banner-title-box[data-v-e8a88d58] {\n      width: 1000px;\n      height: calc(100% - 56px);\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n}\n.bg-color-box .banner-title-box .banner-title[data-v-e8a88d58] {\n        display: flex;\n        flex-direction: column;\n        align-items: flex-start;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-weight[data-v-e8a88d58] {\n          font-family: 'PingFang SC';\n          font-weight: 500;\n          font-size: 50px;\n          margin-bottom: 16px;\n          color: #041128;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-item[data-v-e8a88d58] {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 28px;\n          color: #333333;\n}\n.bg-color-box .banner-nav[data-v-e8a88d58] {\n      height: 56px;\n      width: 1000px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-nav .item-box[data-v-e8a88d58] {\n        width: 175px;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n}\n.bg-color-box .banner-nav .item-box .item-title[data-v-e8a88d58] {\n          color: #333333;\n          font-weight: 500;\n          font-size: 23px;\n          margin-bottom: 3px;\n}\n.bg-color-box .banner-nav .item-box .item-desc[data-v-e8a88d58] {\n          color: #4F4F4F;\n          font-size: 15px;\n}\n.bg-color-box .banner-nav .item-line[data-v-e8a88d58] {\n        width: 1px;\n        height: 60%;\n        background: rgba(0, 0, 0, 0.3);\n}\n}\n@media screen and (max-width: 768px) {\n.bg-color-box[data-v-e8a88d58] {\n    width: 100%;\n    height: 90.69767vw;\n    padding-bottom: 4.65116vw;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n}\n.bg-color-box .banner-title-box[data-v-e8a88d58] {\n      width: 100%;\n      height: calc(100% - 9.30233vw);\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-title-box .banner-title[data-v-e8a88d58] {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        padding-top: 6.97674vw;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-weight[data-v-e8a88d58] {\n          font-family: 'PingFang SC';\n          font-weight: 600;\n          font-size: 5.5814vw;\n          margin-bottom: 3.72093vw;\n          color: #000000;\n}\n.bg-color-box .banner-title-box .banner-title .banner-title-item[data-v-e8a88d58] {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 3.72093vw;\n          color: #000000;\n}\n.bg-color-box .banner-nav[data-v-e8a88d58] {\n      height: 9.30233vw;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n}\n.bg-color-box .banner-nav .item-box[data-v-e8a88d58] {\n        width: 25.34884vw;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n}\n.bg-color-box .banner-nav .item-box .item-title[data-v-e8a88d58] {\n          color: #333333;\n          font-weight: 500;\n          font-size: 3.72093vw;\n          margin-bottom: 0.69767vw;\n}\n.bg-color-box .banner-nav .item-box .item-desc[data-v-e8a88d58] {\n          color: #4F4F4F;\n          font-size: 2.32558vw;\n}\n.bg-color-box .banner-nav .item-line[data-v-e8a88d58] {\n        width: 1px;\n        height: 60%;\n        background: rgba(0, 0, 0, 0.3);\n}\n}\n.vjs-poster[data-v-e8a88d58] {\n  background-size: cover !important;\n}\n.bg-color-box[data-v-e8a88d58] {\n  background: linear-gradient(90deg, #5398FF 0%, #3EB8FF 25%, #0BDEF1 50%, #71FFC6 75%, #D4FF75 100%);\n}\n@media screen and (min-width: 769px) {\n.h-5[data-v-e8a88d58] {\n    display: none;\n}\n.h500[data-v-e8a88d58] {\n    height: 500px;\n}\n.sky-img[data-v-e8a88d58] {\n    width: 361px;\n    height: 361px;\n}\n.content[data-v-e8a88d58] {\n    width: 100%;\n}\n.content .color333[data-v-e8a88d58] {\n      color: #333333;\n}\n.content .color-white[data-v-e8a88d58] {\n      color: #fff;\n}\n.content .f35[data-v-e8a88d58] {\n      font-size: 35px;\n}\n.content .mb250[data-v-e8a88d58] {\n      margin-bottom: 250px;\n}\n.content .mb200[data-v-e8a88d58] {\n      margin-bottom: 200px;\n}\n.content .items-center[data-v-e8a88d58] {\n      width: 100%;\n      height: 100%;\n      top: 0;\n      left: 0;\n}\n.content .w30p[data-v-e8a88d58] {\n      width: 30%;\n}\n.content .tag-item[data-v-e8a88d58] {\n      width: 100px;\n      height: 60px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 22px;\n      box-sizing: border-box;\n      cursor: pointer;\n      color: #6C6868;\n}\n.content .tag-item[data-v-e8a88d58]:hover {\n        color: #6C83FF;\n        border-bottom: 3px solid #6C83FF;\n}\n.content .tag-item-active[data-v-e8a88d58] {\n      color: #6C83FF !important;\n      border-bottom: 3px solid #6C83FF;\n}\n.img-hover[data-v-e8a88d58] {\n    overflow: hidden;\n}\n.img-hover img[data-v-e8a88d58] {\n      transition: transform .3s;\n}\n.img-hover:hover img[data-v-e8a88d58] {\n      transform: scale(1.3);\n}\n.swiper[data-v-e8a88d58] {\n    width: 100%;\n    height: 25.69444vw;\n    position: relative;\n}\n.swiper .swiper-slide[data-v-e8a88d58] {\n      width: 34.72222vw;\n      height: 24.30556vw;\n}\n.swiper .swiper-slide img[data-v-e8a88d58] {\n        border-radius: 25px;\n}\n}\n@media screen and (max-width: 768px) {\n.computer[data-v-e8a88d58] {\n    display: none;\n}\n.h240[data-v-e8a88d58] {\n    height: 55.81395vw;\n}\n.sky-img[data-v-e8a88d58] {\n    width: 41.16279vw;\n    height: 41.16279vw;\n}\n.content[data-v-e8a88d58] {\n    width: 100%;\n}\n.content .color333[data-v-e8a88d58] {\n      color: #333333;\n}\n.content .color-white[data-v-e8a88d58] {\n      color: #fff;\n}\n.content .items-center[data-v-e8a88d58] {\n      width: 100%;\n      height: 100%;\n      top: 0;\n      left: 0;\n}\n.content .w30p[data-v-e8a88d58] {\n      width: 32%;\n}\n.content .w50p[data-v-e8a88d58] {\n      width: 47%;\n}\n.content .tag-item[data-v-e8a88d58] {\n      width: 9.30233vw;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 2.7907vw;\n      box-sizing: border-box;\n      cursor: pointer;\n      color: #6C6868;\n}\n.content .tag-item[data-v-e8a88d58]:hover {\n        color: #6C83FF;\n        border-bottom: 3px solid #6C83FF;\n}\n.content .tag-item-active[data-v-e8a88d58] {\n      color: #6C83FF !important;\n      border-bottom: 3px solid #6C83FF;\n}\n.swiper[data-v-e8a88d58] {\n    width: 100%;\n    height: 41.86047vw;\n    position: relative;\n}\n.swiper .swiper-slide[data-v-e8a88d58] {\n      width: 100%;\n      height: 34.88372vw;\n}\n.swiper .swiper-slide img[data-v-e8a88d58] {\n        border-radius: 25px;\n}\n}\n", "",{"version":3,"sources":["/Users/<USER>/Documents/cuiya官网/cuiya_official_website/src/styles/global.scss","/Users/<USER>/Documents/cuiya官网/cuiya_official_website/src/views/skyclass/index.vue"],"names":[],"mappings":"AA6BA;AAEE;IACE,WAAW;IACX,aAAa;IACb,iBATgB;IAUhB,oBAAoB;IACpB,sBAAsB;IACtB,aAAa;IACb,sBAAsB;IACtB,mBAAmB;IACnB,uBAAuB;AAAA;AATzB;MAYI,aAAa;MACb,yBAAwC;MACxC,aAAa;MACb,8BAA8B;MAC9B,mBAAmB;AAAA;AAhBvB;QAmBM,aAAa;QACb,sBAAsB;QACtB,uBAAuB;AAAA;AArB7B;UAwBQ,0BAA0B;UAC1B,gBAAgB;UAChB,eAAe;UACf,mBAAmB;UACnB,cAAc;AAAA;AA5BtB;UA+BQ,0BAA0B;UAC1B,gBAAgB;UAChB,eAAe;UACf,cAAc;AAAA;AAlCtB;MAwCI,YA7CgB;MA8ChB,aAAa;MACb,aAAa;MACb,uBAAuB;MACvB,mBAAmB;AAAA;AA5CvB;QA+CM,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;AAAA;AApDzB;UAsDQ,cAAc;UACd,gBAAgB;UAChB,eAAe;UACf,kBAAkB;AAAA;AAzD1B;UA4DQ,cAAc;UACd,eAAe;AAAA;AA7DvB;QAiEM,UAAU;QACV,WAAW;QACX,8BAAgC;AAAA;AACjC;AAIP;AAEE;IACE,WAAW;IACX,kBArFqC;IAsFrC,yBAtFqC;IAuFrC,sBAAsB;IACtB,aAAa;IACb,sBAAsB;IACtB,mBAAmB;IACnB,uBAAuB;AAAA;AARzB;MAWI,WAAW;MACX,8BAAyC;MACzC,aAAa;MACb,sBAAsB;MACtB,uBAAuB;MACvB,mBAAmB;AAAA;AAhBvB;QAmBM,aAAa;QACb,sBAAsB;QACtB,mBAAmB;QACnB,sBAzGiC;AAAA;AAmFvC;UAyBQ,0BAA0B;UAC1B,gBAAgB;UAChB,mBA9G+B;UA+G/B,wBA/G+B;UAgH/B,cAAc;AAAA;AA7BtB;UAgCQ,0BAA0B;UAC1B,gBAAgB;UAChB,oBArH+B;UAsH/B,cAAc;AAAA;AAnCtB;MAyCI,iBA5HmC;MA6HnC,WAAW;MACX,aAAa;MACb,uBAAuB;MACvB,mBAAmB;AAAA;AA7CvB;QAgDM,iBAnIiC;QAoIjC,YAAY;QACZ,aAAa;QACb,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;AAAA;AArDzB;UAuDQ,cAAc;UACd,gBAAgB;UAChB,oBA5I+B;UA6I/B,wBA7I+B;AAAA;AAmFvC;UA6DQ,cAAc;UACd,oBAjJ+B;AAAA;AAmFvC;QAkEM,UAAU;QACV,WAAW;QACX,8BAAgC;AAAA;AACjC;AAKP;EACE,iCAAiC;AAAA;AC/KnC;EACE,mGAAmG;AAAA;AAErG;AACE;IACE,aAAa;AAAA;AAEf;IACE,aAAa;AAAA;AAGf;IACE,YAAY;IACZ,aAAa;AAAA;AAGf;IACE,WAAW;AAAA;AADb;MAII,cAAc;AAAA;AAJlB;MAOI,WAAW;AAAA;AAPf;MAUI,eAAe;AAAA;AAVnB;MAcI,oBAAoB;AAAA;AAdxB;MAkBI,oBAAoB;AAAA;AAlBxB;MAsBI,WAAW;MACX,YAAY;MACZ,MAAM;MACN,OAAO;AAAA;AAzBX;MA6BI,UAAU;AAAA;AA7Bd;MAiCI,YAAY;MACZ,YAAY;MACZ,aAAa;MACb,mBAAmB;MACnB,uBAAuB;MACvB,eAAe;MACf,sBAAsB;MACtB,eAAe;MACf,cAAc;AAAA;AAzClB;QA2CM,cAAc;QACd,gCAAgC;AAAA;AA5CtC;MAiDI,yBAAyB;MACzB,gCAAgC;AAAA;AAIpC;IACE,gBAAgB;AAAA;AADlB;MAGI,yBAAyB;AAAA;AAH7B;MAOM,qBAAqB;AAAA;AAK3B;IACE,WAAW;IACX,kBD7EmC;IC8EnC,kBAAkB;AAAA;AAHpB;MAMI,iBDjFiC;MCkFjC,kBDlFiC;AAAA;AC2ErC;QASM,mBAAmB;AAAA;AACpB;AAIP;AACE;IACE,aAAa;AAAA;AAGf;IACE,kBDrFqC;AAAA;ACwFvC;IACE,iBDzFqC;IC0FrC,kBD1FqC;AAAA;AC6FvC;IACE,WAAW;AAAA;AADb;MAII,cAAc;AAAA;AAJlB;MAOI,WAAW;AAAA;AAPf;MAWI,WAAW;MACX,YAAY;MACZ,MAAM;MACN,OAAO;AAAA;AAdX;MAkBI,UAAU;AAAA;AAlBd;MAsBI,UAAU;AAAA;AAtBd;MA0BI,gBDvHmC;MCyHnC,aAAa;MACb,mBAAmB;MACnB,uBAAuB;MACvB,mBD5HmC;MC6HnC,sBAAsB;MACtB,eAAe;MACf,cAAc;AAAA;AAlClB;QAoCM,cAAc;QACd,gCAAgC;AAAA;AArCtC;MA0CI,yBAAyB;MACzB,gCAAgC;AAAA;AAGpC;IACE,WAAW;IACX,kBD7IqC;IC8IrC,kBAAkB;AAAA;AAHpB;MAMI,WAAW;MACX,kBDlJmC;AAAA;AC2IvC;QASM,mBAAmB;AAAA;AACpB","file":"index.vue","sourcesContent":["@import './mixin.scss';\n\n//默认设计稿的宽度\n$designWidth: 1440;\n//默认设计稿的高度\n$designHeight: 810;\n\n//默认设计稿的宽度\n$designWidthH5: 430;\n\n//px转为vw的函数\n@function vw($px) {\n  @return $px * 100vw / #{$designWidth};\n}\n\n//px转为vh的函数\n@function vh($px) {\n  @return $px * 100vh / #{$designHeight};\n}\n\n//H5中px转为vw的函数\n@function pw($px) {\n  @return $px * 100vw / #{$designWidthH5};\n}\n\n$navTopPadding: 80px;\n$bannerNavHeight: 56px;\n$MbannerNavHeight: pw(40);\n\n@media screen and (min-width: 769px) {\n  // 通用首页banner样式\n  .bg-color-box {\n    width: 100%;\n    height: 620px;\n    padding-top: $navTopPadding;\n    padding-bottom: 30px;\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n\n    .banner-title-box {\n      width: 1000px;\n      height: calc(100% - #{$bannerNavHeight});\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .banner-title {\n        display: flex;\n        flex-direction: column;\n        align-items: flex-start;\n\n        .banner-title-weight {\n          font-family: 'PingFang SC';\n          font-weight: 500;\n          font-size: 50px;\n          margin-bottom: 16px;\n          color: #041128;\n        }\n        .banner-title-item {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: 28px;\n          color: #333333;\n        }\n      }\n    }\n\n    .banner-nav {\n      height: $bannerNavHeight;\n      width: 1000px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .item-box {\n        width: 175px;\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        .item-title {\n          color: #333333;\n          font-weight: 500;\n          font-size: 23px;\n          margin-bottom: 3px;\n        }\n        .item-desc {\n          color: #4F4F4F;\n          font-size: 15px;\n        }\n      }\n      .item-line {\n        width: 1px;\n        height: 60%;\n        background: rgba($color: #000000, $alpha: 0.3);\n      }\n    }\n  }\n}\n@media screen and (max-width: 768px) {\n  // 通用首页banner样式\n  .bg-color-box {\n    width: 100%;\n    height: pw(390);\n    padding-bottom: pw(20);\n    box-sizing: border-box;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n\n    .banner-title-box {\n      width: 100%;\n      height: calc(100% - #{$MbannerNavHeight});\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n\n      .banner-title {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        padding-top: pw(30);\n\n        .banner-title-weight {\n          font-family: 'PingFang SC';\n          font-weight: 600;\n          font-size: pw(24);\n          margin-bottom: pw(16);\n          color: #000000;\n        }\n        .banner-title-item {\n          font-family: 'PingFang SC';\n          font-weight: 300;\n          font-size: pw(16);\n          color: #000000;\n        }\n      }\n    }\n\n    .banner-nav {\n      height: $MbannerNavHeight;\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n\n      .item-box {\n        width: pw(109);\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        .item-title {\n          color: #333333;\n          font-weight: 500;\n          font-size: pw(16);\n          margin-bottom: pw(3);\n        }\n        .item-desc {\n          color: #4F4F4F;\n          font-size: pw(10);\n        }\n      }\n      .item-line {\n        width: 1px;\n        height: 60%;\n        background: rgba($color: #000000, $alpha: 0.3);\n      }\n    }\n  }\n}\n\n.vjs-poster {\n  background-size: cover !important;\n}","\n        @import \"@/styles/global.scss\";\n        $src: \"./src/assets\";\n        \n\n.bg-color-box {\n  background: linear-gradient(90deg, #5398FF 0%, #3EB8FF 25%, #0BDEF1 50%, #71FFC6 75%, #D4FF75 100%);\n}\n@media screen and (min-width: 769px) {\n  .h-5 {\n    display: none;\n  }\n  .h500 {\n    height: 500px;\n  }\n\n  .sky-img {\n    width: 361px;\n    height: 361px;\n  }\n\n  .content {\n    width: 100%;\n\n    .color333 {\n      color: #333333;\n    }\n    .color-white {\n      color: #fff;\n    }\n    .f35 {\n      font-size: 35px;\n    }\n\n    .mb250 {\n      margin-bottom: 250px;\n    }\n\n    .mb200 {\n      margin-bottom: 200px;\n    }\n\n    .items-center {\n      width: 100%;\n      height: 100%;\n      top: 0;\n      left: 0;\n    }\n\n    .w30p {\n      width: 30%;\n    }\n\n    .tag-item {\n      width: 100px;\n      height: 60px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 22px;\n      box-sizing: border-box;\n      cursor: pointer;\n      color: #6C6868;\n      &:hover {\n        color: #6C83FF;\n        border-bottom: 3px solid #6C83FF;\n      }\n    }\n\n    .tag-item-active {\n      color: #6C83FF !important;\n      border-bottom: 3px solid #6C83FF;\n    }\n  }\n\n  .img-hover {\n    overflow: hidden;\n    img {\n      transition: transform .3s;\n    }\n    &:hover {\n      img {\n        transform: scale(1.3);\n      }\n    }\n  }\n\n  .swiper {\n    width: 100%;\n    height: vw(370);\n    position: relative;\n\n    .swiper-slide {\n      width: vw(500);\n      height: vw(350);\n      img {\n        border-radius: 25px;\n      }\n    }\n  }\n}\n@media screen and (max-width: 768px) {\n  .computer {\n    display: none;\n  }\n\n  .h240 {\n    height: pw(240);\n  }\n\n  .sky-img {\n    width: pw(177);\n    height: pw(177);\n  }\n\n  .content {\n    width: 100%;\n\n    .color333 {\n      color: #333333;\n    }\n    .color-white {\n      color: #fff;\n    }\n\n    .items-center {\n      width: 100%;\n      height: 100%;\n      top: 0;\n      left: 0;\n    }\n\n    .w30p {\n      width: 32%;\n    }\n\n    .w50p {\n      width: 47%;\n    }\n\n    .tag-item {\n      width: pw(40);\n      // height: 30px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: pw(12);\n      box-sizing: border-box;\n      cursor: pointer;\n      color: #6C6868;\n      &:hover {\n        color: #6C83FF;\n        border-bottom: 3px solid #6C83FF;\n      }\n    }\n\n    .tag-item-active {\n      color: #6C83FF !important;\n      border-bottom: 3px solid #6C83FF;\n    }\n  }\n  .swiper {\n    width: 100%;\n    height: pw(180);\n    position: relative;\n\n    .swiper-slide {\n      width: 100%;\n      height: pw(150);\n      img {\n        border-radius: 25px;\n      }\n    }\n  }\n}\n\n"]}]);
// Exports
module.exports = exports;


/***/ }),

/***/ "./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true&":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/vue-style-loader??ref--9-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--9-oneOf-1-2!./node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true&");
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(/*! ../../../node_modules/vue-style-loader/lib/addStylesClient.js */ "./node_modules/vue-style-loader/lib/addStylesClient.js").default
var update = add("39ae48e4", content, false, {"sourceMap":true,"shadowMode":false});
// Hot Module Replacement
if(true) {
 // When the styles change, update the <style> tags
 if(!content.locals) {
   module.hot.accept(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true&", function() {
     var newContent = __webpack_require__(/*! !../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true& */ "./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true&");
     if(newContent.__esModule) newContent = newContent.default;
     if(typeof newContent === 'string') newContent = [[module.i, newContent, '']];
     update(newContent);
   });
 }
 // When the module is disposed, remove the <style> tags
 module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ "./src/assets/images/sky/Group1.png":
/*!******************************************!*\
  !*** ./src/assets/images/sky/Group1.png ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/Group1.01024160.png";

/***/ }),

/***/ "./src/assets/images/sky/Group2.png":
/*!******************************************!*\
  !*** ./src/assets/images/sky/Group2.png ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/Group2.0005bf33.png";

/***/ }),

/***/ "./src/assets/images/sky/Group3.png":
/*!******************************************!*\
  !*** ./src/assets/images/sky/Group3.png ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/Group3.e080c061.png";

/***/ }),

/***/ "./src/assets/images/sky/Group4.png":
/*!******************************************!*\
  !*** ./src/assets/images/sky/Group4.png ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/Group4.496f81f4.png";

/***/ }),

/***/ "./src/assets/images/sky/Group5.png":
/*!******************************************!*\
  !*** ./src/assets/images/sky/Group5.png ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/Group5.55e3e325.png";

/***/ }),

/***/ "./src/assets/images/sky/Group6.png":
/*!******************************************!*\
  !*** ./src/assets/images/sky/Group6.png ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/Group6.ad12a000.png";

/***/ }),

/***/ "./src/assets/images/sky/Group7.png":
/*!******************************************!*\
  !*** ./src/assets/images/sky/Group7.png ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/Group7.dc403b4e.png";

/***/ }),

/***/ "./src/assets/images/sky/Group8.png":
/*!******************************************!*\
  !*** ./src/assets/images/sky/Group8.png ***!
  \******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/Group8.cd3cf80a.png";

/***/ }),

/***/ "./src/assets/images/sky/aqjy.png":
/*!****************************************!*\
  !*** ./src/assets/images/sky/aqjy.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/aqjy.e0dea816.png";

/***/ }),

/***/ "./src/assets/images/sky/ctwh.png":
/*!****************************************!*\
  !*** ./src/assets/images/sky/ctwh.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/ctwh.dfb33eff.png";

/***/ }),

/***/ "./src/assets/images/sky/fnkx.png":
/*!****************************************!*\
  !*** ./src/assets/images/sky/fnkx.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/fnkx.aa8911be.png";

/***/ }),

/***/ "./src/assets/images/sky/kxsy.png":
/*!****************************************!*\
  !*** ./src/assets/images/sky/kxsy.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/kxsy.175c9f9f.png";

/***/ }),

/***/ "./src/assets/images/sky/ldsj.png":
/*!****************************************!*\
  !*** ./src/assets/images/sky/ldsj.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/ldsj.35f76f28.png";

/***/ }),

/***/ "./src/assets/images/sky/mzfz.png":
/*!****************************************!*\
  !*** ./src/assets/images/sky/mzfz.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/mzfz.f35ebbd9.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-3.png":
/*!*****************************************!*\
  !*** ./src/assets/images/sky/sky-3.png ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-3.16bd7db8.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-4.png":
/*!*****************************************!*\
  !*** ./src/assets/images/sky/sky-4.png ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-4.8819e4f0.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-5.png":
/*!*****************************************!*\
  !*** ./src/assets/images/sky/sky-5.png ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-5.62aecd54.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-6.png":
/*!*****************************************!*\
  !*** ./src/assets/images/sky/sky-6.png ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-6.0db83ccc.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-7.png":
/*!*****************************************!*\
  !*** ./src/assets/images/sky/sky-7.png ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-7.01cab8d1.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-8.png":
/*!*****************************************!*\
  !*** ./src/assets/images/sky/sky-8.png ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-8.7c3822f8.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-9.png":
/*!*****************************************!*\
  !*** ./src/assets/images/sky/sky-9.png ***!
  \*****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-9.e52f4210.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-bg1.png":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-bg1.png ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-bg1.1484190f.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-m-7.jpg":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-m-7.jpg ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-m-7.4dd80698.jpg";

/***/ }),

/***/ "./src/assets/images/sky/sky-m-8.jpg":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-m-8.jpg ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-m-8.930d9b6b.jpg";

/***/ }),

/***/ "./src/assets/images/sky/sky-m-9.jpg":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-m-9.jpg ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-m-9.eba788bb.jpg";

/***/ }),

/***/ "./src/assets/images/sky/sky-s-1.png":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-s-1.png ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-s-1.8ff24c14.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-s-2.png":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-s-2.png ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-s-2.6167355a.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-s-3.png":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-s-3.png ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-s-3.0e9296bc.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-s-4.png":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-s-4.png ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-s-4.aaaba891.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-s-5.png":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-s-5.png ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-s-5.a25a043c.png";

/***/ }),

/***/ "./src/assets/images/sky/sky-s-6.png":
/*!*******************************************!*\
  !*** ./src/assets/images/sky/sky-s-6.png ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/sky-s-6.ec785fc9.png";

/***/ }),

/***/ "./src/assets/images/sky/whcc.png":
/*!****************************************!*\
  !*** ./src/assets/images/sky/whcc.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/whcc.0346e162.png";

/***/ }),

/***/ "./src/assets/images/sky/zhys.png":
/*!****************************************!*\
  !*** ./src/assets/images/sky/zhys.png ***!
  \****************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "static/img/zhys.d6a4ca52.png";

/***/ }),

/***/ "./src/utils/index.js":
/*!****************************!*\
  !*** ./src/utils/index.js ***!
  \****************************/
/*! exports provided: debounce, throttle, changeTitle, getDocumentTop, getScrollHeight, getWindowHeight */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "debounce", function() { return debounce; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "throttle", function() { return throttle; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "changeTitle", function() { return changeTitle; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "getDocumentTop", function() { return getDocumentTop; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "getScrollHeight", function() { return getScrollHeight; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "getWindowHeight", function() { return getWindowHeight; });
// 防抖 immediate 是否开始立即执行

// 使用方式 methods:
// click1: debounce(async function () {
//   await {data} = getInfo()
//   console.log('防抖')
// }, 3000, true)

function debounce(func, wait = 3000, immediate = false) {
  let timeout;
  return function () {
    const context = this;
    const args = arguments;
    if (timeout) clearTimeout(timeout); // timeout 不为null
    if (immediate) {
      const callNow = !timeout; // 第一次会立即执行，以后只有事件执行后才会再次触发
      timeout = setTimeout(function () {
        timeout = null;
      }, wait);
      if (callNow) {
        func.apply(context, args);
      }
    } else {
      timeout = setTimeout(function () {
        func.apply(context, args);
      }, wait);
    }
  };
}

// 节流
// 使用方式 methods:
// click2: throttle(function() {
//   console.log('节流')
// }, 2000)
function throttle(fn, wait = 3000) {
  var timer = null;
  return function () {
    var context = this;
    var args = arguments;
    if (!timer) {
      timer = setTimeout(function () {
        fn.apply(context, args);
        timer = null;
      }, wait);
    }
  };
}
function changeTitle(title) {
  const dom = document.querySelector('title');
  if (dom) {
    dom.innerText = title;
  }
}
function getDocumentTop() {
  var scrollTop = 0;
  var bodyScrollTop = 0;
  var documentScrollTop = 0;
  if (document.body) {
    bodyScrollTop = document.body.scrollTop;
  }
  if (document.documentElement) {
    documentScrollTop = document.documentElement.scrollTop;
  }
  scrollTop = bodyScrollTop - documentScrollTop > 0 ? bodyScrollTop : documentScrollTop;
  return scrollTop;
}
function getScrollHeight() {
  var scrollHeight = 0;
  var bodyScrollHeight = 0;
  var documentScrollHeight = 0;
  if (document.body) {
    bodyScrollHeight = document.body.scrollHeight;
  }
  if (document.documentElement) {
    documentScrollHeight = document.documentElement.scrollHeight;
  }
  scrollHeight = bodyScrollHeight - documentScrollHeight > 0 ? bodyScrollHeight : documentScrollHeight;
  return scrollHeight;
}
function getWindowHeight() {
  var windowHeight = 0;
  if (document.compatMode === 'CSS1Compat') {
    windowHeight = document.documentElement.clientHeight;
  } else {
    windowHeight = document.body.clientHeight;
  }
  return windowHeight;
}

/***/ }),

/***/ "./src/views/skyclass/index.vue":
/*!**************************************!*\
  !*** ./src/views/skyclass/index.vue ***!
  \**************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=e8a88d58&scoped=true& */ "./src/views/skyclass/index.vue?vue&type=template&id=e8a88d58&scoped=true&");
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ "./src/views/skyclass/index.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _index_vue_vue_type_style_index_0_id_e8a88d58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true& */ "./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true&");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");






/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "e8a88d58",
  null
  
)

/* hot reload */
if (true) {
  var api = __webpack_require__(/*! ./node_modules/vue-hot-reload-api/dist/index.js */ "./node_modules/vue-hot-reload-api/dist/index.js")
  api.install(__webpack_require__(/*! vue */ "./node_modules/vue/dist/vue.runtime.esm.js"))
  if (api.compatible) {
    module.hot.accept()
    if (!api.isRecorded('e8a88d58')) {
      api.createRecord('e8a88d58', component.options)
    } else {
      api.reload('e8a88d58', component.options)
    }
    module.hot.accept(/*! ./index.vue?vue&type=template&id=e8a88d58&scoped=true& */ "./src/views/skyclass/index.vue?vue&type=template&id=e8a88d58&scoped=true&", function(__WEBPACK_OUTDATED_DEPENDENCIES__) { /* harmony import */ _index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=e8a88d58&scoped=true& */ "./src/views/skyclass/index.vue?vue&type=template&id=e8a88d58&scoped=true&");
(function () {
      api.rerender('e8a88d58', {
        render: _index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
        staticRenderFns: _index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]
      })
    })(__WEBPACK_OUTDATED_DEPENDENCIES__); }.bind(this))
  }
}
component.options.__file = "src/views/skyclass/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/views/skyclass/index.vue?vue&type=script&lang=js&":
/*!***************************************************************!*\
  !*** ./src/views/skyclass/index.vue?vue&type=script&lang=js& ***!
  \***************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/babel-loader/lib!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=script&lang=js& */ "./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true&":
/*!************************************************************************************************!*\
  !*** ./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true& ***!
  \************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_e8a88d58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/vue-style-loader??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true& */ "./node_modules/vue-style-loader/index.js?!./node_modules/css-loader/dist/cjs.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/sass-loader/dist/cjs.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=style&index=0&id=e8a88d58&lang=scss&scoped=true&");
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_e8a88d58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_e8a88d58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_e8a88d58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_9_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_9_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_9_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_9_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_e8a88d58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ "./src/views/skyclass/index.vue?vue&type=template&id=e8a88d58&scoped=true&":
/*!*********************************************************************************!*\
  !*** ./src/views/skyclass/index.vue?vue&type=template&id=e8a88d58&scoped=true& ***!
  \*********************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/cache-loader/dist/cjs.js?{"cacheDirectory":"node_modules/.cache/vue-loader","cacheIdentifier":"1df0c205-vue-loader-template"}!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/babel-loader/lib!../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib??vue-loader-options!./index.vue?vue&type=template&id=e8a88d58&scoped=true& */ "./node_modules/cache-loader/dist/cjs.js?{\"cacheDirectory\":\"node_modules/.cache/vue-loader\",\"cacheIdentifier\":\"1df0c205-vue-loader-template\"}!./node_modules/cache-loader/dist/cjs.js?!./node_modules/babel-loader/lib/index.js!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/cache-loader/dist/cjs.js?!./node_modules/vue-loader/lib/index.js?!./src/views/skyclass/index.vue?vue&type=template&id=e8a88d58&scoped=true&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_cache_loader_dist_cjs_js_cacheDirectory_node_modules_cache_vue_loader_cacheIdentifier_1df0c205_vue_loader_template_node_modules_cache_loader_dist_cjs_js_ref_13_0_node_modules_babel_loader_lib_index_js_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_cache_loader_dist_cjs_js_ref_1_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_template_id_e8a88d58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);
//# sourceMappingURL=4.js.map