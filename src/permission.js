import router from './router'
import store from './store'
import { message } from '@/utils/singeMessage.js'
// import NProgress from 'nprogress' // progress bar
// import 'nprogress/nprogress.css' // progress bar style
import { getToken, getPartentToken, getAdminToken, getExpertToken, getPublishToken, getAuthorToken } from '@/utils/auth' // get token from cookie
// import getPageTitle from '@/utils/get-page-title'

// NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/classpro/login', '/loginweb', '/jhub-login', '/editor', '/authorInvite', '/classpro/register', '/edit/course', '/parent/login', '/parent/checkLogin', '/parent/hassClassInfo', '/help', '/h5/login', '/h5/checkLogin', '/h5/hassClassInfo', '/h5/checkPraise', '/h5/ScreenCasting', '/datacenter/login', '/publisher/login/', '/expert/login', '/author/login', '/community', '/author/task', '/bingoBook/home', '/bingoBook/login', '/readFromBack', '/chuanEdu', '/chuanEdu/detail'] // no redirect whitelist
// const ua = navigator.userAgent.toLowerCase()

router.beforeEach(async (to, from, next) => {
  // start progress bar
  // NProgress.start()
  // set page title
  if (to.path.startsWith('/h5') || to.path.startsWith('/bingoBook')) {
    document.title = to.meta.title
  }
  // determine whether the user has logged in
  const hasToken = (to.path.startsWith('/parent') || to.path.startsWith('/h5')) ? getPartentToken() : to.path.startsWith('/datacenter') ? getAdminToken() : to.path.startsWith('/expert') ? getExpertToken() : to.path.startsWith('/publisher') ? getPublishToken() : (to.path.startsWith('/author') || to.path.startsWith('/editor')) ? getAuthorToken() : getToken()
  if (hasToken || to.path.startsWith('/doExcelTrainingDetail') || to.path.startsWith('/doTrainingDetail') || to.path.startsWith('/AIChat') || to.path.startsWith('/pyhtonTraining') || to.path.startsWith('aitraining/finacePractice')) {
    if (to.path === '/classpro/login' || to.path === '/loginweb') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      // NProgress.done() // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939
    } else if (to.path === '/datacenter/login') {
      // if is logged in, redirect to the home page
      if (to.query) {
        next()
      } else {
        next({ path: '/datacenter/index' })
      }
      // NProgress.done() // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939
    } else if (to.path.startsWith('/publisher/login')) {
      next({ path: `/publisher/home?publisheId=${localStorage.getItem('publisheId')}` })
    } else if (to.path.startsWith('/expert/login')) {
      next({ path: '/expert/home' })
    } else if (to.path.startsWith('/author/login')) {
      next({ path: '/author/home' })
    } else {
      try {
        // console.log(store.getters.addRoutes)
        // get user info
        // note: roles must be a object array! such as: ['admin'] or ,['developer','editor']
        if (store.getters.addRoutes.length === 0) {
          let userType = 'ASSISTANT'
          if (to.path.startsWith('/parent') || to.path.startsWith('/h5')) {
            userType = 'PARENT'
          }
          if (to.path.startsWith('/datacenter')) {
            await store.dispatch('user/GetDataCenterInfo')
          } else {
            // 非统计平台才从后台获取信息
            await store.dispatch('user/GetInfo', userType)
          }
          if (!(to.path.startsWith('/parent') || to.path.startsWith('/h5') || to.path.startsWith('/datacenter') || to.path.startsWith('/publisher') || to.path.startsWith('/expert') || to.path.startsWith('/author'))) {
            await store.dispatch('user/GetUserRelation')
          }
          const accessRoutes = await store.dispatch('permission/generateRoutes')
          accessRoutes.forEach(element => {
            router.addRoute(element)
          })
          next({ ...to, replace: true })
        } else {
          next()
        }
      } catch (error) {
        console.log(error)
        // remove token and go to login page to re-login
        await store.dispatch('user/FedLogOut')
        message.error(error || 'Has Error')
        if (to.path.startsWith('/parent')) {
          next(`/parent/login?redirect=${to.path}`)
          // NProgress.done()
        } else if (to.path.startsWith('/h5')) {
          next(`/h5/login?redirect=${to.path}`)
          // NProgress.done()
        } else {
          next(`/classpro/login?redirect=${to.path}`)
          // NProgress.done()
        }
      }
    }
  } else {
    /* has no token */
    if (to.path.startsWith('/digitalbooks')) {
      // in the free login whitelist, go directly
      next()
    } else if (to.path.startsWith('/parent/course/homework')) {
      // in the free login whitelist, go directly
      next()
    } else if (to.path.startsWith('/doTrainingDetail')) {
      // in the free login whitelist, go directly
      next()
    } else if (to.path.startsWith('aitraining/finacePractice')) {
      // in the free login whitelist, go directly
      next()
    } else if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else if (to.path.startsWith('/activity/expert') || to.path.startsWith('/activity/work')) {
      // 活动评审页面不用登录
      next()
    } else if (to.path.startsWith('/h5')) {
      // next(`/h5/login?redirect=${to.path}`)
      next()
      // NProgress.done()
    } else if (to.path === '/publisher/login') {
      // next(`/h5/login?redirect=${to.path}`)
      next(`/publisher/login/${localStorage.getItem('publisheId')}`)
      // NProgress.done()
    } else if (to.path.startsWith('/publisher')) {
    // next(`/h5/login?redirect=${to.path}`)
      next()
    // NProgress.done()
    } else if (to.path.startsWith('/expert')) {
      // next(`/h5/login?redirect=${to.path}`)
      next()
      // NProgress.done()
    } else if (to.path.startsWith('/author')) {
      // next(`/h5/login?redirect=${to.path}`)
      next()
      // NProgress.done()
    } else if (to.path.startsWith('/editor')) {
      next()
      // NProgress.done()
    } else if (to.path.startsWith('/authorInvite')) {
      next()
      // NProgress.done()
    } else if (to.path.startsWith('/parent')) {
      next(`/parent/login?redirect=${to.path}`)
      // NProgress.done()
    } else if (to.path.startsWith('/bingoBook')) {
      next()
      // NProgress.done()
    } else if (to.path.startsWith('/datacenter')) {
      next(`/datacenter/login`)
      // NProgress.done()
    } else if (to.path.indexOf('/classprotext/login') > -1) {
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/classpro/login?redirect=${to.path}`)
      // NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  // NProgress.done()
})
