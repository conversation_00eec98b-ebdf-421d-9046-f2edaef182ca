<template>
  <div class="w h overflow-auto">
    <Nav v-if="showHead" />
    <div class="datacenter-content">
      <div class="d-select-box">
        <!-- role：ADMIN-超级管理员；PUBLISHER-出版社管理员；ORGANIZATION-教育局管理员；SCHOOL-学校管理员； -->
        <!-- 学期，地区，出版社，发行机构, 学校 -->
        <div v-if="!showHead" class="pointer back">
          {{ adminInfo.name }}
        </div>
        <el-select
          v-if="['ADMIN', 'PUBLISHER', 'ORGANIZATION', 'SCHOOL'].indexOf(adminInfo.role) !== -1"
          v-model="term"
          placeholder="学期"
          clearable
          @change="termChange"
        >
          <el-option :label="'全部学期'" :value="0" />
          <el-option v-for="item in termList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>

        <el-cascader
          v-if="['ADMIN', 'PUBLISHER'].indexOf(adminInfo.role) !== -1"
          v-model="area"
          class="ml10"
          :props="props"
          placeholder="地区"
          clearable
          @change="areaChange"
        />

        <el-select
          v-if="['ADMIN'].indexOf(adminInfo.role) !== -1"
          v-model="publisher"
          placeholder="出版社"
          clearable
          @change="publisherChange"
        >
          <el-option :label="'全部出版社'" :value="0" />
          <el-option v-for="item in publisherList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select
          v-if="['ADMIN', 'PUBLISHER'].indexOf(adminInfo.role) !== -1"
          v-model="issuer"
          placeholder="发行机构"
          clearable
          @change="issuerChange"
        >
          <el-option :label="'全部发行机构'" :value="0" />
          <el-option v-for="item in issuerList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select
          v-if="['ORGANIZATION'].indexOf(adminInfo.role) !== -1"
          v-model="school"
          placeholder="学校"
          clearable
          @change="schoolChange"
        >
          <el-option :label="'全部学校'" :value="0" />
          <el-option v-for="item in schoolList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>

      <template v-if="adminInfo.role === 'ADMIN'">
        <div class="card-title">
          总基础数据
        </div>

        <div class="flex justify-between mb40">
          <div class="card w">
            <el-row :gutter="5" class="mb40">
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    出版书科目总数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.package_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    合作出版社：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.publisher_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center pointer" @click="+baseInfo.district_total ? handlerDistrict() : ''">
                  <div class="t-title">
                    覆盖区县：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.district_total }}
                  </div>
                  <i v-if="+baseInfo.district_total" class="el-icon-arrow-right"></i>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center pointer" @click="+baseInfo.school_total ? handlerSchool() : ''">
                  <div class="t-title">
                    总学校数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.school_total }}
                  </div>
                  <i v-if="+baseInfo.school_total" class="el-icon-arrow-right"></i>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="5">
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    出版书总册数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.book_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    合作发行渠道：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.issuer_total }}
                  </div>
                </div>
              </el-col>

              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总班级数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.class_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总学生数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.student_total }}
                  </div>
                </div>
              </el-col>
            </el-row>

          </div>
        </div>
      </template>

      <template v-else-if="adminInfo.role === 'PUBLISHER'">
        <div class="card-title">
          总基础数据
        </div>

        <div class="flex justify-between mb40">
          <div class="card w">
            <el-row :gutter="5">
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    出版书科目总数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.package_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    出版书总册数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.book_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center pointer" @click="+baseInfo.school_total ? handlerSchool() : ''">
                  <div class="t-title">
                    总学校数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.school_total }}
                  </div>
                  <i v-if="+baseInfo.school_total" class="el-icon-arrow-right"></i>

                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center pointer" @click="+baseInfo.district_total ? handlerDistrict() : ''">
                  <div class="t-title">
                    覆盖区县：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.district_total }}
                  </div>
                  <i v-if="+baseInfo.district_total" class="el-icon-arrow-right"></i>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>

      <template v-else-if="adminInfo.role === 'ORGANIZATION'">
        <div class="card-title">
          总基础数据
        </div>

        <div class="flex justify-between mb40">
          <div class="card w">
            <el-row :gutter="5">
              <el-col :span="6">
                <div class="flex items-center pointer" @click="+baseInfo.school_total ? handlerSchool() : ''">
                  <div class="t-title">
                    总学校数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.school_total }}
                  </div>
                  <i v-if="+baseInfo.school_total" class="el-icon-arrow-right"></i>
                </div>
              </el-col>
              <el-col v-if="school" :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    学校名称：
                  </div>
                  <div class="t-school">
                    {{ schoolListObj[school] || '-' }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    覆盖班级数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.class_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    覆盖学生数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.student_total }}
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>

      <template v-else-if="adminInfo.role === 'SCHOOL'">
        <div class="card-title">
          总基础数据
        </div>

        <div class="flex justify-between mb40">
          <div class="card w">
            <el-row :gutter="5">
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    覆盖年级：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.grade_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    覆盖班级数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.class_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    覆盖学生数：
                  </div>
                  <div class="t-point">
                    {{ baseInfo.student_total }}
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>

      <template v-if="['ADMIN', 'ORGANIZATION', 'SCHOOL'].indexOf(adminInfo.role) !== -1">
        <div class="card-title">
          总使用情况
        </div>

        <div class="card w mb40">
          <div class="w mb40">
            <el-row :gutter="10">
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总开设课程数：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.course_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总完成课程数：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.course_finished_total }}
                  </div>
                </div>
              </el-col>

              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总完成课程率：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.course_percent }}%
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="w">
            <el-row :gutter="10">
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总课时数：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.unit_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    已上课时数：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.unit_finished_total }}
                  </div>
                </div>
              </el-col>

              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    已上课时率：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.unit_percent }}%
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>

      <template v-if="['ADMIN', 'ORGANIZATION', 'SCHOOL'].indexOf(adminInfo.role) !== -1">
        <div class="card-title">
          图表示意
        </div>

        <div class="flex justify-between mb40">
          <div class="card w-46">
            <div class="chart-title">科目</div>
            <div v-show="columnPlotList.length !== 0" id="column1"></div>
            <el-empty v-show="columnPlotList.length === 0" description="暂无数据" />
          </div>
          <div class="card w-46">
            <div class="chart-title">用户增长曲线</div>
            <div v-show="linePlotList.length !== 0" id="column2"></div>
            <el-empty v-show="linePlotList.length === 0" description="暂无数据" />

          </div>
        </div>
      </template>

      <div class="card-title">
        数字出版课程
      </div>

      <div class="card w mb40 flex flex-wrap">
        <el-empty v-show="packageListInfo.length === 0" class="w" description="暂无数据" />

        <div v-for="item in packageListInfo" :key="item.id" class="course-box" @click="handlerDetail(item)">
          <img :src="item.cover_url || DefaultCover" alt="" />

          <div class="c-title">
            <div class="w article-singer-container">
              {{ item.name }}
            </div>
          </div>

          <div class="w flex justify-between items-center">
            <div v-if="+item.course_num" class="c-tag">共{{ +item.course_num }}册</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
    >
      <div class="datacenter-dialog" v-html="dialogNode"></div>
    </el-dialog>
  </div>
</template>

<script>
import Nav from './components/nav.vue'
import { Column, Line } from '@antv/g2plot'

import {
  getAreaByPid,
  getPublisher,
  getIssuer,
  baseData,
  useData,
  chartBar,
  chartLine,
  getPackageList,
  getDistrictList,
  getSchool,
  getSchoolList
} from '@/api/datacenter-api.js'
import { mapGetters } from 'vuex'
import DefaultCover from '@/assets/images/default-cover.jpg'

export default {
  components: {
    Nav
  },
  data () {
    return {
      dialogVisible: false,
      dialogTitle: '',
      dialogNode: '',
      DefaultCover,
      props: {
        lazy: true,
        async lazyLoad (node, resolve) {
          const { level } = node
          const nodes = []

          // level: 层级 node 节点数据  一级菜单数据
          // 为1代表第一次请求
          const type = level === 0 ? '' : node.value
          if (level === 0) {
            nodes.push({ value: -1, label: '全部地区', leaf: true })
            const { data } = await getAreaByPid()
            // 节点数组
            // nodes.push({ value: '', label: '全部',leaf: node.level >= 2 })
            data.map((item) => {
            // {value:'',label:'全部'}
              const obj = {
                value: `${item.id},${item.name}`,
                label: item.name,
                leaf: node.level >= 2
              }
              nodes.push(obj)
            })
            // resolve 节点返回
            resolve(nodes)
          } else {
            if (level !== 0 && node.value !== -1) {
              const formData = new FormData()
              if (type) {
                formData.append('pid', type.split(',')[0])
              }

              const { data } = await getAreaByPid(formData)
              // 节点数组
              // nodes.push({ value: '', label: '全部',leaf: node.level >= 2 })
              data.map((item) => {
                // {value:'',label:'全部'}
                const obj = {
                  value: `${item.id},${item.name}`,
                  label: item.name,
                  leaf: node.level >= 2
                }
                nodes.push(obj)
              })
              // resolve 节点返回
              resolve(nodes)
            } else {
              resolve(nodes)
            }
          }
        }
      },
      area: [],
      publisher: '',
      publisherList: '',
      term: '',
      termList: '',
      issuer: '',
      issuerList: '',
      school: '',
      schoolList: '',
      schoolListObj: {},
      baseInfo: {
        'package_total': '-', // 科目总数
        'book_total': '-', // 书总册数
        'publisher_total': '-', // 出版社数
        'issuer_total': '-', // 发行机构数
        'district_total': '-', // 覆盖区县
        'school_total': '-', // 总学校数
        'class_total': '-', // 总班级数
        'student_total': '-' // 总学生数
      },
      useDataInfo: {
        'course_total': '-',
        'course_finished_total': '-',
        'course_percent': '-',
        'unit_total': '-',
        'unit_finished_total': '-',
        'unit_percent': '-'
      },
      columnPlot: null,
      linePlot: null,
      packageListInfo: [],
      linePlotList: [],
      columnPlotList: [],
      showHead: true
    }
  },
  computed: {
    ...mapGetters([
      'adminInfo'
    ])
  },
  mounted () {
    this._getPublisher()
    this._getIssuer()
    this.getTerm()
    this._getSchool()
    this.initDate()

    if (window.self !== window.top) {
      // 在 iframe 中
      this.showHead = false
    } else {
      this.showHead = true
    }
  },
  methods: {
    async initDate () {
      this._baseData()
      this._useData()
      this._getPackageList()
      if (['ADMIN', 'ORGANIZATION', 'SCHOOL'].indexOf(this.adminInfo.role) !== -1) {
        await this._chartBar()
        await this._chartLine()
      }
    },
    getTerm () {
      const data = new Date()
      let year = data.getFullYear() + 1
      const obj = []
      while (year > 2022) {
        obj.push({
          name: `${year - 1}~${year}学年度上学期`,
          id: `${year - 1},first`
        })
        obj.push({
          name: `${year - 1}~${year}学年度下学期`,
          id: `${year - 1},second`
        })
        year--
      }
      this.termList = obj
    },
    async _getPublisher () {
      const { data } = await getPublisher()
      this.publisherList = data
    },
    async _getIssuer () {
      const { data } = await getIssuer()
      this.issuerList = data
    },
    async _getSchool () {
      const { data } = await getSchool()
      this.schoolList = data
      const obj = {}
      if (data && data.length > 0) {
        data.map(val => {
          obj[val.id] = val.name
        })
      }

      this.schoolListObj = obj
    },
    getFormData () {
      const formData = new FormData()
      if (this.term) {
        const arr = this.term.split(',')
        formData.append('year', arr[0])
        formData.append('term', arr[1])
      }

      if (this.area && this.area.length > 2) {
        formData.append('province', this.area[0].split(',')[1])
        formData.append('city', this.area[1].split(',')[1])
        formData.append('district', this.area[2].split(',')[1])
      }

      if (this.publisher) {
        formData.append('publisher_id', this.publisher)
      }

      if (this.issuer) {
        formData.append('issuer_id', this.issuer)
      }
      if (this.school) {
        formData.append('school_id', this.school)
      }
      return formData
    },
    async _baseData () {
      const formData = this.getFormData()
      const { data } = await baseData(formData)
      this.baseInfo = data
    },
    async _useData () {
      const formData = this.getFormData()
      const { data } = await useData(formData)
      this.useDataInfo = data
    },
    async _chartBar () {
      const formData = this.getFormData()
      const { data } = await chartBar(formData)
      if (this.columnPlot) {
        this.columnPlot.destroy()
      }
      const arr = []
      this.columnPlotList = data

      if (data.length > 0) {
        data.map(val => {
          arr.push({
            name: val.package_name,
            id: val.package_id,
            value: Number(val.finished),
            type: '完成'
          })
          arr.push({
            name: val.package_name,
            id: val.package_id,
            value: Number(val.total),
            type: '总数'
          })
        })
      }

      this.columnPlot = new Column('column1', {
        data: arr,
        isStack: true,
        xField: 'name',
        yField: 'value',
        seriesField: 'type',
        height: 254,
        xAxis: {
          label: {
            autoHide: true,
            autoRotate: false
          }
        },
        label: {
          // 可手动配置 label 数据标签位置
          position: 'middle', // 'top', 'bottom', 'middle'
          // 可配置附加的布局方法
          layout: [
          // 柱形图数据标签位置自动调整
            { type: 'interval-adjust-position' },
            // 数据标签防遮挡
            { type: 'interval-hide-overlap' },
            // 数据标签文颜色自动调整
            { type: 'adjust-color' }
          ]
        }
      })
      this.columnPlot.render()
    },
    async _chartLine () {
      if (this.linePlot) {
        this.linePlot.destroy()
      }
      const formData = this.getFormData()
      const { data } = await chartLine(formData)
      this.linePlotList = data

      this.linePlot = new Line('column2', {
        data,
        height: 254,
        padding: 'auto',
        xField: 'ym',
        yField: 'num',
        meta: {
          ym: {
            alias: '日期'
          },
          num: {
            alias: '数量'
          }
        },
        tooltip: { showMarkers: false },
        xAxis: {
        // type: 'timeCat',
          tickCount: 5
        }
      })
      this.linePlot.render()
    },
    async _getPackageList () {
      const formData = this.getFormData()
      formData.append('page', 1)
      formData.append('limit', 1000)
      const { data } = await getPackageList(formData)
      this.packageListInfo = data.items
    },

    termChange () {
      this.initDate()
    },
    areaChange () {
      this.initDate()
    },
    publisherChange () {
      this.initDate()
    },
    issuerChange () {
      this.initDate()
    },
    schoolChange (val) {
      console.log(val)
      this.initDate()
    },
    async handlerDetail (item) {
      this.$router.push({ path: '/datacenter/course', query: { id: item.id }})
    },
    async handlerDistrict () {
      this.dialogTitle = '覆盖区县'
      const formData = this.getFormData()
      const { data } = await getDistrictList(formData)
      let div = ''
      if (data && data.length > 0) {
        data.map((val) => {
          div += `<p>
            <span>${val.district}</span>
            </p>`
        })
      }
      this.dialogNode = div
      this.dialogVisible = true
    },
    async handlerSchool () {
      this.dialogTitle = '总学校数'
      const formData = this.getFormData()
      const { data } = await getSchoolList(formData)
      let div = ''
      if (data && data.length > 0) {
        data.map((val) => {
          div += `<p>
            <span>${val.school_name}</span>
            </p>`
        })
      }
      this.dialogNode = div
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.datacenter-content {
  //padding-bottom: 40px;
  padding: 20px;

  .ml10 {
    margin-left: 10px;
  }

  padding-top: 110px;
  width: 1100px;
  margin: 0 auto;

  .d-select-box {
    display: flex;
    width: 100%;
    justify-content: flex-end;
    position: relative;

    .back {
      position: absolute;
      left: 0;
      color: #000;
      display: flex;
      align-items: center;
      height: 40px;
      font-size: 16px;
      i {
        margin-right: 10px;
      }
    }

    & ::placeholder {
      color: #000;
    }

    ::v-deep .el-input__inner {
      color: #000;
    }

    .el-select {
      margin-left: 10px;
    }
  }

  .card-title {
    color: #000;
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .card {
    padding: 17px 20px;
    background-color: #fff;
  }

  .chart-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .w-46 {
    width: 48%;
  }

  .mb40 {
    margin-bottom: 40px;
  }

  .t-title {
    color: #000;
    font-size: 16px;
    flex-shrink: 0;
  }

  .t-school {
    color: #000;
    font-size: 16px;
  }

  .t-point {
    color: #000;
    font-size: 20px;
    font-weight: 500;
  }

  .course-box {
    width: 260px;
    height: 260px;
    padding: 0 15px;
    box-sizing: border-box;
    cursor: pointer;

    img {
      width: 230px;
      height: 160px;
      object-fit: cover;
      border-radius: 10px;
    }

    .c-title {
      color: #000;
      font-size: 20px;
      font-weight: 500;
      height: 40px;
      display: flex;
      align-items: center;
    }

    .c-des {
      height: 30px;
      color: #000;
      font-size: 16px;
      display: flex;
      align-items: center;
      width: calc(100% - 80px);
    }

    .c-tag {
      width: 70px;
      border-radius: 7px;
      background: #E9F0F5;
      color: #476389;
      font-family: PingFang SC;
      font-size: 12;
      font-weight: 500;
      padding: 2px;
      text-align: center;
    }
  }
}
.datacenter-dialog {
  ::v-deep p {
    font-size: 16px;
    color: #000;
    margin: 10px 0;
  }
}
</style>
