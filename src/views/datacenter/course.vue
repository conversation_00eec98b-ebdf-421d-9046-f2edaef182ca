<template>
  <div class="w h overflow-auto">
    <Nav v-if="showHead" />
    <div class="datacenter-content">
      <div class="d-select-box">
        <div class="pointer back" @click="$router.push('/datacenter/index')">
          <i class="el-icon-back"></i>返回
        </div>
        <!-- role：ADMIN-超级管理员；PUBLISHER-出版社管理员；ORGANIZATION-教育局管理员；SCHOOL-学校管理员； -->
        <!-- 学期，地区，出版社，发行机构, 学校 -->
        <el-select
          v-if="['ADMIN', 'PUBLISHER', 'ORGANIZATION', 'SCHOOL'].indexOf(adminInfo.role) !== -1"
          v-model="term"
          placeholder="学期"
          clearable
          @change="termChange"
        >
          <el-option :label="'全部学期'" :value="0" />
          <el-option v-for="item in termList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>

        <el-cascader
          v-if="['ADMIN', 'PUBLISHER'].indexOf(adminInfo.role) !== -1"
          v-model="area"
          class="ml10"
          :props="props"
          placeholder="地区"
          clearable
          @change="areaChange"
        />

        <!-- <el-select
          v-if="['ADMIN', 'PUBLISHER'].indexOf(adminInfo.role) !== -1"
          v-model="publisher"
          placeholder="出版社"
          clearable
          @change="publisherChange"
        >
          <el-option v-for="item in publisherList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-select
          v-if="['ADMIN', 'PUBLISHER'].indexOf(adminInfo.role) !== -1"
          v-model="issuer"
          placeholder="发行机构"
          clearable
          @change="issuerChange"
        >
          <el-option v-for="item in issuerList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select> -->
        <el-select
          v-if="['ORGANIZATION'].indexOf(adminInfo.role) !== -1"
          v-model="school"
          placeholder="学校"
          clearable
          @change="schoolChange"
        >
          <el-option :label="'全部学校'" :value="0" />
          <el-option v-for="item in schoolList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>

      <div class="card w mb40">
        <div class="banner-box">
          <img class="b-img" :src="baseInfo.cover_url || DefaultCover" />

          <div class="b-content">
            <div class="b-title">
              <div class="w article-singer-container">
                {{ baseInfo.name }}
              </div>
            </div>
            <div class="b-des">
              {{ baseInfo.sub_title }}
            </div>
            <div class="flex">
              <div v-if="+baseInfo.course_total" class="b-tag">
                共{{ +baseInfo.course_total }}册
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-row :gutter="20" type="flex" justify="space-between">
        <el-col :span="8">
          <div class="h">
            <div class="card-title">
              基本数据
            </div>
            <div class="card w mb40 h-place">
              <div class="flex d-box">
                <div class="d-title">出版：</div>
                <div class="d-content">{{ baseInfo.publisher_name || '-' }}</div>
              </div>

              <div class="flex d-box">
                <div class="d-title">发行：</div>
                <div class="d-content">{{ baseInfo.issuer_name || '-' }}</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="16">
          <div class="h">
            <div class="card-title">
              合作情况
            </div>
            <div class="card w mb40 h-place">
              <div v-if="['ADMIN', 'PUBLISHER'].indexOf(adminInfo.role) !== -1" class="flex d-box">
                <div class="d-title">覆盖省份：</div>
                <div class="d-content">{{ baseInfo.province || '-' }}</div>
              </div>

              <div v-if="['ADMIN', 'PUBLISHER'].indexOf(adminInfo.role) !== -1" class="flex d-box">
                <div class="d-title">覆盖区县：</div>
                <div class="d-content">{{ baseInfo.district || '-' }}</div>
              </div>
              <div v-if="['ADMIN', 'PUBLISHER', 'ORGANIZATION'].indexOf(adminInfo.role) !== -1" class="flex items-center d-box pointer" @click="baseInfo.school_total ? handlerSchool() : ''">
                <div class="d-title">学校数量：</div>
                <div class="d-content">{{ baseInfo.school_total || '-' }}</div>
                <i v-if="baseInfo.school_total" class="el-icon-arrow-right"></i>
              </div>

              <div v-if="['ADMIN', 'ORGANIZATION', 'SCHOOL'].indexOf(adminInfo.role) !== -1" class="flex d-box">
                <div class="d-title">班级数量：</div>
                <div class="d-content">{{ baseInfo.class_total || '-' }}</div>
              </div>

              <div v-if="['ADMIN', 'ORGANIZATION', 'SCHOOL'].indexOf(adminInfo.role) !== -1" class="flex d-box">
                <div class="d-title">学生人数：</div>
                <div class="d-content">{{ baseInfo.student_total || '-' }}</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <template v-if="['ADMIN', 'ORGANIZATION', 'SCHOOL'].indexOf(adminInfo.role) !== -1">
        <div class="card-title">
          使用情况
        </div>

        <div class="card w mb40">
          <div class="w mb40">
            <el-row :gutter="10">
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总开设课程数：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.course_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总完成课程：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.course_finished_total }}
                  </div>
                </div>
              </el-col>

              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总完成课程率：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.course_percent }}%
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="w">
            <el-row :gutter="10">
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    总课时数：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.unit_total }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    已上课时数：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.unit_finished_total }}
                  </div>
                </div>
              </el-col>

              <el-col :span="6">
                <div class="flex items-center">
                  <div class="t-title">
                    已上课时率：
                  </div>
                  <div class="t-point">
                    {{ useDataInfo.unit_percent }}%
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>

      <div class="card-title">
        数字出版
      </div>

      <div v-show="aiCourseListInfo.length === 0" class="w card mb40">
        <el-empty class="w" description="暂无数据" />
      </div>
      <div v-for="item in aiCourseListInfo" :key="item.id" class="card2 w mb40">

        <div class="couser-box">
          <img class="b-img" :src="item.cover_url || DefaultCover" />

          <div class="b-content">
            <div class="b-title">
              <div class="w article-singer-container">
                {{ item.title }}
              </div>
            </div>
            <div class="w">
              <el-row :gutter="10">
                <el-col :span="6">
                  <div class="flex items-center">
                    <div class="t-title">
                      课时数：
                    </div>
                    <div class="t-point">
                      {{ item.total }}
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
    >
      <div class="datacenter-dialog" v-html="dialogNode"></div>
    </el-dialog>
  </div>
</template>

<script>
import Nav from './components/nav.vue'
import DefaultCover from '@/assets/images/default-cover.jpg'
import { mapGetters } from 'vuex'

import {
  packageBaseData,
  packageUseData,
  getAiCourseList,
  getSchoolList,
  getAreaByPid,
  getPublisher,
  getIssuer,
  getSchool
} from '@/api/datacenter-api.js'

export default {
  components: {
    Nav
  },
  data () {
    return {
      dialogVisible: false,
      dialogTitle: '',
      dialogNode: '',
      DefaultCover,
      props: {
        lazy: true,
        async lazyLoad (node, resolve) {
          const { level } = node
          const nodes = []

          // level: 层级 node 节点数据  一级菜单数据
          // 为1代表第一次请求
          const type = level === 0 ? '' : node.value
          if (level === 0) {
            nodes.push({ value: -1, label: '全部地区', leaf: true })
            const { data } = await getAreaByPid()
            // 节点数组
            // nodes.push({ value: '', label: '全部',leaf: node.level >= 2 })
            data.map((item) => {
            // {value:'',label:'全部'}
              const obj = {
                value: `${item.id},${item.name}`,
                label: item.name,
                leaf: node.level >= 2
              }
              nodes.push(obj)
            })
            // resolve 节点返回
            resolve(nodes)
          } else {
            if (level !== 0 && node.value !== -1) {
              const formData = new FormData()
              if (type) {
                formData.append('pid', type.split(',')[0])
              }

              const { data } = await getAreaByPid(formData)
              // 节点数组
              // nodes.push({ value: '', label: '全部',leaf: node.level >= 2 })
              data.map((item) => {
                // {value:'',label:'全部'}
                const obj = {
                  value: `${item.id},${item.name}`,
                  label: item.name,
                  leaf: node.level >= 2
                }
                nodes.push(obj)
              })
              // resolve 节点返回
              resolve(nodes)
            } else {
              resolve(nodes)
            }
          }
        }
      },
      area: [],
      publisher: '',
      publisherList: '',
      term: '',
      termList: '',
      issuer: '',
      issuerList: '',
      school: '',
      schoolList: '',
      schoolListObj: {},
      id: '',
      baseInfo: {
        'id': '',
        'name': '',
        'sub_title': '',
        'cover_url': '',
        'publisher_name': '',
        'issuer_name': '',
        'province': '',
        'district': '',
        'school_total': '',
        'class_total': '',
        'course_num': '',
        'student_total': ''
      },
      useDataInfo: {
        'course_total': '',
        'course_finished_total': '',
        'course_percent': '',
        'unit_total': '',
        'unit_finished_total': '',
        'unit_percent': ''
      },
      aiCourseListInfo: [],
      showHead: true
    }
  },
  computed: {
    ...mapGetters([
      'adminInfo'
    ])
  },
  mounted () {
    this.id = this.$route.query.id
    // this._getPublisher()
    // this._getIssuer()
    this.getTerm()
    this._getSchool()
    this.initDate()
    if (window.self !== window.top) {
      // 在 iframe 中
      this.showHead = false
    } else {
      this.showHead = true
    }
  },
  methods: {
    initDate () {
      this._baseData()
      this._useData()
      this._getAiCourseList()
    },
    getFormData () {
      const formData = new FormData()
      if (this.term) {
        const arr = this.term.split(',')
        formData.append('year', arr[0])
        formData.append('term', arr[1])
      }

      if (this.area && this.area.length > 2) {
        formData.append('province', this.area[0].split(',')[1])
        formData.append('city', this.area[1].split(',')[1])
        formData.append('district', this.area[2].split(',')[1])
      }

      if (this.publisher) {
        formData.append('publisher_id', this.publisher)
      }

      if (this.issuer) {
        formData.append('issuer_id', this.issuer)
      }
      if (this.school) {
        formData.append('school_id', this.school)
      }
      formData.append('package_id', this.id)
      return formData
    },
    async _baseData () {
      const formData = this.getFormData()
      const { data } = await packageBaseData(formData)
      this.baseInfo = data
    },
    async _useData () {
      const formData = this.getFormData()
      const { data } = await packageUseData(formData)
      this.useDataInfo = data
    },
    async _getAiCourseList () {
      const formData = this.getFormData()
      formData.append('page', 1)
      formData.append('limit', 1000)
      const { data } = await getAiCourseList(formData)
      this.aiCourseListInfo = data.items
    },
    async handlerSchool () {
      this.dialogTitle = '学校列表'
      const formData = this.getFormData()
      const { data } = await getSchoolList(formData)
      let div = ''
      if (data && data.length > 0) {
        data.map((val) => {
          div += `<p>
            <span>${val.school_name}</span>
            </p>`
        })
      }
      this.dialogNode = div
      this.dialogVisible = true
    },
    getTerm () {
      const data = new Date()
      let year = data.getFullYear() + 1
      const obj = []
      while (year > 2022) {
        obj.push({
          name: `${year - 1}~${year}学年度上学期`,
          id: `${year - 1},first`
        })
        obj.push({
          name: `${year - 1}~${year}学年度下学期`,
          id: `${year - 1},second`
        })
        year--
      }
      this.termList = obj
    },
    async _getPublisher () {
      const { data } = await getPublisher()
      this.publisherList = data
    },
    async _getIssuer () {
      const { data } = await getIssuer()
      this.issuerList = data
    },
    async _getSchool () {
      const { data } = await getSchool()
      this.schoolList = data
      const obj = {}
      if (data && data.length > 0) {
        data.map(val => {
          obj[val.id] = val.name
        })
      }

      this.schoolListObj = obj
    },
    termChange () {
      this.initDate()
    },
    areaChange () {
      this.initDate()
    },
    publisherChange () {
      this.initDate()
    },
    issuerChange () {
      this.initDate()
    },
    schoolChange (val) {
      console.log(val)
      this.initDate()
    }
  }
}
</script>

<style lang="scss" scoped>
.datacenter-content {
  padding-top: 110px;
  width: 1100px;
  margin: 0 auto;

  .d-select-box {
    display: flex;
    width: 100%;
    height: 60px;
    justify-content: flex-end;
    position: relative;

    .back {
      position: absolute;
      left: 0;
      color: #000;
      display: flex;
      align-items: center;
      height: 40px;
      font-size: 16px;
      i {
        margin-right: 10px;
      }
    }

    .ml10 {
      margin-left: 10px;
    }

    & ::placeholder {
      color: #000;
    }

    ::v-deep .el-input__inner {
      color: #000;
    }

    .el-select {
      margin-left: 10px;
    }
  }

  .card-title {
    color: #000;
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .card {
    padding: 17px 20px;
    background-color: #fff;
  }
  .card2 {
    padding: 17px 20px;
    background-color: #fff;
    border-radius: 12px;
    border: 1px solid #E0E0E0;
    background: #FDFDFD;
    box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.05);
  }

  .banner-box {
    display: flex;

    .b-img {
      width: 300px;
      height: 200px;
      object-fit: cover;
      flex-shrink: 0;
      border-radius: 10px;
    }

    .b-content {
      padding: 0 0 0 20px;
      width: calc(100% - 320px);
      box-sizing: border-box;
      .b-title {
        width: 100%;
        color: #333;
        font-size: 26px;
        font-weight: 500;
        margin-bottom: 20px;
      }
      .b-des {
        color: #000;
        font-size: 12px;
        font-weight: 300;
        margin-bottom: 20px;
        height: 100px;
        line-height: 20px;
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 5; //需要显示的行数
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .b-tag {
        color: #2D9CDB;
        font-size: 12;
        font-weight: 500;
        padding: 6px 4px;
        text-align: center;
        border-radius: 5px;
        background: rgba(45, 156, 219, 0.24);
        margin-right: 10px;
      }
    }
  }

  .d-box {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .d-title {
    color: #828282;
    font-size: 16px;
    font-weight: 500;
  }
  .d-content {
    color: #000;
    font-size: 16px;
    font-weight: 500;
  }

  .t-title {
    color: #000;
    font-size: 16px;
  }
  .t-point {
    color: #000;
    font-size: 20px;
    font-weight: 500;
  }

  .couser-box {
    display: flex;

    .b-img {
      width: 200px;
      height: 150px;
      object-fit: cover;
      flex-shrink: 0;
      border-radius: 10px;
    }

    .b-content {
      padding: 0 0 0 20px;
      width: calc(100% - 220px);
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .b-title {
        width: 100%;
        color: #000;
        font-size: 22px;
        font-weight: 500;
        margin-bottom: 20px;
      }
    }
  }

  .w-46 {
    width: 48%;
  }

  .mb40 {
    margin-bottom: 40px;
  }

  .h-place {
    height: calc(100% - 34px - 40px);
  }
}
.datacenter-dialog {
  ::v-deep p {
    font-size: 16px;
    color: #000;
    margin: 10px 0;
  }
}
</style>
