<template>
  <div class="datacenter-nav">
    <div class="flex items-center">
      <img class="logo" src="../../../assets/datacenter/logo.svg" />
      <span class="logo-t">缤果课堂数据统计平台</span>
    </div>
    <el-dropdown @command="handleCommand">
      <div class="flex items-center">
        <span class="user-t">{{ adminInfo.name }}</span>
        <img v-if="!adminInfo.logo" class="user" src="../../../assets/datacenter/logo.svg" />
        <img v-else class="user" :src="adminInfo.logo" />
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="quite">退出</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data () {
    return {}
  },
  computed: {
    ...mapGetters([
      'adminInfo'
    ])
  },
  methods: {
    async handleCommand (command) {
      if (command === 'quite') {
        await this.$store.dispatch('user/DataCenterLogout')
        this.$router.push({ path: `/datacenter/login` })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.datacenter-nav {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100px;
  background: #FFF;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.03);
  z-index: 99;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 50px 10px 50px;

  .logo {
    width: 50px;
    height: 50px;
  }

  .logo-t {
    margin-left: 10px;
    font-size: 20px;
  }

  .user {
    margin-left: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }

  .user-t {
    font-size: 21px;
    font-weight: 500;
  }
}
</style>
