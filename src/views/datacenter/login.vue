<template>
  <div class="w h datacenter">
    <template v-if="$route.query && $route.query.token">
      数据加载中...
    </template>
    <template v-else>
      <div>
        <img class="logo" src="../../assets/datacenter/logo.svg" />
      </div>
      <div class="logo-text">缤果课堂数据</div>
      <el-form
        ref="loginRuleForm"
        :model="loginRuleForm"
        :rules="loginRules"
      >
        <el-form-item prop="mobile" class="inputs">
          <div class="input-box">
            <el-input
              v-model="loginRuleForm.mobile"
              type="text"
              auto-complete="off"
              placeholder="输入手机号"
            />
          </div>
        </el-form-item>
        <div class="h40"></div>
        <el-form-item prop="yzm">
          <div class="input-box">
            <el-input
              v-model="loginRuleForm.yzm"
              :disabled="!loginRuleForm.mobile"
              type="text"
              auto-complete="off"
              placeholder="请输入验证码"
            >
              <el-button v-if="codeShow" slot="append" @click.stop="sendCode">发送验证码</el-button>
              <template v-else slot="append">{{ count }}秒后重试</template>
            </el-input>
          </div>

        </el-form-item>
      </el-form>

      <div class="login-btn" @click="_login">
        登录
      </div>
    </template>
  </div>
</template>

<script>
import { getYzm } from '@/api/datacenter-api.js'
export default {
  data () {
    return {
      codeShow: true,
      count: '',
      timer: null,
      loginRuleForm: {
        mobile: '',
        yzm: ''
      },
      loginRules: {
        mobile: [
          { required: true, message: '手机号必填', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
        ],
        yzm: [
          { required: true, message: '请输入验证码', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  mounted () {
    if (this.$route.query && this.$route.query.token) {
      window.adminToken = this.$route.query.token
      this.$store.dispatch('user/DataCenterTokenLogin', this.$route.query.token).then(() => {
        this.$router.push({ path: '/datacenter/index' }).catch(() => { })
      })
    }
  },
  methods: {
    async _login () {
      this.$refs['loginRuleForm'].validate(async (valid) => {
        if (valid) {
          this.$store.dispatch('user/DataCenterLogin', this.loginRuleForm).then(() => {
            this.$router.push({ path: '/datacenter/index' }).catch(() => { })
          })
        } else {
          return false
        }
      })
    },
    async sendCode () {
      try {
        if (this.loginRuleForm.mobile) {
          const Reg = /^[1][3456789][0-9]{9}$/
          if (Reg.test(this.loginRuleForm.mobile)) {
            const formData = new FormData()
            formData.append('mobile', this.loginRuleForm.mobile.trim())
            const { data } = await getYzm(formData)
            console.log(data)

            const TIME_COUNT = 60
            if (!this.timer) {
              this.count = TIME_COUNT
              this.codeShow = false
              this.timer = setInterval(() => {
                if (this.count > 0 && this.count <= TIME_COUNT) {
                  this.count--
                } else {
                  this.codeShow = true
                  clearInterval(this.timer)
                  this.timer = null
                }
              }, 1000)
            }
          }
        }
      } catch (error) {
        console.log(error)
        this.codeShow = true
        clearInterval(this.timer)
        this.timer = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.datacenter {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .logo {
    width: 100px;
    height: 100px;
  }

  .h40 {
    height: 30px;
  }

  .logo-text {
    color: #000;
    font-size: 34px;
    margin-top: 20px;
    margin-bottom: 60px;
  }

  .input-box {
    border-radius: 10px;
    border: 1px solid #D7D7D7;
    background: #FFF;
    width: 400px;
    height: 50px;
    overflow: hidden;

    ::v-deep .el-input__inner {
      background-color: transparent;
      border: none;
      height: 50px;
    }

    ::v-deep .el-input-group__append {
      border: none;
      height: 50px;
    }
  }

  .login-btn {
    width: 400px;
    height: 50px;
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    background: #2F80ED;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    margin-top: 40px;
  }
}
</style>
