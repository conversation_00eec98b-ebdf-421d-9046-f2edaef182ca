<template>
  <div class="geolocation-test">
    <div class="header">
      <h2>地理位置功能测试</h2>
    </div>

    <div class="content">
      <!-- 系统信息 -->
      <div class="section">
        <h3>系统信息</h3>
        <div class="status-item">
          <span>运行环境:</span>
          <span>{{ isElectron ? 'Electron应用' : '浏览器' }}</span>
        </div>
        <div class="status-item">
          <span>用户代理:</span>
          <span class="user-agent">{{ userAgent }}</span>
        </div>
        <div class="status-item">
          <span>HTTPS:</span>
          <span :class="isSecure ? 'status-success' : 'status-error'">
            {{ isSecure ? '是' : '否' }}
          </span>
        </div>
      </div>

      <!-- 权限状态 -->
      <div class="section">
        <h3>权限状态</h3>
        <div class="status-item">
          <span>API支持:</span>
          <span :class="supportStatus.class">{{ supportStatus.text }}</span>
        </div>
        <div class="status-item">
          <span>权限状态:</span>
          <span :class="permissionStatus.class">{{ permissionStatus.text }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="section">
        <h3>操作</h3>
        <div class="button-group">
          <button @click="checkPermission" :disabled="loading">检查权限</button>
          <button @click="requestPermission" :disabled="loading">请求权限</button>
          <button @click="getCurrentLocation" :disabled="loading">获取当前位置</button>
          <button @click="getIPLocation" :disabled="loading">获取IP位置</button>
          <button @click="startWatch" :disabled="loading || watching">开始监听</button>
          <button @click="stopWatch" :disabled="!watching">停止监听</button>
        </div>
      </div>

      <!-- 位置信息 -->
      <div class="section" v-if="locationData">
        <h3>位置信息</h3>
        <div class="location-info">
          <div class="info-item">
            <span>纬度:</span>
            <span>{{ locationData.latitude }}</span>
          </div>
          <div class="info-item">
            <span>经度:</span>
            <span>{{ locationData.longitude }}</span>
          </div>
          <div class="info-item" v-if="locationData.accuracy">
            <span>精度:</span>
            <span>{{ locationData.accuracy }}米</span>
          </div>
          <div class="info-item" v-if="locationData.city">
            <span>城市:</span>
            <span>{{ locationData.city }}</span>
          </div>
          <div class="info-item" v-if="locationData.region">
            <span>地区:</span>
            <span>{{ locationData.region }}</span>
          </div>
          <div class="info-item" v-if="locationData.country">
            <span>国家:</span>
            <span>{{ locationData.country }}</span>
          </div>
          <div class="info-item">
            <span>数据源:</span>
            <span>{{ locationData.source === 'gps' ? 'GPS定位' : 'IP定位' }}</span>
          </div>
          <div class="info-item" v-if="locationData.timestamp">
            <span>时间:</span>
            <span>{{ formatTime(locationData.timestamp) }}</span>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div class="section" v-if="errorMessage">
        <h3>错误信息</h3>
        <div class="error-message">{{ errorMessage }}</div>
      </div>

      <!-- 日志 -->
      <div class="section" v-if="logs.length > 0">
        <h3>操作日志</h3>
        <div class="logs">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import geolocationHelper from '@/utils/geolocation'

export default {
  name: 'GeolocationTest',
  data() {
    return {
      loading: false,
      watching: false,
      locationData: null,
      errorMessage: '',
      logs: [],
      permissionState: 'unknown',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '未知'
    }
  },
  computed: {
    isElectron() {
      return !!(window && window.process && window.process.type)
    },
    isSecure() {
      if (typeof window !== 'undefined' && window.location) {
        return window.location.protocol === 'https:' || window.location.hostname === 'localhost'
      }
      return false
    },
    supportStatus() {
      const isSupported = geolocationHelper.isGeolocationSupported()
      return {
        text: isSupported ? '支持' : '不支持',
        class: isSupported ? 'status-success' : 'status-error'
      }
    },
    permissionStatus() {
      const statusMap = {
        granted: { text: '已授权', class: 'status-success' },
        denied: { text: '已拒绝', class: 'status-error' },
        prompt: { text: '待询问', class: 'status-warning' },
        unknown: { text: '未知', class: 'status-info' },
        unsupported: { text: '不支持', class: 'status-error' }
      }
      return statusMap[this.permissionState] || statusMap.unknown
    }
  },
  mounted() {
    this.addLog('页面加载完成')
    this.checkPermission()
  },
  methods: {
    addLog(message) {
      this.logs.unshift({
        time: new Date().toLocaleTimeString(),
        message
      })
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20)
      }
    },

    async checkPermission() {
      this.addLog('检查权限状态...')
      try {
        this.permissionState = await geolocationHelper.checkPermission()
        this.addLog(`权限状态: ${this.permissionState}`)
      } catch (error) {
        this.addLog(`检查权限失败: ${error.message}`)
      }
    },

    async requestPermission() {
      this.loading = true
      this.errorMessage = ''
      this.addLog('开始请求地理位置权限...')
      this.addLog('注意：请在弹出的系统对话框中点击"允许"')

      try {
        const currentPermission = await geolocationHelper.checkPermission()
        this.addLog(`当前权限状态: ${currentPermission}`)

        if (currentPermission === 'granted') {
          this.addLog('权限已经被授予')
          await this.checkPermission()
          return
        }

        const granted = await geolocationHelper.requestPermission()
        if (granted) {
          this.addLog('✅ 权限请求成功！')
          await this.checkPermission()
        } else {
          this.addLog('❌ 权限请求被拒绝')
          this.addLog('提示：如果没有看到权限对话框，请检查系统设置中的位置权限')
        }
      } catch (error) {
        this.errorMessage = error.message
        this.addLog(`❌ 权限请求失败: ${error.message}`)
        this.addLog('提示：请在系统设置 > 安全性与隐私 > 位置服务中检查应用权限')
      } finally {
        this.loading = false
      }
    },

    async getCurrentLocation() {
      this.loading = true
      this.errorMessage = ''
      this.addLog('获取当前位置...')
      try {
        this.locationData = await geolocationHelper.getCurrentPosition()
        this.addLog('GPS定位成功')
      } catch (error) {
        this.errorMessage = error.message
        this.addLog(`GPS定位失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },

    async getIPLocation() {
      this.loading = true
      this.errorMessage = ''
      this.addLog('获取IP位置...')

      try {
        this.locationData = await geolocationHelper.getIPLocation()
        this.addLog('IP定位成功')
      } catch (error) {
        this.errorMessage = error.message
        this.addLog(`IP定位失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },

    startWatch() {
      this.addLog('开始监听位置变化...')
      this.watching = true
      geolocationHelper.watchPosition(
        (position) => {
          this.locationData = position
          this.addLog('位置更新')
        },
        (error) => {
          this.errorMessage = error.message
          this.addLog(`监听失败: ${error.message}`)
          this.watching = false
        }
      )
    },

    stopWatch() {
      this.addLog('停止监听位置变化')
      geolocationHelper.clearWatch()
      this.watching = false
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    }
  },
  beforeDestroy() {
    if (this.watching) {
      this.stopWatch()
    }
  }
}
</script>

<style scoped>
.geolocation-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
}

.section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.status-item, .info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.user-agent {
  font-size: 11px;
  color: #666;
  max-width: 400px;
  word-break: break-all;
  text-align: right;
}

.status-success { color: #52c41a; }
.status-error { color: #ff4d4f; }
.status-warning { color: #faad14; }
.status-info { color: #1890ff; }

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.button-group button {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.button-group button:hover:not(:disabled) {
  border-color: #40a9ff;
  color: #40a9ff;
}

.button-group button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.location-info {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
}

.error-message {
  color: #ff4d4f;
  background: #fff2f0;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ffccc7;
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  background: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-message {
  color: #333;
}
</style>
