<script>
export default {
  name: 'AnalysisTestItem',
  props: {
    questionStatsItem: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}

</script>

<template>
  <el-popover
    placement="right-start"
    width="200"
    trigger="hover"
  >
    <span slot="reference" class="choice-item-ratio">正确率：{{ (questionStatsItem.correctRate * 100).toFixed(0) }}%</span>
    <div>
      <div style="text-align: center;color: red;border-bottom: 1px solid #afafaf">正确率：{{ (questionStatsItem.correctRate * 100).toFixed(0) }}%</div>
      <div class="item_content_analysis">
        <div
          v-for="(item,index) in questionStatsItem.users"
          :key="index"
          style="display: flex;justify-content: space-between; padding: 5px 2px;font-size: 16px"
        >
          <span style="">{{ item.user }}</span>
          <span v-if="item.isCorrect" style="color: #1CBA76"><i class="el-icon-success"></i></span>
          <span v-else style="color: #FF0000"><i class="el-icon-error"></i></span>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<style scoped lang="scss">
.choice-item-ratio {
  margin-left: 20px;
  color: red;
  text-decoration: underline;
  text-underline-offset: 2px;
  cursor: pointer;
}

.item_content_analysis {
  max-height: 180px;
  overflow-y: auto;

  -webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
