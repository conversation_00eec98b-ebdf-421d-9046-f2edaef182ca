<template>
  <NormalDialog
    v-if="dialogShow"
    v-loading="loading"
    element-loading-text="AI分析中请稍等"
    width="90%"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="false"
    @closeDialog="close"
  >
    <div class="flex flex-col main w">
      <div v-if="testPaperType===0" class="header">
        <div v-if="testTitle" class="test_title">试卷标题：<span>{{ testTitle }}</span></div>
        <div class="score">分数情况：<span style="margin-left: 0;">
          <span v-if="choiceList.length !== 0" style="margin-left: 0;">选择题 共{{ choiceScore().num }}道 <span v-if="choiceScore().score" class="score_num">{{ choiceScore().score }}分</span></span>
          <span v-if="fillList.length !== 0">填空题 共{{ fillScore().num }}道 <span v-if="fillScore().score" class="score_num">{{ fillScore().score }}分</span></span>
          <span v-if="sopmlieList.length !== 0">判断题 共{{ sopmlieScore().num }}道 <span v-if="sopmlieScore().score" class="score_num">{{ sopmlieScore().score }}分</span></span>
          <span v-if="essayList.length !== 0">简答题 共{{ essayScore().num }}道 <span v-if="essayScore().score" class="score_num">{{ essayScore().score }}分</span></span>
          <span><span v-if="choiceScore().score+fillScore().score+sopmlieScore().score+essayScore().score" class="score_num">共{{ choiceScore().score+fillScore().score+sopmlieScore().score+essayScore().score }}分</span></span>
        </span>
        </div>
      </div>
      <div v-else class="header">
        <div class="result"><p>答题总数：<span>{{ userTestpaper?userTestpaper.totalQuantity:0 }}道题</span></p>
          <p>答对：<span>{{ userTestpaper?userTestpaper.rightQuantity:0 }}道题</span></p>
          <p>答错：<span>{{ userTestpaper?userTestpaper.wrongQuantity:0 }}道题</span></p>
        </div>
        <div v-if="analysis||userTestpaper.analysis" class="pingjia">综合评价(AI大模型提供)：<p v-html="analysis||userTestpaper.analysis"></p></div>
        <p v-if="showScore" class="score_num1">{{ userTestpaper?userTestpaper.score:0 }}分</p>
      </div>
      <div v-if="choiceList.length !== 0" class="test">
        <div class="content1">
          <div class="title">
            <p>选择题</p>
          </div>
          <div class="test_content">
            <div v-for="(item,index) in choiceList" :key="item.id" class="qus_item">
              <div class="item_content">
                <div>{{ index+1+'.'+' '+ item.question }}<span v-if="item.score">({{ item.score }}分)</span></div>
                <div v-for="(option,index1) in item.answerOptionList" :key="index1" class="chese_item" :class="hasAnser(item,option)?isWrong(item)?'answer_wrong':'selected':''" @click="testPaperType===0&&addAnser(item,option)">
                  <div class="lebel" :class="hasAnser(item,option)?isWrong(item)?'answer_wrong':'selected':''">{{ getOptionLabel(index1) }}</div>
                  {{ option.answer
                  }}</div>
                <div v-if="testPaperType===1" class="result_anwser" :class="isWrong(item)?'answer_wrong':''">正确答案：{{ getAnswer(item) }}</div>
                <div v-if="testPaperType===1&&item.answerUser&&item.answerUser.analysis" class="result_anwser">解析(AI大模型提供)：<span :class="isWrong(item)?'answer_wrong':''">{{ item.answerUser.analysis }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="fillList.length !== 0" class="test">
        <div class="content1">
          <div class="title">
            <p>填空题</p>
          </div>
          <div class="test_content">
            <div v-for="(item,index) in fillList" :key="item.id" class="qus_item">
              <div class="item_content">
                <Fill ref="Fill" :index1="index" :test-paper-type="testPaperType" :original-string="item" @inputDown="subMitFill" />
                <div v-if="testPaperType===1" class="result_anwser" :class="isWrong(item)?'answer_wrong':''">正确答案：{{ item.answer }}</div>
                <div v-if="testPaperType===1&&item.answerUser&&item.answerUser.analysis" class="result_anwser">解析(AI大模型提供)：<span :class="isWrong(item)?'answer_wrong':''">{{ item.answerUser.analysis }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="sopmlieList.length !== 0" class="test">
        <div class="content1">
          <div class="title">
            <p>判断题</p>
          </div>
          <div class="test_content">
            <div v-for="(item,index) in sopmlieList" :key="item.id" class="qus_item">
              <div class="item_content">
                <div>{{ index+1+'.'+' '+ item.question }}<span v-if="item.score">({{ item.score }}分)</span></div>
                <div v-for="(option,index1) in item.answerOptionList" :key="index1" class="chese_item" :class="hasAnser(item,option)?isWrong(item)?'answer_wrong':'selected':''" @click="testPaperType===0&&simpoleAnswer(item,option)">
                  <div class="lebel" :class="hasAnser(item,option)?isWrong(item)?'answer_wrong':'selected':''">{{ getOptionLabel(index1) }}</div>
                  {{ option.answer
                  }}</div>
                <div v-if="testPaperType===1" class="result_anwser" :class="isWrong(item)?'answer_wrong':''">正确答案：{{ getAnswer(item) }}</div>
                <div v-if="testPaperType===1&&item.answerUser&&item.answerUser.analysis" class="result_anwser">解析(AI大模型提供)：<span :class="isWrong(item)?'answer_wrong':''">{{ item.answerUser.analysis }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="essayList.length !== 0" class="test">
        <div class="content1">
          <div class="title">
            <p>简答题</p>
          </div>
          <div class="test_content">
            <div v-for="(item,index) in essayList" :key="item.id" class="qus_item">
              <div class="item_content">
                <div>{{ index+1+'.'+' '+ item.question }}<span v-if="item.score">({{ item.score }}分)</span></div>
                <el-input v-if="testPaperType===0" v-model="item.answer" style="margin-top: 20px;" class="essay_input" type="textarea" maxlength="500" :rows="5" show-word-limit @blur="testPaperType===0&&submitEssay(item)" />
                <div v-if="testPaperType===1" class="eszy_result">我的答案：<p>{{ item.answerUser&&item.answerUser.answerIds }}</p></div>
                <div v-if="testPaperType===1" class="eszy_result">参考答案：<p>{{ item.analysis }}</p></div>
                <div v-if="testPaperType===1&&item.answerUser&&item.answerUser.analysis" class="eszy_result">解析(AI大模型提供)：<p>{{ item.answerUser.analysis }}</p></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">{{ testPaperType===0?'提交':'重做' }}</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import { getTestPaperQuestionList, answerQuestion, redoTestPaper, submiteTestpaper } from '@/api/test-api'
import router from '../../../../router'
import Fill from './fill.vue'
export default {
  components: { NormalDialog, Fill },
  props: {
    testId: {
      type: String,
      default: '0'
    },
    ids: { type: String, default: '' }
  },
  data () {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '习题集',
      testTitle: '',
      testType: 0,
      content: '',
      questionList: [],
      choiceList: [],
      fillList: [],
      sopmlieList: [],
      essayList: [],
      testPaperType: 0,
      userTestpaper: null,
      showScore: false,
      studentCourseId: router.currentRoute.query.studentCourseId,
      analysis: '',
      loading: false
    }
  },
  mounted () {
  },
  methods: {
    isWrong(item) {
      if (this.testPaperType === 0) {
        return false
      } else if (item.answerUser && item.answerUser.result === 'WRONG') {
        return true
      } else {
        return false
      }
    },
    submitEssay(item) {
      if (!item.answer) {
        return
      }
      answerQuestion({ questionId: item.id, testPaperId: this.testId, studentCourseId: this.studentCourseId, answer: item.answer }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        item.answerUser = res.data
      })
    },
    subMitFill(data) {
      const item = data.item
      const anser = data.arr.join(',')
      if (!anser) {
        return
      }
      answerQuestion({ questionId: item.id, testPaperId: this.testId, studentCourseId: this.studentCourseId, answer: anser }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        item.answerUser = res.data
      })
    },
    addAnser(item, option) {
      let arr = item.answerUser && item.answerUser.answerIds ? item.answerUser.answerIds.split(',') : []
      if (arr.indexOf(String(option.id)) !== -1) {
        arr = arr.filter(function(item) {
          return item !== String(option.id)
        })
      } else {
        arr.push(String(option.id))
      }
      arr = arr.sort((a, b) => {
        const indexA = item.answerOptionList.findIndex(obj => String(obj.id) === a)
        const indexB = item.answerOptionList.findIndex(obj => String(obj.id) === b)
        return indexA - indexB
      })
      let answer = ''
      if (arr.length === 0) {
        answer = ''
      } else if (arr.length === 1) {
        answer = arr[0]
      } else {
        answer = arr.join(',')
      }
      answerQuestion({ questionId: item.id, testPaperId: this.testId, studentCourseId: this.studentCourseId, answer: answer }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        item.answerUser = res.data
      })
    },
    simpoleAnswer(item, option) {
      answerQuestion({ questionId: item.id, testPaperId: this.testId, studentCourseId: this.studentCourseId, answer: String(option.id) }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        item.answerUser = res.data
      })
    },
    hasAnser(item, option) {
      return item.answerUser && item.answerUser.answerIds.split(',').indexOf(String(option.id)) !== -1
    },
    choiceScore() {
      let score = 0
      this.choiceList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.choiceList.length }
    },
    fillScore() {
      let score = 0
      this.fillList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.fillList.length }
    },
    sopmlieScore() {
      let score = 0
      this.sopmlieList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.sopmlieList.length }
    },
    essayScore() {
      let score = 0
      this.essayList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.essayList.length }
    },
    async onSubmit() {
      if (this.testPaperType === 0) {
        this.loading = true
        setTimeout(() => {
          const arr = this.questionList.filter(item => { return !item.answerUser })
          if (arr.length !== 0) {
            this.$message.warning('请完成所有题目')
            this.loading = false
            return
          }
          submiteTestpaper({ testPaperId: this.testId, questionIds: this.ids, studentCourseId: this.studentCourseId }, {
            authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
          }).then(res => {
            this.analysis = res.data.userTestpaper.analysis
            this.loading = false
            this.testPaperType = 1
          })
            .catch((e) => {
              console.log('e', e)
              this.loading = false
              this._getTestPaperQuestionList()
            })
        }, 1000)
      } else {
        await redoTestPaper({ testPaperId: this.testId, studentCourseId: this.studentCourseId, questionIds: this.ids }, {
          authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
        })
        await this._getTestPaperQuestionList()
        this.testPaperType = 0
      }
    },
    formatterFill(item) {
      return item.replace(/\[\*\]/g, '___')
    },
    getAnswer(item) {
      let idArr = item.answer.split(',')
      idArr = idArr.map((item) => {
        return Number(item)
      })
      const answer = []
      let text = ''
      item.answerOptionList.forEach((choice, index) => {
        if (idArr.indexOf(choice.id) !== -1) {
          answer.push(index)
        }
      })
      answer.forEach((index) => {
        text = text + this.getOptionLabel(index) + ''
      })
      return text
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    // 拖拽结束事件

    close () {
      this.dialogShow = false
    },
    open () {
      this.$nextTick(() => {
        this.dialogShow = true
        this.testPaperType = 0
        this._getTestPaperQuestionList()
      })
    },
    async  _getTestPaperQuestionList() {
      await getTestPaperQuestionList({ testPaperId: this.testId, questionIds: this.ids, studentCourseId: this.studentCourseId }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        if (!res.data) {
          this.$message.warning('该题块已被删除')
          this.close()
          return
        }
        this.userTestpaper = res.data.userTestpaper
        if (this.userTestpaper && this.userTestpaper.progressStatus === 'FINISHED') {
          this.testPaperType = 1
        }
        this.showScore = res.data.configScore
        this.questionList = res.data.questionList
        this.testTitle = res.data.title
        this.choiceList = res.data.questionList.filter((item) => { return item.questionType === 'CHOICE' })
        this.fillList = res.data.questionList.filter((item) => { return item.questionType === 'FILL_IN_THE_BLANK_INPUT' })
        this.sopmlieList = res.data.questionList.filter((item) => { return item.questionType === 'SIMPLE_CHOOSE' })
        this.essayList = res.data.questionList.filter((item) => { return item.questionType === 'ESSAY_QUESTION' })
        this.essayList.forEach(item => {
          if (item.answerUser && item.answerUser.answerIds) {
            item.answer = item.answerUser.answerIds
          }
        })
      })
    },
    onCancel () {
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-loading-mask{
  background-color: rgba(255, 255, 255, 0.4)
}
.el-loading-parent--relative {
    position: relative;
}
.header{
    width: 95%;
    margin: 0 auto;
    background: #f9f7f7;
    border-radius: 10px;
    padding: 20px;
    position: relative;
    line-height: 25px;
    .score_num1{
        font-size: 30px;
        color:#409EFF ;
        position: absolute;
        top:0px;
        right:40px
    }
    .test_title{
      font-size: 12px;
      font-weight: 700;
      color: #000;
      span{
        font-weight: 400;
        color:#409EFF ;
      }
    }
    .pingjia{
        margin-top: 10px;
        font-size: 10px;
        color:#409EFF ;
        p{
            color: #000;
        }
    }
    .result{
      display: flex;
      width: 300px;
      justify-content: space-between;
      font-size: 12px;
      font-weight: 700;
      color: #000;
      span{
        font-weight: 400;
        color:#409EFF ;
      }
    }
    .score{
      font-size: 12px;
      font-weight: 700;
      color: #000;
      margin-top: 20px;
     .score_num{
        color:#409EFF ;
        margin-left: 5px;
     }
     span{
        font-weight: 400;
        margin-left: 20px;
     }
    }
}
.test{
    width: 95%;
    margin: 0 auto;
    background: #f9f7f7;
    border-radius: 10px;
    margin-top: 15px;
}
.main {
    padding: 0;
    ::v-deep .el-input__inner {
        transition: none;
    }
    .school-disc {
        margin-top: 10PX;
    }
.content1{
    width: 100%;
    line-height: 25px;
    .title{
        width: 100%;
        font-size: 12px;
        color: #333333;
        font-weight: 700;
        display: flex;
        padding-left: 20px;
        padding-right: 20px;
        justify-content: space-between;
        p{
            height: 20px;
        }
        .button{
            font-size: 20px;
        }
        }
        .test_content{
        min-height: 100px;
        width: 100%;
        .qus_item{
            width: 100%;
            font-size: 12px;
            .answer_btn{
                margin-top: 10px;
                padding:8px  20px 8px 20px  ;
                font-size: 12px;
            }
            .item_content{
            padding: 20px;
            white-space: pre-wrap;
            .eszy_result{
                font-size: 12px;
                color:#409EFF ;
                margin-top: 10px;
                p{
                    color:#4F4F4F
                }
            }
            .result_anwser{
                font-size: 12px;
                color: #000000;
                margin-top: 10px;
            }
            .chese_item{
                margin-top: 10px;
                line-height: 25px;
                font-size: 12px;
                display: flex;
                cursor: pointer;
                .lebel{
                min-width: 25px;
                height: 25px;
                border: 1px solid ;
                border-color: #E0E0E0;
                text-align: center;
                line-height: 25px;
                font-weight: 600;
                margin-right: 15px;
                }
            }
            .selected{
                color:#409EFF ;
                border-color: #409EFF !important;
             }
             .answer_wrong{
                color:red ;
                border-color: red !important;
             }
            }
        }
        ::v-deep .el-empty__image{
            width: 100px;
        }
        ::v-deep .el-empty__description{
            p{
                font-size: 18px;
                margin-top: -20px;
            }
        }
        }
}
    .mb10 {
        margin-bottom: 20px;
    }

    .school-disc2 {
        width: 5PX;
        height: 5PX;
        background: #828282;
        border-radius: 50%;
        margin-right: 5PX;
    }
    }
    </style>
