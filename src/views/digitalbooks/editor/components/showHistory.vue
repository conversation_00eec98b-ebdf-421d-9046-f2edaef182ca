<template>
  <NormalDialog
    v-if="dialogShow"
    width="1200px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="content1" v-html="content"></div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },
  props: {
    content: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '历史记录'
    }
  },
  mounted () {
  },
  methods: {
    close () {
      this.dialogShow = false
    },
    open () {
      this.dialogShow = true
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    }

  }
}
</script>

  <style lang="scss" scoped>
.content1{
    width: 100%;
    height: 400px;
    overflow: auto;
    @include scrollBar;
  ::v-deep table {
    border-collapse: collapse;
  }
  ::v-deep th {
    border: 1px solid #bbb;
  }
  ::v-deep td {
    border: 1px solid #bbb;
  }
}
  </style>
