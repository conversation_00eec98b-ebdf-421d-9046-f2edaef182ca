<template>
  <NormalDialog
    v-if="dialogShow"
    width="1000px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="mb10">
        <el-input v-model="listTitle" maxlength="20" show-word-limit class="w" placeholder="请输入视频描述" />
      </div>
      <div class="content">
        <div class="left">
          <el-button
            type="text"
            icon="el-icon-plus"
            @click="uploadImg"
          >添加封面</el-button>
          <div class="image">
            <img :src="imgSrc" alt="" />
          </div>
        </div>
        <div class="right">
          <el-button
            type="text"
            icon="el-icon-plus"
            @click="uploadVideo"
          >上传视频</el-button>
          <div class="video">
            <video v-if="videoSrc!==''" controls width="auto" height="auto">
              <source :src="videoSrc" />
            </video>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { Notification } from 'element-ui'
import empty3 from '@/assets/images/empty3.png'
export default {
  components: { NormalDialog },

  data () {
    return {
      cbs: null,
      imgSrc: '',
      videoSrc: '',
      tabIndex: 0,
      empty3: empty3,
      dialogShow: false,
      appendToBody: false,
      title: '添加视频块',
      listTitle: '',
      content: '',
      drag: false
    }
  },
  mounted () {
    this.dialogShow = true
  },
  methods: {
    uploadImg () {
      var input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      // 执行上传文件操作
      const _this = this
      input.addEventListener('change', async function (e) {
        var file = e.target.files[0]
        const { data } = await getFileUploadAuthor({
          mediaType: 'IMAGE',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })
        const ossCDN = data[0].ossConfig.ossCDN
        try {
          const formData = new FormData()
          formData.append('success_action_status', '200')
          formData.append('callback', '')
          formData.append('key', data[0].fileName)
          formData.append('policy', data[0].policy)
          formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
          formData.append('signature', data[0].signature)
          formData.append('file', file)
          const notif = Notification({
            title: `${file.name}上传中`,
            message: '0%',
            duration: 0
          })
          await axios.post(data[0].ossConfig.host, formData, {
            onUploadProgress: (progress) => {
              const complete = Math.floor(progress.loaded / progress.total * 100)
              notif.message = complete + '%'
              console.log(complete)
              if (complete >= 100) {
                notif.close()
              }
            }
          })
          _this.imgSrc =
            `${ossCDN}/${data[0].fileName}`
        } catch (error) {
          console.log(error)
        }
      }, false)
      input.click()
    },
    uploadVideo () {
      const _this = this
      var input = document.createElement('input')
      input.type = 'file'
      input.accept = 'video/mp4'
      // 执行上传文件操作
      input.addEventListener('change', async function (e) {
        var file = e.target.files[0]
        const { data } = await getFileUploadAuthor({
          mediaType: 'IMAGE',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })
        const ossCDN = data[0].ossConfig.ossCDN
        try {
          const formData = new FormData()
          formData.append('success_action_status', '200')
          formData.append('callback', '')
          formData.append('key', data[0].fileName)
          formData.append('policy', data[0].policy)
          formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
          formData.append('signature', data[0].signature)
          formData.append('file', file)
          const notif = Notification({
            title: `${file.name}上传中`,
            message: '0%',
            duration: 0
          })
          await axios.post(data[0].ossConfig.host, formData, {
            onUploadProgress: (progress) => {
              const complete = Math.floor(progress.loaded / progress.total * 100)
              notif.message = complete + '%'
              console.log(complete)
              if (complete >= 100) {
                notif.close()
              }
            }
          })
          _this.videoSrc = ''
          _this.$nextTick(() => {
            _this.videoSrc = `${ossCDN}/${data[0].fileName}`
          })
        } catch (error) {
          console.log(error)
        }
      }, false)
      input.click()
    },
    close () {
      this.dialogShow = false
      this.listTitle = ''
      this.imgSrc = ''
      this.videoSrc = ''
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    open (cbs = {}) {
      console.log(this)
      this.dialogShow = true
      this.cbs = cbs
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
    },
    onSubmit () {
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      if (this.videoSrc === '') {
        this.$message.warning('请上传视频')
        return
      }
      if (this.imgSrc === '') {
        this.$message.warning('请上传封面')
        return
      }
      // if (this.listTitle === '') {
      //   this.$message.warning('请输入标题')
      //   return
      // }
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit']({
          title: this.listTitle,
          postUrl: this.imgSrc,
          videoUrl: this.videoSrc
        })
        this.listTitle = ''
        this.imgSrc = ''
        this.videoSrc = ''
      }
    },
    setData (data) {
      this.listTitle = data.title
      this.imgSrc = data.postUrl
      this.videoSrc = data.videoUrl
    },
    onCancel () {
      if (Object.prototype.toString.call(this.cbs['onCancel']) === '[object Function]') {
        this.cbs['onCancel']()
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.editor-dig {
  ::v-deep .el-input__inner {
    transition: none;
  }
  .school-disc {
    margin-top: 10px;
  }
  .content {
    display: flex;
    justify-content: space-between;
  }
  .left {
    width: 500px;
    margin-top: 20px;
    .image {
      width: 400px;
      min-height: 200px;
      border: 1px dashed #1890ff;
      overflow: hidden;
      img {
        width: 400px;
      }
    }
  }
  .right {
    margin-top: 20px;
    width: 500px;

    .video {
      width: 400px;
      min-height: 200px;
      border: 1px dashed #1890ff;
      overflow: hidden;
      video {
        width: 400px;
      }
    }
  }
  .school-disc2 {
    width: 5px;
    height: 5px;
    background: #828282;
    border-radius: 50%;
    margin-right: 5px;
  }
}
</style>
