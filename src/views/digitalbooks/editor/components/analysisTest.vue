<template>
  <NormalDialog
    v-if="dialogShow"
    v-loading="loading"
    element-loading-text="AI分析中请稍等"
    width="90%"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="false"
    @closeDialog="close"
  >
    <div class="flex flex-col main w">
      <div class="header">
        <div class="header-left">
          <div class="team-overview-box">
            <div>班级人数：<span>{{ testData.users.length }}人</span></div>
            <div>作答：<span>{{ testData.users.length }}人</span></div>
            <div>未作答：<span>{{ 0 }}人</span></div>
            <div>满分：<span>{{ testData.overallStats.totalScore }}分</span></div>
            <div>平均分：<span>{{ testData.overallStats.averageScore }}分</span></div>
            <div>最高分：<span>{{ testData.overallStats.highestScore }}分</span></div>
            <div>最低分：<span>{{ testData.overallStats.lowestScore }}分</span></div>
            <div>全班正确率：<span>{{ (testData.overallStats.averageCorrectRate * 100).toFixed(0) }}%</span></div>
          </div>
          <div class="team-analysis-box">
            <div class="team-analysis-title">班级答题分析：</div>
            <div>
              实施分层辅导(优生攻函数建模、中生练错题变式、弱生抓计算口诀)结合GeoGebra动态图解与高频错题追踪，3个月班级均分突破75+。
            </div>
          </div>
          <!--          <div style="font-weight: bolder;">-->
          <!--            <span style="color: #5592e7;">每道题分布统计:</span>-->
          <!--            <div style="display: inline-block;background-color: red;width: 15px;height: 15px"></div>正确率30%以下-->
          <!--            <div style="display: inline-block;background-color: red;width: 15px;height: 15px"></div>正确率30%-50%-->
          <!--            <div style="display: inline-block;background-color: red;width: 15px;height: 15px"></div>正确率51%-80%-->
          <!--            <div style="display: inline-block;background-color: red;width: 15px;height: 15px"></div>正确率80%以上-->
          <!--          </div>-->
        </div>
        <div class="header-right">
          <div id="team-overview-chart" style="width: 100%;height: 100%;"></div>
        </div>
      </div>
      <div v-if="choiceList.length !== 0" class="test">
        <div class="content1">
          <div class="title">
            <p>选择题</p>
          </div>
          <div class="test_content">
            <div v-for="(item,index) in choiceList" :key="item.id" class="qus_item">
              <div class="item_content">
                <div>
                  {{ index + 1 + '.' + ' ' + item.question }}
                  <span v-if="item.score">({{ item.score }}分)</span>
                  <AnalysisTestItem :question-stats-item="testData.questionStats.find(i => i.id == item.id)" />
                </div>
                <div
                  v-for="(option,index1) in item.answerOptionList"
                  :key="index1"
                  class="chese_item"
                  :class="hasAnser(item,option)?isWrong(item)?'answer_wrong':'selected':''"
                  @click="testPaperType===0&&addAnser(item,option)"
                >
                  <div class="lebel" :class="hasAnser(item,option)?'selected':''">
                    {{ getOptionLabel(index1) }}
                  </div>
                  {{ option.answer
                  }}
                </div>
                <div class="result_anwser" :class="isWrong(item)?'answer_wrong':''">
                  正确答案：{{ getAnswer(item) }}
                </div>
                <div class="result_anwser">
                  <span style="color: #409EFF">解析(AI大模型提供)：</span>
                  <span :class="isWrong(item)?'answer_wrong':''">{{ item.analysis
                  }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="fillList.length !== 0" class="test">
        <div class="content1">
          <div class="title">
            <p>填空题</p>
          </div>
          <div class="test_content">
            <div v-for="(item,index) in fillList" :key="item.id" class="qus_item">
              <div class="item_content">
                <Fill
                  ref="Fill"
                  :index1="index"
                  :test-paper-type="1"
                  :question-stats-item="testData.questionStats.find(i => i.id == item.id)"
                  :original-string="item"
                  @inputDown="subMitFill"
                />
                <div class="result_anwser" :class="isWrong(item)?'answer_wrong':''">
                  正确答案：{{ item.answer }}
                </div>
                <div class="result_anwser">
                  <span style="color: #409EFF">解析(AI大模型提供)：</span>
                  <span :class="isWrong(item)?'answer_wrong':''">{{ item.analysis
                  }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="sopmlieList.length !== 0" class="test">
        <div class="content1">
          <div class="title">
            <p>判断题</p>
          </div>
          <div class="test_content">
            <div v-for="(item,index) in sopmlieList" :key="item.id" class="qus_item">
              <div class="item_content">
                <div>
                  {{ index + 1 + '.' + ' ' + item.question }}
                  <span v-if="item.score">({{ item.score }}分)</span>
                  <AnalysisTestItem :question-stats-item="testData.questionStats.find(i => i.id == item.id)" />
                </div>
                <div
                  v-for="(option,index1) in item.answerOptionList"
                  :key="index1"
                  class="chese_item"
                  :class="hasAnser(item,option)?isWrong(item)?'answer_wrong':'selected':''"
                  @click="testPaperType===0&&simpoleAnswer(item,option)"
                >
                  <div class="lebel" :class="hasAnser(item,option)?isWrong(item)?'answer_wrong':'selected':''">
                    {{ getOptionLabel(index1) }}
                  </div>
                  {{ option.answer
                  }}
                </div>
                <div class="result_anwser" :class="isWrong(item)?'answer_wrong':''">
                  正确答案：{{ getAnswer(item) }}
                </div>
                <div class="result_anwser">
                  <span style="color: #409EFF">解析(AI大模型提供)：</span>
                  <span :class="isWrong(item)?'answer_wrong':''">{{ item.analysis
                  }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="essayList.length !== 0" class="test">
        <div class="content1">
          <div class="title">
            <p>简答题</p>
          </div>
          <div class="test_content">
            <div v-for="(item,index) in essayList" :key="item.id" class="qus_item">
              <div class="item_content">
                <div>{{ index+1+'.'+' '+ item.question }}<span v-if="item.score">({{ item.score }}分)</span></div>
                <div class="eszy_result" style="font-weight: bolder">解析(AI大模型提供)：<p>{{ item.analysis }}</p></div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import { answerQuestion, getTestPaperQuestionList, redoTestPaper, submiteTestpaper } from '@/api/test-api'
import router from '../../../../router'
import Fill from './fill.vue'
import AnalysisTestItem from './analysisTestItem.vue'
import * as echarts from 'echarts'
import { generateTestData } from './analysisTest.js'
import { mapGetters } from 'vuex'

export default {
  components: { NormalDialog, Fill, AnalysisTestItem },

  props: {
    testId: {
      type: String,
      default: '0'
    },
    ids: { type: String, default: '' }
  },
  data() {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '习题集',
      testTitle: '',
      testType: 0,
      content: '',
      questionList: [],
      choiceList: [],
      fillList: [],
      sopmlieList: [],
      essayList: [],
      testPaperType: 0,
      userTestpaper: null,
      showScore: false,
      studentCourseId: router.currentRoute.query.studentCourseId,
      analysis: '',
      loading: false,
      classNum: 20,
      testData: {
        users: [],
        overallStats: {
          averageCorrectRate: 0
        },
        questionStats: []
      }
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  mounted() {
  },
  methods: {
    isWrong(item) {
      if (this.testPaperType === 0) {
        return false
      } else if (item.answerUser && item.answerUser.result === 'WRONG') {
        return true
      } else {
        return false
      }
    },
    submitEssay(item) {
      if (!item.answer) {
        return
      }
      answerQuestion({
        questionId: item.id,
        testPaperId: this.testId,
        studentCourseId: this.studentCourseId,
        answer: item.answer
      }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        item.answerUser = res.data
      })
    },
    subMitFill(data) {
      const item = data.item
      const anser = data.arr.join(',')
      if (!anser) {
        return
      }
      answerQuestion({
        questionId: item.id,
        testPaperId: this.testId,
        studentCourseId: this.studentCourseId,
        answer: anser
      }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        item.answerUser = res.data
      })
    },
    addAnser(item, option) {
      let arr = item.answerUser && item.answerUser.answerIds ? item.answerUser.answerIds.split(',') : []
      if (arr.indexOf(String(option.id)) !== -1) {
        arr = arr.filter(function(item) {
          return item !== String(option.id)
        })
      } else {
        arr.push(String(option.id))
      }
      arr = arr.sort((a, b) => {
        const indexA = item.answerOptionList.findIndex(obj => String(obj.id) === a)
        const indexB = item.answerOptionList.findIndex(obj => String(obj.id) === b)
        return indexA - indexB
      })
      let answer = ''
      if (arr.length === 0) {
        answer = ''
      } else if (arr.length === 1) {
        answer = arr[0]
      } else {
        answer = arr.join(',')
      }
      answerQuestion({
        questionId: item.id,
        testPaperId: this.testId,
        studentCourseId: this.studentCourseId,
        answer: answer
      }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        item.answerUser = res.data
      })
    },
    simpoleAnswer(item, option) {
      answerQuestion({
        questionId: item.id,
        testPaperId: this.testId,
        studentCourseId: this.studentCourseId,
        answer: String(option.id)
      }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        item.answerUser = res.data
      })
    },
    hasAnser(item, option) {
      return item.answer && item.answer.split(',').indexOf(String(option.id)) !== -1
    },
    choiceScore() {
      let score = 0
      this.choiceList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.choiceList.length }
    },
    fillScore() {
      let score = 0
      this.fillList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.fillList.length }
    },
    sopmlieScore() {
      let score = 0
      this.sopmlieList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.sopmlieList.length }
    },
    essayScore() {
      let score = 0
      this.essayList.forEach(item => {
        if (item.score) {
          score += item.score
        }
      })
      return { score, num: this.essayList.length }
    },
    async onSubmit() {
      if (this.testPaperType === 0) {
        this.loading = true
        setTimeout(() => {
          const arr = this.questionList.filter(item => {
            return !item.answerUser
          })
          if (arr.length !== 0) {
            this.$message.warning('请完成所有题目')
            this.loading = false
            return
          }
          submiteTestpaper({ testPaperId: this.testId, questionIds: this.ids, studentCourseId: this.studentCourseId }, {
            authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
          }).then(res => {
            this.analysis = res.data.userTestpaper.analysis
            this.loading = false
            this.testPaperType = 1
          })
            .catch((e) => {
              console.log('e', e)
              this.loading = false
              this._getTestPaperQuestionList()
            })
        }, 1000)
      } else {
        await redoTestPaper({
          testPaperId: this.testId,
          studentCourseId: this.studentCourseId,
          questionIds: this.ids
        }, {
          authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
        })
        await this._getTestPaperQuestionList()
        this.testPaperType = 0
      }
    },
    formatterFill(item) {
      return item.replace(/\[\*\]/g, '___')
    },
    getAnswer(item) {
      let idArr = item.answer.split(',')
      idArr = idArr.map((item) => {
        return Number(item)
      })
      const answer = []
      let text = ''
      item.answerOptionList.forEach((choice, index) => {
        if (idArr.indexOf(choice.id) !== -1) {
          answer.push(index)
        }
      })
      answer.forEach((index) => {
        text = text + this.getOptionLabel(index) + ''
      })
      return text
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    // 拖拽结束事件

    close() {
      this.dialogShow = false
    },
    open() {
      this.$nextTick(() => {
        this.dialogShow = true
        this.testPaperType = 0
        this._getTestPaperQuestionList()
      })
    },
    async _getTestPaperQuestionList() {
      await getTestPaperQuestionList({
        testPaperId: this.testId,
        questionIds: this.ids,
        studentCourseId: this.studentCourseId
      }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        if (!res.data) {
          this.$message.warning('该题块已被删除')
          this.close()
          return
        }
        this.userTestpaper = res.data.userTestpaper
        if (this.userTestpaper && this.userTestpaper.progressStatus === 'FINISHED') {
          this.testPaperType = 1
        }
        this.showScore = res.data.configScore
        this.questionList = res.data.questionList
        this.testTitle = res.data.title
        this.choiceList = res.data.questionList.filter((item) => {
          return item.questionType === 'CHOICE'
        })
        this.fillList = res.data.questionList.filter((item) => {
          return item.questionType === 'FILL_IN_THE_BLANK_INPUT'
        })
        this.sopmlieList = res.data.questionList.filter((item) => {
          return item.questionType === 'SIMPLE_CHOOSE'
        })
        this.essayList = res.data.questionList.filter((item) => {
          return item.questionType === 'ESSAY_QUESTION'
        })
        this.essayList.forEach(item => {
          if (item.answerUser && item.answerUser.answerIds) {
            item.answer = item.answerUser.answerIds
          }
        })
        console.log('this.ids', this.ids)
        const testDataQuestions = this.questionList.map(item => {
          return {
            id: item.id,
            score: item.score
          }
        })
        this.testData = generateTestData(this.testId, testDataQuestions)
        console.log('testData', this.testData)
        this.canvasTeamOverview()
      })
    },
    onCancel() {
    },
    canvasTeamOverview() {
      const teamOverviewChartDom = document.getElementById('team-overview-chart')
      const teamOverviewChart = echarts.init(teamOverviewChartDom)

      const score60 = (this.testData.overallStats.totalScore * 0.6).toFixed(0)
      const score80 = (this.testData.overallStats.totalScore * 0.8).toFixed(0)
      const xAxisData = [`0-${score60}分`, `${score60}-${score80}分`, `${score80}-${this.testData.overallStats.totalScore}分`]
      const seriesData = [
        {
          value: 0,
          itemStyle: {
            color: '#3479FF'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: '#FFA800'
          }
        },
        {
          value: 0,
          itemStyle: {
            color: '#8c00ff'
          }
        }
      ]
      this.testData.userResults.forEach(item => {
        console.log(item.totalScore)
        if (item.totalScore >= score60 && item.totalScore < score80) {
          seriesData[1].value += 1
        } else if (item.totalScore >= score80) {
          seriesData[2].value += 1
        } else {
          seriesData[0].value += 1
        }
      })
      const option = {
        title: {
          text: '分数段人数分布',
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          top: '35', // 顶部内边距
          bottom: '0', // 底部内边距
          left: '0',
          right: '0',
          containLabel: true // 包含坐标轴标签，避免标签被裁剪
        },
        xAxis: {
          data: xAxisData,
          axisTick: { show: false }, // 刻度与标签对齐
          splitLine: {
            show: true, // 显示网格线
            lineStyle: {
              type: 'dashed', // 虚线样式
              color: '#ccc' // 网格线颜色
            }
          }
        },
        yAxis: {
          axisLine: { onZero: true }, // 轴线在y=0处
          splitLine: {
            show: true, // 显示网格线
            lineStyle: {
              type: 'dashed', // 虚线样式
              color: '#ccc' // 网格线颜色
            }
          }
        },
        series: [{
          type: 'bar',
          data: seriesData,
          itemStyle: {
            borderRadius: [4, 4, 0, 0] // 柱子顶部圆角（若需）
          },
          barWidth: '30%', // 柱子宽度占比，可调整视觉效果
          label: {
            show: true,
            position: 'top',
            color: '#000000',
            fontSize: 12
          }
        }]
      }
      option && teamOverviewChart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.4)
}

.el-loading-parent--relative {
  position: relative;
}

.header {
  width: 95%;
  margin: 0 auto;
  background: #f9f7f7;
  border-radius: 10px;
  padding: 20px;
  position: relative;
  line-height: 25px;
  display: flex;

  .header-left {
    width: calc(100% - 260px);
    font-size: 12px;

    .team-overview-box {
      display: flex;
      flex-wrap: wrap;
      font-weight: bolder;
      color: #0F0F0F;

      div {
        flex: 0 0 25%;

        span {
          color: #5592e7;
        }
      }
    }

    .team-analysis-box {
      margin-top: 10px;

      .team-analysis-title {
        color: #3479FF;
        font-weight: bolder;
      }
    }
  }

  .header-right {
    min-width: 240px;
    margin-left: auto;
  }

  .score_num1 {
    font-size: 30px;
    color: #409EFF;
    position: absolute;
    top: 0px;
    right: 40px
  }

  .test_title {
    font-size: 12px;
    font-weight: 700;
    color: #000;

    span {
      font-weight: 400;
      color: #409EFF;
    }
  }

  .pingjia {
    margin-top: 10px;
    font-size: 10px;
    color: #409EFF;

    p {
      color: #000;
    }
  }

  .result {
    display: flex;
    width: 300px;
    justify-content: space-between;
    font-size: 12px;
    font-weight: 700;
    color: #000;

    span {
      font-weight: 400;
      color: #409EFF;
    }
  }

  .score {
    font-size: 12px;
    font-weight: 700;
    color: #000;
    margin-top: 20px;

    .score_num {
      color: #409EFF;
      margin-left: 5px;
    }

    span {
      font-weight: 400;
      margin-left: 20px;
    }
  }
}

.test {
  width: 95%;
  margin: 0 auto;
  background: #f9f7f7;
  border-radius: 10px;
  margin-top: 15px;
}

.main {
  padding: 0;

  ::v-deep .el-input__inner {
    transition: none;
  }

  .school-disc {
    margin-top: 10PX;
  }

  .content1 {
    width: 100%;
    line-height: 25px;

    .title {
      width: 100%;
      font-size: 12px;
      color: #333333;
      font-weight: 700;
      display: flex;
      padding-left: 20px;
      padding-right: 20px;
      justify-content: space-between;

      p {
        height: 20px;
      }

      .button {
        font-size: 20px;
      }
    }

    .test_content {
      min-height: 100px;
      width: 100%;

      .qus_item {
        width: 100%;
        font-size: 12px;

        .answer_btn {
          margin-top: 10px;
          padding: 8px 20px 8px 20px;
          font-size: 12px;
        }

        .item_content {
          padding: 20px;
          white-space: pre-wrap;

          .eszy_result {
            font-size: 12px;
            color: #409EFF;
            margin-top: 10px;

            p {
              color: #4F4F4F
            }
          }

          .result_anwser {
            font-weight: bolder;
            font-size: 12px;
            margin-top: 10px;
          }

          .chese_item {
            margin-top: 10px;
            line-height: 25px;
            font-size: 12px;
            display: flex;
            cursor: pointer;

            .lebel {
              min-width: 25px;
              height: 25px;
              border: 1px solid;
              border-color: #E0E0E0;
              text-align: center;
              line-height: 25px;
              font-weight: 600;
              margin-right: 15px;
            }
          }

          .selected {
            color: #409EFF;
            border-color: #409EFF !important;
          }

          .answer_wrong {
            color: red;
            border-color: red !important;
          }

        }
      }

      ::v-deep .el-empty__image {
        width: 100px;
      }

      ::v-deep .el-empty__description {
        p {
          font-size: 18px;
          margin-top: -20px;
        }
      }
    }
  }

  .mb10 {
    margin-bottom: 20px;
  }

  .school-disc2 {
    width: 5PX;
    height: 5PX;
    background: #828282;
    border-radius: 50%;
    margin-right: 5PX;
  }

}
</style>
