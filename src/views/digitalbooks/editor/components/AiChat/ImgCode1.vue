<template>
  <div class="box-card">
    <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="130px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="尺寸选择" prop="size">
            <el-select v-model="form.size" placeholder="请选择">
              <el-option
                v-for="size in sizes"
                :key="size"
                :label="size"
                :value="size"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="图像风格" prop="style">
            <el-select v-model="form.style" placeholder="请选择">
              <el-option
                v-for="(label, value) in styles"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生成数量" prop="n">
            <el-input-number v-model="form.n" :min="1" :max="4" controls-position="right" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="随机种子" prop="seed">
            <el-input-number v-model="form.seed" :min="0" :max="4294967295" controls-position="right" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  data () {
    return {
      sizes: [
        '720*1280',
        '768*1152',
        '1280*720',
        '1024*1024'
      ],
      styles: {
        '<auto>': '默认',
        '<photography>': '摄影',
        '<portrait>': '人像写真',
        '<3d cartoon>': '3D卡通',
        '<anime>': '动画',
        '<oil painting>': '油画',
        '<watercolor>': '水彩',
        '<sketch>': '素描',
        '<chinese painting>': '中国画',
        '<flat illustration>': '扁平插画'
      },
      form: {
        n: 1,
        size: '720*1280',
        style: '<auto>',
        seed: 0
      },
      rules: {

      }
    }
  },
  methods: {
    getData () {
      return this.form
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

::v-deep {
  // 表单项样式
  .el-form-item {
    margin-bottom: 10px;

    .el-form-item__label {
      font-size: 12px;
      color: #606266;
      padding-right: 12px;
      line-height: 32px;
      white-space: nowrap;
    }

    .el-form-item__content {
      line-height: 32px;
      white-space: nowrap;
    }
  }

  // 输入框通用样式
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
  }

  // 数字输入框样式
  .el-input-number {
    width: 100%;
    line-height: 30px;

    .el-input-number__decrease,
    .el-input-number__increase {
      display: none !important;  // 强制隐藏加减按钮
    }

    .el-input__inner {
      text-align: left;
      padding: 0 12px;
    }
  }

  // 下拉选择框样式
  .el-select {
    width: 100%;

    .el-input {
      width: 100%;
    }
  }
}

// 行间距
.el-row {
  margin-bottom: 0;

  &:last-child {
    margin-bottom: 0;
  }
}

// 列样式
.el-col {
  padding: 0 5px;
}

// 下拉菜单样式 - 移到全局样式
:global(.el-select-dropdown__item) {
  padding: 0 12px !important;
  height: 32px !important;
  line-height: 32px !important;
  font-size: 12px !important;
}
</style>
