<template>
  <div class="box-card">
    <el-form ref="form" :model="form" label-position="right" :rules="rules" label-width="130px">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="尺寸选择" prop="size">
            <el-select v-model="form.size" placeholder="请选择">
              <el-option
                v-for="size in sizes"
                :key="size"
                :label="size"
                :value="size"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="提示词相关性" prop="cfg_scale">
            <el-input-number v-model="form.cfg_scale" :min="0" :max="30" controls-position="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生成数量" prop="n">
            <el-input-number v-model="form.n" :min="1" :max="4" controls-position="right" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="随机种子" prop="seed">
            <el-input-number v-model="form.seed" :min="0" :max="4294967295" controls-position="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="迭代轮次" prop="steps">
            <el-input-number v-model="form.steps" :min="10" :max="50" controls-position="right" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采样方法" prop="sampler_index">
            <el-select v-model="form.sampler_index" placeholder="请选择">
              <el-option
                v-for="method in samplingMethods"
                :key="method"
                :label="method"
                :value="method"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  data () {
    return {
      sizes: [
        '768x768',
        '768x1024',
        '1024x768',
        '1024x1024',
        '1536x2048',
        '2048x1536'
      ],
      styles: {
        'Base': '基础风格',
        '3D Model': '3D模型',
        'Analog Film': '模拟胶片',
        'Anime': '动漫',
        'Cinematic': '电影',
        'Comic Book': '漫画',
        'Craft Clay': '工艺黏土',
        'Digital Art': '数字艺术',
        'Enhance': '增强',
        'Fantasy Art': '幻想艺术',
        'Isometric': '等距风格',
        'Line Art': '线条艺术',
        'Lowpoly': '低多边形',
        'Neonpunk': '霓虹朋克',
        'Origami': '折纸',
        'Photographic': '摄影',
        'Pixel Art': '像素艺术',
        'Texture': '纹理'
      },
      samplingMethods: [
        'Euler a',
        'Euler',
        'DPM++ 2M',
        'DPM++ 2M Karras',
        'LMS Karras',
        'DPM++ SDE',
        'DPM++ SDE Karras',
        'DPM2 a Karras',
        'Heun',
        'DPM++ 2M SDE',
        'DPM++ 2M SDE Karras',
        'DPM2',
        'DPM2 Karras',
        'DPM2 a',
        'LMS'
      ],
      form: {
        sampler_index: 'Euler a',
        steps: 20,
        n: 1,
        cfg_scale: 5,
        size: '768x768',
        style: '',
        seed: 0
      },
      rules: {
      }
    }
  },
  methods: {
    getData () {
      return this.form
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

::v-deep {
  // 表单项样式
  .el-form-item {
    margin-bottom: 10px;

    .el-form-item__label {
      font-size: 12px;
      color: #606266;
      padding-right: 12px;
      line-height: 32px;
      white-space: nowrap;
    }

    .el-form-item__content {
      line-height: 32px;
      white-space: nowrap;
    }
  }

  // 输入框通用样式
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
  }

  // 数字输入框样式
  .el-input-number {
    width: 100%;
    line-height: 30px;

    .el-input-number__decrease,
    .el-input-number__increase {
      display: none !important;  // 强制隐藏加减按钮
    }

    .el-input__inner {
      text-align: left;
      padding: 0 12px;
    }
  }

  // 下拉选择框样式
  .el-select {
    width: 100%;

    .el-input {
      width: 100%;
    }
  }
}

// 行间距
.el-row {
  margin-bottom: 0;

  &:last-child {
    margin-bottom: 0;
  }
}

// 列样式
.el-col {
  padding: 0 5px;
}

// 下拉菜单样式 - 移到全局样式
:global(.el-select-dropdown__item) {
  padding: 0 12px !important;
  height: 32px !important;
  line-height: 32px !important;
  font-size: 12px !important;
}
</style>
