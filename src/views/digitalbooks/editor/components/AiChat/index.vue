<template>
  <div v-if="show" class="main_content">
    <div class="head-box">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="handleClose" />
      <div class="head-title">{{ '步骤'+(index+1) }}</div>
    </div>
    <div class="main_content_box">
      <div class="left_content" v-html="info"></div>
      <div class="content_right">
        <div class="top_content" :class="!isEditMode ? 'no_edit' :aiType===1?'is_edit':'is_img'">
          <img
            v-if="content.length === 0"
            class="content_empty"
            src="../../../../../assets/aigc/right_empty.png"
            alt=""
          />
          <div v-else class="content_text">
            <div ref="scrollContent" class="inner" @click="showImg">
              <div
                v-for="(item, index) in content"
                :key="index"
                class="conversation_item"
                :class="item.type === 'user' ? 'user_text' : 'ai_text'"
              >
                <img v-if="item.type !== 'user'" :src="item.type === 'user' ? '' : aiUrl" alt="" />
                <div class="inner_text">
                  <template v-if="item.thinking">
                    <div class="thinking-process">
                      <div v-if="!item.completed" class="thinking-header">
                        <i class="el-icon-loading"></i>
                        <span>深度思考中...</span>
                      </div>
                      <div class="thinking-steps">
                        {{ item.thinking }}
                      </div>
                    </div>
                  </template>
                  <div class="markdown-content" v-html="renderMarkdown(item.text || '')"></div>
                </div>
              <!-- <img
                v-if="item.type === 'ai' && canUse && isEditMode"
                class="delete"
                src="../../../../../assets/aigc/delete.png"
                alt=""
                @click="deleteItem(index)"
              /> -->
              </div>
            </div>
          </div>
          <div v-if="isEditMode" class="type_button">
            <div
              class="set_button"
              :class="aiType === 1 ? 'selected' : 'deselect'"
              @click="setType(1)"
            >
              文生文
            </div>
            <div
              class="set_button"
              :class="aiType === 2 ? 'selected' : 'deselect'"
              @click="setType(2)"
            >
              文生图
            </div>
          </div>
        </div>

        <el-input
          v-if="isEditMode&&aiType===1"
          v-model="textarea"
          type="textarea"
          placeholder="请输入描述词"
          show-word-limit
          rows="3"
          class="textarea"
          style="margin-top: 10px"
        />
        <div v-else-if="isEditMode&&aiType===2" style="display: flex;">
          <el-input
            v-model="leftTextarea"
            type="textarea"
            placeholder="请输入正提示词"
            show-word-limit
            :maxlength="type === 'BAIDU_WENXIN'?150:500"
            rows="3"
            class="textarea"
            style="margin-top: 10px;margin-right: 10px;"
          />
          <el-input
            v-model="rightTextarea"
            type="textarea"
            placeholder="请输入逆提示词"
            show-word-limit
            rows="3"
            :maxlength="type === 'BAIDU_WENXIN'?150:500"
            class="textarea"
            style="margin-top: 10px"
          />
        </div>
        <div v-if="aiType===2&&type === 'BAIDU_WENXIN'" class="img_code">
          <imgCode ref="baidu" />
        </div>
        <div v-if="aiType===2&&type === 'ALI_TYQW'" class="img_code">
          <imgCode1 ref="ali" />
        </div>
        <div v-if="isEditMode" class="submit">
          <el-dropdown
            style="margin-left: 10px; margin-top: 10px; cursor: pointer"
          >
            <span class="el-dropdown-link">
              计算模型：{{
                type === 'BAIDU_WENXIN'
                  ? '文心一言'
                  : type === 'ALI_TYQW'
                    ? '通义千问'
                    : type === 'DEEPSEEK_V3' && aiType === 1
                      ? 'Deepseek-V3'
                      : type === 'DEEPSEEK_R1' && aiType === 1
                        ? 'Deepseek-R1'
                        : ''
              }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                class="dropdown-item"
                @click.native="type = 'BAIDU_WENXIN'"
              >文心一言</el-dropdown-item>
              <el-dropdown-item
                class="dropdown-item"
                @click.native="type = 'ALI_TYQW'"
              >通义千问</el-dropdown-item>
              <el-dropdown-item
                v-if="aiType === 1"
                class="dropdown-item"
                @click.native="type = 'DEEPSEEK_V3'"
              >Deepseek-V3</el-dropdown-item>
              <el-dropdown-item
                v-if="aiType === 1"
                class="dropdown-item"
                @click.native="type = 'DEEPSEEK_R1'"
              >Deepseek-R1</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="button_sub" @click="submit">
            立即生成
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import MarkdownIt from 'markdown-it'
import mk from 'markdown-it-katex'
import hljs from 'highlight.js'
import ImgCode from './ImgCode'
import ImgCode1 from './ImgCode1'
import axios from 'axios'
import { getToken } from '../../../../../utils/auth'
import { getFileUploadAuthor } from '@/api/user-api'
import { getTrainingAiChat, updateTrainingAigcImg } from '@/api/training-api'
import 'highlight.js/lib/languages/python'
import 'highlight.js/lib/languages/javascript'
import 'highlight.js/lib/languages/java'
import 'highlight.js/lib/languages/cpp'
import 'highlight.js/lib/languages/bash'
import 'highlight.js/styles/github.css'
import 'katex/dist/katex.min.css'
export default {
  name: 'AiChat',
  components: { ImgCode, ImgCode1 },
  props: {
    isEditMode: {
      type: Boolean,
      default: true
    },
    info: {
      type: String,
      default: ''
    },
    index: {
      type: Number,
      default: 0
    },
    trainingId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      textarea: '',
      userUrl: require('../../../../../assets/aigc/user.png'),
      aiUrl: require('../../../../../assets/aigc/ai.png'),
      canUse: true,
      type: 'ALI_TYQW',
      aiType: 1,
      show: false,
      coast: 1,
      leftTextarea: '',
      rightTextarea: '',
      fetchAbortController: null,
      md: null,
      content: [],
      userCourseId: this.$route.query.studentCourseId || 0,
      token: getToken() || 'Bearer ' + this.$route.query.token
    }
  },
  created() {
    this.initMarkdown()
    this.fetchAbortController = new AbortController()
    // 添加复制功能到 window 对象
    window.copyCode = function (button) {
      const pre = button.parentElement.parentElement
      const code = pre.querySelector('code')
      const textArea = document.createElement('textarea')
      textArea.value = code.textContent
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      button.textContent = '已复制!'
      setTimeout(() => {
        button.textContent = '复制'
      }, 2000)
    }
  },
  beforeDestroy() {
    this.fetchAbortController.abort()
  },
  methods: {
    open() {
      this.show = true
      this.$nextTick(() => {
        this._getTrainingAiChat()
        if (window.MathJax) {
          window.MathJax.typesetPromise()
        }
      })
    },
    async _getTrainingAiChat() {
      const res = await getTrainingAiChat({
        trainingId: this.trainingId,
        studentCourseId: this.userCourseId
      }, {
        authorization: this.token
      })
      const processedContent = []
      for (let i = 0; i < res.data.length; i++) {
        const item = res.data[i]
        processedContent.push({
          type: item.role === 'user' ? 'user' : 'ai',
          text: this.renderMarkdown(item.content || '')
        })
      }
      this.content = processedContent
    },
    handleClose() {
      this.show = false
    },
    initMarkdown() {
      this.md = new MarkdownIt({
        html: true,
        breaks: true,
        linkify: true,
        highlight: function (str, lang) {
          if (!lang) lang = 'plaintext'
          if (hljs.getLanguage(lang)) {
            try {
              return '<pre class="hljs"><div class="code-header">' +
                    '<span class="code-lang">' + lang + '</span>' +
                    '<button class="copy-btn" onclick="copyCode(this)">复制</button>' +
                    '</div><code class="' + lang + '">' +
                    hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                    '</code></pre>'
            } catch (__) {
              return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
            }
          }
          return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
        }
      }).use(mk, {
        throwOnError: true,
        trust: true,
        macros: {
          '\\sum': '\\sum\\limits',
          '\\f': '\\frac'
        }
      })
    },
    renderMarkdown(text) {
      if (!text) return ''
      try {
        return this.md.render(text)
      } catch (e) {
        console.error('Markdown render error:', e)
        return text
      }
    },
    setType(val) {
      this.aiType = val
      this.$emit('update:aiType', val)
    },
    deleteItem(index) {
      this.$confirm('是否要删除该条记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$emit('delete-item', index)
        })
        .catch(() => {})
    },
    showImg(e) {
      if (e.target.nodeName === 'IMG' && e.target.className !== 'delete') {
        this.$emit('show-img', e.target.currentSrc)
      }
    },
    async submit () {
      if (this.textarea === '' && this.aiType === 1) return
      if (this.leftTextarea === '' && this.aiType === 2) return
      if (!this.canUse) {
        this.$message.warning('正在回答中，请等待回答完毕！')
        return
      }
      this.aiType === 1 ? this.content.push({
        type: 'user',
        text: this.textarea
      }) : this.content.push({
        type: 'user',
        text: this.leftTextarea
      })
      this.content.push({
        type: 'ai',
        text: '正在生成中...',
        thinking: ''
      })
      // return
      this.canUse = false
      console.log(this.aiType)
      if (this.aiType === 1) {
        this.wordToWord()
      } else if (this.aiType === 2) {
        this.wordToImg()
      }
      this.textarea = ''
    },
    async wordToImg () {
      try {
        const formData = this.type === 'BAIDU_WENXIN' ? this.$refs.baidu.getData() : this.$refs.ali.getData()
        const response = await fetch(
          `${process.env.VUE_APP_BASE_API}/api/v1/training/trainingAigcImg?trainingId=${this.trainingId}&studentCourseId=${this.userCourseId}&aigcModeType=${this.type}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: `Bearer ${this.token}`

            },
            signal: this.fetchAbortController.signal,
            body: JSON.stringify({
              prompt: this.leftTextarea,
              negativePrompt: this.rightTextarea,
              ...formData
            })
          }
        )
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }

        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        while (result) {
          const { done, value } = await reader.read()
          if (done) {
            console.log('Stream ended')
            result = false
            if (JSON.parse(res).code !== 200) {
              this.$message.error(JSON.parse(res).message)
              this.canUse = true
              this.content[this.content.length - 1].text =
                '图片生成失败，请重试'
              return
            }
            this.aigcFreeTimes = JSON.parse(res).data.aigcFreeTimesLeft
            let imgList = JSON.parse(res).data.imgList
            const type = JSON.parse(res).data.type
            const uuid = JSON.parse(res).data.imgUuid
            if (type !== 'imgUrl') {
              imgList = await Promise.all(imgList.map(async (img) => {
                return await this.uploadImg(img)
              }))
            } else {
              imgList = await Promise.all(imgList.map(async (img) => {
                const base64Img = await this.getBase64Image(img)
                return await this.uploadImg(base64Img, false)
              }))
            }
            if (!imgList) {
              this.$message.error('图片生成失败，请重试')
              this.content[this.content.length - 1].text =
                '图片生成失败，请重试'
            } else {
              const imgHtml = imgList.map(img => `<img class='imgPost' src='${img}' data-src='${img}'/>`).join('')
              this.content[this.content.length - 1].text = imgHtml
              updateTrainingAigcImg({
                trainingId: this.trainingId,
                studentCourseId: this.userCourseId,
                imgUuid: uuid,
                imageUrl: imgHtml
              }, {
                authorization: this.token
              })
            }
            this.canUse = true
            break
          }

          const chunkText = textDecoder.decode(value)
          res += chunkText
          // console.log('Received chunk:', chunkText)
        }
      } catch (e) {
        // this.$message.error('图片生成失败，请重试')
        // this.content[this.content.length - 1].text = '图片生成失败，请重试'
        this.canUse = true
      }
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    async wordToWord () {
      try {
        const response = await fetch(
          `${process.env.VUE_APP_BASE_API}/api/v1/training/trainingAiChat?trainingId=${this.trainingId}&studentCourseId=${this.userCourseId}&aigcModeType=${this.type}`,
          {
            method: 'POST',
            signal: this.fetchAbortController.signal,
            body: this.textarea,
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: `Bearer ${this.token}`
            }
          }
        )
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }

        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        let thinking = ''
        while (result) {
          const { done, value } = await reader.read()
          console.log(value)
          if (done) {
            console.log('Done reading')
            result = false
            this.canUse = true
            this.content[this.content.length - 1].completed = true
            break
          }

          const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
          const arr = chunkText.split('-down-')
          arr.forEach((item) => {
            if (this.isJSON(item)) {
              if (JSON.parse(item).data) {
                res += JSON.parse(item).data.result
                if (JSON.parse(item).data.reasoning_content) {
                  thinking += JSON.parse(item).data.reasoning_content
                }
                this.aigcFreeTimes = JSON.parse(item).data.aigcFreeTimesLeft
              }
            }
          })
          this.content[this.content.length - 1].text = res
          this.content[this.content.length - 1].thinking = thinking
          this.$refs.scrollContent.scrollTop =
            this.$refs.scrollContent.scrollHeight
        }
      } catch (e) {
        this.canUse = true
        console.log(e)
      }
    },
    async uploadImg (base64, type = true) {
      let imgData
      if (type) {
        imgData = 'data:image/png;base64,' + base64
      } else {
        imgData = base64
      }
      console.log(imgData)
      const arr = imgData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const suffix = mime.split('/')[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      const file = new File([u8arr], `'test'.${suffix}`, {
        type: mime
      })
      const { data } = await getFileUploadAuthor({
        mediaType: 'IMAGE',
        contentType: '',
        quantity: 1,
        fileName: file.name
      })
      const ossCDN = data[0].ossConfig.ossCDN
      try {
        const formData = new FormData()
        formData.append('success_action_status', '200')
        formData.append('callback', '')
        formData.append('key', data[0].fileName)
        formData.append('policy', data[0].policy)
        formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
        formData.append('signature', data[0].signature)
        formData.append('file', file)
        await axios.post(data[0].ossConfig.host, formData, {})
        return `${ossCDN}/${data[0].fileName}`
      } catch (error) {
        console.log(error)
        return false
      }
    },
    getBase64Image (url) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        const canvas = document.createElement('canvas')
        img.crossOrigin = '*'
        img.src = url
        img.onload = function () {
          const width = img.width
          const height = img.height
          canvas.width = width
          canvas.height = height

          const ctx = canvas.getContext('2d')
          ctx.fillStyle = 'white'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          ctx.drawImage(img, 0, 0, width, height)
          const base64 = canvas.toDataURL()
          resolve(base64)
        }
        img.onerror = function (e) {
          reject(new Error(e))
        }
      })
    }
  }
}
</script>

  <style scoped lang="scss">
  .main_content{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    background: #F1F7FF;
    padding: 10px;
    .main_content_box{
      display: flex;
      width: 100%;
      height: 95%;
      .left_content{
      width: 400px;
      height: 100%;
      background: #FFFFFFBA;
      border-radius: 10px;
      margin-right: 30px;
      padding: 10px;
      overflow: auto;
      position: relative;
      @include scrollBar;
      }
    }
    .head-box {
    height: 10px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    margin-top: 10px;
    position: relative;
    .share {
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      text-decoration: underline
    }

    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: 12px;
      font-weight: 500;
      margin-left: 10px;
    }
  }
  }
  .dropdown-item {
    font-size: 10px;
    padding: 0 12px;
  }
  .textarea {
    font-size: 10px;
  }
  // 复制原来的相关样式
  .content_right {
    width: 100%;
    height: 100%;
    padding-right: 10px;
    overflow-x: hidden;
    overflow-y: auto;
    @include scrollBar;
    .is_edit {
      height: 75.5%;
    }
    .no_edit {
      height: 55%;
    }
    .is_img{
      height: 80%;
    }
    .top_content {
      width: 100%;
      background: #fff;
      border-radius: 10px;
      position: relative;
      .type_button {
        width: 95px;
        height: 20px;
        position: absolute;
        bottom: 5px;
        left: 10px;
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        .set_button {
          width: 44px;
          height: 20px;
          line-height: 20px;
          font-size: 10px;
          text-align: center;
          border-radius: 16px;
          cursor: pointer;
        }
        .selected {
          background: #2F80ED;
          color: #fff;
        }
        .deselect {
          color: #828282;
          background: #f2f2f2;
        }
      }
    }
  }

  .content_text {
    width: 100%;
    height: 100%;
    padding: 8px;
    line-height: 24px;
    font-size: 11px;
    padding-bottom: 40px;
    .inner {
      width: 100%;
      height: 100%;
      overflow: auto;
      padding-bottom: 20px;
      @include scrollBar;
    }
    .conversation_item:nth-child(odd) {
      margin-top: 30px;
    }
    .conversation_item:nth-child(1) {
      margin-top: 10px;
    }
    .user_text{
      justify-content: flex-end;
      .inner_text{
        background: #DFEDFF;
      }
    }
    .ai_text{
      justify-content: flex-start;
      .inner_text{
        background: #f2f2f2;
      }
    }
    .conversation_item {
      width: 100%;
      display: flex;
      position: relative;
      margin-top: 8px;
      gap: 10px;
      padding-right: 20px;
      .delete {
        position: absolute;
        right: 0;
        bottom: -18px;
        width: 16px;
        height: auto;
        cursor: pointer;
      }
      img {
        width: 21px;
        height: 21px;
        margin-top: 4px;
      }
      .inner_text {
        padding: 4px;
        padding-left: 8px;
        border-radius: 4px;
        font-size: 11px;
        line-height: 12px;
        img {
          width: 160px;
        }
      }
    }
  }
  .content_empty {
    width: 115px;
    height: 87px;
    margin: 0 auto;
    position: absolute;
    left: 40%;
    top: 40%;
  }

  .submit {
    width: 100%;
    height: 52px;
    background: #fff;
    border-radius: 10px;
    margin-top: 8px;
    position: relative;
  }

  .button_sub {
    width: 187px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    color: #fff;
    border-radius: 16px;
    position: absolute;
    right: 8px;
    bottom: 8px;
    font-size: 13px;
    background: #2F80ED;

    cursor: pointer;
  }

  .el-dropdown-link {
    font-size: 11px;
    color: #2F80ED;

  }

  .suanli {
    position: relative;
    color: #2F80ED;
    margin-left: 8px;
    margin-top: 8px;
    font-size: 11px !important;
  }

  .img_code{
    margin-top:5px;
  }

  .thinking-process {
    background: #f8f9fa;
    margin-bottom: 8px;
    color:#808080;
    white-space: pre-wrap;
    font-size: 10px;
    .thinking-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      color: #2F80ED;
      font-weight: 500;

      i {
        margin-right: 6px;
      }
    }

    .thinking-steps {
      padding: 8px;
      border-left:2px solid #c0c4cc;
    }
  }
  ::v-deep table {
    border: 1px solid #cac5c5;
    border-collapse: collapse;
    width: 100%;
    thead {
      border: 1px solid #cac5c5;
    }
    tr {
      border: 1px solid #cac5c5;
    }
  }

  /* Padding and font style */
  ::v-deep td,
  ::v-deep th {
    padding: 5px 10px;
    font-size: 10px;
    font-family: Verdana;
    color: #000;
    text-align: left;
    border: 1px solid #cac5c5;
  }

  /* Alternating background colors */
  ::v-deep tr:nth-child(even) {
    background: #f2f2f2;
  }
  ::v-deep tr:nth-child(odd) {
    background: #fff;
  }
  .katex {
    font-size: 1em !important;
    font-family: KaTeX_Main, "Times New Roman", serif !important;
    line-height: 1.4 !important;
  }

  .katex-display {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 1.5em 0.5em;
    margin: 0.8em 0 !important;
    background: #f8f9fa;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.08);
  }

  .katex-display > .katex {
    text-align: center;
    color: #2c3e50;
    max-width: 100%;
  }

  .katex .mfrac {
    margin: 0 0.1em;
    vertical-align: -0.5em;
  }

  .katex .msupsub {
    margin-right: 0.03em;
    margin-left: 0.03em;
  }

  .katex .mbin {
    margin: 0 0.1em;
  }

  .katex .mrel {
    margin: 0 0.1em;
  }

  .katex .mop {
    margin-top: 0.1em;
    margin-bottom: 0.1em;
  }

  .katex .delimsizing {
    vertical-align: middle;
  }

  .katex .array {
    vertical-align: middle;
    text-align: center;
  }

  .katex .vlist-t {
    vertical-align: middle !important;
  }

  .katex-display .katex {
    display: flex !important;
    justify-content: center;
    align-items: center;
    min-height: 2em;
  }

  .markdown-content {
    font-size: 10px;
    .katex {
      vertical-align: middle;
      margin: 0 0.1em;
    }
    p {
      line-height: 1.8 !important;
    }
  }

  /* 代码块样式 */
  .hljs {
    display: block;
    overflow-x: auto;
    padding: 0;
    background: #f8f9fa;
    border-radius: 4px;
    font-family: 'Courier New', Consolas, monospace;
    font-size: 14px;
    line-height: 1.5;
    margin: 1em 0;
    position: relative;
  }

  /* 代码块头部样式 */
  .code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5em 1em;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    border-radius: 4px 4px 0 0;
  }

  /* 语言标识样式 */
  .code-lang {
    color: #6c757d;
    font-size: 0.85em;
    text-transform: uppercase;
  }

  /* 复制按钮样式 */
  .copy-btn {
    padding: 4px 8px;
    font-size: 10px;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    font-weight: bold;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background-color: #e9ecef;
      border-color: #adb5bd;
    }
  }

  /* 数学公式样式 */
  .katex {
    font-size: 1.1em !important;
    font-family: KaTeX_Main, "Times New Roman", serif !important;
    line-height: 1.4 !important;
  }

  .katex-display {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 1.5em 0.5em;
    margin: 0.8em 0 !important;
    background: #f8f9fa;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.08);
  }

  .katex-display > .katex {
    text-align: center;
    color: #2c3e50;
    max-width: 100%;
  }

  /* 优化分数显示 */
  .katex .mfrac {
    margin: 0 0.1em;
    vertical-align: -0.5em;
  }

  /* 优化上下标显示 */
  .katex .msupsub {
    margin-right: 0.03em;
    margin-left: 0.03em;
  }

  /* 优化运算符显示 */
  .katex .mbin {
    margin: 0 0.1em;
  }

  .katex .mrel {
    margin: 0 0.1em;
  }

  /* 优化大型运算符 */
  .katex .mop {
    margin-top: 0.1em;
    margin-bottom: 0.1em;
  }

  /* 确保公式内容垂直居中 */
  .katex-display .katex {
    display: flex !important;
    justify-content: center;
    align-items: center;
    min-height: 2em;
  }

  .markdown-content {
    .katex {
      vertical-align: middle;
      margin: 0 0.1em;
    }

    p {
      line-height: 1.8 !important;
    }
  }

  .code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5em 1em;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  border-radius: 4px 4px 0 0;
}

/* 语言标识样式 */
.code-lang {
  color: #6c757d;
  font-size: 0.85em;
  text-transform: uppercase;
}
.copy-btn {
  padding: 4px 8px;
  font-size: 10px;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  font-weight: bold;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s;
}
  </style>

<style lang="scss">
/* 代码块基础样式 */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
  background: #f8f9fa;
  border-radius: 4px;
  font-family: 'Courier New', Consolas, monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 1em 0;
  border: 1px solid #e9ecef;
}

/* 代码块头部 */
.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  border-radius: 4px 4px 0 0;
  margin-top: 1em;
}

/* 语言标识 */
.code-lang {
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

/* 复制按钮 */
.copy-btn {
  padding: 4px 8px;
  font-size: 12px;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
  }

  &:active {
    background-color: #dee2e6;
  }
}

/* 代码内容容器 */
.hljs pre {
  margin: 0;
  padding: 16px;
  background: #ffffff;
  border-radius: 0 0 4px 4px;
}

/* 代码内容 */
.hljs code {
  font-family: 'Courier New', Consolas, monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* 代码块内的滚动条 */
.hljs::-webkit-scrollbar {
  height: 8px;
  background-color: #f5f5f5;
}

.hljs::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #ccc;
}

.hljs::-webkit-scrollbar-track {
  border-radius: 4px;
  background-color: #f5f5f5;
}

/* 确保代码块在 markdown-content 中正确显示 */
.markdown-content {
  .hljs {
    margin: 1em 0;
    font-size: 10px;
  }

  pre {
    margin: 0;
    code {
      display: block;
      font-size: 10px;
      padding: 1em;
      overflow-x: auto;
    }
  }
}
</style>
