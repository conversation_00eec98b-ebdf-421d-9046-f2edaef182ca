<template>
  <NormalDialog
    v-if="dialogShow"
    width="1500px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div v-if="info" class="content">
        <div class="middle">
          <video v-if="info.src!==''" width="100%" height="100%" controls :src="info.src" :poster="info.poster" alt=""></video>
          <p>{{ info.text }}</p>
        </div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },
  props: {
    info: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      dialogShow: false,
      appendToBody: false,
      title: '查看视频',
      index: 0

    }
  },
  mounted () {
  },
  methods: {
    close () {
      this.dialogShow = false
    },
    open () {
      this.dialogShow = true
    }
  }
}
</script>

<style lang="scss" scoped>

.editor-dig {
  .content{
    display: flex;
    justify-content: space-between;
    width: 100%;
    .middle{
      width: 100%;
      overflow: hidden;
      text-align: center;
      video{
        height:700px;
       display:block;
       margin: 0 auto;
      }
    }
  }
}
</style>
