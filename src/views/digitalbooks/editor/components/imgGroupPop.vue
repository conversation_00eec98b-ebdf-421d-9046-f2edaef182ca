<template>
  <NormalDialog
    v-if="dialogShow"
    width="100%"
    title="图集"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="main">
      <div class="left">
        <viewer v-if="info&&info.content.length!==0" ref="viewer" :options="options" :images="info.content" class="right" @inited="inited" @hide="isOpen=true">
          <template #default="scope">
            <img v-for="item in scope.images" :key="item.src" style="width: 0;height: 0;" :src="item.src" class="img-responsive" />
          </template>
        </viewer>
      </div>
      <div class="right">
        {{ info.content[index].info||'暂无内容' }}
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import 'viewerjs/dist/viewer.css'
import Vue<PERSON>iewer from 'v-viewer'
import Vue from 'vue'
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
Vue.use(VueViewer)
VueViewer.setDefaults({
  title: (image) => image.alt || ''
})
export default {
  components: {
    NormalDialog
  },
  props: {
    info: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      dialogShow: false,
      appendToBody: false,
      title: '查看图集',
      index: 0,
      isOpen: true,
      $viewer: null,
      options: {
        scalable: false,
        toolbar: true,
        inline: true,
        title: false,
        url: 'src',
        viewed: (e) => {
          this.index = e.detail.index
        },
        show: () => {
          this.isOpen = false
        },
        hide: () => {
          this.isOpen = true
        }
      }

    }
  },
  mounted () {
  },
  methods: {
    inited (viewer) {
      this.$viewer = viewer
    },
    close () {
      this.dialogShow = false
      this.index = 0
    },
    open () {
      this.dialogShow = true
    },
    go (val) {
      if (val === 'right') {
        if (this.index + 1 === this.info.content.length) { this.index = 0 } else { this.index += 1 }
      } else {
        if (this.index === 0) { this.index = this.info.content.length - 1 } else { this.index -= 1 }
      }
    }
  }
}
</script>
<style >
.viewer-title{
  font-size: 12px;
  color: #ffffff;
  height: auto;
  white-space: normal;
  text-align: left;
  text-indent: 2em;
  font-weight: 700;
  margin-bottom: 20px;
  opacity: 1;
}
.viewer-backdrop{
  background-color:#E2F5FF;
}
</style>
<style lang="scss" scoped>
.main{
  width: 100%;
  height: 450px;
  display: flex;
  .left{
    width: 75%;
  }
  .right{
    width: 25%;
    padding: 10px;
    font-size: 12px;
    white-space: pre-wrap;
  }
}
</style>
