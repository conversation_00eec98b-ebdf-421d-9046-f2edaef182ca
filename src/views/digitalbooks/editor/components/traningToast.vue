<template>
  <NormalDialog
    v-if="dialogShow"
    width="1200px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div v-if="data"  class="main" v-html="data"></div>
      <div v-show="!data" class="emty">
        <Empty description="暂无数据" />
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import Empty from '@/components/classPro/Empty/index.vue'
export default {
  components: { NormalDialog, Empty },

  data () {
    return {
      data: '',
      dialogShow: false,
      title: '实验说明'

    }
  },
  mounted () {
  },
  methods: {
    close () {
      this.dialogShow = false
    },
    open(data, title) {
      this.title = title
      this.data = data
      this.dialogShow = true
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .emty {
    width: 100%;
    height: 100%;
}
  .main{
    width: 100%;
    min-height: 400px;
    background: #ffffff;
  }

  .editor-dig {
    background: #F1F7FF;
    ::v-deep .el-input__inner {
      transition: none;
    }
    .school-disc {
      margin-top: 10PX;
    }

    .mb10 {
      margin-bottom: 10PX;
    }

    .school-disc2 {
      width: 5PX;
      height: 5PX;
      background: #828282;
      border-radius: 50%;
      margin-right: 5PX;
    }
  }
  </style>
