<template>
  <div class="system-view" style='top: 60px'>
    <div class="close-view" @click="handleClose">隐藏</div>
    <div class="title">您好！我是您的AI写作助手</div>
<!--    <div class="sub-title">，我有一下惊人能力：</div>-->
    <div class="list-view">
      <div
        class="list-item"
        v-for='(item, index) in systemList'
        :key="index"
        @click='handleClick(item.type)'
        :style="{
          background: item.background
        }">
        <div class="list-item-label">{{ item.label }}</div>
        <div class="list-item-description">{{ item.description }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SystemView',
  data() {
    return {
      systemList: [
        {
          label: 'AI教材目录生成',
          description: '快速生成教材大纲',
          background: 'linear-gradient(90deg, #84FAB0 0%, #8FD3F4 100%)',
          type: 'menu'
        },
        {
          label: 'AI教材内容生成',
          description: '根据提示词生成数字教材内容或分析相关资料生成数字教材',
          background: 'linear-gradient(90deg, #F6D365 0%, #FDA085 100%)',
          type: 'content'
        },
        {
          label: 'AI图片生成',
          description: '生成教材所需的图片内容',
          background: 'linear-gradient(90deg, #FBC2EB 0%, #A6C1EE 100%)',
          type: 'image'
        },
        {
          label: 'AI试题生成',
          description: '提炼教材内容，一键生成测试题',
          background: 'linear-gradient(90deg, #FEADA6 0%, #FDC2C2 100%)',
          type: 'training'
        }
      ]
    };
  },
  methods: {
    handleClose() {
      this.$emit('closeView');
    },
    handleClick(type) {
      this.$emit('handleClick', type);
    }
  },
}
</script>

<style scoped lang='scss'>
.system-view{
  position: absolute;
  z-index: 999;
  width: 100%;
  background: white;
  padding: 10px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.3);
  .close-view{
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    font-size: 12px;
    color: #2F80ED;
    cursor: pointer;
  }
  .title{
    font-size: 14px;
    font-weight: 500;
  }
  .sub-title{
    margin-top: 10px;
    font-size: 10px;
  }
  .list-view{
    width: 100%;
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    .list-item{
      width: calc(50% - 5px);
      height: 80px;
      border-radius: 10px;
      padding: 10px 10px;
      cursor: pointer;
      .list-item-label{
        width: 100%;
        font-size: 14px;
        font-weight: 500;
      }
      .list-item-description{
        width: 100%;
        font-size: 10px;
        margin-top: 10px;
        color: #666666;
      }
    }
  }
}
</style>
