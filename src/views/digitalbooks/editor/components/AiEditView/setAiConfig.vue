<template>
  <div class="set-body" v-if='show'>
    <div class="set-main" v-loading='fileLoading' element-loading-background="rgba(255, 255, 255, 0.5)" element-loading-text="文件上传中">
      <div class="set-title">
        <span v-if="type === 'menu'">{{`让我为您的书《${bookTitle}》编写一个目录`}}</span>
        <span v-else>{{`让AI帮您章节《${configTitle}》编写内容`}}</span>
      </div>
      <div class="set-form">
        <el-form ref='form' :model='formData'>
          <el-form-item label='目标提示词'>
            <el-input v-model='formData.tipStr' placeholder='请输入提示词' type="textarea" :rows="3" />
          </el-form-item>
          <el-form-item label='参考上传资料(选填)'>
            <el-upload
              class="uploader"
              :multiple="true"
              action=""
              :show-file-list="false"
              :before-upload="beforeUpload"
              style='width: 100%'
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
            >
              <i class="el-icon-plus uploader-icon">
                上传教材相关资料（支持多个文件上传）
              </i>
              <div slot="tip" class='upload-tip'>
                内容仅限于PDF、Word、Excel、PPT，限制6个文件。单个文件大小不超过150M
              </div>
            </el-upload>
          </el-form-item>
          <div class="file-list-view">
            <div class="file-item" v-for="item in fileList" :key="item.fileData">
              <div class="file-delete" @click="deleteFile(item)">
                <i class="el-icon-error"></i>
              </div>
              <img class="file-img" :src="officeImageUrl(item.name)" alt="文件图标" />
              <div class="file-name">{{item.name}}</div>
            </div>
          </div>
          <div class="bottom-view">
            <div class="btn-view" @click="handleCancel">取消</div>
            <div class="btn-view btn-primary" @click="handleGenerate">
              {{type === 'menu' ? 'AI生成章节' : 'AI立即生成'}}
            </div>
          </div>
        </el-form>
      </div>
      <div class="bottom-tip-view" v-if="type === 'content'">
        <el-checkbox v-model="aiTip">本书不再提示AI创作</el-checkbox>
      </div>
    </div>
    <AiPreviewView ref="previewRef" :type='type' :book-title='bookTitle' :chapter-title='chapterTitle' :configTitle='configTitle' :tree-str='treeStr' :desc='desc' @confirm='handleConfirm'/>
  </div>
</template>

<script>
import PPTXParser from 'pptx-parser';
import AiPreviewView from '@/views/digitalbooks/editor/components/AiEditView/aiPreviewView'
import axios from 'axios'
import parse from 'pptx-parser'

export default {
  name: 'SetAiConfig',
  components: {
    AiPreviewView
  },
  props: {
    bookTitle: {
      type: String,
      default: ''
    },
    aiTipProps: {
      type: Boolean,
      default: false
    },
    preNode:{
      type: Object,
      default: () => ({})
    },
    treeData: {
      type: Array,
      default: () => []
    },
    desc: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      wordImg: require('@/assets/digitalbooks/edit/word.png'),
      excelImg: require('@/assets/digitalbooks/edit/excel.png'),
      pptImg: require('@/assets/digitalbooks/edit/ppt.png'),
      pdfImg: require('@/assets/digitalbooks/edit/pdf.png'),
      show: false,
      formData: {
        tipStr: ''
      },
      fileList: [],
      type: 'menu',
      aiTip: this.aiTipProps,
      chapterTitle: '',
      configTitle: '',
      treeStr: '',
      fileLoading: false
    }
  },
  watch: {
    bookTitle (newVal) {
      if (newVal || newVal !== '') {
        this.formData.tipStr = `现要编写一本书，书名为《${newVal}》请为这本书生成目录大纲`
      }
    },
    chapterTitle (newVal) {
      if (newVal || newVal !== '') {
        this.formData.tipStr = `帮我生成《${this.bookTitle}》教材中${this.configTitle}的内容，要求内容详实、逻辑清晰、覆盖基础概念到动手实践。`
      }
    },
    aiTip (newVal) {
      this.$emit('updateAITip', newVal)
    }
  },
  methods: {
    close () {
      this.show = false
      this.$emit('close')
    },
    open (type) {
      this.type = type
      this.show = true
      this.fileList = []
      if (type === 'content'){
        this.configTitle = this.preNode.title
        this.chapterTitle = this.getSelectedNodeText(this.treeData, this.preNode)
        this.treeStr = this.treeToAIReadableText(this.treeData)
      }
    },
    async beforeUpload (file) {
      if (this.fileList.length >= 6) {
        this.$message.error('最多只能上传6个文件')
        return false
      }
      // if (file.name.endsWith('.pptx') || file.name.endsWith('.ppt')){
      //   await this.uploadPPT(file)
      // } else {
      //
      // }
      try {
        this.fileLoading = true
        const formData = new FormData()
        formData.append('file', file)
        const { data } = await axios.post(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/uploadAiFile`, formData)
        if (data.code === 200) {
          this.fileList.push({
            name: file.name,
            size: file.size,
            type: file.type,
            fileData: `fileid://${data.data}`
          })
        } else {
          this.$message.error(`上传文件${file.name}时${data.message}` || '文件上传失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.fileLoading = false
      }
    },
    async uploadPPT(file) {
      const isLtM = file.size / 1024 / 1024 < 80
      if (!isLtM) {
        this.$message.error('上传PPT大小不能超过80MB!')
        return
      }
      this.fileLoading = true
      const pptJson = await parse(file, { flattenGroup: true })
      const parsedText = this.extractTextFromPPT(pptJson)
      try {
        const formData = new FormData()
        const textBlob = new Blob([parsedText], { type: 'text/plain' })
        const txtFileName = file.name.replace(/\.(ppt|pptx)$/, '.txt')
        // 添加到FormData
        formData.append('file', textBlob, txtFileName)
        const { data } = await axios.post(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/uploadAiFile`, formData)
        if (data.code === 200) {
          this.fileList.push({
            name: file.name,
            size: file.size,
            type: file.type,
            fileData: `fileid://${data.data}`
          })
        } else {
          this.$message.error(`上传文件${file.name}时${data.message}` || '文件上传失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.fileLoading = false
      }
      // this.parsePptx(file)
    },
    // 从解析结果中提取文本
    extractTextFromPPT(pptData) {
      let textContent = ''
      // 遍历每一页幻灯片
      if (pptData && pptData.slides) {
        pptData.slides.forEach((slide, index) => {
          textContent += `=== 幻灯片 ${index + 1} ===\n`
          if (slide.pageElements){
            const subText = this.getSubText(slide.pageElements)
            textContent += subText + '\n'
          }
          textContent += '\n'
        })
      }
      return textContent
    },
    getSubText(list) {
      let textContent = ''
      list.forEach(ele => {
        // 提取文本
        if (ele.name && ele.name.includes('文本') && ele.shape && ele.shape.text && ele.shape.text.paragraphs) {
          ele.shape.text.paragraphs.forEach(paragraph => {
            if (paragraph && paragraph.textSpans && paragraph.textSpans.length > 0){
              paragraph.textSpans.forEach(run => {
                if (run.textRun && run.textRun.content) {
                  textContent += run.textRun.content + '\n'
                }
              })
            }
          })
        }
      })
      return textContent
    },
    officeImageUrl (fileName) {
      if (fileName.includes('.doc') || fileName.includes('docx')) {
        return this.wordImg
      } else if (fileName.includes('xls') || fileName.includes('xlsx')) {
        return this.excelImg
      } else if (fileName.includes('ppt') || fileName.includes('pptx')) {
        return this.pptImg
      } else if (fileName.includes('pdf')) {
        return this.pdfImg
      }
    },
    deleteFile (item) {
      const index = this.fileList.findIndex(file => file.fileData === item.fileData)
      if (index !== -1) {
        this.fileList.splice(index, 1)
      }
    },
    handleCancel() {
      this.close()
    },
    handleGenerate() {
      this.$refs.previewRef.open({
        tipStr: this.formData.tipStr,
        fileList: this.fileList
      })
    },
    getSelectedNodeText(treeData, selectedNode) {
      if (!treeData || !treeData.length || !selectedNode || !selectedNode.id) {
        return ""
      }
      let chapter = null
      let section = null
      let item = null
      // 遍历树形结构查找选中节点及其父节点
      for (const c of treeData) {
        // 检查是否是章节级别被选中
        if (c.id === selectedNode.id) {
          chapter = c
          item = c
          break
        }

        if (c.childCatalogue && c.childCatalogue.length > 0) {
          for (const s of c.childCatalogue) {
            // 检查是否是节级别被选中
            if (s.id === selectedNode.id) {
              chapter = c
              section = s
              item = s
              break
            }

            if (s.childCatalogue && s.childCatalogue.length > 0) {
              for (const i of s.childCatalogue) {
                // 检查是否是最底层节点被选中
                if (i.id === selectedNode.id) {
                  chapter = c
                  section = s
                  item = i
                  break
                }
              }
              if (item) break
            }
          }
          if (item) break
        }
      }
      // 如果没有找到对应的节点，返回空
      if (!item) {
        return ""
      }
      // 构建结果字符串
      let result = ""

      // 添加章信息
      if (chapter) {
        result += chapter.title
      }

      // 添加节信息
      if (section) {
        result += `的${section.title}`
      }

      // 如果选中的是最底层节点且与节不同，添加具体内容
      if (item !== section && item !== chapter) {
        result += `的${item.title}`
      }

      return result
    },
    // 树形结构转换成文字
    treeToAIReadableText(treeData, options = {}) {
      const {
        chapter: chapterName = '章',
        section: sectionName = '节',
        item: itemName = '小节'
      } = options
      let result = ''
      // 遍历章
      treeData.forEach((chapter, chapterIndex) => {
        // 跳过无效章节
        if (!chapter || !chapter.title) return

        // 章节编号从1开始
        const chapterNumber = chapterIndex + 1
        result += `第${chapterNumber}${chapterName}：${chapter.title}\n`

        // 遍历节
        if (chapter.childCatalogue && Array.isArray(chapter.childCatalogue)) {
          chapter.childCatalogue.forEach((section, sectionIndex) => {
            // 跳过无效节
            if (!section || !section.title) return

            // 节编号从1开始
            const sectionNumber = sectionIndex + 1
            result += `  第${sectionNumber}${sectionName}：${section.title}\n`

            // 遍历子项
            if (section.childCatalogue && Array.isArray(section.childCatalogue)) {
              section.childCatalogue.forEach((item, itemIndex) => {
                // 跳过无效子项
                if (!item || !item.title) return

                // 子项编号从1开始
                const itemNumber = itemIndex + 1
                result += `    第${itemNumber}${itemName}：${item.title}\n`
              })
            }
          })
        }
      })

      return result
    },
    handleConfirm(type, html) {
      this.show = false
      this.$emit('confirm', type, html)
    }
  }
}
</script>

<style scoped lang='scss'>
.set-body{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  .set-main{
    width: 550px;
    max-height: 500px;
    overflow-y: auto;
    background: linear-gradient(0deg, #EBF8FF 0%, #E4F5FF 100%);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 20px 10px 20px;
    text-align: center;
    font-size: 18px;
    color: #333;
    .set-title{
      font-size: 22px;
      font-weight: 500;
      margin-bottom: 20px;
      color: rgba(47, 128, 237, 1);
    }
    .set-form{
      width: 100%;
    }
    .bottom-tip-view{
      width: 100%;
      display: flex;
      justify-content: start;
      margin-top: 10px;
    }
  }
}
::v-deep .el-form-item{
  margin-bottom: 10px;
}
::v-deep .el-form-item__label{
  width: 100px;
}
::v-deep .el-form-item__content{
  display: flex;
}
::v-deep .uploader .el-upload {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  background-color: white;
}
.uploader-icon {
  font-size: 14px;
  color: #8c939d;
  width: 100%;
  height: 80px;
  line-height: 80px;
  text-align: center;
}
.upload-tip{
  width: 100%;
  height: 20px;
  font-size: 10px;
  color: #8c939d;
  margin-top: -20px;
  display: flex;
  justify-content: end;
}
.file-list-view{
  width: 100%;
  padding: 2px 2px 2px 100px;
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  .file-item{
    width: 130px;
    height: 50px;
    border: 1px solid #d9d9d9;
    background-color: #f5f5f5;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    position: relative;
    padding: 2px;
    .file-delete{
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      color: #ff4d4f;
      font-size: 14px;
    }
    .file-img{
      width: 30px;
      height: 40px;
      object-fit: scale-down;
    }
    .file-name{
      flex: 1;
      margin-left: 5px;
      font-size: 12px;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.bottom-view{
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  align-items: center;
}
.btn-view{
  width: 160px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
  color: rgba(47, 128, 237, 1);
  border: 1px solid rgba(47, 128, 237, 1);
  background-color: white;
  font-size: 14px;
}
.btn-primary{
  background-color: rgba(47, 128, 237, 1);
  color: white;
}
</style>
