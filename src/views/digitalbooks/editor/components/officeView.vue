<template>
  <NormalDialog
    v-if="dialogShow"
    width="1500px"
    title="查看附件"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="w h" style="position: relative;">
      <div class="empty">
        <img class="loadingImg" src="@/assets/images/loading.gif" alt="占位图2" />
      </div>
      <div ref="ratioBox" class="radio-box">
        <div id="whiteboardOffice" class="w h"></div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import { mapGetters } from 'vuex'
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { generateWebofficeToken, refreshWebofficeToken } from '@/api/aliyun-api'
import { getToken } from '@/utils/auth'
export default {
  components: { NormalDialog },
  props: {
    url: {
      type: String,
      default: ''
    },
    ctoken: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogShow: false,
      token: {
        boardToken: '',
        boardUuid: ''
      },
      hasData: false

    }
  },
  computed: {
    ...mapGetters(['id'])
  },
  mounted() {

  },
  methods: {
    close() {
      this.dialogShow = false
    },
    open() {
      this.$nextTick(() => {
        this._getGenerateToken([this.url], getToken() || this.ctoken)
        this.dialogShow = true
      })
    },
    async _getGenerateToken(list, token) {
      if (token) {
        this.accessToken = token
      }
      if (list.length === 0) {
        return false
      }
      this.hasData = true
      const header = this.accessToken === '' ? {} : { authorization: this.accessToken }
      const { data } = await generateWebofficeToken({
        fileUrl: list[0]
        // coursecommUnitResourceId: list[0].id
      }, header)
      this.tokenList = data.body
      this.weboffice({
        AccessToken: data.body.accessToken,
        WebofficeURL: data.body.webofficeURL
      })
    },
    weboffice(tokenInfo) {
      var mount = document.getElementById('whiteboardOffice')
      var demo = window.aliyun.config({
        mount: mount,
        url: tokenInfo.WebofficeURL,
        refreshToken: this.refreshTokenPromise
      })
      demo.setToken({
        token: tokenInfo.AccessToken,
        timeout: 25 * 60 * 1000
      })
    },
    async refreshTokenPromise() {
      const header = this.accessToken === '' ? {} : { authorization: this.accessToken }
      const { data } = await refreshWebofficeToken({
        accessToken: this.tokenList.accessToken,
        refreshToken: this.tokenList.refreshToken
      }, header)
      return {
        token: data.body.accessToken,
        timeout: 10 * 60 * 1000
      }
    }

  }

}
</script>

<style scoped lang="scss">
.empty{
    position: absolute;
    height: 500px;
    width: 100%;
    left: 0;
    top:0;
    img{
        width: 300px;
        position: absolute;
        left: 250px;
        top:140px
    }
}
  .radio-box {
    width: 100%;
    height: 500px;
    position: relative;
    z-index: 11;
    #whiteboardOffice{
        height: 100%;
    }
  }
</style>
