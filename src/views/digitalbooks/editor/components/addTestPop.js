
import addTestPop from './addTestPop.vue'
import Vue from 'vue'
let $vm, dialogConstructor

export function addTestPopModal (cbs) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTestPop)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().open(cbs)
}
export function closeTest () {
  $vm.$mount().close()
}
export function setData (params) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTestPop)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().setData(params)
}

