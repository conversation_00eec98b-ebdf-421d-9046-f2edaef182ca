<template>
  <NormalDialog
    v-if="dialogShow"
    width="400px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="mb10">
        <el-input v-model="keyword" class="w" placeholder="请输入关键字" disabled />
      </div>
      <div class="mb10">
        <el-input v-model="content" :rows="3" type="textarea" class="w" placeholder="请输入气泡内容" />
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },

  data () {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '添加气泡',
      keyword: '',
      content: ''
    }
  },
  mounted () {
    this.dialogShow = true
  },
  methods: {
    close () {
      this.dialogShow = false
      this.keyword = ''
      this.content = ''
    },
    open (cbs = {}) {
      console.log(this)
      this.dialogShow = true
      this.cbs = cbs
    },
    onSubmit () {
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit']({
          keyword: this.keyword,
          content: this.content
        })
        this.keyword = ''
        this.content = ''
      }
    },
    setData (data) {
      this.keyword = data.keyword
      this.content = data.content
    },
    onCancel () {
      if (Object.prototype.toString.call(this.cbs['onCancel']) === '[object Function]') {
        this.cbs['onCancel']()
      }
    }

  }
}
</script>

<style lang="scss" scoped>

.editor-dig {
  ::v-deep .el-input__inner {
    transition: none;
  }
  .school-disc {
    margin-top: 10PX;
  }

  .mb10 {
    margin-bottom: 10PX;
  }

  .school-disc2 {
    width: 5PX;
    height: 5PX;
    background: #828282;
    border-radius: 50%;
    margin-right: 5PX;
  }
}
</style>
