<template>
  <NormalDialog
    v-if="dialogShow"
    width="900px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="closewithCancel"
  >
    <div class="flex flex-col editor-dig w" style='height: 80vh'>
      <p class="label">{{ formatBytes(size) }}
        <el-tooltip popper-class="tips_tooltip" effect="dark" content="png格式调整图片质量无效，请调整分辨率" placement="top-start">
          <i class="el-icon-question item"></i>
        </el-tooltip>
      </p>
      <p class="label">图片质量</p>
      <Slider v-model="valSet" class="slider" :min="10" @change="val=valSet" />
      <p class="label">分辨率</p>
      <Slider v-model="val1Set" class="slider" :min="10" @change="val1=val1Set" />
      <div class='set_img'>
        <el-image class="set_img_view" :src='urlres' fit="cover"/>
      </div>

    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { Slider } from 'element-ui'
export default {
  components: { NormalDialog, Slider },

  data () {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '设置图片尺寸',
      val: '',
      val1: '',
      url: '',
      size: '',
      val1Set: '',
      valSet: ''
    }
  },
  computed: {
    urlres() {
      return this.url + '?x-oss-process=image/quality,q_' + this.val + '/resize,p_' + this.val1
    }
  },
  watch: {
    urlres(val) {
      const _this = this
      fetch(val).then(function(res) {
        return res.blob()
      }).then(function(data) {
        console.log(data)
        _this.size = data.size
        _this.changeImageViewSize()
      })
    }
  },
  mounted () {
    this.dialogShow = true
  },
  methods: {
    closewithCancel() {
      this.dialogShow = false
      this.onCancel()
    },
    close () {
      this.dialogShow = false
    },
    open (cbs = {}) {
      this.dialogShow = true
      this.cbs = cbs
      this.changeImageViewSize()
    },
    changeImageViewSize() {
      // this.$nextTick(() => {
      //   const imageView = document.querySelector('.set_img_view')
      //   if (imageView) {
      //     const scaleX = 400 / imageView.clientWidth
      //     const scale = Math.min(scaleX, 1)
      //     imageView.style.transform = `scale(${scale})`
      //   }
      // })
    },
    onSubmit () {
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit']({
          url: this.urlres
        })
        this.url = ''
        this.val = ''
        this.valSet = ''
        this.val1 = ''
        this.val1Set = ''
      }
    },
    setData (data) {
      this.url = this.removeParameters(data.url)
      const parmsData = this.extractParameters(data.url)
      this.val = parmsData.quality
      this.valSet = parmsData.quality
      this.val1 = parmsData.resize
      this.val1Set = parmsData.resize
    },
    extractParameters(url) {
      const qualityRegex = /quality,q_(\d+)/
      const resizeRegex = /resize,p_(\d+)/

      const qualityMatch = url.match(qualityRegex)
      const resizeMatch = url.match(resizeRegex)

      const quality = qualityMatch ? parseInt(qualityMatch[1], 10) : 100
      const resize = resizeMatch ? parseInt(resizeMatch[1], 10) : 100

      return {
        quality: quality,
        resize: resize
      }
    },
    removeParameters(url) {
      const index = url.indexOf('?')
      if (index !== -1) {
        return url.substring(0, index)
      }
      return url
    },
    onCancel () {
      if (Object.prototype.toString.call(this.cbs['onCancel']) === '[object Function]') {
        this.cbs['onCancel']()
      }
    },
    formatBytes (value) {
      if (value == null || value === '') {
        return '0 Bytes'
      }
      // eslint-disable-next-line no-array-constructor
      var unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      var index = 0
      var srcsize = parseFloat(value)
      index = Math.floor(Math.log(srcsize) / Math.log(1024))
      var size = srcsize / Math.pow(1024, index)
      size = size.toFixed(2)// 保留的小数位数
      return size + unitArr[index]
    }
  }
}
</script>
<style>
  .tips_tooltip{
    font-size: 8px !important;
  padding: 10px;
  }
</style>
<style lang="scss" scoped>
.item{
  font-size: 10px;
}
.slider{
  width: 98%;
  height: 20px;
}
.editor-dig{
  overflow-x: hidden;
  //padding: 20px;
}
.set_img{
  width: 100%;
  height: 250px;
}
.set_img_view{
  height: 100%;
  margin: 20px auto;
  transform-origin: top left;
}
.label{
  font-size: 12px;
  line-height: 12px;
  height: 0px;
  display: inline;
}
::v-deep .bingo-normal-dialog2 .dialog-box{
  padding: 0 20px;
}
</style>
