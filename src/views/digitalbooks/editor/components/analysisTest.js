// Helper function to get or set cookie
function getCookie(name) {
  return localStorage.getItem('testPaper' + name)
}

function setCookie(name, value, days) {
  localStorage.setItem('testPaper' + name, value)
  console.log('setCookie', name, value, days)
}

// Main function to generate or retrieve test data
export function generateTestData(testpaperId, questions) {
  // const testpaperId = 'test123'; // You can change this ID as needed
  const cookieData = getCookie(testpaperId)
  console.log('getCookie', testpaperId, cookieData)
  if (cookieData) {
    return JSON.parse(cookieData)
  }

  // Generate correctness for each question for each user
  const questionStats = questions.map(q => {
    return {
      id: q.id,
      score: q.score,
      users: [],
      correctUsers: [],
      incorrectUsers: [],
      correctRate: 0
    }
  })

  const userResults = users.map(user => {
    const result = {
      name: user,
      answers: {},
      totalScore: 0,
      correctCount: 0
    }

    questions.forEach(q => {
      const rand = Math.random()
      let correctRate

      // Determine correct rate based on probability
      if (rand < 0.1) {
        correctRate = 0.3 + Math.random() * 0.3 // 0.3-0.6
      } else if (rand < 0.4) {
        correctRate = 0.6 + Math.random() * 0.2 // 0.6-0.8
      } else {
        correctRate = 0.8 + Math.random() * 0.2 // 0.8-1
      }

      // Determine if user answered correctly
      const isCorrect = Math.random() < correctRate
      result.answers[q.id] = {
        isCorrect,
        score: isCorrect ? q.score : 0
      }

      if (isCorrect) {
        result.totalScore += q.score
        result.correctCount++

        // Update question stats
        const qStat = questionStats.find(qs => qs.id === q.id)
        qStat.correctUsers.push(user)
      } else {
        const qStat = questionStats.find(qs => qs.id === q.id)
        qStat.incorrectUsers.push(user)
      }
      const qStat = questionStats.find(qs => qs.id === q.id)
      qStat.users.push({
        isCorrect: isCorrect,
        user
      })
    })

    return result
  })

  // Calculate question correct rates
  questionStats.forEach(qStat => {
    qStat.correctRate = qStat.correctUsers.length / users.length
  })

  // Calculate overall statistics
  const totalScores = userResults.map(u => u.totalScore)
  questions.reduce((sum, q) => sum + q.score, 0)
  const overallStats = {
    totalScore: questions.reduce((a, b) => a + b.score, 0),
    averageScore: totalScores.reduce((a, b) => a + b, 0) / users.length,
    highestScore: Math.max(...totalScores),
    lowestScore: Math.min(...totalScores),
    averageCorrectRate: userResults.reduce((sum, u) => sum + (u.correctCount / questions.length), 0) / users.length
  }

  // Prepare final data structure
  const testData = {
    testpaperId,
    users,
    questions,
    userResults,
    questionStats,
    overallStats,
    generatedAt: new Date().toISOString()
  }

  // Store in cookie for 1 month (30 days)
  setCookie(testpaperId, JSON.stringify(testData))

  return testData
}

// eslint-disable-next-line no-unused-vars
const users = ['张伟', '李强', '王星博', '王芳', '刘敏', '陈静怡', '杨洋', '郭婷', '何勇鹏', '马丽', '高飞', '林娜', '郑凯', '谢芳方', '冯宇', '韩杰', '曾苏敏', '吕强', '苏静', '卢永浩']
