<template>
  <NormalDialog
    v-if="dialogShow"
    width="1000px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="mb10">
        <el-input v-model="listTitle" maxlength="20" show-word-limit class="w" placeholder="请输入图集标题" />
      </div>
      <div class="content">
        <div class="left">
          <el-button type="text" icon="el-icon-plus" @click="uploadImg">添加图片</el-button>
          <div class="img_list">
            <div v-if="myArray.length === 0" style="height: calc(100% - 72px);" class="w flex justify-center items-center">
              <el-empty style="width: 8rem;" :image="empty3" description="暂无数据" />
            </div>
            <draggable v-model="myArray" chosen-class="" force-fallback="true" group="people" animation="1000" @start="onStart" @end="onEnd">
              <transition-group>
                <div v-for="(element,index) in myArray" :key="element.src" class="item" @click="tabIndex=index">
                  <img :src="element.src" :class="tabIndex===index?'chosen':''" alt="" />
                  <div>
                    <i class="el-icon-delete deleteImg" style="color:red" @click="deleteImg(index)"></i>
                    <i class="el-icon-s-tools deleteImg" @click="setImg(element,index)"></i>
                  </div>
                </div>
              </transition-group>
            </draggable>
          </div>
        </div>
        <div class="right">
          <el-input v-if="myArray[tabIndex]" v-model="myArray[tabIndex].info" show-word-limit maxlength="200" :rows="10" type="textarea" class="w" placeholder="请输入图片信息" />
        </div>
      </div>

    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import draggable from 'vuedraggable'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { Notification, Message } from 'element-ui'
import empty3 from '@/assets/images/empty3.png'
import { addSetImgSizeModal, closeSetImgSize, setData } from './setImgSize'
export default {
  components: { NormalDialog, draggable },

  data () {
    return {
      cbs: null,
      tabIndex: 0,
      empty3: empty3,
      dialogShow: false,
      appendToBody: false,
      title: '添加图片集',
      listTitle: '',
      content: '',
      drag: false,
      myArray: [

      ]
    }
  },
  mounted () {
    this.dialogShow = true
  },
  methods: {
    setImg(element, index) {
      const _this = this
      setData({
        url: element.src
      })
      addSetImgSizeModal({
        onSubmit (data) {
          _this.myArray[index].src = data.url
          closeSetImgSize()
        }
      })
    },
    deleteImg(index) {
      this.myArray = this.myArray.filter((item, index1) => { return index !== index1 })
    },
    uploadImg () {
      var input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      // 执行上传文件操作
      const _this = this
      input.addEventListener('change', async function (e) {
        var file = e.target.files[0]
        const isLt2M = file.size / 1024 / 1024 < 1
        if (!isLt2M) {
          Message.warning('上传图片超过1M,可能会影响阅读体验，请压缩图片')
        }
        const { data } = await getFileUploadAuthor({
          mediaType: 'IMAGE',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })
        const ossCDN = data[0].ossConfig.ossCDN
        try {
          const formData = new FormData()
          formData.append('success_action_status', '200')
          formData.append('callback', '')
          formData.append('key', data[0].fileName)
          formData.append('policy', data[0].policy)
          formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
          formData.append('signature', data[0].signature)
          formData.append('file', file)
          const notif = Notification({
            title: `${file.name}上传中`,
            message: '0%',
            duration: 0
          })
          await axios.post(data[0].ossConfig.host, formData, {
            onUploadProgress: (progress) => {
              const complete = Math.floor(progress.loaded / progress.total * 100)
              notif.message = complete + '%'
              console.log(complete)
              if (complete >= 100) {
                notif.close()
              }
            }
          })
          if (!isLt2M) {
            setData({
              url: `${ossCDN}/${data[0].fileName}`
            })
            addSetImgSizeModal({
              onSubmit (data) {
                _this.myArray.push({
                  src: data.url
                })
                closeSetImgSize()
              },
              onCancel() {
                _this.myArray.push({
                  src: `${ossCDN}/${data[0].fileName}`
                })
              }
            })
          } else {
            _this.myArray.push({
              src: `${ossCDN}/${data[0].fileName}`
            })
          }
        } catch (error) {
          console.log(error)
        }
      }, false)
      input.click()
    },
    close () {
      this.dialogShow = false
      this.listTitle = ''
      this.myArray = []
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    open (cbs = {}) {
      console.log(this)
      this.dialogShow = true
      this.cbs = cbs
    },
    onSubmit () {
      if (this.myArray.length === 0) {
        this.$message.warning('请至少上传一张图片')
        return
      }
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit']({
          title: this.listTitle,
          content: this.myArray
        })
        this.listTitle = ''
        this.myArray = []
      }
    },
    setData (data) {
      this.listTitle = data.title
      this.myArray = data.content
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
    },
    onCancel () {
      if (Object.prototype.toString.call(this.cbs['onCancel']) === '[object Function]') {
        this.cbs['onCancel']()
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      }
    },
    // 开始拖拽事件
    onStart () {
      this.drag = true
    },
    // 拖拽结束事件
    onEnd () {
      this.drag = false
    }

  }
}
</script>

<style lang="scss" scoped>
.img_list{
  height: 85%;
  overflow: auto;
  @include scrollBar;
  ::v-deep.el-empty__image{
      width: 5rem !important;
    }
    ::v-deep.el-empty__description{
      p{
      font-size: 1rem;

      }
    }
  img{
    max-width: 100%;
    max-height: 100%;
  }
}
 .item {
            background-color: #fdfdfd;
            margin-bottom: 10px;
            cursor: move;
            display: flex;
            img{
              width: 90%;
              display: block;
            }
            .deleteImg{
              margin-left: 3px;
              font-size: 12px;
              cursor: pointer;
            }
        }
        /*选中样式*/
        .chosen {
            border: solid 1px #3089dc !important;
        }
.editor-dig {
  ::v-deep .el-input__inner {
    transition: none;
  }
  .school-disc {
    margin-top: 10PX;
  }
.content{
  display: flex;
  justify-content: space-between;
}
  .left {
    height: 500px;
    width: 300px;
    margin-top: 20px;
  }
.right{
  width: 600px;
  height: 500px;
  padding-top: 70px;
}
  .school-disc2 {
    width: 5PX;
    height: 5PX;
    background: #828282;
    border-radius: 50%;
    margin-right: 5PX;
  }
}
</style>
