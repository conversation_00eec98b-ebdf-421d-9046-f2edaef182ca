<template>
  <NormalDialog
    v-if="dialogShow"
    width="400px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="appendToBody"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="mb10">
        <el-input v-model="chapterName" class="w" placeholder="请输入内容" />
      </div>
    </div>
    <template #footer>
      <div class="edu-view">
        <div v-if="!isStatic" class="edu-btn" @click="bind">确定</div>
        <div v-else class="edu-btn" @click="confirm">确定</div>
        <div v-if="showAIBtn" class="edu-btn edu-btn-ai" @click="handleAI">AI生成</div>
      </div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { debounce } from '@/utils/index'
import { editBookCatalogue } from '@/api/digital-api.js'
import { getToken } from '@/utils/auth'

export default {
  components: { NormalDialog },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: false
    },
    nodeInfo: {
      type: Object,
      default: () => {
        return null
      }
    },
    titleObj: {
      type: Object,
      default: () => {
        return {
          addTitle: '新建章节/任务',
          editTitle: '编辑章节/任务'
        }
      }
    },
    type: {
      type: String,
      default: 'CHAPTER'
    },
    showAIBtn: {
      type: Boolean,
      default: false
    },
    isStatic: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      chapterName: '',
      dialogShow: false,
      token: ''
    }
  },
  mounted () {
    this.dialogShow = this.show
    this.token = getToken()
    this.token = `Bearer ${this.$route.query && this.$route.query.token}`
    if (this.nodeInfo.data) {
      this.title = this.titleObj.editTitle
      this.chapterName = this.nodeInfo.data.title
    } else {
      this.title = this.titleObj.addTitle
    }
  },
  methods: {
    close () {
      this.dialogShow = false
      this.$emit('close')
    },
    bind: debounce(async function () {
      if (this.chapterName) {
        try {
          const obj = {
            bookId: this.nodeInfo.bookId,
            parentId: this.nodeInfo.parentId,
            title: this.chapterName,
            type: this.type
          }
          if (this.nodeInfo.data) {
            obj.id = this.nodeInfo.data.id
          }
          const { data } = await editBookCatalogue(obj, { authorization: this.token })
          this.dialogShow = false
          this.$emit('emitSucess', data)
        } catch (error) {
          console.log(error)
        }
      }
    }, 3000, true),
    handleAI () {
      this.$emit('handleAI')
    },
    confirm() {
      this.$emit('emitSuccess', this.chapterName, this.nodeInfo)
    }
  }
}
</script>

<style lang="scss" scoped>

.editor-dig {
  ::v-deep .el-input__inner {
    transition: none;
  }
  .school-disc {
    margin-top: 10PX;
  }

  .mb10 {
    margin-bottom: 10PX;
  }

  .school-disc2 {
    width: 5PX;
    height: 5PX;
    background: #828282;
    border-radius: 50%;
    margin-right: 5PX;
  }
}
.edu-view {
  width: 100%;
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  .edu-btn-ai {
    background: linear-gradient(90deg, #2AF598 0%, #009EFD 100%);
    border: 0 !important;
  }
}
</style>
