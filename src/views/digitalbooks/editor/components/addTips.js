
import addTips from './addTips.vue'
import Vue from 'vue'
let $vm, dialogConstructor

export function addTipsModal (cbs) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTips)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().open(cbs)
}
export function closeTips () {
  $vm.$mount().close()
}
export function setData (params) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTips)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().setData(params)
}

