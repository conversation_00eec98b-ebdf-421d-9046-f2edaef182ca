
import addTraining from './addTraining.vue'
import Vue from 'vue'
let $vm, dialogConstructor

export function addTrainingsModal (cbs) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTraining)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().open(cbs)
}
export function closeTrainings () {
  $vm.$mount().close()
}
export function setData (params) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTraining)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().setData(params)
}

