<template>
  <NormalDialog
    v-if="dialogShow"
    width="700px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <!-- <div class="mb10">
        <p>类型：</p><el-select v-model="form.type" placeholder="请选择实训类型">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div> -->
      <div class="mb10">
        <p>标题：</p><el-input v-model="form.title" class="w" placeholder="标题" disabled />
      </div>
      <div class="mb10">
        <p>副标题：</p><el-input v-model="form.subTitle" class="w" placeholder="请输入副标题" maxlength="20" show-word-limit />
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },

  data () {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '添加实训',
      form: {
        title: '操作实训',
        subTitle: '',
        type: 'sql'
      },
      options: [{
        value: 'sql',
        label: 'SQL实训'
      }]
    }
  },
  mounted () {
    this.dialogShow = true
  },
  methods: {
    close () {
      this.dialogShow = false
      this.form = {
        title: '操作实训',
        subTitle: '',
        type: 'sql'
      }
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    open (cbs = {}) {
      this.dialogShow = true
      this.cbs = cbs
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
    },
    onSubmit () {
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit'](this.form)
        this.form = {
          title: '操作实训',
          subTitle: '',
          type: 'sql'
        }
      }
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    setData (data) {
      this.form = data
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
    },
    onCancel () {
      if (Object.prototype.toString.call(this.cbs['onCancel']) === '[object Function]') {
        this.cbs['onCancel']()
      }
    }

  }
}
</script>

  <style lang="scss" scoped>

  .editor-dig {
    position: relative;
    z-index: 99999;
    ::v-deep .el-select{
      width: 83%;
      .el-input__suffix{
        top:13px
      }
      .is-focus{
        .el-input__suffix{
        top:-13px
      }
      }
      .el-input{
        width: 100%;
      }
    }
    ::v-deep .el-input{
      width: 83%;
    }
    ::v-deep .el-input__inner {
      transition: none;
    }
    .school-disc {
      margin-top: 10PX;
    }

    .mb10 {
      margin-bottom: 10PX;
      display: flex;
      justify-content: space-between;
      p{
        white-space: nowrap;
        line-height: 0px;
      }
    }

    .school-disc2 {
      width: 5PX;
      height: 5PX;
      background: #828282;
      border-radius: 50%;
      margin-right: 5PX;
    }
  }
  </style>
