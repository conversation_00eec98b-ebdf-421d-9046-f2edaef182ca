// 引入 Vue 和 addexcelTraining
import Vue from 'vue'
import addexcelTraining from './addexcelTraining.vue'

let $vm, dialogConstructor

function initInstance() {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addexcelTraining)
  }
  if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
    $vm.$mount() // 挂载一次
    document.body.appendChild($vm.$el) // 将组件添加到 DOM
  }
}

export function addexcelsModal(cbs) {
  initInstance()
  $vm.open(cbs) // 调用 open 方法而无需重复挂载
}

export function closeexcels() {
  if ($vm) {
    $vm.close()
    // 销毁实例以释放内存
    $vm.$destroy()
    document.body.removeChild($vm.$el)
    $vm = null // 重置 $vm
  }
}

export function setData(params) {
  initInstance() // 确保 $vm 已初始化
  $vm.setData(params) // 设置数据
}
