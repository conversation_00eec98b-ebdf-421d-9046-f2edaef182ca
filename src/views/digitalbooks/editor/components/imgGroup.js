
import addTips from './imgGroup.vue'
import Vue from 'vue'
let $vm, dialogConstructor

export function addImgGroup (cbs) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTips)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().open(cbs)
}
export function closeImgGroup () {
  $vm.$mount().close()
}
export function setData (params) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTips)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().setData(params)
}

