<template>
  <div v-if="showTraing" class="traing_main">
    <div class="head-box">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        {{ traingData && traingData.trainingName }}
      </div>
      <div class="progress">实验结果：<span>总验证数：<span class="value">{{ userTrainingData.total||allTotal }}</span></span><span>正确：<span class="value">{{ userTrainingData.right||0 }}</span></span><span>错误：<span class="value">{{ userTrainingData.wrong||0 }}</span></span><span>未做：<span class="value">{{ userTrainingData.unCompleteLabel||0 }}</span></span><span>总分：<span class="value">{{ userTrainingData.scoreTotal||'未配置' }}</span></span><span>得分：<span class="value">{{ !userTrainingData.scoreTotal?'-':userTrainingData.userScore||0 }}</span></span></div>
      <div class="share" @click="openPop">实验说明</div>
    </div>
    <div class="main">
      <img
        v-if="isCop"
        class="close"
        src="../../../../assets/digitalbooks//read/close.png"
        alt=""
        @click="isCop = false"
      />
      <div class="left" :class="isCop ? '' : 'cop'">
        <div v-if="isCop && traingData && traingData.trainingStepList">
          <div
            v-for="(content, index) in traingData.trainingStepList"
            class="step_item"
            :key="index"
            :ref="content"
            :class="index === tabIndex ? 'active' : ''"
            @click="handleFocus($event,index)"
          >
            <div class="title">
              <div class="idot">{{ index + 1 }}</div>
              <div
                class="type"
                :class="content.userTrainingStep && content.userTrainingStep.trainingStatus === 'FINISHED' ? '' : 'no_fininsh'"
              >
                {{ content.userTrainingStep && content.userTrainingStep.trainingStatus === 'FINISHED' ? '已完成' : '未完成' }}
              </div>
            </div>
            <div class="content" ref='htmlContent' :style="{ height: content.isCop ? 'auto' : '' }" v-html="content.instructions"></div>
            <div v-if="isOverflow[index]" class="up"><span class="no_show" @click="content.isCop = !content.isCop">{{ content.isCop ? '收起' : '展开' }}</span><i
              :class="!content.isCop ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
            ></i></div>
            <img
              v-if="traingData && index !==traingData.trainingStepList.length-1"
              class="down no_show"
              src="../../../../assets/digitalbooks//read/down.png"
              alt=""
            />
          </div>
        </div>
        <div v-else class="open">
          <img src="../../../../assets/digitalbooks//read/open.png" alt="" @click="isCop = true" />
        </div>
      </div>
      <div class="right">
        <div class="right_top">
          <!-- <div class="title">实验步骤{{ tabIndex + 1 }}</div> -->
          <el-button
            v-if="traingData &&traingData.trainingStepList&& traingData.trainingStepList[tabIndex].attachFile && demo && !noEdit"
            class="start"
            type="primary"
            @click="startTraing"
          >开始实操</el-button>
          <el-button
            v-if="traingData &&traingData.trainingStepList&& traingData.trainingStepList[tabIndex].attachFile && demo && !noEdit&&traingData.trainingStepList[tabIndex].userTrainingStep&&traingData.trainingStepList[tabIndex].userTrainingStep.trainingStatus === 'FINISHED'"
            class="restart"
            type="primary"
            @click="redoTrainingStep"
          >清空重做</el-button>
          <div
            v-show="traingData && traingData.trainingStepList[tabIndex].attachFile"
            id="whiteboard"
            ref="excel"
            v-loading="loading"
            element-loading-text="数据加载中..."
            class="excel"
          >
          </div>
          <div v-show="!traingData || !traingData.trainingStepList[tabIndex].attachFile" class="emty">
            <Empty description="暂无数据" />
          </div>
        </div>
        <div class="right_bottom">
          <div class="check">
            <el-button class="button1" type="primary" @click="submitTraing">确认完成</el-button>
            <el-button
              v-if="traingData && traingData.trainingStepList[tabIndex].rightResult && traingData.trainingStepList[tabIndex].userTrainingStep&&traingData.trainingStepList[tabIndex].userTrainingStep.trainingStatus === 'FINISHED'"
              type="text"
              class="button2"
              @click="showRightResult"
            >
              查看答案</el-button>
            <div v-for="(item, index) in userResult" :key="index" class="formItem">
              <div class="label">{{ item.label }}：</div>
              <el-input
                v-model="item.anser"
                type="textarea"
                autosize
              />
              <span v-if="rightResult[index].score " class="score">{{ rightResult[index].score }}分</span>
              <span v-else class="score">无评分</span>
              <img
                v-if="traingData && traingData.trainingStepList[tabIndex].userTrainingStep && traingData.trainingStepList[tabIndex].userTrainingStep.trainingStatus === 'FINISHED' && traingData.trainingStepList[tabIndex].rightResult"
                class="result"
                :src="isRight(item.anser,index) ? require('../../../../assets/digitalbooks/read/wrong.png') : require('../../../../assets/digitalbooks/read/right.png')"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <infoPop ref="infoPop" />
    <div ref="annimate" class="annimate">
      <lottie :options="defaultOptions" @animCreated="handleAnimation" />
    </div>
    <imgGroupPop ref="imgs" :info="imgListInfo" />
  </div>
</template>
<script>
import { getTraining, applyTrainingOffice, updateTrainingStepProgress, redoTrainingStep } from '@/api/training-api'
import { generateWebofficeToken, refreshWebofficeToken } from '@/api/aliyun-api'
import Empty from '@/components/classPro/Empty/index.vue'
import infoPop from './traningToast.vue'
import { getToken } from '@/utils/auth'
import lottie from 'vue-lottie'
import animationData from '../../../../assets/annimate/excel.json'
import imgGroupPop from './imgGroupPop.vue'
import { MessageBox } from 'element-ui'
export default {
  components: {
    Empty,
    infoPop,
    lottie,
    imgGroupPop
  },
  props: {
    studentCourseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      defaultOptions: { animationData: animationData, loop: false, autoplay: false },
      showTraing: false,
      traingData: null,
      tabIndex: 0,
      token: '',
      isCop: true,
      demo: null,
      loading: false,
      userResult: [],
      rightResult: [],
      noEdit: false,
      anim: null,
      imgListInfo: null,
      allTotal: 0,
      right: 0,
      wrong: 0,
      userTrainingData: null,
      isOverflow: []
    }
  },
  computed: {
    currentStudentCourseId() {
      if (this.studentCourseId) {
        return this.studentCourseId
      }
      if (this.$route.query.studentCourseId) {
        return parseInt(this.$route.query.studentCourseId) || 0
      }
      return 0
    }
  },
  async mounted() {
    this.token = this.$route.query && this.$route.query.token ? `Bearer ${this.$route.query.token}` : getToken()
    const trainingId = this.$route.query.trainingId
    const studentCourseId = this.$route.query.studentCourseId
    if (trainingId && studentCourseId) {
      if (!this.studentCourseId) {
        this.studentCourseId = Number(studentCourseId)
      }
      await this.open(Number(trainingId))
    }
  },
  methods: {
    checkOverflow() {
      this.isOverflow = this.$refs.htmlContent.map(el => {
        return el.scrollHeight > el.clientHeight
      })
    },
    setProgress() {
      this.allTotal = 0
      this.right = 0
      this.wrong = 0
      this.traingData.trainingStepList.forEach(item => {
        const rightArr = JSON.parse(item.rightResult)
        this.allTotal += JSON.parse(item.rightResult).length
        if (item.userTrainingStep && item.userTrainingStep.userResult) {
          this.right += JSON.parse(item.userTrainingStep.userResult).filter((item, index) => {
            return item.anser === rightArr[index].anser
          }).length
          this.wrong += JSON.parse(item.userTrainingStep.userResult).filter((item, index) => {
            return item.anser !== rightArr[index].anser
          }).length
        }
      })
    },
    isRight(item, index) {
      return this.rightResult[index].anser !== item
    },
    async redoTrainingStep() {
      try {
        MessageBox.confirm('确认清空重做?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await redoTrainingStep({
            trainingStepId: this.traingData.trainingStepList[this.tabIndex].trainingStepId,
            studentCourseId: this.currentStudentCourseId
          }, { authorization: this.token })
          await this._getTraining(this.traingData.trainingId)
          this.handleFocus(null, this.tabIndex, true)
          this.startTraing()
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    handleAnimation(anim) {
      this.anim = anim
    },
    doAnnimate() {
      this.anim.play()
      setTimeout(() => {
        this.anim.stop()
      }, 3000)
    },
    showRightResult() {
      let str = ''
      this.rightResult.forEach(item => {
        str += `<p>${item.label}：${item.anser}</p>`
      })
      this.$refs.infoPop.open(str, '实验答案')
    },
    openPop() {
      this.$refs.infoPop.open(this.traingData.description, '实验说明')
    },
    async submitTraing() {
      const { data } = await updateTrainingStepProgress({
        trainingStepId: this.traingData.trainingStepList[this.tabIndex].trainingStepId,
        userResult: JSON.stringify(this.userResult),
        studentCourseId: this.currentStudentCourseId
      }, { authorization: this.token })
      if (data.userResultType === 'CORRECT') {
        // this.doAnnimate()
      }
      this._getTraining(this.traingData.trainingId)
    },
    async startTraing() {
      this.noEdit = true
      const { data } = await applyTrainingOffice({
        trainingStepId: this.traingData.trainingStepList[this.tabIndex].trainingStepId,
        studentCourseId: this.currentStudentCourseId
      }, { authorization: this.token })
      this.weboffice({
        AccessToken: data.body.accessToken,
        WebofficeURL: data.body.webofficeURL
      })
    },
    async _getTraining(id) {
      const { data } = await getTraining({
        trainingId: id,
        studentCourseId: this.currentStudentCourseId
      }, { authorization: this.token })
      if (!data) {
        this.$message.warning('该实训已被删除')
        this.back()
        return
      }
      const Trandata = data.trainingStepList.map(item => {
        return {
          ...item,
          isCop: false
        }
      })
      this.traingData = {
        ...data,
        trainingStepList: Trandata
      }
      this.userTrainingData = data.userTrainingData
      this.setProgress()
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    },
    async handleFocus(event, index, flag = false) {
      if (index === this.tabIndex && !flag) {
        if (event.target.tagName === 'IMG' && !event.target.classList.contains('no_show')) {
          this.imgListInfo = {
            content: [{
              src: event.target.currentSrc,
              info: ''
            }]
          }
          this.$refs.imgs.open()
        }
        return
      }
      this.tabIndex = index
      this.demo && this.demo.destroy()
      this.demo = null
      this.noEdit = false
      if (!this.traingData.trainingStepList[index].userTrainingStep || !this.traingData.trainingStepList[index].userTrainingStep.userResult || !this.isJSON(this.traingData.trainingStepList[index].userTrainingStep.userResult)) {
        this.userResult = this.traingData.trainingStepList[index].rightResult ? JSON.parse(this.traingData.trainingStepList[index].rightResult).map(item => {
          return {
            label: item.label,
            anser: ''
          }
        }) : []
      } else {
        const result = JSON.parse(this.traingData.trainingStepList[index].rightResult)
        const userResult = JSON.parse(this.traingData.trainingStepList[index].userTrainingStep.userResult)
        result.forEach(item => {
          item.anser = userResult.filter(item1 => { return item1.label === item.label }).length === 0 ? '' : userResult.filter(item1 => { return item1.label === item.label })[0].anser
        })
        this.userResult = result
      }
      this.rightResult = this.traingData.trainingStepList[index].rightResult ? JSON.parse(this.traingData.trainingStepList[index].rightResult) : []
      if (this.traingData.trainingStepList[index].attachFile) {
        if (this.traingData.trainingStepList[index].userTrainingStep && this.traingData.trainingStepList[index].userTrainingStep.userAttachFileId) {
          this._getGenerateToken(this.traingData.trainingStepList[index].userTrainingStep.userAttachFile.url, this.token)
        } else {
          this._getGenerateToken(this.traingData.trainingStepList[index].attachFile.url, this.token)
        }
      }
    },
    isJSON(str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    async open(id) {
      await this._getTraining(id)
      this.showTraing = true
      this.tabIndex = 1
      this.handleFocus(null, 0)
      this.$nextTick(() => {
        this.checkOverflow()
      })
    },
    back() {
      this.showTraing = false
      this.$emit('close')
    },
    async _getGenerateToken(url, token) {
      const header = { authorization: token }
      const { data } = await generateWebofficeToken({
        fileUrl: url
      }, header)
      this.tokenList = data.body
      this.weboffice({
        AccessToken: data.body.accessToken,
        WebofficeURL: data.body.webofficeURL
      })
    },
    async weboffice(tokenInfo) {
      var mount = document.getElementById('whiteboard')
      this.demo = window.aliyun.config({
        mount: mount,
        url: tokenInfo.WebofficeURL,
        refreshToken: this.refreshTokenPromise
      })
      this.loading = true
      this.demo.setToken({
        token: tokenInfo.AccessToken,
        timeout: 25 * 60 * 1000 // Token过期时间，单位为ms。25分钟之后刷新Token。
      })
      await this.demo.ready()
      this.loading = false
    },
    async refreshTokenPromise() {
      const header = { authorization: this.token }
      const { data } = await refreshWebofficeToken({
        accessToken: this.tokenList.accessToken,
        refreshToken: this.tokenList.refreshToken
      }, header)
      return {
        token: data.body.accessToken,
        timeout: 10 * 60 * 1000
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-loading-mask{
  background-color: rgba(255, 255, 255, 0.4)
}
.annimate {
  position: fixed;
  width: 300px;
  height: 300px;
  top: 25%;
  left: 35%;
  pointer-events: none;
}

.emty {
  width: 100%;
  height: 90%;
}

#whiteboard {
  width: 100%;
  height: 100%;

  ::v-deep iframe {
    width: 100% !important;
    height: 100% !important;
  }
}

.traing_main {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  overflow: auto;
  background: #F1F7FF;
  padding: 10px;

  .head-box {
    height: 10px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    margin-top: 10px;
    position: relative;

    .share {
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      text-decoration: underline
    }
    .progress{
      position: absolute;
      right: 330px;
      font-size: 12px;
      font-weight: bold;
      span{
        margin-right: 10px;
        font-weight: 400;
        .value{
          color: #4FACFE;
        }
      }
    }
    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: 12px;
      font-weight: 500;
      margin-left: 10px;
    }
  }

  .main {
    width: 100%;
    height: 93%;
    display: flex;
    position: relative;
    justify-content: space-between;

    .close {
      width: 12px;
      position: absolute;
      left: 295px;
      top: 50%;
      cursor: pointer;
    }

    .left {
      width: 400px;
      height: 100%;
      background: #FFFFFFBA;
      border-radius: 10px;
      margin-right: 30px;
      padding: 10px;
      overflow: auto;
      position: relative;
      @include scrollBar;

      .up {
        margin-top: 4px;
        font-size: 8px;
        font-weight: bold;
        text-align: right;
        padding-right: 5px;
        padding-bottom: 5px;

        span {
          cursor: pointer;
        }
      }

      .step_item {
        width: 100%;
        background: #F9F9F9;
        border-radius: 5px;
        min-height: 200px;
        margin-bottom: 24px;
        position: relative;
        ::v-deep img{
          cursor: pointer;
        }
        .title {
          width: 100%;
          height: 30px;
          display: flex;
          justify-content: space-between;
          padding: 5px;

          .idot {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
            background: linear-gradient(90deg, #4FACFE 0%, #00F2FE 100%);

          }

          .type {
            width: 40px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            border-radius: 5px;
            font-size: 10px;
            font-weight: bold;
            color: #ffffff;
            background: #2D9CDB;
          }

          .no_fininsh {
            background: #EB5757;
          }
        }

        .content {
          padding: 5px;
          min-height: 150px;
          height: 150px;
          overflow: auto;
          overflow-x: hidden;
          transition: height 1s linear;
          @include scrollBar;

          ::v-deep * {
            max-width: 100%;
          }

          ::v-deep pre>code {
            display: block;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }

        .down {
          width: 15px;
          position: absolute;
          left: 50%;
          bottom: -20px;
        }
      }

      .active {
        background: #D2EFFF;
      }
    }

    .cop {
      width: 20px;
      margin-right: 5px;
    }

    .open {
      width: 100%;
      height: 100%;
      position: relative;

      img {
        width: 15px;
        position: absolute;
        left: -7px;
        top: 50%;
        cursor: pointer;
      }
    }

    .right {
      width: 100%;
      height: 100%;
      position: relative;

      .right_top {
        width: 100%;
        padding: 10px;
        height: 91%;
        background: #FFFFFFBA;
        border-radius: 10px;
      }
      .right_bottom{
        width: 100%;
        padding: 10px;
        min-height: 3.5%;
        padding-bottom: 6%;
        margin-top: 1%;
        background: #FFFFFFBA;
        border-radius: 10px;
        position: relative;
      }
      .start {
        position: absolute;
        padding: 10px;
        width: 80px;
        font-size: 10px;
        right: 30px;
        bottom: 24vh;
      }
      .restart{
        position: absolute;
        padding: 10px;
        width: 80px;
        font-size: 10px;
        right: 30px;
        bottom: 18vh;
      }
      .title {
        font-size: 12px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .excel {
        width: 100%;
        height: 90%;
      }

        .result {
          width: 20px;
          height: 20px;

          img {
            width: auto;
            height: 18px;
            margin-left: 10px;
          }

          margin-top: 3px;
        }

        .el-input {
          width: 70%;
        }

        ::v-deep .el-input__inner {
          width: 100%;
          height: 30px;
          font-size: 12px;
        }

        .button1 {
          padding: 10px;
          width: 80px;
          font-size: 10px;
          position: absolute;
          right: 20px;
          bottom:10px;
        }
        .button2{
          position: absolute;
          right: 120px;
          bottom:0px;
          font-size: 10px;
          text-decoration: underline;
        }
      }

      .check {
        width: 100%;
        height: 75%;
        flex-wrap: wrap;
        overflow: auto;
        @include scrollBar;

        .formItem {
          display: flex;
          gap: 10px;
          padding-right: 10px;
          justify-content: space-between;
          line-height: 20px;
          height: auto;
          margin-top: 5px;
          span{
            white-space: nowrap;
            width: 50px;
          }
          .label{
            width: 200px;
            font-size: 10px;
            text-align: right;
            white-space: pre-wrap;
          }
          img {
            // width: ;
            height: 12px;
            margin-top: 4px;
          }
            ::v-deep .el-textarea__inner {
              font-size: 10px;
          }
        }

    }
  }
}
</style>
