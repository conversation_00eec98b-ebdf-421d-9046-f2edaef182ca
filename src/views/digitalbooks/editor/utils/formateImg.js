import tinymce from 'tinymce/tinymce'
import { addSetImgSizeModal, closeSetImgSize, setData } from '../components/setImgSize'
export function formateImg (editor, url) {
  editor.ui.registry.addButton('changeimg50', {
    // icon: 'edit-block',
    text: '50%',
    tooltip: '图片宽度50%',
    onAction: () => {
      const selectContent = tinymce.activeEditor.selection.getNode()
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      const url = selectContent.src
      var media = `<img style="width:50%" src="${url}" >`
      if (selectContent.getAttribute('data-latex')) {
        console.log(selectContent.getAttribute('data-latex'))
        media = `<img style="width:50%" src="${url}" data-latex="${selectContent.getAttribute('data-latex')}"  >`
      }
      tinymce.activeEditor.selection.moveToBookmark(bookmark)
      tinymce.activeEditor.selection.setContent(media)
      tinymce.activeEditor.selection.moveToBookmark(bookmark)
    }
  })
  editor.ui.registry.addButton('changeimg100', {
    // icon: 'edit-block',
    text: '100%',
    tooltip: '图片宽度100%',
    onAction: () => {
      const selectContent = tinymce.activeEditor.selection.getNode()
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      const url = selectContent.src
      var media = `<img style="width:100%" src="${url}" >`
      if (selectContent.getAttribute('data-latex')) {
        media = `<img style="width:100%" src="${url}" data-latex="${selectContent.getAttribute('data-latex')}"  >`
      }
      tinymce.activeEditor.selection.moveToBookmark(bookmark)
      tinymce.activeEditor.selection.setContent(media)
      tinymce.activeEditor.selection.moveToBookmark(bookmark)
    }
  })
  editor.ui.registry.addButton('setSize', {
    // icon: 'edit-block',
    text: '设置',
    tooltip: '设置图片质量',
    onAction: () => {
      const selectContent = tinymce.activeEditor.selection.getNode()
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      const url = selectContent.src
      setData({
        url
      })
      addSetImgSizeModal({
        onSubmit (data) {
          const media = `<img style="width:100%" src="${data.url}" >`
          tinymce.activeEditor.selection.moveToBookmark(bookmark)
          tinymce.activeEditor.selection.setContent(media)
          tinymce.activeEditor.selection.moveToBookmark(bookmark)
          closeSetImgSize()
        }
      })
    }
  })
  return editor.ui.registry.addContextToolbar('imgwidthcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      // return node.classList.contains('img_list')
      return node.nodeName.toLowerCase() === 'img'
    },
    items: 'changeimg50 changeimg100 setSize', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
    // scope: 'editor'
  })
}
