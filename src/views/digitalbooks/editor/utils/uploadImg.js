import tinymce from 'tinymce/tinymce'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { Notification, Message } from 'element-ui'
import { addSetImgSizeModal, closeSetImgSize, setData } from '../components/setImgSize'
export function uploadImg (editor, url) {
  return editor.ui.registry.addButton('uploadImg', {
    icon: 'uploadImage',
    tooltip: '插入图片',
    onAction: function () {
      var input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      // 执行上传文件操作
      input.addEventListener('change', async function (e) {
        var file = e.target.files[0]
        const isLt2M = file.size / 1024 / 1024 < 1
        console.log(file.size, isLt2M)
        if (!isLt2M) {
          Message.warning('上传图片超过1M,可能会影响阅读体验，请压缩图片')
        }
        const { data } = await getFileUploadAuthor({
          mediaType: 'IMAGE',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })
        const ossCDN = data[0].ossConfig.ossCDN
        try {
          const formData = new FormData()
          formData.append('success_action_status', '200')
          formData.append('callback', '')
          formData.append('key', data[0].fileName)
          formData.append('policy', data[0].policy)
          formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
          formData.append('signature', data[0].signature)
          formData.append('file', file)
          const notif = Notification({
            title: `${file.name}上传中`,
            message: '0%',
            duration: 0
          })
          await axios.post(data[0].ossConfig.host, formData, {
            onUploadProgress: (progress) => {
              const complete = Math.floor(progress.loaded / progress.total * 100)
              notif.message = complete + '%'
              console.log(complete)
              if (complete >= 100) {
                notif.close()
              }
            }
          })
          if (!isLt2M) {
            setData({
              url: `${ossCDN}/${data[0].fileName}`
            })
            addSetImgSizeModal({
              onSubmit (data) {
                var media = `<img style="width:100%" src="${data.url}" >
                </img>`
                tinymce.activeEditor.selection.setContent(media)
                closeSetImgSize()
              },
              onCancel() {
                var media = `<img style="width:100%" src="${ossCDN}/${data[0].fileName}" >
                </img>`
                tinymce.activeEditor.selection.setContent(media)
              }
            })
          } else {
            var media = `<img style="width:100%" src="${ossCDN}/${data[0].fileName}" >
            </img>`
            tinymce.activeEditor.selection.setContent(media)
          }

          // success(`${ossCDN}/${data[0].fileName}`, data[0].fileName, '')
        } catch (error) {
          console.log(error)
          // failure('文件上传失败，请重试')
        }
      }, false)
      // 触发点击事件，打开选择文件的对话框
      input.click()
    }
  })
}
