// 引入主题和图标信息
import 'tinymce/themes/silver/theme.min.js'
import '../../../../../public/tinymce/icons/default/icons'
import tinymce from 'tinymce/tinymce'
// 引入插件
// https://www.tiny.cloud/docs/plugins/
import 'tinymce/plugins/lists'// 列表插件
import 'tinymce/plugins/code' // 源代码插件
import 'tinymce/plugins/pagebreak' // 分页符插件
import 'tinymce/plugins/charmap' // 特殊符号插件
import 'tinymce/plugins/emoticons' // 表情插件
import 'tinymce/plugins/save' // 保存插件
import 'tinymce/plugins/preview' // 预览插件
// import 'tinymce/plugins/print' // 打印
import 'tinymce/plugins/image'// 上传图片插件
// import 'tinymce/plugins/media'// 视频插件
import 'tinymce/plugins/link' // 链接插件
import 'tinymce/plugins/anchor' // 锚点插件
import 'tinymce/plugins/codesample' // 代码插件
import 'tinymce/plugins/table'// 表格插件
import 'tinymce/plugins/searchreplace' // 查找、替换插件
// import 'tinymce/plugins/hr' // 水平分割线插件
import 'tinymce/plugins/insertdatetime' // 时间日期插件
import 'tinymce/plugins/wordcount'// 字数统计插件
import 'tinymce/plugins/fullscreen' // 全屏插件
import 'tinymce/plugins/help' // 帮助插件
import 'tinymce/plugins/noneditable'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { Notification, Message } from 'element-ui'
import './ui'
// https://www.tiny.cloud/docs/configure/integration-and-setup/
export default {
  selector: '#tinymce',
  external_plugins: {
    'powerpaste': '/tinymce/powerpaste/plugin.min.js',
    'kityformula-editor': '/tinymce/kityformula-editor/plugin.js',
    'mathjax': '/tinymce/tinymce-mathjax/plugin.js'
  },
  mathjax: { lib: '/tex-mml-svg.js' },
  /**
   * 语言路径
   */
  language_url: '/tinymce/langs/zh_CN.js',
  // fontsize_formats: '8px 10px 12px 14px 16px 18px 24px 36px 48px 72px',
  base_url: '/',
  /**
   * 语言
   */
  language: 'zh_CN',

  /**
   * 主题样式路径
   */
  skin_url: '/tinymce/skins/ui/oxide1',

  /**
   * 文本样式路径
   */
  content_css: '/tinymce/skins/content/default/content.css',

  /**
   * 表情路径
   */
  // emoticons_database_url: '/public/tinymce/emojis/emojis.min.js',
  /**
   * 字体
   */
  font_formats: '宋体=songti;黑体=heiti;仿宋=fangsong;微软雅黑=yahei;楷体=kaiti;思源宋体=siyuansongti;思源黑体=siyuanheiti',
  /**
   * 宽度
   */
  width: '100%',

  /**
   * 高度
   */
  height: '300px',

  /**
   * 插件
   */
  plugins: 'customIcons lists code pagebreak charmap  save preview  uploadImg  link tips noneditable indent2em lineHeight ' +
           'anchor codesample table wordcount fullscreen help searchreplace  insertdatetime ' +
           'uploadAudio uploadVideo ',
  /**
   * 菜单栏
   * file 文件
   * edit 编辑
   * view 视图
   * insert 插入
   * format 格式
   * tools 工具
   * table 表格
   * help 帮助
   */
  menubar: '',
  /**
   * 工具栏
   * https://www.tiny.cloud/docs/demo/full-featured/
   * | formatselect fontselect fontsizeselect | 段落、字体、字号
   * | undo redo | 撤销、重做
   * | code bold italic underline strikethrough | 源代码、粗体、斜体、下划线、删除线
   * | alignleft aligncenter alignright alignjustify | 左对齐、中间对齐、右对齐、两端对齐
   * | outdent indent numlist bullist insertdatetime | 减少缩进、增加缩进、编号列表、项目列表、时间日期
   * | table forecolor backcolor removeformat | 表格、文字颜色、背景色、清除格式
   * | hr searchreplace pagebreak charmap emoticons | 水平分割线、查找替换、分页符、特殊符号、表情
   * | fullscreen preview save print | 全屏、预览、保存、打印
   * | image media link anchor codesample | 上传文件、上传素材、插入链接、锚点、插入代码
   */
  toolbar_groups: {
    formatting: {
      text: '',
      icon: 'formatterText',
      tooltip: '文字格式',
      items: ' italic underline strikethrough  superscript subscript'
    },
    alinglign: {
      text: '',
      icon: 'formatterAlign',
      tooltip: '对齐方式',
      items: 'alignleft aligncenter alignright alignjustify'
    },
    indentGroup: {
      text: '',
      icon: 'indent',
      tooltip: '缩进',
      items: 'outdent indent numlist bullist   indent2em'
    }},
  toolbar1: ' fontsizeselect ' +
           ' bold lineHeight  formatting   wordcount' +
  'table forecolor backcolor removeformat ' +
           'charmap emoticons kityformula-editor ' +
           'codesample   uploadImg  uploadVideo ',

  /**
   * 工具栏展开方式
   */
  toolbar_mode: 'floating',
  toolbar_location: 'right',
  statusbar: true,
  /**
   * 格式化工具
   */
  style_formats: [
    {
      title: '首行缩进',
      block: 'p',
      styles: {
        'text-indent': '2em'
      }
    }
  ],
  // extended_valid_elements: 'span[class|style|data-*],mjx-container[class|style|jax|tabindex|ctxtmenu_counter],mjx-math[class|aria-hidden],mjx-msub,mjx-script,mjx-texatom[class|size|texclass],mjx-mn[class],mjx-c[class]',
  // custom_elements: 'mjx-container,mjx-math,mjx-msub,mjx-script,mjx-texatom,mjx-mn,mjx-c',
  // init_instance_callback: function (editor) {
  //   // 重新渲染 MathJax 公式
  //   window.MathJax.typesetPromise()
  // },
  /**
   * 是否允许拖动
   * true（仅允许改变高度）, false（完全不让你动）, 'both'（宽高都能改变，注意引号）
   */
  resize: true,

  /**
   * 底部状态栏
   */

  /**
   * 是否显示版权信息
   */
  branding: false,

  /**
   * 是否允许黏贴图片
   */
  paste_data_images: true,
  //    添加扩展插件

  powerpaste_word_import: 'merge', // 参数可以是propmt, merge, clear，效果自行切换对比
  powerpaste_html_import: 'merge', // propmt, merge, clear
  powerpaste_allow_local_images: true,
  /**
   * 时间日期格式化
   */
  insertdatetime_formats: ['%H点%M分', '%Y年%m月%d日', '%Y年%m月%d日 %H点%M分', '%Y-%m-%d %H:%M'],
  paste_preprocess: function(plugin, args) {
    if (args.content.indexOf('test_card') !== -1) {
      Message.warning('粘贴内容含有习题集，禁止粘贴')
      args.content = ''
      return
    }
    if (args.content.indexOf('mceNonEditable') !== -1) {
      Message.warning('粘贴内容含有图片集，视频集等功能，请确认其是否有效')
    }
  },
  // 默认使用base64格式
  urlconverter_callback: (url, node, onSave, name) => {
    if (node === 'img' && url.startsWith('blob:')) {
      console.log('urlConverter:', url, node, onSave, name)
      tinymce.activeEditor && tinymce.activeEditor.uploadImages()
    }
    // Return new URL
    return url
  },
  images_upload_handler: async function (blobInfo, success, failure) {
    const file = blobInfo.blob()
    const isLt2M = file.size / 1024 / 1024 < 1
    console.log(file.size, isLt2M)
    if (!isLt2M) {
      Message.warning('上传图片超过1M,可能会影响阅读体验，请压缩图片')
    }
    const { data } = await getFileUploadAuthor({
      mediaType: 'IMAGE',
      contentType: '',
      quantity: 1,
      fileName: file.name
    })
    const ossCDN = data[0].ossConfig.ossCDN
    try {
      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)
      const name = file.name ? file.name : ''
      const notif = Notification({
        title: `${name}上传中`,
        message: '0%',
        duration: 0
      })
      await axios.post(data[0].ossConfig.host, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          notif.message = complete + '%'
          console.log(complete)
          if (complete >= 100) {
            notif.close()
          }
        }
      })
      success(`${ossCDN}/${data[0].fileName}`, data[0].fileName, '')
    } catch (error) {
      console.log(error)
      failure('文件上传失败，请重试')
    }
  },
  init_instance_callback: function (editor) {
    editor.on('NodeChange', function (e) {
      if (e && e.element.nodeName.toLowerCase() === 'img') {
        const img = e.element
        const width = img.style.width
        const height = img.style.height
        img.removeAttribute('width')
        img.removeAttribute('height')
        // 如果宽度和高度是以像素为单位，则转换为百分比
        if (width.endsWith('px')) {
          const newWidth = (parseFloat(width) / editor.getBody().offsetWidth * 100) + '%'
          img.style.width = newWidth
          img.setAttribute('data-mce-style', `width: ${newWidth};`)
        }
        if (height.endsWith('px')) {
          const newHeight = (parseFloat(height) / editor.getBody().offsetHeight * 100) + '%'
          img.style.height = newHeight
          img.setAttribute('data-mce-style', `height: ${newHeight};`)
        }
      }
      if (e && e.element.nodeName.toLowerCase() === 'video') {
        const video = e.element
        const editorWidth = editor.getBody().offsetWidth
        if (video.getAttribute('width') === '100%') {
          return
        }
        const width = video.style.width || video.getAttribute('width') + 'px'
        console.log(width)
        // 确保宽度是有效的数值
        if (width && width.endsWith('px')) {
          const pixelWidth = parseFloat(width)
          const widthPercent = ((pixelWidth / editorWidth) * 100).toFixed(2) + '%'

          // 设置视频宽度为百分比
          video.setAttribute('width', widthPercent)
          video.style.width = widthPercent
          video.setAttribute('data-mce-style', `width: ${widthPercent};`)
          video.removeAttribute('height') // 可选：移除高度以保持比例
        }
      }
    })
  }
}

