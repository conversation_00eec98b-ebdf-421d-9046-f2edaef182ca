import tinymce from 'tinymce/tinymce'
import { addMoveAndDrag } from '@/utils/tinymceEditor'
tinymce.PluginManager.add('colorBlock', function (editor) {
  const selectedDiv = null
  editor.ui.registry.addButton('colorBlock', {
    icon: 'addColorBlock',
    test: '添加色块',
    tooltip: '颜色选择',
    onAction: function () {
      // 创建一个简单的颜色选择器对话框
      editor.windowManager.open({
        title: '选择颜色',
        body: {
          type: 'panel',
          items: [
            {
              type: 'colorinput',
              name: 'color',
              label: '选择颜色'
            }
          ]
        },
        buttons: [
          {
            text: '应用',
            type: 'submit',
            primary: true
          },
          {
            text: '取消',
            type: 'cancel'
          }
        ],
        onSubmit: function (api) {
          const data = api.getData()
          const color = data.color
          console.log(color)
          // editor.execCommand('mceApplyTextcolor', false, color)
          api.close()
          const media = `<div class="draggable-div" style='width:100%;height: auto;box-sizing: border-box;background: ${color};overflow:hidden;padding:5px;position:relative;margin-bottom:2px;z-index:100'>
                           <p class='draggable-main' style="width: 100%;height: auto">请输入</p>
                        </div><br>`
          editor.insertContent(media)
        }
      })
    }
  })
  addMoveAndDrag(editor, selectedDiv)
})
