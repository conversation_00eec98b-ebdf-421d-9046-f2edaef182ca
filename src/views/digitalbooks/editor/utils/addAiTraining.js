import { addAiTrainingModal, setData, closeAiTraining } from '../components/addAiTraining/addAiTraining'
import { MessageBox } from 'element-ui'
export function aiTraining(editor) {
  editor.ui.registry.addContextToolbar('aiEditcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      return node.classList.contains('ai_card')
      // return node.nodeName.toLowerCase() === 'p'
    },
    items: 'aiEditcontrol aiRemovecontrol', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
    // scope: 'editor'
  })
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('aiEditcontrol', {
    icon: 'edit-block',
    title: '编辑实训',
    tooltip: '编辑实训',
    onAction: () => {
      const bookmark = editor.selection.getBookmark()
      const selectContent = editor.selection.getNode()
      const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
      setData(oldData)
      addAiTrainingModal({
        onSubmit (data) {
          editor.selection.moveToBookmark(bookmark)
          const media = `<div class='ai_card mceNonEditable' data-id="${data.id}" style='width:100%;background: #E3F5FF;overflow:hidden;padding:30px;box-sizing: border-box;position:relative;margin-bottom:2px'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%">${data.subTitle}</div></div><div class='to_aiTrain' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
          editor.selection.setContent(media)
          closeAiTraining()
        }
      })
    }
  })
  editor.ui.registry.addButton('aiRemovecontrol', {
    icon: 'remove',
    title: '删除实训',
    tooltip: '删除实训',
    onAction: () => {
      const bookmark = editor.selection.getBookmark()
      MessageBox.confirm('确认删除实训？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        editor.selection.moveToBookmark(bookmark)
        editor.focus()
        editor.selection.setContent('')
        editor.selection.collapse()
      })
    }
  })
  editor.ui.registry.addButton('aiTraining', {
    text: '人工智能实训',
    title: '人工智能实训',
    tooltip: '人工智能实训',
    onAction: function () {
      let type = 'add'
      const selectContent = editor.selection.getNode()
      const bookmark = editor.selection.getBookmark()
      if (selectContent.classList.contains('ai_card')) {
        const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
        setData(oldData)
        type = 'edit'
      }
      addAiTrainingModal({
        onSubmit (data) {
          if (type === 'add') {
            // editor.selection.moveToBookmark(bookmark)
            const media = `<div class='ai_card mceNonEditable' data-id="${data.id}" style='width:100%;background: #E3F5FF;overflow:hidden;padding:30px;box-sizing: border-box;position:relative;margin-bottom:2px'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%;">${data.subTitle}</div></div><div class='to_aiTrain' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
            editor.selection.setContent(media)
            const content = editor.getContent()
            editor.setContent(content)
          }
          if (type === 'edit') {
            editor.selection.moveToBookmark(bookmark)
            const media = `<div class='ai_card mceNonEditable' data-id="${data.id}" style='width:100%;background: #E3F5FF;overflow:hidden;padding:30px;box-sizing: border-box;position:relative;margin-bottom:2px'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%;">${data.subTitle}</div></div><div class='to_aiTrain' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
            editor.selection.setContent(media)
          }
          closeAiTraining()
        }

      })
    }
  })
}
