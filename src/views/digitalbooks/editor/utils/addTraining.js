import tinymce from 'tinymce/tinymce'
import { addTrainingsModal, setData, closeTrainings } from '../components/addTraining/addTraining'
import { MessageBox } from 'element-ui'
export function trainingdPlugin(editor) {
  editor.ui.registry.addContextToolbar('trainingEditcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      return node.classList.contains('training_card')
      // return node.nodeName.toLowerCase() === 'p'
    },
    items: 'trainingtrol trainingremovecontrol', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
    // scope: 'editor'
  })
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('trainingtrol', {
    icon: 'edit-block',
    tooltip: '编辑实训',
    onAction: () => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      const selectContent = tinymce.activeEditor.selection.getNode()
      const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
      setData(oldData)
      addTrainingsModal({
        onSubmit (data) {
          tinymce.activeEditor.selection.moveToBookmark(bookmark)
          const media = `<div class='training_card mceNonEditable' data-id="${data.id}" style='margin-bottom:2px;width:100%;background: #E3F5FF;overflow:hidden;padding:30px;box-sizing: border-box;position:relative'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%">${data.subTitle}</div></div><div class='to_training' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
          tinymce.activeEditor.selection.setContent(media)
          closeTrainings()
        }
      })
    }
  })
  editor.ui.registry.addButton('trainingremovecontrol', {
    icon: 'remove',
    tooltip: '删除实训',
    onAction: (editor) => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      MessageBox.confirm('确认删除实训？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tinymce.activeEditor.selection.moveToBookmark(bookmark)
        tinymce.activeEditor.focus()
        tinymce.activeEditor.selection.setContent('')
        tinymce.activeEditor.selection.collapse()
      })
    }
  })
  editor.ui.registry.addButton('trainingdPlugin', {
    text: 'SQL实训',
    tooltip: 'sql',
    onAction: function () {
      let type = 'add'
      const selectContent = tinymce.activeEditor.selection.getNode()
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      if (selectContent.classList.contains('training_card')) {
        const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
        setData(oldData)
        type = 'edit'
      }
      addTrainingsModal({
        onSubmit (data) {
          if (type === 'add') {
            const media = `<div class='training_card mceNonEditable' data-id="${data.id}" style='margin-bottom:2px;width:100%;background: #E3F5FF;overflow:hidden;padding:30px;box-sizing: border-box;position:relative'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%;">${data.subTitle}</div></div><div class='to_training' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
            tinymce.activeEditor.selection.setContent(media)
            const content = editor.getContent()
            editor.setContent(content)
          }
          if (type === 'edit') {
            tinymce.activeEditor.selection.moveToBookmark(bookmark)
            const media = `<div class='training_card mceNonEditable' data-id="${data.id}" style='margin-bottom:2px;width:100%;background: #E3F5FF;overflow:hidden;padding:30px;box-sizing: border-box;position:relative'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%;">${data.subTitle}</div></div><div class='to_training' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
            tinymce.activeEditor.selection.setContent(media)
          }
          closeTrainings()
        }

      })
    }
  })
}
