import tinymce from 'tinymce/tinymce'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { Notification } from 'element-ui'
export function uploadAudio (editor, url) {
  return editor.ui.registry.addButton('uploadAudio', {
    icon: 'audioUpload',
    tooltip: '插入音频',
    onAction: function () {
      var input = document.createElement('input')
      input.type = 'file'
      input.accept = 'audio/*'
      // 执行上传文件操作
      input.addEventListener('change', async function (e) {
        var file = e.target.files[0]
        const { data } = await getFileUploadAuthor({
          mediaType: 'AUDIO',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })
        const ossCDN = data[0].ossConfig.ossCDN
        try {
          const formData = new FormData()
          formData.append('success_action_status', '200')
          formData.append('callback', '')
          formData.append('key', data[0].fileName)
          formData.append('policy', data[0].policy)
          formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
          formData.append('signature', data[0].signature)
          formData.append('file', file)
          const notif = Notification({
            title: `${file.name}上传中`,
            message: '0%',
            duration: 0
          })
          await axios.post(data[0].ossConfig.host, formData, {
            onUploadProgress: (progress) => {
              const complete = Math.floor(progress.loaded / progress.total * 100)
              notif.message = complete + '%'
              console.log(complete)
              if (complete >= 100) {
                notif.close()
              }
            }
          })
          var media = `<audio controlsList="nodownload" controls="controls"  src="${ossCDN}/${data[0].fileName}">`
          tinymce.activeEditor.selection.setContent(media)
          // success(`${ossCDN}/${data[0].fileName}`, data[0].fileName, '')
        } catch (error) {
          console.log(error)
          // failure('文件上传失败，请重试')
        }
      }, false)
      // 触发点击事件，打开选择文件的对话框
      input.click()
    }
  })
}
