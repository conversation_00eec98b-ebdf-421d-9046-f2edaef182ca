import tinymce from 'tinymce/tinymce'
tinymce.PluginManager.add('trainingListbox', function (editor) {
  editor.ui.registry.addMenuButton('trainingListbox', {
    icon: 'addTraining',
    tooltip: '实训',
    fetch: function (callback) {
      const items = [
        {
          type: 'menuitem',
          text: 'SQL实训',
          onAction: function () {
            const button = editor.ui.registry.getAll().buttons.trainingdplugin
            if (button && button.onAction) {
              button.onAction()
            }
          }
        },
        {
          type: 'menuitem',
          text: '财务实训',
          onAction: function () {
            const button = editor.ui.registry.getAll().buttons.exceltrain
            if (button && button.onAction) {
              button.onAction()
            }
          }
        },
        {
          type: 'menuitem',
          text: '人工智能实训',
          onAction: function () {
            const button = editor.ui.registry.getAll().buttons.aitraining
            if (button && button.onAction) {
              button.onAction()
            }
          }
        }
      ]
      callback(items)
    }
  })
})
