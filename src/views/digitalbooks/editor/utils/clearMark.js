import tinymce from 'tinymce/tinymce'
export function clear (editor, url) {
  // 添加悬浮 上下文工具栏
  editor.ui.registry.addContextToolbar('Markeditcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      return node.classList.contains('set_review')
      // return node.nodeName.toLowerCase() === 'p'
    },
    items: 'clearControl', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
    // scope: 'editor'
  })
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('clearControl', {
    text: '删除标注',
    tooltip: '编辑图片集',
    onAction: () => {
      const selectContent = tinymce.activeEditor.selection.getNode()
      selectContent.removeAttribute('data-id')
      selectContent.removeAttribute('data-mce-style')
      selectContent.style.border = 'none'
      selectContent.style.color = ''
      selectContent.classList.remove('set_review')
    }
  })
}
