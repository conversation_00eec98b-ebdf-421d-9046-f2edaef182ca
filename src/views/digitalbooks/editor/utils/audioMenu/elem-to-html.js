
function audioToHtml (elemNode, childrenHtml) {
  const { src = '', width = 'auto', height = 'auto' } = elemNode
  const html = `
                <div data-w-e-type="audio" data-w-e-is-void>
                  <audio controls width="${width}" height="${height}">
                  <source src="${src}" type="audio/mpeg"/>
                  <source src="${src}" type="audio/ogg"/>
                  <source src="${src}" type="audio/wav"/>
                  </audio>
                </div>
              `
  return html
}

export const audioToHtmlConf = {
  type: 'audio',
  elemToHtml: audioToHtml
}
