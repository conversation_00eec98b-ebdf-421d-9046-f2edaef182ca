// import { Boot, SlateTransforms, DomEditor } from '@wangeditor/editor'
// import { getFileUploadAuthor } from '@/api/user-api'
// import axios from 'axios'
// import { Notification } from 'element-ui'
// import $ from 'jquery'
// import { h, VNode } from 'snabbdom'

// class AudioMenu {
//   /**
//    * 【注意】svg 字符串的长度 ，否则会导致代码体积过大
//    * 尽量选择 https://www.iconfont.cn/collections/detail?spm=a313x.7781069.0.da5a778a4&cid=20293
//    * 找不到再从 iconfont.com 搜索
//    */
//   constructor () {
//     this.title = '音频' // 自定义菜单标题
//     this.iconSvg = '<svg t="1699433346543" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6274" width="128" height="128"><path d="M960 0h64v736c0 88.352-100.288 160-224 160s-224-71.648-224-160 100.288-160 224-160c62.688 0 119.328 18.4 160 48.032V256l-512 113.792V864c0 88.352-100.288 160-224 160s-224-71.648-224-160 100.288-160 224-160c62.688 0 119.328 18.4 160 48.032V128l576-128z" p-id="6275"></path></svg>' // 可选
//     this.tag = 'button'
//   }

//   // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
//   getValue (editor) {
//     return 'hello'
//   }

//   // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
//   isActive (editor) {
//     return false
//   }

//   // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
//   isDisabled (editor) {
//     return false
//   }

//   // 点击菜单时触发的函数
//   exec (editor, value) {
//     if (this.isDisabled(editor)) return
//     uploadBtn(editor)
//   }
// }

// const uploadBtn = (editor) => {
//   const body = document.body
//   const input = document.createElement('input')
//   input.type = 'file'
//   input.accept = 'audio/*'
//   input.style.display = 'none'
//   body.appendChild(input)
//   input.click()
//   input.addEventListener('change', (event) => {
//     const file = event.target.files[0]
//     console.log('File selected:', file)
//     // insertAudio(editor, file)
//     constcustomUpload(file, (src, poster) => insertAudio(editor, src))
//   })
// }

// const constcustomUpload = async (file, insertFn) => {
//   const { data } = await getFileUploadAuthor({
//     mediaType: 'AUDIO',
//     contentType: '',
//     quantity: 1,
//     fileName: file.name
//   })
//   const ossCDN = data[0].ossConfig.ossCDN
//   try {
//     const formData = new FormData()
//     formData.append('success_action_status', '200')
//     formData.append('callback', '')
//     formData.append('key', data[0].fileName)
//     formData.append('policy', data[0].policy)
//     formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
//     formData.append('signature', data[0].signature)
//     formData.append('file', file)

//     const notif = Notification({
//       title: `${file.name}上传中`,
//       message: '0%',
//       duration: 0
//     })
//     await axios.post(data[0].ossConfig.host, formData, {
//       onUploadProgress: (progress) => {
//         const complete = Math.floor(progress.loaded / progress.total * 100)
//         notif.message = complete + '%'
//         console.log(complete)
//         if (complete >= 100) {
//           notif.close()
//         }
//       }
//     })
//     insertFn(`${ossCDN}/${data[0].fileName}`)
//   } catch (error) {
//     console.log(error)
//   }
// }

// const insertAudio = (editor, src) => {
//   if (!src) return

//   // 还原选区
//   editor.restoreSelection()

//   const audio = {
//     type: 'audio',
//     src: src,
//     children: [{ text: '' }] // 【注意】void node 需要一个空 text 作为 children
//   }

//   Promise.resolve().then(() => {
//     SlateTransforms.insertNodes(editor, audio)
//   })
// }

// /* -------------------------------------定义渲染方法------------------------------------ */
// function renderAudio (elemNode, children, editor) {
//   // 获取“附件”的数据，参考上文 myResume 数据结构
//   const { src = '', width = 'auto', height = 'auto' } = elemNode
//   // 是否选中
//   const selected = DomEditor.isNodeSelected(editor, elemNode)
//   // 其他，mp3 格式
//   const audioVnode = (
//     <audio controls>
//       <source src={src} type='audio/mpeg' />
//       <source src={src} type='audio/ogg' />
//       <source src={src} type='audio/wav' />
//       {`Sorry, your browser doesn't support embedded audios.\n 抱歉，浏览器不支持 audio 音频`}
//     </audio>
//   )
//   // @ts-ignore 添加尺寸
//   if (width !== 'auto') audioVnode.datba.width = width
//   // @ts-ignore
//   if (height !== 'auto') audioVnode.data.height = height
//   const vnode = (
//     <div
//       className='w-e-textarea-audio-container'
//       data-selected={selected ? 'true' : ''} // 标记为 选中
//     >
//       {audioVnode}
//     </div>
//   )
//   // 【注意】void node 中，renderElem 不用处理 children 。core 会统一处理。
//   const containerVnode = h(
//     'div',
//     {
//       props: {
//         contentEditable: false
//       },
//       on: {
//         mousedown: e => e.preventDefault()
//       }
//     },
//     vnode
//   )
//   return containerVnode
// }
// const renderAudioConf = {
//   type: 'audio', // 新元素 type ，重要！！！
//   renderElem: renderAudio
// }

// /* ------------------------------------新元素转化为HTML-------------------------------- */
// function audioToHtml (elemNode) {
//   // 获取附件元素的数据
//   const { src = '', width = 'auto', height = 'auto' } = elemNode
//   let html = '<div data-w-e-type="audio" data-w-e-is-void>\n'
//   // 其他，mp3 等 url 格式
//   html += `<audio controls width="${width}" height="${height}">
//       <source src="${src}" type="audio/mpeg"/>
//       <source src="${src}" type="audio/ogg"/>
//       <source src="${src}" type="audio/wav"/>
//   </audio>`
//   html += '\n</div>'
//   return html
// }
// const audioToHtmlConf = {
//   type: 'audio',
//   elemToHtml: audioToHtml
// }

// /* -----------------------------------预解析元素-------------------------------------- */
// function preParse (elem) {
//   const $elem = $(elem)
//   const $audio = $elem
//   // const elemTagName = getTagName($elem)
//   const audioTagName = getTagName($audio)
//   if (audioTagName !== 'audio') return $audio[0]
//   // 已经符合 V5 格式
//   const $parent = $audio.parent()
//   if ($parent.attr('data-w-e-type') === 'audio') return $audio[0]

//   const $container = $(`<div data-w-e-type="audio" data-w-e-is-void></div>`)
//   $container.append($audio)
//   return $container[0]
// }
// const preParseHtmlConf = {
//   selector: 'audio',
//   preParseHtml: preParse
// }
// /* --------------------------------解析新元素 HTML 到编辑器---------------------------- */
// function getAudioElem (src, width, height) {
//   return {
//     type: 'audio',
//     src,
//     width,
//     height,
//     children: [{ text: '' }] // void 元素有一个空 text
//   }
// }
// function parseAudioHtml (elem) {
//   const $elem = $(elem)
//   let src = ''
//   let width = 'auto'
//   let height = 'auto'
//   // <audio> 形式
//   const $audio = $elem.find('audio')
//   src = $audio.attr('src') || ''
//   if (!src) {
//     if ($audio.length > 0) {
//       const $source = $audio.find('source')
//       src = $source.attr('src') || ''
//     }
//   }
//   width = $audio.attr('width') || 'auto'
//   height = $audio.attr('height') || 'auto'
//   return getAudioElem(src, width, height)
// }
// const parseHtmlConf = {
//   // selector: 'div[data-w-e-type="audio"]', // CSS 选择器，匹配特定的 HTML 标签
//   selector: 'audio',
//   parseElemHtml: parseAudioHtml
// }
// // 注册按钮
// const creatAudioMenu = () => {
//   const that = this
//   const menu1Conf = {
//     key: 'audioMenu', // 定义 menu key ：要保证唯一、不重复（重要）
//     factory () {
//       return new AudioMenu(that)
//     }
//   }
//   Boot.registerMenu(menu1Conf)
//   // Boot.registerModule('audio')
// }
// export default creatAudioMenu

import withAudio from './plugin'
import { renderAudioConf } from './render-elem'
import { audioToHtmlConf } from './elem-to-html'
import { preParseHtmlConf } from './pre-parse-html'
import { parseHtmlConf } from './parse-elem-html'
import { uploadAudioMenuConf } from './menu'

const audio = {
  renderElems: [renderAudioConf],
  elemsToHtml: [audioToHtmlConf],
  preParseHtml: [preParseHtmlConf],
  parseElemsHtml: [parseHtmlConf],
  menus: [uploadAudioMenuConf],
  editorPlugin: withAudio
}

export default audio
