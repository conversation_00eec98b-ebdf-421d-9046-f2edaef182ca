import $ from 'jquery'

function getTagName ($elem) {
  if ($elem.length) return $elem[0].tagName.toLowerCase()
  return ''
}

function preParse (elem) {
  const $elem = $(elem)
  const $audio = $elem

  const audioTagName = getTagName($audio)
  if (audioTagName !== 'audio') return $audio[0]

  // 已经符合 V5 格式
  const $parent = $audio.parent()
  if ($parent.attr('data-w-e-type') === 'audio') return $audio[0]

  const $container = $(`<div data-w-e-type="audio" data-w-e-is-void></div>`)
  $container.append($audio)

  return $container[0]
}

export const preParseHtmlConf = {
  selector: 'audio',
  preParseHtml: preParse
}
