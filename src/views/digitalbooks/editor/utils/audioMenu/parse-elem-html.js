import $ from 'jquery'

function getAudioElem (src, width = 'auto', height = 'auto') {
  return {
    type: 'audio',
    src,
    width,
    height,
    children: [{ text: '' }] // void 元素有一个空 text
  }
}

function parseHtml (elem, children, editor) {
  const $elem = $(elem)
  let src = ''
  let width = 'auto'
  let height = 'auto'

  // <audio> 形式
  const $audio = $elem.find('audio')
  src = $audio.attr('src') || ''
  if (!src) {
    if ($audio.length > 0) {
      const $source = $audio.find('source')
      src = $source.attr('src') || ''
    }
  }
  width = $audio.attr('width') || 'auto'
  height = $audio.attr('height') || 'auto'
  return getAudioElem(src, width, height)
}

export const parseHtmlConf = {
  selector: 'div[data-w-e-type="audio"]',
  // selector: 'audio',
  parseElemHtml: parseHtml
}
