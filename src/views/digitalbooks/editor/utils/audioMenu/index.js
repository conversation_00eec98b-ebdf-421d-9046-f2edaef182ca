import withAudio from './plugin'
import { renderAudioConf } from './render-elem'
import { audioToHtmlConf } from './elem-to-html'
import { preParseHtmlConf } from './pre-parse-html'
import { parseHtmlConf } from './parse-elem-html'
import { audioMenuConf } from './menu'

const audio = {
  renderElems: [renderAudioConf],
  elemsToHtml: [audioToHtmlConf],
  preParseHtml: [preParseHtmlConf],
  parseElemsHtml: [parseHtmlConf],
  menus: [audioMenuConf],
  editorPlugin: withAudio
}

export default audio
