import { DomEditor } from '@wangeditor/editor'
import { h } from 'snabbdom'

function renderAudio (elemNode, children, editor) {
  const { src = '', width = 'auto', height = 'auto' } = elemNode

  // 是否选中
  const selected = DomEditor.isNodeSelected(editor, elemNode)

  // 其他，mp3 格式
  const audioVnode = (
    <audio controls>
      <source src={src} type='audio/mpeg' />
      <source src={src} type='audio/ogg' />
      <source src={src} type='audio/wav' />
      {`Sorry, your browser doesn't support embedded audios.\n 抱歉，浏览器不支持 audio 音频`}
    </audio>
  )
  // @ts-ignore 添加尺寸
  if (width !== 'auto') audioVnode.data.width = width
  // @ts-ignore
  if (height !== 'auto') audioVnode.data.height = height

  const vnode = (
    <div
      className='w-e-textarea-audio-container'
      data-selected={selected ? 'true' : ''} // 标记为 选中
    >
      {audioVnode}
    </div>
  )

  // 【注意】void node 中，renderElem 不用处理 children 。core 会统一处理。

  const containerVnode = h(
    'div',
    {
      props: {
        contentEditable: false
      },
      on: {
        mousedown: e => e.preventDefault()
      }
    },
    vnode
  )

  return containerVnode
}

const renderAudioConf = {
  type: 'audio', // 和 elemNode.type 一致
  renderElem: renderAudio
}

export { renderAudioConf }
