import { DomEditor, SlateTransforms, SlateRange } from '@wangeditor/editor'
import $ from 'jquery'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { Notification } from 'element-ui'

async function insertAudio (editor, src) {
  if (!src) return

  // 还原选区
  editor.restoreSelection()

  const audio = {
    type: 'audio',
    src: src,
    children: [{ text: '' }] // 【注意】void node 需要一个空 text 作为 children
  }

  Promise.resolve().then(() => {
    SlateTransforms.insertNodes(editor, audio)
  })
}

async function customUpload (file, insertFn) {
  const { data } = await getFileUploadAuthor({
    mediaType: 'AUDIO',
    contentType: '',
    quantity: 1,
    fileName: file.name
  })
  const ossCDN = data[0].ossConfig.ossCDN
  try {
    const formData = new FormData()
    formData.append('success_action_status', '200')
    formData.append('callback', '')
    formData.append('key', data[0].fileName)
    formData.append('policy', data[0].policy)
    formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
    formData.append('signature', data[0].signature)
    formData.append('file', file)

    const notif = Notification({
      title: `${file.name}上传中`,
      message: '0%',
      duration: 0
    })
    await axios.post(data[0].ossConfig.host, formData, {
      onUploadProgress: (progress) => {
        const complete = Math.floor(progress.loaded / progress.total * 100)
        notif.message = complete + '%'
        console.log(complete)
        if (complete >= 100) {
          notif.close()
        }
      }
    })
    insertFn(`${ossCDN}/${data[0].fileName}`)
  } catch (error) {
    console.log(error)
  }
}

class AudioMenu {
  /**
   * 【注意】svg 字符串的长度 ，否则会导致代码体积过大
   * 尽量选择 https://www.iconfont.cn/collections/detail?spm=a313x.7781069.0.da5a778a4&cid=20293
   * 找不到再从 iconfont.com 搜索
   */
  constructor () {
    this.title = '音频' // 自定义菜单标题
    this.iconSvg = '<svg t="1699433346543" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6274" width="128" height="128"><path d="M960 0h64v736c0 88.352-100.*********** 160s-224-71.*********** 100.***********-160c62.688 0 119.328 18.4 160 48.032V256l-512 113.792V864c0 88.352-100.*********** 160s-224-71.*********** 100.***********-160c62.688 0 119.328 18.4 160 48.032V128l576-128z" p-id="6275"></path></svg>' // 可选
    this.tag = 'button'
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue (editor) {
    return false
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive (editor) {
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled (editor) {
    const { selection } = editor
    if (selection == null) return true
    if (!SlateRange.isCollapsed(selection)) return true // 选区非折叠，禁用

    const selectedElems = DomEditor.getSelectedElems(editor)
    const hasVoidOrPre = selectedElems.some(elem => {
      const type = DomEditor.getNodeType(elem)
      if (type === 'pre') return true
      if (type === 'list-item') return true
      if (editor.isVoid(elem)) return true
      return false
    })
    if (hasVoidOrPre) return true // void 或 pre ，禁用

    return false
  }

  // 点击菜单时触发的函数
  exec (editor, value) {
    if (this.isDisabled(editor)) return
    // 设置选择文件的类型
    const acceptAttr = `accept="${['audio/*'].join(', ')}"`

    // 添加 file input（每次重新创建 input）
    const $body = $('body')
    const $inputFile = $(`<input type="file" ${acceptAttr}/>`)
    $inputFile.hide()
    $body.append($inputFile)
    $inputFile.click()
    // 选中文件
    $inputFile.on('change', () => {
      const files = ($inputFile[0]).files
      uploadAudios(editor, files) // 上传文件
    })
  }
}

async function uploadAudios (editor, files) {
  if (files == null) return
  const fileList = Array.prototype.slice.call(files)

  // 按顺序上传
  for await (const file of fileList) {
    // 上传
    // 自定义上传
    await customUpload(file, (src, poster) => insertAudio(editor, src, poster))
  }
}

export const audioMenuConf = {
  key: 'uploadAudio',
  factory () {
    return new AudioMenu()
  }
}
