import tinymce from 'tinymce/tinymce'
import { addTipsModal, closeTips, setData } from '../components/addTips'
import { Message } from 'element-ui'
export function tips (editor, url) {
  editor.ui.registry.addContextToolbar('tipscardeditcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      return node.classList.contains('tips_item')
      // return node.nodeName.toLowerCase() === 'p'
    },
    items: 'tipscardchangecontrol tipscardremovecontrol', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
    // scope: 'editor'
  })
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('tipscardchangecontrol', {
    icon: 'edit-block',
    tooltip: '编辑气泡',
    onAction: () => {
      const selectContent = tinymce.activeEditor.selection.getNode()
      console.log(selectContent)
      setData({
        keyword: selectContent.innerText,
        content: selectContent.children[0].innerText
      })
      addTipsModal({
        onSubmit (data) {
          let content = editor.getContent()
          const oldMedia = `<span class="tips_item mceNonEditable" style="text-decoration: underline; cursor: pointer;">${selectContent.innerText}<span class="tips_content" style="display: none;">${selectContent.children[0].innerText}</span></span>`
          const NewMedia = `<span style='text-decoration: underline;cursor: pointer;' class='tips_item mceNonEditable'>${data.keyword}<span class='tips_content' style='display:none'>${data.content}</span></span>`
          content = content.replace(oldMedia, NewMedia)
          editor.setContent(content)
          closeTips()
        }
      })
    }
  })
  editor.ui.registry.addButton('tipscardremovecontrol', {
    icon: 'remove',
    tooltip: '删除气泡',
    onAction: (editor) => {
      // tinymce.activeEditor.undoManager.add()
      console.log(tinymce.activeEditor)
      tinymce.activeEditor.focus()
      tinymce.activeEditor.selection.setContent('')
      tinymce.activeEditor.selection.collapse()
    }
  })
  return editor.ui.registry.addButton('tips', {
    icon: 'tips',
    tooltip: '添加气泡',
    onAction: function () {
      let type = 'add'
      const selectContent = tinymce.activeEditor.selection.getNode()
      const text = tinymce.activeEditor.selection.getContent()
      console.log(selectContent)
      if (!selectContent.classList.contains('tips_item') && text === '') {
        Message.warning('请选择内容')
        return
      }
      if (selectContent.classList.contains('tips_item')) {
        setData({
          keyword: selectContent.innerText,
          content: selectContent.children[0].innerText
        })
        type = 'edit'
      } else {
        setData({
          keyword: text,
          content: ''
        })
      }
      addTipsModal({
        onSubmit (data) {
          console.log(data)
          if (data.keyword === '' || data.content === '') {
            Message.warning('请填写气泡内容')
            return
          }
          if (type === 'add') {
            const media = `<span style='text-decoration: underline;cursor: pointer;' class='tips_item mceNonEditable'>${data.keyword}<span class='tips_content' style='display:none'>${data.content}</span></span>`
            tinymce.activeEditor.selection.setContent(media)
          } else {
            let content = editor.getContent()
            const oldMedia = `<span class="tips_item mceNonEditable" style="text-decoration: underline; cursor: pointer;">${selectContent.innerText}<span class="tips_content" style="display: none;">${selectContent.children[0].innerText}</span></span>`
            const NewMedia = `<span style='text-decoration: underline;cursor: pointer;' class='tips_item mceNonEditable'>${data.keyword}<span class='tips_content' style='display:none'>${data.content}</span></span>`
            content = content.replace(oldMedia, NewMedia)
            editor.setContent(content)
          }
          closeTips()
        }
      })
    }
  })
}
