export function addIframe (editor, url) {
  return editor.ui.registry.addButton('customIframe', {
    icon: 'iframe',
    tooltip: '嵌入网页',
    onAction: function () {
      // 打开自定义对话框
      editor.windowManager.open({
        title: '嵌入网页',
        body: {
          type: 'panel',
          items: [
            {
              type: 'input',
              name: 'url',
              label: ' 网页地址'
            },
            {
              type: 'input',
              name: 'height',
              label: ' 网页高度(px)'
            }
          ]
        },
        initialData: {
          url: '',
          height: '400'
        },
        buttons: [
          {
            type: 'submit',
            text: '添加'
          },
          {
            type: 'cancel',
            text: '取消'
          }
        ],
        onSubmit: function (api) {
          // 获取输入的 URL
          var data = api.getData()
          if (!data.url.trim()) {
            editor.windowManager.alert('网页地址不能为空')
            return
          }
          if (!data.height.trim()) {
            editor.windowManager.alert('网页高度不能为空')
            return
          }
          // 如果URL没有带协议,添加http
          if (!/^https?:\/\//i.test(data.url)) {
            data.url = 'https://' + data.url
          }
          var iframeHtml = `<iframe src="${data.url}" width="100%" height="${data.height}" frameborder="0" scrolling="no"></iframe>`
          // 插入 iframe 到编辑器
          editor.insertContent(iframeHtml)
          api.close()
        }
      })
    }
  })
}
