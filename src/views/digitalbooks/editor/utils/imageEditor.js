import tinymce from 'tinymce/tinymce'
import { getFileUploadAuthor } from '@/api/user-api'
import { Message, Notification } from 'element-ui'
import axios from 'axios'
import { addSetImgSizeModal, closeSetImgSize, setData } from '@/views/digitalbooks/editor/components/setImgSize'
import { addMoveAndDrag } from '@/utils/tinymceEditor'
tinymce.PluginManager.add('imageEditor', function (editor) {
  const selectedDiv = null
  // 创建隐藏的文件输入元素
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = 'image/*'
  fileInput.style.display = 'none'
  // 添加到文档中
  document.body.appendChild(fileInput)
  // 监听文件选择事件
  fileInput.addEventListener('change', async function() {
    const file = fileInput.files[0]
    const isLt2M = file.size / 1024 / 1024 < 1
    console.log(file.size, isLt2M)
    if (!isLt2M) {
      Message.warning('上传图片超过1M,可能会影响阅读体验，请压缩图片')
    }
    const { data } = await getFileUploadAuthor({
      mediaType: 'IMAGE',
      contentType: '',
      quantity: 1,
      fileName: file.name
    })
    const ossCDN = data[0].ossConfig.ossCDN
    try {
      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)
      const notif = Notification({
        title: `${file.name}上传中`,
        message: '0%',
        duration: 0
      })
      await axios.post(data[0].ossConfig.host, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          notif.message = complete + '%'
          console.log(complete)
          if (complete >= 100) {
            notif.close()
          }
        }
      })
      if (!isLt2M) {
        setData({
          url: `${ossCDN}/${data[0].fileName}`
        })
        addSetImgSizeModal({
          onSubmit (data) {
            addImage(`${data.url}`)
            closeSetImgSize()
          },
          onCancel() {

          }
        })
      } else {
        addImage(`${ossCDN}/${data[0].fileName}`)
      }
    } catch (e) {
      console.log(e)
    }
    function addImage(url) {
      if (selectedDiv) {
        selectedDiv.style.backgroundImage = `url('${url}')`
      } else {
        const media = `
          <div class="draggable-div"
                 style="width: 200px; height: 200px;padding: 5px;background-size: cover;position: relative;
                 background-image: url('${url}');background-size: cover;overflow: hidden;margin-bottom: 2px">
                 <p class='draggable-main' style="width: 100%;height: auto">请编辑</p>
          </div><br>
        `
        editor.insertContent(media)
      }
      fileInput.value = ''
    }
  })
  // 添加按钮
  editor.ui.registry.addButton('imageEditor', {
    icon: 'imageEditor',
    test: '可编辑图片',
    tooltip: '可编辑图片',
    onAction: function () {
      fileInput.click()
    }
  })
  addMoveAndDrag(editor, selectedDiv)
})
