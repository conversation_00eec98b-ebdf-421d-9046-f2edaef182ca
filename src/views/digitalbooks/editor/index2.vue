<template>
  <div class="editor-dig">
    <div class="dig-head">
      <!-- <div>
        <div class="pointer flex items-center" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          返回
        </div>
      </div> -->
      <div>{{ bookTitle }}</div>
      <div class="flex">
        <div v-show="autoTips" class="dig-tips">自动保存中...</div>
        <div class="dig-btn" @click="preShowFn()">预览</div>
        <div class="dig-btn" @click="saveHtml(true)">保存</div>
      </div>
    </div>
    <div class="dig-box">
      <div class="dig-left">
        <div class="chapter-title">
          <div class="icon1">
            <img src="../../../assets/digitalbooks/chapter.svg" />
            目录
          </div>
          <div class="flex items-center">
            <div class="add-btn" @click="treeAppend(null , null)">
              +新建章节/任务
            </div>
            <!-- <i class="el-icon-s-fold pointer"></i> -->
            <!-- <i class="el-icon-s-unfold pointer"></i> -->
          </div>
        </div>
        <div class="chapter-body">
          <el-tree
            :data="bookTree"
            :props="treeProps"
            node-key="id"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
            draggable
            @node-drag-start="handleDragStart"
            @node-drop="handleDrop"
            @node-click="handleNodeClick"
          >
            <div slot-scope="{ node, data }" class="tree-body">
              <div class="chapter-name">
                <div :title="data.title" class="w article-singer-container">
                  {{ data.title }}
                </div>
              </div>
              <div class="chapter-option">
                <el-button
                  type="text"
                  size="mini"
                  @click.stop="() => treeAppend(node, data)"
                >
                  <i class="el-icon-plus"></i>
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click.stop="() => treeEdit(node, data)"
                >
                  <i class="el-icon-edit"></i>
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click.stop="() => treeRemove(node, data)"
                >
                  <i class="el-icon-delete"></i>
                </el-button>
              </div>
            </div>
          </el-tree>
        </div>
      </div>
      <div v-loading="loading" class="dig-center">
        <div v-show="otherUse" class="other-use">
          <div style="background-color: white;min-width: 1vw;position: absolute;left: 50%;top: 50%">已被其它用户占用</div>
        </div>
        <div class="toolbar">
          <Toolbar
            v-if="!loading"
            :editor="editor"
            :default-config="toolbarConfig"
            :mode="mode"
          />
        </div>
        <div class="eidtor" @click="handleEdit">
          <Editor
            v-if="!loading"
            v-model="html"
            style="min-height: 400px;overflow-y: hidden;"
            :default-config="editorConfig"
            :mode="mode"
            @onCreated="onCreated"
            @onFocus="onFocus"
            @onBlur="onBlur"
            @onChange="onChange"
          />
        </div>
      </div>
    </div>
    <editChapter
      v-if="editChapterShow"
      :show="editChapterShow"
      :append-to-body="true"
      :node-info="currChapter"
      @close="editChapterShow = false"
      @emitSucess="eidtDone"
    />
    <div v-if="preShow" class="pre">
      <Read :pre-mode="true" :node="preNode" @close="preShow = false" />
    </div>
  </div>
</template>

<script>

import { Boot } from '@wangeditor/editor'
import { SlateTransforms } from '@wangeditor/editor'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'
import editChapter from './components/editChapter.vue'
import { getBook, deleteBookCatalogue, saveContent, getContent, getBookCatalogue, dragCatalogue, takeBook } from '@/api/digital-api.js'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { getToken } from '@/utils/auth'
import Read from '@/views/digitalbooks/read/index.vue'
import { Notification } from 'element-ui'

// 注册音频组件
import audio from '@/views/digitalbooks/editor/utils/audioMenu/index.js'
Boot.registerModule(audio)

export default {
  components: { Editor, Toolbar, editChapter, Read },
  data () {
    return {
      slateTransforms: SlateTransforms,
      preShow: false,
      editor: null,
      chapterShow: true,
      loading: false,
      html: '<p><br></p>',
      newhtml: '',
      toolbarConfig: {
        toolbarKeys: [
          'headerSelect',
          // 'blockquote',
          '|',
          'bold',
          'underline',
          'italic',
          'through',
          // 'code',
          'sup',
          'sub',
          'clearStyle',
          'color',
          'bgColor',
          '|',
          'fontSize',
          'fontFamily',
          'lineHeight',
          '|',
          'bulletedList',
          'numberedList',
          'todo',
          {
            key: 'group-justify',
            title: '对齐',
            iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>', // 可选
            menuKeys: ['justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify']
          },
          {
            key: 'group-indent',
            title: '缩进',
            iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>', // 可选
            menuKeys: ['indent', 'delIndent']
          },
          '|',
          // 'emotion',
          // 'insertLink',
          'uploadImage',
          'uploadVideo',
          'insertTable',
          // 'codeBlock',
          'divider',
          '|',
          'undo',
          'redo'

        ],
        insertKeys: {
          index: 24,
          keys: ['uploadAudio'] // show menu in toolbar
        }
      },
      editorConfig: {
        scroll: false,
        MENU_CONF: {
          uploadImage: {
            // 自定义上传
            async customUpload (file, insertFn) {
              const { data } = await getFileUploadAuthor({
                mediaType: 'IMAGE',
                contentType: '',
                quantity: 1,
                fileName: file.name
              })
              const ossCDN = data[0].ossConfig.ossCDN
              try {
                const formData = new FormData()
                formData.append('success_action_status', '200')
                formData.append('callback', '')
                formData.append('key', data[0].fileName)
                formData.append('policy', data[0].policy)
                formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
                formData.append('signature', data[0].signature)
                formData.append('file', file)
                const notif = Notification({
                  title: `${file.name}上传中`,
                  message: '0%',
                  duration: 0
                })
                await axios.post(data[0].ossConfig.host, formData, {
                  onUploadProgress: (progress) => {
                    const complete = Math.floor(progress.loaded / progress.total * 100)
                    notif.message = complete + '%'
                    console.log(complete)
                    if (complete >= 100) {
                      notif.close()
                    }
                  }
                })
                // console.log(`${ossCDN}/${data[0].fileName}`)
                insertFn(`${ossCDN}/${data[0].fileName}`, data[0].fileName, '')
              } catch (error) {
                console.log(error)
              }
            }
          },
          uploadVideo: {
            // 自定义上传
            async customUpload (file, insertFn) {
              const { data } = await getFileUploadAuthor({
                mediaType: 'VIDEO',
                contentType: '',
                quantity: 1,
                fileName: file.name
              })
              const ossCDN = data[0].ossConfig.ossCDN
              try {
                const formData = new FormData()
                formData.append('success_action_status', '200')
                formData.append('callback', '')
                formData.append('key', data[0].fileName)
                formData.append('policy', data[0].policy)
                formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
                formData.append('signature', data[0].signature)
                formData.append('file', file)

                const notif = Notification({
                  title: `${file.name}上传中`,
                  message: '0%',
                  duration: 0
                })
                await axios.post(data[0].ossConfig.host, formData, {
                  onUploadProgress: (progress) => {
                    const complete = Math.floor(progress.loaded / progress.total * 100)
                    notif.message = complete + '%'
                    console.log(complete)
                    if (complete >= 100) {
                      notif.close()
                    }
                  }
                })
                insertFn(`${ossCDN}/${data[0].fileName}`, `${ossCDN}/${data[0].fileName}?x-oss-process=video/snapshot,f_png,w_300,t_0`)
              } catch (error) {
                console.log(error)
              }
            },
            onProgress (progress) {
              // progress 是 0-100 的数字
              console.log('progress', progress)
            }
          }
        }
      },
      mode: 'default', // or 'simple'
      id: 1000,
      bookId: 0,
      treeData: null,
      bookTitle: '',
      bookTree: [],
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      currChapter: null,
      editChapterShow: false,
      selectTreeId: 0,
      preNode: null,
      token: '',
      saveTimer: null,
      autoTips: false,
      otherUse: false,
      keepHeartTime: null,
      contentChanged: false
    }
  },
  mounted () {
    this.token = getToken()
    this.token = `Bearer ${this.$route.query && this.$route.query.token}`
    this.bookId = this.$route.query && this.$route.query.id
    // this._takeBook()
    this._getBook()
    this._getBookCatalogue()
    window.addEventListener('beforeunload', e => this.beforeunloadFn(e))
  },
  async beforeDestroy () {
    const editor = this.editor
    if (editor == null) return
    await this.saveHtml()
    if (this.keepHeartTime) {
      clearInterval(this.keepHeartTime)
    }
  },
  methods: {
    async _takeBook () {
      // 判断是否有人正在使用该书
      const { data } = await takeBook({
        bookId: this.bookId,
        scene: 'BOOK_CATALOGUE_OWN',
        catalogueId: this.selectTreeId
      }, {
        authorization: this.token
      })
      if (!data) {
        this.otherUse = true
        this.editor.blur()
      } else {
        if (this.otherUse) {
          this._getContent(this.selectTreeId)
        }
        this.otherUse = false
        this.editor.enable()
      }
    },
    async _getBook () {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN'
        }, {
          authorization: this.token
        })
        this.bookTitle = data.title
      }
    },
    async _getBookCatalogue () {
      const { data } = await getBookCatalogue({
        bookId: this.bookId,
        type: 'CHAPTER'
      }, {
        authorization: this.token
      })
      this.bookTree = data
    },
    onCreated (editor) {
      // window.editor = this.editor
      // window.slateTransforms = this.slateTransforms
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      if (!this.selectTreeId) {
        editor.disable()
      }
    },
    getHtml () {
      return this.editor.getHtml()
    },
    async handleNodeClick (nodeData) {
      // if (this.editor.isDisabled()) {
      //   this.editor.enable()
      // }
      // this.editor.blur()
      // this.resetHtml()
      // if (this.contentChanged) {
      //   // 切换章节的时候  保存一次编辑的内容
      //   await this.saveHtml()
      // }
      await this.saveHtml()
      this.loading = true
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
      this.preNode = nodeData
      this.selectTreeId = nodeData.id
      // 判断是否加锁
      if (this.keepHeartTime) {
        clearInterval(this.keepHeartTime)
      }
      this._takeBook()
      this.keepHeartTime = setInterval(() => {
        this._takeBook()
      }, 10000)
      // 重置html内容避免编辑器报错
      // this.loading = true
      this.catalogueId = 0
      this.htmlId = 0
      this.html = '<p><br></p>'
      this.htmlTemp = null
      setTimeout(async () => {
        await this._getContent(nodeData.id)
        this.loading = false
        this.contentChanged = false
      }, 200)
    },
    async _getContent (id) {
      if (this.selectTreeId === id) {
        const { data } = await getContent({
          catalogueId: id
        }, {
          authorization: this.token
        })
        if (data && data.length > 0) {
          this.htmlId = data[0].id
          this.html = data[0].data
          // this.editor.setHtml(data[0].data)
        } else {
          this.htmlId = 0
          this.html = '<p><br></p>'
          // this.editor.setHtml('<p>编辑器创建时的默认内容。</p>')
        }
        this.catalogueId = id
        this.htmlTemp = this.html
      }
    },
    handleDragStart (node, ev) {
      console.log('drag start', node)
    },
    async handleDrop (draggingNode, dropNode, dropType, ev) {
      // console.log('tree drop: ', draggingNode.data, dropNode.data, dropType)
      await dragCatalogue({
        catalogueId: draggingNode.data.id,
        referCatalogueId: dropNode.data.id,
        position: dropType.toUpperCase()
      }, {
        authorization: this.token
      })
      this._getBookCatalogue()
    },
    handleEdit () {
      if (!this.editor.isFocused() && this.selectTreeId) {
        this.editor.blur()
        this.editor.focus(true)
      }
    },
    eidtDone (data) {
      this.editChapterShow = false
      this._getBookCatalogue()
      // if (this.currChapter.data) {
      //   // 编辑
      //   this.treeData.title = data.title
      //   // this._getContent(this.treeData.id)
      // } else {
      //   // 新增
      //   if (!this.currChapter.parentId) {
      //     // 根目录
      //     this.bookTree.push(data)
      //   } else {
      //     const newChild = data
      //     this.treeData.childCatalogue.push(newChild)
      //   }
      // }
    },
    treeAppend (node, data) {
      this.treeData = data
      if (node) {
        this.currChapter = { bookId: this.bookId, parentId: data.id, data: null }
      } else {
        this.currChapter = { bookId: this.bookId, parentId: 0, data: null }
      }
      this.editChapterShow = true
    },
    treeEdit (node, data) {
      this.treeData = data
      this.currChapter = { bookId: this.bookId, parentId: data.parentId, data }
      this.editChapterShow = true
    },
    async treeRemove (node, data) {
      try {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await deleteBookCatalogue({
            catalogueId: data.id
          }, {
            authorization: this.token
          })
          this._getBookCatalogue()
          // const parent = node.parent
          // const children = parent.data.childCatalogue || parent.data
          // const index = children.findIndex(d => d.id === data.id)
          // children.splice(index, 1)
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    async saveHtml (type = false) {
      if (this.selectTreeId && !this.loading && this.catalogueId !== 0 && !this.otherUse) {
        const obj = {
          data: this.getHtml(),
          catalogueId: this.selectTreeId
        }
        if (obj.data === this.htmlTemp) {
          console.log('文本内容一样，不提交')
          return
        }
        if (this.htmlId) {
          obj.id = this.htmlId
        }
        const { data } = await saveContent(obj, {
          authorization: this.token
        })
        if (type) {
          this.$message.success('保存成功')
        }
        this.htmlTemp = obj.data
        this.htmlId = data
      }
    },
    onFocus (editor) {
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
      this.saveTimer = setInterval(async () => {
        if (!this.contentChanged) return
        if (!this.editor.isFocused()) {
          clearInterval(this.saveTimer)
          this.saveTimer = null
          return
        }
        this.autoTips = true
        await this.saveHtml()
        this.autoTips = false
        this.contentChanged = false
      }, 5000)
    },
    onBlur (editor) {
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
    },
    onChange (editor) {
      this.contentChanged = true
    },
    resetHtml () {
      // 使用原生slate删除表格内容
      const editor = this.editor
      editor.children.map(item => {
        this.slateTransforms.delete(editor, { at: [0] })
      })

      // reset init
      editor.children = [
        {
          type: 'p',
          children: [{ text: '' }]
        }]
    },
    beforeunloadFn (e) {
      if (this.getHtml() !== this.htmlTemp) {
        this.saveHtml()
        // console.log("文本内容一样，不提交")
        e.returnValue = ('确定离开当前页面吗？')
        return
      }
    },
    preShowFn () {
      this.preShow = true
      this.saveHtml()
    }
  }
}
</script>
<style lang="scss" scoped>
.editor-dig {
  width: 100%;
  height: 100%;
  background: #FFF;
  box-sizing: border-box;
  position: relative;

  .other-use {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: #000000, $alpha: .4);
    z-index: 999999;
  }

  .dig-head {
    width: 100%;
    min-width: 1060px;
    overflow-x: auto;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    box-sizing: border-box;

    .dig-tips {
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .dig-btn {
      border-radius: 4px;
      background: #2F80ED;
      color: #FFF;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 5px 10px;
      box-sizing: border-box;
      cursor: pointer;
      margin-left: 10px;
    }
  }

  .dig-box {
    width: 100%;
    min-width: 1060px;
    height: calc(100% - 50px);
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #E0E0E0;
    background: #F9F9F9;
    overflow: hidden;
  }

  .dig-left {
    width: 260px;
    height: 100%;
    background: #fff;
    border-right: 1px solid #E0E0E0;

    .chapter-title {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 5px;

      .icon1 {
        color: #000;
        font-size: 16px;
        display: flex;
        align-items: center;

        img {
          width: 27px;
          height: 27px;
          margin-right: 5px;
        }
      }

      .add-btn {
        color: #2F80ED;
        font-size: 14px;
        margin-right: 10px;
        cursor: pointer;
      }
    }

    .chapter-body {
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      ::v-deep .el-tree-node__content {
        height: 40px;
      }

      ::v-deep .el-button + .el-button {
        margin-left: 5px;
      }

      ::v-deep .el-button {
        font-size: 14px;
      }

      ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
        padding: 5px;
      }

      .tree-body {
        width: calc(100% - 30px);
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;

        .chapter-name {
          flex: 1;
          overflow: hidden;
          @include scrollBar;
        }

        .chapter-option {
          flex-shrink: 0;
        }
      }
    }
  }

  .dig-center {
    width: 793.667px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #FFF;
    position: relative;

    .toolbar {
      width: 793.667px;
      height: 80px;
    }

    .eidtor {
      width: 793.667px;
      padding: 25px;
      overflow-y: auto;
    }
  }

  ::v-deep .w-e-bar-divider {
    display: none;
  }

  ::v-deep .w-e-textarea-video-container {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;

    video {
      width: 100%;
    }
  }

  .pre {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 9999999;
    overflow: auto;
  }
}
</style>
