<template>
  <div class="statis-box">
    <div class="preview-content">
      <div class="preview-content-head">
        <div class="title">学习统计</div>
        <div @click.stop="$emit('close')">
          <i class="el-icon-close pointer"></i>
        </div>
      </div>
      <div class="preview-content-content">
        <div class="left">
          <div class="book-img">
            <div class="r-container">
              <div class="img-box">
                <img
                  v-if="bookInfo && bookInfo.digitalBook.cover"
                  :src="bookInfo.digitalBook.cover"
                />
                <img v-else src="@/assets/images/default-cover.jpg" />
              </div>
            </div>
          </div>
          <div class="s-title">
            {{ bookInfo && bookInfo.digitalBook.title }}
          </div>
          <div class="s-des">{{ bookInfo && bookInfo.digitalBook.author }}</div>
        </div>
        <div class="right">
          <div v-if="activeIndex === 0" class="task-content">
            <div class="table-head">
              <div>学生姓名</div>
              <div
                v-if="userList && userList[0] && userList[0].testPaperProgress"
              >
                互动答题
              </div>
              <div
                v-if="userList && userList[0] && userList[0].trainingProgress"
              >
                实训
              </div>
              <div
                v-if="
                  userList && userList[0] && userList[0].commTrainingProgress
                "
              >
                实训
                <span
                  class="export-btn"
                  @click="exportTrainingScore"
                >导出成绩</span>
              </div>
              <div>阅读学习进度</div>
              <div>状态</div>
            </div>
            <div
              v-for="item in userList"
              :key="item.user.id"
              class="table-body pointer"
            >
              <div>{{ item.user.displayName || item.user.mobile }}</div>
              <div
                v-if="userList && userList[0].testPaperProgress"
                class="button"
                @click="hanledUserTest(item)"
              >
                {{ item.testPaperProgress || '未配置' }}
              </div>
              <div
                v-if="userList && userList[0].trainingProgress"
                class="button"
                @click="hanledUserTraing(item)"
              >
                {{ item.trainingProgress || '未配置' }}
              </div>
              <div
                v-if="userList && userList[0].commTrainingProgress"
                class="button"
                @click="hanledUserTraingAi(item)"
              >
                {{ item.commTrainingProgress || '未配置' }}
              </div>
              <div class="button" @click="hanledUser(item)">
                {{ item.digitalBookProgress }}%
              </div>
              <div>
                <span
                  v-if="item.progressStatus !== 'GOING'"
                  class="done"
                >已完成</span>
                <span v-else class="undone">未完成</span>
              </div>
            </div>
            <div
              v-if="userList && userList.length === 0"
              class="w"
              style="height: 40vh"
            >
              <Empty :msg="'暂无数据'" />
            </div>
          </div>
          <div v-if="activeIndex === 1" class="w h">
            <div class="w name-head">
              <p class="title">阅读统计</p>
              <p class="name">{{ selectName }}</p>
              <div class="back" @click="handleBack">
                <i class="el-icon-arrow-left"></i>
              </div>
            </div>
            <!-- <div class="static-head">
              <div> <span style="font-weight: 500;margin-right: .5vh;">学习进度</span> {{ selectProgress }}%</div>
              <div> <span style="font-weight: 500;margin-right: .5vh;">状态</span> {{ selectProgress >= 100 ? '已完成' : '未完成' }}</div>
            </div> -->
            <div class="static-body">
              <div class="task-content">
                <div class="table-head">
                  <div>目录</div>
                  <div>阅读进度</div>
                  <div>状态</div>
                </div>
                <div
                  v-for="item in userTreeProgress"
                  :key="item.id"
                  class="table-body pointer"
                >
                  <div style="justify-content: flex-start; padding-left: 2rem">
                    {{ item.title }}
                  </div>
                  <div>
                    {{
                      (item &&
                        item.userDigitalCatalogue &&
                        item.userDigitalCatalogue.progress) ||
                        0
                    }}%
                  </div>
                  <div>
                    <span
                      v-if="
                        item &&
                          item.userDigitalCatalogue &&
                          item.userDigitalCatalogue.progress >= 100
                      "
                      class="done"
                    >已完成</span>
                    <span v-else class="undone">未完成</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="activeIndex === 2" class="w h">
            <div class="w name-head">
              <p class="title">实训统计</p>
              <p class="name">{{ selectName }}</p>
              <div class="back" @click="handleBack">
                <i class="el-icon-arrow-left"></i>
              </div>
            </div>
            <div class="setp_content">
              <div
                v-for="(item, index) in traningData"
                :key="index"
                class="step_item"
              >
                <p class="title">
                  <span>{{ index + 1 }}.</span>
                  {{ item.trainingName }}
                </p>
                <p class="label">实验结果</p>
                <div class="content">
                  <p>
                    验证总数：<span>{{
                      item.userTrainingData
                        ? item.userTrainingData.total || '未配置'
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    正确：<span>{{
                      item.userTrainingData
                        ? item.userTrainingData.right === null
                          ? '-'
                          : item.userTrainingData.right
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    错误：<span>{{
                      item.userTrainingData
                        ? item.userTrainingData.wrong === null
                          ? '-'
                          : item.userTrainingData.wrong
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    未完成：<span>{{
                      item.userTrainingData
                        ? item.userTrainingData.unCompleteLabel === null
                          ? '-'
                          : item.userTrainingData.unCompleteLabel
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    实验步骤：<span>{{
                      item.userTrainingData
                        ? item.userTrainingData.userCompleteStep || '0'
                        : '未配置'
                    }}{{ item.userTrainingData ? '/' : ''
                    }}<span>{{
                      item.userTrainingData
                        ? item.userTrainingData.totalStep || '0'
                        : ''
                    }}</span></span>
                  </p>
                  <p>
                    总分：<span>{{
                      item.userTrainingData
                        ? item.userTrainingData.scoreTotal || '未配置'
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    得分：<span>{{
                      item.userTrainingData
                        ? item.userTrainingData.userScore === null
                          ? '-'
                          : item.userTrainingData.userScore
                        : '未配置'
                    }}</span>
                  </p>
                </div>
                <div
                  class="tag"
                  :class="
                    item.userTrainingData &&
                      item.userTrainingData.progressStatus !== 'GOING'
                      ? 'done'
                      : 'undone'
                  "
                >
                  {{
                    item.userTrainingData &&
                      item.userTrainingData.progressStatus !== 'GOING'
                      ? '已完成'
                      : '未完成'
                  }}
                </div>
              </div>
              <div
                v-if="userList && traningData.length === 0"
                class="w"
                style="height: 40vh"
              >
                <Empty :msg="'暂无数据'" />
              </div>
            </div>
          </div>
          <div v-if="activeIndex === 3" class="w h">
            <div class="w name-head">
              <p class="title">答题统计</p>
              <p class="name">{{ selectName }}</p>
              <div class="back" @click="handleBack">
                <i class="el-icon-arrow-left"></i>
              </div>
            </div>
            <div class="setp_content">
              <div
                v-for="(item, index) in testData"
                :key="index"
                class="step_item"
              >
                <p class="title">
                  <span>{{ index + 1 }}.</span>
                  {{ item.title }}
                </p>
                <p class="label">分数情况</p>
                <div class="content">
                  <p>
                    答题总数：<span>{{
                      item.userTestpaper
                        ? item.userTestpaper.totalQuantity + '道'
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    答对：<span>{{
                      item.userTestpaper
                        ? item.userTestpaper.rightQuantity + '道'
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    答错：<span>{{
                      item.userTestpaper
                        ? item.userTestpaper.wrongQuantity + '道'
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    总分：<span>{{
                      item.userTestpaper
                        ? item.userTestpaper.totalScore
                          ? item.userTestpaper.totalScore + '分'
                          : '未配置'
                        : '未配置'
                    }}</span>
                  </p>
                  <p>
                    得分：<span>{{
                      item.userTestpaper
                        ? item.userTestpaper.totalScore
                          ? item.userTestpaper.score + '分'
                          : '-'
                        : '未配置'
                    }}</span>
                  </p>
                </div>
                <div
                  class="tag"
                  :class="
                    item.userTestpaper &&
                      item.userTestpaper.progressStatus !== 'COMING'
                      ? 'done'
                      : 'undone'
                  "
                >
                  {{
                    item.userTestpaper &&
                      item.userTestpaper.progressStatus !== 'COMING'
                      ? '已完成'
                      : '未完成'
                  }}
                </div>
              </div>
              <div
                v-if="userList && testData.length === 0"
                class="w"
                style="height: 40vh"
              >
                <Empty :msg="'暂无数据'" />
              </div>
            </div>
          </div>
          <div v-if="activeIndex === 4" class="w h">
            <div class="w name-head">
              <p class="title">实训统计</p>
              <p class="name">{{ selectName }}</p>
              <div class="back" @click="handleBack">
                <i class="el-icon-arrow-left"></i>
              </div>
            </div>
            <div class="setp_content">
              <div
                v-for="(item, index) in traningData"
                :key="index"
                class="step_item"
              >
                <p class="title">
                  <span>{{ index + 1 }}.</span>
                  {{ item.trainingName }}
                </p>
                <p class="label">
                  实训结果：
                  <span class="result-link" @click="handleTraningPop(item)">查看实训结果</span>
                </p>
                <p class="label-score">
                  <span
                    class="score"
                  >分数：{{
                    item.userTrainingData.userScore!==null? item.userTrainingData.userScore
                      +'分'
                    : '-'
                  }}</span>
                  <span v-if="selectUserId !== id" class="score-unit" @click="handleScore(item)">评分</span>
                  <span
                    :class="
                      item.userTrainingData.progressStatus === 'GOING'
                        ? 'undone'
                        : 'done'
                    "
                  >{{
                    item.userTrainingData.progressStatus === 'GOING'
                      ? '未完成'
                      : '已完成'
                  }}</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <TraningPop :visible="traningPopShow" :file-list="fileList" @closeTraningPop="traningPopShow=false" />
    <ScorePop
      :visible="scoreShow"
      :title="'评分'"
      @closeDialog="scoreShow = false"
      @confirm="handleScoreConfirm"
    />
  </div>
</template>

<script>
import {
  getDigitalBookProgress,
  getUserDigitalBookProgress
} from '@/api/digital-api.js'
import { getUserBookTrainingData, updateTrainingUserScore } from '@/api/training-api.js'
import { getUserBookTestpaperData } from '@/api/test-api.js'
import Empty from '@/components/classPro/Empty/index.vue'
import TraningPop from './component/traningPop.vue'
import ScorePop from './component/scorePop.vue'
import { mapGetters } from 'vuex'
export default {
  components: {
    Empty,
    TraningPop,
    ScorePop
  },
  props: {
    bookInfo: {
      type: [Array, Object],
      default: () => {
        return null
      }
    }
  },
  data() {
    return {
      userList: [],
      activeIndex: 0,
      userTreeProgress: [],
      selectProgress: '',
      selectName: '',
      traningData: [],
      testData: [],
      traningPopShow: false,
      fileList: [],
      scoreItem: null,
      scoreShow: false
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  mounted() {
    this._getDigitalBookProgress()
  },

  methods: {
    handleScore(item) {
      this.scoreItem = item
      this.scoreShow = true
    },
    async handleScoreConfirm(score) {
      await updateTrainingUserScore({
        studentCourseId: this.bookInfo.id,
        userId: this.selectUserId,
        trainingId: this.scoreItem.trainingId,
        userScore: score
      })
      this.scoreShow = false
      this.$message.success('评分成功')
      await this._getUserBookTrainingData(this.selectUserId, 4)
    },
    handleTraningPop(item) {
      this.traningPopShow = true
      this.fileList = item.userTrainingData.resultFileList || []
    },
    async exportTrainingScore() {
      this.$message.info('敬请期待')
    },
    async _getDigitalBookProgress() {
      const { data } = await getDigitalBookProgress({
        studentCourseId: this.bookInfo.id
      })
      this.userList = data
    },
    async _getUserBookTrainingData(id, index) {
      const { data } = await getUserBookTrainingData({
        studentCourseId: this.bookInfo.id,
        userId: id,
        trainingType: index === 4 ? 'COMM_PRACTICE' : 'FINACE_PRACTICE'
      })
      this.activeIndex = index
      this.traningData = data || []
    },
    async _getUserBookTestpaperData(id) {
      const { data } = await getUserBookTestpaperData({
        bookId: this.bookInfo.digitalBook.id,
        userId: id
      })
      this.activeIndex = 3
      this.testData = data || []
    },
    async _getUserDigitalBookProgress(id) {
      const { data } = await getUserDigitalBookProgress({
        studentCourseId: this.bookInfo.id,
        userId: id
      })
      this.activeIndex = 1
      this.userTreeProgress = data
    },
    async hanledUser(item) {
      this.selectName = item.user.displayName || item.user.mobile
      this.selectProgress = item.digitalBookProgress
      await this._getUserDigitalBookProgress(item.user.id)
    },
    async hanledUserTraingAi(item) {
      this.selectName = item.user.displayName || item.user.mobile
      this.selectProgress = item.digitalBookProgress
      this.selectUserId = item.user.id
      await this._getUserBookTrainingData(item.user.id, 4)
    },
    async hanledUserTraing(item) {
      this.selectName = item.user.displayName || item.user.mobile
      this.selectProgress = item.digitalBookProgress
      await this._getUserBookTrainingData(item.user.id, 2)
    },
    async hanledUserTest(item) {
      this.selectName = item.user.displayName || item.user.mobile
      this.selectProgress = item.digitalBookProgress
      await this._getUserBookTestpaperData(item.user.id)
    },
    handleBack() {
      this.activeIndex = 0
      this.userTreeProgress = []
      this.selectName = ''
      this.selectProgress = ''
      this.traningData = []
    }
  }
}
</script>

<style lang="scss" scoped>
.statis-box {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 80px;
  box-sizing: border-box;
  background: rgba($color: #000000, $alpha: 0.5);
  z-index: 9;

  .preview-content {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
    @include scrollBar;

    .preview-content-head {
      height: 40px;
      display: flex;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: 500;
      }

      .el-icon-close {
        font-size: var(--font-size-XXL);
      }
    }

    .preview-content-content {
      height: calc(100% - 40px);
      display: flex;

      .left {
        width: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-right: 1px solid #e9e9e9;
        box-sizing: border-box;

        .s-title {
          color: #000;
          font-size: 12px;
          font-weight: 500;
          margin-top: 30px;
        }

        .s-des {
          font-size: 10px;
          color: #000;
          margin-top: 20px;
        }

        .book-img {
          width: 110px;
        }

        .r-container {
          position: relative;
          min-height: 0;
          padding-bottom: 133.33%;

          .img-box {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }

      .right {
        width: calc(100% - 300px);
        padding: 0 0 0 10px;
        .setp_content {
          width: 100%;
          height: calc(100% - 40px);
          overflow-y: auto;
          @include scrollBar;
        }
        .step_item {
          width: 100%;
          height: 100px;
          border-bottom: 1px solid #e9e9e9;
          position: relative;
          .title {
            font-size: 12px;
            font-weight: 500;
          }
          .label {
            position: absolute;
            left: 0px;
            top: 30px;
            font-size: 10px;
            font-weight: 500;
            color: #4f4f4f;
          }
          .label-score {
            position: absolute;
            right: 100px;
            top: 30px;
            font-size: 10px;
            font-weight: 500;
            .score {
              color: #333;
              margin-left: auto;
              padding-right: 20px;
            }
            .score-unit {
              color: #1890ff;
              cursor: pointer;
              text-decoration: underline;
              padding-right: 20px;
            }
          }
          .content {
            width: 400px;
            height: 60px;
            position: absolute;
            left: 100px;
            font-size: 10px;
            top: 30px;
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            p {
              font-size: 10px;
              white-space: nowrap;
              margin-left: 30px;
              span {
                color: #2f80ed;
              }
            }
          }
          .tag {
            position: absolute;
            right: 30px;
            top: 30px;
            font-size: 12px;
          }
          .done {
            color: #27ae60;
          }
          .undone {
            color: #eb5757;
          }
        }
        .task-content {
          width: 100%;
          height: calc(100%);
          overflow-y: auto;
          @include scrollBar;

          .table-head,
          .table-body {
            display: flex;
            padding: 0 10px;
            .button {
              color: #2f80ed;
              text-decoration: underline;
            }
            .export-btn {
              color: #2f80ed;
              text-decoration: underline;
              cursor: pointer;
              padding-left: 5px;
            }
          }

          .table-head > div,
          .table-body > div {
            width: 33.3333333%;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 40px;
            font-size: 14px;

            .done,
            .undone {
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 8px 15px;
              font-size: 14px;
              border-radius: 5px;
            }

            .done {
              background: #ceffe2;
              color: #219653;
            }

            .undone {
              background: #ffc1c1;
              color: #eb5757;
            }

            .classpro-btn,
            .classpro-btn-disable {
              padding: 5px 10px;
            }
          }

          .table-head > div {
            font-weight: 500;
          }
        }

        .name-head {
          font-size: 16px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          position: relative;
          border-bottom: 1px solid #e9e9e9;
          .title {
            position: absolute;
            left: 20px;
            top: -10px;
            font-size: 14px;
          }
          .name {
            color: #007abf;
            position: absolute;
            left: 100px;
            top: -10px;
            font-size: 14px;
          }
          .back {
            position: absolute;
            font-size: 18px;
            left: 0;
            top: 3px;
            cursor: pointer;
            padding: 0 10px 0 0;
          }
        }
        .static-head {
          font-size: 14px;
          height: 40px;
          display: flex;
          justify-content: space-around;
          align-items: flex-start;
        }

        .static-body {
          height: calc(100% - 40px);
          // background: #F8F8F8;
        }
      }
    }
  }
}

.label {
  display: flex;
  align-items: center;

  .result-link {
    color: #1890ff;
    cursor: pointer;
    text-decoration: underline;
  }
  .undone,
  .done {
    margin-left: 8px;
  }

  .undone {
    color: #ff4d4f;
  }

  .done {
    color: #52c41a;
  }
}
</style>
