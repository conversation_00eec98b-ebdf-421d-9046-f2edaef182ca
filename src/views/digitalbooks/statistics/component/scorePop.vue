<template>
  <NormalDialog
    width="600px"
    :title="title"
    :dialog-visible="visible"
    :is-center="true"
    :append-to-body="true"
    @closeDialog="closeDialog"
  >
    <div class="score-content">
      <div class="score-input">
        <span class="label">实训分数：</span>
        <el-input
          v-model="score"
          placeholder="请输入实训分数"
          type="number"
          :min="0"
          :max="100"
        />
      </div>
      <div class="btn-group">
        <el-button class="submit-btn" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'

export default {
  name: 'ScorePop',
  components: {
    NormalDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '评分'
    }
  },
  data() {
    return {
      score: ''
    }
  },
  methods: {
    closeDialog() {
      this.score = ''
      this.$emit('closeDialog')
    },
    handleConfirm() {
      if (!this.score) {
        this.$message.warning('请输入分数')
        return
      }
      if (this.score < 0 || this.score > 100) {
        this.$message.warning('分数范围为0-100')
        return
      }
      this.$emit('confirm', this.score)
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.score-content {
  padding: 20px;

  .score-input {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .label {
      width: 80px;
      font-size: 14px;
      color: #333;
    }

    .el-input {
      width: 200px;
    }
  }

  .btn-group {
    display: flex;
    justify-content: center;

    .submit-btn {
      width: 100px;
      padding: 5px;
    }
  }
}

// 添加全局样式来提高弹窗层级
::v-deep .el-dialog__wrapper {
  z-index: 3000 !important;
}

::v-deep .v-modal {
  z-index: 2999 !important;
}
</style>
