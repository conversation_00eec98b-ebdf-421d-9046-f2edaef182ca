<template>
  <div>
    <NormalDialog
      width="700px"
      title="实训结果"
      :dialog-visible="visible"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="closeDialog"
    >
      <div class="traning-result">
        <Empty v-if="fileList.length===0" />
        <div v-for="(item, index) in fileList" :key="index" class="file-item">
          <div class="file-info">
            <span class="file-name">{{ item.fileName ? item.fileName + '.' + item.expendType : item.name }}</span>
            <span class="file-size">{{ formatFileSize(item.size) }}</span>
          </div>
          <div class="file-actions">
            <span class="action preview" @click="previewFile(item)">预览</span>
            <span class="action download" @click="downloadFile(item)">下载</span>
          </div>
        </div>
      </div>
    </NormalDialog>
    <imgGroupPop ref="imgs" :info="imgListInfo" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
  </div>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import imgGroupPop from '../../editor/components/imgGroupPop.vue'
import officeView from '../../editor/components/officeView.vue'
import videoCardPop from '../../editor/components/videoPop.vue'
import { getToken } from '@/utils/auth'
import { Notification } from 'element-ui'
import { saveAs } from 'file-saver'
import { throttle } from '@/utils'
import Empty from '@/components/classPro/Empty/index.vue'
export default {
  name: 'TraningPop',
  components: {
    NormalDialog,
    imgGroupPop,
    officeView,
    videoCardPop,
    Empty
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    fileList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      imgListInfo: null,
      officeUrl: '',
      token: '',
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      }
    }
  },
  mounted() {
    this.token = this.$route.query && this.$route.query.token ? `Bearer ${this.$route.query.token}` : getToken()
  },
  methods: {
    closeDialog() {
      this.$emit('closeTraningPop')
    },
    formatFileSize(size) {
      if (size < 1024) return size + 'B'
      if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
      return (size / (1024 * 1024)).toFixed(1) + 'MB'
    },
    previewFile(file) {
      let url = file.url
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = `https://${url}`
      }

      const fileName = file.fileName ? `${file.fileName}.${file.expendType}` : file.name
      const fileType = fileName.toLowerCase()

      // 检查文件类型
      if (fileType.endsWith('.doc') || fileType.endsWith('.docx') ||
          fileType.endsWith('.xls') || fileType.endsWith('.xlsx') ||
          fileType.endsWith('.ppt') || fileType.endsWith('.pptx') ||
          fileType.endsWith('.pdf')) {
        // Office 文件预览
        this.officeUrl = url
        this.$refs.officeView.open()
      } else if (fileType.endsWith('.jpg') || fileType.endsWith('.jpeg') ||
                 fileType.endsWith('.png') || fileType.endsWith('.gif')) {
        // 图片预览
        this.imgListInfo = {
          content: [{
            src: url,
            info: fileName
          }]
        }
        this.$refs.imgs.open()
      } else if (fileType.endsWith('.mp4') || fileType.endsWith('.webm') ||
                 fileType.endsWith('.ogg')) {
        // 视频预览
        this.videoInfo = {
          src: url,
          poster: '', // 可选的视频封面图
          text: fileName
        }
        this.$refs.videoCard.open()
      } else {
        this.$message.warning('暂不支持预览该文件类型')
      }
    },
    downloadFile(file) {
      const url = file.url
      const fileName = file.fileName ? `${file.fileName}.${file.expendType}` : file.name

      const notif = Notification({
        title: fileName,
        dangerouslyUseHTMLString: true,
        message: '',
        duration: 0
      })

      throttle(function () {
        const xhr = new XMLHttpRequest()
        xhr.open('get', url)
        xhr.responseType = 'blob'
        xhr.addEventListener('progress', (e) => {
          const complete = Math.floor(e.loaded / e.total * 100)
          notif.message = complete + '%'
          if (complete >= 100) {
            notif.close()
          }
        })
        xhr.send()
        xhr.onload = function () {
          if (this.status === 200 || this.status === 304) {
            const blob = new Blob([this.response], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
            })
            saveAs(blob, fileName)
          }
        }
      }, 2000)
    }
  }
}
</script>

<style lang="scss" scoped>
.traning-result {
  padding: 10px 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  @include scrollBar;
  .file-item {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #EBEEF5;

    &:last-child {
      border-bottom: none;
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 20px;

      .file-name {
        color: #333;
        font-size: 14px;
        width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-size {
        color: #999;
        font-size: 14px;
      }
    }

    .file-actions {
      display: flex;
      gap: 16px;
      padding-right: 10px;
      .action {
        color: #1890ff;
        cursor: pointer;
        font-size: 10px;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
