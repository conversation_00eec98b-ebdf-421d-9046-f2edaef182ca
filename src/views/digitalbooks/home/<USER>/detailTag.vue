<template>
  <div class="my-course">
    <div class="tag-box flex">
      <div
        :class="tagIndex === 1 ? 'tag-item__active' : 'tag-item'"
        @click="changeTag(1)"
      >
        目录
      </div>
      <div
        :class="tagIndex === 2 ? 'tag-item__active' : 'tag-item'"
        @click="changeTag(2)"
      >
        简介
      </div>
      <div
        v-if="bookInfo&&bookInfo.publishingCertificate"
        :class="tagIndex === 3 ? 'tag-item__active' : 'tag-item'"
        @click="changeTag(3)"
      >
        证书
      </div>
    </div>
    <div class="my-course-body">
      <div v-if="tagIndex === 1" class="intro">
        <div v-if="catalogueList" style='width: 100%;height: 100%'>
<!--          <ul v-for="item in catalogueList" :key="item.id">-->
<!--            <digTree :model="item" :index="0" />-->
<!--          </ul>-->
          <el-tree
            ref="treeSelect"
            :data="treeList"
            :props="treeProps"
            node-key="id"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
          >
            <div slot-scope="{ node, data }" class="tree-body">
              <div class="chapter-name" :class="{'chapter-name-1' : data.depth === 0, 'chapter-name-3' : data.depth === 2}">
                <div :title="data.title" class="w article-singer-container">
                  {{ data.title }}
                </div>
              </div>
            </div>
          </el-tree>
        </div>
        <template v-if="catalogueList.length === 0">
          <div class="w h flex flex-col justify-center items-center">
            <img class="empty" src="@/assets/images/empty.png" />
            <div class="empty-text">暂无数据</div>
          </div>
        </template>
      </div>

      <template v-if="tagIndex === 2">
        <div class="intro">
          <template
            v-if="(bookInfo &&bookInfo.intro &&bookInfo.intro.split('\n').length > 0 )||bookInfo && bookInfo.introVideo
            "
          >
            <video v-if="bookInfo && bookInfo.introVideo" class="video_cover" :src="bookInfo && bookInfo.introVideo" controlsList="nodownload" controls="controls"></video>
            <p
              v-for="(item, index) in bookInfo.intro &&
                bookInfo.intro.split('\n')"
              :key="index"
              :style="item.charAt(0) === '#' ? 'font-weight: bold;' : ''"
            >
              {{ item.charAt(0) === '#' ? item.substring(1) : item || '' }}
            </p>
          </template>
          <template v-else>
            <div class="w h flex flex-col justify-center items-center">
              <img class="empty" src="@/assets/images/empty.png" />
              <div class="empty-text">暂无数据</div>
            </div>
          </template>
        </div>
      </template>
      <template v-if="tagIndex === 3">
        <div
          class=" certificate_img_content"
        >
          <img
            v-if="bookInfo.publishingCertificate"
            class="certificate_img"
            :src="
              bookInfo.publishingCertificate
            "
            alt=""
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import digTree from './digTree.vue'

export default {
  components: {
    digTree
  },
  props: {
    bookInfo: {
      type: Object,
      default: () => {
        return null
      }
    },
    catalogueList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      tagIndex: 1,
      introList: [],
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      treeList: []
    }
  },
  watch: {
    catalogueList: {
      handler (newVal) {
        this.treeList = this.addDepthToTree(newVal, 0)
      },
      immediate: true
    }
  },
  methods: {
    changeTag (index) {
      this.tagIndex = index
    },
    addDepthToTree (tree, currentDepth = 0) {
      // 确保输入是数组
      if (!Array.isArray(tree)) {
        return tree
      }
      // 遍历每个节点
      return tree.map(node => {
        // 为当前节点添加深度属性
        const nodeWithDepth = {
          ...node,
          depth: currentDepth
        }
        // 如果有子节点，递归处理并增加深度
        if (node.childCatalogue && Array.isArray(node.childCatalogue)) {
          nodeWithDepth.childCatalogue = this.addDepthToTree(node.childCatalogue, currentDepth + 1)
        }
        return nodeWithDepth
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.video_cover {
  width: 40%;
  display: block;
  margin:  0 auto;
  height: auto;
}
.my-course {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  border-radius: 0 0 10px 10px;

  .tag-box {
    width: 100%;
    height: 32px;

    .tag-item,
    .tag-item__active {
      border-radius: 8px 25px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: var(--font-size-L);
      height: 100%;
      width: 110px;
      cursor: pointer;
    }

    .tag-item {
      background: #f4f4f4;
      color: #717171;
    }

    .tag-item__active {
      background: #ffffff;
      font-weight: 500;
      color: #3479ff;
    }
  }
  .certificate_img_content {
    height: 100%;
    overflow-y: auto;
    line-height: 30px;
    font-size: var(--font-size-L);
    padding: 30px;
    @include scrollBar;
  }
  .certificate_img {
    width: 300px;
    display: block;
    margin: 0 auto;
    height: auto;
  }
  .my-course-body {
    height: calc(100% - 32px);
    background: #fff;
    border-radius: 0 10px 10px 10px;
    padding: 10px;
    box-sizing: border-box;
    overflow: hidden;

    .intro {
      height: 100%;
      overflow-y: auto;
      line-height: 30px;
      font-size: var(--font-size-L);
      //font-weight: 500;
      @include scrollBar;

      p {
        font-size: var(--font-size-L);
      }

      .empty {
        width: 80px;
        height: 80px;
        margin-bottom: 10px;
      }

      .empty-text {
        font-weight: 400;
        font-size: var(--font-size-L);
        color: #8c8c8c;
      }
    }
  }
}
.tree-body {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  .chapter-name {
    flex: 1;
    overflow: hidden;
    color: black;
    @include scrollBar;
  }
  .chapter-name-1 {
    font-weight: 800 !important;
    font-size: 14px !important;
  }
  .chapter-name-3 {
    color: #5c5a5a;
  }
}
::v-deep .el-tree-node__expand-icon{
  font-size: 18px;
  color: black;
}
::v-deep .el-tree-node__expand-icon.is-leaf{
  color: transparent !important;
}
</style>
