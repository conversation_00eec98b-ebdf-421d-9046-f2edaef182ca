<template>
  <div class="index-box">
    <div v-if="list && list.length !== 0" class="card-box">
      <div class="card-title-box">
        <div class="t-title">数字教材</div>
<!--        <img src="../../../assets/digitalbooks/home/<USER>" alt="" />-->
      </div>

      <!-- <div class="course-tags-box mt30">
        <div class="tags-tips">教学层次</div>
        <div class="tags-btn">全部</div>
        <div class="tags-btn active-btn">全部1</div>
      </div>
      <div class="course-tags-box">
        <div class="tags-tips">学科分类</div>
        <div class="tags-btn">全部</div>
        <div class="tags-btn active-btn">全部1</div>
      </div> -->

      <div class="w mt30">
        <div
          class="flex flex-wrap"
          style="margin-left: -10px; margin-right: -10px"
        >
          <div v-for="item in list" :key="item.id" class="course-box">
            <div
              class="w h pointer"
              @click="
                $router.push(
                  `/classpro/digitalbooks/detail?id=${item.id}&f=recom2`
                )
              "
            >
              <div class="r-container">
                <div class="img-box">
                  <!-- <img class="ronghe" src="../../../assets/images/ronghe.png" alt="" /> -->
                  <img v-if="item.cover" :src="item.cover" />
                  <img v-else src="../../../assets/images/default-cover.jpg" />
                </div>
              </div>
              <div class="course_title article-singer-container">
                <span v-if="item.digitalBookType === 'PAPER_DIGITAL'" class="ronghe-title">纸数融合</span>
                {{ item.title }}
              </div>
              <div class="course_des article-singer-container">
                {{ item.author }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="card-box w h">
      <div class="card-title-box">
        <div class="t-title">数字教材</div>
        <img src="../../../assets/digitalbooks/home/<USER>" alt="" />
      </div>
      <div class="empty-box">
        <img class="empty" src="@/assets/images/empty.png" alt="占位图2" />
        <div>暂无数字教材</div>
      </div>
    </div>
  </div>
</template>

<script>
import { bookList } from '@/api/digital-api.js'
export default {
  data () {
    return {
      list: []
    }
  },
  mounted () {
    this._bookList()
  },
  methods: {
    async _bookList () {
      const { data } = await bookList()
      console.log(data)
      this.list = data
    }
  }
}
</script>

<style lang="scss" scoped>
.index-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  @include scrollBar;
  background: #fff;
  border-radius: 10px;

  .empty-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: calc(100% - 50px);

    .empty {
      width: 100px;
      height: 100px;
      margin-bottom: 20px;
    }
  }

  .course-tags-box {
    width: 100%;
    padding: 0 0 15px 0;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .tags-tips {
      color: #000;
      font-weight: 500;
      font-size: var(--font-size-M);
      display: flex;
      min-width: 40px;
      justify-content: center;
      align-items: center;
      margin: 0 10px 10px 0;
    }

    .tags-btn {
      display: flex;
      min-width: 40px;
      padding: 5px 10px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      color: #000;
      font-size: var(--font-size-M);
      margin: 0 10px 10px 0;
      cursor: pointer;
    }

    .active-btn {
      background: #2d9cdb;
      border-radius: 5px;
      color: #fff;
    }
  }

  .course-box {
    margin-bottom: 20px;
    width: 20%;
    padding: 10px;
  }

  .mt30 {
    margin-top: 30px;
  }

  .card-box {
    padding: 5px 15px;
    margin-bottom: 10px;
    box-sizing: border-box;

    &:last-child {
      margin-bottom: 0;
    }

    .card-title-box {
      margin: 10px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      display: flex;
      justify-content: space-between;
      img {
        width: 80px;
      }
      .t-title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
      }
    }

    // 23:30 图片
    .r-container {
      position: relative;
      min-height: 0;
      padding-bottom: 133.33%;
      border-radius: 10px;

      .img-box {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 10px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .ronghe {
          position: absolute;
          top: 0;
          right: 0;
          width: 50px;
          height: 50px;
        }
      }
    }

    .course_title {
      color: #000;
      font-size: var(--font-size-L);
      margin: 10px 0;
      .ronghe-title {
        display: inline-block;
        background: linear-gradient(90deg, #00C6FB 0%, #005BEA 100%);
        color: #fff;
        font-size: var(--font-size-M);
        padding: 3px 2px;
        border-radius: 4px;
      }
    }

    .course_des {
      color: #4f4f4f;
      font-size: var(--font-size-M);
      margin-bottom: 10px;
    }
  }
}
</style>
