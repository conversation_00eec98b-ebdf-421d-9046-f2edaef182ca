<template>
  <div class="dig-c">
    <div class="header">
      <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="backToHome" />
      <div class="back" @click="backToHome">返回</div>
    </div>
    <div class="content">
      <div class="dig-title-box">
        <div class="dig-btns-box">
          <div class="dig-btn2 print">
            打印
            <div class="type">
              <div @click="handlePrintBook">数字教材</div>
              <div @click="handlePrint">原版教材</div>
            </div>
          </div>
        </div>

        <div class="dig-img-box">
          <div class="r-container">
            <div class="img-box">
              <img v-if="bookInfo && bookInfo.cover" :src="bookInfo.cover" />
              <img src="../../../assets/images/default-cover.jpg" />
            </div>
          </div>
        </div>

        <div class="dig-r-title">
          <div class="title">{{ bookInfo && bookInfo.title }}</div>
          <div v-show="bookInfo && bookInfo.title" class="des">书名：{{ bookInfo && bookInfo.title }}</div>
          <div v-show="bookInfo && bookInfo.author" class="des">主编：{{ bookInfo && bookInfo.author }}</div>
          <div v-show="bookInfo && bookInfo.isbn" class="des">ISBN：{{ bookInfo && bookInfo.isbn }}</div>
          <div v-show="bookInfo && bookInfo.publisher" class="des">出版社：{{ bookInfo && bookInfo.publisher }}</div>
          <div v-if="bookInfo && bookInfo.goodsComm&&bookInfo.goodsComm.price!==null" class="des price">{{ bookInfo.goodsComm.price }}元</div>
          <!-- <div class="des">出品方：萃雅教育</div> -->
        </div>

        <div class="dig-btn-box">
          <div v-if="!$route.query.f" class="dig-btn2" @click="toResource">
            资源库
          </div>
          <div v-if="!$route.query.f" class="dig-btn" @click="attendClass">
            <div v-show="bookInfo && !bookInfo.studentCourseId" class="use">试用</div>
            教学
          </div>
          <el-popover
            placement="bottom-end"
            trigger="hover"
          >
            <div class="pop_main">
              <div class="title">选择阅读模式</div>
              <div class="main">
                <img src="@/assets/images/booktype/read.jpg" alt="" @click="read" />
                <img src="@/assets/images/booktype/study.jpg" alt="" @click="study" />
                <img src="@/assets/images/booktype/graph.jpg" alt="" @click="graph" />
              </div>
            </div>
            <div slot="reference" class="dig-btn" @click="read">
              <div v-show="bookInfo && !bookInfo.studentCourseId" class="use">试用</div>
              阅读
            </div>
          </el-popover>
          <div class="dig-btn2" @click="buyCoures">
            {{ bookInfo && bookInfo.studentCourseId?'已购买':'购买/兑换' }}
          </div>
        </div>
      </div>

      <div class="dig-tag">
        <detailTag :book-info="bookInfo" :catalogue-list="catalogueList" />
      </div>
    </div>
    <PayToast ref="pay" :good-info="bookInfo&&bookInfo.goodsComm" />
    <Print ref="print" :book-config="bookConfig" />
    <BindBook
      v-if="bindBookShow"
      :show="bindBookShow"
      :append-to-body="true"
      @close="bindBookShow = false"
    />
    <printBook ref="printBook" />
  </div>
</template>

<script>
import detailTag from './components/detailTag.vue'
import { getBook, getBookCatalogue, getDigitalBookConfig } from '@/api/digital-api.js'
import PayToast from '@/components/classPro/Pay/index.vue'
import Print from '../read/component/print.vue'
import BindBook from '@/views/digitalbooks/myDigitalbooks/components/Dialog/bindBook.vue'
import printBook from '../read/component/bookPrint.vue'
export default {
  components: {
    detailTag,
    PayToast,
    Print,
    BindBook,
    printBook
  },
  data () {
    return {
      bookId: 0,
      bookInfo: null,
      catalogueList: [],
      studentCourseId: 0,
      bookConfig: null,
      bindBookShow: false
    }
  },
  mounted () {
    this.bookId = this.$route.query && this.$route.query.id
    this._getBook()
    this._getBookCatalogue()
    this._getDigitalBookConfig()
    this.studentCourseId = this.$route.query.studentCourseId || 0
  },
  methods: {
    handlePrintBook() {
      this.$refs.printBook.open(this.bookInfo.id, this.bookInfo.studentCourseId)
    },
    handlePrint() {
      if (!(this.bookConfig && this.bookConfig.originBookPath)) {
        this.$message.warning('暂无原版教材')
        return
      }
      this.$refs.print.open()
    },
    async _getDigitalBookConfig() {
      const { data } = await getDigitalBookConfig({
        digitalBookId: this.bookId
      })
      this.bookConfig = data
    },
    buyCoures() {
      if (this.bookInfo.studentCourseId) {
        return
      }
      if (!this.bookInfo.goodsComm || this.bookInfo.goodsComm.price === null) {
        this.bindBookShow = true
        return
      }
      this.$refs.pay.show()
    },
    backToHome () {
      if (this.$route.query && this.$route.query.f === 'recom') {
        this.$router.push('/classpro')
      } else if (this.$route.query && this.$route.query.f === 'recom2') {
        this.$router.push('/classpro/digitalbooks')
      } else {
        this.$router.push('/classpro/myDigitalbooks')
      }
    },
    read () {
      if (this.$route.query && this.$route.query.f === 'recom') {
        this.$router.push(`/digitalbooks/read?id=${this.bookId}&studentCourseId=${this.studentCourseId}&f=detail`)
      } else {
        // 返回书本+推荐页面
        this.$router.push(`/digitalbooks/read?id=${this.bookId}&studentCourseId=${this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId}&f=detail&f2=recom`)
      }
    },
    study() {
      if (this.$route.query && this.$route.query.f === 'recom') {
        this.$router.push(`/digitalbooks/task?id=${this.bookId}&studentCourseId=${this.studentCourseId}&f=detail`)
      } else {
        // 返回书本+推荐页面
        this.$router.push(`/digitalbooks/task?id=${this.bookId}&studentCourseId=${this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId}&f=detail&f2=recom`)
      }
    },
    graph() {
      if (this.$route.query && this.$route.query.f === 'recom') {
        this.$router.push(`/digitalbooks/read?id=${this.bookId}&studentCourseId=${this.studentCourseId}&f=detail&to=graph`)
      } else {
        // 返回书本+推荐页面
        this.$router.push(`/digitalbooks/read?id=${this.bookId}&studentCourseId=${this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId}&f=detail&f2=recom&to=graph`)
      }
    },
    attendClass () {
      if (this.$route.query && this.$route.query.f) {
        // 返回书本+推荐页面
        this.$router.push(`/digitalbooks/attendClass?id=${this.bookId}&studentCourseId=${this.studentCourseId}&f=detail&f2=${this.$route.query.f}`)
      } else {
        this.$router.push(`/digitalbooks/attendClass?id=${this.bookId}&studentCourseId=${this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId}&f=detail`)
      }
    },
    toResource () {
      if (this.$route.query && this.$route.query.f) {
        // 返回书本+推荐页面
        this.$router.push(`/digitalbooks/resource?id=${this.bookId}&studentCourseId=${this.studentCourseId}&f=detail&f2=${this.$route.query.f}`)
      } else {
        this.$router.push(`/digitalbooks/resource?id=${this.bookId}&studentCourseId=${this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId}&f=detail`)
      }
    },
    async _getBook () {
      const { data } = await getBook({
        bookId: this.bookId,
        scene: 'BOOK_CATALOGUE_OWN'
      })
      this.bookInfo = data
    },
    async _getBookCatalogue () {
      const { data } = await getBookCatalogue({
        bookId: this.bookId,
        type: 'CHAPTER'
      })
      this.catalogueList = data
    },
    handleTips () {
      // this.$message.warning('敬请期待！')
      this.$alert('敬请期待！', '', {
        confirmButtonText: '确定',
        callback: action => {

        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pop_main{
  width: 500px;
  height: 200px;
  .title{
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
    color:#000000
  }
  .main {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    img{
      width: 150px;
      cursor: pointer;
    }
  }
}

.dig-c {
  width: 100%;
  height: 100%;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    img {
      width: 13px;
      height: 11px;
      cursor: pointer;
      object-fit: contain;
    }

    .back {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-L);
      color: #1C1B1A;
      letter-spacing: 0.22px;
      margin: 0 20px 0 8px;
      cursor: pointer;
    }

    span {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-M);
      color: #999999;
      letter-spacing: 0.16px;
    }
  }

  .content {
    height: calc(100% - 30px);
    overflow-y: auto;
    @include scrollBar;

    .dig-title-box {
      display: flex;
      width: 100%;
      padding: 10px;
      background: #FFF;
      border-radius: 10px;
      position: relative;

      .dig-btns-box {
        position: absolute;
        right: 20px;
      }

      .dig-img-box {
        width: 150px;
      }

      // 图片  4:3
      .r-container {
        position: relative;
        min-height: 0;
        border-radius: 10px;
        padding-bottom: 133.33%;

        .img-box {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          border-radius: 10px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    .dig-r-title {
      margin-left: 10px;
      .title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
        margin-bottom: 20px;
      }

      .des {
        color: #333;
        font-size: var(--font-size-L);
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
      .price{
        font-size: var(--font-size-XXL);
        color: red;
      }
    }

    .dig-btn-box {
      position: absolute;
      right: 10px;
      bottom: 10px;
      display: flex;
    }

    .dig-btn {
      display: flex;
      padding: 8px 10px;
      justify-content: center;
      align-items: center;
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
      border-radius: 3px;
      background: #F2C94C;
      margin-left: 20px;
      cursor: pointer;
      position: relative;

      .use {
        position: absolute;
        top: -19px;
        right: 0;
        display: flex;
        padding: 2px 5px;
        justify-content: center;
        align-items: center;
        color: #fff;
        background: linear-gradient(90deg, #F46B45 0%, #EEA849 100%);
        border-radius: 5px;
        font-size: var(--font-size-M);

        &::before {
          content: '';
          position: absolute;
          top: 100%;
          left: 45%;
          width: 0;
          height: 0;
          border-width: 3px;
          border-style: solid;
          border-color: transparent;
          border-top-width: 3px;
          border-top-color: currentColor;
          color: rgba($color: #EEA849, $alpha: .8);
        }
      }
    }

    .dig-btn2 {
      border-radius: 3px;
      border: 1px solid #000;
      display: flex;
      padding: 8px 10px;
      justify-content: center;
      align-items: center;
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
      margin-left: 20px;
      cursor: pointer;
      .type{
        position: absolute;
        transition: all .3s linear;
        width: 74px;
        height: 59px;
        background: #f6f6f6;
        border-radius: 9px;
        top:36px;
        opacity: 0;
        div{
          font-size: var(--font-size-L);
          width: 100%;
          height: 28px;
          text-align: center;
          line-height: 28px;
          cursor: pointer;
        }
        div:hover{
          background: #E8F2FF;
        }
      }
    }
  .print:hover{
    .type{
      opacity: 1;
    }
  }
    .dig-tag {
      margin-top: 10px;
      height: calc(100% - 150px * 1.3043 - 35px);
    }
  }
}
</style>
