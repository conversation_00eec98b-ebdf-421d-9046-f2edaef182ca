<template>
  <div class="main">
    <div v-if="step===0">
      <div class="w flex flex-col items-center">
        <img :src="bookInfo&&bookInfo.cover" alt="" />
      </div>
      <div class="title w flex flex-col items-center">
        <p>《{{ bookInfo&&bookInfo.title }}》</p>
      </div>
      <div class="info w flex flex-col items-center">
        <p>{{ $route.query.userName }}   <span style="color:#000">邀请你一起编写数字教材</span></p>
      </div>
      <div class="w flex flex-col items-center">
        <el-button class="button" type="primary" @click="toSecondStep">接受邀请</el-button>
      </div>
    </div>
    <div v-if="step===1">
      <div class="form flex flex-col items-center">
        <el-form ref="form" class="el_from">
          <el-form-item label="手机号：" placeholder="请输入手机号">
            <el-input v-model="form.mobileOrEmail" type="text" />
          </el-form-item>
          <el-form-item label="验证码：" style="position: relative" placeholder="请输入验证码">
            <el-input v-model="form.code" />
            <el-button
              type="info"
              :disabled="smsDisabled"
              class="sms-btn"
              @click="getCode"
            >
              {{
                smsDisabled
                  ? countdown > 0
                    ? countdown + 's后重新获取'
                    : '发送验证码'
                  : '发送验证码'
              }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="title w flex flex-col items-center">
          <el-button
            type="primary"
            class="button1"
            @click="takeInvite"
          >接受邀请</el-button>
        </div>
      </div>
    </div>
    <div v-if="step===2">
      <div class=" w flex flex-col items-center">
        <img class="success_img" src="../../../assets/publishingReview/success.png" alt=""/>
      </div>
      <div class=" w flex flex-col items-center">
        <p class="tips">已被邀请成功</p>
      </div>
      <div class="  w flex flex-col items-center">
        <p class="tips">在web网站上打开网页，开始您的创作</p>
      </div>
      <div class=" w flex flex-col items-center">
        <p class="link">https://binguoketang.com/#/author/login <span
          v-clipboard:copy="'https://binguoketang.com/#/author/login'"
          v-clipboard:success="onCopy"
        >复制</span></p>
      </div>
    </div>
  </div>
</template>

<script>
import { getBook, verifyDigitalAuthorInviteValid } from '@/api/digital-api.js'
import { verifyCodeForWeb } from '@/api/user-api'
import { validMobile } from '@/utils/validate'
import { acceptDigitalAuthorInvite } from '@/api/publishing.js'
export default {
  data() {
    return {
      bookInfo: null,
      step: 0,
      form: {
        mobileOrEmail: '',
        code: '',
        loginType: 'SMS_CODE'
      },
      smsDisabled: false,
      countdown: 0

    }
  },
  mounted() {
    this._getBook()
  },
  methods: {
    /**
     * 复制操作函数
     *
     * @returns 无返回值
     */
    onCopy() {
      this.$message.success('复制成功')
    },
    takeInvite() {
      const mobile = this.form.mobileOrEmail
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      } else if (this.form.code.length !== 4) {
        this.$message.error('请输入正确的验证码')
        return false
      }
      // login({ loginType: 'SMS_CODE', ...this.form }).then(res => {
      //   if (res.code === 200) {
      //     acceptDigitalAuthorInvite({ bookId: this.$route.query.bookId, sign: this.$route.query.sign, expiredTime: this.$route.query.expiredTime }, {
      //       authorization: 'Bearer ' + res.data.access_token
      //     }).then(res => {
      //       if (res.code === 200) {
      //         this.$message.success('接受成功')
      //         this.step = 2
      //       }
      //     })
      //   }
      // })
      this.$store.dispatch('user/AuthorLogin', this.form).then(() => {
        acceptDigitalAuthorInvite({ bookId: this.$route.query.bookId, sign: this.$route.query.sign, expiredTime: this.$route.query.expiredTime }).then(res => {
          if (res.code === 200) {
            this.$message.success('接受成功')
            this.step = 2
          }
        })
      })
    },
    getCode() {
      if (this.smsDisabled) return
      const mobile = this.form.mobileOrEmail
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this._verifyCodeForWeb()
      // this.nocaptchaVisible = true
    },
    _verifyCodeForWeb() {
      verifyCodeForWeb({ mobile: this.form.mobileOrEmail }).then((res) => {
        this.countdown = 60
        this.smsDisabled = true
        setTimeout(this.tick, 1000)
      })
    },
    tick() {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    },
    /**
     * 跳转到第二步
     *
     * @returns 无返回值
     */
    toSecondStep() {
      verifyDigitalAuthorInviteValid({ bookId: this.$route.query.bookId, sign: this.$route.query.sign, expiredTime: this.$route.query.expiredTime }).then(res => {
        if (res.code === 200) {
          this.step = 1
        }
      })
    },
    /**
     * 获取书籍信息
     *
     * @returns 无返回值，通过修改this.bookInfo来更新书籍信息
     */
    _getBook() {
      getBook({ bookId: this.$route.query.bookId }).then(res => {
        this.bookInfo = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
    .main{
    width: 100%;
    height: 100%;
    overflow: hidden;
    .el_from{
    margin-top: 120px;
    width: 300px;
    ::v-deep .el-input{
      font-size: 12px;
    }
    ::v-deep .el-form-item__label{
      font-size: 12px;
      height:30px;
      line-height: 42px;
      text-align: left;
    }
    ::v-deep .el-input__inner{
      width: 100%;
      height: 30px;
      line-height: 80px;
    }
    ::v-deep .el-form-item__label{
      width: 60px ;
    }
    ::v-deep .el-form-item__content{
      margin-left: 60px;
    }
  }
  .success_img{
    width: 50px;
    margin-top: 70px;
  }
  .tips{
    font-size: 12px;
  }
  .link{
    font-size: 12px;
    color:#2F80ED;
    span{
      cursor: pointer;
    }
  }
    .sms-btn {
        position: absolute;
        top: 8px;
        right:10px;
        margin: 3px;
        padding: 0;
        width: 55px;
        font-size: 8px !important;
        height: 20px;
        line-height: 15px;
        border-radius: 6px;
        font-weight: 500;
        background: #1f66ff;
        color: #ffffff;
        // box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.16);

        // &.is-disabled {
        //   border: #ffffff;
        //   background: #a1a1a1;
        //   color: #ffffff;
        // }

        // &.is-disabled:hover {
        //   border: #ffffff;
        //   background: #a1a1a1;
        //   color: #ffffff;
        // }
      }

      .sms-btn:hover {
        background: #1f66ff;
        box-shadow: 1px 1px 1px #fff;
      }
    img{
        width: 140px;
        margin-top: 20px;
        margin-top: 100px;
    }
    .title{
      font-size: 14px;
      font-weight: 600;
      text-align: center;
    }
    .info{
      font-size: 14px;
      color: #2F80ED;
    }
    .button{
      width: 140px;
      font-size: 14px;
      padding: 5px;
      margin-top: 20px;
    }
    .button1{
      width: 150px;
      font-size: 12px;
      padding: 10px;
      margin-top: 20px;
    }
}
@media screen and (max-width: 700px) {
    .main{
    width: 100%;
    height: 100%;
    overflow: hidden;
  .el_from{
    margin-top: 200px;
    width: 90%;
    ::v-deep .el-input{
      font-size: 40px;
    }
    ::v-deep .el-form-item__label{
      font-size: 40px;
      height: 80px;
      line-height: 80px;
      text-align: right;
    }
    ::v-deep .el-input__inner{
      width: 90%;
      height: 80px;
      line-height: 80px;
    }
    ::v-deep .el-form-item__label{
      width: 250px ;
    }
    ::v-deep .el-form-item__content{
      margin-left: 250px;
    }
  }
  .success_img{
    width: 300px;
  }
  .tips{
    font-size: 40px;
  }
  .link{
    font-size: 40px;
    color:#2F80ED;
  }
    .sms-btn {
        position: absolute;
        top: 12px;
        right: 110px;
        margin: 3px;
        width: 150px;
        height: 50px;
        padding-left:5px;
        padding-right: 5px;
        font-size: 20px !important;
        // transform: scale(2.0);
        line-height: 20px;
        border-radius: 10px;
        font-weight: 500;
        background: #1f66ff;
        color: #ffffff;
        border: none;
        box-sizing: border-box !important;
        // box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.16);

        // &.is-disabled {
        //   border: #ffffff;
        //   background: #a1a1a1;
        //   color: #ffffff;
        // }

        // &.is-disabled:hover {
        //   border: #ffffff;
        //   background: #a1a1a1;
        //   color: #ffffff;
        // }
      }

      // .sms-btn:hover {
      //   background: #1f66ff;
      //   box-shadow: 1px 1px 1px #fff;
      // }
    img{
        width: 600px;
        margin-top: 80px;
    }
    .title{
      font-size: 60px;
      font-weight: 600;
    }
    .info{
      font-size: 40px;
      color: #2F80ED;
    }
    .button{
      width: 400px;
      font-size: 40px;
      padding: 20px;
      margin-top: 50px;
    }
    .button1{
      width: 600px;
      font-size: 40px;
      padding: 20px;
      margin-top: 80px;
    }
}
}
</style>
