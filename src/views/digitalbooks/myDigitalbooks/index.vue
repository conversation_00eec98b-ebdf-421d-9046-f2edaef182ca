<template>
  <div class="my-course">
    <!-- <div class="tag-box flex">
      <div :class="tagIndex === 1 ? 'tag-item__active' : 'tag-item'" @click="changeTag(1)">
        数字教材
      </div>
      <div :class="tagIndex === 2 ? 'tag-item__active' : 'tag-item'" @click="changeTag(2)">
        教务管理
      </div>
    </div> -->
    <div class="my-course-body">
      <Books v-if="tagIndex === 1" ref="book" @handleClass="handleClass" />
      <Grade v-if="tagIndex === 2" @back="back" />
      <Class v-if="tagIndex === 3" @back="back" />
    </div>
    <BindBook
      v-if="bindBookShow"
      :show="bindBookShow"
      :append-to-body="true"
      @close="bindBookShow = false; $refs.book._userDigitalBooks()"
    />
    <Pop ref="Pop" @setIndex="setIndex" />
  </div>
</template>

<script>
import Books from './components/books.vue'
import Grade from './components/grade.vue'
import Class from './components/class.vue'
import BindBook from './components/Dialog/bindBook.vue'
import Pop from './components/coursePop.vue'

export default {
  components: {
    Books,
    Grade,
    Class,
    BindBook,
    Pop
  },
  data () {
    return {
      tagIndex: 1,
      bindBookShow: false
    }
  },
  mounted () {
    const fromClassInfo = window.sessionStorage.getItem('diginfoback')
    if (fromClassInfo) {
      this.tagIndex = 2
    }
    setTimeout(() => {
      window.sessionStorage.removeItem('diginfoback')
    }, 2000)
    if (!localStorage.getItem('currRole')) {
      this.$refs.Pop.open()
    }
  },
  methods: {
    setIndex() {
      this.$emit('changeIndex', 10)
    },
    back() {
      this.tagIndex = 1
    },
    changeTag (index) {
      this.tagIndex = index
    },
    handleClass(role) {
      this.tagIndex = role === 'TEACHER' ? 2 : 3
    }
  }
}
</script>

<style lang="scss" scoped>
.my-course {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  border-radius: 0 0 10px 10px;

  .tag-box {
    width: 100%;
    height: 32px;
    position: relative;

    .tag-item,
    .tag-item__active {
      border-radius: 8px 25px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: var(--font-size-L);
      font-weight: 500;
      height: 100%;
      width: 110px;
      cursor: pointer;
    }

    .tag-item {
      background: #f4f4f4;
      color: #717171;
    }

    .tag-item__active {
      background: #ffffff;
      font-weight: 500;
      color: #3479ff;
    }

    .bind-book {
      position: absolute;
      right: 10px;
      height: 100%;
      display: flex;
      align-items: center;
      color: #000;
      font-size: var(--font-size-L);
      cursor: pointer;
      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .my-course-body {
    height: calc(100%);
    background: #fff;
    border-radius: 10px 10px 10px 10px;
    padding: 10px;
    box-sizing: border-box;
    @include scrollBar;
  }
}
</style>
