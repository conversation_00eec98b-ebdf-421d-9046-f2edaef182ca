<template>
  <div class="w h">
    <div class="my-book-title">
      <div class="flex justify-between items-center">
        <el-popover
          :value="popShow"
          placement="bottom-start"
          width="400"
          trigger="manual"
        >
          <div class="max-height-300">
            <div
              v-for="(item, index) in userList"
              :key="index"
              class="list-li"
              @click="handleUser(item)"
            >
              {{ item.name }}
            </div>
          </div>
          <div slot="reference" class="flex items-center">
            <div class="user-name">
              {{ currUser && currUser.name }}
            </div>
            <img
              v-if="userList.length > 1"
              class="pointer change-img"
              src="../../../../assets/digitalbooks/exchange.svg"
              @click="popShow = !popShow"
            />
          </div>
        </el-popover>
        <div class="role-switch">
          <el-popover
            :value="popShowRole"
            placement="bottom-start"
            width="300"
            trigger="manual"
          >
            <div class="max-height-300">
              <div
                v-for="item in roleList"
                :key="'role-' + item.id"
                class="list-li"
                @click="changeRole(item)"
              >
                {{ item.name }}
              </div>
            </div>
            <div slot="reference" class="flex items-center">
              <div class="user-name">
                当前角色：{{
                  currRole && currRole === 'PERSON'
                    ? '个人'
                    : currRole === 'STUDENT'
                      ? '学生'
                      : '老师'
                }}
              </div>
              <img
                class="pointer change-role-img"
                src="../../../../assets/digitalbooks/exchangeRole.png"
                @click="popShowRole = !popShowRole"
              />
            </div>
          </el-popover>
          <el-button class="button" type="primary" @click="handleClass">{{ currRole === 'TEACHER' ? '教务管理' : '我的班级' }}</el-button>
        </div>
      </div>
    </div>

    <div
      v-if="bookList.length === 0"
      class="my-book-body empty-book-body flex flex-col items-center"
    >
      <div class="mt50 fl">
        {{
          id === (currUser && currUser.userId)
            ? '暂无任何教材，请先兑换教材'
            : '暂无任何教材，请先添加教材'
        }}
      </div>
      <div
        v-if="id === (currUser && currUser.userId)"
        class="exchange-btn mt50"
        @click="bindBookShow = true"
      >
        兑换教材
      </div>
      <div v-else class="exchange-btn mt50" @click="addBookShow = true">
        添加教材
      </div>
    </div>

    <div v-else class="my-book-body body-box">
      <div v-for="item in bookList" :key="'book-' + item.id" class="w flex mb10">
        <div
          class="book-img"
          @click="
            $router.push(
              `/classpro/digitalbooks/detail?id=${item.digitalBook.id}&studentCourseId=${item.id}`
            )
          "
        >
          <div class="r-container">
            <div class="img-box">
              <!-- <img
                class="ronghe"
                src="../../../../assets/images/ronghe.png"
                alt=""
              /> -->
              <img
                v-if="item.digitalBook.cover"
                :src="item.digitalBook.cover"
              />
              <img v-else src="../../../../assets/images/default-cover.jpg" />
            </div>
          </div>
        </div>

        <div class="book-content">
          <div>
            <div class="book-content-title flex justify-between items-center">
              <div class="book-name">
                <span v-if="item.digitalBook.digitalBookType === 'PAPER_DIGITAL'" class="ronghe-title">纸数融合</span>
                {{ item.digitalBook.title }}
              </div>

              <div
                v-if="item.userClass"
                class="class-box article-singer-container"
              >
                班级：{{ item.userClass.name }}
              </div>
            </div>
            <div class="book-content-name">{{ item.digitalBook.author }}</div>
          </div>
          <div class="book-content-btns flex">
            <el-popover placement="bottom-start" trigger="hover">
              <div class="pop_main">
                <div class="title">选择阅读模式</div>
                <div class="main">
                  <img
                    src="@/assets/images/booktype/read.jpg"
                    alt=""
                    @click="
                      $router.push(
                        `/digitalbooks/read?id=${item.digitalBook.id}&studentCourseId=${item.id}`
                      )
                    "
                  />
                  <img
                    src="@/assets/images/booktype/study.jpg"
                    alt=""
                    @click="study(item)"
                  />
                  <img
                    src="@/assets/images/booktype/graph.jpg"
                    alt=""
                    @click="
                      $router.push(
                        `/digitalbooks/read?id=${item.digitalBook.id}&studentCourseId=${item.id}&to=graph`
                      )
                    "
                  />
                </div>
              </div>
              <div
                slot="reference"
                class="b-btn"
                @click="
                  $router.push(
                    `/digitalbooks/read?id=${item.digitalBook.id}&studentCourseId=${item.id}`
                  )
                "
              >
                阅读
              </div>
            </el-popover>
            <div
              class='b-btn'
              @click=' $router.push({
              path:"/digitalbooks/lectureNotes",
              query:{
                bookId:item.digitalBook.id,
                type: "check",
                path: "/classpro/myDigitalbooks"
              }
            }) '>智能云讲义</div>
            <div
              v-show="
                ['ASSISTANT'].indexOf(
                  item.userClass && item.userClass.userType
                ) > -1
              "
              class="b-btn"
              @click="
                $router.push(
                  `/digitalbooks/attendClass?id=${item.digitalBook.id}&studentCourseId=${item.id}`
                )
              "
            >
              教学
            </div>
            <template v-if="item && item.digitalBook">
              <div
                v-show="
                  ['ASSISTANT', 'SCHOOL_STUDENT'].indexOf(
                    item.userClass && item.userClass.userType
                  ) > -1
                "
                class="b-btn"
                @click="handleTask(item)"
              >
                任务
                <div
                  v-if="item.digitalBook.unReadDigitalHomeworkCount"
                  class="num"
                >
                  {{ item.digitalBook.unReadDigitalHomeworkCount }}
                </div>
              </div>
            </template>
            <!-- <div class="b-btn" @click="handleTips">题库</div> -->
            <div
              class="b-btn"
              @click="
                $router.push(
                  `/digitalbooks/resource?id=${item.digitalBook.id}&studentCourseId=${item.id}`
                )
              "
            >
              资源库
            </div>
            <!-- <div class="b-btn" @click="handleTips">讨论</div> -->
            <div class="b-btn" @click="handleStatic(item)">学习统计</div>
            <div v-if="currRole === 'TEACHER'" class='b-btn' @click='handleInvitation(item)'>邀请使用</div>
          </div>
        </div>
      </div>
    </div>

    <BindBook
      v-if="bindBookShow"
      :show="bindBookShow"
      :append-to-body="true"
      @close="
        bindBookShow = false
        _userDigitalBooks()
      "
    />

    <addBook
      v-if="addBookShow"
      :row="currUser"
      :user-id="id"
      :show="addBookShow"
      :append-to-body="true"
      @close="addBookShow = false"
      @added="
        addBookShow = false
        _userDigitalBooks()
      "
    />

    <staticBox
      v-if="staticShow"
      :book-info="selectBookInfo"
      @close="staticShow = false"
    />

    <invitationDialog ref="invitationDialog" :book-id='bookId' :book-title='bookTitle'/>
  </div>
</template>

<script>
import invitationDialog from '@/views/publishingReview/author/components/invitationDialog'
import BindBook from './Dialog/bindBook.vue'
import staticBox from '../../statistics/index.vue'
import addBook from '@/components/classPro/ClassBind/addBook.vue'
import { mapGetters } from 'vuex'
import { userDigitalBooks, getUserClassList } from '@/api/digital-api.js'

export default {
  components: {
    BindBook,
    addBook,
    staticBox,
    invitationDialog
  },
  data() {
    return {
      bindBookShow: false,
      addBookShow: false,
      nowEditRow: null,
      bookList: [],
      currUser: null,
      userList: [],
      selectBookInfo: null,
      staticShow: false,
      popShow: false,
      isTeacher: false,
      popShowRole: false,
      roleList: [
        {
          id: 'TEACHER',
          name: '老师'
        },
        {
          id: 'STUDENT',
          name: '学生'
        },
        {
          id: 'PERSON',
          name: '个人'
        }
      ],
      bookId: 0,
      bookTitle: '',
    }
  },
  computed: {
    ...mapGetters(['name', 'id', 'currRole'])
  },
  mounted() {
    this._userDigitalBooks()
    this._getUserClassList()

    this.currUser = {
      id: this.id,
      userId: this.id,
      name: this.name
    }
  },
  methods: {
    handleClass() {
      this.$emit('handleClass', this.currRole)
    },
    changeRole(item) {
      localStorage.setItem('currRole', item.id)
      this.$store.dispatch('user/SetCurrRole', item.id)
      this.popShowRole = false
      // this.currRole = item.id
    },
    study(item) {
      if (this.$route.query && this.$route.query.f === 'recom') {
        this.$router.push(
          `/digitalbooks/task?id=${item.digitalBook.id}&studentCourseId=${item.id}&f=detail`
        )
      } else {
        // 返回书本+推荐页面
        this.$router.push(
          `/digitalbooks/task?id=${item.digitalBook.id}&studentCourseId=${item.id}&f=detail&f2=recom`
        )
      }
    },
    async _userDigitalBooks() {
      const obj = {}
      if (this.currUser && +this.currUser.userId !== +this.id) {
        obj.classUserId = +this.currUser.userId
      }
      obj.role = this.isTeacher ? 'TEACHER' : 'STUDENT'
      const { data } = await userDigitalBooks(obj)
      this.bookList = data
    },
    async _getUserClassList() {
      this.userList = []
      const { data } = await getUserClassList()
      const my = {
        id: this.id,
        userId: this.id,
        name: this.name
      }
      this.userList = data
      this.userList.unshift(my)
      let curr = window.localStorage.getItem('selectCurrUser')
      if (curr) {
        curr = JSON.parse(curr)
        const flag = this.userList.find((val) => {
          return +val.userId === +curr.userId
        })
        if (flag) {
          this.currUser = flag
          this._userDigitalBooks()
        }
      }
    },
    handleUser(item) {
      this.currUser = item
      this.popShow = false
      this._userDigitalBooks()
      window.localStorage.setItem('selectCurrUser', JSON.stringify(item))
    },
    handleTask(item) {
      if (item && item.userClass && item.userClass.userType) {
        if (item.userClass.userType === 'ASSISTANT') {
          this.$router.push(
            `/digitalbooks/task-t?id=${item.digitalBook.id}&studentCourseId=${item.id}`
          )
        } else if (item.userClass.userType === 'SCHOOL_STUDENT') {
          this.$router.push(
            `/digitalbooks/task-s?id=${item.digitalBook.id}&studentCourseId=${item.id}`
          )
        }
      }
    },
    handleStatic(item) {
      this.selectBookInfo = item
      this.staticShow = true
    },
    handleTips() {
      this.$alert('敬请期待！', '', {
        confirmButtonText: '确定',
        callback: (action) => {}
      })
    },
    handleRoleChange(val) {
      this.isTeacher = val
      this._userDigitalBooks()
    },
    handleInvitation(item) {
      this.bookId = item.digitalBook.id
      this.bookTitle = item.digitalBook.title
      this.$refs.invitationDialog.show(this.bookId, this.bookTitle)
    }
  }
}
</script>

<style lang="scss" scoped>
.pop_main {
  width: 500px;
  height: 200px;
  .title {
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
    color: #000000;
  }
  .main {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    img {
      width: 150px;
      cursor: pointer;
    }
  }
}
.my-book-title {
  color: #000;
  font-size: var(--font-size-L);
  font-weight: 500;
  height: 40px;
  width: 100%;

  :deep(.el-switch) {
    margin-right: 20px;
  }
}

.empty-book-body {
  width: 100%;
  height: calc(100% - 40px);
  overflow: auto;
  @include scrollBar;
}

.body-box {
  height: calc(100% - 40px);
}
.my-book-body {
  width: 100%;
  // height: calc(100% - 40px);
  overflow: auto;
  @include scrollBar;

  .exchange-btn {
    padding: 10px 15px;
    border-radius: 28.327px;
    background: #266bfb;
    font-size: var(--font-size-M);
    color: #fff;
    cursor: pointer;
  }

  .mt50 {
    margin-top: 50px;
  }

  //.f16 {
  //  font-size: 16px;
  //}
  .fl {
    font-size: var(--font-size-L);
  }

  .book-img {
    width: 110px;
  }

  .r-container {
    position: relative;
    min-height: 0;
    padding-bottom: 133.33%;

    .img-box {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .ronghe {
        position: absolute;
        top: 0;
        right: 0;
        width: 50px;
        height: 50px;
      }
    }
  }

  .book-content {
    padding-left: 10px;
    box-sizing: border-box;
    width: calc(100% - 110px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .book-content-title {
      width: 100%;
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;

      .book-name {
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; //需要显示的行数
        overflow: hidden;
        text-overflow: ellipsis;
        .ronghe-title {
          display: inline-block;
          background: linear-gradient(90deg, #00c6fb 0%, #005bea 100%);
          color: #fff;
          font-size: var(--font-size-M);
          padding: 3px 2px;
          border-radius: 4px;
        }
      }
      .class-box {
        border-radius: 5px;
        background: #edf5ff;
        padding: 5px;
        box-sizing: border-box;
        color: #2f80ed;
        font-size: var(--font-size-M);
        flex-shrink: 0;
        max-width: 250px;
        margin-left: 20px;
      }
    }

    .book-content-name {
      margin-top: 20px;
      font-size: var(--font-size-L);
      color: #000;
    }

    .book-content-btns {
      .b-btn {
        color: #2f80ed;
        font-size: var(--font-size-L);
        cursor: pointer;
        margin-right: 20px;
        position: relative;

        .num {
          position: absolute;
          right: -10px;
          top: -20px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: #eb5757;
          color: #fff;
          font-size: var(--font-size-M);
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}

.list-li {
  line-height: 40px;
  height: 40px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  padding: 0 10px;
  &:hover {
    background-color: #edf5ff;
    border-radius: 5px;
  }
}

.user-name {
  max-width: calc(100%);
  padding-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.change-img {
  width: 25px;
}
.change-role-img {
  width: 12px;
}

.mb10 {
  margin-bottom: 10px;
}

.max-height-300 {
  max-height: 300px;
  overflow-y: auto;
  @include scrollBar;
}

.role-switch {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: var(--font-size-M);

  :deep(.el-switch) {
    margin: 0 20px;
    .el-switch__label {
      color: #1890ff;

      &.is-active {
        color: #1890ff;
      }
    }
  }
  .button {
    background: #266bfb;
    width: 50px;
    height: 20px;
    padding: 0;
    font-size: var(--font-size-M);
    color: #fff;
    margin-left: 10px;
  }
}
</style>
