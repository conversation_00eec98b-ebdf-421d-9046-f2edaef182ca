<template>
  <NormalDialog
    v-if="bindBookShow"
    width="30vw"
    :title="'兑换'"
    :dialog-visible="bindBookShow"
    :is-center="true"
    :append-to-body="appendToBody"
    @closeDialog="close"
  >
    <div class="flex flex-col w">
      <div class="mb10">
        <el-input v-model="code" class="w" placeholder="请输入兑换码" />
      </div>
      <!-- <div style="color: #828282">
        备注：
        <div class="school-disc flex items-center">
          <div class="school-disc2"></div>
          输入数字教材兑换码
        </div>
      </div> -->
    </div>
    <template #footer>
      <div class="edu-btn" @click="getBookInfo">确定</div>
      <NormalDialog
        v-if="bookInfoShow"
        width="30vw"
        :title="'兑换信息'"
        :dialog-visible="bookInfoShow"
        :is-center="true"
        :append-to-body="true"
        @closeDialog="bookInfoShow = false"
      >
        <div class="flex flex-col items-center w">
          <div class="book-title">您确定兑换以下教材/课程</div>
          <div class="book-img">
            <div class="r-container">
              <div class="img-box">
                <el-image v-if="bookInfo.cover" :src="bookInfo.cover" fit='contain'/>
                <el-image v-else src="@/assets/images/default-cover.jpg" fit='contain' />
              </div>
            </div>
          </div>
          <div class="book-name">{{ bookInfo.title }}</div>
          <div class="book-author">
            {{ bookInfo.author }}
          </div>
          <div class="edu-btn" @click="bind">确定</div>
        </div>
      </NormalDialog>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { debounce } from '@/utils/index'
import { exchangeEmpowerCourse, empowerExchangeGoodsInfo } from '@/api/digital-api.js'
import { mapGetters } from 'vuex'
export default {
  components: { NormalDialog },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      code: '',
      bindBookShow: false,
      bookInfoShow: false,
      bookInfo: null
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  mounted () {
    this.bindBookShow = this.show
  },
  methods: {
    close () {
      this.bindBookShow = false
      this.$emit('close')
    },
    async getBookInfo () {
      const { data } = await empowerExchangeGoodsInfo({
        code: this.code
      })
      console.log(data)
      if (data) {
        if (data.courseType === 'AI_COURSE') {
          this.$message.warning('不支持兑换的课程类型，请到“缤果伴学”公众号兑换')
          return
        }
        this.bookInfo = data.dataInfo
        this.bookInfoShow = true
      }
    },
    bind: debounce(async function () {
      if (this.code) {
        try {
          await exchangeEmpowerCourse({
            exchangeCode: this.code,
            childId: this.id
          })
          this.bindBookShow = false
          this.close()
          this.$router.go(0)
        } catch (error) {
          console.log(error)
        }
      }
    }, 3000, true)
  }
}
</script>

<style lang="scss" scoped>
.school-disc {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.school-disc2 {
  width: 5px;
  height: 5px;
  background: #828282;
  border-radius: 50%;
  margin-right: 5px;
}

.book-title {
  color: #333;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 20px;
}

.book-img {
  width: 40%;
  margin-bottom: 20px;

  .r-container {
    position: relative;
    min-height: 0;
    padding-bottom: 133.33%;

    .img-box {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.book-name {
  color: #000;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
}

.book-author {
  color: #000;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 20px;
}
</style>
