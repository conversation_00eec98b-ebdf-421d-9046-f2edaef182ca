<template>
  <div class="edu">
    <div class="wrap">
      <div class="tag-box flex">
        <div class="pointer flex items-center" @click="backToEdu">
          <img
            class="h-img"
            src="@/assets/images/skyclass/arrow-back.png"
            alt="返回按钮"
          />
          <div class="back">返回</div>
        </div>
      </div>
      <div class="grade-box">
        <div class="grade-wrap">
          <div class="grade-title">
            <div>
              当前班级： <span style="color: #3479ff">{{ name }}</span>
            </div>
          </div>
          <div class="table-box">
            <div class="table-title">
              <div class="flex items-center">
                <div class="icon-i"></div>
                <div>班级教材</div>
              </div>
            </div>
            <el-table
              :data="tableData"
              :header-row-style="{ background: '#F8FAFF' }"
              :header-cell-style="{
                background: '#F8FAFF',
                color: '#3479FF',
                border: 'none',
                'font-weight': '400'
              }"
              :header-row-class-name="tableHeader"
              :row-class-name="rowClass"
              :cell-class-name="cellClass"
              style="width: 100%; height: 100vh"
            >
              <el-table-column
                prop="date"
                align="left"
                label="教材名称"
              >
                <template slot-scope="scope">
                  {{ scope.row.digitalBook.title }}
                </template>
              </el-table-column>

              <el-table-column
                prop="name"
                align="left"
                label="操作"
              >
                <template slot-scope="scope">
                  <span
                    class="item-handle-del"
                    @click="handleRemove(scope.row)"
                  >删除</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <ComponentDialog
      :width="'400px'"
      :title="'删除'"
      :dialog-visible="removeVisible"
      :is-center="true"
      @closeDialog="
        removeVisible = false
        clearItem()
      "
    >
      <div class="flex flex-col w items-center">
        <div style="margin-bottom: 20px" class="f14">确认删除该教材吗？</div>
        <div class="flex justify-around w">
          <div
            class="edu-btn-opacity f14"
            @click="
              removeVisible = false
              clearItem()
            "
          >
            放弃
          </div>
          <div class="edu-btn f14" @click="remove">确定删除</div>
        </div>
      </div>
    </ComponentDialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ComponentDialog from '@/components/classPro/ComponentDialog'
import {
  getUserInfo,
  updateStudentCoursePlan,
  removeStudentCourse
} from '@/api/educational-api.js'

import { userDigitalBooks } from '@/api/digital-api.js'
import { debounce } from '@/utils/index'

export default {
  components: { ComponentDialog },
  filters: {
    courseType (val) {
      let str = ''
      switch (val) {
        case 'COURSE':
          str = '空中课堂'
          break
        case 'AI_COURSE':
          str = '双师AI课堂'
          break
      }
      return str
    }
  },
  data () {
    return {
      name: '',
      childId: '',
      editCourseShow: false,
      hasWeeks: false,
      weeksList: [],
      studentCourseId: '',
      tableData: [],
      removeVisible: false,
      removeId: ''
    }
  },
  computed: {
    ...mapGetters([
      'school',
      'id'
    ])
  },
  mounted () {
    this._getStudentCourseList()
    this._getUserInfo()
  },
  methods: {
    tableHeader (row, rowIndex) {
      return 'table-header'
    },
    rowClass (row, rowIndex) {
      return 'row-class'
    },
    cellClass (row, rowIndex) {
      return 'cell-class'
    },
    async _getStudentCourseList () {
      const token = this.$route.query.t
      const { data } = await userDigitalBooks({}, {
        'authorization': 'Bearer ' + token
      })
      this.tableData = data
    },
    async _getUserInfo () {
      const token = this.$route.query.t
      const { data } = await getUserInfo({ userType: 'STUDENT' }, token)
      this.childId = data.id
      this.name = data.displayName
    },
    editCourse: debounce(async function () {
      const obj = {
        studentCourseId: this.studentCourseId
      }
      await updateStudentCoursePlan(obj)
      this._getStudentCourseList()
      this.clearItem()
      this.editCourseShow = false
    }, 3000, true),
    handleRemove (row) {
      this.removeId = row.id
      this.removeVisible = true
    },
    remove: debounce(async function () {
      try {
        await removeStudentCourse({ studentCourseId: this.removeId })
        this.$message.success('删除成功')
        this._getStudentCourseList()
        this.removeVisible = false
      } catch (error) {
        this.removeVisible = false
        this.clearItem()
      }
    }, 3000, true),
    clearItem () {
      this.hasWeeks = false
      this.weeksList = []
      this.studentCourseId = ''
      this.removeId = ''
    },
    backToEdu () {
      window.sessionStorage.setItem('diginfoback', true)
      this.$router.push({ path: '/classpro/myDigitalbooks' })
    }
  }
}
</script>

<style lang="scss" scoped>
.edu {
  width: 100%;
  height: calc(100% - 65px);
}
.wrap {
  padding: 20px 28px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  //@include scrollBar

  .h-img {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }

  .back {
    font-size: 14px;
    font-weight: 500;
    color: #0b0b0b;
    line-height: 20px;
    margin-left: 17px;
    cursor: pointer;
  }

  .tag-box {
    width: 100%;
    height: 32px;
  }

  .grade-box {
    height: calc(100% - 32px);
    background: #fff;
    padding: 20px;
    //@include scrollBar;
    //overflow: scroll;
    //overflow-x: hidden;
    /*增加滚动条*/
    overflow-y:auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 5px;
      height: 1px;
    }

    .grade-wrap {
      width: 100%;
      height: 100%;
      .grade-title {
        height: 50px;
        border: 1px solid #c3d5f9;
        border-radius: 5px;
        padding: 0 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: var(--font-size-L);
      }

      .table-box {
        height: calc(100% - 70px);
        //height: 50vh;
        padding: 0 20px 0 0;
        .table-title {
          height: 50px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 500;
          font-size: 16px;
          color: #1c1b1a;
          .icon-i {
            width: 3px;
            height: 16px;
            background: #3479ff;
            border-radius: 1px;
            margin-right: 5px;
          }
        }
      }

      .btn,
      .btn-active {
        width: 100px;
        height: 35px;
        border-radius: 5px;
        font-size: 14px;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .btn {
        background: #aaaaaa;
      }

      .btn-active {
        background: #3479ff;
      }
    }
    .tr {
      text-align: right;
    }

    .tl {
      text-align: left;
    }

    .item-scope {
      width: 100%;
      @include ellipses(1);
    }

    .item-handle {
      color: #3479ff;
    }

    .item-handle-dis {
      color: #595959;
    }

    .item-handle-del {
      color: #ff3434;
    }
    .item-handle,
    .item-handle-dis,
    .item-handle-del {
      font-size: 14px;
      cursor: pointer;
      margin-right: 10px;
      text-decoration: underline;
    }
  }
  .f14 {
    font-size: 14px;
  }
}

.pop-box {
  max-height: 300px;
  overflow-y: auto;
  .item-scope {
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
<style lang="scss">
.edu {
  .wrap {
    .table-header {
      font-weight: 400;
      font-size: 16px;
      color: #3479ff;

      th {
        background: #f8faff;
      }

      .el-table__cell {
        border-bottom: none;
      }
    }

    .cell-class {
      font-size: 14px;
    }

    .row-class {
      font-weight: 400;
      font-size: 14px;
      color: #0e0e0e;
      letter-spacing: 0.22px;
    }
  }
}
</style>
