<template>
  <NormalDialog
    v-if="dialogShow"
    width="400px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class=" editor-dig w">
      <div class="item_content" @click="student">
        <p class="title">我是学生</p>
        <p>加入自己的班级</p>
      </div>
      <div class="item_content" @click="teacher">
        <p class="title">我是老师</p>
        <p>创建班级</p>
        <p>并给班级添加教材</p>
      </div>
      <div class="item_content" @click="person">
        <p class="title">个人</p>
        <p>自己个人使用</p>
      </div>
      <p class="p1" @click="dialogShow=false">稍后再说</p>
      <p class="p2">*也可以在左侧菜单“班级管理”设置</p>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },

  data () {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '请选择角色',
      keyword: '',
      content: ''
    }
  },
  mounted () {
  },
  methods: {
    student() {
      localStorage.setItem('currRole', 'STUDENT')
      this.$store.dispatch('user/SetCurrRole', 'STUDENT')
      this.close()
      // window.sessionStorage.setItem('menuSelectIndex', 10)
      // this.$emit('setIndex', 10)
      // this.$router.push({ path: '/classpro/myClass', query: { type: 'student' }})
    },
    teacher() {
      localStorage.setItem('currRole', 'TEACHER')
      this.$store.dispatch('user/SetCurrRole', 'TEACHER')
      this.close()
      // window.sessionStorage.setItem('menuSelectIndex', 10)
      // this.$emit('setIndex', 10)
      // this.$router.push({ path: '/classpro/myClass' })
    },
    person() {
      localStorage.setItem('currRole', 'PERSON')
      this.$store.dispatch('user/SetCurrRole', 'PERSON')
      this.close()
    },
    close () {
      this.dialogShow = false
    },
    open () {
      this.dialogShow = true
    }
  }
}
</script>

  <style lang="scss" scoped>
  .item_content{
    width: 100%;
    height: 100px;
    background: #FAFAFA;
    border-radius: 10px;
    margin-top: 20px;
    overflow: hidden;
    cursor: pointer;
    p{
        width: 100%;
        text-align: center;
        font-size: 10px;
    }
    .title{
        width: 100%;
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        color:#000000;
        margin-top: 20px;
    }
  }
  .item_content:hover{
    background: #CFE4FF;
  }
  .p1{
    width: 100%;
    text-align: center;
    font-size: 10px;
    color:#2F80ED;
    text-decoration-line:underline;
    cursor: pointer;
  }
  .p2{
    width: 100%;
    text-align: center;
    font-size: 10px;
    color:#2F80ED;
  }

  .editor-dig {
    .school-disc {
      margin-top: 10PX;
    }
    .mb10 {
      margin-bottom: 10PX;
    }

    .school-disc2 {
      width: 5PX;
      height: 5PX;
      background: #828282;
      border-radius: 50%;
      margin-right: 5PX;
    }
  }
  </style>
