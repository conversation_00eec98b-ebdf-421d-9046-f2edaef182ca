<template>
  <!-- 课时选择 -->
  <div class="attend-class">
    <div class="head-box">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        {{ bookTitle }}
      </div>
    </div>

    <div class="attend-content">
      <swiper ref="lineSwiper" class="swiper" :options="swiperOption" @slideChange="onSlideChange">
        <swiper-slide v-for="(item, index) in lessonList.filter(item=>{return item.coursecommUnitResourceList&&item.coursecommUnitResourceList.filter(item1=>{return item1.resourceType==='DIGITAL_TEACH_COURSEWARE'}).length!==0})" :key="item.id">
          <div class="course-card">
            <div class="c-img">
              <img class="w h object-cover" :src="item.background || defaultCourse" />
              <div class="c-tag">{{ index + 1 }}</div>
              <!-- <div v-show="unitFinished(null)" class="c-done">
                <div class="text1">已完成</div>
              </div> -->
              <div v-show="!(+studentCourseId || index === 0)" class="c-done">
                <div class="text1"><i class="el-icon-lock"></i></div>
              </div>
            </div>
            <div class="c-title">
              {{ item.title }}
            </div>
            <div v-show="item.subtitle" class="c-coin" @click="handleSub(item.subtitle)">
              课时说明>
            </div>
            <div v-show="!item.subtitle" class="c-coin">
              <!-- 占位用 -->
            </div>
            <div class="c-btns swiper-no-swiping">
              <div class="btn1" @click.stop="goBackUp(item, index)">备课</div>
              <div class="btn2" @click.stop="goInClass(item, index)">上课</div>
            </div>
          </div>
          <!-- <div class="a-here">
            <img class="triangle" src="@/assets/digitalbooks/triangle.svg" />
            上到这里
          </div> -->
        </swiper-slide>
        <div slot="button-prev" class="swiper-button-prev swiper-button-white"></div>
        <div slot="button-next" class="swiper-button-next swiper-button-white"></div>
      </swiper>
      <div v-if="lessonList && lessonList.filter(item=>{return item.coursecommUnitResourceList&&item.coursecommUnitResourceList.filter(item1=>{return item1.resourceType==='DIGITAL_TEACH_COURSEWARE'}).length!==0}).length===0" class="w" style="height: 40vh">
        <Empty :msg="'暂无数据'" />
      </div>
    </div>
  </div>
</template>

<script>
import defaultCourse from '@/assets/images/default-cover.jpg'
import 'swiper/css/swiper.css'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import { getBookCatalogueByVersionForLesson, getBook } from '@/api/digital-api.js'
import Empty from '@/components/classPro/Empty/index.vue'

export default {
  components: {
    Swiper,
    SwiperSlide,
    Empty
  },
  data () {
    return {
      defaultCourse,
      swiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
        slidesPerGroup: 3,
        // allowTouchMove: false,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      },
      lessonList: [],
      bookId: 0,
      studentCourseId: 0,
      bookTitle: ''
    }
  },
  computed: {
    swiper () {
      return this.$refs.lineSwiper.$swiper
    },
    // 当前学习进度
    currentProgress () {
      for (var item of this.unitList) {
        if (!this.unitFinished(item)) {
          return item.unitNo
        }
      }
      return this.unitList.length + 1
    }
  },
  mounted () {
    this.bookId = this.$route.query && this.$route.query.id
    this.studentCourseId = this.$route.query && this.$route.query.studentCourseId
    this._getBookCatalogue()
    this._getBook()
  },
  methods: {
    back () {
      if (this.$route.query && this.$route.query.f === 'detail') {
        if (this.$route.query && this.$route.query.f2) {
          this.$router.push(`/classpro/digitalbooks/detail?id=${this.bookId}&f=${this.$route.query.f2}`)
        } else {
          this.$router.push(`/classpro/digitalbooks/detail?id=${this.bookId}`)
        }
      } else {
        this.$router.push('/classpro/myDigitalbooks')
      }
    },
    async _getBook () {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN'
        })
        this.bookTitle = data.title
      }
    },
    async _getBookCatalogue () {
      const { data } = await getBookCatalogueByVersionForLesson({
        bookId: this.bookId
      })
      this.lessonList = data
    },
    onSlideChange () {
      this.activeIndex = this.swiper.activeIndex
    },
    // 单元是否完成
    unitFinished (item) {
      // return (
      //   item.aicourseUnitUser &&
      //   item.aicourseUnitUser.complete &&
      //   item.aicourseUnitUser.total &&
      //   item.aicourseUnitUser.complete >= item.aicourseUnitUser.total &&
      //   (item.aicourseUnitUser.challengeStatus === 'NONE' ||
      //     item.aicourseUnitUser.challengeStatus === 'END')
      // )
    },
    goBackUp (item, index) {
      if (+this.studentCourseId || index === 0) {
        let str = `/digitalbooks/backPack?unitId=${item.id}&courseId=${this.bookId}&studentCourseId=${this.studentCourseId}&r=${item.exerciseRoomId}&d=${item.documentId}`
        if (this.$route.query && this.$route.query.f) {
          str = str + `&f=${this.$route.query.f}`
        }
        if (this.$route.query && this.$route.query.f2) {
          str = str + `&f2=${this.$route.query.f2}`
        }
        this.$router.push(str)
      } else {
        this.$message.warning('您未兑换，暂无权限使用!')
      }
    },
    goInClass (item, index) {
      if (!item.coursecommUnitResourceList || item.coursecommUnitResourceList.filter((item1) => { return item1.resourceType === 'DIGITAL_TEACH_COURSEWARE' }).length === 0) {
        this.$message.warning('当前章节尚未提供配套课件内容')
        return
      }
      if (+this.studentCourseId || index === 0) {
        let str = `/digitalbooks/inClass?unitId=${item.id}&courseId=${this.bookId}&studentCourseId=${this.studentCourseId}&r=${item.roomId}&d=${item.documentId}`
        if (this.$route.query && this.$route.query.f) {
          str = str + `&f=${this.$route.query.f}`
        }
        if (this.$route.query && this.$route.query.f2) {
          str = str + `&f2=${this.$route.query.f2}`
        }
        this.$router.push(str)
      } else {
        this.$message.warning('您未兑换，暂无权限使用!')
      }
    },
    handleSub (item) {
      this.$alert(item, '课时说明', {
        confirmButtonText: '确定',
        callback: action => {
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.attend-class {
  width: 100%;
  height: 100%;
  padding: 10px;

  .head-box {
    height: 40px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;

    .share {
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .back {
      width: 30px;
      height: 30px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: 24px;
      font-weight: 500;
      margin-left: 10px;
    }
  }

  .attend-content {
    padding: 70px 0 0 0;
    box-sizing: border-box;

    .swiper-slide {
      width: 280px;
      padding: 0 10px;
      box-sizing: border-box;
    }

    .swiper-button-prev,
    .swiper-button-next {
      background: rgba(0, 0, 0, 0.4);
      border: 1px solid #FFFFFF;
      border-radius: 6px;
      font-size: var(--font-size-XL) !important;
      padding: 0 3px;
      position: fixed;
      top: auto;
      bottom: 10px;

      &::after {
        font-size: var(--font-size-XL) !important;
      }
    }

    .swiper-button-next {
      right: 15px;
    }

    .swiper-button-prev {
      left: auto;
      right: 55px;
    }

    .a-here {
      font-weight: 500;
      font-size: var(--font-size-XXL);
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #333333;
      margin-top: 15px;

      .triangle {
        width: 30px;
        height: 24px;
      }
    }

    .course-card {
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(79, 79, 79, 0.23);
      // box-shadow: 0px 4px 2px rgba(0, 0, 0, 0.07);
      border-radius: 10px;
      padding: 10px 10px 20px 10px;
      position: relative;

      .c-img {
        width: 100%;
        height: 170px;
        margin-bottom: 10px;
        position: relative;

        .c-tag {
          position: absolute;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 52px;
          height: 45px;
          right: 5px;
          top: 5px;
          background: #F2C94C;
          border: 1px solid #F2C94C;
          box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
          border-radius: 7px;
          font-size: 32px;
          color: #000000;
          font-weight: 500;
          z-index: 4;
        }

        .c-done {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          background: rgba(0, 0, 0, 0.31);
          border-radius: 10px;
          color: #fff;
          z-index: 2;

          .text1 {
            font-weight: 500;
            font-size: 34px;
            color: #FFFFFF;
            margin-bottom: 5px;
          }
        }

        .c-result {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 10px;
          color: #fff;
          z-index: 3;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          padding-bottom: 15px;
          box-sizing: border-box;

          .text2 {
            font-size: 16px;
            color: #FFFFFF;
            text-decoration-line: underline;
          }
        }

        img {
          border-radius: 10px;
        }
      }

      .c-title {
        width: 100%;
        min-height: 50px;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; //需要显示的行数
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
      }

      .c-coin {
        width: 100%;
        height: 60px;
        display: flex;
        justify-content: left;
        align-items: center;
        color: #333333;
        font-size: var(--font-size-XL);
        cursor: pointer;

        .coin {
          width: 30px;
          height: 30px;
          margin-right: 5px;
        }

        .num {
          font-size: 20px;
          font-weight: 500;
          color: #000;
          margin-left: 5px;
        }
      }

      .c-btns {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;

        .btn1,
        .btn2 {
          border-radius: 5px;
          color: #000000;
          width: 70px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          font-weight: 500;
          font-size: 18px;
        }

        .btn1 {
          background: rgba(255, 255, 255, 0.6);
          border: 1px solid #000000;
          margin-right: 30px;
        }

        .btn2 {
          background: linear-gradient(90deg, #FFB347 0%, #FFCC33 100%);
        }
      }
    }
  }
}
</style>
