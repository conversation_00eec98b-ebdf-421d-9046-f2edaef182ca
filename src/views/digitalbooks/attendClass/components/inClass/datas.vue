<template>
  <div class="w h flex">
    <div class="left">
      <div
        v-for="item in filesList"
        :key="item.id"
        :class="{ 'item-active': (+activeIndex === +item.mediaFileId) }"
        class="item-box"
        @click="handleClick(item)"
      >
        {{ item.mediaFile.fileName }}.{{ item.mediaFile.expendType }}
      </div>
    </div>
    <div class="right">
      <template v-if="preInfo">
        <div v-if="preInfo.type === 'IMAGE'" class="w h overflow-hidden flex justify-center items-center">
          <img class="img-cover" :src="preInfo.url" />
        </div>
        <div v-else-if="preInfo.type === 'VIDEO'" class="w h overflow-hidden">
          <video-js :options="videoOptions" @player="videoPlay" />
        </div>
        <div v-else-if="preInfo.expendType === 'pdf'" class="w h overflow-hidden">
          <Pdf ref="pdf" />
        </div>
        <div v-else class="ban">暂不支持预览</div>
      </template>
    </div>
  </div>
</template>

<script>
import { getBookCatalogueResource } from '@/api/digital-api.js'
import VideoJs from '@/components/classPro/h5Video'
import Pdf from '../backpack/pdf.vue'
export default {
  components: {
    Pdf,
    VideoJs
  },
  data () {
    return {
      unitId: this.$route.query.unitId,
      filesList: [],
      preInfo: null,
      activeIndex: 0,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false, //  x5内核浏览器不允许自动播放，需要手动播放
        fileShow: false,
        loop: false,
        fluid: false,
        language: 'zh-CN',
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    }
  },
  mounted () {
    this._getBookCatalogueResource()
  },
  methods: {
    async _getBookCatalogueResource () {
      const { data } = await getBookCatalogueResource({ catalogueId: this.unitId })
      if (data && data.length) {
        this.filesList = data.filter((v) => {
          return v.resourceType === 'DIGITAL_TEACH_RESOURCE'
        })
        this.handleClick(this.filesList[0])
      } else {
        this.filesList = []
      }
    },
    handleClick (item) {
      this.preInfo = item.mediaFile
      this.activeIndex = item.mediaFileId
      if (item.mediaFile.type === 'VIDEO') {
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: item.mediaFile.url,
            type: 'video/mp4'
          }]
        }
      }
      if (item.mediaFile.expendType === 'pdf') {
        this.$nextTick(() => {
          this.$refs.pdf._loadFile(item.mediaFile.url)
        })
      }
    },
    videoPlay (player) {
      player.play()
    }
  }
}
</script>

<style lang="scss" scoped>
.left {
  width: 30%;
  padding: 10px;
  box-sizing: border-box;
  border-right: 1px solid #ddd;

  .item-box {
    min-height: 40px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 10px 5px;
    box-sizing: border-box;
    font-size: 12px;
    cursor: pointer;
    word-wrap: break-word;
    word-break: break-all;

    &:hover {
      background: #2F80ED;
      color: #fff;
    }
  }

  .item-active {
    background: #2F80ED;
    color: #fff;
  }
}

.right {
  width: 70%;
  padding: 10px;

  .ban {
    font-size: 16px;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .img-cover {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
  }
}
</style>
