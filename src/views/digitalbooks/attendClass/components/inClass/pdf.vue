<template>
  <!-- 教案 -->
  <div ref="pdfBox" class="canvas-container">
    <canvas v-for="page in pages" :id="'the-canvas'+page" :key="page" :style="`width: ${width ? width + 'px' : ''}`">
    </canvas>
  </div>
</template>

<script>
const PDFJS = require('pdfjs-dist')
PDFJS.GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.entry.js')
export default {
  props: {
    width: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      pdfDoc: null,
      pages: 1,
      // pdf_src: 'https://static.bingotalk.cn/courses/docs/********************************.pdf'
      pdf_src: ''
    }
  },
  created () {
    this._loadFile(this.pdf_src)
  },
  methods: {
    _renderPage (num) {
      const windowWidth = this.$refs.pdfBox.clientWidth - 10
      this.pdfDoc.getPage(num).then((page) => {
        const canvas = document.getElementById('the-canvas' + num)
        var vp = page.getViewport({ scale: 1 })
        const ctx = canvas.getContext('2d')
        const dpr = window.devicePixelRatio || 1
        const bsr = ctx.webkitBackingStorePixelRatio ||
                    ctx.mozBackingStorePixelRatio ||
                    ctx.msBackingStorePixelRatio ||
                    ctx.oBackingStorePixelRatio ||
                    ctx.backingStorePixelRatio || 1
        const ratio = dpr / bsr
        const viewport = page.getViewport({ scale: windowWidth / vp.width })
        canvas.width = viewport.width * ratio
        canvas.height = viewport.height * ratio
        canvas.style.width = viewport.width + 'px'
        ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
        const renderContext = {
          canvasContext: ctx,
          viewport: viewport
        }
        page.render(renderContext)
        if (this.pages > num) {
          this._renderPage(num + 1)
        }
      })
    },
    _loadFile (url) {
      // this.$showLoading()
      PDFJS.getDocument(url).promise.then((pdf) => {
        // this.$closeLoading()
        this.$emit('success')
        this.pdfDoc = pdf
        this.pages = this.pdfDoc.numPages
        this.$nextTick(() => {
          this._renderPage(1)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.canvas-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  @include scrollBar;
}
</style>
