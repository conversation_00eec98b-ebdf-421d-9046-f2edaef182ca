<template>
  <div ref="rightResize" class="right-resize">
    <i ref="rightResizeBar"></i>
  </div>
</template>

<script>
export default {
  data () {
    return {
      rightAsideWidth: 300
    }
  },
  mounted () {
    const screenWidth = document.body.offsetWidth
    this.rightAsideWidth = Math.floor(screenWidth * 0.5)
    this.dragChangeRightAsideWidth()
  },
  methods: {
    dragChangeRightAsideWidth () {
      // 保留this引用
      const resize = this.$refs.rightResize
      const resizeBar = this.$refs.rightResizeBar
      resize.onmousedown = e => { // 鼠标按下执行的方法
        let startX = e.clientX // 记录赋值横坐标起始位置
        // 鼠标按下颜色改变
        // resize.style.background = '#ccc'
        // resizeBar.style.background = '#aaa'
        resize.style.background = 'transparent'
        resizeBar.style.background = '#ccc'
        resize.left = resize.offsetLeft // 记录距离父辈元素左边缘的距离
        document.onmousemove = e2 => { // 鼠标移动执行的方法
          // 计算并应用位移量
          const endX = e2.clientX // 记录赋值横坐标结束的位置
          const moveLen = startX - endX // 计算鼠标拖动之间的位移差
          // 如果向右拖动且最大不超过1000px，或者向左拖动且做小不小于minWidth ，则执行里面的方法
          const screenWidth = document.body.offsetWidth
          const minWidth = Math.floor(screenWidth * 0.2) // 最新不超过十分之二
          // const maxWidth = Math.floor(screenWidth * 0.8) // 最新不超过十分之八

          if ((moveLen < 0 && this.rightAsideWidth < 1000) || (moveLen > 0 && this.rightAsideWidth > minWidth)) {
            startX = endX // 当前值赋值给初始值
            this.rightAsideWidth -= moveLen // 侧边的宽度加（减）上拖动后的位移量，就是此时的宽度
            if (this.rightAsideWidth < minWidth) { // 设置最小宽度
              this.rightAsideWidth = minWidth
            }
            this.$emit('input', this.rightAsideWidth) // 需要执行的方法，可不写
            this.$emit('change', this.rightAsideWidth)// 宽度改变后 需要把宽度传给父组件
          }
        }
        document.onmouseup = () => { // 鼠标松开，样式复原
          // 颜色恢复
          // resize.style.background = '#fafafa'
          // resizeBar.style.background = '#ccc'
          resize.style.background = 'transparent'
          resizeBar.style.background = '#FFF'
          document.onmousemove = null
          document.onmouseup = null
          this.$emit('mouseup')
        }
        return false
      }
    }
  }
}
</script>

<style scoped>
.right-resize {
  width: 5px;
  height: 100%;
  cursor: w-resize;
  /* background: #fafafa; */
  background: transparent;
  z-index: 999;
}

.right-resize i {
  margin-top: 38vh;
  margin-left: -4px;
  width: 14px;
  height: 56px;
  display: inline-block;
  word-wrap: break-word;
  word-break: break-all;
  /* line-height: 8px; */
  border-radius: 5px;
  border-radius: 16px;
  background: #FFF;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}
</style>
