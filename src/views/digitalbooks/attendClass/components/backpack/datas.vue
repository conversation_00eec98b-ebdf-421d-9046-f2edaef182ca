<template>
  <!-- 我的资源 -->
  <div class="datas-box">
    <div class="head-box">
      <div class="flex justify-end">
        <el-upload
          action="''"
          :before-upload="beforeUpload"
          :show-file-list="false"
          @change="handleChange"
        >
          <div v-if="!progress" class="classpro-btn">上传资源</div>
        </el-upload>
        <div v-if="progress" class="progress">上传中：{{ percent }}%</div>
        <p class="tips">上传文件不能超过2G</p>
      </div>
    </div>
    <div class="d-content">
      <div v-for="item in list" :key="item.id" class="list">
        <div class="items1">
          <div>
            {{ item.mediaFile.fileName }}.{{ item.mediaFile.expendType }}
          </div>
          <div class="size">{{ formatBytes(item.mediaFile.size) }}</div>
        </div>
        <div class="items2">
          <div
            v-show="
              ['VIDEO', 'IMAGE'].indexOf(item.mediaFile.type) !== -1 ||
                item.mediaFile.expendType === 'pdf'
            "
            class="btn"
            @click="preview(item.mediaFile)"
          >
            预览
          </div>
          <div
            class="btn"
            @click="
              downloadFile(
                item.mediaFile.url,
                `${item.mediaFile.fileName}.${item.mediaFile.expendType}`
              )
            "
          >
            下载
          </div>
          <div class="btn ml10" @click="delFile(item)">删除</div>

          <!-- <el-popconfirm title="确定删除吗？" @confirm="delFile(item)">
            <div slot="reference" class="btn ml10">删除</div>
          </el-popconfirm> -->
        </div>
      </div>
      <div
        v-if="list && list.length === 0"
        class="w h flex flex-col justify-center items-center"
      >
        <img class="empty" src="@/assets/images/empty.png" alt="占位图2" />
        暂无数据
      </div>
    </div>

    <div v-if="previewBox" class="preview-box">
      <div class="preview-content">
        <div class="preview-content-head">
          <div @click.stop="previewBox = false">
            <i class="el-icon-close pointer"></i>
          </div>
        </div>
        <div class="preview-content-content">
          <div
            v-if="preInfo.type === 'IMAGE'"
            class="w h overflow-hidden flex justify-center items-center"
          >
            <img class="img-cover" :src="preInfo.url" />
          </div>
          <div v-if="preInfo.type === 'VIDEO'" class="w h overflow-hidden">
            <video-js :options="videoOptions" @player="videoPlay" />
          </div>
          <div v-if="preInfo.expendType === 'pdf'" class="w h overflow-hidden">
            <Pdf ref="pdf" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { getFileUploadAuthor } from '@/api/user-api'
import { throttle } from '@/utils/index'
import VideoJs from '@/components/classPro/h5Video'
import Pdf from './pdf.vue'
import { removeBookCatalogueResource, addBookCatalogueResource } from '@/api/digital-api.js'
export default {
  components: {
    VideoJs,
    Pdf
  },
  props: {
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      previewBox: false,
      unitId: 0,
      fileList: [],
      ossUrl: '',
      progress: false,
      percent: 0,
      preInfo: null,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false, //  x5内核浏览器不允许自动播放，需要手动播放
        fileShow: false,
        loop: false,
        fluid: false,
        language: 'zh-CN',
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    }
  },
  mounted () {
    this.unitId = this.$route.query && this.$route.query.unitId
  },
  methods: {
    preview (item) {
      this.preInfo = item
      if (item.type === 'VIDEO') {
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: item.url,
            type: 'video/mp4'
          }]
        }
      }
      this.previewBox = true
      if (item.expendType === 'pdf') {
        this.$nextTick(() => {
          this.$refs.pdf._loadFile(item.url)
        })
      }
    },
    formatBytes (bytes, decimals = 2) {
      if (bytes === 0) return '0 Bytes'

      const k = 1024
      const dm = decimals < 0 ? 0 : decimals
      const sizes = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
    },
    async delFile (item) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await removeBookCatalogueResource({ catalogueId: this.unitId, resourceLinkId: item.id })
        this.$message.success('删除成功')
        this.$emit('del')
      }).catch(() => {

      })
    },
    downloadFile (path, name) {
      throttle(function () {
        const xhr = new XMLHttpRequest()
        xhr.open('get', path)
        xhr.responseType = 'blob'
        xhr.send()
        xhr.onload = function () {
          if (this.status === 200 || this.status === 304) {
            const fileReader = new FileReader()
            fileReader.readAsDataURL(this.response)
            fileReader.onload = function () {
              const a = document.createElement('a')
              a.style.display = 'none'
              a.href = this.result
              a.download = name
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
            }
          }
        }
      }, 2000)
    },
    async beforeUpload (file) {
      if (file.size >= 2147483648) {
        this.$message.warning('上传文件不能超过2G')
        return
      }
      this.fileList = [...this.fileList, file]

      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }

      const { data } = await this.getOssSign(mediaType, file.name)
      this.ossUrl = data[0].ossConfig.host
      this.progress = true
      const filename = file.name

      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)

      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })

      await addBookCatalogueResource({
        catalogueId: this.unitId
      }, {
        size: Math.floor(file.size / 1024),
        type: mediaType,
        fileName: filename.substring(0, filename.lastIndexOf('.')),
        url: data[0].fileName,
        expendType: filename.substring(filename.lastIndexOf('.') + 1)
      })
      this.$emit('del')

      return Promise.reject()
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    handleChange (info) {
      let fileList = [...info.fileList]
      fileList = fileList.slice(-1)
      this.fileList = fileList
    },
    videoPlay (player) {
      player.play()
    }
  }
}
</script>

<style lang="scss" scoped>
.datas-box {
  width: 100%;
  height: 100%;

  .head-box {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: flex-end;
    position: relative;
    align-items: center;
    .tips {
      position: absolute;
      right: 8px;
      top: 30px;
      font-size: var(--font-size-S);
    }
    .classpro-btn {
      padding: 5px 15px;
    }

    .progress {
      font-size: var(--font-size-L);
    }
  }

  .d-content {
    height: calc(100% - 40px);
    overflow-y: auto;
    @include scrollBar;

    .list {
      border-bottom: 1px solid #e0e0e0;
      padding: 30px 0;
      display: flex;
      align-items: center;

      .size {
        color: #828282;
        width: 100px;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        padding-left: 10px;
        box-sizing: border-box;
      }

      .items1 {
        width: calc(100% - 120px);
        display: flex;
        justify-content: space-between;
        font-size: var(--font-size-XL);
      }

      .items2 {
        width: 120px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: var(--font-size-XL);

        .btn {
          color: #2f80ed;
          text-decoration-line: underline;
          margin-left: 10px;
          cursor: pointer;

          &:first-child {
            margin: 0;
          }
        }

        .ml10 {
          margin-left: 10px !important;
        }
      }
    }
  }

  .preview-box {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 100px;
    box-sizing: border-box;
    background: rgba($color: #000000, $alpha: 0.5);

    .preview-content {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;
      box-sizing: border-box;
      overflow-y: auto;
      @include scrollBar;

      .preview-content-head {
        height: 40px;
        display: flex;
        justify-content: flex-end;

        .el-icon-close {
          font-size: var(--font-size-XXL);
        }
      }

      .preview-content-content {
        height: calc(100% - 40px);
        overflow-y: auto;

        .img-cover {
          max-width: 100%;
          max-height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  .empty {
    width: 126px;
    height: 126px;
    margin-bottom: 15px;
  }

  .empty-text {
    font-weight: 400;
    font-size: 14px;
    color: #8c8c8c;
  }
}
</style>
