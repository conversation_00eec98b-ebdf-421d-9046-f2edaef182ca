<template>
  <!-- 教案 -->
  <div class="w h">

    <div v-if="pages" ref="pdfBox" class="canvas-container">
      <div class="download-box">
        <div class="edu-btn" @click="downloadFile(pdf_src, pdf_name)">下载</div>
      </div>
      <canvas v-for="page in pages" :id="'the-canvas' + page" :key="page">
      </canvas>
    </div>
    <div v-if="!pages" class="w h flex flex-col justify-center items-center">
      <img class="empty" src="@/assets/images/empty.png" alt="占位图2" />
      暂无数据
    </div>
  </div>
</template>

<script>
import { downloadFile } from '@/utils/index'
const PDFJS = require('pdfjs-dist')
PDFJS.GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.entry.js')
export default {
  data () {
    return {
      pdfDoc: null,
      pages: 0,
      // pdf_src: 'https://static.bingotalk.cn/courses/docs/********************************.pdf'
      pdf_src: '',
      pdf_name: ''
    }
  },
  created () {
    // this._loadFile(this.pdf_src)
  },
  methods: {
    _renderPage (num) {
      const windowWidth = this.$refs.pdfBox.clientWidth - 10
      this.pdfDoc.getPage(num).then((page) => {
        const canvas = document.getElementById('the-canvas' + num)
        var vp = page.getViewport({ scale: 1 })
        const ctx = canvas.getContext('2d')
        const dpr = window.devicePixelRatio || 1
        const bsr = ctx.webkitBackingStorePixelRatio ||
          ctx.mozBackingStorePixelRatio ||
          ctx.msBackingStorePixelRatio ||
          ctx.oBackingStorePixelRatio ||
          ctx.backingStorePixelRatio || 1
        const ratio = dpr / bsr
        const viewport = page.getViewport({ scale: windowWidth / vp.width })
        canvas.width = viewport.width * ratio
        canvas.height = viewport.height * ratio
        canvas.style.width = viewport.width + 'px'
        ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
        const renderContext = {
          canvasContext: ctx,
          viewport: viewport
        }
        page.render(renderContext)
        if (this.pages > num) {
          this._renderPage(num + 1)
        }
      })
    },
    _loadFile (url, name) {
      // this.$showLoading()
      this.pdf_src = url
      this.pdf_name = name
      if (!url) {
        this.pdfDoc = null
        this.pages = 0
        return
      }
      PDFJS.getDocument(url).promise.then((pdf) => {
        // this.$closeLoading()
        this.pdfDoc = pdf
        this.pages = this.pdfDoc.numPages
        this.$nextTick(() => {
          this._renderPage(1)
        })
      })
    },
    downloadFile
  }
}
</script>

<style lang="scss" scoped>
.canvas-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  @include scrollBar;
}

.empty {
  width: 126px;
  height: 126px;
  margin-bottom: 15px;
}

.empty-text {
  font-weight: 400;
  font-size: 14px;
  color: #8C8C8C;
}

.download-box {
  height: 40px;
  display: flex;
  justify-content: flex-end;
  padding: 0 10px;
  font-size: 14px;

  .edu-btn {
    width: 80px;
  }
}
</style>
