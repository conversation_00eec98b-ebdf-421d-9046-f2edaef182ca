<template>
  <!-- 课件 -->
  <div v-loading="loading" class="w h">
    <div ref="ratioBox" class="radio-box">
      <div ref="box" class="ppt-box">
        <div id="whiteboard" class="w h"></div>
        <div v-if="!hasData" class="w h flex flex-col justify-center items-center emty">
          <img class="empty" src="@/assets/images/empty.png" alt="占位图2" />
          暂无数据
        </div>
        <div v-else class="w h flex flex-col justify-center items-center emty">
          <img class="loadingImg" src="@/assets/images/loading.gif" alt="占位图2" />
          加载中
        </div>
      </div>
    </div>

    <div v-if="documentId" class="control-box">
      <div @click="pageChange('-')"> <img src="@/assets/digitalbooks/ppt-left.svg" /></div>
      <div>{{ currPage + 1 }} / {{ total }}</div>
      <div @click="pageChange('+')"> <img src="@/assets/digitalbooks/ppt-right.svg" /></div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import WhiteClient from 'sdk/whiteBoard'
// import { getGenerateToken, getDocumentInfo } from '@/api/classroom-api'
import { generateWebofficeToken, refreshWebofficeToken } from '@/api/aliyun-api'
export default {
  data () {
    return {
      currPage: -1,
      total: 0,
      boardClient: null,
      scenes: null,
      sceneUUid: null,
      tokenList: null,
      token: {
        boardToken: '',
        boardUuid: ''
      },
      pageList: {
        show: false
      },
      useLocalFont: false,
      documentId: 0,
      loading: false,
      accessToken: '',
      hasData: false
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  mounted () {
    this.keepRatio()
    // this._getGenerateToken()
  },
  methods: {
    keepRatio () {
      const w = this.$refs.ratioBox.clientWidth - 40
      const h = this.$refs.ratioBox.clientHeight - 40
      let h2 = w * (3 / 4)
      let w2 = w
      if (h2 > h) {
        // 当高度超出一屏的范围时 取该高度下最合适的宽度
        w2 = h * (4 / 3)
        h2 = h
      }
      this.$refs.box.style.width = Math.floor(w2) + 'px'
      this.$refs.box.style.height = Math.floor(h2) + 'px'
    },
    async _getGenerateToken (list, token) {
      if (token) {
        this.accessToken = 'Bearer ' + token
      }
      if (list.length === 0) {
        return false
      }
      this.hasData = true
      const header = this.accessToken === '' ? {} : { authorization: this.accessToken }
      console.log(list[0])
      const { data } = await generateWebofficeToken({
        fileUrl: list[0].mediaFile.url,
        coursecommUnitResourceId: list[0].id
      }, header)
      this.tokenList = data.body
      this.weboffice({
        AccessToken: data.body.accessToken,
        WebofficeURL: data.body.webofficeURL
      })
    },
    weboffice(tokenInfo) {
      var mount = document.getElementById('whiteboard')
      var demo = window.aliyun.config({
        mount: mount,
        url: tokenInfo.WebofficeURL,
        refreshToken: this.refreshTokenPromise
      })
      demo.setToken({
        token: tokenInfo.AccessToken,
        timeout: 25 * 60 * 1000 // Token过期时间，单位为ms。25分钟之后刷新Token。
      })
    },
    async refreshTokenPromise() {
      const header = this.accessToken === '' ? {} : { authorization: this.accessToken }
      const { data } = await refreshWebofficeToken({
        accessToken: this.tokenList.accessToken,
        refreshToken: this.tokenList.refreshToken
      }, header)
      return {
        token: data.body.accessToken,
        timeout: 10 * 60 * 1000
      }
    },
    // async _getDocumentInfo (documentId) {
    //   const { data } = await getDocumentInfo(documentId)
    //   this.getDocument(data && data.scenes)
    // },
    // getDocument (scenes) {
    //   try {
    //     if (scenes) {
    //       const newScenes = JSON.parse(scenes)
    //       const pptScenes = []
    //       if (newScenes.progress.convertedFileList.length > 0) {
    //         newScenes.progress.convertedFileList.map((f, i) => {
    //           pptScenes.push({
    //             name: 'page' + i,
    //             ppt: {
    //               src: f.conversionFileUrl,
    //               width: f.width,
    //               height: f.height,
    //               previewURL: f.preview
    //             }
    //           })
    //         })
    //         this.scenes = pptScenes
    //         this.sceneUUid = newScenes.uuid
    //       }
    //     }
    //     this.joinRoom()
    //   } catch (error) {
    //     console.error(error)
    //   }
    // },
    async joinRoom () {
      try {
        const option = this.useLocalFont ? {
          pptParams: {
            useServerWrap: false
          }
        } : {}
        this.boardClient = new WhiteClient(option)
        const joinRoomParams = {
          uuid: this.token.boardUuid,
          uid: this.id + '',
          roomToken: this.token.boardToken,
          disableCameraTransform: true
        }
        const room = await this.boardClient.joinRoom(joinRoomParams, {
          onPhaseChanged: (phase) => {
            if (phase === 'connected') {
              setTimeout(() => {
                this.$room.moveCamera({ centerX: 0, centerY: 0 })
                this.$room.refreshViewSize()
                this.$room.scalePptToFit('continuous')
              }, 1000)
            }
          },
          onRoomStateChanged: state => {
            if (state.sceneState) {
              this.currPage = state.sceneState.index
            }
          }
        })
        if (this.scenes && this.scenes.length > 0) {
          if (room.state.sceneState.scenePath === '/init' || room.state.globalState.__pptState.uuid !== this.sceneUUid) {
            room.putScenes(`/${this.sceneUUid}`, this.scenes)
            room.setScenePath(`/${this.sceneUUid}/${this.scenes[0].name}`)
          }
        }
        room.bindHtmlElement(document.getElementById('whiteboard'))
        room.setMemberState({ currentApplianceName: 'selector' })
        this.total = room.state.sceneState.scenes.length
        room.setSceneIndex(0)
        this.currPage = room.state.sceneState.index
        this.$room = room
        this.refreshView()
        this.loading = false
      } catch (error) {
        this.loading = false
        console.log(error)
      }
    },
    pageChange (type) {
      switch (type) {
        case '-':
          // if (this.currPage === 0) return
          // this.currPage--
          this.$room.pptPreviousStep()

          break
        case '+':
          // if (this.currPage + 1 === this.total) return
          // this.currPage++
          this.$room.pptNextStep()
          break
        default:
          this.currPage = +type - 1
          this.$room.setSceneIndex(this.currPage)
      }
      this.currPage = this.$room.state.sceneState.index
    },
    refreshView () {
      this.$room.moveCamera({ centerX: 0, centerY: 0 })
      this.$room.refreshViewSize()
      this.$room.scalePptToFit('continuous')
    }
  }
}
</script>

<style lang="scss" scoped>

.radio-box {
  width: 100%;
  height: calc(100%);
  display: flex;
  justify-content: center;
  // align-items: center;
  .ppt-box {
    width: 100% !important;
    height: 100% !important;
    position: relative;
    // background-color: #eee;
    #whiteboard{
      position: absolute;
      z-index: 11;
    }
    ::v-deep iframe{
      width: 100% !important;
      height: 100% !important;
      z-index: 99;
    }
    .loading{
      .loadingImg{
        width: 150px;
      }
    }
    .emty{
      position: absolute;
      left: 0;
      top:0
    }
  }
}

.control-box {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-XXL);

  img {
    width: 45px;
    height: 45px;
    margin: 10px 10px 0 10px;
    cursor: pointer;
  }
}

.empty {
  width: 126px;
  height: 126px;
  margin-bottom: 15px;
}

.empty-text {
  font-weight: 400;
  font-size: 14px;
  color: #8C8C8C;
}
</style>
