<template>
  <div ref="tools" class="ai-tools">
    <div v-if="showTools" class="ai-tools-box">
      <div class="tool-item" :class="{'a-tool-item':active === 0}" @click="hanleClick(0)">
        <img :src="active === 0 ? Apk : pk" />
        分组PK
      </div>

      <div class="tool-item" :class="{'a-tool-item':isDraw}" @click="hanleDraw">
        <img :src="isDraw ? Apen : pen" />
        画笔
      </div>

      <div class="tool-item" :class="{'a-tool-item':active === 1}" @click="hanleClick(1)">
        <img :src="active === 1 ? Atime : time" />
        倒计时
      </div>

      <div class="tool-item" :class="{'a-tool-item':active === 2}" @click="hanleClick(2)">
        <img :src="active === 2 ? Aquestion : question" />
        抽问
      </div>

      <div class="tool-item" :class="{'a-tool-item':active === 3}" @click="startAnimate">
        <img :src="active === 3 ? Ahecai : hecai" />
        喝彩
      </div>
      <div class="tool-item" :class="{'a-tool-item':active === 4}" @click="hanleClick(4)">
        <img :src="active === 4 ? Ascreen : Screen" />
        投屏
      </div>
      <div v-show="quickPkShow" class="ai-tools-pk" @click="handleQuickPk">
        <img src="@/assets/digitalbooks/tools-img/pk-icon.png" />
      </div>
    </div>
    <img class="open" :src="open" @click="showTools=!showTools" />
    <div class="ai-tools-items">
      <Pk v-if="active === 0" ref="pk" :unit-id="unitId" @close="active = -1" @quickPkReload="_getUserCatalogue" />
      <Time v-if="active === 1" @close="active = -1" />
      <lotterys v-else-if="active === 2" @close="active = -1" />
      <screeen v-else-if="active === 4" :unit-id="unitId" @close="active = -1" />
    </div>

    <div v-if="showAnimate" class="svga-box">
      <div v-if="showSvga" class="shadow-box2"></div>
      <div id="svga-animate"></div>
      <audio ref="audioAnimate" controls="controls" hidden></audio>
    </div>

  </div>
</template>

<script>
import SVGA from 'svgaplayerweb'

import pen from '@/assets/digitalbooks/tools-img/pen.png'
import Apen from '@/assets/digitalbooks/tools-img/pen-active.png'
import pk from '@/assets/digitalbooks/tools-img/pk.png'
import Apk from '@/assets/digitalbooks/tools-img/pk-active.png'
import time from '@/assets/digitalbooks/tools-img/time.png'
import Atime from '@/assets/digitalbooks/tools-img/time-active.png'
import question from '@/assets/digitalbooks/tools-img/question.png'
import Aquestion from '@/assets/digitalbooks/tools-img/question-active.png'
import hecai from '@/assets/digitalbooks/tools-img/hecai.png'
import Ahecai from '@/assets/digitalbooks/tools-img/hecai-active.png'
import Help from '@/assets/digitalbooks/tools-img/help.png'
import close from '@/assets/digitalbooks/tools-img/video-close.svg'
import open from '@/assets/digitalbooks/tools-img/open.png'
import dianzan from '@/assets/audio/dianzan.mp3'
import guzhang from '@/assets/audio/guzhang.mp3'
import Screen from '@/assets/digitalbooks/tools-img/screen.png'
import Ascreen from '@/assets/digitalbooks/tools-img/screen_active.png'
import screeen from './components/screeen.vue'
import { getUserCatalogue } from '@/api/digital-api.js'
import Time from './components/time.vue'
import Lotterys from './components/lotterys.vue'
import Pk from './components/pk.vue'

export default {
  components: {
    Time,
    Lotterys,
    Pk,
    screeen
  },
  props: {
    unitId: {
      type: [String, Number],
      default: 0
    }
  },
  data () {
    return {
      isDraw: false,
      quickPkShow: false,
      active: -1,
      close,
      Help,
      pen,
      Apen,
      pk,
      Apk,
      time,
      Atime,
      question,
      Aquestion,
      hecai,
      Ahecai,
      open,
      Ascreen,
      Screen,
      groupNum: 0,
      showAnimate: false,
      showSvga: false,
      showTools: false,
      svgaFiles: [
        { 'name': '点赞', 'url': 'https://static.bingotalk.cn/courses/courseware/63ec9da875703.svga', 'music': dianzan },
        { 'name': '鼓掌', 'url': 'https://static.bingotalk.cn/courses/courseware/63ec9f64e508a.svga', 'music': guzhang }
      ]
    }
  },
  watch: {
    unitId: {
      handler (val) {
        if (val) {
          this._getUserCatalogue()
        }
      }
    }
  },
  mounted () {
    this.$bus.$on('click', () => {
      this.active = -1
    })
    setTimeout(() => {
      if (this.unitId) {
        this._getUserCatalogue()
      }
    }, 0)
    // 预加载
    this.prefetch()
  },
  methods: {
    async _getUserCatalogue () {
      const { data } = await getUserCatalogue({
        studentCourseId: this.$route.query.studentCourseId,
        catalogueId: this.unitId
      })
      if (data && data.groupNum) {
        this.groupNum = data.groupNum
        this.quickPkShow = true
      } else {
        this.quickPkShow = false
      }
    },
    hanleClick (val) {
      this.active = val
    },
    hanleDraw () {
      this.isDraw = !this.isDraw
      this.$emit('handleActive', this.isDraw)
    },
    closeDraw () {
      this.isDraw = false
    },
    handleTools (e) {
      if (!this.$refs.tools.contains(e.target)) this.active = -1
    },
    handleQuickPk () {
      this.active = 0
      this.$nextTick(() => {
        this.$refs.pk.active = 'pkDetail'
        this.$refs.pk.handelGroupItem(this.groupNum)
      })
    },
    startAnimate () {
      const self = this
      const randomIndex = Math.floor(Math.random() * this.svgaFiles.length)
      const item = this.svgaFiles[randomIndex]
      self.showAnimate = true
      this.$nextTick(() => {
        const audio = this.$refs.audioAnimate
        audio.volume = 0.1
        audio.src = item.music
        var parser = new SVGA.Parser('#svga-animate')
        var player = new SVGA.Player('#svga-animate')
        player.loops = 1
        player.onFinished(() => {
          self.showSvga = false
          self.showAnimate = false
          audio.pause()
          player.clear()
        })
        parser.load(
          item.url,
          function (videoItem) {
            self.showSvga = true
            audio.play()
            player.setContentMode('Fill')
            player.setVideoItem(videoItem)
            player.startAnimation()
          },
          function (error) {
            console.log(error)
          }
        )
      })
    },
    prefetch () {
      for (const img of this.svgaFiles) {
        const link = document.createElement('link')
        link.rel = 'prefetch'
        link.href = img.url
        document.head.appendChild(link)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-tools {
  position: absolute;
  z-index: 1000;
  right: 10px;
  top: 10%;
  .open{
    width: 40px;
    position: absolute;
    right: 0;
    top:350px;
    cursor: pointer;
  }
  .ai-tools-box {
    width: 73px;
    height: 350px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid #FFFFFF;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 10px;
    color: #333333;
    font-weight: 500;
    font-size: 12px;
    padding: 20px 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    position: relative;

    .bg-filter {
      background: rgba(64, 183, 242, .61);
      filter: blur(13px);
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .a-tool-item {
      color: #2D9CDB;
    }

    .tool-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      position: relative;
      margin: 5px;
      img {
        width: 24px;
        height: 24px;
        margin-bottom: 5px;
      }
    }

    .tool-help {
      position: absolute;
      bottom: - 34px;
      width: 70px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      background: rgba(255, 255, 255, 0.60);
      cursor: pointer;

      img {
        width: 15px;
        height: 15px;
        margin-right: 3px;
        margin-top: -2px;
      }
    }
  }

  .ai-tools-items {
    position: absolute;
    right: 85px;
    top: 0;
    bottom: 0;
    // display: inline-block;
    display: flex;
    align-items: center;
  }

  .ai-tools-pk {
    position: absolute;
    right: 1px;
    bottom: 350px;
    display: inline-block;
    width: 70px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    img {
      width: 58px;
      height: 46px;
    }
  }
  .svga-box{
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba($color: #000000, $alpha: 0.8);

    .shadow-box2 {
      width: 100%;
      height: 100%;
      // background-color: rgba($color: #000000, $alpha: 0.8);
    }

    #svga-animate {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }

}
.navbar-video {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999999;

    .shadow {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.2);
    }

    .video-container {
      position: absolute;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50%;
      width: 633px;
      height: 415px;
      z-index: 9999;

      .close {
        position: absolute;
        object-fit: contain;
        top: 12px;
        right: 8px;
        width: 16px;
        height: 16px;
        cursor: pointer;
        z-index: 99999;

        .close-icon {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
</style>
