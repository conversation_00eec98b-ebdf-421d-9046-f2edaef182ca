<template>
  <div>
    <div v-show="isOpen" class="lottery-bg-box">
      <div class="lottery-bg no-conversion">
        <div class="pointer" @click="close"><i class="el-icon-error" style="color: #BDBDBD;"></i></div>
        <div class="left" @click="creatQrCode">
          <p class="left_title">微信扫码投屏</p>
          <div id="qrcode"></div>
        </div>
        <viewer v-if="fileList.length!==0" :options="options" :images="fileList" class="right" @hide="isOpen=true">
          <div v-for="(file, index) in fileList" :key="index" :src="file.url" class="file-box">
            <div class="pointer" @click="deleteImg(index)"><i class="el-icon-error" style="color: #BDBDBD;"></i></div>
            <div v-if="isVideo(file.url)" class="video" @click="playVideo(file.url)"><i class="el-icon-video-play" style="color:#BDBDBD;"></i></div>
            <img
              v-if="isVideo(file.url)"
              :key="file.url"
              :src="file.url+'?x-oss-process=video/snapshot,t_1000,m_fast'"
              :alt="file.fileName"
            />
            <img
              v-if="isImage(file.url)"
              :src="file.url"
              :alt="file.fileName"
            />
          </div>
        </viewer>
        <div v-if="fileList.length===0" class="flex flex-col justify-center items-center" style="height: 100%;width: 100%;">
          <Empty :msg="'暂无数据'" />
        </div>
      </div>
      <div v-if="showVideo" class="videoPlay">
        <div class="pointer" @click="showVideo=false"><i class="el-icon-error" style="color: #BDBDBD;font-size: 40px;"></i></div>
        <video :src="videoSrc" controls></video>
      </div>
      <div>
      </div>
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import { castingShareFile, getCastingShareFileList } from '@/api/digital-api'
import 'viewerjs/dist/viewer.css'
import VueViewer from 'v-viewer'
import Vue from 'vue'
import Empty from '@/components/classPro/Empty/index.vue'
Vue.use(VueViewer)
export default {
  components: {
    Empty
  },
  props: {
    unitId: {
      type: [String, Number],
      default: 0
    }
  },
  data () {
    return {
      fileList: [],
      timer: null,
      host: '',
      key: '',
      isOpen: true,
      videoSrc: '',
      showVideo: false,
      options: {
        show: () => {
          this.isOpen = false
        },
        hide: () => {
          this.isOpen = true
        }
      }
    }
  },
  async mounted () {
    await this.getList()
    this.creatQrCode()
  },
  beforeDestroy () {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    playVideo (url) {
      this.videoSrc = url
      this.showVideo = true
    },
    deleteImg (index) {
      try {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await castingShareFile(this.fileList[index], {
            key: this.key,
            apiType: 'delete'
          })
          this.fileList.slice(index, 1)
        }).catch(() => {
        })
      } catch (error) {
        console.log(error)
      }
    },
    show () {
      this.$viewerApi({
        images: this.fileList
      })
    },
    isImage (url) {
      return /\.(jpeg|jpg|gif|png|svg|webp|bmp)$/i.test(url)
    },
    isVideo (url) {
      return /\.(mp4|mov|avi|wmv|flv|mkv|webm)$/i.test(url)
    },
    async getList () {
      this.timer = setInterval(async () => {
        const { data } = await getCastingShareFileList({ castingType: 'AI_CASTING', sourceId: this.unitId })
        if (this.fileList.length === data.mediaFileList.length) {
          return
        }
        this.fileList = data.mediaFileList || []
      }, 2000)
    },
    close () {
      clearInterval(this.timer)
      this.timer = null
      this.$emit('close')
    },
    async creatQrCode () {
      const { data } = await getCastingShareFileList({ castingType: 'AI_CASTING', sourceId: this.unitId })
      this.host = data.uploadH5Url
      this.key = data.key
      this.fileList = data.mediaFileList || []
      this.$nextTick(() => {
        var codeHtml = document.getElementById('qrcode')
        codeHtml.innerHTML = ''
        new QRCode(
          codeHtml,
          {
            text: this.host,
            width: 150,
            height: 150,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          }
        )
        codeHtml.removeAttribute('title')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
    .empty-text {
    font-size: 25px;
    color: #8A96A3;
    line-height: 30px;
  }

  .empty-img {
    width: 160px;
    height: 160px;
    object-fit: contain;
  }
    .el-icon-error {
      font-size: 12px;
      cursor: pointer;
    }

    .lottery-bg-box {
      position: fixed;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(0,0,0,0.72);
    }
    .pointer{
      position: absolute;
      right: 5px;
      top:-5px;
      cursor: pointer;
      z-index: 9;
    }
    .video{
      position: absolute;
      left: 30px;
      top: 30px;
      font-size: 40px;
      cursor: pointer;
    }
    .videoPlay{
      width: 100%;
      height: 100%;
      position: fixed;
      left: 0;
      top:0;
      z-index: 100000;
      background: #000;
      video{
          width: 100%;
          height: 100%;
      }
    }
    .lottery-bg {
      background: rgba(255, 255, 255, 1);
      border: 1px solid #FFFFFF;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      border-radius: 10px;
      width: 600px;
      height: 350px;
      padding: 15px 10px 10px 10px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      color: #000;
      font-size: 20px;
      position: relative;
      .left{
          width: 200px;
          height: 100%;
          position: relative;
          border-right: 1px solid #DFDFDF;
          overflow: hidden;
          .left_title{
              width: 100%;
              text-align: center;
              margin-top: 80px;;
              font-size: 12px;
          }
          #qrcode{
              position: absolute;
              left: 50%;
              top:50%;
              transform: translate(-50%, -50%);
          }
      }
      .right{
          width: 585px;
          height: 100%;
          display: flex;
          padding: 5px;
          justify-content: flex-start;
          align-content: flex-start;
          flex-wrap: wrap;
          gap: 5px;
          margin-top: 5px;
         .file-box{
          width: 100px;
          height: 100px;
          background: #f8f8f8;
          position: relative;
         }
          img{
               width: 100%;
               height: 100%;
              object-fit: cover;
          }
      }
    }

    </style>
    <style>
   .viewer-container{
      z-index: 1000 !important;
  }
  </style>
