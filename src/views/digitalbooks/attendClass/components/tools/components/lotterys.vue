<template>
  <div class="lottery-bg-box">
    <div class="lottery-bg">
      <audio ref="startAu" controls="controls" hidden :src="startAu"></audio>
      <audio ref="endAu" controls="controls" hidden :src="endAu"></audio>
      <div class="t-title">
        <div>
          抽问
          <span>随机选人</span>
        </div>
        <div @click="close">
          <i class="el-icon-error" style="color: #BDBDBD;"></i>
        </div>
      </div>
      <div class="w flex justify-center lottery-box-div">
        <div v-if="hasStuList" v-loading="load" class="lottery-box" element-loading-background="rgba(0, 0, 0, 0.8)">
          <div class="lottery-name-box f26">
            <div class="relative w h">
              <div ref="boxs" class="boxs">
                <div v-for="(box, key1) in boxs" :key="key1" class="box">
                  <div class="items" :style="{ top: box.top + '%' }">
                    <div v-for="(item, key2) in items" :key="key2" class="item">
                      <div class="text">
                        <span>{{ item.name }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="lottery-btn-box">
            <div v-if="!runStatus" class="lottery-btn" @click="start">开始抽选</div>
            <div v-else class="lottery-btn" @click="stop">停止</div>
          </div>
        </div>
        <div v-else v-loading="load" class="lottery-box" element-loading-background="rgba(0, 0, 0, 0.8)">
          <div class="lottery-name-box flex justify-center items-center f26">没有学生名单</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import lodash from 'lodash'
import { getUserLotteryInfo } from '@/api/digital-api.js'
import startAu from '@/assets/audio/lottery/start.mp3'
import endAu from '@/assets/audio/lottery/end.mp3'
export default {
  data () {
    return {
      hasStuList: true,
      lotteryShow: false,
      closeTimer: 0,
      djs: 0, // 倒计时后可停止

      step: 10, // 初始步长
      runStatus: false, // 运动状态
      startTime: 0, // 开始时刻
      boxsNum: 1, // 盒子个数（目前只有一个）
      boxs: [ // 盒子列表
        // {top: 0, timer: 0, winner: -1, ending: false},
        // {top: 0, timer: 0, winner: -1, ending: false},
      ],
      rosterNum: 0, // 学生个数
      rosterList: [ // 学生列表
        // '张三',
        // '李四'
      ],
      items: [ // 显示学生列表（比实际多一个）
        // { name: '张三' },
        // { name: '李四' },
        // { name: '张三' },
      ],
      startAu,
      endAu,
      load: false
    }
  },
  mounted () {
    this._getUserLotteryInfo()
  },
  methods: {
    close () {
      this.$emit('close')
    },
    async _getUserLotteryInfo () {
      try {
        this.load = true
        const { data } = await getUserLotteryInfo({ userType: 'STUDENT' })
        if (data.userExtra && data.userExtra.studentList) {
          this.hasStuList = true
          this.rosterList = data.userExtra.studentList.split(',')
        } else {
          this.hasStuList = false
          this.rosterList = []
        }
        this.rosterNum = this.rosterList.length
        this.initItems()
        this.initBoxs()
        this.load = false
      } catch (error) {
        this.hasStuList = false
        this.rosterList = []
        this.load = false
      }
    },

    // 初始候选者列表
    initItems () {
      this.items = []
      this.rosterList.forEach((name) => {
        this.items.push({ name })
      })
      // 第一个复制一份到最后，来过渡
      this.items.push(this.items[0])
    },

    // 设置盒子个数
    initBoxs () {
      // 限制盒子个数
      const maxNum = this.rosterNum > 5 ? 5 : this.rosterNum
      if (this.boxsNum > maxNum) {
        this.boxsNum = 0
      }
      // 组装每个盒子
      this.boxs = []
      for (let i = 0; i < this.boxsNum; i++) {
        const box = { top: 0, timer: 0, winner: -1, ending: false }
        this.boxs.push(box)
      }
    },

    // 计算中奖名单
    getWinner () {
      // 候选下标
      let candidate = [] // 比如 4个学生时[0, 1, 2, 3]
      for (let i = 0; i < this.rosterNum; i++) {
        candidate.push(i)
      }
      // 打乱数组
      candidate = lodash.shuffle(candidate)
      // 获奖下标，每个盒子一个中奖者
      const winner = candidate.slice(0, this.boxsNum)
      // console.log('获奖名单数组：' + JSON.stringify(winner))
      return winner // 比如 2个盒子时[2, 0]
    },

    // 开始抽奖
    start () {
      // 必须所有都已停止
      let running = false
      this.boxs.forEach((box) => {
        if (box.timer > 0) {
          running = true
        }
      })
      if (running) {
        this.$message({
          message: '已有正在进行的抽问',
          type: 'warning'
        })
        return false
      }
      // 中奖名单
      const winner = this.getWinner()
      // 全部启动
      this.runAll(winner)
    },

    // 停止抽奖
    stop () {
      // 全部停止
      this.endAll()
    },

    // 全部启动
    runAll (winner) {
      this.runStatus = true
      clearTimeout(this.closeTimer)
      // 3秒后才能停止
      this.djs = 3
      const djsInter = setInterval(() => {
        this.djs--
        if (this.djs === 0) {
          clearInterval(djsInter)
        }
      }, 1000)
      // 运动开始时间
      this.startTime = new Date().getTime()
      // 逐个开始运动
      this.boxs.forEach((box, index) => {
        box.winner = winner[index] // 获奖者编号
        this.run(box)
      })
    },

    // 全部停止
    endAll () {
      this.runStatus = false
      this.playAudio()
    },

    playAudio () {
      const startAu = this.$refs.startAu
      startAu.volume = 0.1
      const endAu = this.$refs.endAu
      endAu.volume = 0.1
      if (!startAu.paused) {
        startAu.load()
      }
      startAu.play()
      setTimeout(() => {
        if (!startAu.paused) {
          startAu.load()
        }
        endAu.play()
      }, 2000)
    },

    // 动画效果
    run (box) {
      // 开始运动
      box.top -= this.step
      // 反复循环
      if (box.top === -this.rosterNum * 100) {
        box.top = 0
      }
      // 点击停止
      if (!this.runStatus) {
        if (!box.ending) {
          // 状态设为正在停止中
          if (box.top % 100 === 0) {
            // 瞬间移动到中奖位置的前一个
            const winnerBefore = (box.winner - 1) >= 0 ? (box.winner - 1) : (this.rosterNum - 1)
            box.top = -winnerBefore * 100
            box.ending = true
          }
        } else {
          // 如果中奖
          if (box.top === -box.winner * 100) {
            clearTimeout(box.timer)
            box.timer = 0
            box.ending = false
            return false
          }
        }
      }

      // 已过时间
      const overTime = new Date().getTime() - this.startTime
      // 最多20秒自动停止（防止长时间不停或学生端没收到停止指令）
      if (overTime > 20000 && this.runStatus) {
        this.endAll()
      }
      // 初始间隔
      let speed = 150
      // 变速算法
      if (!box.ending) {
        speed = parseInt(speed / (parseInt(overTime / 200) + 1)) + 1
      } else {
        // 停止过程中保持这种慢速
        speed = 150
      }
      // 继续运动
      box.timer = setTimeout(() => {
        this.run(box)
      }, speed)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-icon-error {
  font-size: 20px;
  cursor: pointer;
}

.lottery-bg-box {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0,0,0,0.72);
}
.lottery-bg {
  background: rgba(255, 255, 255, 1);
  border: 1px solid #FFFFFF;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  width: 400px;
  height: 350px;
  padding: 15px 10px 10px 10px;
  box-sizing: border-box;
  color: #000;
  font-size: 20px;

  .t-title {
    height: 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    img {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
    span {
      font-size: 12px;
      padding-left: 5px;
    }
  }
  .lottery-box {
    background: rgba(252, 218, 9, 0.14);
    border: 5px solid #F2C94C;
    box-shadow: inset 0px 0px 16px rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    width: 100%;
    height: 220px;
  }

  .lottery-box-div {
    padding: 0 20px;
    box-sizing: border-box;
  }

  .lottery-btn-box {
    margin-top: 20px;
    display: flex;
    width: 100%;
    justify-content: center;
  }

  .lottery-name-box {
    width: 100%;
    height: 100%;
    text-align: center;

    .boxs {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0%;
      display: flex;
      overflow: hidden;
      // border: 1px solid green;
      .box {
        width: 100%;
        height: 100%;
        overflow: hidden;
        .items {
          position: absolute;
          width: 100%;
          height: 100%;
          .item {
            width: 100%;
            height: 100%;
            // padding: 5% 0;
            .text {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              // padding: 0 10px;
              span {
                color: #000;
                font-weight: 700;
                font-size: 43px;
                text-align: justify;
              }
            }
          }
        }
      }
    }
  }

  .f26 {
    font-size: 26px;
  }
  .f18 {
    font-size: 18px;
  }

  .lottery-btn {
    width: 122px;
    height: 33px;
    background: rgba(252, 218, 9, 0.14);
    border: 1px solid rgba(0, 0, 0, 0.3);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}
</style>
