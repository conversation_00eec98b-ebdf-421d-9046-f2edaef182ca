<template>
  <!-- 上课 -->
  <div v-loading="loading" class="w h in-class">
    <Tools ref="tools" :unit-id="unitId" @handleActive="handleActive" />
    <!-- 画板 -->
    <Draw v-show="toolActive" ref="draw" :active="toolActive" @closeDraw="toolActive = false; $refs.tools.closeDraw()" />
    <div class="in-class-constent">
      <div class="radio-box" :style="`width:${panelShow ? rightAsideWidth + 'px' : '100%'}`">
        <div ref="box" class="ppt-box">
          <div class="w h flex flex-col justify-center items-center emty">
            <img class="loadingImg" src="@/assets/images/loading.gif" alt="占位图2" />
            加载中
          </div>
          <div v-show="!drag" id="whiteboard" class="w h"></div>
        </div>
      </div>
      <panelLine v-show="panelShow" v-model="rightAsideWidth" @change="rightAsideWidthChange" @mouseup="drag=false" />
      <div v-if="panelShow" ref="pdfbox" class="pdf-box" :style="`width:calc(100% - 5px - ${rightAsideWidth}px)`">
        <div class="w h overflow-y-auto">
          <!-- <Pdf :width="pdfWidth" /> -->
          <div v-for="item in htmlList" :key="item.id">
            <div class="html-title">
              {{ item.title }}
            </div>
            <div class="html-content" v-html="(item.contents && item.contents.length > 0) && item.contents[0].data"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="control-box">
      <div class="btns-box">
        <img class="btns" src="@/assets/digitalbooks/ppt-back.svg" @click="back" />
      </div>

      <!-- <div class="control">
        <div @click="pageChange('-')"> <img src="@/assets/digitalbooks/ppt-left.svg" /></div>
        <div>{{ currPage + 1 }} / {{ total }}</div>
        <div @click="pageChange('+')"> <img src="@/assets/digitalbooks/ppt-right.svg" /></div>
      </div> -->

      <div class="btns-box tr">
        <img v-show="filesList && filesList.length" class="btns" src="@/assets/digitalbooks/ziyuan.svg" @click="previewBox = true" />
        <img v-show="htmlList && htmlList.length" class="btns" src="@/assets/digitalbooks/jiaocai.svg" @click="openText" />
        <img v-show="!isFullAllScreen" class="btns" src="@/assets/digitalbooks/full.svg" @click="fullScreen" />
        <img v-show="isFullAllScreen" class="btns" src="@/assets/digitalbooks/unfull.svg" @click="fullScreen" />
      </div>
    </div>
    <div v-if="previewBox" class="preview-box">
      <div class="preview-content">
        <div class="preview-content-head">
          <div class="title">我的资源</div>
          <div @click.stop="previewBox = false"><i class="el-icon-close pointer"></i></div>
        </div>
        <div class="preview-content-content">
          <datas />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import screenfull from 'screenfull'
import { mapGetters } from 'vuex'
import WhiteClient from 'sdk/whiteBoard'
// import { getGenerateToken, getDocumentInfo } from '@/api/classroom-api'
import { generateWebofficeToken, refreshWebofficeToken } from '@/api/aliyun-api'
import { getCatalogueTreeContent, getBookCatalogueResource } from '@/api/digital-api'
import panelLine from './components/inClass/panelLine.vue'
import datas from './components/inClass/datas.vue'
// import Pdf from './components/inClass/pdf.vue'
import Tools from './components/tools/index.vue'
import Draw from './components/tools/components/draw.vue'
export default {
  components: {
    datas,
    panelLine,
    // Pdf,
    Tools,
    Draw
  },
  data () {
    return {
      currPage: -1,
      total: 0,
      boardClient: null,
      scenes: null,
      sceneUUid: null,
      token: {
        boardToken: '',
        boardUuid: ''
      },
      pageList: {
        show: false
      },
      useLocalFont: false,
      previewBox: false,
      rightAsideWidth: 300,
      panelShow: false,
      pdfWidth: 0,
      courseId: this.$route.query.courseId,
      unitId: this.$route.query.unitId,
      toolActive: false,
      roomId: 0,
      documentId: 0,
      studentCourseId: 0,
      bookId: 0,
      htmlList: [],
      pptList: [],
      pdfList: [],
      filesList: [],
      isFullAllScreen: false,
      loading: false,
      drag: false
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  async mounted () {
    this.roomId = this.$route.query && this.$route.query.r
    this.documentId = this.$route.query && this.$route.query.d
    this.studentCourseId = this.$route.query && this.$route.query.studentCourseId
    this.bookId = this.$route.query.courseId
    const screenWidth = document.body.offsetWidth
    this.rightAsideWidth = Math.floor(screenWidth * 0.5)
    this._getLessonCatalogues()
    await this._getBookCatalogueResource()
    this._getGenerateToken()
    screenfull.on('change', () => {
      if (screenfull.isFullscreen) {
        this.isFullAllScreen = true
      } else {
        this.isFullAllScreen = false
      }
    })
  },
  methods: {
    openText() {
      this.panelShow = !this.panelShow
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    },
    back () {
      if (this.$route.query && this.$route.query.f) {
        let str = `/digitalbooks/attendClass?id=${this.bookId}&studentCourseId=${this.studentCourseId}&f=${this.$route.query.f}`
        if (this.$route.query && this.$route.query.f2) {
          str = str + `&f2=${this.$route.query.f2}`
        }
        this.$router.push(str)
      } else {
        this.$router.push(`/digitalbooks/attendClass?id=${this.bookId}&studentCourseId=${this.studentCourseId}`)
      }
    },
    preview () {
      this.previewBox = true
    },
    rightAsideWidthChange (width) {
      this.rightAsideWidth = width
      this.pdfWidth = this.$refs.pdfbox.offsetWidth
      this.drag = true
    },
    async _getLessonCatalogues () {
      // 获取教材内容（富文本）
      const { data } = await getCatalogueTreeContent({ catalogueId: this.unitId })
      this.htmlList = data
    },
    async _getGenerateToken () {
      const list = this.pptList
      if (list.length === 0) {
        return false
      }
      const { data } = await generateWebofficeToken({
        fileUrl: list[0].mediaFile.url,
        coursecommUnitResourceId: list[0].id
      })
      this.tokenList = data.body
      this.weboffice({
        AccessToken: data.body.accessToken,
        WebofficeURL: data.body.webofficeURL
      })
    },
    weboffice(tokenInfo) {
      var mount = document.getElementById('whiteboard')
      var demo = window.aliyun.config({
        mount: mount,
        url: tokenInfo.WebofficeURL,
        refreshToken: this.refreshTokenPromise
      })
      demo.setToken({
        token: tokenInfo.AccessToken,
        timeout: 25 * 60 * 1000 // Token过期时间，单位为ms。25分钟之后刷新Token。
      })
    },
    async refreshTokenPromise() {
      const { data } = await refreshWebofficeToken({
        accessToken: this.tokenList.accessToken,
        refreshToken: this.tokenList.refreshToken
      })
      return {
        token: data.body.accessToken,
        timeout: 10 * 60 * 1000
      }
    },
    // async _getDocumentInfo () {
    //   if (!+this.documentId) {
    //     this.$message.error('课件还未准备好')
    //     return
    //   }
    //   this.loading = true
    //   const { data } = await getDocumentInfo(this.documentId)
    //   this.getDocument(data && data.scenes)
    // },
    getDocument (scenes) {
      try {
        if (scenes) {
          const newScenes = JSON.parse(scenes)
          const pptScenes = []
          if (newScenes.progress.convertedFileList.length > 0) {
            newScenes.progress.convertedFileList.map((f, i) => {
              pptScenes.push({
                name: 'page' + i,
                ppt: {
                  src: f.conversionFileUrl,
                  width: f.width,
                  height: f.height,
                  previewURL: f.preview
                }
              })
            })
            this.scenes = pptScenes
            this.sceneUUid = newScenes.uuid
          }
        }
        this.joinRoom()
      } catch (error) {
        console.error(error)
      }
    },
    async joinRoom () {
      try {
        const option = this.useLocalFont ? {
          pptParams: {
            useServerWrap: false
          }
        } : {}
        this.boardClient = new WhiteClient(option)
        const joinRoomParams = {
          uuid: this.token.boardUuid,
          uid: this.id + '',
          roomToken: this.token.boardToken,
          disableCameraTransform: true
        }
        const room = await this.boardClient.joinRoom(joinRoomParams, {
          onPhaseChanged: (phase) => {
            if (phase === 'connected') {
              setTimeout(() => {
                this.$room.moveCamera({ centerX: 0, centerY: 0 })
                this.$room.refreshViewSize()
                this.$room.scalePptToFit('continuous')
              }, 1000)
            }
          },
          onRoomStateChanged: state => {
            if (state.sceneState) {
              this.currPage = state.sceneState.index
            }
          }
        })
        if (this.scenes && this.scenes.length > 0) {
          if (room.state.sceneState.scenePath === '/init' || room.state.globalState.__pptState.uuid !== this.sceneUUid) {
            room.putScenes(`/${this.sceneUUid}`, this.scenes)
            room.setScenePath(`/${this.sceneUUid}/${this.scenes[0].name}`)
          }
        }
        room.bindHtmlElement(document.getElementById('whiteboard'))
        room.setMemberState({ currentApplianceName: 'selector' })
        this.total = room.state.sceneState.scenes.length
        room.setSceneIndex(0)
        this.currPage = room.state.sceneState.index
        this.$room = room
        this.refreshView()
        this.loading = false
      } catch (error) {
        this.loading = false
        console.log(error)
      }
    },
    pageChange (type) {
      switch (type) {
        case '-':
          // if (this.currPage === 0) return
          // this.currPage--
          this.$room.pptPreviousStep()

          break
        case '+':
          // if (this.currPage + 1 === this.total) return
          // this.currPage++
          this.$room.pptNextStep()
          break
        default:
          this.currPage = +type - 1
          this.$room.setSceneIndex(this.currPage)
      }
      this.currPage = this.$room.state.sceneState.index
    },
    refreshView () {
      this.$room.moveCamera({ centerX: 0, centerY: 0 })
      this.$room.refreshViewSize()
      this.$room.scalePptToFit('continuous')
    },
    async _getBookCatalogueResource () {
      const { data } = await getBookCatalogueResource({ catalogueId: this.unitId })
      if (data && data.length) {
        this.pptList = data.filter((v) => {
          return v.resourceType === 'DIGITAL_TEACH_COURSEWARE'
        })
        this.pdfList = data.filter((v) => {
          return v.resourceType === 'DIGITAL_TEACH_PLAN'
        })
        this.filesList = data.filter((v) => {
          return v.resourceType === 'DIGITAL_TEACH_RESOURCE'
        })
      } else {
        this.pptList = []
        this.pdfList = []
        this.filesList = []
      }
    },
    fullScreen (domName) {
      if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit()
      } else {
        screenfull.request()
      }
    },

    // 工具条
    handleActive (val) {
      this.toolActive = val
    }
  }
}
</script>

<style lang="scss" scoped>
.loadingImg{
  width: 50%;
}
.in-class {
  padding: 0 0 20px 0;
  box-sizing: border-box;
  position: relative;

  .in-class-constent {
    width: 100%;
    height: calc(100% - 60px);
    display: flex;
  }

  .radio-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .ppt-box {
      padding: 0;
      width: 100%;
      height: 100%;
      position: relative;
      background-color: #ffffff;
    #whiteboard{
      position: absolute;
      top:0;
      left: 0;
      z-index: 11;
    }
    ::v-deep iframe{
      width: 100% !important;
      height: 100% !important;
    }
    }
  }

  .pdf-box {
    flex: 1;
    padding: 5px;
    box-sizing: border-box;

    .html-title {
      color: #000;
      font-size: 26px;
      font-weight: 500;
      width: 100%;
      text-align: center;
    }

    .html-content {
      ::v-deep img {
        max-width: 100%;
      }

      ::v-deep video {
        width: 100%;
      }

      ::v-deep p,
      ::v-deep li {
        white-space: pre-wrap;
        /* 保留空格 */
      }

      ::v-deep blockquote {
        border-left: 8px solid #d0e5f2;
        padding: 10px 10px;
        margin: 10px 0;
        background-color: #f1f1f1;
      }

      ::v-deep table {
        border-collapse: collapse;
      }

      ::v-deep th,
      ::v-deep td {
        border: 1px solid #ccc;
        min-width: 50px;
        height: 20px;
      }

      ::v-deep th {
        background-color: #f1f1f1;
      }

      ::v-deep code {
        font-family: monospace;
        background-color: #eee;
        padding: 3px;
        border-radius: 3px;
      }

      ::v-deep pre>code {
        display: block;
        padding: 10px;
      }

      ::v-deep input[type="checkbox"] {
        margin-right: 5px;
      }

      ::v-deep ul,
      ::v-deep ol {
        padding-left: 20px;
      }
    }
  }

  .control-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-XXL);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.49);
    margin: 0 20px;
    padding: 0 20px;

    .btns {
      width: 45px;
      height: 45px;
      margin: 10px 10px 0 10px;
      cursor: pointer;
    }

    .control {
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 45px;
        height: 45px;
        margin: 10px 10px 0 10px;
        cursor: pointer;
      }
    }

    .btns-box {
      width: 200px;
    }

    .tr {
      text-align: right;
    }
  }

  .preview-box {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 100px;
    box-sizing: border-box;
    background: rgba($color: #000000, $alpha: .5);
    z-index: 99999;

    .preview-content {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;
      box-sizing: border-box;
      overflow-y: auto;
      @include scrollBar;

      .preview-content-head {
        height: 40px;
        display: flex;
        justify-content: space-between;

        .title {
          font-size: 16px;
          font-weight: 500;
        }

        .el-icon-close {
          font-size: var(--font-size-XXL);
        }
      }

      .preview-content-content {
        height: calc(100% - 40px);
        overflow-y: auto;
      }
    }
  }
}
</style>
