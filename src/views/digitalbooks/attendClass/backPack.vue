<template>
  <!-- 备课 -->
  <div class="attend-class">
    <div class="head-box">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="relative">
        <div
          class="flex items-center"
          @click="lessonList && lessonList.length > 1 ? showLessonList = !showLessonList : ''"
        >
          <div class="head-title pointer">{{ unitName }}</div>
          <img v-if="lessonList && lessonList.length > 1" class="more pointer" :src="triangleDown" alt="筛选" />
        </div>
        <ul v-if="showLessonList" class="grade_ul">
          <li
            v-for="item in lessonList"
            :key="item.id"
            :class="{ 'blue': +unitId === +item.id }"
            @click="handleCourse(item)"
          >
            <div class="article-singer-container">{{ item.title }}</div>
          </li>
        </ul>
      </div>
    </div>

    <div class="attend-content">
      <div class="content-left">
        <div class="btn" :class="{ 'btn-active': activeIndex === 0 }" @click="handleType(0)">
          配套教案
        </div>
        <div class="btn" :class="{ 'btn-active': activeIndex === 1 }" @click="handleType(1)">
          配套课件
        </div>
        <div class="btn" :class="{ 'btn-active': activeIndex === 2 }" @click="handleType(2)">
          我的资源
        </div>
      </div>
      <div class="content-right">
        <div class="right-box">
          <Pdf v-if="activeIndex === 0" ref="pdf" />
          <Ppt v-if="activeIndex === 1" ref="ppt" />
          <Datas v-if="activeIndex === 2" :list="filesList" @del="refreshFileList" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Datas from './components/backpack/datas.vue'
import Pdf from './components/backpack/pdf.vue'
import Ppt from './components/backpack/ppt.vue'
import triangleDown from '@/assets/digitalbooks/triangle-down.svg'

import { getBookCatalogueByVersionForLesson, getBookCatalogueResource } from '@/api/digital-api.js'
export default {
  components: {
    Datas,
    Pdf,
    Ppt
  },
  data () {
    return {
      triangleDown,
      lessonList: [],
      showLessonList: false,
      activeIndex: 0,
      bookId: 0,
      roomId: 0,
      documentId: 0,
      unitId: 0,
      studentCourseId: 0,
      pptList: [],
      pdfList: [],
      filesList: [],
      unitName: ''
    }
  },
  mounted () {
    this.initData()
    this._getBookCatalogue()
  },
  methods: {
    initData () {
      this.bookId = this.$route.query.courseId
      this.roomId = this.$route.query && this.$route.query.r
      this.documentId = this.$route.query && this.$route.query.d
      this.unitId = this.$route.query && this.$route.query.unitId
      this.studentCourseId = this.$route.query && this.$route.query.studentCourseId
      this._getBookCatalogueResource()
    },
    back () {
      if (this.$route.query && this.$route.query.f) {
        let str = `/digitalbooks/attendClass?id=${this.bookId}&studentCourseId=${this.studentCourseId}&f=${this.$route.query.f}`
        if (this.$route.query && this.$route.query.f2) {
          str = str + `&f2=${this.$route.query.f2}`
        }
        this.$router.push(str)
      } else {
        this.$router.push(`/digitalbooks/attendClass?id=${this.bookId}&studentCourseId=${this.studentCourseId}`)
      }
    },
    handleType (index) {
      this.activeIndex = index
      if (index === 0) {
        this.$nextTick(() => {
          if (this.pdfList && this.pdfList.length) {
            this.$refs.pdf._loadFile(this.pdfList[0].mediaFile.url, `${this.pdfList[0].mediaFile.fileName}.${this.pdfList[0].mediaFile.expendType}`)
          } else {
            this.$refs.pdf._loadFile('')
          }
        })
      } else if (index === 1) {
        this.$nextTick(() => {
          this.$refs.ppt._getGenerateToken(this.pptList)
        })
      }
    },
    async _getBookCatalogue () {
      const { data } = await getBookCatalogueByVersionForLesson({
        bookId: this.bookId
      })
      this.lessonList = data
      this.findName()
    },
    findName () {
      if (this.lessonList && this.lessonList.length) {
        const nameArr = this.lessonList.find(v => {
          return +v.id === +this.unitId
        })
        this.unitName = nameArr.title
      }
    },
    async _getBookCatalogueResource () {
      const { data } = await getBookCatalogueResource({ catalogueId: this.unitId })
      if (data && data.length) {
        this.pptList = data.filter((v) => {
          return v.resourceType === 'DIGITAL_TEACH_COURSEWARE'
        })
        this.pdfList = data.filter((v) => {
          return v.resourceType === 'DIGITAL_TEACH_PLAN'
        })
        this.filesList = data.filter((v) => {
          return v.resourceType === 'DIGITAL_TEACH_RESOURCE'
        })
      } else {
        this.pptList = []
        this.pdfList = []
        this.filesList = []
      }
      this.handleType(0)
    },
    handleCourse (item) {
      this.showLessonList = false
      this.$router.push(`/digitalbooks/backPack?unitId=${item.id}&courseId=${this.bookId}&studentCourseId=${this.studentCourseId}&r=${item.exerciseRoomId}&d=${item.documentId}`)
      this.initData()
      this.findName()
    },
    async refreshFileList () {
      const { data } = await getBookCatalogueResource({ catalogueId: this.unitId })
      if (data && data.length) {
        this.filesList = data.filter((v, i) => {
          return v.resourceType === 'DIGITAL_TEACH_RESOURCE'
        })
      }
    },
    formatBytes (bytes, decimals = 2) {
      if (bytes === 0) return '0 Bytes'

      const k = 1024
      const dm = decimals < 0 ? 0 : decimals
      const sizes = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
    }
  }
}
</script>

<style lang="scss" scoped>
.attend-class {
  width: 100%;
  height: 100%;
  padding: 20px;

  .head-box {
    height: 40px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;

    .share {
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .back {
      width: 30px;
      height: 30px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: 24px;
      font-weight: 500;
      margin-left: 10px;

    }
    .more {
      width: 15px;
      height: 8px;
      margin-left: 15px;
    }

    .grade_ul {
      position: absolute;
      left: 0;
      top: 50px;
      min-width: 150px;
      max-width: 400px;
      max-height: 340px;
      background: #FFFFFF;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
      border-radius: 16px;
      padding: 30px;
      z-index: 12;
      display: flex;
      flex-direction: column;
      gap: 23px;
      overflow-x: hidden;
      @include scrollBar;

      li {
        text-align: left;
        font-family: 'PingFang SC';
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        cursor: pointer;
      }

      .blue {
        color: #2D9CDB;
      }
    }
  }

  .attend-content {
    display: flex;
    padding: 10px 0;
    height: calc(100% - 40px);

    .content-left {
      border-radius: 6px;
      border: 1px solid rgba(255, 255, 255, 0.68);
      background: rgba(255, 255, 255, 0.50);
      height: 100%;
      width: 130px;
      padding: 20px;
      box-sizing: border-box;
      overflow-y: auto;

      .btn {
        display: flex;
        height: 30px;
        padding: 5px 10px;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        cursor: pointer;
        font-size: var(--font-size-XL);
        margin-bottom: 10px;

        &:hover {
          background: linear-gradient(90deg, #22C1C3 0%, #FDBB2D 100%);
          color: #fff;
        }
      }

      .btn-active {
        background: linear-gradient(90deg, #22C1C3 0%, #FDBB2D 100%);
        color: #fff;
      }
    }

    .content-right {
      width: calc(100% - 130px);
      padding: 0 0 0 10px;
      box-sizing: border-box;

      .right-box {
        border-radius: 6px;
        border: 1px solid rgba(255, 255, 255, 0.68);
        background: rgba(255, 255, 255, 0.50);
        height: 100%;
        padding: 10px;
        box-sizing: border-box;
      }
    }
  }
}
</style>
