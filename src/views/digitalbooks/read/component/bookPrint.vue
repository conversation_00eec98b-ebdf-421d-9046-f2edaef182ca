<template>
  <NormalDialog
    v-if="dialogShow"
    v-loading="loading"
    width="100%"
    title="数字教材打印"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    element-loading-text="正在生成内容，请稍候"
    @closeDialog="close"
  >
    <div class="container">
      <div class="left">
        <el-tree
          ref="tree"
          :data="treeData"
          :props="treeProps"
          check-strictly
          show-checkbox
          default-expand-all
          node-key="id"
          highlight-current
          @check-change="handleCheckChange"
        />
      </div>
      <div ref="content" class="rich-text-container">
        <div v-if="selectedContent===''" class="emty">
          <Empty description="暂无数据" />
        </div>
        <div
          v-else
          ref="richText"
          class="rich-text-content"
          v-html="selectedContent"
        ></div>
        <div class="print-btn">
          <el-button style="margin-left: 75%;" class="print" type="primary" @click="printContent">打印内容</el-button>
          <el-button class="print" type="" @click="close">取消</el-button>
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
          >
            <div slot="content" style="width:30vw;">
              <p>在macOS系统上，打印时中文文字丢失通常是由于系统字体设置问题导致的‌。解决方法：</p>
              <p>打开macOS系统的访达，搜索“字体册”，点击进入字体册，搜索“苹方-简”；右键点击该字体并选择“激活”（会提示下载）；</p>
              <p>关闭软件平台/浏览器后再打开即可。</p>
            </div>
            <p class="tips">文字丢失<i class="el-icon-question"></i></p>
          </el-tooltip>
          <p class="waring">*请确保电脑已连接打印机</p>
        </div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { getContent, getBookCatalogue } from '@/api/digital-api.js'
import Empty from '@/components/classPro/Empty/index.vue'
export default {
  components: {
    NormalDialog,
    Empty
  },
  data() {
    return {
      dialogShow: false,
      studentCourseId: 0,
      bookId: 0,
      treeProps: {
        children: 'childCatalogue',
        label: 'title',
        disabled: 'canRead'
      },
      treeData: [],
      selectedContent: '',
      selectedNodes: [],
      isPrint: false,
      printHtml: '',
      loading: false
    }
  },
  methods: {
    async _getBookCatalogue() {
      const { data } = await getBookCatalogue({
        bookId: this.bookId,
        type: 'CHAPTER'
      })
      if (data && data.length) {
        data.map((val, index) => {
          if (this.studentCourseId) {
            this.setTryRead(val, false)
          } else if (this.preMode) {
            this.setTryRead(val, false)
          } else {
            if (index === 0 || index === 1) {
              this.setTryRead(val, false)
            } else {
              this.setTryRead(val, true)
            }
          }
        })
      }
      this.treeData = data
    },
    setTryRead (data, type) {
      if (data && data.length) {
        data.forEach(element => {
          this.setTryRead(element, type)
        })
      } else if (data.childCatalogue && data.childCatalogue.length) {
        this.setTryRead(data.childCatalogue, type)
      }
      if (!(data && data.length)) {
        data.canRead = type
      }
    },
    close() {
      this.dialogShow = false
      this.selectedNodes = []
      this.selectedContent = ''
    },

    open(bookId, studentCourseId) {
      this.dialogShow = true
      this.bookId = bookId
      this.studentCourseId = studentCourseId
      this._getBookCatalogue()
    },

    handleCheckChange() {
      this._getContent(this.$refs.tree.getCheckedNodes())
    },

    async _getContent(arr) {
      try {
        // 使用Promise.all并行请求内容
        const contentPromises = arr.map(item =>
          getContent({ catalogueId: item.id })
            .then(({ data }) => ({
              title: item.title,
              content: data ? data[0].data : ''
            }))
        )
        const results = await Promise.all(contentPromises)
        // 拼接内容
        const tem = results.map(({ title, content }) =>
          `<h3 style="width:100%;text-align: center;">${title}</h3>${content}`
        ).join('')
        this.selectedContent = tem
        await this.$nextTick()
        await window.MathJax.typesetPromise()
      } catch (error) {
        console.error('获取内容失败:', error)
      }
    },
    async printContent() {
      const richTextElement = this.$refs.richText
      let printContent = richTextElement.innerHTML

      // 使用正则移除 font-family 样式
      printContent = printContent.replace(/font-family:[^;"]*;?/gi, '') // 去掉行内的 font-family
      printContent = printContent.replace(/style="[^"]*font-family:[^;"]*;?[^"]*"/gi, '') // 去掉带有 font-family 的整个 style 属性
      this.loading = true
      let iframe = null
      this.$nextTick(() => {
        try {
        // 创建 iframe 用于打印
          iframe = document.createElement('iframe')
          iframe.style.position = 'absolute'
          iframe.style.width = '0'
          iframe.style.height = '0'
          iframe.style.border = 'none'
          document.body.appendChild(iframe)

          const iframeDoc = iframe.contentWindow.document
          iframeDoc.open()

          // 写入 HTML 内容并移除字体样式
          iframeDoc.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>打印页面</title>
          <style>
            body {
              font-size: 14px;
              color: #000;
              margin: 0;
              padding: 0;
              width: 100%;
              height: auto;
              overflow: visible;
            }
            img {
              max-width: 100%;
              height: auto;
            }
            @media print {
              body {
                -webkit-print-color-adjust: exact;
              }
            }
          </style>
        </head>
        <body>${printContent}</body>
      </html>
    `)

          iframeDoc.close()

          // 打印逻辑
          const _this = this
          iframe.contentWindow.onload = () => {
            iframe.contentWindow.focus()
            iframe.contentWindow.print()
            _this.loading = false
            iframe.contentWindow.onafterprint = () => {
              document.body.removeChild(iframe)
            }
          }
        } catch (error) {
          this.loading = false
          console.error('打印失败:', error)
          if (iframe) document.body.removeChild(iframe)
        }
      })
    }

    // async printContent() {
    //   if (this.selectedNodes.length > 5) {
    //     this.$message.warning('一次最多打印5个章节！')
    //     return
    //   }

    //   const richTextElement = this.$refs.richText
    //   const width = richTextElement.scrollWidth
    //   const height = richTextElement.scrollHeight
    //   const maxHeightPerPage = 500
    //   const totalPages = Math.ceil(height / maxHeightPerPage)

    //   let printWindow
    //   try {
    //     this.loading = true
    //     printWindow = window.open('', '_blank')

    //     if (!printWindow) throw new Error('无法打开新窗口')

    //     // 初始内容：等待图像生成
    //     printWindow.document.write(`
    //   <html>
    //     <head>
    //       <title>打印页面</title>
    //       <style>
    //         body { margin: 0; text-align: center; }
    //         img { width: 95%; margin: 10px 0; }
    //       </style>
    //     </head>
    //     <body>
    //       <div>正在生成打印内容，请稍候...</div>
    //     </body>
    //   </html>
    // `)
    //     printWindow.document.close()

    //     for (let page = 0; page < totalPages; page++) {
    //       const yOffset = page * maxHeightPerPage
    //       const currentHeight = Math.min(maxHeightPerPage, height - yOffset)

    //       await richTextElement.scrollTo(0, yOffset)

    //       const canvas = await html2canvas(richTextElement, {
    //         scale: 1.5,
    //         useCORS: true,
    //         width: width,
    //         height: currentHeight
    //       })

    //       const imgSrc = canvas.toDataURL('image/png')

    //       // 直接写入新窗口
    //       const imgTag = printWindow.document.createElement('img')
    //       imgTag.src = imgSrc
    //       printWindow.document.body.appendChild(imgTag)

    //       // 释放 canvas 内存
    //       canvas.width = 0
    //       canvas.height = 0
    //     }

    //     // 打印
    //     printWindow.onload = () => {
    //       printWindow.print()
    //       printWindow.onafterprint = () => printWindow.close()
    //     }
    //   } catch (error) {
    //     console.error('打印失败:', error)
    //     if (printWindow) printWindow.close()
    //   } finally {
    //     this.loading = false
    //   }
    // }

  }
}
</script>
<style>
@media print{
   *{
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
   }
}
</style>
<style scoped lang="scss">

.container {
  width: 100%;
  height: 80vh;
  display: flex;
  justify-content: space-between;

  .left {
    width: 200px;
    min-width: 200px;
    height: 100%;
    overflow: auto;
    @include scrollBar;

    ::v-deep .el-checkbox__inner::after {
      border-width: 2px;
    }

    ::v-deep .el-checkbox__label {
      padding-left: 3px !important;
    }

    ::v-deep .el-tree-node__label {
      width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 8px;
    }
  }
}
.print-btn{
  display: flex;
  align-items: center;
  height: 30px;
  position: relative;
}
.waring{
  font-size: 10px;
  position: absolute;
  bottom: -30px;
  right: 8vw;
  color: red;
}
.rich-text-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  margin-left: 20px;
  position: relative;
}
.tips{
    cursor: pointer;
    font-size: 10px;
    margin-left: 10px;
    margin-top: 26px;
  }
.emty {
    width: 100%;
    height: 93%;
  }
.print{
  padding: 6px;
  font-size: 10px;
  margin-top: 10px;
}
.rich-text-content {
  height: 93%;
  overflow: auto;
  @include scrollBar;
  ::v-deep * {
    max-width: 100%;
  }

  ::v-deep pre > code {
    display: block;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
