<template>
  <div class="main">
    <div class="progress" :style="{right:showDetail?'20vw':'1vw'}">
      <img class="down" src="../../../../assets/digitalbooks/up.svg" alt="" @click="down" />
      <div class="content">{{ zoom }}%</div>
      <img class="up" src="../../../../assets/digitalbooks/dowm.svg" alt="" @click="up" />
    </div>
    <div id="myChart" ref="myChart">
    </div>
  </div>
</template>

<style scoped>
.main{
  width: 100%;
  height: 100%;
  position: relative;
}
.progress{
  width: 154px;
  height: 34px;
  background: rgb(10, 43, 98, 0.898);
  border-radius: 6px;
  position: absolute;
  bottom: 10px;
  right: 10px;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  z-index: 10;
}
.up,.down{
  width: 19px;
  cursor: pointer;
}
.content{
  height: 14px;
  width: 75px;
  display: block;
  line-height: 14px;
  text-align: center;
  border-left: 1px solid rgba(255, 255, 255, 0.42);
  border-right: 1px solid rgba(255, 255, 255, 0.42);
}
#myChart {
  width: 100%;
  height: 100%;
  position: relative;
}

</style>

<script>

export default {
  name: 'Chart',
  props: {
    chartData: {
      type: Object,
      default: null
    },
    showDetail: {
      type: Boolean,
      default: false
    },
    activeId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      chart: null,
      showNodes: [],
      showLinks: [],
      categories: [],
      nodes: [],
      links: [],
      zoom: 100,
      hoverNodeId: null, // 鼠标悬浮时的节点ID
      hoverNodePos: { x: 0, y: 0 }, // 鼠标悬浮节点的位置
      buttonStyle: {
        position: 'absolute',
        display: 'none',
        zIndex: 10,
        top: '0',
        left: '0',
        transform: 'translate(-50%, -50%)'
      },
      option: {
        tooltip: {
          show: true,
          trigger: 'item'
        },
        series: [{
          name: '知识图谱',
          type: 'graph',
          layout: 'force',
          roam: true,
          draggable: true,
          scaleLimit: { min: 0.2, max: 2 },
          nodeScaleRatio: 1,
          label: {
            show: true,
            formatter: function (params) {
              var val = params.data.name
              return val.length > 12 ? val.substr(0, 12) + '...' : val
            },
            position: 'inside',
            fontSize: 14,
            color: '#fff',
            fontWeight: 'bold',
            overflow: 'break',
            width: 100
          },
          lineStyle: { color: '#FFFFFF' }, // 设置连线为白色
          edgeSymbol: ['none', 'arrow'],
          edgeSymbolSize: [0, 6],
          edgeLabel: {
            normal: {
              show: true,
              color: '#FFFFFF', // 连线文本颜色设置为白色
              width: 120,
              overflow: 'break',
              formatter: function (x) {
                return x.data.name
              },
              position: 'middle', // 设置文本位置为连线中间
              distance: 10 // 调整文本与连线的距离，避免重叠
            }
          },
          force: {
            repulsion: 1000, // 调整节点的排斥力，避免重叠
            edgeLength: [80, 120], // 调整边长，避免连线过长
            gravity: 0.1 // 调整节点的引力，使图形布局更加均匀
          },
          data: [],
          links: [],
          categories: []
        }]
      },
      selectedLinkId: 0
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      window.addEventListener('resize', this.chart.resize)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.chart.resize)
  },
  methods: {
    get_node_levels(sourceId, dataLinks, levels = [], level = 0) {
      if (!levels[sourceId] || levels[sourceId] > level) {
        levels[sourceId] = level
        dataLinks.forEach((val, idx) => {
          if (val.source_id === sourceId) {
            this.get_node_levels(val.target_id, dataLinks, levels, level + 1)
          }
        })
      }
      return levels
    },
    deal_chart_data(data) {
      // 变成一个整体（利于找级别）
      const targetIds = []
      data.links.forEach((val, idx) => {
        targetIds.push(val.target_id)
      })
      data.nodes.forEach((val, idx) => {
        if (targetIds.indexOf(val.id) === -1) { // 不在targets中说明是是第一级
          data.links.push({ id: `${idx}-${idx + 1}`, source_id: 0, target_id: val.id, name: '' })
        }
      })
      data.nodes.unshift({ id: 0, name: '整书图谱', type: '整书图谱' })
      // 获取节点级别
      const levels = this.get_node_levels(0, data.links, [], 0)
      data.nodes.forEach((val, idx) => {
        val.level = levels[val.id]
      })
      // 不是整书图谱
      if (this.activeId !== 0) {
        data.nodes.shift() // 删除整数图谱
      }
      // 获取关系下标（源关系对应用的id，echarts关系通过下标）
      this.get_link_index(data.nodes, data.links)
      // 拷贝一份数据
      this.nodes = data.nodes.map(d => ({ ...d }))
      this.links = data.links.map(d => ({ ...d }))
    },
    get_link_index(dataNodes, dataLinks) {
      const dataNodeIds = []
      dataNodes.forEach((val, idx) => {
        dataNodeIds.push(val.id)
      })
      for (let i = dataLinks.length - 1; i >= 0; i--) {
        const val = dataLinks[i]
        const source = dataNodeIds.indexOf(val.source_id)
        const target = dataNodeIds.indexOf(val.target_id)
        if (source === -1 || target === -1) {
          dataLinks.splice(i, 1) // 没用到的关系删除
        } else {
          val.source = source
          val.target = target
        }
      }
    },
    down() {
      if (this.zoom === 20) {
        return
      }
      this.chart.setOption({
        series: [{
          zoom: this.zoom / 100 - 0.1,
          label: {
            fontSize: 14 * (this.zoom / 100 - 0.1) > 10 ? 14 * (this.zoom / 100 - 0.1) : 10
          }
        }]
      })
      this.zoom = parseInt((this.zoom / 100 - 0.1) * 100)
      this.setOption()
    },
    setOption() {
      let option = null
      if (this.zoom / 100 < 0.5) {
        option = {
          series: [{
            label: { normal: { width: 'auto', color: '#fff', position: 'top', verticalAlign: 'bottom', distance: 0 }},
            edgeLabel: { normal: { show: false }}
          }]
        }
      } else {
        option = {
          series: [{
            label: { normal: { color: '#fff', position: 'inside', verticalAlign: 'middle', distance: 0, fontWeight: 'bold', overflow: 'break', width: 100 }},
            edgeLabel: { normal: { show: true }}
          }]
        }
      }

      this.chart.setOption(option)
    },
    up() {
      if (this.zoom === 200) {
        return
      }
      this.chart.setOption({
        series: [{
          zoom: this.zoom / 100 + 0.1,
          label: {
            fontSize: 14 * (this.zoom / 100 - 0.1) > 10 ? 14 * (this.zoom / 100 - 0.1) : 10
          }
        }]
      })
      this.zoom = parseInt((this.zoom / 100 + 0.1) * 100)
      this.setOption()
    },
    handleZoomProgress(event) {
      if (event.zoom) {
        const zoom = this.chart.getOption().series[0].zoom
        this.zoom = parseInt(zoom * 100)
        this.chart.setOption({
          series: [{
            label: {
              fontSize: 14 * zoom > 14 ? 14 * zoom : 14
            }
          }]
        })
        this.setOption()
      }
    },
    initChart() {
      const echarts = require('echarts')
      this.chart = echarts.init(this.$refs.myChart)
      this.chart.on('click', this.chartClick)
      this.chart.on('mouseover', this.nodeHover)
      this.chart.on('mouseout', this.nodeHoverOut)
      this.chart.setOption(this.option)
      this.chart.on('graphRoam', this.handleZoomProgress)
      this.chart.getZr().on('click', this.initHighLight)
    },
    initHighLight(event) {
      if (!event.target) { // 空白处
        this.setColor()
        this.reloadChart()
      }
    },
    formatNode() {
      this.nodes.forEach((val) => {
        val.leaf = false
        val.open = true
        val.fontSize = 14

        const textLength = val.name.length

        // 调整节点大小以适应文本
        const minWidth = 80 // 增加最小宽度
        const maxWidth = 150 // 增加最大宽度
        const baseHeight = 70 // 基础高度

        // 根据文本长度计算所需宽度
        const width = Math.min(Math.max(textLength * 15, minWidth), maxWidth)

        // 设置节点大小，确保文字能够完整显示
        val.symbolSize = [width, baseHeight]

        // 其他属性设置
        val.fontWidth = width - 20 // 文字宽度略小于节点宽度
        val.edgeLength = 120
        val.repulsion = 700
      })
      this.getShowNodes()
    },

    // 渲染图表数据
    renderChart() {
      this.deal_chart_data(this.chartData)
      this.categories = this.chartData.categories
      const len = this.nodes.length
      let zoom = 1
      if (len >= 120) {
        zoom = 0.3
      } else {
        zoom = 1 - Math.floor(len / 10) / 10
      }
      this.chart.setOption({
        series: [{
          zoom: zoom < 0.4 ? 0.4 : zoom,
          label: {
            fontSize: 14 * (this.zoom / 100 - 0.1) > 10 ? 14 * (this.zoom / 100 - 0.1) : 10
          }
        }]
      })
      this.zoom = parseInt(zoom < 0.4 ? 0.4 * 100 : zoom * 100)
      this.formatNode()
      this.$nextTick(() => {
        this.setColor()
        this.chart.setOption({
          series: [{
            data: this.showNodes,
            links: this.showLinks,
            categories: this.categories
          }]
        })
        // this.highlightRelatedNodes(this.nodes[0].id)
        this.setOption()
      })
    },
    chartClick(params) {
      if (params.dataType === 'node') {
        this.selectedNodeId = params.data.id
        this.highlightRelatedNodes(params.data.id)
        this.$emit('getHtml', params.data.id)
      } else if (params.dataType === 'edge') {
        this.selectedLinkId = params.data.id
      }
    },
    setColor() {
      const echarts = require('echarts')
      const graphic = echarts.graphic
      const colorArray = [
        { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
          { offset: 0, color: '#4FACFE' },
          { offset: 1, color: '#00F2FE' }
        ]) },
        { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
          { offset: 0, color: '#F6D365' },
          { offset: 1, color: '#FDA085' }
        ]) },
        { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
          { offset: 0, color: '#84FAB0' },
          { offset: 1, color: '#8FD3F4' }
        ]) },
        { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
          { offset: 0, color: '#ED6EA0' },
          { offset: 1, color: '#EC8C69' }
        ]) },
        { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
          { offset: 0, color: '#EC77AB' },
          { offset: 1, color: '#7873F5' }
        ]) },
        { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
          { offset: 0, color: '#9BE15D' },
          { offset: 1, color: '#00E3AE' }
        ]) }]
      this.showNodes.forEach((node, index) => {
        // 仅更新相关节点的样式，不修改未相关节点
        node.itemStyle = {
          color: colorArray[node.level >= colorArray.length ? node.level - colorArray.length : node.level].color,
          borderWidth: 2
        }
      })
      // 更新边样式：相关边高亮，其他边置灰
      this.showLinks.forEach((link) => {
        link.lineStyle = { color: '#eee',
          curveness: 0.1,
          opacity: 1 }
      })
    },
    // 高亮显示相关节点和边
    highlightRelatedNodes(nodeId) {
      const relatedNodes = new Set() // 存储相关节点的索引
      const relatedLinks = [] // 存储相关连线
      // const echarts = require('echarts')
      // const graphic = echarts.graphic

      // 颜色数组，分别对应不同层级的颜色
      // const colorArray = [
      //   { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
      //     { offset: 0, color: '#4FACFE' },
      //     { offset: 1, color: '#00F2FE' }
      //   ]) },
      //   { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
      //     { offset: 0, color: '#F6D365' },
      //     { offset: 1, color: '#FDA085' }
      //   ]) },
      //   { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
      //     { offset: 0, color: '#84FAB0' },
      //     { offset: 1, color: '#8FD3F4' }
      //   ]) },
      //   { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
      //     { offset: 0, color: '#ED6EA0' },
      //     { offset: 1, color: '#EC8C69' }
      //   ]) },
      //   { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
      //     { offset: 0, color: '#EC77AB' },
      //     { offset: 1, color: '#7873F5' }
      //   ]) },
      //   { offset: 0, color: new graphic.LinearGradient(0, 0, 1, 0, [ // 设置渐变方向和颜色
      //     { offset: 0, color: '#9BE15D' },
      //     { offset: 1, color: '#00E3AE' }
      //   ]) }]
      // 递归查找父级节点
      const findParentNodes = (currentNodeId, level = 0) => {
        this.links.forEach((link) => {
          // 向上查找：父级是当前节点的 `source`，目标是 `target`
          if (this.nodes[link.target].id === currentNodeId) {
            const nextNodeId = link.source // 当前节点的父节点是 `source`
            relatedNodes.add(this.nodes[nextNodeId].id) // 记录父节点
            this.showNodes.forEach(item => {
              if (item.id === this.nodes[nextNodeId].id) {
                item.level = level
              }
              if (item.id === nodeId) {
                item.level = 0
              }
            })
            relatedLinks.push(link) // 记录边
            findParentNodes(this.nodes[nextNodeId].id, level + 1) // 递归查找父级
          }
        })
      }

      // 递归查找子集节点
      const findChildNodes = (currentNodeId, level = 0) => {
        this.links.forEach((link) => {
          // 向下查找：子集是当前节点的 `target`，源是 `source`
          if (this.nodes[link.source].id === currentNodeId) {
            const nextNodeId = link.target // 当前节点的子节点是 `target`
            relatedNodes.add(this.nodes[nextNodeId].id) // 记录子节点
            this.showNodes.forEach(item => {
              if (item.id === this.nodes[nextNodeId].id) {
                item.level = level
              }
              if (item.id === nodeId) {
                item.level = 0
              }
            })
            relatedLinks.push(link) // 记录边
            findChildNodes(this.nodes[nextNodeId].id, level + 1) // 递归查找子集
          }
        })
      }

      // 查找当前节点及其相关节点（向下查找）
      findChildNodes(nodeId, 1)

      // 查找父级节点及其相关节点（向上查找）
      findParentNodes(nodeId, 1)

      // 更新节点样式：相关节点高亮，其他节点置灰
      this.showNodes.forEach((node, index) => {
        // 仅更新相关节点的样式，不修改未相关节点
        if (node.id === nodeId || relatedNodes.has(node.id)) {
          node.itemStyle = {
            ...node.itemStyle,
            opacity: 1
          }
        } else {
          node.itemStyle = { ...node.itemStyle, opacity: 0.2 }
        }
      })
      // 更新边样式：相关边高亮，其他边置灰
      this.showLinks.forEach((link) => {
        if (relatedLinks.filter(item => { return item.id === link.id }).length !== 0) {
          link.lineStyle = { color: '#eee',
            curveness: 0.1,
            opacity: 1 }
        } else {
          link.lineStyle = { color: '#888',
            curveness: 0.1,
            opacity: 0.1 }
        }
      })

      // 刷新图表
      this.reloadChart()
    },
    reloadChart() {
      this.chart.setOption({
        series: [{
          data: this.showNodes,
          links: this.showLinks,
          categories: this.categories
        }]
      })
    },
    getShowNodes() {
      this.showNodes = []
      this.showLinks = []
      const hides = this.get_need_hides()
      const indexes = []
      this.nodes.forEach((val, idx) => {
        if (hides.indexOf(idx) === -1) {
          var showNode = Object.assign({}, val)
          this.showNodes.push(showNode)
          indexes.push(idx)
        }
      })

      this.links.forEach((link) => {
        var newSource = indexes.indexOf(link.source)
        var newTarget = indexes.indexOf(link.target)
        if (newSource !== -1 && newTarget !== -1) {
          var showLink = Object.assign({}, link)
          showLink.source = newSource
          showLink.target = newTarget
          showLink.lineStyle = {
            curveness: 0.1
          }
          this.showLinks.push(showLink)
        }
      })
    },

    get_need_hides() {
      let hides = []
      this.nodes.forEach((val, idx) => {
        if (!val.open) {
          const childes = this.get_child_by_node(idx, idx, [])
          hides = [...hides, ...childes]
        }
      })
      hides = Array.from(new Set(hides)) // 去重
      return hides
    },
    get_child_by_node(self, idx, childes) {
      this.links.forEach((link) => {
        if (link.source === idx && link.target !== self && childes.indexOf(link.target) === -1) {
          childes.push(link.target)
          this.get_child_by_node(self, link.target, childes)
        }
      })
      return childes
    }
  }
}
</script>
