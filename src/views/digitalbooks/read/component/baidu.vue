<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="80%"
  >
    <iframe v-if="dialogVisible" class="baidu_iframe" :src="url"></iframe>
  </el-dialog>
</template>

<script>
export default {
  props: {
    item: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      url: '',
      dialogVisible: false
    }
  },
  mounted () {
    this.url = 'https://baike.baidu.com/item/' + this.item
  },
  methods: {
    show () {
      this.dialogVisible = true
      this.url = 'https://baike.baidu.com/item/' + this.item
    },
    hide () {
      this.dialogVisible = false
    }
  }
}
</script>

<style>
.baidu_iframe{
  width: 100%;
  height: 300px;

}
</style>
