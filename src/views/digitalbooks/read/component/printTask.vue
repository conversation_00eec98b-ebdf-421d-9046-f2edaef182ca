<template>
  <NormalDialog
    v-if="dialogShow"
    v-loading="loading"
    width="100%"
    title="任务打印"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    element-loading-text="正在生成内容，请稍候"
    @closeDialog="close"
  >
    <div class="container">
      <div class="left">
        <el-tree
          ref="tree"
          :data="treeData"
          :props="treeProps"
          check-strictly
          show-checkbox
          default-expand-all
          node-key="id"
          highlight-current
          @check="handleCheck"
        />
      </div>
      <div ref="content" class="rich-text-container">
        <div class="content-wrapper">
          <div v-if="html===''" class="emty">
            <Empty description="暂无数据" />
          </div>
          <div v-else ref="richText" class="rich-text-content" v-html="html"></div>
        </div>
        <div class="print-btn">
          <el-button style="margin-left: 75%;" class="print" type="primary" @click="printContent">打印内容</el-button>
          <el-button class="print" type="" @click="close">取消</el-button>
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
          >
            <div slot="content" style="width:30vw;">
              <p>在macOS系统上，打印时中文文字丢失通常是由于系统字体设置问题导致的‌。解决方法：</p>
              <p>打开macOS系统的访达，搜索"字体册"，点击进入字体册，搜索"苹方-简"；右键点击该字体并选择"激活"（会提示下载）；</p>
              <p>关闭软件平台/浏览器后再打开即可。</p>
            </div>
            <p class="tips">文字丢失<i class="el-icon-question"></i></p>
          </el-tooltip>
          <p class="waring">*请确保电脑已连接打印机</p>
        </div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import Empty from '@/components/classPro/Empty/index.vue'
import { getDigitalTaskListByCatalogueId, getBookCatalogue, digitalTask } from '@/api/digital-api.js'
import Prism from 'prismjs'
import 'prismjs/themes/prism-tomorrow.min.css'
export default {
  components: {
    NormalDialog,
    Empty
  },
  data() {
    return {
      dialogShow: false,
      loading: false,
      treeProps: {
        children: 'childCatalogue',
        label: 'title',
        disabled: 'canRead'
      },
      treeData: [],
      html: '',
      resourcesList: [],
      bookId: '',
      selectedTasks: []
    }
  },
  methods: {
    async _getBookCatalogue() {
      const { data } = await getBookCatalogue({
        bookId: this.bookId
      })
      const cataloguePromises = data.map(async (item) => {
        const childData = await getDigitalTaskListByCatalogueId({
          digitalCatalogueId: item.id
        })
        return {
          ...item,
          childCatalogue: childData.data.filter(item => item.sourceId === 0)
        }
      })
      this.treeData = await Promise.all(cataloguePromises)
    },

    handleCheck(data, checked) {
      // 如果是一级节点，取消选中
      if (data.childCatalogue) {
        this.$refs.tree.setChecked(data, false)
        return
      }

      // 获取所有选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes()
      if (checkedNodes.length > 5) {
        this.$message.warning('一次最多打印5个任务！')
        this.$refs.tree.setChecked(data, false)
        return
      }
      this.selectedTasks = checkedNodes
      this.combineContent()
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    async combineContent() {
      if (this.selectedTasks.length === 0) {
        this.html = ''
        return
      }

      try {
        const contentPromises = this.selectedTasks.map(async (item) => {
          const { data } = await digitalTask({
            apiType: 'get',
            id: item.id
          })
          return data
        })

        const results = await Promise.all(contentPromises)
        let finalHtml = ''
        this.resourcesList = []

        results.forEach(data => {
          const isJson = this.isJSON(data.content)
          const content = isJson ? JSON.parse(data.content) : data.content

          if (isJson) {
            finalHtml += `<h2  style="width:100%;text-align: center;">${data.title}</h4>` +
              content.map(c => `<div><p class="title">${c.title}</p>${c.content}</div>`).join('')
          } else {
            finalHtml += `<p class="title" style="width:100%;text-align: center;">${data.title}</p>${content}`
          }

          if (data.resourcesList) {
            this.resourcesList = [...this.resourcesList, ...data.resourcesList]
          }
        })

        this.html = finalHtml
        await this.$nextTick()
        window.MathJax.typesetPromise()
        Prism.highlightAll()
      } catch (error) {
        console.error('获取内容失败:', error)
      }
    },

    async printContent() {
      if (this.selectedTasks.length > 5) {
        this.$message.warning('一次最多打印5个章节！')
        return
      }

      if (!this.$refs.richText) {
        this.$message.warning('暂无可打印的内容')
        return
      }

      const richTextElement = this.$refs.richText
      const printContent = richTextElement.innerHTML

      // 确保内容不为空
      if (!printContent.trim()) {
        this.$message.warning('暂无可打印的内容')
        return
      }

      this.loading = true
      let iframe = null

      try {
        iframe = document.createElement('iframe')
        iframe.style.position = 'absolute'
        iframe.style.width = '0'
        iframe.style.height = '0'
        iframe.style.border = 'none'
        document.body.appendChild(iframe)

        const iframeDoc = iframe.contentWindow.document
        iframeDoc.open()
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>打印页面</title>
              <style>
                @media print {
                  body {
                    -webkit-print-color-adjust: exact;
                  }
                }
                body {
                  font-size: 14px;
                  color: #000;
                  margin: 0;
                  padding: 20px;
                }
                h3 {
                  text-align: center;
                  margin: 20px 0;
                }
                img {
                  max-width: 100%;
                  height: auto;
                }
                pre {
                  white-space: pre-wrap;
                  word-break: break-all;
                }
              </style>
            </head>
            <body>${printContent}</body>
          </html>
        `)
        iframeDoc.close()

        // 等待内容加载完成后打印
        setTimeout(() => {
          iframe.contentWindow.focus()
          iframe.contentWindow.print()
          this.loading = false

          iframe.contentWindow.onafterprint = () => {
            document.body.removeChild(iframe)
          }
        }, 500)
      } catch (error) {
        this.loading = false
        console.error('打印失败:', error)
        if (iframe) document.body.removeChild(iframe)
        this.$message.error('打印失败，请重试')
      }
    },

    open(bookId, studentCourseId) {
      this.dialogShow = true
      this.bookId = bookId
      this.studentCourseId = studentCourseId
      this.selectedTasks = []
      this.html = ''
      this._getBookCatalogue()
    },

    close() {
      this.dialogShow = false
      this.selectedNodes = []
      this.selectedContent = ''
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 80vh;
  display: flex;
  justify-content: space-between;

  .left {
    width: 200px;
    min-width: 200px;
    height: 100%;
    overflow: auto;
    @include scrollBar;

    ::v-deep .el-checkbox__inner::after {
      border-width: 2px;
    }

    ::v-deep .el-checkbox__label {
      padding-left: 3px !important;
    }

    ::v-deep .el-tree-node__label {
      width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 8px;
    }
  }
}

.rich-text-container {
  width: 100%;
  height: 95%;
  padding: 20px;
  margin-left: 20px;
  position: relative;
  background: #fff;

  .content-wrapper {
    height: calc(100% - 30px);
    overflow: hidden;

    .rich-text-content {
      height: 100%;
      overflow-y: auto;
      @include scrollBar;
    }
  }
}

.print-btn {
  display: flex;
  align-items: center;
  height: 30px;
  position: relative;
  margin-top: 10px;

  .print {
    padding: 6px;
    font-size: 10px;
  }

  .tips {
    cursor: pointer;
    font-size: 10px;
    margin-left: 10px;
    margin-top: 26px;
  }

  .waring {
    font-size: 10px;
    position: absolute;
    bottom: -30px;
    right: 8vw;
    color: red;
  }
}

.emty {
  width: 100%;
  height: 93%;
}

.print {
  padding: 6px;
  font-size: 10px;
  margin-top: 10px;
}

.rich-text-content {
  height: 93%;
  overflow: auto;
  @include scrollBar;
  ::v-deep .title {
      color: #2F80ED;
      font-size: 12px;
      font-weight: 800;
    }

  ::v-deep * {
    max-width: 100%;
  }

  ::v-deep pre > code {
    display: block;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
