<template>
  <el-tooltip
    ref="tlp"
    effect="dark"
    popper-class="custom-tooltip"
    :disabled="tooltipFlag"
    :placement="placement"
  >
    <div class="tooltip" @mouseover="onMouseOver('name')">
      <span ref="name">{{ text ? text : "-" }}</span>
    </div>
    <div slot="content" style="font-size:1rem">{{ text }}</div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'EllipsisTooltip',
  props: {
    text: { type: String, default: '' }, // 字符内容
    placement: { type: String, default: 'top' }
  },
  data () {
    return {
      tooltipFlag: false
    }
  },
  methods: {
    onMouseOver (str) {
      const tag = this.$refs[str]
      if (tag) {
        const parentWidth = tag.parentNode.offsetWidth // 获取元素父级可视宽度
        const contentWidth = tag.offsetWidth // 获取元素可视宽度
        this.tooltipFlag = contentWidth <= parentWidth
      }
    }
  }
}
</script>
<style>
.tooltip {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 2rem;
  cursor: pointer;
}
.custom-tooltip {
  max-width: 400px;
  max-height: 400px;
  /* background-color: #3280fc !important; */
}
.custom-tooltip[x-placement^="top"] .popper__arrow {
  /* border-top-color: #3280fc !important; */
}
.custom-tooltip[x-placement^="top"] .popper__arrow::after {
  /* border-top-color: #3280fc !important; */
}

</style>

