<template>
  <NormalDialog
    v-if="dialogShow"
    v-loading="loading"
    width="1500px"
    title="打印"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    element-loading-text="正在生成内容，请稍候"
    @closeDialog="close"
  >
    <div class="content_main">
      <div v-loading="loading" class="pdf_main" @scroll="onScroll">
        <div id="pdfContainer">
          <div v-if="pdfPages.length === 0">未加载 PDF</div>
          <div v-else class="pages">
            <div
              v-for="(page, index) in visiblePages"
              :key="index"
              class="page-container"
            >
              <canvas :ref="`canvas${index}`"></canvas>
              <div class="page-number">{{ index + 1 + '/' + totalPages }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="title">打印范围：</div>
        <div class="print_info">
          范围从<el-input v-model="from" type="number" />至<el-input
            v-model="to"
            type="number"
          />
        </div>
        <div class="descrip">
          试用试教，仅支持打印 {{ startPage }}–{{ endPage }}页
        </div>
        <div class="button_group">
          <el-button class="button" @click="close">取消</el-button>
          <el-button
            class="button"
            type="primary"
            @click="printPages"
          >开始打印</el-button>
        </div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import * as pdfjsLib from 'pdfjs-dist/build/pdf'
pdfjsLib.GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.entry')
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'

export default {
  components: {
    NormalDialog
  },
  props: {
    bookConfig: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      pdfDoc: null, // PDF.js 加载的 PDF 对象
      pdfPages: [], // 存储页面对象的数组
      visiblePages: [], // 当前可见页面
      dialogShow: false, // 控制对话框的显示
      loading: false, // 用于控制加载指示器的显示
      pageLimit: 5, // 每次加载的最大页面数
      loadedPages: 0, // 已加载页面数
      totalPages: 0, // 总页数
      startPage: 1, // 打印起始页
      endPage: 1, // 打印结束页
      from: 0,
      to: 0
    }
  },
  methods: {
    close() {
      this.dialogShow = false
    },

    open() {
      this.dialogShow = true
      this.onFileChange(this.bookConfig.originBookPath)
      const fromTo = this.bookConfig.originBookPrintPages.split(',')
      this.startPage = Number(fromTo[0])
      this.endPage = Number(fromTo[1])
      this.from = this.startPage
      this.to = this.endPage
    },

    async onFileChange(url) {
      if (!url) {
        console.error('URL 不能为空')
        return
      }

      this.loading = true

      try {
        // 使用网络地址加载 PDF
        this.pdfDoc = await pdfjsLib.getDocument(url).promise
        this.totalPages = this.pdfDoc.numPages // 获取总页数
        console.log(this.pdfDoc.numPages)
        console.log('PDF loaded', this.pdfDoc)

        // 渲染 PDF
        this.renderPDF()
      } catch (error) {
        console.error('Error loading PDF', error)
        this.loading = false
      }
    },

    async renderPDF() {
      if (!this.pdfDoc) {
        console.error('No PDF document found')
        return
      }

      this.pdfPages = []
      this.visiblePages = []

      // 初始加载第一页到 pageLimit 页
      const startPage = 0
      const endPage = Math.min(this.pageLimit, this.totalPages)
      this.visiblePages = Array.from(
        { length: endPage - startPage },
        (_, i) => i
      )
      for (let i = startPage; i < endPage; i++) {
        await this.renderPage(i)
      }

      this.loadedPages = endPage

      // 加载完毕，隐藏加载指示器
      this.loading = false
    },

    async renderPage(pageIndex) {
      try {
        const page = await this.pdfDoc.getPage(pageIndex + 1)
        console.log('Rendering page', pageIndex + 1)

        this.pdfPages.push(page)
        const viewport = page.getViewport({ scale: 2 })

        // 等待 DOM 渲染完成后再渲染到 Canvas
        this.$nextTick(() => {
          const canvas = this.$refs[`canvas${pageIndex}`]
          if (canvas && canvas.length > 0) {
            const context = canvas[0].getContext('2d')
            canvas[0].width = viewport.width
            canvas[0].height = viewport.height

            page
              .render({ canvasContext: context, viewport })
              .promise.then(() => {
                console.log(`Page ${pageIndex + 1} rendered`)
              })
              .catch((error) => {
                console.error(`Error rendering page ${pageIndex + 1}`, error)
              })
          } else {
            console.error(
              `Canvas element for page ${pageIndex + 1} is not available`
            )
          }
        })
      } catch (error) {
        console.error(`Error rendering page ${pageIndex + 1}`, error)
      }
    },

    // 滚动加载更多页面
    onScroll(event) {
      const container = event.target
      const bottom =
        container.scrollTop + container.clientHeight >=
        container.scrollHeight - 10

      if (bottom && this.loadedPages < this.totalPages) {
        const nextPageStart = this.loadedPages
        const nextPageEnd = Math.min(
          nextPageStart + this.pageLimit,
          this.totalPages
        )

        // 逐页渲染
        for (let i = nextPageStart; i < nextPageEnd; i++) {
          this.renderPage(i)
        }

        // 更新可见页面范围
        this.visiblePages.push(
          ...Array.from(
            { length: nextPageEnd - nextPageStart },
            (_, i) => nextPageStart + i
          )
        )
        this.loadedPages = nextPageEnd
      }
    },

    async printPages() {
      if (
        this.from < 1 ||
        this.to > this.totalPages ||
        this.from > this.to ||
        this.from < this.startPage ||
        this.to > this.endPage
      ) {
        this.$message.warning('请输入有效的页码')
        return
      }
      this.loading = true
      const printContent = []
      const printWindow = window.open('', '', 'width=800,height=600')

      // 获取指定范围内的所有页面并渲染为图像
      for (let i = this.from - 1; i < this.to; i++) {
        try {
          const page = await this.pdfDoc.getPage(i + 1)
          const viewport = page.getViewport({ scale: 2 })
          const canvas = document.createElement('canvas')
          canvas.width = viewport.width
          canvas.height = viewport.height
          const context = canvas.getContext('2d')
          await page.render({ canvasContext: context, viewport }).promise
          this.pdfPages[i] = canvas.toDataURL()
        } catch (error) {
          console.error(`Error rendering page ${i + 1} for printing`, error)
          continue
        }

        const imageData = this.pdfPages[i]
        if (imageData) {
          printContent.push(`<img src="${imageData}" style="width:100%;">`)
        }
      }

      printWindow.document.write(`
          <html>
            <head>
              <title>打印 PDF</title>
              <style> img { width: 100%; } </style>
            </head>
            <body>
              ${printContent.join('<br>')}
            </body>
          </html>
        `)
      setTimeout(() => {
        printWindow.print()
        printWindow.close()
        this.loading = false
      }, 1000)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.content_main {
  width: 100%;
  height: 450px;
  display: flex;
  justify-content: space-between;
}
.pdf_main {
  width: 67%;
  height: 100%;
  overflow: auto;
  padding-right: 10px;
  @include scrollBar;
}
.right {
  padding: 10px;
  width: 30%;
  color: #000;
  .title {
    font-size: 14px;
    font-weight: bold;
  }
  .print_info {
    display: flex;
    width: 80%;
    justify-content: space-between;
    flex-wrap: nowrap;
    font-size: 12px;
    margin-top: 30px;
    align-items: center;
    ::v-deep .el-input {
      width: 50px;
      .el-input__inner {
        font-size: 12px;
        text-align: center;
      }
    }
  }
  .descrip {
    color: #2f80ed;
    margin-top: 10px;
    font-size: 10px;
  }
  .button_group {
    width: 80%;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .button {
      width: 50px;
      height: 25px;
      font-size: 10px;
      padding: 5px;
    }
  }
}
#pdfContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.page-container {
  position: relative;
  margin: 10px;
  width: 500px;
  height: auto;
}

canvas {
  width: 100%;
  height: auto;
  border: 1px solid #ededed;
}

.page-number {
  position: absolute;
  bottom: 10px;
  right: 10px;
  color: #000;
  font-size: 14px;
}

.edu-btn {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  text-align: center;
  border-radius: 5px;
  cursor: pointer;
}

.edu-btn:hover {
  background-color: #0056b3;
}
</style>
