<template>
  <el-dialog
    :show-close="false"
    width="30%"
    :close-on-click-modal="true"
    :visible="showShare"
    :custom-class="'activity-share'"
    @close="closeDialog"
  >
    <div class="wrap">
      <div class="header">
        <span>分享</span>
        <i class="el-dialog__close el-icon el-icon-close" @click="closeDialog"></i>
      </div>
      <div class="line mb-20"></div>
      <div ref="qrCodeUrl" class="qrcode mb-10"></div>
      <div class="hint mb-30">手机微信扫描二维码</div>
      <!-- <div class="flex mh-20">
        <div class="url flex items-center mr-10">
          <img :src="linkSvg" alt="" />
          <div class="text">{{ urlH5 }}</div>
        </div>
        <div
          v-clipboard:copy="urlH5"
          v-clipboard:success="onCopy"
          v-clipboard:error="onError"
          class="copy"
        >复制</div>
      </div> -->
    </div>
  </el-dialog>
</template>

<script>
import QRCode from 'qrcodejs2'
import linkSvg from '@/assets/images/activity/link.svg'
export default {
  data () {
    return {
      showShare: false,
      linkSvg,
      createSuccess: false,
      BookId: this.$route.query.id,
      urlH5: ''
    }
  },
  mounted () {
    const origin = window.location.origin
    this.urlH5 = origin + `/#/bingoBook/bookInfo?id=${this.BookId}`
  },
  methods: {
    openDialog () {
      this.showShare = true
      if (!this.createSuccess) this.creatQrCode()
    },
    closeDialog () {
      this.showShare = false
    },
    creatQrCode () {
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl, {
            text: this.urlH5, // 需要转换为二维码的内容
            width: 123,
            height: 123,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
          this.createSuccess = true
        }
      )
    },
    onCopy () {
      this.$message.success('内容已复制到剪切板！')
    },
    onError () {
      this.$message.error('抱歉，复制失败！')
    }
  }
}
</script>

  <style lang="scss">
  .activity-share {

    background: #FFFFFF;
    border-radius: 5px;
    padding-bottom: 23px;

    .el-dialog__header {
      display: none;
    }

    .el-dialog__body {
      padding: 0;
    }

    .wrap {

      .header {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;

        span {
          font-weight: 500;
          font-size: 18px;
          color: #181919;
        }

        .el-dialog__close  {
          font-size: 16px;
          color: #000000;
          cursor: pointer;
        }
      }

      .line {
        height: 1px;
        border: 1px solid #DADFEA;
      }

      .qrcode {
        width: 123px;
        height: 123px;
        margin: 0 auto;
        img {
          height: 100%;
          width: 100%;
        }
      }

      .hint {
        font-weight: 400;
        font-size: 12px;
        color: #7A7A7B;
        text-align: center;
      }

      .url {
        height: 33px;
        border: 1px solid #DADFEA;
        border-radius: 5px;
        flex: 1;
        padding: 0 10px;

        img {
          width: 14px;
          height: 14px;
          margin: auto 10px auto 0;
        }

        .text {
          font-weight: 400;
          font-size: 12px;
          color: #3B4E61;
          letter-spacing: 0;
          line-height: 33px;
          @include ellipses(1);
        }
      }

      .copy {
        width: 65px;
        height: 33px;
        line-height: 33px;
        background: #3479FF;
        border-radius: 5px;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        text-align: center;
        cursor: pointer;
      }

      .mb-10 {
        margin-bottom: 10px;
      }

      .mb-20 {
        margin-bottom: 20px;
      }

      .mb-30 {
        margin-bottom: 30px;
      }

      .mr-10 {
        margin-right: 10px;
      }

      .mh-20 {
        margin: 0 20px;
      }
    }

  }
  </style>

