<template>
  <div>
    <div class="main">
      <!-- <i class="el-icon-close close_drawer" @click="drawer = false"></i> -->
      <div class="content" @click="readfunc" v-html="html"></div>
    </div>
    <tipsPop ref="tips" :position="tipsPositon" :info="tipsInfo" />
    <imgGroupPop ref="images" :info="imgListInfo" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <doTest ref="doTest" :test-id="testId" :ids="ids" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <doExcelTraing ref="excelRraing" :student-course-id="studentCourseId" />
  </div>
</template>

<script>
import { getContent } from '@/api/digital-api.js'
import { throttle } from '@/utils/index'
import { saveAs } from 'file-saver'
import { getSqlPlatformToken } from '@/api/training-api'
import tipsPop from './tipsPop.vue'
import imgGroupPop from './imgGroupPop.vue'
import videoCardPop from './videoPop.vue'
import doExcelTraing from '../..//editor/components/doExcelTraing.vue'
import officeView from '../../editor/components/officeView.vue'
import doTest from '@/views/digitalbooks/editor/components/doTest.vue'
export default {
  components: {
    doTest,
    officeView,
    doExcelTraing,
    videoCardPop,
    imgGroupPop,
    tipsPop

  },
  data () {
    return {
      id: [],
      drawer: false,
      loading: true,
      html: '',
      officeUrl: '',
      token: '',
      studentCourseId: 0,
      testId: '0',
      ids: '',
      imgListInfo: null,
      tipsPositon: {
        top: 0,
        left: 0
      },
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      },
      tipsInfo: {
        keyword: '',
        content: ''
      }
    }
  },
  watch: {
    async '$route' (to, from) {
      if (to.query.ids !== from.query.ids) {
        this.id = this.$route.query.ids.split(',')
        this.$nextTick(() => {
          this._getContent()
        })
      }
    }
  },
  mounted () {
    this.id = this.$route.query.ids.split(',')
    this.$nextTick(() => {
      this._getContent()
    })
  },
  beforeDestroy () {
  },
  methods: {
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      console.log(url, fileName)
      if (this.getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.images.open()
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    },
    readfunc (e) {
      this.$emit('clickTest', e.target)
      this.$refs.tips.close()
      if (e.target.classList.contains('img_card_button')) {
        console.log(1)
        this.imgListInfo = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.images.open()
      }
      if (e.target.classList.contains('video_button')) {
        e.preventDefault()
        this.videoInfo = {
          src: e.target.src,
          poster: e.target.poster,
          text: e.target.parentNode.getElementsByTagName('p')[1].innerText
        }
        this.$refs.videoCard.open()
      }
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        let y = item.getBoundingClientRect().top + 20
        if (window.innerHeight - e.pageY < 195) {
          y = window.innerHeight - 200
        }
        this.tipsPositon = {
          top: y,
          left: item.getBoundingClientRect().left
        }
        console.log(item, item.children)
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.show()
      }
      if (e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          const url = e.target.parentNode.children[1].innerText
          const fileName = e.target.parentNode.children[2].innerText
          const notif = Notification({
            title: fileName,
            dangerouslyUseHTMLString: true,
            message: '',
            duration: 0
          })
          throttle(function () {
            const xhr = new XMLHttpRequest()
            xhr.open('get', url)
            xhr.responseType = 'blob'
            xhr.addEventListener('progress', (e) => {
              const complete = Math.floor(e.loaded / e.total * 100)
              notif.message = complete + '%'
              if (complete >= 100) {
                notif.close()
              }
            })
            xhr.send()
            xhr.onload = function () {
              if (this.status === 200 || this.status === 304) {
                const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
                saveAs(blob, fileName)
              }
            }
          }, 2000)
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.doTest.open()
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
      if (e.target.classList.contains('to_excel')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.$refs.excelRraing.open(id)
        // this.openExcelTraing(id)
      }
    },
    async _getContent() {
      let tem = ''
      for (let i = 0; i < this.id.length; i++) {
        const { data } = await getContent({
          catalogueId: this.id[i]
        })
        tem += data ? data[0].data : ''
      }
      this.html = tem
      this.$nextTick(() => {
        this.setVideo()
        window.MathJax.typesetPromise()
      })
    },
    setVideo () {
      const videoList = document.getElementsByTagName('video')
      for (let i = 0; i < videoList.length; i++) {
        videoList[i].setAttribute('controlslist', 'nodownload noplaybackrate')
        videoList[i].setAttribute('disablePictureInPicture', true)
      }
      const audioList = document.getElementsByTagName('audio')
      for (let i = 0; i < audioList.length; i++) {
        audioList[i].setAttribute('controlslist', 'nodownload noplaybackrate')
        // audioList[i].setAttribute('disablePictureInPicture', true)
      }
    }
  }
}
</script>

      <style lang="scss" scoped>
      .main{
        width: 100%;
        height: 100%;
        background: rgb(224, 234, 244);
        .content{
          width: 100%;
          height: 100%;
          padding: 10px;
          overflow: auto;
          @include scrollBar;
        }
      }
      .iframe{
        width: 100%;
        height: 100%;
      }
      ::v-deep .el-drawer__container {
        pointer-events: none;
        .close_drawer {
          position: absolute;
          z-index: 999;
          right: 10px;
          top: 5px;
          font-size: 15px;
          cursor: pointer;
          color: rgb(24, 22, 22);
        }
        overflow: hidden;
      }
      .AIdrawerWrapper {
        pointer-events: none;
        // position: relative;
      }
      ::v-deep .el-drawer {
        box-shadow: 0 0.727273vw 0.909091vw -0.454545vw rgba(0, 0, 0, 0.2),
          0 1.454545vw 2.181818vw 0.181818vw rgba(0, 0, 0, 0.14),
          0 0.545455vw 2.727273vw 0.454545vw rgba(0, 0, 0, 0.12);
      }
      ::v-deep .el-drawer__body {
        pointer-events: auto;
        padding: 0px;
        background: #fff;
        overflow: hidden;
      }
      </style>
