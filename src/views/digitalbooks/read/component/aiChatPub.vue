<template>
  <el-drawer
    title="我是标题"
    :visible.sync="drawer"
    :direction="direction"
    size="60%"
    :modal="false"
    :with-header="false"
    :wrapper-closable="false"
    modal-class="AIdrawer"
    class="AIdrawerWrapper"
    :append-to-body="false"
    :modal-append-to-body="false"
  >
    <div class="drawer-main" v-loading="loading">
      <div class="drawer-header">
        <img style="height:30px" src="@/assets/digitalbooks/read/aimate.png" fit='cover' />
        <i class="el-icon-close close_drawer" @click="drawer = false"></i>
      </div>
      <div class="drawer-content">
        <iframe
          v-if="drawer"
          id="scratch-iframe"
          ref="scratchFrame"
          :src="url"
          style="border: none"
          width="100%"
          height="100%"
          allowfullscreen
          allow="microphone *; camera *"
          sandbox="allow-same-origin allow-scripts allow-popups allow-modals"
          @load="handleLoad"
        ></iframe>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  data() {
    return {
      drawer: false,
      direction: 'rtl', // 可选值：'ltr', 'rtl', 'ttb', 'btt'
      url: 'https://chatpub.com.cn/chat/share?shareId=52c0mrcsmrrrpyl6jx7yymxs',
      loading: false
    }
  },
  methods: {
    open() {
      this.drawer = true
    },
    close() {
      this.drawer = false
    },
    handleLoad() {
      this.loading = false
    }
  }
}
</script>

<style scoped lang='scss'>
.drawer-main{
  width: 100%;
  height: 100%;
  background: white;
  overflow: hidden;
  .drawer-header{
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #E0E0E0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    background-color: #cbe2ff;
    .close_drawer {
      font-size: 15px;
      cursor: pointer;
    }
  }
  .drawer-content{
    width: 100%;
    height: calc(100% - 40px);
    box-sizing: border-box;
  }
}
::v-deep .el-drawer {
  box-shadow: 0 0.727273vw 0.909091vw -0.454545vw rgba(0, 0, 0, 0.2),
  0 1.454545vw 2.181818vw 0.181818vw rgba(0, 0, 0, 0.14),
  0 0.545455vw 2.727273vw 0.454545vw rgba(0, 0, 0, 0.12);
}
</style>
