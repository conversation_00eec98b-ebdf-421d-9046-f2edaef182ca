<template>
  <NormalDialog
    v-if="dialogShow"
    width="1500px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="content">
        <img :src="src" alt="" />
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },
  props: {
    src: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dialogShow: false,
      appendToBody: false,
      title: '查看图片',
      index: 0

    }
  },
  mounted () {
  },
  methods: {
    close () {
      this.dialogShow = false
    },
    open () {
      this.dialogShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-dig {
  .content {
    display: flex;
    justify-content: space-between;
    width: 100%;
     img {
        max-width: 100%;
        max-height:700px;
        display: block;
        margin: 0 auto;
      }

  }
}
</style>
