<template>
  <NormalDialog
    v-if="dialogVisible"
    width="40vw"
    :title="'笔记'"
    :is-center="false"
    :dialog-visible="dialogVisible"
    :append-to-body="true"
    @closeDialog="dialogVisible = false"
    @click="test"
  >
    <div class="w flex-col">
      <p class="ellipsis"><Tooltip :text="'引用：'+title" /></p>
      <el-input
        v-model="textarea"
        class="input"
        type="textarea"
        :rows="5"
        placeholder="请输入内容"
      />
    </div>
    <template #footer>
      <el-button class="button_add" @click="dialogVisible=false">取消</el-button>
      <el-button class="button_add" type="primary" @click="addNote">{{ editType }}笔记</el-button>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import Tooltip from './toolTips.vue'
import { digitalNotes } from '@/api/digital-api.js'
export default {
  components: {
    NormalDialog,
    Tooltip
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    editType: {
      type: String,
      default: '添加'
    },
    catalogue: {
      type: Object,
      default: null
    },
    form: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      dialogVisible: false,
      textarea: ''

    }
  },
  methods: {
    test () {
      console.log(111)
    },
    show () {
      this.dialogVisible = true
      this.textarea = this.form ? this.form.notes : ''
    },
    hide () {
      this.dialogVisible = false
    },
    addNote () {
      digitalNotes({
        catalogueId: this.catalogue.id,
        apiType: this.editType === '添加' ? 'create' : 'update',
        id: this.editType === '添加' ? null : this.form.id,
        digitalNotesType: 'notes',
        selection: this.title,
        notes: this.textarea
      }).then(res => {
        if (res.code === 200) {
          this.editType === '添加' ? this.$message.success('添加成功') : this.$message.success('编辑成功')
        } else {
          this.$message.error(res.message)
        }
        this.$emit('success')
        this.hide()
      })
    }
  }

}
</script>

<style scoped>
 .content{
  display:block;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 1rem;
  width:90%; /* 超过该宽度将显示省略号 */
}
.input{
  font-size: 1rem;
}
.button_add{
  font-size: 1rem;
  padding: 0.5rem 0.8rem;
}
</style>
