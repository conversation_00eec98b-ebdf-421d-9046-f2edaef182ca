<template>
  <div class="editor-dig bg" :style="show ? hasLeftWidth : noLeftWidth">
    <div v-if='showBg' class='knowledge-bg'></div>
    <noteMenu
      ref="noteMenu"
      :position="position"
      @heighLight="heighLight"
      @copyText="copyText"
      @baidu="baidu"
      @addNote="addNote"
    />
    <iframeBaidu ref="iframeBaidu" :item="baiduItem" />
    <!-- <div v-show="!taskMode && !isAuthCodeMode" class="head-box" :style="show ? hasLeft : noLeft">
      <img
        class="back"
        src="@/assets/digitalbooks/arrow-left.svg"
        @click="back"
      />
      <div class="head-title article-singer-container">
        {{ bookInfo.title }}
      </div>

      <div class="share" @click="handleTips">
        <div>
          <img src="../../../assets//digitalbooks/share.svg" />
        </div>
        分享
      </div>
      <div v-if="!preMode" class="print">
        <div>
          <img src="../../../assets//digitalbooks/print.svg" />
        </div>
        打印
        <div class="type">
          <div @click="handlePrintBook">数字教材</div>
          <div @click="handlePrint">原版教材</div>
          <div @click="handlePrintTask">工单打印</div>
        </div>
      </div>
    </div> -->

    <div class="content-box" :style="show ? hasLeft : noLeft">
      <div v-show="show" class="card-left">
        <div class="flex justify-between items-center">
          <div class="mulu">目录</div>
          <div class="show-btn">
            <i v-show="show" class="el-icon-s-fold" @click="show = false"></i>
          </div>
        </div>
        <template v-if="catalogueList">
          <ul v-for="item in catalogueList" :key="item.id">
            <digTree
              :model="item"
              :index="0"
              :heighit-id="heighitId"
              :is-dynamic="false"
              @handleClick="handleTree"
            />
          </ul>
        </template>
      </div>
      <div v-show="!show" class="contorl">
        <div class="flex flex-col items-center pointer" @click="show = true">
          <i v-show="!show" class="el-icon-s-unfold"></i>
          目录
        </div>
      </div>
      <div
        id="readbox"
        :class="{ 'card-right': show, 'card-right-all': !show }"
        :style="
          !preMode
            ? {
              backgroundColor: backgroundColor.color,
              fontFamily: checkFont(font.defaultFont)
                ? font.defaultFont
                : font.font + '!important',
            }
            : {}
        "
        @mouseup="showMenu"
        @click="readfunc"
        @wheel="setPage"
      >
        <div
          v-if="showPageText"
          ref="turnPageText"
          class="turn-page"
          :class="direction === 1 ? 'next-page-text' : 'up-page-text'"
        >
          {{ direction === 1 ? '继续滑动切换下一章' : '继续滑动切换上一章' }}
        </div>

        <transition
          :css="false"
          mode="out-in"
          @before-enter="beforeEnter"
          @enter="enter"
          @leave="leave"
        >
          <div
            id="serchContent"
            :key="contentVersion"
            ref="richContext"
            class="editor-content-view"
            :data-direction="direction"
            @scroll="setToTop"
            v-html="html"
          ></div>
        </transition>
        <div v-if="showTop" class="to_top">
          <img class="arrow" src="@/assets/digitalbooks/read/back_top.png" alt="" @click="scrollToTop" />
          <div class="tips">回到顶部</div>
        </div>
        <template v-if="!html">
          <div class="w h flex items-center justify-center">
            <div class="flex flex-col items-center">
              <img
                class="empty"
                src="@/assets/images/empty.png"
                alt="占位图2"
              />
              <div>{{ tips }}</div>
            </div>
          </div>
        </template>
      </div>

      <!-- <div v-if="!preMode && !isAuthCodeMode" class="edit_box"> -->
        <!-- <img
          v-if="hasKnowledge"
          class="word_button"
          src="@/assets/digitalbooks/read/word_button.png"
          @click="showKown"
        />
        <img
          class="task_button"
          src="@/assets/digitalbooks/read/ai_read.png"
          @click="showAi"
        /> -->
        <!-- <img
          class="task_button"
          src="@/assets/digitalbooks/read/task_button.png"
          @click="showKown"
        /> -->
        <!-- <el-popover
          popper-class="pop_search"
          placement="right-end"
          width="380"
          trigger="click"
        >
          <div id="findwindow"></div>

          <div slot="reference" class="trigger_content">
            <img src="@/assets/digitalbooks/search.svg" alt="" />
            <p style="font-size: 12px">搜索</p>
          </div>
        </el-popover>

        <el-popover
          v-model="noteShow"
          popper-class="pop_note"
          placement="right-end"
          width="909"
          trigger="click"
        >
          <el-card class="pop_card">
            <div slot="header" style="padding: 0" class="pop_card_header">
              <span>笔记</span>
            </div>
            <div style="display: flex">
              <div class="note_content">
                <div
                  v-if="noteList.length === 0"
                  style="height: calc(100% - 72px)"
                  class="w flex justify-center items-center"
                >
                  <el-empty
                    style="width: 5rem"
                    :image="empty3"
                    description="暂无数据"
                  />
                </div>
                <div
                  v-for="(item, index) in noteList"
                  :key="index"
                  class="note_item"
                  :class="index == noteIndex ? 'selected' : ''"
                  @click="noteIndex = index"
                >
                  <p>
                    引用：<span>{{ item.selection }}</span>
                  </p>
                  <p>{{ item.notes }}</p>
                </div>
              </div>
              <div class="note_info">
                <p v-if="noteList[noteIndex]">
                  引用：<span>{{ noteList[noteIndex].selection }}</span>
                </p>
                <p v-if="noteList[noteIndex]" class="note">
                  笔记：<span>{{ noteList[noteIndex].notes }}</span>
                </p>
                <div v-if="noteList[noteIndex]" class="flex">
                  <el-button
                    class="button"
                    type="primary"
                    @click="editNote(noteList[noteIndex])"
                  >编辑</el-button>
                  <el-button
                    class="button"
                    type="danger"
                    @click="deleteNote(noteList[noteIndex])"
                  >删除</el-button>
                </div>
              </div>
            </div>
          </el-card>
          <div slot="reference" class="trigger_content">
            <img src="@/assets/digitalbooks/note.svg" alt="" />
            <p style="font-size: 12px">笔记</p>
          </div>
        </el-popover>
        <el-popover
          popper-class="pop_note"
          placement="right-end"
          width="242"
          trigger="click"
        >
          <el-card class="pop_card">
            <p>正文字号</p>
            <p class="size">
              {{
                fontTitleList.filter((item) => {
                  return item.value === size
                })[0].title
              }}
            </p>
            <div class="block">
              <span>小</span>
              <el-slider
                v-model="size"
                :min="15"
                :max="27"
                :step="3"
                :marks="marks"
                :show-stops="true"
                :show-tooltip="false"
              />
              <span>大</span>
            </div>

            <div class="block">
              <span>正文字体</span>
              <el-dropdown style="font-size: 0.8rem">
                <div class="el-dropdown-link">
                  <p>
                    {{ font.title
                    }}<i class="el-icon-arrow-down el-icon--right"></i>
                  </p>
                </div>
                <el-dropdown-menu
                  slot="dropdown"
                  style="font-size: 0.8rem !important"
                >
                  <el-dropdown-item
                    v-for="item in fontFamily"
                    :key="item.font"
                    class="drop_down_item"
                    style="line-height: 2rem !important"
                    @click.native="changeFontFamily(item)"
                  >{{ item.title }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <div class="block">
              <span>阅读背景</span>
              <el-dropdown style="font-size: 0.8rem" trigger="click">
                <div class="el-dropdown-link">
                  <div
                    class="color"
                    :style="{ backgroundColor: backgroundColor.color }"
                  ></div>
                  <p>
                    {{ backgroundColor.title
                    }}<i class="el-icon-arrow-down el-icon--right"></i>
                  </p>
                </div>
                <el-dropdown-menu slot="dropdown" style="font-size: 0.8rem">
                  <el-dropdown-item
                    v-for="item in colorList"
                    :key="item.color"
                    class="drop_down_item"
                    @click.native="changeBgColor(item)"
                  >
                    <div
                      class="color"
                      :style="{ backgroundColor: item.color }"
                    ></div>
                    <p>{{ item.title }}</p></el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </el-card>
          <div slot="reference" class="trigger_content">
            <img src="@/assets/digitalbooks/font.svg" alt="" />
            <p style="font-size: 12px">字体</p>
          </div>
        </el-popover>
      </div> -->

    </div>
    <addNoteDialog
      ref="addNoteDialog"
      :title="noteTitle"
      :edit-type="editNoteType"
      :catalogue="currChapter"
      :form="noteForm"
      @success="addNoteSuccess"
    />
    <tipsPop ref="tips" :position="tipsPositon" :info="tipsInfo" />
    <imgGroupPop ref="images" :info="imgListInfo" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <knowledgeGraph
      ref="knowledge"
      :book-id="bookId"
      :catalogue-list="catalogueList"
      :book-info="bookInfo"
      :curr-chapter="currChapter"
      :book-config="bookConfig"
      :hasKnowledge="hasKnowledge"
    />
    <PayToast ref="pay" :good-info="bookInfo&&bookInfo.goodsComm" />
    <doTest ref="doTest" :test-id="testId" :ids="ids" />
    <analysisTest ref="analysisTest" :test-id="testId" :ids="ids" />
    <shareBook ref="shareBook" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <doExcelTraing ref="excelRraing" :student-course-id="studentCourseId" />
    <aiChat ref="chatDrawer" :book-config="bookConfig" />
    <aiChatPub ref="chatPubRef"></aiChatPub>
    <Print ref="print" :book-config="bookConfig" />
    <printBook ref="printBook" />
    <printTask ref="printTask" />
    <doAiTraing ref="doAiTraing" :student-course-id="studentCourseId" />
  </div>
</template>

<script>
import {
  digitalNotes,
  getBook,
  getBookCatalogueByVersion,
  getContentEditor,
  getDigitalBookConfig,
  getDigitalNotesList,
  getUserCatalogue,
  hasKnowledge,
  updateReadProgress,
  checkAuthCodeValideStatus
} from '@/api/digital-api.js'
import { getCacheS } from '@/api/test-api'
import digTree from '@/views/digitalbooks/home/<USER>/digTree.vue'
import { getToken } from '@/utils/auth'
import { debounce, throttle } from '@/utils/index'
import Highlighter from 'web-highlighter'
import noteMenu from './component/noteMenu.vue'
import iframeBaidu from './component/baidu.vue'
import initFindWin from 'find5'
import addNoteDialog from './component/addNoteDialog.vue'
import empty3 from '@/assets/images/empty3.png'
import checkFont from './util/checkFont'
import tipsPop from './component/tipsPop.vue'
import imgGroupPop from './component/imgGroupPop.vue'
import videoCardPop from './component/videoPop.vue'
import aiChat from './component/aiChat.vue'
import aiChatPub from '@/views/digitalbooks/read/component/aiChatPub'
import doExcelTraing from '../editor/components/doExcelTraing.vue'
import doAiTraing from '../editor/components/doAiTraing.vue'
import { Notification } from 'element-ui'
import { saveAs } from 'file-saver'
import knowledgeGraph from './component/knowledgeGraph.vue'
import PayToast from '@/components/classPro/Pay/index.vue'
import officeView from '../editor/components/officeView.vue'
import doTest from '@/views/digitalbooks/editor/components/doTest.vue'
import analysisTest from '@/views/digitalbooks/editor/components/analysisTest.vue'
import shareBook from './component/shareBook.vue'
import Print from './component/print.vue'
import printBook from './component/bookPrint.vue'
import printTask from './component/printTask.vue'
import { getSqlPlatformToken } from '@/api/training-api'
import { mapGetters } from 'vuex'
import Prism from 'prismjs'
import 'prismjs/themes/prism-tomorrow.min.css'
import 'viewerjs/dist/viewer.css'
import VueViewer from 'v-viewer'
import Vue from 'vue'
import gsap from 'gsap'

Vue.use(VueViewer)
export default {
  components: {
    digTree,
    noteMenu,
    iframeBaidu,
    addNoteDialog,
    tipsPop,
    imgGroupPop,
    videoCardPop,
    knowledgeGraph,
    PayToast,
    doTest,
    analysisTest,
    shareBook,
    officeView,
    doExcelTraing,
    aiChat,
    aiChatPub,
    Print,
    printBook,
    doAiTraing,
    printTask
  },
  props: {
    preMode: {
      type: Boolean,
      default: false
    },
    taskMode: {
      type: Boolean,
      default: false
    },
    node: {
      type: [Object, Array],
      default: () => {
        return null
      }
    },
    bookIdProps: {
      type: String,
      default: '0'
    },
    prePublishMode: {
      type: Boolean,
      default: false
    },
    preEditionId: {
      type: String,
      default: '0'
    }
  },
  data () {
    return {
      isAuthCodeMode: false,
      authCodeValid: false,
      authCode: '',
      showBg: false,
      showTop: false,
      testId: '0',
      ids: '',
      tipsPositon: {
        top: 0,
        left: 0
      },
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      },
      tipsInfo: {
        keyword: '',
        content: ''
      },
      imgListInfo: null,
      empty3: empty3,
      editNoteType: '添加',
      copyHtml: '',
      marks: {
        15: '超小',
        18: '小',
        21: '正常',
        24: '大',
        27: '超大'
      },
      fontTitleList: [
        {
          title: '超小',
          value: 15
        },
        {
          title: '小',
          value: 18
        },
        {
          title: '正常',
          value: 21
        },
        {
          title: '大',
          value: 24
        },
        {
          title: '超大',
          value: 27
        }
      ],
      noteForm: null,
      show: true,
      tipsShow: true,
      position: { top: 0, left: 0 },
      html: '',
      contentCopy: '',
      bookId: this.bookIdProps,
      size: 21,
      noteIndex: 0,
      showSearch: false,
      studentCourseId: 0,
      currChapter: null,
      heighitId: 0,
      bookInfo: {},
      catalogueList: [],
      noteList: [
      ],
      highLineList: [],
      hasLeftWidth: { 'min-width': '1180px', background: '#e6f1fc' },
      noLeftWidth: { 'min-width': '970px', background: '#e6f1fc' },
      hasLeft: { 'min-width': '1140px' },
      noLeft: { 'min-width': '940px' },
      tips: '请先从目录中选择章节后开始阅读!',
      token: '',
      progress: 0,
      noteTitle: '',
      bookConfig: null,
      backgroundColor: {
        title: '默认',
        color: ''
      },
      font: {
        title: '默认',
        font: '',
        defaultFont: ''

      },
      baiduItem: '',
      highLineRenderList: [],
      colorList: [{
        title: '默认',
        color: ''
      }, {
        title: '浅灰色',
        color: '#F5F5F5'
      },
      {
        title: '银白色',
        color: '#E8E8E8'
      },
      {
        title: '象牙白',
        color: '#F2F1F1'
      },
      {
        title: '浅米色',
        color: '#FBF8F1'
      },
      {
        title: '乳白色',
        color: '#FEFDF9'
      },
      {
        title: '浅绿色',
        color: '#F0FFF0'
      },
      {
        title: '牛奶白',
        color: '#FCFBF9 '
      },
      {
        title: '玉米色',
        color: '#FFF8DC'
      },
      {
        title: '浅蓝灰',
        color: '#F1F8F9'
      },
      {
        title: '天蓝色',
        color: '#F2F8FC '
      },
      {
        title: '粉蓝色',
        color: '#E0EAF4'
      }
      ],
      fontFamily: [
        {
          title: '默认',
          font: ''
        },
        {
          title: '宋体',
          font: 'songti',
          defaultFont: 'SimSun'
        },
        {
          title: '黑体',
          font: 'heiti',
          defaultFont: 'SimHei'
        },
        {
          title: '仿宋',
          font: 'fangsong',
          defaultFont: 'FangSong'
        },
        {
          title: '楷体',
          font: 'kaiti',
          defaultFont: 'KaiTi'
        },
        {
          title: '微软雅黑',
          font: 'yahei',
          defaultFont: 'Microsoft Yahei'

        },
        {
          title: '思源宋体',
          font: 'siyuansongti',
          defaultFont: 'Source Han Serif SC'
        },
        {
          title: '思源黑体',
          font: 'siyuanheiti',
          defaultFont: 'Source Han Sans CN'
        }
      ], highlighter: null,
      noteShow: false,
      highLineData: [],
      noteSelection: null,
      hasKnowledge: true,
      openFlag: false,
      officeUrl: '',
      isLoading: false, // 防止重复翻页
      isAtTop: false, // 是否已经在顶部
      isAtBottom: false, // 是否已经在底部
      pageTurnThreshold: 0,
      pageTurnDelayTimeThreshold: 2000,
      showPageText: false,
      lastTurnPageTimestamp: 0,
      contentVersion: 0, // 内容版本号
      direction: 1, // 滚动方向
      boundaryThreshold: 50
    }
  },
  computed: {
    ...mapGetters(['id'])
  },
  watch: {
    'size' (val) {
      localStorage.setItem('readSetfontSize', val)
      this.size = val
      this.setSize()
    }
  },
  beforeMount() {
    if (this.$route.query && this.$route.query.to === 'graph') {
      this.showBg = true
    }

    this.authCode = this.$route.query.authcode || ''
    this.isAuthCodeMode = !!this.authCode

    if (this.authCode) {
      this.authCodeValid = true
    }
  },
  async mounted () {
    if (this.$route.query.id) {
      this.bookId = this.$route.query && this.$route.query.id
    }
    if (this.$route.query.bookId) {
      this.bookId = this.$route.query && this.$route.query.bookId
    }
    this.token = getToken()
    if (this.$route.query && this.$route.query.token) {
      this.token = `Bearer ${this.$route.query && this.$route.query.token}`
    }
    await this._getBook()
    this.studentCourseId = Number(this.$route.query.studentCourseId) || this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId
    if (!this.preMode) {
      initFindWin(false, 'serchContent')
      document.getElementsByClassName('txt')[0].innerHTML = '结果：<span id=total>0</span>'
      this.initHighLight()
      this.init()
    }
    await this._getDigitalBookConfig()
    this.checkKnowledge()
    if (this.$route.query && this.$route.query.to === 'graph') {
      setTimeout(() => {
        this.showKown()
      }, 200
      )
    }
    const chapterId = this.getReadingProgress(this.id, this.bookInfo.id)
    if (chapterId) {
      this.handleTree(this.findChapter(this.catalogueList, chapterId))
    }
    this.pageTurnDelayTimeThreshold = navigator.userAgent?.toLowerCase().indexOf('mac') !== -1 ? 1200 : 2000
    if (this.authCode) {
      this.verifyAuthCode()
    }
  },
  beforeDestroy () {
    if (document.getElementById('readbox')) {
      document.getElementById('readbox').removeEventListener('scroll', this.getProgress)
    }
    if (!this.preMode) {
      this.highlighter.dispose()
      document.removeEventListener('click',
        this.setEvent
      )
      document.removeEventListener('mousedown',
        this.$refs.noteMenu.hide
      )
    }
    if (!this.bookConfig.contentCopy) {
      window.removeEventListener('copy', this.enableCopy)
    }
  },
  methods: {
    async verifyAuthCode() {
      try {
        const { data } = await checkAuthCodeValideStatus({
          bookId: this.bookId,
          authCode: this.authCode
        })
        this.authCodeValid = data === true
        if (this.authCodeValid) {
          console.log('verity success:', this.authCode)
        } else {
          this.$message.error('授权码无效或已过期')
          await this._getBookCatalogue()
        }
      } catch (error) {
        console.error('授权码验证失败:', error)
        this.authCodeValid = false
        this.$message.error('授权码验证失败')
        await this._getBookCatalogue()
      }
    },
    handlePrintBook() {
      this.$refs.printBook.open(this.bookInfo.id, this.bookInfo.studentCourseId)
    },
    handlePrintTask() {
      this.$refs.printTask.open(this.bookInfo.id, this.bookInfo.studentCourseId)
    },
    handlePrint() {
      if (!(this.bookConfig && this.bookConfig.originBookPath)) {
        this.$message.warning('暂无原版教材')
        return
      }
      this.$refs.print.open()
    },
    findChapter(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i]
        }
      }

      return null
    },
    showAi() {
      if (this.bookId === '131'){
        this.$refs.chatPubRef.open()
      }else {
        this.$refs.chatDrawer.open()
      }
    },
    enableCopy(event) {
      this.$message.warning('为保护作者版权，当前教材暂不支持复制')
      event.preventDefault()
    },
    enableDonload() {
      const download = document.getElementsByClassName('download_button')
      for (let i = 0; i < download.length; i++) {
        download[i].style.display = 'none'
      }
    },
    async _getDigitalBookConfig() {
      const { data } = await getDigitalBookConfig({
        digitalBookId: this.bookInfo.id
      }, {
        authorization: this.token
      })
      this.bookConfig = data
      if (!this.bookConfig.contentCopy) {
        window.addEventListener('copy', this.enableCopy)
      }
    },
    setPage(event) {
      const container = this.$refs.richContext
      const { scrollTop, scrollHeight, clientHeight } = container ||
      { scrollTop: 0, scrollHeight: 0, clientHeight: 0 }

      // 计算边界距离
      const bottomDistance = scrollHeight - (scrollTop + clientHeight)
      const isNearTop = scrollTop < this.boundaryThreshold
      const isNearBottom = bottomDistance < this.boundaryThreshold

      const normalizedDeltaY = event.deltaY * (event.deltaMode === 1 ? 40 : 1)
      // 判断滚动趋势（惯性方向）
      const isScrollingDown = normalizedDeltaY > 0
      const speed = Math.abs(normalizedDeltaY)
      const isBoundary = (isNearBottom && isScrollingDown) || (isNearTop && !isScrollingDown)
      let node
      if (isBoundary) {
        event.preventDefault()
        event.stopPropagation()
        node = this.findAdjacentNode()
      }
      if (this.isLoading) return

      if (isNearBottom && isScrollingDown) {
        this.direction = 1
      } else if (isNearTop && !isScrollingDown) {
        this.direction = -1
      }
      const currentTime = Date.now()

      if (node) {
        if (!this.showPageText && isBoundary) {
          this.lastTurnPageTimestamp = currentTime
          this.showPageText = true
          return
        } else {
          this.showPageText = isBoundary
        }
      } else {
        this.showPageText = false
      }

      console.log('speed', event.deltaY)
      if (currentTime - this.lastTurnPageTimestamp < this.pageTurnDelayTimeThreshold) {
        return
      }
      // 有效切换条件（需同时满足）：
      // 1. 处于边界区域
      // 2. 滚动速度达到阈值（避免误触）
      // 3. 滚动方向与边界一致
      if (this.showPageText) {
        this.pageTurnThreshold += Math.max(speed, 20)
        if (this.pageTurnThreshold > 200 || speed > 80) {
          this.checkScrollEnd()
        }
      } else {
        this.pageTurnThreshold = 0
      }
    },

    checkScrollEnd() {
      const container = this.$refs.richContext
      const { scrollTop, scrollHeight, clientHeight } = container

      // 最终位置验证
      const isValid = this.direction === 1
        ? (scrollHeight - (scrollTop + clientHeight) < this.boundaryThreshold)
        : (scrollTop < this.boundaryThreshold)

      if (isValid) {
        this.direction === 1 ? this.loadNextChapter() : this.loadPreviousChapter()
        this.showPageText = false
        this.pageTurnThreshold = 0
        this.lastTurnPageTimestamp = Date.now()
      }
    },
    async loadPreviousChapter() {
      const preNode = this.findPreviousNode(this.catalogueList, this.currChapter.id)
      if (preNode) {
        this.isLoading = true
        await this.handleTree(preNode)
        this.$toast('已切换到上一章')
        this.isAtTop = false
        setTimeout(() => {
          this.isLoading = false
        }, 2000)
      }
    },
    // 加载下一章
    async loadNextChapter() {
      const nextNode = this.findNextNode(this.catalogueList, this.currChapter.id)
      if (nextNode) {
        this.isLoading = true
        await this.handleTree(nextNode)
        this.$toast('已切换到下一章')
        this.isAtBottom = false
        setTimeout(() => {
          this.isLoading = false
        }, 2000)
      }
    },

    findAdjacentNode(tree = this.catalogueList, targetId = this.currChapter.id, direction = this.direction) {
      const flattenedArray = []

      const flatten = node => {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      const index = flattenedArray.findIndex(node => node.id === targetId)
      if (index === -1) return null

      const targetIndex = index + direction

      return (targetIndex >= 0 && targetIndex < flattenedArray.length)
        ? flattenedArray[targetIndex]
        : null
    },
    getFlatList(tree) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)
      return flattenedArray
    },
    findNextNode(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i + 1] || null
        }
      }

      return null
    },
    findPreviousNode(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i - 1] || null
        }
      }

      return null
    },
    getReadingProgress(userId, bookId) {
      // 获取所有用户的阅读进度数据
      const allProgress =
        JSON.parse(localStorage.getItem('readingProgress')) || {}

      // 返回指定用户和书籍的当前章节
      return typeof (allProgress[userId]?.[bookId]) === 'number' ? allProgress[userId]?.[bookId] : null
    },
    saveReadingProgress(userId, bookId, chapter) {
      // 获取所有用户的阅读进度数据
      const allProgress =
        JSON.parse(localStorage.getItem('readingProgress')) || {}

      // 如果当前用户没有数据，则初始化
      if (!allProgress[userId]) {
        allProgress[userId] = {}
      }

      // 更新当前用户的书籍进度
      allProgress[userId][bookId] = chapter.id

      // 保存回 localStorage
      localStorage.setItem('readingProgress', JSON.stringify(allProgress))
    },
    setToTop() {
      if (this.$refs.richContext.scrollTop > 200) {
        this.showTop = true
      } else {
        this.showTop = false
      }
    },
    scrollToTop() {
      this.$refs.richContext.scrollTo({
        top: 0,
        behavior: 'smooth' // 平滑滚动
      })
    },
    async checkKnowledge () {
      const { data } = await hasKnowledge({
        digitalBookId: this.bookId
      })
      this.hasKnowledge = data.hasKnowledge
    },

    showKown () {
      this.$refs.knowledge.open()
    },
    checkFont (f) {
      if (typeof f !== 'string') {
        return false
      }
      var h = 'Arial'
      if (f.toLowerCase() === h.toLowerCase()) {
        return true
      }
      var e = 'a'
      var d = 100
      var a = 100
      var i = 100
      var c = document.createElement('canvas')
      var b = c.getContext('2d')
      c.width = a
      c.height = i
      b.textAlign = 'center'
      b.fillStyle = 'black'
      b.textBaseline = 'middle'
      var g = function (j) {
        b.clearRect(0, 0, a, i)
        b.font = d + 'px ' + j + ', ' + h
        b.fillText(e, a / 2, i / 2)
        var k = b.getImageData(0, 0, a, i).data
        return [].slice.call(k).filter(function (l) {
          return l !== 0
        })
      }
      return g(h).join('') !== g(f).join('')
    },
    readfunc (e) {
      this.$emit('clickTest', e.target)
      this.$refs.tips.close()
      if (e.target.classList.contains('img_card_button')) {
        console.log(1)
        this.imgListInfo = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.images.open()
      }
      if (e.target.classList.contains('video_button')) {
        e.preventDefault()
        this.videoInfo = {
          src: e.target.src,
          poster: e.target.poster,
          text: e.target.parentNode.getElementsByTagName('p')[1].innerText
        }
        this.$refs.videoCard.open()
      }
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        let y = item.getBoundingClientRect().top + 20
        if (window.innerHeight - e.pageY < 195) {
          y = window.innerHeight - 200
        }
        this.tipsPositon = {
          top: y,
          left: item.getBoundingClientRect().left
        }
        console.log(item, item.children)
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.show()
      }
      if (e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          const url = e.target.parentNode.children[1].innerText
          const fileName = e.target.parentNode.children[2].innerText
          const notif = Notification({
            title: fileName,
            dangerouslyUseHTMLString: true,
            message: '',
            duration: 0
          })
          throttle(function () {
            const xhr = new XMLHttpRequest()
            xhr.open('get', url)
            xhr.responseType = 'blob'
            xhr.addEventListener('progress', (e) => {
              const complete = Math.floor(e.loaded / e.total * 100)
              notif.message = complete + '%'
              if (complete >= 100) {
                notif.close()
              }
            })
            xhr.send()
            xhr.onload = function () {
              if (this.status === 200 || this.status === 304) {
                const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
                saveAs(blob, fileName)
              }
            }
          }, 2000)
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.doTest.open()
      }
      if (e.target.classList.contains('analysis_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.analysisTest.open()
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
      if (e.target.classList.contains('to_excel')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.openExcelTraing(id)
      }
      if (e.target.classList.contains('to_aiTrain')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.$refs.doAiTraing.open(id)
      }
      // if (e.target.tagName === 'IMG' && !e.target.classList.contains('arrow')) {
      //   const img = e.target
      //   const parent = img.parentElement
      //   const grandparent = parent ? parent.parentElement : null
      //   const parentHasClass = parent && parent.classList.contains('mceNonEditable')
      //   const grandparentHasClass = grandparent && grandparent.classList.contains('mceNonEditable')
      //   if (parentHasClass || grandparentHasClass) {
      //     return
      //   }
      //   this.$viewerApi({
      //     images: [e.target.src],
      //     options: {
      //       toolbar: false,
      //       navbar: false,
      //       title: false,
      //       transition: false
      //     }
      //   })
      // }
    },
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      console.log(url, fileName)
      if (this.getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.images.open()
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    setSize () {
      if (this.size === 21) {
        this.html = ''
        this.$nextTick(() => {
          this.html = this.contentCopy
          this.$nextTick(() => {
            window.MathJax.typesetPromise()
            Prism.highlightAll()
            this.setVideo()
            this.getHighLine()
            if (!this.bookConfig.attachFileDownload) {
              this.enableDonload()
            }
          })
        })
        return
      }
      const nodesObj = this.$refs.richContext.getElementsByTagName('*')
      const nodes = Object.values(nodesObj)
      this.$nextTick(() => {
        nodes.forEach(node => {
          node.style.fontSize = this.size + 'px'
        })
        // this.getHighLine()
      })
    },
    addNoteSuccess () {
      this.getNoteData()
      if (this.editNoteType === '添加') {
        this.highlighter.fromRange(this.noteSelection)
      }
    },
    addNoteData (data) {
      digitalNotes({
        catalogueId: this.currChapter.id,
        apiType: 'create',
        digitalNotesType: 'hightline',
        selection: data
      }).then(async () => {
        const { data } = await getDigitalNotesList({
          catalogueId: this.currChapter.id,
          digitalNotesType: 'hightline'
        })
        this.highLineData = data
      })
    },
    deleteNote (item) {
      try {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          digitalNotes({
            catalogueId: this.currChapter.id,
            apiType: 'delete',
            id: item.id
          }).then((res) => {
            if (res.code === 200) {
              this.getNoteData()
              this.$message.success('删除成功')
              const id = this.highLineList.filter(item1 => { return item.selection === item1.text })[0].id
              this.highlighter.remove(id)
              const deId = this.highLineData.filter(item => {
                return JSON.parse(item.selection).id === id
              })[0].id
              digitalNotes({
                catalogueId: this.currChapter.id,
                apiType: 'delete',
                digitalNotesType: 'hightline',
                id: deId
              })
            }
          })
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    async getHighLine () {
      if (!this.currChapter) {
        return
      }
      this.highLineList = []
      const buttons = document.getElementsByClassName('my-remove-tip')
      for (var i = 0; i < buttons.length; i++) {
        buttons[i].parentNode.removeChild(buttons[i])
      }
      this.highLineList = []
      const { data } = await getDigitalNotesList({
        catalogueId: this.currChapter.id,
        digitalNotesType: 'hightline'
      })
      this.highLineData = data
      data.forEach(item => {
        this.highLineList.push(JSON.parse(item.selection))
      })
      this.highLineRenderList = []
      this.highLineList.forEach(item => {
        // console.log(item)
        if (item.id) { this.highlighter.fromStore(item.startMeta, item.endMeta, item.text, item.id) }
      })
    },
    async getNoteData () {
      this.noteList = []
      const { data } = await getDigitalNotesList({
        catalogueId: this.currChapter.id,
        digitalNotesType: 'notes'
      })
      this.noteList = data
      this.noteIndex = 0
    },
    initHighLight () {
      this.highlighter = new Highlighter({
        $root: document.getElementById('readbox')
      })
      this.highlighter.on(Highlighter.event.CREATE, ({ sources }) => {
        if (this.highLineRenderList.includes(sources[0].id)) return
        this.highLineRenderList.push(sources[0].id)
        sources.forEach(s => {
          const position = this.getPosition(this.highlighter.getDoms(s.id)[0])
          this.createTag(position.top, position.left, s.id)
        })
        const res = this.highLineData.filter(item => {
          return JSON.parse(item.selection).id === sources[0].id
        })
        if (res.length === 0) {
          this.addNoteData(JSON.stringify(sources[0]))
          this.highLineList.push(sources[0])
        }
        //
      }).on(Highlighter.event.CLICK, ({ id }, _, e) => {
        const text = this.highLineList.filter(item => { return item.id === id })[0].text
        let flag = false
        const cleanText = text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
        for (let i = 0; i < this.noteList.length; i++) {
          if (cleanText === this.noteList[i].selection.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')) {
            flag = true
          }
        }
        const elements = document.getElementsByClassName('my-remove-tip')
        for (let i = 0; i < elements.length; i++) {
          // console.log(typeof (elements[i].style.display))
          if (elements[i].dataset.id === id) {
            if (flag) {
              elements[i].innerHTML = '查看笔记'
            }
            elements[i].style.display === 'block' ? elements[i].style.display = 'none' : elements[i].style.display = 'block'
          }
        }
      })
      document.addEventListener('click',
        this.setEvent
      )
      document.addEventListener('mousedown',
        this.$refs.noteMenu.hide
      )
    },
    setEvent (e) {
      const $ele = e.target
      // delete highlight
      if ($ele.classList.contains('my-remove-tip') && $ele.innerHTML === '删除高亮') {
        const id = $ele.dataset.id
        this.highlighter.removeClass('highlight-wrap-hover', id)
        this.highlighter.remove(id)
        const deId = this.highLineData.filter(item => {
          return JSON.parse(item.selection).id === id
        })[0].id
        digitalNotes({
          catalogueId: this.currChapter.id,
          apiType: 'delete',
          digitalNotesType: 'hightline',
          id: deId
        })
        $ele.parentNode.removeChild($ele)
      } else if ($ele.classList.contains('my-remove-tip') && $ele.innerHTML === '查看笔记') {
        let text = ''
        this.highLineData.forEach(item => {
          const id = $ele.dataset.id
          if (JSON.parse(item.selection).id === id) { text = JSON.parse(item.selection).text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '') }
        })
        this.noteList.forEach((item, index) => {
          if (item.selection.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '') === text) {
            this.noteShow = true
            this.noteIndex = index
            $ele.style.display = 'none'
          }
        })
      } else if (!$ele.classList.contains('highlight-mengshou-wrap')) {
        const elements = document.getElementsByClassName('my-remove-tip')
        for (let i = 0; i < elements.length; i++) {
          // console.log(typeof (elements[i].style.display))
          elements[i].style.display = 'none'
        }
      }
    },
    openExcelTraing(id) {
      this.$refs.excelRraing.open(id)
    },
    getPosition ($node) {
      const offset = {
        top: 0,
        left: 0
      }
      while ($node && $node.id !== 'serchContent') {
        offset.top += $node.offsetTop
        offset.left += $node.offsetLeft
        $node = $node.offsetParent
      }

      return offset
    },
    createTag (top, left, id) {
      const $span = document.createElement('span')
      $span.style.left = `${left - 20}px`
      $span.style.top = `${top - 25}px`
      $span.dataset['id'] = id
      $span.textContent = '删除高亮'
      $span.classList.add('my-remove-tip')
      this.$refs.richContext.appendChild($span)
    },
    baidu () {
      this.baiduItem = window.getSelection().toString()
      this.$nextTick(() => {
        this.$refs.iframeBaidu.show()
        this.$refs.noteMenu.hide()
      })
    },
    addNote () {
      this.noteForm = null
      this.editNoteType = '添加'
      this.noteSelection = window.getSelection().getRangeAt(0)
      this.$nextTick(() => {
        this.noteTitle = window.getSelection().toString()
        this.$refs.addNoteDialog.show()
        this.$refs.noteMenu.hide()
      })
    },
    editNote (item) {
      this.noteForm = item
      this.editNoteType = '编辑'
      this.$nextTick(() => {
        this.noteTitle = item.selection
        this.$refs.addNoteDialog.show()
        this.$refs.noteMenu.hide()
      })
    },
    heighLight () {
      const selection = window.getSelection()
      if (!selection.isCollapsed) {
        this.highlighter.fromRange(selection.getRangeAt(0))
        this.$refs.noteMenu.hide()
      }
    },
    copyText () {
      if (!this.bookConfig.contentCopy) {
        this.$message.warning('为保护作者版权，当前教材暂不支持复制')
        return
      }
      const _this = this
      this.$copyText(window.getSelection().toString()).then(function (e) {
        _this.$message.success('复制成功')
        _this.$refs.noteMenu.hide()
        window.getSelection().removeAllRanges()
      }, function (e) {
        _this.$message.error('失败')
      })
    },
    showMenu (e) {
      if (this.preMode) return
      const selection = window.getSelection()
      if (e.button === 0 && !selection.isCollapsed) {
        if (!this.$refs.noteMenu.showMenu) {
          let y = e.pageY
          if (window.innerHeight - e.pageY < 195) {
            y = window.innerHeight - 200
          }
          this.position = {
            top: y,
            left: e.pageX
          }
          this.$refs.noteMenu.show()
        }
      } else if (e.button === 0) {
        this.$refs.noteMenu.hide()
      }
    },
    init () {
      const fontFamily = localStorage.getItem('readSetfontFamily')
      const BgColor = localStorage.getItem('readSetBgColor')
      const fontSize = localStorage.getItem('readSetfontSize')
      this.font = JSON.parse(fontFamily) || this.font
      this.backgroundColor = JSON.parse(BgColor) || this.backgroundColor
      this.size = Number(fontSize) || this.size
    },
    changeFontFamily (val) {
      this.font = val
      if (!checkFont(val.defaultFont) && !document.fonts.check(`14px ${val.font}`)) {
        this.$message.info('当前系统无该字体,正在下载请稍后')
      }
      localStorage.setItem('readSetfontFamily', JSON.stringify(val))
    },
    changeBgColor (val) {
      this.backgroundColor = val
      localStorage.setItem('readSetBgColor', JSON.stringify(val))
    },
    back () {
      if (this.preMode) {
        this.$emit('close')
      } else {
        this.$router.go(-1)
      }
    },
    async _getBook () {
      const { data } = await getBook({
        bookId: this.bookId,
        scene: 'BOOK_CATALOGUE_OWN'
      }, {
        authorization: this.token
      })
      this.bookInfo = data
      await this._getBookCatalogue()
    },
    async _getBookCatalogue () {
      const { data } = await getBookCatalogueByVersion({
        bookId: this.bookId,
        type: 'CHAPTER',
        approvedOnly: false
      }, {
        authorization: this.token
      })

      if (data && data.length) {
        data.map((val, index) => {
          if (this.authCodeValid) {
            this.setTryRead(val, true)
          } else if (this.bookInfo.studentCourseId) {
            this.setTryRead(val, true)
          } else if (this.preMode) {
            this.setTryRead(val, true)
          } else {
            if (index === 0 || index === 1) {
              this.setTryRead(val, true)
            } else {
              this.setTryRead(val, false)
            }
          }
        })
      }

      this.catalogueList = data
      if (this.node) {
        this.node.canRead = true
        if (this.findNodeById(this.catalogueList, this.node.id)) {
          this.handleTree(this.node)
        } else {
          this.$message.warning('当前版本无此章节')
        }
        this.node = null
      }
    },
    findNodeById(nodes, idToFind) {
      // eslint-disable-next-line no-unused-vars
      for (const node of nodes) {
        if (node.id === idToFind) {
          return true // 找到匹配的 id，返回 true
        }
        if (node.childCatalogue) {
          const foundInChildren = this.findNodeById(node.childCatalogue, idToFind) // 递归查找子节点
          if (foundInChildren) {
            return true // 如果子节点中找到匹配的 id，返回 true
          }
        }
      }
      return false// 在当前节点及其子节点中都未找到匹配的 id，返回 false
    },
    setTryRead (data, type) {
      if (data && data.length) {
        data.forEach(element => {
          this.setTryRead(element, type)
        })
      } else if (data.childCatalogue && data.childCatalogue.length) {
        this.setTryRead(data.childCatalogue, type)
      }
      if (!(data && data.length)) {
        data.canRead = type
      }
    },
    addControlsListToVideo(htmlString) {
      // 创建临时DOM元素
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlString

      // 获取所有video标签
      const videoElements = tempDiv.getElementsByTagName('video')

      // 遍历并添加controlsList属性
      Array.from(videoElements).forEach(video => {
        // 设置controlsList为nodownload（可根据需要修改值）
        video.setAttribute('controlsList', 'nodownload')

        // 如果video没有controls属性，添加controls以显示控件
        if (!video.hasAttribute('controls')) {
          video.setAttribute('controls', '')
        }
      })

      // 返回处理后的HTML字符串
      return tempDiv.innerHTML
    },
    async handleTree (item) {
      // 判断滚动方向（示例逻辑，需根据实际场景调整）
      // this.direction = item.id < this.currChapter?.id ? -1 : 1
      const list = this.getFlatList(this.catalogueList)
      let currentIndex, targetIndex

      if (this.currChapter) {
        currentIndex = list.findIndex((listItem) => listItem.id === this.currChapter.id)
        targetIndex = list.findIndex((listItem) => listItem.id === item.id)
        this.direction = targetIndex > currentIndex ? 1 : -1
      }
      this.showPageText = false
      if (this.currChapter && this.currChapter.id === item.id) { return }
      if (item.canRead) {
        if (item.id !== this.currChapter?.id) {
          // 触发内容版本更新
          this.contentVersion++
        }
        this.currChapter = item
        this.saveReadingProgress(this.id, this.bookInfo.id, item)
        const { data } = await getContentEditor({
          catalogueId: item.id
        }, {
          authorization: this.token
        })
        this.heighitId = item.id
        if (data && data.length) {
          if (data[0].data === '<p><br><p>') {
            this.html = ''
            this.tips = '暂无内容'
          } else {
            const cacheS = await getCacheS()
            if (cacheS && cacheS.data && cacheS.data.split(',').findIndex((item) => parseInt(item) === this.id) !== -1) {
              const tmpElement = document.createElement('div')
              tmpElement.innerHTML = data[0].data
              const elementNodeListOf = tmpElement.querySelectorAll('.test_card')
              elementNodeListOf.forEach(element => {
                const htmlString = `<div class="analysis_test" style="position: absolute; right: 22%; bottom: 26%; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb; cursor: pointer; padding: 5px 10px 5px 10px;">精准教学</div>`
                element.querySelector('.do_test').insertAdjacentHTML('beforebegin', htmlString)
              })
              data[0].data = tmpElement.innerHTML
            }
            const newHtml = this.addControlsListToVideo(data[0].data)
            this.html = newHtml
            this.contentCopy = JSON.parse(JSON.stringify(newHtml))
            this.$nextTick(() => {
              this.setVideo()
              window.MathJax.typesetPromise()
              Prism.highlightAll()
              if (!this.bookConfig.attachFileDownload) {
                this.enableDonload()
              }
            })
            this.copyHtml = newHtml
          }
        } else {
          this.html = ''
          this.contentCopy = ''
          this.tips = '暂无内容'
        }

        if (this.taskMode) {
          // 任务选择模式 返回章节名+章节内容
          const obj = {
            catalogueId: item.id,
            catalogueName: item.title,
            html: this.html
          }
          this.$emit('selectInfo', obj)
        }
        if (!getToken()) {
          return
        }
        if (!this.preMode) {
          const { data: catalogueData } = await getUserCatalogue({
            catalogueId: item.id,
            studentCourseId: this.studentCourseId
          }, {
            authorization: this.token
          })
          this.$nextTick(() => {
            const scrollHeight = document.getElementById('readbox').scrollHeight
            const cliengHeight = document.getElementById('readbox').clientHeight
            // 重置值
            this.progress = 0
            // 如果有阅读进度了 则回显记忆
            if (catalogueData.progress) {
              // 只有在阅读进度小于100的时候 才定位到上次阅读的位置
              if (catalogueData.progress < 100 && scrollHeight > cliengHeight) {
                // 只在有内容且非一屏展示完成的时候 才进行滚动 避免空章节内容
                const height = catalogueData.progress / 100 * (scrollHeight - cliengHeight)
                this.$message.info('已定位到上次阅读进度')
                document.getElementById('readbox').scrollTop = height
                this.progress = catalogueData.progress
              }
            } else {
              // 如果一屏显示完全 则判定阅读进度为100%
              if (scrollHeight <= cliengHeight) {
                this.progress = 100
                this.sendProgress()
              }
            }
            // 开启监听
            if (scrollHeight > cliengHeight) {
              // 有超出范围的滚动条时才监听进度
              if (+this.studentCourseId) {
                document.getElementById('readbox').addEventListener('scroll', this.getProgress)
              }
            } else {
              document.getElementById('readbox').removeEventListener('scroll', this.getProgress)
            }
          })
          if (!this.preMode) {
            this.getNoteData()
            this.getHighLine()
            this.setSize()
          }
        }
      } else {
        console.log('权限检查 - isAuthCodeMode:', this.isAuthCodeMode, 'authCode:', this.authCode, 'authCodeValid:', this.authCodeValid)
        if (this.isAuthCodeMode) {
          this.$message.warning('请提供有效的授权码以访问此章节!')
        } else {
          this.$message.warning('您暂无权限阅读此章节!')
        }
        return
      }
    },
    setVideo () {
      const videoList = document.getElementsByTagName('video')
      for (let i = 0; i < videoList.length; i++) {
        videoList[i].setAttribute('controlslist', 'nodownload noplaybackrate')
        videoList[i].setAttribute('disablePictureInPicture', true)
      }
      const audioList = document.getElementsByTagName('audio')
      for (let i = 0; i < audioList.length; i++) {
        audioList[i].setAttribute('controlslist', 'nodownload noplaybackrate')
        // audioList[i].setAttribute('disablePictureInPicture', true)
      }
    },
    handleTips () {
      this.$refs.shareBook.openDialog()
    },
    async getProgress () {
      const scrollTop = document.getElementById('readbox').scrollTop
      const scrollHeight = document.getElementById('readbox').scrollHeight
      const cliengHeight = document.getElementById('readbox').clientHeight
      if (scrollHeight <= cliengHeight) {
        this.progress = 100
      } else {
        this.progress = Math.floor(+(scrollTop / (scrollHeight - cliengHeight)).toFixed(2) * 100)
      }
      this.sendProgress()
    },
    sendProgress: debounce(async function () {
      if (this.currChapter && this.currChapter.canRead) {
        await updateReadProgress({
          studentCourseId: this.studentCourseId,
          catalogueId: this.currChapter.id,
          progress: this.progress
        })
      }
    }, 500),
    // 动画方法
    beforeEnter(el) {
      const dir = this.direction
      gsap.set(el, {
        y: dir === 1 ? '100%' : '-100%',
        opacity: 0,
        filter: 'blur(0)'
      })
    },
    enter(el, done) {
      gsap.to(el, {
        duration: 0.8,
        y: '0%',
        opacity: 1,
        filter: 'blur(0)',
        ease: 'power3.out',
        onComplete: done
      })
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    },
    leave(el, done) {
      const dir = this.direction
      gsap.to(el, {
        duration: 0.6,
        y: dir === 1 ? '-100%' : '100%',
        opacity: 0,
        ease: 'power3.in',
        onComplete: done
      })
    }
  }
}
</script>
<style lang="scss">
@font-face {
  font-family: fangsong;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918054519.woff2')
    format('woff2');
}
@font-face {
  font-family: kaiti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918253536.woff')
    format('woff');
}
@font-face {
  font-family: heiti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024030517123260.TTF')
    format('opentype');
}
@font-face {
  font-family: yahei;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918324516.woff')
    format('woff');
}
@font-face {
  font-family: pingfang;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918353783.woff')
    format('woff');
}
@font-face {
  font-family: siyuanheiti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918540864.otf')
    format('opentype');
}
@font-face {
  font-family: siyuansongti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918583626.otf')
    format('opentype');
}
@font-face {
  font-family: songti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022919221599.woff')
    format('woff');
}

.my-remove-tip {
  box-sizing: border-box;
  position: absolute;
  border: 1px solid #fff;
  border-radius: 3px;
  height: 2rem;
  width: 6rem;
  color: #fff;
  display: none;
  background: #444;
  text-align: center;
  font-size: 0.8rem;
  cursor: pointer;
  line-height: 2rem;
  overflow: visible;
}
.drop_down_item {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  font-size: 0.8rem !important;
  line-height: 1rem !important;
  white-space: nowrap !important;
  padding: 0 1.3rem !important;
  text-align: center;
  overflow: hidden;
  .color {
    width: 1rem;
    height: 1rem;
    margin-top: 0.8rem;
    margin-right: 0.5rem;
  }
}

.pop_note {
  padding: 0 !important;
  border-radius: 0.5rem;
  background: #fff;
  box-shadow: none;
}
.pop_card {
  padding: 0 !important;
  font-size: 1rem;
  border-radius: 0.5rem;
  position: relative;
  .size {
    position: absolute;
    font-size: 0.8rem;
    left: 7.2rem;
  }
  .el-slider {
    width: 75%;
    height: 100%;
  }
  p {
    margin: 0;
    height: 1rem;
  }
  .block {
    display: flex;
    height: 3rem;
    margin: 0;
    line-height: 3.6rem;
    justify-content: space-between;
    vertical-align: middle;
    flex-wrap: nowrap;
  }
  .el-dropdown-link {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    vertical-align: middle;
    cursor: pointer;
    .color {
      width: 1rem;
      height: 1rem;
      margin-top: 1.28rem;
      margin-right: 0.5rem;
    }
  }
  .note_content {
    max-height: 30rem;
    width: 22rem;
    overflow: auto;
    @include scrollBar;
    .el-empty__image {
      width: 5rem;
    }
    .el-empty__description {
      p {
        font-size: 1rem;
      }
    }
  }
  .note_info {
    width: 32rem;
    height: 30rem;
    background: #f9f9f9;
    padding: 2rem;
    overflow: auto;
    @include scrollBar;
    .note {
      margin-top: 2rem;
    }
    .flex {
      margin-top: 2rem;
      float: right;
    }
    .button {
      font-size: 1rem;
      padding: 0.5rem 0.8rem;
    }
    p {
      height: auto;
    }
    .el-textarea {
      font-size: 1rem;
      margin-top: 2rem;
    }
    span {
      font-weight: 700;
    }
  }
  .note_item {
    display: flex;
    width: 19.5rem;
    height: 6.5rem;
    cursor: pointer;
    margin-bottom: 0.5rem;
    margin-left: 1rem;
    padding: 10px 7px;
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
    border-radius: 0.4rem;
    border: 1px solid #e0e0e0;
    p {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 17rem;
      font-size: 0.9rem;
      height: 2rem;
      span {
        font-weight: 700;
      }
    }
  }
  .selected {
    background: #f9f9f9;
  }
  .el-slider__runway {
    height: 0.5rem;
    margin-top: 1.6rem;
  }
  .el-slider__marks {
    display: none;
  }
  .el-slider__bar {
    height: 0;
  }
  .el-slider__button {
    width: 1rem;
    height: 1rem;
  }
  .el-slider__button-wrapper {
    width: 1rem;
    height: 1rem;
    top: -0.4rem;
  }
  .el-slider__stop {
    width: 0.5rem;
    height: 0.5rem;
  }
}

.el-card__header {
  padding: 8px !important;
}
.el-card__body {
  padding: 1rem !important;
}
</style>
<style lang="scss" scoped>
.bg{
  background: #F1F7FF !important;
}
.editor-dig {
  width: 100%;
  height: 100%;
  padding: 10px;
  position: relative;
  .knowledge-bg {
    width: 100%;
    height: 100%;
    background: #F1F7FF;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    background: url('../../../assets/digitalbooks/read/bg.png') no-repeat center/cover;
  }
  .word_button {
    width: 96px;
    // height: 26px;
    cursor: pointer;
    position: absolute;
    right:88px;
    top:-50px;
  }
  .task_button{
    width: 96px;
    // height: 26px;
    cursor: pointer;
    position: absolute;
    right:198px;
    top:-50px;
  }
  #findwindow {
    background: #fff;
    margin: 1rem;
  }
  .head-box {
    height: 40px;
    min-width: 1040px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;

    .share {
      position: absolute;
      right: 5px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
    .print{
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .type{
        position: absolute;
        width: 74px;
        height: 89px;
        background: #fff;
        border-radius: 9px;
        top:45px;
        opacity: 0;
        transition: all .3s linear;
        z-index: 1;
        div{
          font-size: 12px;
          width: 100%;
          height: 28px;
          text-align: center;
          line-height: 28px;
          cursor: pointer;
        }
        div:hover{
          background: #E8F2FF;
        }
      }
      img {
        width: 20px;
        height: 20px;
      }
    }
    .print:hover{
      .type{
        opacity: 1;
      }
    }
    .back {
      width: 30px;
      height: 30px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: 16px;
      font-weight: 500;
      margin-left: 10px;
    }
  }

  .content-box {
    min-width: 1040px;
    height: calc(100% - 40px);
    margin: 20px 0;
    display: flex;
    justify-content: center;
    .edit_box {
      width: 74px;
      height: 202px;
      border-radius: 10px;
      padding: 20px;
      border: 1px solid #f2f2f2;
      background: #fff;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
      margin-left: 10px;
      position: relative;

      .box-card {
        padding: 0px;
      }
      .trigger_content {
        width: 44px;
        height: 60px;
        cursor: pointer;
        line-height: 0px;
        img {
          width: 24px;
          height: auto;
          margin-left: 5px;
        }
        p {
          width: 34px;
          text-align: center;
        }
      }
    }
    .card-left {
      position: relative;
      display: flex;
      min-width: 220px;
      max-width: 25vw;
      height: 100%;
      padding: 10px;
      flex-direction: column;
      flex-shrink: 1;
      background-color: #ffffff;
      border-radius: 10px;
      box-sizing: border-box;
      overflow: auto;
      @include scrollBar;

      .mulu {
        color: #000;
        font-size: 16px;
        font-weight: 800;
      }
      .show-btn {
        cursor: pointer;
        i {
          font-size: 20px;
        }
      }
    }

    .contorl {
      width: 50px;
      background: #fff;
      border-radius: 10px;
      padding-top: 10px;
      font-size: 12px;

      .el-icon-s-unfold {
        font-size: 20px;
        margin-bottom: 2px;
      }
    }

    .card-right {
      //width: 793.667px;
      width: 825.990px;
    }

    .card-right-all {
      // width: calc(100% - 10px);
      //width: 793.667px;
      width: 825.990px;
    }

    .card-right,
    .card-right-all {
      display: flex;
      height: 100%;
      flex-direction: column;
      flex-shrink: 0;
      background-color: #fff;
      border-radius: 10px;
      margin-left: 10px;
      position: relative;
      .to_top{
        position: absolute;
        right: -70px;
        bottom: 30px;
        cursor: pointer;
        .tips{
          font-size: 15px;
          width: 80px;
          height: 30px;
          text-align: center;
          line-height: 30px;
          font-weight: 700;
          border-radius: 3px;
          color: #ffffff;
          background-color: #1296db;
          position: absolute;
          right: -90px;
          top:6px;
          display: none;

        }
        .arrow{
          width: 40px;
        }
        .arrow:hover ~ .tips{
          display: block !important;
        }
      }

      .turn-page {
        position: absolute;
        left: 0;
        right: 0;
        text-align: center;
        color: #9b9da9;
        font-size: 14px;
        z-index: 2;
        pointer-events: none;
        opacity: 1;
      }

      .up-page-text {
        top: 0;
        animation: turnPageUp 0.15s ease-out;
      }

      .next-page-text {
        bottom: 0;
        animation: turnPageNext 0.15s ease-out; /* 应用动画 */
      }
    }

    .empty {
      width: 100px;
      height: 100px;
      margin-bottom: 20px;
    }
  }

  .editor-content-view {
    position: absolute;
    width: 100%;
    height: 100%;
    line-height: 1.4;
    will-change: transform, opacity;
    padding: 40px;
      overflow-y: auto;
      @include scrollBar;
    ::v-deep img {
      max-width: 100%;
    }
    ::v-deep video {
      width: 100%;
    }
    ::v-deep code {
    white-space: pre-wrap;
    word-break: break-all;
    }
    ::v-deep p {
    //::v-deep li {
      white-space: pre-wrap;
      word-wrap: break-word; /* 保留空格 */
    }
    ::v-deep blockquote {
      border-left: 8px solid #d0e5f2;
      padding: 10px 10px;
      margin: 10px 0;
      background-color: #f1f1f1;
    }
    ::v-deep table {
      border-collapse: collapse;
    }
    ::v-deep pre > code {
      display: block;
      padding: 10px;
    }
    ::v-deep input[type='checkbox'] {
      margin-right: 5px;
    }
    ::v-deep ul {
      display: block;
      list-style-type: disc;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0px;
      margin-inline-end: 0px;
      padding-inline-start: 40px;
    }
    ::v-deep ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0px;
      margin-inline-end: 0px;
      padding-inline-start: 40px;
    }
  }
}

@keyframes turnPageNext {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes turnPageUp {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#readbox {
  position: relative;
  overflow: hidden;
}

//::v-deep video::-webkit-media-controls {
//  overflow: hidden !important;
//}
//
//::v-deep video::-webkit-media-controls-enclosure {
//  width: calc(100% + 40px);
//  margin-left: auto;
//}

</style>
