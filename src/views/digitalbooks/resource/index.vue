<template>
  <div class="main">
    <div class="head-box">
      <img
        class="back"
        src="@/assets/digitalbooks/arrow-left.svg"
        @click="back"
      />
      <div class="title article-singer-container">资源库</div>
    </div>

    <div v-if="!$route.query.type" class="status_change">
      <div class="status_content">
        <div
          class="status_title"
          :class="status == 0 ? 'selected' : 'deselect'"
          @click="
            status = 0
            resType = 'DIGITAL_RESOURCE'
          "
        >
          学校资源
          <div class="down_line"></div>
        </div>
        <div
          class="status_title"
          :class="status == 1 ? 'selected' : 'deselect'"
          @click="
            status = 1
            resType = 'DIGITAL_RESOURCE_OFFICIAL'
          "
        >
          教材资源
          <div class="down_line"></div>
        </div>
      </div>
    </div>
    <div class="content_main">
      <div class="left_card">
        <div class="dig-left">
          <div class="chapter-title">
            <div class="icon1">
              <img src="../../../assets/digitalbooks/chapter.svg" />
              目录
            </div>
            <div class="flex items-center">
              <div
                v-if="!status && access"
                class="add-btn"
                @click="treeAppend(null, null)"
              >
                +创建
              </div>
              <!-- <i class="el-icon-s-fold pointer"></i> -->
              <!-- <i class="el-icon-s-unfold pointer"></i> -->
            </div>
          </div>
          <div class="chapter-body">
            <el-tree
              :data="bookTree"
              :props="treeProps"
              node-key="id"
              default-expand-all
              highlight-current
              :expand-on-click-node="false"
              draggable
              @node-drag-start="handleDragStart"
              @node-drop="handleDrop"
              @node-click="handleNodeClick"
            >
              <div slot-scope="{ node, data }" class="tree-body">
                <div class="chapter-name">
                  <div :title="data.title" class="w article-singer-container">
                    {{ data.title }}
                  </div>
                </div>
                <div v-if="!status && access" class="chapter-option row">
                  <el-button
                    v-if="showAdd(data)"
                    type="text"
                    size="mini"
                    @click.stop="() => treeAppend(node, data)"
                  >
                    <i class="el-icon-plus"></i>
                  </el-button>
                  <el-button
                    type="text"
                    size="mini"
                    @click.stop="() => treeEdit(node, data)"
                  >
                    <i class="el-icon-edit"></i>
                  </el-button>
                  <el-button
                    type="text"
                    size="mini"
                    @click.stop="() => treeRemove(node, data)"
                  >
                    <i class="el-icon-delete"></i>
                  </el-button>
                </div>
              </div>
            </el-tree>
          </div>
        </div>
      </div>
      <div class="right_card">
        <div class="right_head">
          <el-breadcrumb class="bread" separator="/">
            <el-breadcrumb-item v-for="item in breadList" :key="item">{{
              item
            }}</el-breadcrumb-item>
          </el-breadcrumb>
          <el-input
            v-model="searchValue"
            class="input"
            placeholder="根据关键字查询"
          >
            <i slot="prefix" class="el-icon-search icon"></i>
          </el-input>
        </div>
        <div v-if="!status && access && unitId !== null" class="table_title">
          <div class="cheack_box">
            <el-checkbox
              v-model="checkAll"
              :indeterminate="
                multipleSelection.length !== 0 &&
                  multipleSelection.length !== tableData.length
              "
              @change="handleCheckAllChange"
            >全选</el-checkbox>
          </div>
          <div class="flex">
            <el-button
              v-show="multipleSelection.length >= 2"
              size="mini"
              type="danger"
              style="margin-right: 10px"
              @click="deleteGroup"
            >批量删除</el-button>
            <div class="flex justify-end">
              <el-upload
                action="''"
                :before-upload="beforeUpload"
                :show-file-list="false"
                :multiple="true"
              >
                <!-- <div v-if="!progress" class="classpro-btn">上传资源</div> -->
                <el-button
                  v-if="!progress"
                  size="mini"
                  type="primary"
                >上传资源</el-button>
              </el-upload>
              <div v-if="progress" class="progress">上传中：{{ percent }}%</div>
              <p class="tips">文件大小不超过2GB,office资源超过200MB无法在线预览</p>
            </div>
          </div>
        </div>
        <el-table
          ref="multipleTable"
          class="table"
          :row-class-name="rowClass"
          :data="tableData"
          height="50"
          :show-header="false"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            v-if="!status && access"
            type="selection"
            width="75"
          />
          <el-table-column label="文件名" show-overflow-tooltip>
            <template slot-scope="scope">
              <img class="img" :src="findTitleImg(scope.row)" alt="" />
              <span style="margin-left: 50px">{{
                scope.row.mediaFile.fileName +
                  '.' +
                  scope.row.mediaFile.expendType
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="大小">
            <template slot-scope="scope">
              <span>{{ formatBytes(scope.row.mediaFile.size) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="上传时间">
            <template slot-scope="scope">
              <p>
                上传时间： <span>{{ scope.row.createdAt }}</span>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="preview(scope.row)"
              >预览</el-button>
              <el-button
                type="text"
                size="small"
                @click="download(scope.row)"
              >下载</el-button>
              <el-button
                v-if="!status && access"
                type="text"
                size="small"
                @click="delFile(scope.row)"
              >删除</el-button>
              <el-dropdown size="mini" style="margin-left: 15px">
                <span class="el-dropdown-link">
                  <el-button
                    type="text"
                    size="small"
                  >更多<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item><p class="drop_text" @click="openMessageModal(scope.row)">
                    举报留言
                  </p></el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <editChapter
      v-if="editChapterShow"
      :show="editChapterShow"
      :append-to-body="true"
      :node-info="currChapter"
      :res-type="resType"
      @close="editChapterShow = false"
      @emitSucess="eidtDone"
    />
    <editMessage
      v-if="editMessageShow"
      :show="editMessageShow"
      :append-to-body="true"
      :node-info="messageType"
      :res-type="resType"
      @close="editMessageShow = false"
      @emitSucess="tiptDone"
    />
    <div v-if="previewUserBox" class="preview-box">
      <div class="preview-content">
        <div class="preview-content-head">
          <div class="title">文件列表</div>
          <div @click.stop="previewUserBox = false">
            <i class="el-icon-close pointer"></i>
          </div>
        </div>
        <div class="preview-content-content">
          <resourceDetail
            :id="preCurrentId"
            :files-list="tableData"
            :has-del="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import axios from 'axios'
import { getFileUploadAuthor } from '@/api/user-api'
import { deleteBookCatalogue, getBookCatalogue, dragCatalogue, getResourceList, deleteResource, addResource } from '@/api/digital-api.js'
import editChapter from './components/editChapter.vue'
import editMessage from './components/editMessage.vue'
import { throttle } from '@/utils/index'
import resourceDetail from './components/datas.vue'
import { saveAs } from 'file-saver'
import { Notification } from 'element-ui'
export default {
  components: {
    editChapter,
    editMessage,
    resourceDetail
  },
  data () {
    return {
      imgList: {
        word: require('@/assets/digitalbooks/resource/word.svg'),
        excel: require('@/assets/digitalbooks/resource/excel.svg'),
        ppt: require('@/assets/digitalbooks/resource/ppt.svg'),
        pdf: require('@/assets/digitalbooks/resource/pdf.svg'),
        unknown: require('@/assets/digitalbooks/resource/unknown.svg'),
        zip: require('@/assets/digitalbooks/resource/zip.svg')
      },
      resType: 'DIGITAL_RESOURCE',
      status: 0,
      bookId: '',
      preCurrentId: null,
      previewUserBox: false,
      unitId: null,
      progress: false,
      percent: 0,
      messageType: null,
      access: true,
      preInfo: {
        type: 'VIDEO'
      },
      breadList: [],
      searchValue: '',
      checkAll: false,
      multipleSelection: [],
      currChapter: null,
      token: '',
      editChapterShow: false,
      editMessageShow: false,
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      downloadInfo: [],
      bookTree: [],
      tableData: [],
      tableDataCopy: []
    }
  },
  computed: {
    findPercent: function () {
      return (val) => {
        const data = this.downloadInfo
        return Math.floor(data.filter(item => item.name === val)[0].loaded / data.filter(item => item.name === val)[0].total * 100)
      }
    }
  },
  watch: {
    'resType' () {
      this._getBookCatalogue()
      this.tableDataCopy = []
      this.tableData = []
      this.unitId = null
      this.breadList = []
    },
    'searchValue' (val) {
      this.tableData = this.tableDataCopy.filter(item => {
        return item.mediaFile.expendType.includes(val) || item.mediaFile.fileName.includes(val)
      })
    },
    'downloadInfo' (val) {
      console.log(val)
    }
  },
  mounted () {
    this.bookId = this.$route.query && this.$route.query.id
    if (this.$route.query.type) {
      this.resType = this.$route.query.type
    }
    this.token = this.$route.query.token ? 'Bearer ' + this.$route.query.token : getToken()
    this._getBookCatalogue()
  },
  methods: {
    checkUpdate () {
      let data = JSON.parse(localStorage.getItem('resource'))
      if (!data) {
        data = [{
          id: this.unitId,
          time: new Date()
        }]
        localStorage.setItem('resource', JSON.stringify(data))
        return
      }
      const flag = data.filter((item) => { return item.id === this.unitId })
      if (flag.length === 0) {
        data.push({
          id: this.unitId,
          time: new Date()
        })
        localStorage.setItem('resource', JSON.stringify(data))
        return
      }
      let tag = 0
      this.tableData.forEach(item => {
        if (new Date(flag[0].time).getTime() < new Date(item.updatedAt).getTime()) {
          tag += 1
        }
      })
      if (tag !== 0) {
        this.$message.info(`有${tag}条资源更新`)
      }
      data = data.map(item => {
        if (item.id === this.unitId) {
          return {
            id: item.id,
            time: new Date()
          }
        }
        return item
      })
      localStorage.setItem('resource', JSON.stringify(data))
    },
    findTitleImg (row) {
      if (row.mediaFile.type === 'IMAGE' && row.mediaFile.expendType !== 'svg') {
        return row.mediaFile.url.split('?x-oss-process')[0] + '?x-oss-process=image/format,jpg/resize,w_30'
      }
      if (row.mediaFile.expendType === 'svg') {
        return row.mediaFile.url
      }
      if (row.mediaFile.type === 'VIDEO') {
        return `${row.mediaFile.url}?x-oss-process=video/snapshot,t_1000,m_fast`
      }
      const type = this.getFileType(row.mediaFile.expendType)
      if (type === 'WORD') {
        return this.imgList.word
      }
      if (type === 'EXCEL') {
        return this.imgList.excel
      }
      if (type === 'PDF') {
        return this.imgList.pdf
      }
      if (type === 'PPT') {
        return this.imgList.ppt
      }
      if (type === 'ZIP') {
        return this.imgList.zip
      }
      return this.imgList.unknown
    },
    preview (row) {
      this.preCurrentId = row.id
      this.previewUserBox = true
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
      this.checkAll = this.multipleSelection.length === this.tableData.length
    },
    deleteGroup () {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        Promise.all(this.multipleSelection.map(async (item) => {
          await deleteResource({ coursecommUnitResourceId: item.id })
        })).then(res => {
          this.$message.success('删除成功')
          this.getSourceList()
        }
        )
      }).catch(() => {

      })
    },
    handleCheckAllChange (val) {
      val
        ? this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection()
          this.tableData.forEach(item => {
            this.$refs.multipleTable.toggleRowSelection(item, true)
          })
        })
        : this.$refs.multipleTable.clearSelection()
    },
    back () {
      if (this.$route.query.token && this.$route.query.token.indexOf('Bearer') === -1) {
        window.close()
        return
      }
      this.$router.go(-1)
    },
    handleDragStart (node, ev) {
      console.log('drag start', node)
    },
    async _getBookCatalogue () {
      const { data } = await getBookCatalogue({
        bookId: this.bookId,
        type: this.resType
      }, {
        authorization: this.token
      })
      if (data.length === 1 && data[0].id === null) {
        this.bookTree = []
      } else {
        this.bookTree = data
      }
      this.access = data[0].role === 'write'
    },
    async handleDrop (draggingNode, dropNode, dropType, ev) {
      // console.log('tree drop: ', draggingNode.data, dropNode.data, dropType)
      await dragCatalogue({
        catalogueId: draggingNode.data.id,
        referCatalogueId: dropNode.data.id,
        position: dropType.toUpperCase()
      }, {
        authorization: this.token
      })
      this._getBookCatalogue()
    },
    handleNodeClick (nodeData) {
      this.tableData = []
      this.tableDataCopy = []
      this.$refs.multipleTable.clearSelection()
      this.checkAll = false
      this.unitId = nodeData.id
      this.breadList = this.getBread(this.bookTree, nodeData).reverse()
      this.getSourceList()
    },
    getFileType (str) {
      var result = ''
      var imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'svg']
      // 进行图片匹配
      result = imglist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'IMAGE'
        return result
      }
      // 匹配txt
      var txtlist = ['txt']
      result = txtlist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'TXT'
        return result
      }
      // 匹配 excel
      var excelist = ['xls', 'xlsx']
      result = excelist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'EXCEL'
        return result
      }
      // 匹配 word
      var wordlist = ['doc', 'docx']
      result = wordlist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'WORD'
        return result
      }
      // 匹配 pdf
      var pdflist = ['pdf']
      result = pdflist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'PDF'
        return result
      }
      // 匹配 ppt
      var pptlist = ['ppt', 'pptx']
      result = pptlist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'PPT'
        return result
      }
      // 匹配 视频
      var videolist = ['mp4', 'm2v', 'mkv']
      result = videolist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'VIDEO'
        return result
      }
      // 匹配 音频
      var radiolist = ['mp3', 'wav', 'wmv']
      result = radiolist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'RADIO'
        return result
      }
      // 匹配压缩包
      var zipList = ['zip', 'rar']
      result = zipList.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'ZIP'
        return result
      }
      // 其他 文件类型
      result = 'OTHER'
      return result
    },
    async getSourceList () {
      const { data } = await getResourceList({
        sourceId: this.unitId,
        resourceType: this.resType
      }, {
        authorization: this.token
      })
      this.tableData = data || []
      // }
      this.tableDataCopy = this.tableData
      this.checkUpdate()
    },
    getImgSrc (rich) {
      var imgList = []
      rich.replace(/ [^>]*src=['"]([^'"]+)[^>]*>/g, (match, capture) => {
        imgList.push(capture)
      })
      return imgList
    },

    getBread (treeData, nodeData) {
      const data = treeData
      const val = nodeData.id
      for (let i = 0; i < data.length; i++) {
        if (data[i] && data[i].id === val) {
          return [data[i].title]
        }
        if (data[i] && data[i].childCatalogue
        ) {
          const d = this.getBread(data[i].childCatalogue
            , nodeData)
          if (d) {
            return d.concat(data[i].title)
          }
        }
      }
    },
    showAdd (data) {
      const arr = []
      this.bookTree.forEach(item => {
        arr.push(item.id)
      })
      if (data.parentId !== 0 && arr.indexOf(data.parentId) === -1) {
        return false
      } else {
        return true
      }
    },
    treeAppend (node, data) {
      this.treeData = data

      if (node) {
        this.currChapter = { bookId: this.bookId, parentId: data.id, data: null }
      } else {
        this.currChapter = { bookId: this.bookId, parentId: 0, data: null }
      }
      this.editChapterShow = true
    },
    openMessageModal (row) {
      this.messageType = {
        id: row.id
      }
      this.editMessageShow = true
    },
    treeEdit (node, data) {
      this.treeData = data
      this.currChapter = { bookId: this.bookId, parentId: data.parentId, data }
      this.editChapterShow = true
    },
    async treeRemove (node, data) {
      try {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await deleteBookCatalogue({
            catalogueId: data.id
          }, {
            authorization: this.token
          })
          this._getBookCatalogue()
          // const parent = node.parent
          // const children = parent.data.childCatalogue || parent.data
          // const index = children.findIndex(d => d.id === data.id)
          // children.splice(index, 1)
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    eidtDone (data) {
      this.editChapterShow = false
      this._getBookCatalogue()
      // if (this.currChapter.data) {
      //   // 编辑
      //   this.treeData.title = data.title
      //   // this._getContent(this.treeData.id)
      // } else {
      //   // 新增
      //   if (!this.currChapter.parentId) {
      //     // 根目录
      //     this.bookTree.push(data)
      //   } else {
      //     const newChild = data
      //     this.treeData.childCatalogue.push(newChild)
      //   }
      // }
    },
    tiptDone () {
      this.editMessageShow = false
    },
    rowClass (row, rowIndex) {
      return 'row-class'
    },
    downloadFile (path, name, type) {
      const notif = Notification({
        title: name + '.' + type,
        dangerouslyUseHTMLString: true,
        message: '',
        duration: 0
      })
      throttle(function () {
        const xhr = new XMLHttpRequest()
        xhr.open('get', path)
        xhr.responseType = 'blob'
        xhr.addEventListener('progress', (e) => {
          const complete = Math.floor(e.loaded / e.total * 100)
          notif.message = complete + '%'
          if (complete >= 100) {
            notif.close()
          }
        })
        xhr.send()

        xhr.onload = function () {
          if (this.status === 200 || this.status === 304) {
            const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
            saveAs(blob, name + '.' + type)
          }
        }
      }, 2000)
    },
    async beforeUpload (file) {
      if (file.size >= 2147483648) {
        this.$message.warning('上传文件不能超过2G')
        return
      }
      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }

      const { data } = await this.getOssSign(mediaType, file.name)
      this.ossUrl = data[0].ossConfig.host
      this.progress = true
      const filename = file.name

      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)

      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })

      await addResource({
        sourceId: this.unitId,
        resourceType: this.resType
      }, [{
        size: Math.floor(file.size / 1024),
        type: mediaType,
        fileName: filename.substring(0, filename.lastIndexOf('.')),
        url: data[0].fileName,
        expendType: filename.substring(filename.lastIndexOf('.') + 1)
      }], { authorization: this.token }
      )
      this.getSourceList()
      return Promise.reject()
    },
    async delFile (item) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deleteResource({ coursecommUnitResourceId: item.id }, { authorization: this.token })
        this.$message.success('删除成功')
        this.getSourceList()
      }).catch(() => {

      })
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    formatBytes (bytes, decimals = 2) {
      if (bytes === 0) return '0 Bytes'

      const k = 1024
      const dm = decimals < 0 ? 0 : decimals
      const sizes = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
    },
    download (row) {
      this.downloadFile(row.mediaFile.url, row.mediaFile.fileName, row.mediaFile.expendType)
    }
  }
}
</script>
<style lang="scss">
.el-notification__title {
  font-size: var(--font-size-L) !important;
}
</style>
<style lang="scss" scoped>
.progress {
  width: 300px;
}
.font_size {
  font-size: var(--font-size-S) !important;
}
.classpro-btn {
  padding: 5px 15px;
  font-size: var(--font-size-L) !important;
}
.progress {
  font-size: var(--font-size-L);
}
.row-class {
  font-weight: 800;
  font-size: var(--font-size-L);
  color: #0e0e0e;
  width: 100px;
  letter-spacing: 0.22px;
}
::v-deep .el-dropdown-menu__item {
  font-size: var(--font-size-M) !important;
}
::v-deep .el-button--mini {
  font-size: var(--font-size-M);
  padding: 5px 10px;
}
.main {
  width: 100%;
  height: 100%;
  padding: 10px;
  overflow: auto;
  position: relative;
  .preview-box {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    background: rgba(51, 51, 51, 0.5);
  }
  .preview-content {
    width: 90%;
    height: 90%;
    position: absolute;
    left: 5%;
    top: 5%;
    background-color: #fff;
    border-radius: 5px;

    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
    @include scrollBar;

    .preview-content-head {
      height: 40px;
      display: flex;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: 500;
      }

      .el-icon-close {
        font-size: var(--font-size-XXL);
      }
    }

    .preview-content-content {
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }

  .head-box {
    height: 40px;
    // min-width: 1040px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
    z-index: 8;
    .back {
      width: 20px;
      height: 20px;
      margin-left: 10px;
      object-fit: cover;
      cursor: pointer;
    }
    .title {
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
      margin-left: 10px;
    }
  }
  .status_change {
    width: 100%;
    height: 30px;
    position: absolute;
    left: 0px;
    top: 23px;
    .status_content {
      width: 200px;
      height: 30px;
      margin: 0 auto;
      display: flex;
      position: relative;
      z-index: 9;
      justify-content: space-between;
      .status_title {
        font-size: var(--font-size-L);
        cursor: pointer;
      }
      .selected {
        font-weight: 500;
        position: relative;
        .down_line {
          width: 50px;
          height: 3px;
          background: #2f80ed;
          position: absolute;
          top: 25px;
          left: 0px;
        }
      }
      .deselect {
        font-weight: 400;
      }
    }
  }
  .content_main {
    display: flex;
    margin: 0 auto;
    max-width: 1280px;
    // margin-top: 30px;
    height: calc(100% - 40px - 10px);
    padding: 8px 8px 8px 8px;
    justify-content: space-between;
    border-radius: 6px;
    // background: rgba(255, 255, 255, 0.5);

    .left_card {
      width: 241px;
      height: 100%;
      flex-shrink: 0;
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.5);
      .dig-left {
        width: 241px;
        height: 100%;
        border-radius: 6px;
        background: #fff;
        .chapter-title {
          width: 100%;
          height: 40px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 5px;
          .icon1 {
            color: #000;
            font-size: var(--font-size-L);
            display: flex;
            align-items: center;
            img {
              width: 27px;
              height: 27px;
              margin-right: 5px;
            }
          }
          .add-btn {
            color: #2f80ed;
            font-size: var(--font-size-L);
            margin-right: 10px;
            cursor: pointer;
          }
        }

        .chapter-body {
          width: 100%;
          height: calc(100% - 40px);
          overflow: auto;
          @include scrollBar;
          ::v-deep .el-tree-node__content {
            height: 40px;
          }

          ::v-deep .el-button + .el-button {
            margin-left: 5px;
          }

          ::v-deep .el-button {
            font-size: var(--font-size-L);
          }

          ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
            padding: 5px;
          }

          .tree-body {
            width: calc(100% - 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: var(--font-size-L);
            padding-right: 8px;

            .chapter-name {
              flex: 1;
              overflow: hidden;
              @include scrollBar;
            }

            .chapter-option {
              display: flex;
              // align-items: center;
              justify-content: space-between;
              // flex-shrink: 0;
              ::v-deep .el-button--mini {
                padding: 0px;
              }
            }
          }
        }
      }
    }
    .right_card {
      display: flex;
      width: calc(100% - 245px);
      height: 100%;
      padding: 13px 20px 42px 21px;
      flex-direction: column;
      align-items: flex-start;
      flex-shrink: 0;
      gap: 5px;
      border-radius: 6px;
      background: #fff;
      .right_head {
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: space-between;
        .bread {
          font-size: var(--font-size-L);
        }
        .input {
          width: 200px;
          height: 18px;
          padding: 0;
          font-size: var(--font-size-S);
          ::v-deep .el-input__inner {
            height: 18px;
            padding-left: 10px;
          }
          .icon {
            position: absolute;
            top: 5px;
            right: -190px;
          }
        }
      }
      .table_title {
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        position: relative;
        .tips{
          font-size: var(--font-size-S);
          position: absolute;
          right:0;
          top:-20px
        }
        .cheack_box {
          font-size: var(--font-size-S);
          margin-left: 14px;
        }
        ::v-deep .el-checkbox__inner::after {
          width: 4px;
          height: 8px;
          top: 0px;
          border: 2px solid #ffffff;
          border-left: 0;
          border-top: 0;
        }
      }
      .table {
        margin-top: 5px;
        .img {
          display: inline-block;
          vertical-align: middle;
          // margin-bottom: 3px;
          width: 30px;
        }
        ::v-deep .el-table__body-wrapper .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        // ::v-deep.el-table__body-wrapper .cell:hover {
        //   overflow: visible;
        //   white-space: normal;
        // }
        font-size: var(--font-size-M);
        .drop_text {
          font-size: var(--font-size-S);
        }
        ::v-deep .el-button--text {
          font-size: var(--font-size-M);
        }
        ::v-deep .el-checkbox__inner::after {
          width: 4px;
          height: 8px;
          top: 0px;
          border: 2px solid #ffffff;
          border-left: 0;
          border-top: 0;
        }
        ::v-deep .el-table_1_column_1 {
          width: 100px;
        }
        ::v-deep
          .el-table__header
          .el-table-column--selection
          .cell
          .el-checkbox:after {
          content: '全选';
          font-size: var(--font-size-L);
          margin-left: 10px;
        }
        ::v-deep
          .el-table__header
          .el-table-column--selection
          .cell
          .el-checkbox {
          margin-left: 3px;
        }
        ::v-deep .el-table__body-wrapper {
          @include scrollBar;
        }
      }
    }
  }
}
</style>
