<template>
  <NormalDialog
    v-if="dialogShow"
    width="400px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="appendToBody"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="mb10">
        <el-input
          v-model="text"
          type="textarea"
          class="w"
          placeholder="对资源有疑问留言"
        />
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="bind">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { debounce } from '@/utils/index'
import { feedBack } from '@/api/digital-api.js'
import { getToken } from '@/utils/auth'

export default {
  components: { NormalDialog },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: false
    },
    nodeInfo: {
      type: Object,
      default: () => {
        return null
      }
    },
    resType: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      text: '',
      dialogShow: false,
      title: '举报留言',
      token: ''
    }
  },
  mounted () {
    this.dialogShow = this.show
    this.token = getToken()
    // this.token = `Bearer ${this.$route.query && this.$route.query.token}`
  },
  methods: {
    close () {
      this.dialogShow = false
      this.$emit('close')
    },
    bind: debounce(async function () {
      if (this.text === '') {
        this.$message.warning('请输入内容')
        return
      }
      const { code } = await feedBack({
        content: this.text,
        feedbackType: 'tip',
        feedbackSence: this.resType,
        targetId: this.nodeInfo.id
      })
      if (code === 200) {
        this.$message.success('提交成功')
        this.$emit('emitSucess')
      } else {
        this.$message.console.error('提交失败')
      }
    }, 3000, true)
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  @include scrollBar;
}
.editor-dig {
  ::v-deep .el-input__inner {
    transition: none;
  }
  .school-disc {
    margin-top: 10px;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .school-disc2 {
    width: 5px;
    height: 5px;
    background: #828282;
    border-radius: 50%;
    margin-right: 5px;
  }
}
</style>
