<template>
  <NormalDialog
    v-if="dialogShow"
    width="400px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="appendToBody"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="mb10">
        <el-input v-model="chapterName" class="w" placeholder="请输入内容" />
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="bind">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { debounce } from '@/utils/index'
import { editBookCatalogue } from '@/api/digital-api.js'
import { getToken } from '@/utils/auth'

export default {
  components: { NormalDialog },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: false
    },
    nodeInfo: {
      type: Object,
      default: () => {
        return null
      }
    },
    resType: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      chapterName: '',
      dialogShow: false,
      title: '创建',
      token: ''
    }
  },
  mounted () {
    this.dialogShow = this.show
    this.token = this.$route.query.token ? 'Bearer ' + this.$route.query.token : getToken()
    // this.token = `Bearer ${this.$route.query && this.$route.query.token}`
    if (this.$route.query.type) {
      this.resType = this.$route.query.type
    }
    if (this.nodeInfo.data) {
      this.title = '编辑'
      this.chapterName = this.nodeInfo.data.title
    } else {
      this.title = '创建'
    }
  },
  methods: {
    close () {
      this.dialogShow = false
      this.$emit('close')
    },
    bind: debounce(async function () {
      if (this.chapterName) {
        try {
          const obj = {
            bookId: this.nodeInfo.bookId,
            type: this.resType,
            parentId: this.nodeInfo.parentId,
            title: this.chapterName
          }
          if (this.nodeInfo.data) {
            obj.id = this.nodeInfo.data.id
          }
          const { data } = await editBookCatalogue(obj, { authorization: this.token })
          this.dialogShow = false
          this.$emit('emitSucess', data)
        } catch (error) {
          console.log(error)
        }
      }
    }, 3000, true)
  }
}
</script>

<style lang="scss" scoped>

.editor-dig {
  ::v-deep .el-input__inner {
    transition: none;
  }
  .school-disc {
    margin-top: 10PX;
  }

  .mb10 {
    margin-bottom: 10PX;
  }

  .school-disc2 {
    width: 5PX;
    height: 5PX;
    background: #828282;
    border-radius: 50%;
    margin-right: 5PX;
  }
}
</style>
