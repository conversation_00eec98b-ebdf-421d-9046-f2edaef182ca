<template>
  <div class="push">
    <div class="head-box">
      <div class="flex" style="align-items: center;">
        <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
        <div class="head-title article-singer-container">
          任务
        </div>
      </div>
      <div class="flex">
        <div class="classpro-btn" @click="showSelect = true">工单中选择</div>
        <div class="classpro-btn" @click="showBoookSelect">教材中选择</div>
        <div class="classpro-btn" @click="submitTask">发布</div>
      </div>
    </div>

    <div class="content">
      <div class="task-title">
        <div class="taks-tips-box-need flex-shrink-0">任务标题</div>
        <el-input v-model="taskTitle" placeholder="请输入内容" />
      </div>
      <div v-if="formType==='html'" class="task-body">
        <div class="taks-tips-box-need flex-shrink-0">任务内容</div>
        <div class="flex flex-col" style=" width: 100%;">
          <TinymceEditor
            v-model="html"
            :init="init"
            style="min-height: 400px;"
          />
        </div>
      </div>
      <div v-if="formType==='json'">
        <div v-for="(item,index) in formList" :key="index" class="task-item">
          <div class="taks-tips-box-no-need flex-shrink-0"><i class="el-icon-circle-close close" @click="deleteFormItem(index)"></i> {{ item.title }}</div>
          <div class="flex flex-col" style="width: 100%;">
            <TinymceEditor
              v-model="item.content"
              :init="init"
              style="min-height: 400px;"
            />
          </div></div>
      </div>
      <div v-if="formType==='json'" class="task-file">
        <div class="taks-tips-box flex-shrink-0">添加字段</div>
        <el-button class="add_button" type="primary" plain @click="openTaskPop">+自定义字段</el-button>
        <el-button v-for="(item,index) in addTaskList" :key="index" class="add_button" type="primary" plain @click="addFormItem(item.title)">{{ item.title }}</el-button>
      </div>
      <div class="task-file">
        <div class="taks-tips-box flex-shrink-0">任务附件</div>
        <el-upload action="''" :before-upload="beforeUpload" :show-file-list="false">
          <div v-if="!progress" class="classpro-btn">上传任务</div>
        </el-upload>
        <div v-if="progress" class="progress">上传中：{{ percent }}%</div>
        <div v-show="resourcesList.length" class="file-length" @click="previewBox = true">已上传{{ resourcesList.length }}个文件</div>
      </div>
    </div>

    <div v-if="selectShow" class="select-box">
      <div class="select-body">
        <div class="select-title ">
          <div class="fb f12">选择任务</div>
          <div class="flex">
            <div class="classpro-btn" @click="handleImport">开始导入</div>
            <i class="el-icon-close pointer" @click="handleCloseSelect"></i>
          </div>
        </div>

        <div class="select-content">
          <Read :task-mode="true" :pre-mode="true" @selectInfo="handleSelectInfo" />
        </div>
      </div>
    </div>

    <div v-if="previewBox" class="preview-box">
      <div class="preview-content">
        <div class="preview-content-head">
          <div class="title">文件列表</div>
          <div @click.stop="previewBox = false"><i class="el-icon-close pointer"></i></div>
        </div>
        <div class="preview-content-content">
          <taskDatas :files-list="resourcesList" :has-del="true" @del="handleDelResources" />
        </div>
      </div>
    </div>
    <addTaskPop ref="taskPop" @success="addFormItem" />
    <tipsPop ref="tips" :position="tipsPositon" :info="tipsInfo" />
    <imgGroupPop ref="imgs" :info="imgListInfo" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <doTest ref="doTest" :test-id="testId" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <div v-if="showSelect" class="select-box">
      <div class="select-body">
        <div class="select-title ">
          <div class="fb f12">选择任务</div>
          <div class="flex">
            <div class="classpro-btn" @click="handleImportTask">开始导入</div>
            <i class="el-icon-close pointer" @click="showSelect=false"></i>
          </div>
        </div>

        <div class="select-content">
          <taskSelect ref="taskSelect" :book-id="bookId" :show-select="showSelect" @selectTaskInfo="handleSelectTaskInfo" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { Boot } from '@wangeditor/editor'
import '@wangeditor/editor/dist/css/style.css'
import { digitalHomework, addResource, deleteResource } from '@/api/digital-api.js'
import { getFileUploadAuthor } from '@/api/user-api'
import Read from '@/views/digitalbooks/read/index.vue'
import taskDatas from '../datas.vue'
import addTaskPop from './addTaskPop.vue'
import tinymce from 'tinymce/tinymce'
import TinymceEditor from '@tinymce/tinymce-vue'
import defaultConfig from '../../editor/utils/config'
import { uploadVideo } from '../../editor/utils/video'
import { uploadAudio } from '../../editor/utils/audio'
import { tips } from '../../editor/utils/tips'
import { imgGroup } from '../../editor/utils/imgGroup'
import { uploadVideoCard } from '../../editor/utils/videoCard'
import { fileDownLoad } from '../../editor/utils/fileDownload'
import { indent2em } from '../../editor/utils/indent2em'
import { lineHeight } from '../../editor/utils/lineHeight'
import tipsPop from '../../editor/components/tipsPop.vue'
import { uploadImg } from '../../editor/utils/uploadImg'
import { formateImg } from '../../editor/utils/formateImg'
import imgGroupPop from '../../editor/components/imgGroupPop.vue'
import videoCardPop from '../../editor/components/videoPop.vue'
import { throttle } from '@/utils/index'
import { saveAs } from 'file-saver'
import { Notification } from 'element-ui'
import { trainingdPlugin } from '../../editor/utils/addTraining'
import doTest from '../../../digitalbooks/task/doTest.vue'
import officeView from '../../editor/components/officeView.vue'
import { getSqlPlatformToken } from '@/api/training-api'
import { getToken } from '@/utils/auth'
import taskSelect from './taskSelect.vue'
tinymce.PluginManager.add('uploadVideo', uploadVideo)
tinymce.PluginManager.add('uploadAudio', uploadAudio)
tinymce.PluginManager.add('formateImg', formateImg)
tinymce.PluginManager.add('imgGroup', imgGroup)
tinymce.PluginManager.add('lineHeight', lineHeight)
tinymce.PluginManager.add('uploadImg', uploadImg)
tinymce.PluginManager.add('indent2em', indent2em)
tinymce.PluginManager.add('tips', tips)
tinymce.PluginManager.add('uploadVideoCard', uploadVideoCard)
tinymce.PluginManager.add('fileDownLoad', fileDownLoad)
tinymce.PluginManager.add('trainingdPlugin', trainingdPlugin)

// 注册音频组件
import audio from '@/views/digitalbooks/editor/utils/audioMenu/index.js'
Boot.registerModule(audio)
export default {
  components: { TinymceEditor, Read, taskDatas, addTaskPop, videoCardPop, imgGroupPop, tipsPop, doTest, officeView, taskSelect },
  data () {
    return {
      init: Object.assign(defaultConfig, {
      }),
      selectShow: false,
      selectInfo: null,
      videoInfo: null,
      tipsInfo: {
        keyword: '',
        content: ''
      },
      tipsPositon: {
        top: 0,
        left: 0
      },
      imgListInfo: null,
      resourcesList: [],
      taskTitle: '',
      taskId: '',
      bookId: 0,
      studentCourseId: 0,
      editor: [],
      html: '',
      formList: [
        {
          title: '任务目标',
          content: `<p></p>`
        }
      ],
      addTaskList: [
        {
          title: '任务介绍',
          content: `<p></p>`
        },
        {
          title: '任务资讯',
          content: `<p></p>`
        },
        {
          title: '任务规划',
          content: `<p></p>`
        },
        {
          title: '任务实施',
          content: `<p></p>`
        },
        {
          title: '任务扩展',
          content: `<p></p>`
        },
        {
          title: '任务日志',
          content: `<p></p>`
        },
        {
          title: '工作日志',
          content: `<p></p>`
        },
        {
          title: '任务总结',
          content: `<p></p>`
        },
        {
          title: '考核评价',
          content: `<p></p>`
        }
      ],
      toolbarConfig: {
        toolbarKeys: [
          'headerSelect',
          // 'blockquote',
          '|',
          'bold',
          'underline',
          'italic',
          'through',
          // 'code',
          'sup',
          'sub',
          'clearStyle',
          'color',
          'bgColor',
          '|',
          'fontSize',
          'fontFamily',
          'lineHeight',
          '|',
          'bulletedList',
          'numberedList',
          'todo',
          {
            key: 'group-justify',
            title: '对齐',
            iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>', // 可选
            menuKeys: ['justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify']
          },
          {
            key: 'group-indent',
            title: '缩进',
            iconSvg: '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>', // 可选
            menuKeys: ['indent', 'delIndent']
          },
          '|',
          // 'emotion',
          // 'insertLink',
          'uploadImage',
          'uploadVideo',
          'insertTable',
          // 'codeBlock',
          'divider',
          '|',
          'undo',
          'redo'

        ],
        insertKeys: {
          index: 24,
          keys: ['uploadAudio'] // show menu in toolbar
        }
      },
      acitveId: 0,
      mode: 'default', // or 'simple'
      // 0 创建，1 获取，2 更新，3 删除
      apiType: ['create', 'get', 'update', 'delete'],
      ossUrl: '',
      percent: 0,
      progress: false,
      previewBox: false,
      formType: '',
      testId: 0,
      openFlag: false,
      officeUrl: '',
      showSelect: false,
      token: getToken(),
      selectTaskInfo: null
    }
  },
  mounted () {
    this.bookId = this.$route.query && this.$route.query.id
    this.studentCourseId = this.$route.query && this.$route.query.studentCourseId
    // 编辑模式
    this.taskId = this.$route.query && this.$route.query.eId
    if (this.taskId) {
      this.getTaskInfo()
    } else {
      this.formType = 'json'
    }
    tinymce.init({}).then(() => {
      setTimeout(() => {
        this.initFun()
      }, 1000)
    })
  },
  methods: {
    showBoookSelect() {
      if (this.formList.length === 0) {
        this.$message.warning('请至少添加一个任务步骤')
        return
      }
      this.selectShow = true
    },
    handleImportTask() {
      this.showSelect = false
      this.html = this.selectTaskInfo.content
      this.formList = this.isJSON(this.selectTaskInfo.content) ? JSON.parse(this.selectTaskInfo.content) : []
      this.formType = this.isJSON(this.selectTaskInfo.content) ? 'json' : 'html'
      this.taskTitle = this.selectTaskInfo.title
      this.resourcesList = this.selectTaskInfo.resourcesList.map(item => {
        return {
          ...item,
          mediaFileId: null
        }
      })
      this.selectTaskInfo = null
    },
    handleSelectTaskInfo(data) {
      this.selectTaskInfo = data
    },
    closePop () {
      if (this.$refs.tips) { this.$refs.tips.close() }
    },
    initFun () {
      if (!document.getElementsByClassName('tox-edit-area__iframe')) return
      document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.addEventListener('click', this.richEvent, false)
      document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.addEventListener('dblclick', this.showImageList, false)
      document.addEventListener('click', this.closePop)
    },
    showImageList (e) {
      if (e.target.classList.contains('img_card_button')) {
        this.imgListInfo = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.imgs.open()
      }
      if (e.target.classList.contains('video_button')) {
        this.videoInfo = {
          src: e.target.src,
          poster: e.target.poster,
          text: e.target.parentNode.getElementsByTagName('p')[1].innerText
        }
        this.$refs.videoCard.open()
      }
    },
    richEvent (e) {
      this.$refs.tips.close()
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        const view = document.getElementById('tinymce_ifr')
        let y = item.getBoundingClientRect().top + view.getBoundingClientRect().top + 20
        if (window.innerHeight - e.pageY < 195) {
          y = window.innerHeight - 200
        }
        this.tipsPositon = {
          top: y,
          left: item.getBoundingClientRect().left + view.getBoundingClientRect().left
        }
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.show()
      }
      if (e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          const url = e.target.parentNode.children[1].innerText
          const fileName = e.target.parentNode.children[2].innerText
          const notif = Notification({
            title: fileName,
            dangerouslyUseHTMLString: true,
            message: '',
            duration: 0
          })
          throttle(function () {
            const xhr = new XMLHttpRequest()
            xhr.open('get', url)
            xhr.responseType = 'blob'
            xhr.addEventListener('progress', (e) => {
              const complete = Math.floor(e.loaded / e.total * 100)
              notif.message = complete + '%'
              if (complete >= 100) {
                notif.close()
              }
            })
            xhr.send()
            xhr.onload = function () {
              if (this.status === 200 || this.status === 304) {
                const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
                saveAs(blob, fileName)
              }
            }
          }, 2000)
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.doTest.open()
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
    },
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      console.log(url, fileName)
      if (this.getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.imgs.open()
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    deleteFormItem(index) {
      this.formList.splice(index, 1)
      this.editor.splice(index, 1)
    },
    openTaskPop() {
      this.$refs.taskPop.open()
    },
    addFormItem(data) {
      this.formList.push({ title: data, content: '' })
    },
    back () {
      this.$router.push(`/digitalbooks/task-t?id=${this.bookId}&studentCourseId=${this.studentCourseId}`)
    },
    handleSelectInfo (item) {
      this.selectInfo = item
    },
    handleImport () {
      if (this.selectInfo) {
        this.taskTitle = this.selectInfo.catalogueName
        if (this.formType === 'html') { this.html = this.selectInfo.html } else { this.formList[this.acitveId].content = this.selectInfo.html }
        this.selectInfo = null
        this.selectShow = false
      }
    },
    handleCloseSelect () {
      this.selectInfo = null
      this.selectShow = false
    },
    onCreated (editor) {
      this.editor.push(Object.seal(editor))
      console.log(this.editor)// 一定要用 Object.seal() ，否则会报错
    },
    getHtml () {
      return this.editor.getHtml()
    },
    handleEdit (index) {
      this.acitveId = index
      if (!this.editor[index].isFocused()) {
        this.editor[index].focus(true)
      }
    },
    async submitTask () {
      if (this.formList.length === 0) {
        this.$message.warning('请至少添加一个任务步骤')
        return
      }
      if (this.taskTitle && (this.html || this.formList[0].content)) {
        let data
        if (this.taskId) {
          data = await digitalHomework({
            apiType: 'update',
            id: this.taskId,
            title: this.taskTitle,
            studentCourseId: this.studentCourseId
          }, { content: this.formType === 'html' ? this.html : JSON.stringify(this.formList) })
        } else {
          data = await digitalHomework({
            apiType: 'create',
            title: this.taskTitle,
            studentCourseId: this.studentCourseId
          }, { content: this.formType === 'html' ? this.html : JSON.stringify(this.formList) })
          this.taskId = data.data.id
        }
        // 处理文件逻辑

        if (this.resourcesList && this.resourcesList.length) {
          const arr = []

          this.resourcesList.map(val => {
            if (!val.mediaFileId) {
              arr.push(val.mediaFile)
            }
          })
          // 上传
          await addResource({
            sourceId: this.taskId,
            resourceType: 'DIGITAL_HOMEWORK_RESOURCE'
          }, arr)
        }

        // 回到任务列表
        this.back()
      } else {
        const str = []
        if (!this.taskTitle) {
          str.push('任务标题')
        }
        if (!this.html || this.html === '<p><br></p>' || this.formList[0].content === '') {
          str.push('任务内容')
        }
        this.$message.error(`${str.join(',')}未填写！`)
      }
    },
    async getTaskInfo () {
      const { data } = await digitalHomework({
        apiType: 'get',
        id: this.taskId
      })
      this.formType = this.isJSON(data.content) ? 'json' : 'html'
      this.html = this.isJSON(data.content) ? '' : data.content
      this.formList = this.isJSON(data.content) ? JSON.parse(data.content) : [{
        title: '任务目标',
        content: `<p></p>`
      }]
      this.taskTitle = data.title
      this.resourcesList = data.resourcesList
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    async handleDelResources ({ item, index }) {
      try {
        if (item.mediaFileId) {
        // 已经上传到后台的资料 调用接口解绑
          await deleteResource({
            coursecommUnitResourceId: item.id
          })
        }
        this.resourcesList.splice(index, 1)
      } catch (error) {
        console.log(error)
      }
    },
    // 文件上传
    async beforeUpload (file) {
      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }

      const { data } = await this.getOssSign(mediaType, file.name)
      const ossCDN = data[0].ossConfig.ossCDN
      this.ossUrl = data[0].ossConfig.host
      this.progress = true
      const filename = file.name

      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)

      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })

      const fileObj = {
        mediaFile: {
          size: Math.floor(file.size / 1024),
          type: mediaType,
          fileName: filename.substring(0, filename.lastIndexOf('.')),
          url: data[0].fileName,
          expendType: filename.substring(filename.lastIndexOf('.') + 1)
        },
        mediaFileId: 0,
        ossUrl: ossCDN
      }

      this.resourcesList.push(fileObj)
      return Promise.reject()
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.f12{
  font-size: 16px;
}

.push {
  width: 100%;
  height: 100%;
  padding: 10px 20px;
  background: #FFF;

  .head-box {
    height: 40px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: space-between;
    align-items: center;
    position: relative;

    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: 14px;
      font-weight: 500;
      margin-left: 10px;
    }

    .classpro-btn {
      height: 30px;
      padding: 5px 10px;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 50px);
    overflow: auto;
    @include scrollBar;
    padding-right: 15px;
    .task-title,
    .task-file {
      height: 80px;
      display: flex;
      align-items: center;
      .add_button{
        font-size: var(--font-size-M);
        padding: 8px;
      }
    }

    .task-body {
      display: flex;
      height: calc(100% - 80px - 80px);
    }
    .task-item{
      display: flex;
      margin-top: 20px;
      min-height: 200px;
      border-radius: 10px;
      .close{
        cursor: pointer;
      }
      .close:hover{
        color:#2F80ED
      }
    }
    .task-file {
      .classpro-btn {
        padding: 8px 10px;
        margin-right: 10px;
      }

      .file-length {
        color: #2F80ED;
        font-size: 16px;
        text-decoration-line: underline;
        cursor: pointer;
      }
    }

    .taks-tips-box,
    .taks-tips-box-need,
    .taks-tips-box-no-need {
      width: 100px;
      color: #4F4F4F;
      font-size: 14px;
    }
    .taks-tips-box-need {
      &::after {
        content: '*';
        color: #EB5757;
        font-size: 16px;
      }
    }
  }

  .select-box {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: rgba($color: #000000, $alpha: .4);
    z-index: 3;
    display: flex;
    align-items: flex-end;

    .select-body {
      width: 100%;
      height: 90%;
      background: #e6f1fc;
      border-radius: 10px 10px 0 0;
      padding: 10px;
    }

    .select-title {
      display: flex;
      justify-content: space-between;
      height: 50px;

      .fb {
        font-weight: 500;
      }
      .f26 {
        font-size: 26px;
      }

      .el-icon-close {
        font-size: 26px;
        margin-top: 2px;
      }

      .classpro-btn {
        height: 30px;
        padding: 5px 10px;
        margin-right: 10px;
      }
    }

    .select-content {
      width: 100%;
      height: calc(100% - 50px);
      overflow-x: auto;
      @include scrollBar;
    }
  }

  ::v-deep .w-e-bar-divider {
    display: none;
  }

  ::v-deep .w-e-textarea-video-container {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;

    video {
      width: 100%;
    }
  }

  .preview-box {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 100px;
    box-sizing: border-box;
    background: rgba($color: #000000, $alpha: .5);
    z-index: 9;

    .preview-content {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;
      box-sizing: border-box;
      overflow-y: auto;
      @include scrollBar;

      .preview-content-head {
        height: 40px;
        display: flex;
        justify-content: space-between;

        .title {
          font-size: 16px;
          font-weight: 500;
        }

        .el-icon-close {
          font-size: var(--font-size-XXL);
        }
      }

      .preview-content-content {
        height: calc(100% - 40px);
        overflow-y: auto;
      }
    }
  }
}
</style>
