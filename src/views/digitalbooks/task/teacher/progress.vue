<template>
  <div class="task-progress">
    <div class="task-box">
      <div class="task-head">
        <div class="flex items-center t-left">
          <div class="head-title article-singer-container">
            提交详情
          </div>
        </div>
        <div>
          <i class="el-icon-close close pointer" @click="$emit('close')"></i>
        </div>
      </div>
      <div class="task-name">
        {{ taskInfo && taskInfo.taskInfo && taskInfo.taskInfo.title }}
      </div>
      <div class="task-content">
        <div class="table-head">
          <div>学生姓名</div>
          <div>状态</div>
          <div>操作</div>
        </div>
        <div v-for="item in taskInfo.progress" :key="item.user.id" class="table-body">
          <div>{{ item.user.displayName || item.user.mobile }}</div>
          <div>
            <span v-if="item.digitalHomeworHasCommplete">已提交</span>
            <span v-else class="unsubmit">未提交</span>
          </div>
          <div>
            <div v-if="item.digitalHomeworHasCommplete" class="classpro-btn" @click="handleCheck(item)">查看</div>
            <div v-else class="classpro-btn-disable">查看</div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="detailShow" class="task-pop">
      <taskDetail :task-id="selectUser.taskId" :mode="'tCheck'" :check-user-id="selectUser.userId" @close="detailShow = false" />
    </div>
  </div>
</template>

<script>
import taskDetail from '../detail.vue'
export default {
  components: { taskDetail },
  props: {
    taskInfo: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {
      detailShow: false,
      selectUser: {
        taskId: '',
        userId: ''
      }
    }
  },
  mounted () {
    console.log(this.taskInfo)
  },
  methods: {
    handleCheck (item) {
      this.selectUser = {
        taskId: this.taskInfo.taskInfo.id,
        userId: item.user.id
      }
      this.detailShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.task-progress {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  background: rgba($color: #000000, $alpha: .4);
  display: flex;
  justify-content: center;
  align-items: center;

  .task-box {
    width: 600px;
    height: 500px;
    background-color: #FFF;
    border-radius: 10px;
    padding: 10px;

    .task-head {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      position: relative;

      .t-left {
        width: calc(100% - 40px);
      }

      .eidt {
        width: 60px;
        height: 60px;
        object-fit: cover;
        cursor: pointer;
      }

      .head-title {
        color: #000;
        font-size: 24px;
        font-weight: 500;
        margin-left: 10px;
      }

      .close {
        font-size: 30px;
      }
    }

    .task-name {
      color: #000;
      font-size: 14px;
      padding: 0 10px;
      height: 30px;
      display: flex;
      align-items: center;
    }

    .task-content {
      width: 100%;
      height: calc(100% - 40px - 40px);
      overflow-y: auto;
      @include scrollBar;

      .table-head, .table-body {
        display: flex;
        padding: 0 10px;
      }
      .table-head > div, .table-body > div{
        width: 33.3333333%;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 40px;
        font-size: 14px;

        .unsubmit {
          color: #EB5757;
        }
        .classpro-btn, .classpro-btn-disable {
          padding: 5px 10px;
        }
      }
      .table-head > div{
        font-weight: 500;
      }
    }
  }
  .task-pop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #E9F2FF;
  }
}
</style>
