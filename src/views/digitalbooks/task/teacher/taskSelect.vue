<template>
  <div class="container">
    <div class="left">
      <el-tree
        ref="tree"
        :data="treeData"
        :props="treeProps"
        check-strictly
        :expand-on-click-node="false"
        node-key="id"
        highlight-current
        @node-click="handleNodeClick"
      />
    </div>
    <div ref="content" class="rich-text-container">
      <div class="content-wrapper">
        <div v-if="content.length === 0" class="emty">
          <Empty description="暂无数据" />
        </div>
        <div v-if="contentType==='html'" class="rich-text-content" v-html="html"></div>
        <div v-else>
          <div v-for="(item,index) in content" :key="index" class="rich-text-content">
            <p class="title">{{ item.title }}</p>
            <div v-html="item.content"></div>
          </div>
        </div>
      </div>
      <!-- 文件列表区域 -->
      <div class="footer-wrapper">
        <div v-show="resourcesList && resourcesList.length" class="task-files">
          <span class="title">任务附件:</span>
          <span class="link" @click="previewBox = true">查看附件</span>
        </div>
      </div>
    </div>
    <div v-if="previewBox" class="preview-box">
      <div class="preview-content">
        <div class="preview-content-head">
          <div class="title">文件列表</div>
          <div @click.stop="previewBox = false"><i class="el-icon-close pointer"></i></div>
        </div>
        <div class="preview-content-content">
          <taskDatas :files-list="resourcesList" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Empty from '@/components/classPro/Empty/index.vue'
import { getDigitalTaskListByCatalogueId, getBookCatalogue, digitalTask } from '@/api/digital-api.js'
import taskDatas from '../datas.vue'
import Prism from 'prismjs'
import 'prismjs/themes/prism-tomorrow.min.css'
export default {
  components: {
    Empty,
    taskDatas
  },
  props: {
    bookId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      treeProps: {
        children: 'childCatalogue',
        label: 'title',
        disabled: 'canRead'
      },
      treeData: [],
      selectedContent: '',
      contentType: 'html',
      html: '',
      content: [],
      previewBox: false,
      resourcesList: [],
      taskData: {}
    }
  },
  async mounted() {
    await this.getBookCatalogue()
  },
  methods: {
    async getBookCatalogue() {
      const { data } = await getBookCatalogue({
        bookId: this.bookId
      })
      const cataloguePromises = data.map(async (item) => {
        const childData = await getDigitalTaskListByCatalogueId({
          digitalCatalogueId: item.id
        })
        return {
          ...item,
          childCatalogue: childData.data.filter(item => item.sourceId === 0)
        }
      })
      this.treeData = await Promise.all(cataloguePromises)
    },
    handleNodeClick(data, node) {
      if (node.level === 1) {
        node.expanded = !node.expanded
        return
      }
      this.getTaskInfo(data.id)
    },
    async getTaskInfo (id) {
      const { data } = await digitalTask({
        apiType: 'get',
        id: id
      }, {}, { authorization: this.token })
      this.contentType = this.isJSON(data.content) ? 'json' : 'html'
      this.$emit('selectTaskInfo', data)
      this.html = data.content
      this.content = this.isJSON(data.content) ? JSON.parse(data.content) : []
      this.$nextTick(() => {
      })
      this.taskTitle = data.title
      this.resourcesList = data.resourcesList
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
        Prism.highlightAll()
      })
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    }
  }
}
</script>
  <style scoped lang="scss">
  .container {
    width:75%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    .left {
      width: 200px;
      min-width: 200px;
      height: 100%;
      border-radius: 5px;
      overflow: auto;
      padding-top: 10px;
      @include scrollBar;
      background: #fff;
      ::v-deep .el-tree{
        height: 100%;
      }

      ::v-deep .el-checkbox__inner::after {
        border-width: 2px;
      }

      ::v-deep .el-checkbox__label {
        padding-left: 3px !important;
      }

      ::v-deep .el-tree-node__label {
        width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 10px;
      }
    }
  }
  .preview-content {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
    @include scrollBar;

    .preview-content-head {
      height: 40px;
      display: flex;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: 500;
      }

      .el-icon-close {
        font-size: var(--font-size-XXL);
      }
    }

    .preview-content-content {
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
  .print-btn{
    display: flex;
    align-items: center;
    height: 30px;
    position: relative;
  }
  .waring{
    font-size: 10px;
    position: absolute;
    bottom: -30px;
    right: 8vw;
    color: red;
  }
  .rich-text-container {
    width: 100%;
    height: 100%;
    margin-left: 20px;
    background: #fff;
    border-radius: 5px;
    position: relative;
    display: flex;
    flex-direction: column;

    .content-wrapper {
      flex: 1;
      padding: 20px;
      overflow: hidden;
    }

    .footer-wrapper {
      height: 40px;
      min-height: 40px;
      border-top: 1px solid #eee;
      padding: 0 20px;
    }
  }
  .tips{
      cursor: pointer;
      font-size: 10px;
      margin-left: 10px;
      margin-top: 26px;
    }
  .emty {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .print{
    padding: 6px;
    font-size: 10px;
    margin-top: 10px;
  }
  .rich-text-content {
    height: calc(100% - 40px);
    height: 100%;
    overflow: auto;
    @include scrollBar;

    .title {
      color: #2F80ED;
      font-size: 12px;
      font-weight: 800;
    }
    ::v-deep * {
      max-width: 100%;
    }

    ::v-deep pre > code {
      display: block;
      font-size: 12px;
      white-space: pre-wrap;
      word-break: break-all;
    }

    ::v-deep img {
      max-width: 100%;
    }

    ::v-deep video {
      width: 100%;
    }

    ::v-deep pre {
      padding: 0;
    }

    ::v-deep pre > code {
      display: block;
      padding: 10px;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
  .task-files {
    height: 100%;
    display: flex;
    align-items: center;

    .title {
      color: #000;
      font-size: 14px;
      font-weight: 500;
      margin-right: 10px;
    }

    .link {
      color: #2F80ED;
      font-size: 14px;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }
  .preview-box {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 100px;
    box-sizing: border-box;
    background: rgba($color: #000000, $alpha: .5);
    z-index: 9;
    .preview-content {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
    }
  }
  </style>
