<template>
  <div class="w h flex">
    <div class="left">
      <div
        v-for="(item, index) in filesList"
        :key="item.id"
        :class="{ 'item-active': (+activeIndex === +item.mediaFileId || activeIndex === item.mediaFile.url) }"
        class="item-box"
        @click="handleClick(item)"
      >
        <div class="item">{{ item.mediaFile.fileName }}.{{ item.mediaFile.expendType }}</div>
        <div v-if="hasDel" class="flex-shrink-0" @click.stop="handleDel(item, index)">
          <i class="el-icon-delete"></i>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="download-box">
        <div class="download" @click="downloadFile(preInfo.imgUrl, `${preInfo.fileName}.${preInfo.expendType}`)">下载</div>
      </div>
      <div class="r-content">
        <template v-if="preInfo">
          <div v-if="preInfo.type === 'IMAGE'" class="w h overflow-hidden flex justify-center items-center">
            <img class="img-cover" :src="preInfo.imgUrl" />
          </div>
          <div v-else-if="preInfo.type === 'VIDEO'" class="w h overflow-hidden">
            <video-js :options="videoOptions" @player="videoPlay" />
          </div>
          <div v-else-if="preInfo.expendType === 'pdf'" class="w h overflow-hidden">
            <Pdf ref="pdf" />
          </div>
          <div v-else class="ban">暂不支持预览</div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import VideoJs from '@/components/classPro/h5Video'
import Pdf from '@/views/digitalbooks/attendClass/components/inClass/pdf.vue'
import { throttle } from '@/utils/index'
export default {
  components: {
    Pdf,
    VideoJs
  },
  props: {
    filesList: {
      type: [Object, Array],
      default: () => {
        return []
      }
    },
    hasDel: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      unitId: this.$route.query.unitId,
      preInfo: null,
      activeIndex: 0,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false, //  x5内核浏览器不允许自动播放，需要手动播放
        fileShow: false,
        loop: false,
        fluid: false,
        language: 'zh-CN',
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    }
  },
  mounted () {
    this.handleClick(this.filesList[0])
  },
  methods: {
    handleClick (item) {
      this.preInfo = item.mediaFile
      this.activeIndex = item.mediaFileId
      let url = item.mediaFile.url
      if (!item.mediaFileId) {
        url = `${item.ossUrl}/${item.mediaFile.url}`
        this.activeIndex = item.mediaFile.url
      }
      this.preInfo.imgUrl = url
      if (item.mediaFile.type === 'VIDEO') {
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: url,
            type: 'video/mp4'
          }]
        }
      }
      if (item.mediaFile.expendType === 'pdf') {
        this.$nextTick(() => {
          this.$refs.pdf._loadFile(url)
        })
      }
    },
    handleDel (item, index) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('del', { item, index })
        this.preInfo = null
      }).catch(() => {

      })
    },
    videoPlay (player) {
      player.play()
    },
    downloadFile (path, name) {
      throttle(function () {
        const xhr = new XMLHttpRequest()
        xhr.open('get', path)
        xhr.responseType = 'blob'
        xhr.send()
        xhr.onload = function () {
          if (this.status === 200 || this.status === 304) {
            const fileReader = new FileReader()
            fileReader.readAsDataURL(this.response)
            fileReader.onload = function () {
              const a = document.createElement('a')
              a.style.display = 'none'
              a.href = this.result
              a.download = name
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
            }
          }
        }
      }, 2000)
    }
  }
}
</script>

<style lang="scss" scoped>
.left {
  width: 30%;
  padding: 10px;
  box-sizing: border-box;
  border-right: 1px solid #ddd;
  overflow-y: scroll;
  @include scrollBar;
  .item-box {
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 5px;
    box-sizing: border-box;
    cursor: pointer;

    .el-icon-delete {
      font-size: 20px;
      padding-left: 10px;
    }

    .item {
      word-wrap: break-word;
      word-break: break-all;
      font-size: 12px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }

    &:hover {
      background: #2F80ED;
      color: #fff;
    }
  }

  .item-active {
    background: #2F80ED;
    color: #fff;
  }
}

.right {
  width: 70%;
  padding: 10px;

  .download-box {
    height: 40px;
    display: flex;
    justify-content: flex-end;

    .download {
      font-size: 16px;
      cursor: pointer;
      width: 60px;
      font-weight: 500;
      color: #2F80ED;
      text-align: right;
    }
  }

  .r-content {
    width: 100%;
    height: calc(100% - 40px);
  }

  .ban {
    font-size: 16px;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .img-cover {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
  }
}
</style>
