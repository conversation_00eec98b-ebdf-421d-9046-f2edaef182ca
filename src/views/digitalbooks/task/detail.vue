<template>
  <!-- 任务详情 -->
  <div class="task-class">
    <div class="head-box">
      <div class="flex items-center t-left">
        <img class="eidt" src="@/assets/digitalbooks/task-edit.svg" />
        <div class="head-title article-singer-container">
          {{ taskTitle }}
        </div>
      </div>
      <div>
        <i class="el-icon-close close pointer" @click="$emit('close')"></i>
      </div>
    </div>

    <div class="d-content dig-task-box">
      <div class="content-box" @click="readfunc">
        <div v-if="contentType==='html'" class="editor-content-view" v-html="html"></div>
        <div v-else>
          <div v-for="(item,index) in content" :key="index" class="editor-content-view">
            <p class="title">{{ item.title }}</p>
            <div v-html="item.content"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="d-footer">
      <div v-show="resourcesList && resourcesList.length" class="task-files">
        <span class="title">任务附件:</span>
        <span class="link" @click="previewBox = true">查看附件</span>
      </div>
      <div class="task-submit">
        <template v-if="mode === 'submit'">
          <div class="tips">*任务可以用照片或文件形式上传</div>
          <span v-show="resourcesUserList && resourcesUserList.length" class="link" @click="previewUserBox = true">已上传{{
            resourcesUserList.length }}个文件</span>
          <el-upload action="''" :before-upload="beforeUpload" :show-file-list="false">
            <div v-if="!progress" class="submit-files">上传文件</div>
          </el-upload>
          <div v-if="progress" class="progress">上传中：{{ percent }}%</div>
          <div class="submit" @click="submitTask">提交</div>
        </template>
        <template v-if="mode === 'tCheck'">
          <span v-show="resourcesUserList && resourcesUserList.length" class="link" @click="previewUserBox = true">已上传{{
            resourcesUserList.length }}个文件</span>
        </template>
        <template v-else-if="mode === 'edit'">
          <div class="submit" @click="handleEdit">编辑</div>
        </template>
      </div>
    </div>
    <div v-if="previewBox" class="preview-box">
      <div class="preview-content">
        <div class="preview-content-head">
          <div class="title">文件列表</div>
          <div @click.stop="previewBox = false"><i class="el-icon-close pointer"></i></div>
        </div>
        <div class="preview-content-content">
          <taskDatas :files-list="resourcesList" />
        </div>
      </div>
    </div>
    <!-- 学生文件列表 -->
    <div v-if="previewUserBox" class="preview-box">
      <div class="preview-content">
        <div class="preview-content-head">
          <div class="title">文件列表</div>
          <div @click.stop="previewUserBox = false"><i class="el-icon-close pointer"></i></div>
        </div>
        <div class="preview-content-content">
          <taskDatas :files-list="resourcesUserList" :has-del="true" @del="handleDelResources" />
        </div>
      </div>
    </div>
    <tipsPop ref="tips" :position="tipsPositon" :info="tipsInfo" />
    <imgGroupPop ref="images" :info="imgListInfo" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <doTest ref="doTest" :test-id="testId" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <doAiTraing ref="doAiTraing" :student-course-id="Number(studentCourseId)" />
    <doExcelTraing ref="excelRraing" :student-course-id="Number(studentCourseId)" />
  </div>
</template>

<script>
import doExcelTraing from '@/views/digitalbooks/editor/components/doExcelTraing.vue'
import doAiTraing from '@/views/digitalbooks/editor/components/doAiTraing.vue'
import { digitalHomework, addResource, deleteResource, userDigitalHomework } from '@/api/digital-api.js'
import taskDatas from './datas.vue'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { mapGetters } from 'vuex'
import imgGroupPop from '../editor/components/imgGroupPop.vue'
import videoCardPop from '../editor/components/videoPop.vue'
import tipsPop from '../editor/components/tipsPop.vue'
import { throttle } from '@/utils/index'
import { saveAs } from 'file-saver'
import { Notification } from 'element-ui'
import doTest from '../../digitalbooks/task/doTest.vue'
import { getSqlPlatformToken } from '@/api/training-api'
import officeView from '../editor/components/officeView.vue'
import { getToken } from '@/utils/auth'
import Prism from 'prismjs'
import 'prismjs/themes/prism-tomorrow.min.css'
export default {
  components: { taskDatas, imgGroupPop, videoCardPop, tipsPop, doTest, officeView, doAiTraing, doExcelTraing },
  props: {
    // submit 提交模式，check 查看模式，edit 编辑，tCheck 老师查看学生提交内容模式
    mode: {
      type: String,
      default: 'check'
    },
    taskId: {
      type: [String, Number],
      default: ''
    },
    checkUserId: {
      type: [Number, String],
      default: ''
    }
  },
  data () {
    return {
      tipsPositon: {
        top: 0,
        left: 0
      },
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      },
      tipsInfo: {
        keyword: '',
        content: ''
      },
      imgListInfo: null,
      bookId: '',
      studentCourseId: '',
      html: '',
      taskTitle: '',
      previewBox: false,
      resourcesList: [],
      previewUserBox: false,
      resourcesUserList: [],
      userHomeworkId: '',
      ossUrl: '',
      percent: 0,
      progress: false,
      homeworkId: '',
      contentType: 'html',
      content: [],
      testId: 0,
      openFlag: false,
      officeUrl: '',
      token: getToken()
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  mounted () {
    this.bookId = this.$route.query && this.$route.query.id
    this.studentCourseId = this.$route.query && this.$route.query.studentCourseId
    this.getTaskInfo()
    // 获取学生提交情况
    this._userDigitalHomework()
  },
  methods: {
    readfunc (e) {
      this.$refs.tips.close()
      if (e.target.classList.contains('img_card_button')) {
        console.log(1)
        this.imgListInfo = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.images.open()
      }
      if (e.target.classList.contains('video_button')) {
        e.preventDefault()
        this.videoInfo = {
          src: e.target.src,
          poster: e.target.poster,
          text: e.target.parentNode.getElementsByTagName('p')[1].innerText
        }
        this.$refs.videoCard.open()
      }
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        let y = item.getBoundingClientRect().top + 20
        if (window.innerHeight - e.pageY < 195) {
          y = window.innerHeight - 200
        }
        this.tipsPositon = {
          top: y,
          left: item.getBoundingClientRect().left
        }
        console.log(item, item.children)
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.show()
      }
      if (e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          const url = e.target.parentNode.children[1].innerText
          const fileName = e.target.parentNode.children[2].innerText
          const notif = Notification({
            title: fileName,
            dangerouslyUseHTMLString: true,
            message: '',
            duration: 0
          })
          throttle(function () {
            const xhr = new XMLHttpRequest()
            xhr.open('get', url)
            xhr.responseType = 'blob'
            xhr.addEventListener('progress', (e) => {
              const complete = Math.floor(e.loaded / e.total * 100)
              notif.message = complete + '%'
              if (complete >= 100) {
                notif.close()
              }
            })
            xhr.send()
            xhr.onload = function () {
              if (this.status === 200 || this.status === 304) {
                const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
                saveAs(blob, fileName)
              }
            }
          }, 2000)
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.doTest.open()
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
      if (e.target.classList.contains('to_aiTrain')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.$refs.doAiTraing.open(id)
      }
      if (e.target.classList.contains('to_excel')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.$refs.excelRraing.open(id)
      }
    },
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      if (this.getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.images.open()
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    async getTaskInfo () {
      const { data } = await digitalHomework({
        apiType: 'get',
        id: this.taskId
      })
      this.contentType = this.isJSON(data.content) ? 'json' : 'html'
      this.html = data.content
      this.content = this.isJSON(data.content) ? JSON.parse(data.content) : []
      this.$nextTick(() => {
        Prism.highlightAll()
      })
      this.taskTitle = data.title
      this.resourcesList = data.resourcesList
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    async _userDigitalHomework () {
      let userId = this.id
      if (this.mode === 'tCheck') {
        // 老师查看模式
        userId = this.checkUserId
      }
      const { data } = await userDigitalHomework({
        userId: userId,
        apiType: 'get',
        digitalHomeworkId: this.taskId,
        studentCourseId: this.studentCourseId
      })
      if (data) {
        // 学生提交过的作业id
        this.homeworkId = data.id
        this.resourcesUserList = data.coursecommUnitResourceList
      } else {
        this.homeworkId = ''
        this.resourcesUserList = []
      }
    },
    handleEdit () {
      this.$router.push(`/digitalbooks/push-task?id=${this.bookId}&studentCourseId=${this.studentCourseId}&eId=${this.taskId}`)
    },
    async submitTask () {
      if (this.resourcesUserList && this.resourcesUserList.length === 0) {
        this.$message('请上传文件后提交')
        return
      }
      let data
      if (this.homeworkId) {
        data = await userDigitalHomework({
          apiType: 'update',
          digitalHomeworkId: this.taskId,
          userId: this.id,
          id: this.homeworkId,
          studentCourseId: this.studentCourseId
        })
      } else {
        data = await userDigitalHomework({
          apiType: 'create',
          digitalHomeworkId: this.taskId,
          userId: this.id,
          studentCourseId: this.studentCourseId
        })
        this.homeworkId = data.data.id
      }
      // 处理文件逻辑

      if (this.resourcesUserList && this.resourcesUserList.length) {
        const arr = []

        this.resourcesUserList.map(val => {
          if (!val.mediaFileId) {
            arr.push(val.mediaFile)
          }
        })
        // 上传
        await addResource({
          sourceId: this.taskId,
          resourceType: 'DIGITAL_HOMEWORK_USER_RESOURCE'
        }, arr)
      }
      this.$emit('close')
    },
    async handleDelResources ({ item, index }) {
      try {
        if (item.mediaFileId) {
          // 已经上传到后台的资料 调用接口解绑
          await deleteResource({
            coursecommUnitResourceId: item.id
          })
        }
        this.resourcesUserList.splice(index, 1)
      } catch (error) {
        console.log(error)
      }
    },
    // 文件上传
    async beforeUpload (file) {
      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }

      const { data } = await this.getOssSign(mediaType, file.name)
      const ossCDN = data[0].ossConfig.ossCDN
      this.ossUrl = data[0].ossConfig.host
      this.progress = true
      const filename = file.name

      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)

      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })

      const fileObj = {
        mediaFile: {
          size: Math.floor(file.size / 1024),
          type: mediaType,
          fileName: filename.substring(0, filename.lastIndexOf('.')),
          url: data[0].fileName,
          expendType: filename.substring(filename.lastIndexOf('.') + 1)
        },
        mediaFileId: 0,
        ossUrl: ossCDN
      }

      this.resourcesUserList.push(fileObj)
      return Promise.reject()
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.task-class {
  width: 100%;
  height: 100%;
  padding: 10px;

  .head-box {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    position: relative;

    .t-left {
      width: calc(100% - 40px);
    }

    .eidt {
      width: 60px;
      height: 60px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: 24px;
      font-weight: 500;
      margin-left: 10px;
    }

    .close {
      font-size: 30px;
    }
  }

  .d-content {
    height: calc(100% - 40px - 60px);
    padding: 10px 0;

    .content-box {
      height: 100%;
      width: 100%;
      border-radius: 16px;
      border: 3px solid #FFF;
      background: rgba(255, 255, 255, 0.55);
      padding: 10px 15px;
      overflow-y: auto;
      @include scrollBar;
    }
  }

  .d-footer {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    position: relative;

    .task-files {
      .title {
        color: #000;
        font-size: 16px;
        font-weight: 500;
        margin-right: 10px;
      }

      .link {
        color: #2F80ED;
        font-size: 16px;
        text-decoration-line: underline;
        cursor: pointer;
      }
    }

    .task-submit {
      position: absolute;
      right: 10px;
      display: flex;
      align-items: center;

      .tips {
        font-size: 12px;
        color: #000;
        margin-right: 10px;
      }

      .link {
        color: #2F80ED;
        font-size: 16px;
        text-decoration-line: underline;
        cursor: pointer;
        margin-right: 10px;
      }

      .submit-files {
        padding: 5px 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        border: 1px solid #000;
        font-size: 16px;
        margin-right: 10px;
        cursor: pointer;
        color: #2F80ED;
        &:hover {
          border: 1px solid #2F80ED;
        }
      }

      .submit {
        padding: 5px 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: #2F80ED;
        border: 1px solid #2F80ED;
        font-size: 16px;
        color: #FFF;
        cursor: pointer;
      }
    }
  }

  .preview-box {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 100px;
    box-sizing: border-box;
    background: rgba($color: #000000, $alpha: .5);
    z-index: 9;

    .preview-content {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;
      box-sizing: border-box;
      overflow-y: auto;
      @include scrollBar;

      .preview-content-head {
        height: 40px;
        display: flex;
        justify-content: space-between;

        .title {
          font-size: 16px;
          font-weight: 500;
        }

        .el-icon-close {
          font-size: var(--font-size-XXL);
        }
      }

      .preview-content-content {
        height: calc(100% - 40px);
        overflow-y: auto;
      }
    }
  }

  .dig-task-box {
    .editor-content-view {
      width: 793.667px;
      margin: 0 auto;
      .title{
        color:#2F80ED;
        font-size: 20px;
        font-weight: 800;
      }
    }
  }

  .editor-content-view {

    ::v-deep img {
      max-width: 100%;
    }

    ::v-deep video {
      width: 100%;
    }

    ::v-deep p,
    ::v-deep li {
      white-space: pre-wrap;
      /* 保留空格 */
    }

    ::v-deep blockquote {
      border-left: 8px solid #d0e5f2;
      padding: 10px 10px;
      margin: 10px 0;
      background-color: #f1f1f1;
    }

    ::v-deep table {
      border-collapse: collapse;
    }

    ::v-deep th,
    ::v-deep td {
      border: 1px solid #ccc;
      min-width: 50px;
      height: 20px;
    }

    ::v-deep th {
      background-color: #f1f1f1;
    }

    ::v-deep pre {
      color: #cccccc;
      padding: 0;
    }
    ::v-deep pre>code {
      display: block;
      padding: 10px;
      white-space: pre-wrap;
      word-break: break-all;
    }

    ::v-deep pre>code {
      display: block;
      padding: 10px;
    }

    ::v-deep input[type="checkbox"] {
      margin-right: 5px;
    }

    ::v-deep ul,
    ::v-deep ol {
      padding-left: 20px;
    }
  }
}</style>
