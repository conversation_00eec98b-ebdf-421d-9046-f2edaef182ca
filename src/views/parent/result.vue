<template>
  <div class="result-layout">
    <div class="header">
      <svg-icon icon-class="arrow-round" class-name="arrow-round" @click="back" />
    </div>
    <div class="result-coutent">
      <el-empty v-if="list.length === 0" class="w h" description="暂无数据" />

      <div v-for="item in list" :key="item.id" class="r-c-box" @click="goTo(item)">
        <div class="date-box">
          <div class="flex items-center mb-10">
            <div class="d-date">{{ item.createdAt | formDay }}</div>
            <div class="m-date">{{ item.createdAt | formMouth }}</div>
          </div>
          <div class="y-date">{{ item.createdAt | formYear }}</div>
        </div>
        <div class="r-img-box">
          <img v-if="formImg(item)" class="r-img" :src="formImg(item)" alt="" />
          <img v-else class="r-img" :src="formImg(item)" alt="" />
        </div>
        <div>
          <div class="r-title">《{{ item && item.aicourse && item.aicourse.title }}》</div>
          <div class="r-desc">第{{ item && item.unit && item.unit.unitNo }}节：{{ item && item.unit && item.unit.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getWorkSubmitHistory } from '@/api/partent-api'
import localStore from '@/utils/local-storage.js'
import resultBg from '../../assets/parent/result-bg.svg'
import moment from 'moment'

export default {
  filters: {
    formYear (val) {
      return moment(val).format('YYYY年')
    },
    formMouth (val) {
      return moment(val).format('MM月')
    },
    formDay (val) {
      return moment(val).format('DD')
    }
  },
  data () {
    return {
      resultBg,
      currChild: '',
      list: []
    }
  },
  mounted () {
    const currChild = JSON.parse(localStore.read('currChild'))
    this.currChild = currChild
    this._getWorkSubmitHistory()
  },
  methods: {
    back () {
      this.$router.push({
        'path': '/parent/home'
      })
    },
    goTo (row) {
      const belongClassId = JSON.parse(localStore.read('currChild')).belongClassId ?? 0
      const path = `/parent/course/homework/${row.aicourse && row.aicourse.id}/${row.studentCourseId}/${row.unitId}/${row.sectionId}/${belongClassId}`
      localStore.save('homeworkback', '/parent/result')
      this.$router.push({ path })
    },
    async _getWorkSubmitHistory () {
      if (this.currChild) {
        const { data } = await getWorkSubmitHistory({
          childUserId: this.currChild.id
        })
        console.log(data)
        this.list = data
      }
    },
    formImg (val) {
      let src = resultBg
      if (val.resourceList && val.resourceList.length > 0) {
        if (val.resourceList[0].type === 'IMAGE') {
          src = val.resourceList[0].url
        } else if (val.resourceList[0].type === 'VIDEO') {
          src = `${val.resourceList[0].url}?x-oss-process=video/snapshot,t_2000,m_fast`
        }
      }
      return src
    }
  }
}
</script>

<style lang="scss" scoped>
.result-layout {
    width: 100%;
    height: 100%;
    background: linear-gradient(359.86deg, #56CCF2 0.08%, #2F80ED 98.53%);
    display: flex;
    flex-direction: column;
    padding: 0 12px;
    padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
    overflow: hidden;

    .arrow-round {
        width: 30px;
        height: 30px;
        margin: 26px 0 23px;
        object-fit: contain;
    }

    .result-coutent {
      border-radius: 8px;
      background: #FFF;
      padding: 20px 10px;
      height: calc(100% - 90px);
      overflow-y: auto;

      .r-c-box {
        padding: 10px 0;
        color: #000;
        font-size: 14px;
        border-bottom: 1px solid #E2E2E2;
        height: 120px;
        box-sizing: border-box;
        display: flex;

        &:last-child {
          border-bottom: none;
        }

        .date-box {
          width: 80px;
          flex-shrink: 0;
        }

        .mb-10 {
          margin-bottom: 10px;
        }

        .d-date {
          color: #000;
          font-size: 26px;
          font-weight: 500;
          margin-right: 8px;
        }
        .m-date {
          color: #000;
          font-size: 16px;
          font-weight: 500;
        }
        .y-date {
          color: #000;
          font-size: 16px;
        }

        .r-img-box {
          width: 110px;
          display: flex;
          justify-content: flex-start;
          flex-shrink: 0;
        }

        .r-img {
          width: 100px;
          height: 100px;
          object-fit: cover;
        }

        .r-title {
          color: #000;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 10px;
          @include ellipses(2);
        }
        .r-desc {
          color: #000;
          font-size: 14px;
          @include ellipses(3);
        }
      }
    }
}
</style>
