<template>
  <div class="w h flex justify-center items-center">
    登录中...
  </div>
</template>

<script>
import localStore from '@/utils/local-storage.js'
import { message } from '@/utils/singeMessage.js'

export default {
  data () {
    return {
      code: this.getQueryString('code'),
      state: this.getQueryString('state'),
      appid: 'wxfa4a06f2648b3c54'
    }
  },
  mounted () {
    this.check()
  },
  methods: {
    getQueryString (name) {
      if (this.$route.query && this.$route.query[name]) {
        return this.$route.query[name]
      }
      const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
      const result = window.location.search.substring(1).match(reg)
      if (result != null) {
        return decodeURIComponent(result[2])
      }
      return null
    },
    async check () {
      const code = this.code || this.getQueryString('code')
      if (!code) { return }
      try {
        await this.$store.dispatch('user/partentLogin', {
          mobileOrEmail: '',
          password: '',
          loginType: 'GZH_WITHOUT_MOBILE',
          code
        })
        const { data } = await this.$store.dispatch('user/GetInfo', 'PARENT')
        localStore.save('currChild', JSON.stringify(data))
        const goto = localStore.read('acticegoto')
        localStore.clear('acticegoto')
        this.$router.push({ path: goto })
      } catch (error) {
        console.log(error)
        const isActive = location.href.indexOf('parent') === -1
        if (error.code === 602) {
          if (isActive) {
            this.$router.push({ path: '/h5/login', query: { code }})
          } else {
            this.$router.push({ path: '/parent/login', query: { code }})
          }
        } else {
          message({
            message: '登录失败',
            type: 'error',
            duration: 3 * 1000
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
