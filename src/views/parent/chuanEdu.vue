<template>
  <div class="main">
    <!-- <img class="logo" src="../../assets/images/parent/edu/logo.png" alt="" /> -->
    <div v-for="(item,index) in couresList" :key="index" class="course_list" @click="toDetail(item)">
      <img class="cover" :src="item.coverUrl?item.coverUrl:DefaultCover" alt="" />
      <div class="info">{{ item.title }}</div>
    </div>
  </div>
</template>
<script>
import { getAiCourseListCjt } from '@/api/course-api.js'
import DefaultCover from '@/assets/images/default-cover.jpg'
export default {
  data () {
    return {
      couresList: [],
      DefaultCover
    }
  },
  mounted() {
    getAiCourseListCjt().then(res => {
      this.couresList = res.data
    })
  },
  methods: {
    toDetail(item) {
      this.$router.push(`/chuanEdu/detail?couresId=${item.id}&title=${item.title}`)
    }
  }
}

</script>

<style lang="scss" scoped>
.main{
    width: 100%;
    height: 100%;
    background: #f9f8f8;
    overflow-x: hidden;
    padding: 10px;
    .logo{
        width: 180px;
    }
    .course_list{
        width: 100%;
        height: 296px;
        border: 1px solid #E0E0E0;
        border-radius: 10px;
        background: #ffffff;
        margin-top: 10px;
        .cover{
            width: 100%;
            height:234px;
            border-radius: 10px;
            object-fit: cover;
        }
        .info{
            width: 100%;
            height: 50px;
            line-height: 50px;
            padding-left: 10px;
            padding-right: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>

