<template>
  <div class="parent-content">
    <div>
      <div class="flex justify-center items-center f18">
        <img v-if="!isActive" class="logo" src="../../assets/parent/logo.png" alt="" />
        <!-- 家长端 -->
      </div>
      <div class="mt40 login-tips">登录&注册</div>
      <div class="p-login-input">
        <van-field
          v-model="phone"
          type="number"
          clearable
          placeholder="输入手机号"
        />
      </div>
      <div class="p-login-input">
        <van-field
          v-model="sms"
          center
          clearable
          placeholder="输入验证码"
        >
          <template #button>
            <div class="sms" @click="getCode">
              {{
                smsDisabled
                  ? countdown > 0
                    ? countdown + 's后重新获取'
                    : '获取验证码'
                  : '获取验证码'
              }}
            </div>
          </template>
        </van-field>
      </div>
      <div class="p-btn mt60" @click="login">
        登录注册
      </div>
    </div>
  </div>
</template>

<script>
import { validMobile } from '@/utils/validate'
import { verifyCodeForWeb } from '@/api/user-api'
import { getParentChildren, getGzhUserInfo } from '@/api/partent-api'
import localStore from '@/utils/local-storage.js'
export default {
  data () {
    return {
      phone: '',
      sms: '',
      smsDisabled: false,
      countdown: 0,
      isActive: false,
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  mounted () {
    this.isActive = location.href.indexOf('parent') === -1
    this.$bus.$on('popWechatClose', () => {
      this.checkGoPath()
    })
  },
  beforeDestroy () {
    this.$bus.$off('popWechatClose')
  },
  methods: {
    async login () {
      try {
        await this.$store.dispatch('user/partentBindLogin', {
          mobileOrEmail: this.phone,
          smsCode: this.sms,
          userType: 'PARENT',
          loginType: 'WECHAT_GZH',
          authCode: this.$route.query && this.$route.query.code
        })
        const { data } = await getGzhUserInfo()
        if (!data.subscribe) {
          this.$bus.$emit('popWechatOpen')
        } else {
          await this.checkGoPath()
        }
      } catch (error) {
        console.log('bind', error)
      }
    },
    async checkGoPath () {
      const isActive = location.href.indexOf('parent') === -1
      const { data } = await getParentChildren()
      if (data.length === 0) {
        if (isActive) {
          this.$router.push({ path: '/h5/index' })
        } else {
          const scanPath = localStore.read('scanPath')
          if (scanPath) {
            localStore.clear('scanPath')
            this.$router.push({ path: scanPath })
          } else {
            this.$router.push({ path: '/parent/home' })
          }
        }
      } else if (data.length > 1) {
        // 多个学生
        const currChild = localStore.read('currChild')
        if (isActive) {
          if (currChild) {
            const curr = JSON.parse(currChild)
            let flag = false
            data.forEach((val) => {
              if (val.id + '' === curr.id + '') {
                flag = true
              }
            })
            if (flag) {
              this.$router.push({ path: '/h5/index' })
              return
            }
          }
          localStore.save('currChild', JSON.stringify(data[0]))
          this.$router.push({ path: '/h5/index' })
        } else {
          const scanPath = localStore.read('scanPath')
          if (scanPath && scanPath.indexOf('/parent/hassClassInfo') > -1) {
            localStore.clear('scanPath')
            this.$router.push({ path: scanPath })
            return
          }
          if (currChild) {
            const curr = JSON.parse(currChild)
            let flag = false
            data.forEach((val) => {
              if (val.id + '' === curr.id + '') {
                flag = true
              }
            })
            if (flag) {
              if (scanPath) {
                localStore.clear('scanPath')
                this.$router.push({ path: scanPath })
              } else {
                this.$router.push({ path: '/parent/home' })
              }
              return
            }
          }
          // 默认选择第一个学生并去主页
          localStore.save('currChild', JSON.stringify(data[0]))
          this.$router.push({ path: '/parent/home' })
        }
      } else {
        // 只有一个学生
        localStore.save('currChild', JSON.stringify(data[0]))
        if (isActive) {
          this.$router.push({ path: '/h5/index' })
        } else {
          const scanPath = localStore.read('scanPath')
          if (scanPath) {
            localStore.clear('scanPath')
            this.$router.push({ path: scanPath })
          } else {
            this.$router.push({ path: '/parent/home' })
          }
        }
        // this.$router.push({ path: this.redirect || '/parent/home' })
      }
    },
    getCode () {
      if (this.smsDisabled) return
      const mobile = this.phone
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this._verifyCodeForWeb()
    },
    _verifyCodeForWeb () {
      verifyCodeForWeb({ mobile: this.phone }).then(res => {
        this.countdown = 60
        this.smsDisabled = true
        setTimeout(this.tick, 1000)
      })
    },
    tick () {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .parent-content {
    width: 100%;
    min-height: 100%;
    font-size: 14px;
    display: flex;
    justify-content: center;
    padding-top: 200px;
    background: #fff;
     //background: linear-gradient(359.86deg, #56CCF2 0.08%, #2F80ED 98.53%);
    color: #000000;

    .logo {
      height: 70px;
    }
    .f18 {
      font-size: 18px;
    }
    .login-tips {
      margin-bottom: 20px;
      color: #000000;
      font-weight: 400;
      font-size: 19px;
    }
    .mt40 {
      margin-top: 40px;
    }
    .mt60 {
      margin-top: 60px;
    }
    .p-login-input {
      border: 1px solid #E0E0E0;
      width: 380px;
      margin-bottom: 30px;
      border-radius: 5px;
      overflow: hidden;

      ::placeholder {
        color: #828282;
        font-size: 12px;
      }

      .sms {
        color: #000000;
        font-size: 12px;
      }
    }

    .p-btn {
      width: 380px;
      height: 50px;
      background: #2F80ED;
      border-radius: 8px;
      color: #FFFFFF;
      font-size: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      cursor: pointer;
    }
  }
</style>
