<template>
  <div class="report">
    <div class="flex items-center justify-between mb10">
      <div class="course-title">{{ unit && `第${aicourseUnit.unitNo || ''}节 ${aicourseUnit.title || ''}` || '&nbsp;' }}</div>
      <div class="attend-btn" @click="attendCourse">
        <svg-icon icon-class="course-play" class-name="icon" />
        <span>课程学习</span>
      </div>
    </div>
    <!-- 不论学没学 都显示课程报告 2023.8.14 -->
    <!-- <template v-if="unitFinished"> -->
    <template>
      <!-- <div class="course-content performance-box">
        <div class="report-title">课堂表现</div>
        <div class="flex justify-between">
          <div class="box">
            <div class="number-orange">{{ myPerform && myPerform.score || 0 }}</div>
            <span>缤果币</span>
          </div>
          <div class="box">
            <div class="number-orange">{{ wallet && wallet.userLevel.aiScore || 0 }}</div>
            <span>累计获得缤果币</span>
          </div>
          <div class="box">
            <div class="number-blue">{{ myPerform && myPerform.userAnswerTimes || 0 }}</div>
            <span>参与答题次数</span>
          </div>
          <div class="box">
            <div class="number-green">{{ myPerform && myPerform.totalCorrect || 0 }}%</div>
            <span>平均答题正确率</span>
          </div>
        </div>
      </div>
      <div v-if="myPerform && myPerform.challengeStatus !== 'NONE'" class="course-content challenge-box">
        <div class="report-title">挑战成绩</div>
        <div class="challenge-rank">
          超过了
          <font
            :class="{
              'challenge-green': cupStyle === 1,
              'challenge-purple': cupStyle === 2,
              'challenge-blue': cupStyle === 3
            }"
          >
            {{ myPerform && myPerform.challengeResult || 0 }}%&nbsp;
          </font>
          的班级
        </div>
        <img :src="cupStyle === 1 ? cupGreenShadow : cupStyle === 2 ? cupYellowShadow : cupBlueShadow" />
      </div> -->
      <div class="course-content obtain-box">
        <div class="report-title">课堂收获</div>
        <div class="obtain-content">
          <template v-if="harvestList.length > 0">
            <p
              v-for="(item, index) in harvestList"
              :key="index"
              :style="item.charAt(0) === '#'? 'font-weight: bold;' : ''"
            >
              {{ item.charAt(0) === '#' ? item.substring(1) : item || '' }}
            </p>
          </template>
          <template v-else><p>暂无数据</p></template>
        </div>
      </div>
    </template>
    <!-- 不论学没学 都显示课程报告 2023.8.14 -->
    <!-- <div v-else class="no-report-content">
      <svg-icon icon-class="no-report" class-name="no-report" />
      <span>课程未完成，暂无数据</span>
    </div> -->
    <div class="flex justify-center gap34">
      <div v-if="preview.length > 0" class="btn orange" @click="moveTo(0)">前置课程</div>
      <div v-if="learning_achieved.length > 0" class="btn green" @click="moveTo(1)">课中成果</div>
      <div v-if="after_achieved.length > 0" class="btn purple" @click="moveTo(2)">课后成果</div>
    </div>
  </div>
</template>

<script>
import localStore from '@/utils/local-storage.js'
import cupGreenShadow from '@/assets/images/parent/report-cup-green-shadow.png'
import cupYellowShadow from '@/assets/images/parent/report-cup-yellow-shadow.png'
import cupBlueShadow from '@/assets/images/parent/report-cup-blue-shadow.png'
import { getAicourseUnitUser, getWallet, getAiCourseUnitSectionList } from '@/api/course-api'
export default {
  data () {
    return {
      cupGreenShadow,
      cupYellowShadow,
      cupBlueShadow,
      aicourseId: this.$route.params.aicourseId,
      studentCourseId: this.$route.params.studentCourseId,
      unitId: this.$route.params.unitId,
      unit: undefined,
      aicourseUnit: undefined,
      myPerform: undefined,
      wallet: undefined,
      harvestList: [],
      preview: [],
      learning_achieved: [],
      after_achieved: []
    }
  },
  computed: {
    cupStyle () {
      if (!this.myPerform) return 3
      if (+this.myPerform.challengeResult > 80) return 1
      if (+this.myPerform.challengeResult > 50 && +this.myPerform.challengeResult < 81) return 2
      return 3
    },
    // 单元是否完成
    unitFinished () {
      return (
        this.unit &&
        this.unit.complete &&
        this.unit.total &&
        this.unit.complete >= this.unit.total &&
        (this.unit.challengeStatus === 'NONE' ||
        this.unit.challengeStatus === 'END')
      )
    }
  },
  created () {
    this._getAicourseUnitUser()
    this._getWallet()
    this._getAiCourseUnitSectionList()
  },
  methods: {
    moveTo (type) {
      let sectionId
      if (type === 0) {
        sectionId = this.preview[0].id
      } else if (type === 1) {
        sectionId = this.learning_achieved[0].id
      } else if (type === 2) {
        sectionId = this.after_achieved[0].id
      }
      const belongClassId = JSON.parse(localStore.read('currChild')).belongClassId ?? 0
      const path = `/parent/course/homework/${this.aicourseId}/${this.studentCourseId}/${this.unitId}/${sectionId}/${belongClassId}`
      this.$emit('moveTo', path)
    },
    _getAicourseUnitUser () {
      const params = {
        studentCourseId: this.studentCourseId,
        aicourseUnitId: this.unitId
      }
      getAicourseUnitUser(params).then(
        response => {
          this.unit = response.data
          this.aicourseUnit = response.data.aicourseUnit
          this.myPerform = response.data
          if (this.myPerform.aicourseUnit && this.myPerform.aicourseUnit.gain) {
            this.harvestList = this.myPerform.aicourseUnit.gain.split('\n')
          }
        }
      )
    },
    _getWallet () {
      const belongClassId = JSON.parse(localStore.read('currChild')).belongClassId
      const params = {
        'objectUserId': belongClassId
      }
      getWallet(params).then(
        response => {
          this.wallet = response.data
        }
      )
    },
    _getAiCourseUnitSectionList () {
      const params = {
        aicourseUnitId: this.unitId,
        studentCourseId: this.studentCourseId
      }
      getAiCourseUnitSectionList(params).then(
        response => {
          const sectionList = response.data
          this.preview = sectionList.filter(item => item.aiSectionType === 'PREVIEW')
          this.learning_achieved = sectionList.filter(item => item.aiSectionType === 'LEARNING_ACHIEVED')
          this.after_achieved = sectionList.filter(item => item.aiSectionType === 'AFTER_ACHIEVED')
        }
      )
    },
    attendCourse () {
      const aicourseId = this.aicourseId
      const studentCourseId = this.studentCourseId
      const unitId = this.unitId
      window.location.href = `${process.env.VUE_APP_AI_URL}ai/${aicourseId}/${studentCourseId}/${unitId}?preview=1`
    }
  }
}
</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}

.report {
    display: flex;
    flex-direction: column;
    padding-bottom: 22px;

    .course-title {
      font-size: 18px;
      flex: 1;
    }

    .attend-btn {
      width: 102px;
      height: 40px;
      background: #F2C94C;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      font-size: 14px;
      color: #333333;

      .icon {
        width: 20px;
        height: 20px;
      }
    }

    .performance-box {
        padding: 14px 18px 6px;
        margin-bottom: 6px;

        .report-title {
            margin-bottom: 10px;
        }

        .box {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .number-orange,
        .number-blue,
        .number-green {
            font-family: 'Lantinghei SC';
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 26px;
            text-shadow: 0px 0.842884px 2.52865px rgba(0, 0, 0, 0.25);
            margin-bottom: 10px;
        }

        .number-orange {
            color: #F2994A;
        }

        .number-blue {
            color: #2D9CDB;
        }

        .number-green {
            color: #2FC56E;
        }

        span {
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 500;
            font-size: 14px;
            line-height: 19px;
        }
    }

    .challenge-box {
        padding: 8px 16px;
        display: flex;
        align-items: center;
        margin-bottom: 6px;

        .report-title {
            margin-right: 20px;
        }

        .challenge-rank {
            display: flex;
            align-items: center;
            color: #000000;
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 300;
            font-size: 14px;
            line-height: 19px;
        }

        .challenge-green,
        .challenge-pueple,
        .challenge-blue {
            font-size: 25px;
            margin: 0 6px;
            font-family: 'Lantinghei SC';
            text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
            font-weight: 500;
            text-shadow: 0px 3.37153px 3.37153px rgba(0, 0, 0, 0.25);
        }

        .challenge-green {
            color: #DFF741;
        }

        .challenge-purple {
            color: #D05FCE;
        }

        .challenge-blue {
            color: #74B9FF;
        }

        img {
            width: 60px;
            height: 55px;
            object-fit: contain;
            margin-left: 20px;
        }
    }

    .obtain-box {
        padding: 18px 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: scroll;
        margin-bottom: 27px;

        .report-title {
          height: 30px;
          padding: 0 18px;
        }

        .obtain-content {
            overflow: scroll;
            overflow-x: hidden;
            height: calc(100% - 30px);
            padding: 0 18px;

            p {
              font-family: 'PingFang SC';
              font-style: normal;
              font-weight: 500;
              font-size: 14px;
              line-height: 20px;
              color: #000000;
            }
        }
    }

    .no-report-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 110px 0 90px;
        .no-report {
            width: 120px;
            height: 120px;
            object-fit: contain;
            margin-bottom: 6px;
        }

        span {
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            color: rgba(255, 255, 255, 0.7);
        }
    }
}

.report-title {
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 500;
    font-size: 22px;
    line-height: 31px;
    color: #333333;
}

.btn {
    width: 110px;
    height: 44px;
    border-radius: 8px;
    text-align: center;
    line-height: 44px;
    font-size: 14px;
    font-weight: 500;
}

.gap34 {
  gap: 34px
}

.orange {
    background: #F2994A;
    color: #753700;
}

.green {
    background: #4BE98D;
    color: #106132;
}

.purple {
    background: #BB6BD9;
    color: #4B2A57;
}
</style>
