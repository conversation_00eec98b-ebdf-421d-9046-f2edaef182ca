<template>
  <div class="homework flex flex-col">
    <template v-if="sectionInfo">
      <div class="flex items-center">
        <div class="course-title">{{ sectionInfo && sectionTypeJson[sectionInfo.aiSectionType] || '  ' }}</div>
        <div class="course-subtitle">{{ !userWorks || userWorks.reviewStatus === 'UNDER_REVIEW' ? '未评价' : '已评价' }}</div>
        <div class="flex items-center" style="margin-left: auto" @click="openPop">
          <div class="student">{{ (token && childInfo) && childInfo.displayName || '选择学生' }}</div>
          <svg-icon icon-class="arrow-round" class-name="arrow-student" />
        </div>
      </div>

      <div class="content">

        <div
          ref="homeworkInfo"
          class="course-content homework-info"
          :class="{'h250': canExpanded && !isExpanded}"
        >
          <div class="show" :class="{'h230': canExpanded && !isExpanded}">
            <div ref="homeworkAdvice">{{ sectionInfo.advice || '' }}</div>
            <div ref="aiWorkImgList">
              <div v-for="(img, index) in aiWorkImgList" :key="img.id">
                <img :src="img.mediaFile.url" alt="" @click="showPreview(0,index)" />
              </div>
            </div>
          </div>
          <div v-if="canExpanded" class="expand-text">
            <span @click="isExpanded = !isExpanded">{{ isExpanded ? '收起' : '展开' }}</span>
            <div class="double-arrow" :class="{'reverse': isExpanded}" @click="isExpanded = !isExpanded">
              <svg-icon
                icon-class="double-arrow"
                class-name="svg"
              />
            </div>
          </div>
        </div>

        <template v-if="!userWorks || userWorks.reviewStatus === 'UNDER_REVIEW'">
          <div class="course-content submit-container">
            <el-input
              v-model="input"
              type="textarea"
              placeholder="文字描述（非必填）"
              maxlength="100"
              :autosize="{ minRows: 3, maxRows: 5}"
              show-word-limit
              @focus="beforeClick"
            />

            <div class="file-container">
              <!-- 图片展示 -->
              <div v-for="(file, index) in fileList" :key="`${file.name}-${index}`" class="file-box">
                <div v-if="file.status" class="shadow"></div>
                <div v-if="!file.status && file.type === 'VIDEO'" class="video-shadow" @click="showUploadPreview(1, file.url)">
                  <svg-icon
                    icon-class="video-player"
                    class-name="video-player"
                  />
                </div>
                <img
                  v-if="file.type === 'VIDEO'"
                  :key="file.image"
                  :src="file.image"
                />
                <img
                  v-else
                  :src="file.content || file.url"
                  :alt="file.file.name"
                  @click="showUploadPreview(0, file.url)"
                />
                <!-- 进度条 -->
                <van-circle
                  v-if="file.status === 'uploading'"
                  v-model="file.uploadProgress"
                  :text="`${file.uploadProgress}%`"
                  stroke-width="160"
                  size="40"
                  color="#FFFFFF"
                  fill="rgba(0, 0, 0, 0)"
                  layer-color="rgba(0, 0, 0, 0)"
                  stroke-linecap="square"
                />
                <div v-if="file.status === 'failed'" class="close-container">
                  <van-icon name="close" color="#ffffff" size="30" />
                  <span>上传失败</span>
                </div>
                <svg-icon
                  icon-class="homework-delete"
                  class-name="homework-delete"
                  @click.stop="deleteImage(index)"
                />
              </div>
              <div v-if="fileList.length < 9" class="van-uploader">
                <div class="van-uploader__wrapper">
                  <div class="homework-add-box" @click="chooseFile">
                    <svg-icon icon-class="homework-add" class-name="homework-add" />
                  </div>
                </div>
              </div>
            </div>
            <div class="hint">图片/视频共可传9个，视频不超过5分钟</div>
          </div>
        </template>
        <template v-else>
          <div class="course-content submit-container-2">
            <div v-if="userWorks.introduce" class="input">{{ userWorks.introduce }}</div>
            <div class="img-list">
              <div v-for="(item, index) in userWorks.resourceList" :key="item.id" class="img-wrap">
                <!-- <img :src="item.url" :alt="item.fileName" @click="showPreview(1, index)" /> -->
                <div v-if="item.type === 'VIDEO'" class="video-shadow" @click="showUploadPreview(1, item.url)">
                  <svg-icon
                    icon-class="video-player"
                    class-name="video-player"
                  />
                </div>
                <img
                  v-if="item.type === 'VIDEO'"
                  :key="item.url + index"
                  :src="item.url +'?x-oss-process=video/snapshot,t_1000,m_fast'"
                />
                <img
                  v-else
                  :src="item.url"
                  :alt="item.fileName"
                  @click="showUploadPreview(0, item.url)"
                />
              </div>
            </div>
          </div>

          <div class="course-content evaluation-container">
            <!-- <div class="flex items-center">
            <div class="evaluation">评价得分：</div>
            <div v-if="standardImg.length > 0" class="flex items-center" @click="showCriteria = true">
              <div class="criteria">评分标准</div>
              <svg-icon icon-class="sigh" class-name="sigh" />
            </div>
          </div> -->
            <div class="evaluation">评价得分：</div>
            <div class="score">{{ !userWorks || userWorks.reviewStatus === 'UNDER_REVIEW' ? `暂未评分` : `${userWorks.score}分` }}</div>
          </div>

          <van-action-sheet v-model="showCriteria">
            <div class="sheet-header">
              <div class="title">评分标准</div>
              <svg-icon icon-class="homework-close" class-name="homework-close" @click="showCriteria = false" />
            </div>
            <div class="sheet-content">
              <img :src="standardImg[0].mediaFile.url" alt="" />
            </div>
          </van-action-sheet>
        </template>

      </div>

      <div v-if="!userWorks || userWorks.reviewStatus === 'UNDER_REVIEW'" class="submit-btn" @click="_submitAiCourseWorks">
        <van-loading v-if="uploading" type="spinner" color="#753700" size="14px" />
        保存并提交
      </div>
    </template>

    <!-- 上传按钮 -->
    <van-uploader
      ref="uploader"
      v-model="fileList"
      max-count="9"
      :accept="fileAccept"
      :multiple="multiple"
      :preview-image="false"
      :before-read="beforeUpload"
      :after-read="uploadFile"
      style="display: none"
    />
    <van-popup
      v-model="showFilePop"
      position="bottom"
      :round="true"
      :style="{ width: '100%', height: '30%' }"
    >
      <div class="file-pop">
        <div class="select" @click="chooseImage">图片</div>
        <div class="line1"></div>
        <div class="select" @click="chooseVideo">视频</div>
        <div class="line2"></div>
        <div class="select" @click="closeFilePop">取消</div>
      </div>
    </van-popup>

    <pop-change ref="changePop" @selectCurr="selectCurr" />

    <div v-if="showVideoPreview" class="video-preview">
      <video-js :options="videoOptions" />
      <svg-icon
        icon-class="homework-delete"
        class-name="video-close"
        @click.stop="closeVideoPreview"
      />
    </div>
  </div>
</template>

<script>
import PopChange from '../components/PopChange.vue'
import VideoJs from '@/components/classPro/video'
import localStore from '@/utils/local-storage.js'
import axios from 'axios'
import { getFileUploadAuthor } from '@/api/user-api'
import { getAiCourseUnitSection, getUserAiCourseWorks, submitAiCourseWorks } from '@/api/course-api'
import { ImagePreview } from 'vant'
import { getPartentToken } from '@/utils/auth'
export default {
  components: { PopChange, VideoJs },
  data () {
    return {
      rate: '70',
      input: '',
      fileList: [],
      showCriteria: false,
      aicourseId: this.$route.params.aicourseId,
      studentCourseId: this.$route.params.studentCourseId,
      unitId: this.$route.params.unitId,
      sectionId: this.$route.params.sectionId,
      classId: this.$route.params.classId,
      isScan: this.$route.query.isScan,
      token: undefined,
      sectionInfo: undefined,
      sectionTypeJson: {
        'PREVIEW': '前置课程',
        'LEARNING_ACHIEVED': '课中成果',
        'AFTER_ACHIEVED': '课后成果'
      },
      aiWorkImgList: [],
      standardImg: [],
      userWorks: undefined,
      uploading: false,
      childInfo: undefined,
      showVideoPreview: false,
      showFilePop: false,
      multiple: false,
      fileAccept: '',
      canExpanded: true,
      isExpanded: false,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false, //  x5内核浏览器不允许自动播放，需要手动播放
        fileShow: false,
        loop: false,
        fluid: false,
        language: 'zh-CN',
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    }
  },
  created () {
    this.loadJs('http://res.wx.qq.com/open/js/jweixin-1.0.0.js')
    this.childInfo = JSON.parse(localStore.read('currChild'))
    this._getAiCourseUnitSection()
    this._getUserAiCourseWorks()
  },
  methods: {
    _getAiCourseUnitSection () {
      const params = {
        'studentCourseId': this.studentCourseId,
        'aicourseUnitSectionId': this.sectionId
      }
      getAiCourseUnitSection(params).then(
        response => {
          this.sectionInfo = response.data
          this.aiWorkImgList = response.data.resourceList.filter(item => item.linkType === 'AI_WORK_IMG')
          this.standardImg = response.data.resourceList.filter(item => item.linkType === 'AI_WORK_SCORE_STANDARD')
          this.$nextTick(() => {
            const adviceHeight = +(getComputedStyle(this.$refs.homeworkAdvice).height.replace('px', ''))
            // const imgHeight = +(getComputedStyle(this.$refs.aiWorkImgList).height.replace('px', ''))
            const imgHeight = this.aiWorkImgList.length * 150
            this.canExpanded = adviceHeight + imgHeight > 250
          })
        }
      )
    },
    async _getUserAiCourseWorks () {
      this.token = await getPartentToken()
      if (!this.token) return
      const params = {
        'childId': this.childInfo.id,
        'studentCourseId': this.studentCourseId,
        'unitSectionId': this.sectionId
      }
      getUserAiCourseWorks(params).then(
        async response => {
          this.userWorks = response.data
          this.input = ''
          this.fileList = []
          let newFlist = []
          if (this.userWorks) {
            this.input = this.userWorks.introduce || ''
            for (let i = 0; i < this.userWorks.resourceList.length; i++) {
              const resource = this.userWorks.resourceList[i]
              let splitLabel = ''
              if (resource.url && resource.url.indexOf('cn/') > -1) {
                splitLabel = 'cn/'
              } else if (resource.url && resource.url.indexOf('com/') > -1) {
                splitLabel = 'com/'
              }
              const postUrl = resource.url && resource.url.split(splitLabel)[1] || ''
              const file = {
                url: resource.url,
                postUrl: postUrl,
                expendType: resource.expendType,
                type: resource.type,
                file: {
                  'name': resource.fileName
                }
              }
              if (file.type === 'VIDEO') file.image = `${file.url}?x-oss-process=video/snapshot,t_1000,m_fast`
              newFlist = [...newFlist, file]
            }
            this.fileList = newFlist
          }
        }
      )
    },
    async chooseFile () {
      const before = await this.beforeClick()
      if (before) {
        this.showFilePop = true
      }
    },
    closeFilePop () {
      this.showFilePop = false
    },
    chooseImage () {
      this.multiple = true
      this.fileAccept = 'image/*'
      this.$nextTick(() => {
        this.showFilePop = false
        this.$refs.uploader.chooseFile()
      })
    },
    chooseVideo () {
      this.multiple = false
      this.fileAccept = 'video/*'
      this.$nextTick(() => {
        this.showFilePop = false
        this.$refs.uploader.chooseFile()
      })
    },
    async beforeUpload (files) {
      return new Promise((resolve, reject) => {
        if (files.length && files.length > 0) {
          if (files.length + this.fileList.length > 9) {
            this.$toast('图片/视频共可传9个')
            reject()
          }
          resolve(files)
        } else {
          if (files.type.indexOf('video') > -1) {
            const url = URL.createObjectURL(files)
            const video = new Audio(url)
            // //  让移动端视频开始缓冲
            if (window.WeixinJSBridge) {
              window.WeixinJSBridge.invoke('getNetworkType', {}, () => {
                video.play()
                video.pause()
              }, false)
            } else {
              video.play()
              video.pause()
            }
            video.addEventListener('loadedmetadata', () => {
              const time = Math.round(video.duration * 100) / 100
              if (time > 300) {
                this.$toast('上传的视频不能超过5分钟')
                reject()
              } else {
                resolve(files)
              }
            })
          } else {
            resolve(files)
          }
        }
      })
    },
    uploadFile (files) {
      const currentIndex = this.fileList.length - 1
      if (files.length && files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          this.getFile(files[i], currentIndex + i)
        }
      } else {
        this.getFile(files, currentIndex)
      }
    },
    async getFile (files, index) {
      files.status = 'uploading'
      files.type = files.file.type.indexOf('image') > -1 ? 'IMAGE' : 'VIDEO'
      files.uploadProgress = 0
      const file = files.file
      const mediaType = files.type
      const { data } = await this.getOssSign(mediaType, file.name)
      const postData = data[0]
      const url = postData.ossConfig.host
      const formData = new FormData()
      formData.append('key', postData.fileName)
      formData.append('policy', postData.policy)
      formData.append('OSSAccessKeyId', postData.ossConfig.accessKeyId)
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('signature', postData.signature)
      formData.append('file', file)
      try {
        await axios({
          url: url,
          method: 'post',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' },
          cancelToken: new axios.CancelToken((c) => {
            files.cancelToken = c
          }),
          onUploadProgress: (progressEvent) => {
            // progressEvent.loaded:已上传文件大小
            // progressEvent.total:被上传文件的总大小
            const uploadProgress = Number(
              ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)
            )
            files.uploadProgress = uploadProgress
            this.fileList = this.fileList.reverse().reverse()
          }
        })
        const imgFullUrl = `${postData.ossConfig.ossCDN}/${postData.fileName}`
        const imgUrl = `${postData.fileName}`
        files.url = imgFullUrl
        files.postUrl = imgUrl
        if (files.type === 'VIDEO') files.image = `${files.url}?x-oss-process=video/snapshot,t_1000,m_fast`
        files.status = ''
      } catch (error) {
        console.log(error)
        files.status = 'failed'
      }
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    deleteImage (index) {
      if (this.fileList[index].cancelToken) this.fileList[index].cancelToken()
      let newFileList = []
      for (let i = 0; i < this.fileList.length; i++) {
        const newFile = this.fileList[i]
        if (index !== i) {
          newFileList = [...newFileList, newFile]
        }
      }
      this.fileList = newFileList
    },
    // 提交作业
    async _submitAiCourseWorks () {
      const before = await this.beforeClick()
      if (!before) {
        return
      }
      if (this.fileList.length < 1) {
        this.$toast('请上传至少一张图片')
        return
      }
      const formFileList = []
      for (const file of this.fileList) {
        if (file.status) {
          this.$toast('图片/视频未上传完毕')
          return
        }
        formFileList.push({
          'url': file.postUrl,
          'type': file.type,
          'fileName': file.file.name,
          'expendType': file.expendType || file.postUrl.substring(file.url.lastIndexOf('.') + 1)
        })
      }
      this.uploading = true
      const data = {
        'introduce': this.input,
        'userId': this.childInfo.id,
        'sectionId': +this.sectionId,
        'studentCourseId': +this.studentCourseId,
        'resourceList': formFileList
      }
      submitAiCourseWorks(data).then(
        response => {
          this.$toast('提交成功')
          const path = `/parent/course/report/${this.aicourseId}/${this.studentCourseId}/${this.unitId}`
          this.$emit('moveTo', path)
        },
        error => {
          console.log(error)
          this.uploading = false
        }
      )
    },
    showPreview (type, index) {
      const imgs = []
      const showImgs = type === 0 ? this.aiWorkImgList : this.userWorks.resourceList
      for (const img of showImgs) {
        imgs.push(type === 0 ? img.mediaFile.url : img.url)
      }
      ImagePreview({
        images: imgs,
        startPosition: index,
        beforeClose: () => false
      })
    },
    showUploadPreview (type, url) {
      if (type === 0) {
        ImagePreview({
          images: [url],
          beforeClose: () => false,
          showIndex: false
        })
      } else {
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: url,
            type: 'video/mp4'
          }]
        }
        this.showVideoPreview = true
      }
    },
    async openPop () {
      if (!this.token) {
        localStore.save('scanPath', this.$route.path)
        this.$router.push({
          'path': '/parent/checkLogin'
        })
        return false
      }
      this.$refs.changePop.openPop()
    },
    selectCurr (item) {
      // if (+this.classId === 0) {
      //   this.$toast.fail('当前课程不允许切换学生')
      //   return
      // }
      // if (+this.classId !== +item.belongClassId) {
      //   this.$toast.fail('班级不匹配，请重新选择')
      //   return
      // }
      this.childInfo = item
      this.$refs.changePop.closePop()
      this._getUserAiCourseWorks()
    },
    beforeClick () {
      if (!this.token) {
        localStore.save('scanPath', this.$route.path)
        this.$router.push({
          'path': '/parent/checkLogin'
        })
        return false
      }
      if (!this.childInfo) {
        this.openPop()
        return false
      }
      // if (+this.classId !== 0 && +this.classId !== +this.childInfo.belongClassId) {
      //   this.$toast('班级不匹配，请重新选择')
      //   return false
      // }
      return true
    },
    closeVideoPreview () {
      this.showVideoPreview = false
    },
    loadJs (src) {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.type = 'text/javascript'
        script.src = src
        document.body.appendChild(script)

        script.onload = () => {
          resolve()
        }
        script.onerror = () => {
          reject()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.homework {
  .course-title {
      margin-bottom: 6px;
      font-size: 16px;
  }

  .course-subtitle {
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
      color: #FFFFFF;
      margin-left: 17px;
  }

  .student {
    color: #FFFFFF;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
  }

  .arrow-student {
    width: 12px;
    height: 12px;
    object-fit: contain;
    transform: rotate(180deg)
  }

  .content {
    width: 100%;
    flex: 1;
    overflow: scroll;
    display: flex;
    flex-direction: column;
    @include noScrollBar;
  }

  .homework-info {
      padding: 18px;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      word-break: break-all;
      white-space: pre-line;
      margin-bottom: 10px;
      display: flex;
      flex-direction: column;
      // min-height: 190PX;

      img {
        max-width: 100%;
        object-fit: contain;
        max-height: 145PX;
        margin-top: 5PX;
      }

      .show {
        flex: 1
      }

      .expand-text {
        font-family: 'PingFang SC';
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #4F4F4F;
        margin-top: 22PX;
        display: flex;
        justify-content: flex-end;
        align-items: flex-start;
        line-height: 15px;

        .double-arrow {
          margin-left: 5px;
          width: 15px;
          height: 15px;

          .svg {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .reverse {
          transform: rotate(180deg)
        }
      }
  }

  .h250 {
    min-height: 190PX;
    overflow: hidden;
  }

  .h230 {
    height: 230PX;
    overflow: hidden;
  }

  .submit-container {
      padding: 14px;
      margin-bottom: 30px;
      display: flex;
      flex-direction: column;
      flex: 1;

      .homework-add-box {
          width: 100%;
          height: 120px;
          background: #FFFFFF;
          border: 1px solid #BDBDBD;
          position: relative;
      }

      .file-container {
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        row-gap: 6px;
        column-gap: 8px;
        margin-bottom: 10px;
      }

      .file-box {
          position: relative;
          width: 100%;
          height: 0px;
          padding-top: 100%;

          img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
              z-index: 10;
          }

          .shadow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 11;
          }

          .video-shadow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 11;
          }

          .video-player {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 33px;
            height: 33px;
            object-fit: contain;
          }

          .close-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 12;
            gap: 10px;

            span {
              size: 20PX;
              color: #FFFFFF;
              font-weight: bold;
            }
          }

          .homework-delete {
              width: 30px;
              height: 30px;
              object-fit: contain;
              position: absolute;
              right: 2px;
              top: 4px;
              z-index: 13;
          }
      }

      .homework-add {
          width: 42px;
          height: 42px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
      }

      .preview-cover {
          position: absolute;
          bottom: 0;
          box-sizing: border-box;
          width: 100%;
          padding: 4px;
          color: #fff;
          font-size: 12px;
          text-align: center;
          background: rgba(0, 0, 0, 0.3);
      }

      .hint {
          display: inline-block;
          margin-top: auto !important;
          margin-left: auto;
          font-family: 'PingFang SC';
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
          color: #BDBDBD;
          margin-top: 5px;
      }
  }

  .submit-btn {
      width: 100%;
      height: 44px;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      color: #753700;
      line-height: 44px;
      background: #F2994A;
      border-radius: 8px;
      text-align: center;
      margin-bottom: 13px;
      display: flex;
      justify-content: center;
      gap: 10px;
  }

  .submit-container-2 {
      padding: 27px 20px;
      margin-bottom: 15px;
      flex: 1;

      .input {
          font-family: 'PingFang SC';
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
          color: #4F4F4F;
          margin-bottom: 30px;
      }

      .img-list {
          display: grid;
          grid-template-columns: repeat(3, minmax(0, 1fr));
          row-gap: 6px;
          column-gap: 8px;

          .img-wrap {
              position: relative;
              width: 100%;
              height: 0px;
              padding-top: 100%;
              img {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
              }
          }

          .video-shadow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 11;
          }

          .video-player {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 33px;
            height: 33px;
            object-fit: contain;
          }
      }
  }

  .evaluation-container {
      padding: 40px 55px 40px 20px;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .evaluation {
          font-family: 'PingFang SC';
          font-weight: 500;
          font-size: 30px;
          color: #000000;
          margin-right: 16px;
      }

      .criteria {
          font-family: 'PingFang SC';
          font-weight: 600;
          font-size: 10px;
          line-height: 14px;
          text-decoration-line: underline;
          color: #F2994A;
      }

      .sigh {
          width: 13px;
          height: 13px;
      }

      .score {
          font-family: 'PingFang SC';
          font-weight: 500;
          font-size: 30px;
          color: #000000;
      }
  }

  .sheet-header {
      height: 48px;
      position: relative;
      padding-top: 16px;
      .title {
          font-family: 'PingFang SC';
          font-weight: 600;
          font-size: 22px;
          line-height: 30px;
          color: #000000;
          position: absolute;
          left: 50%;
          transform: translate(-50%, 0);
      }

      .homework-close {
          width: 18px;
          height: 18px;
          position: absolute;
          right: 16px;
          top: 12px;
      }
  }

  .sheet-content {
      width: 100%;
      padding: 16px;

      img {
        width: 100%;
        min-height: 190px;
        max-height: 300px;
        object-fit: contain;
      }
  }

  .video-preview {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: black;
    z-index: 20;
    padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/

    .video-close {
      position: absolute;
      right: 20px;
      top: 20px;
      width: 40px;
      height: 40px;
    }
  }

  .file-pop {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/

    .select {
      height: 31%;
      width: 100%;
      font-size: 22px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      color: black;
    }
    .line1 {
      width: 100%;
      height: 1px;
      background: rgba(0, 0, 0, 0.1);
    }

    .line2 {
      width: 100%;
      flex: 1;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>

<style lang="scss">
.homework {
  .video-js.vjs-paused .vjs-big-play-button{ /* 视频暂停时显示播放按钮 */
    display: block;
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
  }
}

.submit-container {
    .el-textarea {
        margin-bottom: 20px;
    }

    .el-textarea__inner {
        font-size: 14px;
    }

    .van-uploader {
        width: 100%;
        flex: 1;
    }

    .van-uploader__input-wrapper {
      width: 100%;
    }

    .van-circle {
      position: absolute;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50%;
      background: rgba(0, 0, 0, 0.45);
      border-radius: 50%;
      z-index: 12;
    }

    .van-circle__text {
      font-size: 10PX;
      color: white;
      font-weight: 600;
    }
}
</style>
