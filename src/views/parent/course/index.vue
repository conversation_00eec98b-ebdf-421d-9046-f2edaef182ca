<template>
  <div class="course">
    <div class="course-title">课程列表（{{ aiCourse.length > 0 && aiCourse[0].title || '' }}）</div>
    <div class="course-content">
      <div v-for="item in aiCourseUnitList" :key="item.id" class="course-item" @click="moveTo(item)">
        <div class="flex items-center">
          <div
            class="course-name"
            :class="{
              'course-name-grey' :!unitFinished(item)
            }"
          >第{{ item.unitNo }}节 {{ item.title || '' }}</div>
          <svg-icon icon-class="arrow-round-black" class-name="arrow-round-black" />
        </div>
        <div class="line"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAiCourse, getAiCourseUnitList } from '@/api/course-api'
export default {
  data () {
    return {
      aicourseId: this.$route.params.aicourseId,
      studentCourseId: this.$route.params.studentCourseId,
      aiCourse: [],
      aiCourseUnitList: []
    }
  },
  created () {
    this._getAiCourse()
    this._getAiCourseUnitList()
  },
  methods: {
    moveTo (unit) {
      const path = `/parent/course/report/${this.aicourseId}/${this.studentCourseId}/${unit.id}`
      this.$emit('moveTo', path)
    },
    _getAiCourse () {
      const params = {
        'aicourseId': this.aicourseId
      }
      getAiCourse(params).then(
        response => {
          this.aiCourse = response.data
        }
      )
    },
    _getAiCourseUnitList () {
      const params = {
        'aicourseId': this.aicourseId,
        'studentCourseId': this.studentCourseId
      }
      getAiCourseUnitList(params).then(
        response => {
          this.aiCourseUnitList = response.data
        }
      )
    },
    // 单元是否完成
    unitFinished (item) {
      return (
        item.aicourseUnitUser &&
        item.aicourseUnitUser.complete &&
        item.aicourseUnitUser.total &&
        item.aicourseUnitUser.complete >= item.aicourseUnitUser.total &&
        (item.aicourseUnitUser.challengeStatus === 'NONE' ||
          item.aicourseUnitUser.challengeStatus === 'END')
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.course {
    display: flex;
    flex-direction: column;
    padding-bottom: 22px;
    .course-title {
      font-size: 18px;
      line-height: 22px;
        margin-bottom: 7px;
    }

    .course-content {
        height: calc(100% - 40px);
        padding: 20px 18px;
        overflow: scroll;
        overflow-x: hidden;
    }

    .course-item {
        padding-top: 23px;
        .flex {
            margin-bottom: 30px;
        }
        .course-name {
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 25px;
            color: #000000;
            flex: 1
        }

        .course-name-grey {
          color: #828282;
        }

        .arrow-round-black {
            width: 20px;
            height: 20px;
            object-fit: contain;
        }

        .line {
            height: 1px;
            width: 100%;
            background: #F2F2F2;
        }

        &:nth-last-of-type(1) {

            .line {
                display: none;
            }
        }
    }
}
</style>
