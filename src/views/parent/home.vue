<template>
  <div class="parent-content">
    <div class="banner">
      <!-- <div v-if="childsList.length !== 0" class="edit-btn" @click="$router.push({ path: '/parent/info', query: { edit: 1} })">
        <img src="../../assets/parent/person-edit.svg" />
      </div> -->
      <div class="b-body flex justify-between">
        <div class="info-l">
          <div class="flex items-center mb10" @click="show = true">
            <img v-if="userInfo.headUrl" class="profile" :src="userInfo.headUrl" />
            <img v-else class="profile" src="../../assets/images/profile.png" />
            <template v-if="childsList.length !== 0">
              <div class="name" @click.stop="$router.push({ path: '/parent/info', query: { edit: 1} })">
                {{ userInfo.name }}
              </div>
              <img class="down" src="../../assets/parent/person-edit.svg" @click.stop="$router.push({ path: '/parent/info', query: { edit: 1} })" />
              <div v-if="childsList.length > 1" class="chang-box">
                <img class="down" src="../../assets/parent/change.svg" />
                切换学生
              </div>
            </template>
            <template v-else>
              <div class="name">
                未绑定
              </div>
            </template>
          </div>
          <div v-if="childsList.length !== 0" class="p-class mb10" @click="userInfo.userClass ? '' : $router.push({ path: '/parent/info', query: { edit: 1} })">
            {{ userInfo.userClass ? userInfo.userClass : '绑定学校>' }}
          </div>
          <div v-if="interList.length !== 0" class="p-class" @click="showInter = true">
            兴趣班>
          </div>
        </div>
        <!-- <div class="info-r">
          <div class="flex items-center h40">
            <img class="coin" src="../../assets/parent/home-icon.svg" />
            <div class="coin-num">
              {{ userInfo.aiScore }}
            </div>
          </div>
        </div> -->
        <!-- <img class="bingo-ip" src="../../assets/parent/home-ip.svg" /> -->
      </div>
    </div>
    <!-- <div class="nav">
      <div class="nav-card flex justify-around items-center">
        <div class="nav-text-box">
          <div class="n-title">累计上课</div>
          <div class="n-num">{{ userInfo.aiLessonCount }}节</div>
        </div>
        <div class="line"></div>
        <div class="nav-text-box">
          <div class="n-title">完成作业</div>
          <div class="n-num">{{ userInfo.aiWorkCount }}次</div>
        </div>
      </div>
    </div> -->
    <div class="result-box">
      <div class="result-box-bg">
        <img class="result-img" src="../../assets/parent/result-file.svg" />
        <div @click="$router.push({ path: '/parent/result' })">
          成果轨迹>
        </div>
      </div>
    </div>
    <div class="p-box">
      <div class="p-box-bg">
        <div class="flex items-center justify-between">
          <div class="p-course">我的课程</div>
          <div class="p-exchange" @click="openExchange">
            <svg-icon icon-class="course" class-name="icon-exchange" />
            <span>课程兑换</span>
          </div>
        </div>
        <div class="p-cards">
          <div v-for="item in courseList" :key="item.id" class="p-course-card flex items-center" @click="goCourse(item)">
            <img v-if="item.aicourse && item.aicourse.coverUrl" class="img" :src="item.aicourse.coverUrl" />
            <img v-else class="img" src="../../assets/images/default-cover.jpg" />
            <div class="p-text">{{ item.aicourse && item.aicourse.title }}</div>
          </div>
          <div v-if="courseList.length === 0 || childsList.length === 0" class="flex flex-col items-center justify-center w h">
            <div v-if="courseList.length === 0" class="w flex flex-col justify-center items-center">
              <img class="p-no-data" src="../../assets/parent/no-data.svg" />
              <div class="mt10">暂无数据</div>
            </div>
            <div v-if="childsList.length === 0" class="flex justify-center mt40">
              <div class="p-btn-box">
                <div
                  class="p-btn-add flex justify-center items-center"
                  @click="$router.push({ path: '/parent/info' })"
                >
                  <img class="p-add" src="../../assets/parent/add.svg" />
                  绑定学生
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <van-popup v-model="show" round closeable close-icon="clear" position="bottom" :style="{ 'min-height': '40%','max-height': '90%' }">
      <div class="pop-chang">
        <div class="p-content">
          <div v-for="item in childsList" :key="item.id" class="p-btn flex justify-center items-center" @click="selectCurr(item)">{{ item.displayName }}</div>
        </div>
        <div class="p-btn flex justify-center items-center" style="color: #828282;" @click="$router.push({ path: '/parent/info' })"><van-icon size="20" name="plus" /> 新增</div>
        <div class="p-btn flex justify-center items-center" style="color: #828282;" @click="quite">退出登录</div>
      </div>
    </van-popup>

    <van-popup v-model="showExchange" round closeable close-icon="clear" position="bottom" :style="{ height: '336px' }">
      <div class="pop-exchange">
        <div class="title">兑换课程</div>
        <div class="p-input">
          <van-field
            v-model="exCode"
            clickable
            placeholder="输入课程兑换码"
            maxlength="8"
          />
        </div>
        <div class="btn" @click="_exchangeEmpowerCourse">确认</div>
      </div>
    </van-popup>

    <van-popup v-model="showQuitTips" round closeable close-icon="clear" position="bottom" :style="{ height: '280px' }">
      <div class="pop-quite">
        <div class="q-title">确定退出兴趣班？</div>
        <div class="q-des">
          退出后将无法查看班级课程
        </div>
        <div class="btn" @click="quitInter">确认</div>
      </div>
    </van-popup>

    <van-popup v-model="showInter" round closeable close-icon="clear" position="bottom" :style="{ 'min-height': '40%','max-height': '90%' }">
      <div class="pop-inter">
        <div class="flex flex-col items-center w">
          <div class="mb15">
            <img v-if="userInfo.headUrl" class="profile" :src="userInfo.headUrl" />
            <img v-else class="profile" src="../../assets/images/profile.png" />
          </div>
          <div class="mb15 inter-name">
            {{ userInfo.name }}
          </div>
          <div v-if="userInfo.userClass" class="mb15 inter-class">
            {{ userInfo.userClass }}
          </div>
        </div>
        <div class="inter-tips mb15">
          兴趣班
        </div>
        <div class="p-content">
          <div v-for="item in interList" :key="item.id" class="inter-btn flex flex-col justify-center items-center">
            <div class="mb5">{{ item.name }}</div>
            <div>加入时间：{{ item.joinTime | formateTime }}</div>
            <div class="inter-del" @click="quitFirst(item)"><i class="el-icon-delete"></i></div>
          </div>
        </div>
      </div>
    </van-popup>

    <div v-if="showOverlay" class="overlay-wrapper">
      <div class="overlay-block">
        <svg-icon :icon-class="isChangeSuccess ? 'pass' : 'notice'" class-name="overlay-icon" />
        <div class="overlay-text">{{ isChangeSuccess ? '兑换成功' : errorChange }}</div>
      </div>
    </div>

  </div>
</template>

<script>
import { getParentChildren, getChildInfo, getStudentCourseList, unBindUser, parentQuitChildInterestClass } from '@/api/partent-api'
import { exchangeEmpowerCourse } from '@/api/course-api'
import localStore from '@/utils/local-storage.js'
import moment from 'moment'

export default {
  filters: {
    formateTime (date) {
      return moment(date).format('YYYY-MM-DD')
    }
  },
  data () {
    return {
      show: false,
      showInter: false,
      showQuitTips: false,
      interList: [],
      showQuitTipsInfo: null,
      showExchange: false,
      showOverlay: false,
      exCode: '',
      isChangeSuccess: false,
      errorChange: '',
      childsList: [],
      courseList: [],
      currChild: '',
      userInfo: {
        'headUrl': '',
        'name': '',
        'userId': '',
        'school': '',
        'userClass': '',
        'aiLessonCount': 0,
        'aiWorkCount': 0,
        'aiScore': 0
      }
    }
  },
  mounted () {
    const currChild = JSON.parse(localStore.read('currChild'))
    this.currChild = currChild
    this._getParentChildren()
  },
  methods: {
    async _getParentChildren () {
      const { data } = await getParentChildren()
      const name = this.$route.query.name
      this.childsList = data
      if (name) {
        data.forEach((val) => {
          // 从已绑定的学生中筛选
          if (val.displayName + '' === name) {
            this.currChild = val
            localStore.save('currChild', JSON.stringify(val))
          }
        })
      } else if (this.currChild) {
        let flag = false
        data.forEach((val) => {
          if (val.id + '' === this.currChild.id + '') {
            flag = true
          }
        })
        if (!flag) {
          this.currChild = null
          localStore.clear('currChild')
        }
      } else {
        if (data && data.length > 0) {
          localStore.save('currChild', JSON.stringify(data[0]))
        }
      }
      this._getChildInfo()
      this._getStudentCourseList()
    },
    async _getChildInfo () {
      if (!this.currChild) return
      const { data } = await getChildInfo({ childId: this.currChild.id })
      this.userInfo = data
      this._getParentChildrenOrg(this.currChild.id)
    },
    async _getParentChildrenOrg (userId) {
      const obj = {
        sourceId: userId,
        scene: 'PARENT_CHILDREN_CLASS'
      }
      const { data } = await getParentChildren(obj)
      console.log(data)
      if (data && data.length > 0 && data[0].classList && data[0].classList.length > 0) {
        const arr = []
        data[0].classList.forEach(v => {
          if (v.type === 'INTEREST') {
            arr.push(v)
          }
        })
        this.interList = arr
      } else {
        this.interList = []
      }

      if (this.interList.length === 0) {
        this.showInter = false
        this.showQuitTips = false
      }
    },
    async _getStudentCourseList () {
      if (!this.currChild) return
      const { data } = await getStudentCourseList({
        childId: this.currChild.id,
        studentCourseListType: 'PARENT'
      })
      this.courseList = data
    },
    selectCurr (item) {
      this.currChild = item
      localStore.save('currChild', JSON.stringify(item))
      this._getChildInfo()
      this._getStudentCourseList()
      this.show = false
    },
    goCourse (item) {
      this.$router.push({ path: `/parent/course/${item.aicourse.id}/${item.id}` })
    },
    async quite () {
      const params = {
        loginType: 'WECHAT_GZH'
      }
      try {
        await unBindUser(params)
        await this.$store.dispatch('user/FedLogOut')
        localStore.clear('currChild')
        localStore.clear('scanPath')
        this.$router.push({ path: `/parent/checkLogin` })
      } catch (error) {
        console.log(error)
      }
    },
    openExchange () {
      if (this.childsList.length === 0) {
        this.$router.push({ path: '/parent/info' })
      } else {
        this.showExchange = true
        this.exCode = ''
      }
    },
    // 兑换课程
    async _exchangeEmpowerCourse () {
      if (this.exCode.length < 8) {
        this.$toast.fail('请输入八位兑换码')
        return
      }
      const currChild = localStore.read('currChild')
      const curr = JSON.parse(currChild)
      const params = {
        'childId': curr.id,
        'exchangeCode': this.exCode
      }
      try {
        await exchangeEmpowerCourse(params)
        this.isChangeSuccess = true
        this._getStudentCourseList()
      } catch (e) {
        this.isChangeSuccess = false
        this.errorChange = e && e.message ? e.message : '兑换失败'
      } finally {
        this.showExchange = false
        this.showOverlay = true
        setTimeout(() => {
          this.showOverlay = false
        }, 1000)
      }
    },
    quitFirst (item) {
      this.showQuitTips = true
      this.showQuitTipsInfo = item
    },
    async quitInter () {
      try {
        await parentQuitChildInterestClass({
          classId: this.showQuitTipsInfo.userId,
          childId: this.currChild.id
        })
        this.$message.success('已退出')
        this._getParentChildrenOrg(this.currChild.id)
        this.showQuitTips = false
      } catch (error) {
        this.$message.error(error.message)
        // this.showQuitTips = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.parent-content {
  width: 100%;
  height: 100%;
  background: #F2F2F2;

  .mb10 {
    margin-bottom: 10px;
  }

  .mt40 {
    margin-top: 40px;
  }

  .mt10 {
    margin-top: 10px;
  }

  .mr10 {
    margin-right: 10px;
  }
  .h40 {
    height: 40px;
  }
  .banner {
    width: 100%;
    height: 240px;
    background: linear-gradient(155.36deg, #1FA2FF 3.53%, #12D8FA 48.3%, #A6FFCB 93.07%);
    border-radius: 0px 0px 39px 40px;
    position: relative;

    .edit-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      z-index: 6;
      img {
        width: 20px;
        height: 20px;
      }
    }

    .b-body {
      padding: 10px;
      padding-top: 60px;
      box-sizing: border-box;
      width: 100%;
      height: 230px;
      position: relative;

      .info-l {
        width: calc(100% - 125px);
        .p-class {
          margin-left: 45px;
          color: #000;
          font-weight: 300;
          font-size: 14px;
          max-width: calc(100% - 10px);
          display: -webkit-box;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; //需要显示的行数
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .profile {
          width: 40px;
          height: 40px;
          object-fit: cover;
        }
        .name {
          font-weight: 400;
          font-size: 18px;
          color: #000;
          margin: 0 5px;
          max-width: calc(100% - 10px);
          display: -webkit-box;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1; //需要显示的行数
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .down {
          width: 20px;
          height: 20px;
        }

        .chang-box {
          background-color: #F2C94C;
          border-radius: 5px;
          padding: 3px 5px;
          color: #333333;
          font-size: 14px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-left: 5px;
          img {
            width: 15px;
            height: 15px;
            margin-right: 5px;
          }
        }
      }
      .info-r {
        text-align: right;
        .coin {
          width: 30px;
          height: 30px;
          object-fit: cover;
          margin-right: 10px;
        }
        .coin-num {
          font-weight: 600;
          color: #000;
          font-size: 20px;
        }
      }

      .bingo-ip {
        position: absolute;
        width: 125px;
        height: 120px;
        bottom: 30px;
        right: 6px;
      }
    }
  }

  .nav {
    padding: 0 10px;
    box-sizing: border-box;
    width: 100%;
    min-height: 100px;
    margin-top: -50px;
    .nav-card {
      background: #FFFFFF;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.07);
      border-radius: 24px;
      height: 100px;
    }

    .line {
      height: 20px;
      width: 1px;
      background: #bdbdbd;
    }

    .nav-text-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 40%;
    }

    .n-title {
      font-weight: 300;
      font-size: 12px;
      color: #000;
    }
    .n-num {
      font-weight: 600;
      font-size: 17px;
      color: #000;
      margin-top: 10px;
    }
  }

  .p-btn-box {
    padding: 5px;
    box-sizing: border-box;
    background: #F2F2F2;
    border-radius: 13px;
    display: inline-block;
    .p-btn-add {
      width: 120px;
      height: 30px;
      font-size: 14px;
      color: #000000;
      border-radius: 10px;
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .result-box {
    width: 100%;
    padding: 10px 10px 0 10px;

    .result-box-bg {
      background: #FFFFFF;
      height: 70px;
      border-radius: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40px;
      font-size: 18px;
      color: #000;
      .result-img {
        width: 40px;
      }
    }
  }

  .p-box {
    height: calc(100% - 240px - 90px);
    width: 100%;
    padding: 10px 10px 40px 10px;
  }

  .p-box-bg {
    background: #fff;
    border-radius: 10px;
    height: 100%;

  }

  .p-course {
    padding: 0 10px;
    box-sizing: border-box;
    font-size: 20px;
    color: #000;
    height: 50px;
    display: flex;
    align-items: center;
  }

  .p-exchange {
    display: flex;
    align-items: center;
    margin-right: 10px;

    .icon-exchange {
      width: 16px;
      height: 16px;
      object-fit: contain;
    }

    span {
      color: #000000;
      font-size: 14px;
    }
  }

  .p-cards {
    // padding: 0 10px;
    box-sizing: border-box;
    height: calc(100% - 50px);
    overflow: auto;
  }

  .p-no-data {
    width: 65px;
    height: 65px;
  }

  .p-add {
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }

  .p-course-card {
    background: #FFFFFF;
    // border-radius: 11px;
    width: 100%;
    height: 90px;
    padding: 8px;
    box-sizing: border-box;
    font-size: 18px;
    color: #000;
    margin: 10px 0;
    border-bottom: 1px solid #F2F2F2;
    .img {
      width: 100px;
      height: 75px;
      object-fit: cover;
      border-radius: 8px;
      margin-right: 10px;
    }
    .p-text {
      width: calc(100% - 100px - 10px);
      display: -webkit-box;
      word-break: break-all;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3; //需要显示的行数
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  ::v-deep .van-popup__close-icon {
    font-size: 26px !important;
    color: #575B66 !important;
  }

  .pop-chang {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 90px 10px 90px;
    box-sizing: border-box;
    overflow-y: auto;

    .p-content {
      max-height: 55vh;
      overflow-y: auto;
      padding-bottom: 20px;
      box-sizing: border-box;
    }
    .p-btn {
      width: 100%;
      height: 50px;
      font-weight: 500;
      font-size: 20px;
      color: #000000;
      background: #FFFFFF;
      border: 1px solid #E0E0E0;
      border-radius: 10px;
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .pop-inter {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 30px 10px 30px;
    box-sizing: border-box;
    overflow-y: auto;

    .profile {
      width: 80px;
      height: 80px;
      object-fit: cover;
    }

    .inter-name {
      color: #000;
      font-size: 24px;
      font-weight: 500;
    }

    .inter-class {
      color: #000;
      font-size: 16px;
      font-weight: 300;
    }

    .inter-tips {
      color: #000;
      font-size: 20px;
      font-weight: 500;
    }

    .mb15 {
      margin-bottom: 15px;
    }
    .mb5 {
      margin-bottom: 5px;
    }

    .inter-del {
      position: absolute;
      right: -40px;
      width: 40px;
      z-index: 9;
      font-size: 26px;
      top: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .p-content {
      max-height: 55vh;
      overflow-y: auto;
      padding-bottom: 20px;
      box-sizing: border-box;
    }
    .inter-btn {
      width: calc(100% - 40px);
      box-sizing: border-box;
      padding: 10px;
      color: #000;
      font-size: 14px;
      border-radius: 10px;
      background: #F2F2F2;
      margin-bottom: 20px;
      position: relative;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .pop-quite {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 60px 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .q-title {
      font-size: 18px;
      color: #000;
      margin-bottom: 30px;
    }

    .q-des {
      color: #000000;
      font-size: 14px;
      margin-bottom: 30px;
    }

    .btn {
      width: 100%;
      height: 44px;
      line-height: 44px;
      background: #2D9CDB;
      border-radius: 8px;
      color: #F2F2F2;
      font-size: 14px;
      text-align: center;
    }
  }

  .pop-exchange {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 60px 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    .p-input {
      width: 100%;
      border-radius: 8px;
      overflow: hidden;
      margin: 37px 0 57px;

      ::placeholder {
        color: #828282;
        font-size: 12px;
      }

      ::v-deep .van-field__control {
        color: #000 !important;
      }

      ::v-deep .van-cell::after {
        border: none !important;
      }
    }

    .btn {
      width: 100%;
      height: 44px;
      line-height: 44px;
      background: #2D9CDB;
      border-radius: 8px;
      color: #F2F2F2;
      font-size: 14px;
      text-align: center;
    }
  }

  .overlay-wrapper {
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
  }

  .overlay-block {
    min-width: 150px;
    max-width: 250px;
    height: 150px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 17px;
    padding: 0 22px;
  }

  .overlay-icon {
    width: 58px !important;
    height: 58px !important;
    object-fit: contain;
  }

  .overlay-text {
    color: #4F4F4F;
    font-size: 18px;
    font-weight: 500;
    line-height: 25px;
  }
}
</style>

<style lang="scss">
.pop-exchange {
  .van-field {
    background: rgba(242, 242, 242, 1);
    font-size: 16px;
    text-align: center;

    .van-field__control {
      text-align: center;
    }
  }
}
</style>
