<template>
  <div class="main">
    <div class="title"><img src="../../assets/images/parent/edu/back.png" alt="" @click="back" /><p>{{ $route.query.title }}</p></div>
    <div v-for="(item,index) in couresList" :key="index" class="course_item" @click="toDetail(item)">
      <img class="cover" :src="item.coverUrl?item.coverUrl:DefaultCover" alt="" />
      <div class="index">{{ index+1 }}</div>
      <div class="info">{{ item.title }}</div>
    </div>
  </div>
</template>
<script>
import { getAiCourseUnitList } from '@/api/course-api.js'
import DefaultCover from '@/assets/images/default-cover.jpg'
export default {
  data () {
    return {
      couresList: [],
      DefaultCover
    }
  },
  mounted() {
    getAiCourseUnitList({
      aicourseId: this.$route.query.couresId
    }).then(res => {
      this.couresList = res.data
    })
  },
  methods: {
    toDetail(item) {
      window.location.href = `${process.env.VUE_APP_AI_URL}ai/${this.$route.query.couresId}/${0}/${item.id}?preview=1&type=chuanEdu&title=${this.$route.query.title}`
    },
    back() {
      this.$router.push('/chuanEdu')
    }
  }
}

</script>

  <style lang="scss" scoped>
  .main{
      width: 100%;
      height: 100%;
      background: #f9f8f8;
      overflow-x: hidden;
      padding: 10px;
     .title{
        width: 100%;
        display: flex;
        align-items: center;
        img{
            width: 20px;
            height: 20px;
            object-fit: cover;
        }
        p{
            font-weight: 500;
        }
     }
      .course_item{
          width: 100%;
          height: 110px;
          border: 1px solid #E0E0E0;
          border-radius: 10px;
          margin-top: 10px;
          background: #ffffff;
          position: relative;
          overflow: hidden;
          box-sizing: content-box;
          .cover{
              width: 120px;
              height:90px;
              border-radius: 10px;
              position: absolute;
              left: 10px;
              top:10px;
              object-fit: cover;
          }
          .index{
            width: 30px;
            height: 30px;
            font-size: 18px;
            font-weight: 500;
            color: #000;
            line-height: 30px;
            text-align: center;
            border-radius: 3px;
            background: #F2C94C;
            position: absolute;
            left: 20px;
            top:20px;
          }
          .info{
              width: 65%;
              height: 50px;
              font-size: 16px;
              line-height: 20px;
              padding-left: 10px;
              padding-right: 10px;
              position: absolute;
              top:10px;
              left: 130px;
          }
      }
  }
  </style>

