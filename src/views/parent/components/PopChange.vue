<template>
  <van-popup v-model="show" round closeable close-icon="clear" position="bottom" :style="{ 'min-height': '40%','max-height': '90%' }">
    <div class="pop-chang">
      <div v-for="item in childsList" :key="item.id" class="p-btn flex justify-center items-center" @click="selectCurr(item)">{{ item.displayName }}</div>
      <div class="p-btn flex justify-center items-center" style="color: #828282" @click="$router.push({ path: '/parent/info', query: {'edit': 3} })"><van-icon size="20" name="plus" /> 新增</div>
    </div>
  </van-popup>
</template>

<script>
import { getParentChildren } from '@/api/partent-api'
import { getPartentToken } from '@/utils/auth'
export default {
  data () {
    return {
      show: false,
      childsList: []
    }
  },
  mounted () {
    this._getParentChildren()
  },
  methods: {
    selectCurr (item) {
      this.$emit('selectCurr', item)
    },
    async _getParentChildren () {
      const token = await getPartentToken()
      if (!token) return
      const { data } = await getParentChildren()
      this.childsList = data
    },
    openPop () {
      this.show = true
    },
    closePop () {
      this.show = false
    }
  }
}
</script>

<style lang="scss" scoped>

.pop-chang {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 90px 0 90px;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 10px); /*兼容 IOS<11.2*/
    padding-bottom: calc(env(safe-area-inset-bottom) + 10px); /*兼容 IOS>11.2*/;
    box-sizing: border-box;
    overflow-y: auto;
    .p-btn {
      width: 100%;
      height: 50px;
      font-weight: 500;
      font-size: 20px;
      color: #000000;
      background: #FFFFFF;
      border: 1px solid #E0E0E0;
      border-radius: 10px;
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
}
</style>
