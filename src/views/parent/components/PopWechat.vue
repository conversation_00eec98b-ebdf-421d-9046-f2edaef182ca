<template>
  <van-popup
    v-model="popShow"
    round
    closeable
    close-icon="clear"
    position="bottom"
    :style="{ 'min-height': '40%','max-height': '90%' }"
    @close="closePop"
  >
    <div class="pop-wechat">
      <div class="w-logo">
        <!-- <img src="../../../assets/parent/logo2.svg" /> -->
        <div>缤果伴学</div>
      </div>
      <div class="w-des">
        提供更好服务先关注公众号
      </div>
      <div class="logo">
        <img :src="wechat" />
      </div>
      <div class="w-tips">
        长按识别关注
      </div>
    </div>
  </van-popup>
</template>

<script>
import wechat from '@/assets/parent/wechat.svg'
export default {
  props: {
    code: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      wechat,
      childsList: [],
      popShow: false
    }
  },
  watch: {
    show: {
      handler (val) {
        if (val) {
          this.popShow = this.show
        }
      }
    }
  },
  mounted () {
    this.popShow = this.show
  },
  methods: {
    closePop () {
      this.$emit('close')
      this.$bus.$emit('popWechatClose')
    }
  }
}
</script>

<style lang="scss" scoped>
.pop-wechat {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 90px 0 90px;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 10px); /*兼容 IOS<11.2*/
    padding-bottom: calc(env(safe-area-inset-bottom) + 10px); /*兼容 IOS>11.2*/;
    box-sizing: border-box;
    overflow-y: auto;
    .w-logo {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 22px;
      font-weight: 500;
      img {
        width: 43px;
        height: 43px;
        margin-right: 5px;
      }
    }
    .w-des {
      width: 100%;
      font-size: 14px;
      font-weight: 300;
      padding: 20px 0;
      text-align: center;
    }
    .logo {
      width: 100%;
      text-align: center;
      img {
        width: 180px;
        height: 180px;
      }
    }
    .w-tips {
      width: 100%;
      font-size: 21px;
      font-weight: 500;
      padding: 20px 0 0 0;
      text-align: center;
    }
}
</style>
