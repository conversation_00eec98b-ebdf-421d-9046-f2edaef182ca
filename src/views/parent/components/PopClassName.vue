<template>
  <div class="p-input">
    <van-field
      readonly
      clickable
      :disabled="disableInput"
      :value="selectVal"
      placeholder="自定义班级"
      @click="!disableInput ? show = true : ''"
    >
      <template #button>
        <div class="down"></div>
      </template>
    </van-field>
    <van-popup v-model="show" round position="bottom">
      <van-picker
        show-toolbar
        :columns="classLists"
        @cancel="show = false"
        @confirm="onConfirm"
      />
    </van-popup>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  props: {
    disableInput: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      show: false,
      fieldValue: true,
      selectVal: '',
      classLists: []
    }
  },
  watch: {
    disableInput: {
      handler: function (val) {
        if (val) this.selectVal = ''
      }
    }
  },
  mounted () {
    const grad = this.gradeOption()
    const className = this.classNameOption()
    this.classLists = [{
      values: grad,
      defaultIndex: 0
    },
    {
      values: className,
      defaultIndex: 0
    }]
  },
  methods: {
    onConfirm (val) {
      this.$emit('onConfirm', val)
      this.selectVal = val[0].text + val[1].text
      this.show = false
    },
    openPop () {
      this.show = true
    },
    closePop () {
      this.show = false
    },
    gradeOption () {
      const mouth = moment().month() + 1
      let year = moment().year()
      if (mouth < 9) {
        year = year - 1
      }
      const arr = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']
      // 初中
      const arr2 = ['七年级', '八年级', '九年级']
      const obj = []
      arr.forEach((val, index) => {
        obj.push({
          gradName: val + (year - index),
          value: year - index,
          text: val,
          level: index + 1
        })
      })
      arr2.forEach((val, index) => {
        obj.push({
          gradName: val + (year - index),
          value: year - index,
          text: val,
          level: 6 + index + 1
        })
      })
      return obj
    },
    classNameOption () {
      const obj = []
      for (let i = 1; i < 51; i++) {
        obj.push({
          value: i,
          text: i + '班'
        })
      }
      return obj
    }
  }
}
</script>

<style lang="scss" scoped>
.p-input {
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  overflow: hidden;

  ::placeholder {
    color: #828282;
    font-size: 12px;
  }

  ::v-deep .van-field__control {
    color: #000 !important;
  }

  ::v-deep .van-cell::after {
    border: none !important;
  }
}
</style>
