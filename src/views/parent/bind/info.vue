<template>
  <div class="parent-content">
    <van-icon size="30" color="#000000" name="arrow-left" @click="$router.go(-1)" />
    <div v-if="!isEdit" class="info-title flex justify-between items-center">
      <div>请绑定学生信息</div>
      <!-- <img class="scan" src="../../../assets/parent/scan.svg" /> -->
    </div>
    <div class="info-card">
      <div>
        <div>
          <div class="input-lable mt20 mb10">
            <span style="color: red;margin-right: 3px;">*</span>学生姓名
          </div>
          <div class="p-input">
            <van-field
              v-model="stuName"
              :disabled="isEdit === '2'"
              clickable
              placeholder="请输入姓名"
            />
          </div>
        </div>
        <template v-if="isEdit !== '3'">
          <div class="input-lable mt20 mb10">
            学生性别
          </div>
          <div class="p-input">
            <van-field
              readonly
              clickable
              :value="selectGender.text"
              placeholder="选择性别"
              @click="showGender = true"
            >
              <template #button>
                <div class="down"></div>
              </template>
            </van-field>
            <van-popup v-model="showGender" round position="bottom">
              <van-picker
                show-toolbar
                :columns="genderList"
                @cancel="showGender = false"
                @confirm="onGenderConfirm"
              />
            </van-popup>
          </div>
          <div class="input-lable mt20 mb10">
            所在地区（选填）
          </div>
          <div class="p-input">
            <van-field
              readonly
              clickable
              :value="fieldValue"
              placeholder="选择所在地区"
              @click="show = true"
            >
              <template #button>
                <div class="down"></div>
              </template>
            </van-field>
            <van-popup v-model="show" round position="bottom">
              <van-area title="选择所在地区" :area-list="areaList" @cancel="show = false" @confirm="onFinish" />
            </van-popup>
          </div>
        </template>
      </div>

      <div v-if="+isEdit !== 3">
        <div class="input-lable mt20 mb10">
          学校名称（选填）
        </div>
        <div class="p-input">
          <van-field
            readonly
            clickable
            :value="selectSchoolName.text"
            placeholder="选择学校"
            @click="showSchoolName = true"
          >
            <template #button>
              <div class="down"></div>
            </template>
          </van-field>
          <van-popup v-model="showSchoolName" round position="bottom">
            <van-picker
              show-toolbar
              :columns="schoolList"
              @change="schoolListChange"
              @cancel="showSchoolName = false"
              @confirm="onSchoolNameConfirm"
            >
              <template #columns-top>
                <div class="custom-input">
                  <div class="p-input">
                    <van-field v-model="customSchoolName" :disabled="schoolCustom" placeholder="如未找到所在学校，请手动输入" />
                  </div>
                </div>
              </template>
            </van-picker>
          </van-popup>
        </div>
      </div>

      <div v-if="+isEdit !== 3">
        <div class="input-lable mt20 mb10">
          班级（选填）
        </div>
        <div class="p-input">
          <van-field
            readonly
            clickable
            :value="selectClass.text"
            placeholder="选择班级"
            @click="showPicker = true"
          >
            <template #button>
              <div class="down"></div>
            </template>
          </van-field>
          <van-popup v-model="showPicker" round position="bottom">
            <van-picker
              show-toolbar
              :columns="classList"
              @change="classListChange"
              @cancel="showPicker = false"
              @confirm="onConfirm"
            >
              <template #columns-top>
                <div class="custom-input">
                  <div class="p-input">
                    <PopClassName :disable-input="classNameCustom" @onConfirm="customClassName" />
                  </div>
                </div>
              </template>
            </van-picker>
          </van-popup>
        </div>
      </div>
    </div>
    <div class="p-btn mt40" @click="bind">
      <van-loading v-show="subLoading" size="24px">加载中...</van-loading>
      <span v-show="!subLoading">确定</span>
    </div>
    <van-popup v-model="confirmOrg" :close-on-click-overlay="false" round close-icon="clear" position="bottom" :style="{ 'min-height': '40%','max-height': '90%' }">
      <div v-if="confirmOrg" class="pop-classinfo-org">
        <div class="p-info-title">{{ stuName }}已加入{{ orgClass.name }}，要切换到{{ selectClass.text }}吗</div>
        <div class="flex justify-between">
          <div class="p-info-org" @click="bindClass">确定</div>
          <div class="p-info-org" @click="subLoading = false;confirmOrg = false">取消</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getChildInfo, getSchoolList, getSchoolClassList, parentBindChild, getParentChildren } from '@/api/partent-api'
import localStore from '@/utils/local-storage.js'
import { areaList } from '@vant/area-data'
import PopClassName from '../components/PopClassName.vue'
import { Toast } from 'vant'

export default {
  components: {
    PopClassName
  },
  data () {
    return {
      classList: [{ text: '其他', value: -1 }],
      schoolList: [{ text: '其他', value: -1 }],
      selectClass: {
        text: '',
        value: ''
      },
      selectSchoolName: {
        text: '',
        value: ''
      },
      selectGender: {
        text: '',
        value: ''
      },
      genderList: [
        {
          text: '男',
          value: 'MALE'
        },
        {
          text: '女',
          value: 'FEMALE'
        },
        {
          text: '保密',
          value: 'SECRET'
        }
      ],
      fieldValue: '',
      cascaderValue: '',
      show: false,
      showPicker: false,
      showSchoolName: false,
      showGender: false,
      stuName: '',
      subLoading: false,
      // 0: 非编辑模式，1: 正常编辑模式，2: 不可编辑学生姓名, 3: 只添加学生名
      isEdit: 0,
      userInfo: null,
      isActive: false,
      areaList,
      areaObj: {
        province: '',
        city: '',
        district: ''
      },
      customSchoolName: '',
      schoolCustom: false,
      classNameCustom: false,
      customClassNameValue: null,
      confirmOrg: false,
      orgClass: null
    }
  },
  async mounted () {
    this.isActive = location.href.indexOf('parent') === -1
    this.isEdit = this.$route.query && this.$route.query.edit
    if (this.isEdit && this.isEdit !== '3') {
      this._getChildInfo()
    }
  },
  methods: {
    async _getChildInfo () {
      const currChild = JSON.parse(localStore.read('currChild'))

      if (!currChild) return
      const { data } = await getChildInfo({ childId: currChild.id })
      this.userInfo = data
      if (data.school) {
        const school = data.school
        const obj = {
          province: school.province,
          city: school.city,
          district: school.district
        }
        this.areaObj = { ...obj }
        if (school.province || school.city || school.district) {
          this.fieldValue = `${school.province ? school.province + '/' : ''}${school.city ? school.city + '/' : ''}${school.district}`
        } else {
          this.fieldValue = ''
        }
        this._getSchoolList(obj)
        this.selectSchoolName = {
          text: school.name,
          value: data.school.id
        }
        if (data.school.id) {
          this._getSchoolClassList(data.school.id)
        }
      } else if (data.userInfo) {
        const school = data.userInfo
        const obj = {
          province: school.province,
          city: school.city,
          district: school.district
        }
        this.areaObj = { ...obj }
        if (school.province || school.city || school.district) {
          this.fieldValue = `${school.province ? school.province + '/' : ''}${school.city ? school.city + '/' : ''}${school.district}`
        } else {
          this.fieldValue = ''
        }
        this._getSchoolList(obj)
        this.selectSchoolName = {
          text: (data.studentInfo && data.studentInfo.school) || '',
          value: ''
        }
        this.customClassNameValue = []
        this.customClassNameValue[0] = {
          value: (data.studentInfo && data.studentInfo.grade) || ''
        }
        this.customClassNameValue[1] = {
          value: (data.studentInfo && data.studentInfo.classroom) || ''
        }
      }
      if (data.belongClassId) {
        this.selectClass = {
          text: data.className,
          value: data.belongClassId
        }
      } else {
        this.selectClass = {
          text: data.className,
          value: ''
        }
      }
      switch (data.userInfo.gender) {
        case 'MALE':
          this.selectGender = this.genderList[0]
          break
        case 'FEMALE':
          this.selectGender = this.genderList[1]
          break

        default:
          this.selectGender = this.genderList[2]
          break
      }
      this.stuName = data.name
    },
    async _getSchoolList (obj) {
      const { data } = await getSchoolList(obj)
      const arr = [{ text: '其他', value: -1 }]
      if (data) {
        data.map((val) => {
          arr.push({ text: val.name, value: val.id })
        })
        this.schoolList = arr
      }
    },
    async _getSchoolClassList (schoolId) {
      const { data } = await getSchoolClassList({ schoolId })
      this.classList = []
      const arr = [{ text: '其他', value: -1 }]
      if (data) {
        data.map((val) => {
          arr.push({ text: val.name, value: val.userId, type: val.type })
        })
        this.classList = arr
      }
    },
    onSchoolNameConfirm (value) {
      if (!value) return
      if (value.value === -1) {
        if (!this.customSchoolName) {
          Toast('未填写学校名称')
          return
        }
        this.selectSchoolName = {
          text: this.customSchoolName,
          value: ''
        }
      } else {
        this.selectSchoolName = value
      }
      this.showSchoolName = false
      this._getSchoolClassList(value.value)
      this.selectClass = {
        text: '',
        value: ''
      }
    },
    schoolListChange (ref, val) {
      if (val.value === -1) {
        this.schoolCustom = false
      } else {
        this.customSchoolName = ''
        this.schoolCustom = true
      }
    },
    onConfirm (value) {
      if (!value) return
      if (value.value === -1) {
        if (!this.customClassNameValue) {
          Toast('未填写班级')
          return
        }
        this.selectClass = {
          text: this.customClassNameValue[0].text + this.customClassNameValue[1].text,
          type: '',
          value: '' // 班级id
        }
      } else {
        this.selectClass = value
      }

      this.showPicker = false
    },
    classListChange (ref, val) {
      if (val.value === -1) {
        this.classNameCustom = false
      } else {
        this.classNameCustom = true
        this.customClassNameValue = null
      }
    },
    customClassName (val) {
      this.customClassNameValue = val
    },
    onGenderConfirm (value) {
      if (!value) return
      this.selectGender = value
      this.showGender = false
    },
    onFinish (val) {
      this.show = false
      this.fieldValue = val.map((option, index) => {
        switch (index) {
          case 0:
            this.areaObj.province = option.name
            break
          case 1:
            this.areaObj.city = option.name
            break
          case 2:
            this.areaObj.district = option.name
            break
        }
        return option.name
      }).join('/')
      this._getSchoolList(this.areaObj)
      this.selectSchoolName = {
        text: '',
        value: ''
      }
      this.selectClass = {
        text: '',
        value: ''
      }
    },
    async bind () {
      if (this.subLoading) return
      this.subLoading = true
      if (!this.stuName) {
        this.$toast('学生姓名未填写')
        this.subLoading = false
        return
      }

      if (this.selectClass.text) {
        // 判断是兴趣班还是行政班 行政班的话 需要判断当前用户是否加入过行政班了
        const flag = await this._getParentChildrenOrg()
        if (flag.inClass) {
          // 已经加入过相同的班
          this.changePage(flag.classUserInfo)
          this.$message.success('成功')
        } else if (this.selectClass.type === 'ORGANIZATION' && flag.flag) {
          // 弹出确认框 是否加入新的行政班
          this.confirmOrg = true
        } else {
          this.bindClass()
        }
      } else {
        this.bindClass()
      }
    },

    async bindClass () {
      try {
        const gender = this.selectGender.value === 'SECRET' ? '' : this.selectGender.value
        const obj = {
          schoolId: this.selectSchoolName.value || 0,
          classId: this.selectClass.value || 0,
          gender,
          childName: this.stuName
        }

        const objData = { ...this.areaObj }

        objData.school = this.selectSchoolName.text

        if (this.selectClass.text && !this.selectClass.value && this.customClassNameValue) {
          objData.grade = this.customClassNameValue[0].value
          objData.level = this.customClassNameValue[0].level
          objData.classroom = this.customClassNameValue[1].value
        }
        if (this.isEdit && this.isEdit !== '3') {
          obj.childId = this.userInfo.userId
        }
        const { data } = await parentBindChild(obj, objData)
        data.belongClassId = this.selectClass.value
        this.changePage(data)
      } catch (error) {
        this.subLoading = false
        if (error.code === 635) {
          if (this.isActive) {
            this.$router.push({ path: '/h5/index', query: { name: this.stuName }})
          } else {
            this.$router.push({ path: '/parent/home', query: { name: this.stuName }})
          }
        }
      }
    },
    changePage (userInfo) {
      // 分割是否已经加入过班级 加入过的 就直接切换了
      localStore.save('currChild', JSON.stringify(userInfo))
      this.subLoading = false
      if (!this.isEdit) {
        if (this.isActive) {
          this.$router.push({ path: '/h5/index' })
        } else {
          const scanPath = localStore.read('scanPath')
          if (scanPath) {
            localStore.clear('scanPath')
            this.$router.push({ path: scanPath })
          } else {
            this.$router.push({ path: '/parent/home' })
          }
        }
      } else {
        localStore.clear('scanPath')
        if (+this.isEdit === 2 || this.isEdit === '3') {
          this.$router.go(-1)
        } else {
          if (this.isActive) {
            this.$router.push({ path: '/h5/index' })
          } else {
            this.$router.push({ path: '/parent/home' })
          }
        }
      }
    },
    // 判断是否有加入的行政班
    async _getParentChildrenOrg () {
      const obj = {
        param: this.stuName,
        scene: 'PARENT_CHILDREN_CLASS'
      }
      const { data } = await getParentChildren(obj)
      const returnObj = {
        flag: false, // 是否加入过行政班
        classUserInfo: null, // 用户信息
        inClass: false, // 是否加入过同班级
        orgClass: null // 行政班信息
      }
      if (data.length > 0 && data[0].classList) {
        returnObj.classUserInfo = data[0]
        for (let i = 0; i < data[0].classList.length; i++) {
          const val = data[0].classList[i]
          if (val.type === 'ORGANIZATION') {
            returnObj.flag = true
            this.orgClass = val
          }
          if (+val.userId === +this.selectClass.value) {
            returnObj.inClass = true
          }
        }
      }
      return returnObj
    }
  }
}
</script>

<style lang="scss" scoped>
  .parent-content {
    width: 100%;
    min-height: 100%;
    padding: 20px;
    // background: linear-gradient(359.86deg, #56CCF2 0.08%, #2F80ED 98.53%);
    background: #F2FAFD;
    color: #000000;

    .custom-input {
      padding: 10px 20px;
    }

    .mt20 {
      margin-top: 20px;
    }
    .mt40 {
      margin-top: 40px;
    }
    .mb10 {
      margin-bottom: 10px;
    }
    .p-city-input, .p-input {
      border: 1px solid #E0E0E0;
      border-radius: 8px;
      overflow: hidden;

      ::placeholder {
        color: #828282;
        font-size: 12px;
      }

      ::v-deep .van-field__control {
        color: #000 !important;
      }

      ::v-deep .van-cell::after {
        border: none !important;
      }
    }

    .p-city-input {
      width: 45%;
    }
    .p-input {
      width: 100%;
    }

    .info-title {
      color: #000000;
      font-weight: 500;
      font-size: 20px;
      margin-top: 40px;
      .scan {
        width: 20px;
        height: 20px;
      }
    }
    .info-card {
      margin-top: 20px;
      min-height: 100px;
      width: 100%;
      background: #FFFFFF;
      border: 1px solid #E0E0E0;
      border-radius: 8px;
      padding: 20px;
      padding-bottom: 40px;
      box-sizing: border-box;

      .down {
        width: 0;
        height: 0;
        margin-top: 5px;
        border: 5px solid transparent;
        border-top: 6px solid #575B66;
      }

      .input-lable {
        color: #4F4F4F;
        font-weight: 500;
        font-size: 16px;
      }
    }
    .p-btn {
      width: 380px;
      height: 50px;
      background: #2F80ED;
      border-radius: 8px;
      color: #ffffff;
      display: flex;
      font-size: 16px;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .pop-classinfo-org {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 90px 10px 90px;
    box-sizing: border-box;
    overflow-y: auto;

    .p-info-title {
      font-size: 24px;
      text-align: center;
      margin-bottom: 10px;
      line-height: 30px;
    }

    .p-info-org {
      text-align: center;
      width: 45%;
      color: #fff;
      background: #2D9CDB;
      border-radius: 8px;
      height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      margin: 15px 0 20px 0;
    }
  }
</style>
