<template>
  <div class="parent-content flex justify-center items-center">
    <div class="info-card">
      <div class="p-title">请选择对应学生名</div>
      <div v-for="item in childList" :key="item.id" class="p-btn flex justify-center items-center" @click="handleSelect(item)">{{ item.displayName }}</div>
    </div>
  </div>
</template>

<script>
import { getParentChildren } from '@/api/partent-api'
import localStore from '@/utils/local-storage.js'
export default {
  data () {
    return {
      value: '',
      childList: []
    }
  },
  mounted () {
    this._getParentChildren()
  },
  methods: {
    async _getParentChildren () {
      const { data } = await getParentChildren()
      this.childList = data
    },
    handleSelect (item) {
      localStore.save('currChild', JSON.stringify(item))
      const scanPath = localStore.read('scanPath')
      if (scanPath) {
        localStore.clear('scanPath')
        this.$router.push({ path: scanPath })
      } else {
        this.$router.push({ path: '/parent/home' })
      }
      // this.$router.push({ path: '/parent/home' })
    }
  }
}
</script>

<style lang="scss" scoped>
  .parent-content {
    width: 100%;
    min-height: 100%;
    padding: 40px;
    background: linear-gradient(359.86deg, #56CCF2 0.08%, #2F80ED 98.53%);

    .info-title {
      color: #fff;
      font-weight: 500;
      font-size: 20px;
      margin-top: 40px;
      .scan {
        width: 20px;
        height: 20px;
      }
    }
    .info-card {
      width: 100%;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 10px;
      padding: 40px 50px;
      box-sizing: border-box;
    }

    .p-title {
      width: 100%;
      font-weight: 500;
      font-size: 20px;
      color: #828282;
      margin-bottom: 40px;
      text-align: center;
    }

    .p-btn {
      width: 100%;
      height: 50px;
      font-weight: 500;
      font-size: 20px;
      color: #000000;
      background: #FFFFFF;
      border: 1px solid #E0E0E0;
      border-radius: 10px;
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>
