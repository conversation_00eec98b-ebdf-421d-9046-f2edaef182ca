<template>
  <!-- 扫码跳转的页面 -->
  <div class="parent-content">
    <!-- <van-icon size="30" color="#ffffff" name="arrow-left" @click="$router.go(-1)" /> -->
    <div class="info-title flex justify-between items-center">
      <div>请绑定学生信息</div>
      <!-- <img class="scan" src="../../../assets/parent/scan.svg" /> -->
    </div>
    <div class="info-card">
      <div>
        <div class="input-lable mt20 mb10">
          <span style="color: red;margin-right: 3px;">*</span>学生姓名
        </div>
        <div class="p-input">
          <van-field
            v-model="stuName"
            clickable
            placeholder="请输入姓名"
            @focus="checkLogin"
          />
        </div>
      </div>

      <div>
        <div class="input-lable mt20 mb10">
          学生性别
        </div>
        <div class="p-input">
          <van-field
            readonly
            clickable
            :value="selectGender.text"
            placeholder="选择性别"
            @click="showGender = true"
          >
            <template #button>
              <div class="down"></div>
            </template>
          </van-field>
          <van-popup v-model="showGender" round position="bottom">
            <van-picker
              show-toolbar
              :columns="genderList"
              @cancel="showGender = false"
              @confirm="onGenderConfirm"
            />
          </van-popup>
        </div>
      </div>

      <div>
        <div class="input-lable mt20 mb10">
          学校名称（选填）
        </div>
        <div class="input-box-disable">
          {{ queryObj.schoolName }}
        </div>
      </div>

      <div>
        <div class="input-lable mt20 mb10">
          班级（选填）
        </div>
        <div class="input-box-disable">
          {{ queryObj.className }}
        </div>
      </div>
    </div>
    <div class="p-btn mt40" @click="bind">
      <van-loading v-show="subLoading" size="24px">加载中...</van-loading>
      <span v-show="!subLoading">确定</span>
    </div>

    <van-popup v-model="bindShow" :close-on-click-overlay="false" round closeable close-icon="clear" position="bottom" :style="{ 'min-height': '40%','max-height': '90%' }">
      <div class="pop-classinfo-chang">
        <div class="p-info-title">检测到该班已绑定学生</div>
        <div class="p-name-box p-info-name">
          {{ childList.join('、') }}
        </div>
        <div class="p-info-btn" @click="skip">无需绑定进入首页</div>
        <div class="p-info-tips" @click="bindShow = false; _getChildInfo()">绑定其他学生</div>
      </div>
    </van-popup>

    <van-popup v-model="confirmOrg" :close-on-click-overlay="false" round close-icon="clear" position="bottom" :style="{ 'min-height': '40%','max-height': '90%' }">
      <div v-if="confirmOrg" class="pop-classinfo-org">
        <div class="p-info-title">{{ stuName }}已加入{{ orgClass.name }}，要切换到{{ queryObj.className }}吗</div>
        <div class="flex justify-between">
          <div class="p-info-org" @click="bindClass">确定</div>
          <div class="p-info-org" @click="subLoading = false;confirmOrg = false">取消</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { parentBindChild, getParentChildren, getClassInfo } from '@/api/partent-api'
import localStore from '@/utils/local-storage.js'
import { getPartentToken } from '@/utils/auth'
export default {
  data () {
    return {
      token: '',
      fieldValue: '',
      bindShow: false,
      show: false,
      showPicker: false,
      showSchoolName: false,
      options: [],
      stuName: '',
      subLoading: false,
      queryObj: {
        schoolId: 0,
        schoolName: '',
        classId: 0,
        className: ''
      },
      childList: [],
      currClassChilds: [],
      selectGender: {
        text: '',
        value: ''
      },
      genderList: [
        {
          text: '男',
          value: 'MALE'
        },
        {
          text: '女',
          value: 'FEMALE'
        },
        {
          text: '保密',
          value: 'SECRET'
        }
      ],
      showGender: false,
      confirmOrg: false,
      classInfo: null,
      orgClass: null
    }
  },
  async mounted () {
    this.isActive = location.href.indexOf('parent') === -1
    this.token = await getPartentToken()
    this.queryObj = {
      schoolId: this.$route.query.schoolId,
      schoolName: this.$route.query.schoolName,
      classId: this.$route.query.classId,
      className: this.$route.query.className
    }

    if (this.token) {
      this._getParentChildren()
      this._getClassInfo()
    }
  },
  methods: {
    async _getChildInfo () {
      // 默认填入当前本地存储的学生
      const currChild = JSON.parse(localStore.read('currChild'))
      if (!currChild) return
      this.stuName = currChild.displayName
      const text = currChild.gender === 'MALE' ? '男' : currChild.gender === 'FEMALE' ? '女' : '保密'
      this.selectGender = {
        text,
        value: currChild.gender ? currChild.gender : 'SECRET'
      }
      // const flag = await this._getParentChildrenOrg()
      // if (this.classInfo.type === 'ORGANIZATION' && flag.flag) {
      //   // 弹出确认框 是否加入新的行政班
      //   this.confirmOrg = true
      // }
    },
    async _getParentChildren () {
      const { data } = await getParentChildren()
      this.childList = []
      this.currClassChilds = []
      let isHave = false
      if (data) {
        data.map(val => {
          if (+val.belongClassId === +this.queryObj.classId) {
            isHave = true
            this.currClassChilds.push(val)
            this.childList.push(val.displayName)
          }
        })

        if (isHave) {
          this.bindShow = true
        } else {
          this._getChildInfo()
        }
      }
    },
    async bind () {
      this.token = await getPartentToken()
      if (!this.token) {
        if (this.isActive) {
          this.$router.push({
            'path': '/h5/checkLogin'
          })
        } else {
          this.$router.push({
            'path': '/parent/checkLogin'
          })
        }
        return
      }
      if (this.subLoading) return
      this.subLoading = true
      try {
        if (!this.stuName) {
          this.$toast('学生姓名未填写')
          this.subLoading = false
          return
        }
        // 判断是兴趣班还是行政班 行政班的话 需要判断当前用户是否加入过行政班了
        const flag = await this._getParentChildrenOrg()
        if (flag.inClass) {
          // 已经加入过相同的班
          this.changePage(flag.classUserInfo)
          this.$message.success('成功')
        } else if (this.classInfo.type === 'ORGANIZATION' && flag.flag) {
          // 弹出确认框 是否加入新的行政班
          this.confirmOrg = true
        } else {
          this.bindClass()
        }
      } catch (error) {
        this.subLoading = false
        if (error.code === 635) {
          if (this.isActive) {
            this.$router.push({ path: '/h5/index', query: { name: this.stuName }})
          } else {
            this.$router.push({ path: '/parent/home', query: { name: this.stuName }})
          }
        } else {
          console.log(error)
        }
      }
    },
    async bindClass () {
      const gender = this.selectGender.value === 'SECRET' ? '' : this.selectGender.value
      const { data } = await parentBindChild({
        schoolId: this.queryObj.schoolId,
        classId: this.queryObj.classId,
        gender,
        childName: this.stuName
      }, {})
      this.$message.success('成功')
      data.belongClassId = this.queryObj.classId
      this.changePage(data)
    },
    changePage (userInfo) {
      // 分割是否已经加入过班级 加入过的 就直接切换了
      localStore.save('currChild', JSON.stringify(userInfo))
      this.subLoading = false
      if (this.isActive) {
        this.$router.push({ path: '/h5/index' })
      } else {
        const scanPath = localStore.read('scanPath')
        if (scanPath) {
          if (scanPath.indexOf('/parent/hassClassInfo') > -1) {
            this.$router.push({ path: '/parent/home' })
          } else {
            this.$router.push({ path: scanPath })
          }
          localStore.clear('scanPath')
        } else {
          this.$router.push({ path: '/parent/home' })
        }
      }
    },
    async _getClassInfo () {
      const { data } = await getClassInfo({
        classUserId: this.$route.query.classId
      })
      // 扫码进入的是否是行政班
      this.classInfo = data
    },
    // 判断是否有加入的行政班
    async _getParentChildrenOrg () {
      const obj = {
        param: this.stuName,
        scene: 'PARENT_CHILDREN_CLASS'
      }
      const { data } = await getParentChildren(obj)
      const returnObj = {
        flag: false, // 是否加入过行政班
        classUserInfo: null, // 用户信息
        inClass: false, // 是否加入过同班级
        orgClass: null // 行政班信息
      }
      if (data.length > 0 && data[0].classList) {
        returnObj.classUserInfo = data[0]
        for (let i = 0; i < data[0].classList.length; i++) {
          const val = data[0].classList[i]
          if (val.type === 'ORGANIZATION') {
            returnObj.flag = true
            this.orgClass = val
          }
          if (+val.userId === +this.queryObj.classId) {
            returnObj.inClass = true
          }
        }
      }
      return returnObj
    },
    async checkLogin () {
      this.token = await getPartentToken()
      if (!this.token) {
        if (this.isActive) {
          this.$router.push({
            'path': '/h5/checkLogin'
          })
        } else {
          this.$router.push({
            'path': '/parent/checkLogin'
          })
        }
        return
      }
    },
    skip () {
      const currChild = localStore.read('currChild')
      localStore.clear('scanPath')
      if (currChild) {
        if (this.isActive) {
          this.$router.push({ path: '/h5/index' })
        } else {
          this.$router.push({ path: '/parent/home' })
        }
      } else {
        if (this.currClassChilds && this.currClassChilds.length > 0) {
          localStore.save('currChild', JSON.stringify(this.currClassChilds[0]))
          if (this.isActive) {
            this.$router.push({ path: '/h5/index' })
          } else {
            this.$router.push({ path: '/parent/home' })
          }
        }
      }
    },
    onGenderConfirm (value) {
      if (!value) return
      this.selectGender = value
      this.showGender = false
    }
  }
}
</script>

<style lang="scss" scoped>
  .parent-content {
    width: 100%;
    min-height: 100%;
    padding: 20px;
    // background: linear-gradient(359.86deg, #56CCF2 0.08%, #2F80ED 98.53%);
    background: #F2FAFD;

    .input-box-disable {
      height: 50px;
      width: 100%;
      background: #F2F2F2;
      border: 1px solid #E0E0E0;
      border-radius: 5px;
      font-weight: 600;
      font-size: 12px;
      color: #000;
      padding: 0 10px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
    }
    .mt20 {
      margin-top: 20px;
    }
    .mt40 {
      margin-top: 40px;
    }
    .mb10 {
      margin-bottom: 10px;
    }
    .p-city-input, .p-input {
      border: 1px solid #E0E0E0;
      border-radius: 8px;
      overflow: hidden;

      ::placeholder {
        color: #828282;
        font-size: 12px;
      }

      ::v-deep .van-field__control {
        color: #000 !important;
      }

      ::v-deep .van-cell::after {
        border: none !important;
      }
    }

    .p-city-input {
      width: 45%;
    }
    .p-input {
      width: 100%;
    }

    .info-title {
      color: #000;
      font-weight: 500;
      font-size: 20px;
      margin-top: 40px;
      .scan {
        width: 20px;
        height: 20px;
      }
    }
    .info-card {
      margin-top: 20px;
      min-height: 100px;
      width: 100%;
      background: #FFFFFF;
      border: 1px solid #E0E0E0;
      border-radius: 8px;
      padding: 20px;
      padding-bottom: 40px;
      box-sizing: border-box;

      .down {
        width: 0;
        height: 0;
        margin-top: 5px;
        border: 5px solid transparent;
        border-top: 6px solid #575B66;
      }

      .input-lable {
        color: #4F4F4F;
        font-weight: 500;
        font-size: 16px;
      }
    }
    .p-btn {
      width: 380px;
      height: 50px;
      background: #2F80ED;
      border-radius: 8px;
      color: #ffffff;
      display: flex;
      font-size: 16px;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .pop-classinfo-chang {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 90px 10px 90px;
    box-sizing: border-box;
    overflow-y: auto;

    .p-info-title {
      font-size: 24px;
      text-align: center;
      margin-bottom: 10px;
    }
    .p-info-name {
      font-size: 24px;
      text-align: center;
      margin-bottom: 10px;
      padding: 10px 0;
    }
    .p-info-btn {
      text-align: center;
      width: 100%;
      color: #fff;
      background: #2D9CDB;
      border-radius: 8px;
      height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      margin: 15px 0 20px 0;
    }
    .p-info-tips {
      text-align: center;
      font-size: 14px;
      padding: 10px 0;
    }

    .p-name-box {
      max-height: 180px;
      overflow-y: auto;
    }
  }

  .pop-classinfo-org {
    height: calc(100% - 60px);
    margin-top: 50px;
    padding: 0 90px 10px 90px;
    box-sizing: border-box;
    overflow-y: auto;

    .p-info-title {
      font-size: 24px;
      text-align: center;
      margin-bottom: 10px;
      line-height: 30px;
    }

    .p-info-org {
      text-align: center;
      width: 45%;
      color: #fff;
      background: #2D9CDB;
      border-radius: 8px;
      height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      margin: 15px 0 20px 0;
    }
  }
</style>
