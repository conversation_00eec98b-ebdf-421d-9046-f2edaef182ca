<template>
  <div class="w h flex justify-center items-center">
    登录中...
  </div>
</template>

<script>
import localStore from '@/utils/local-storage.js'
import { getParentChildren, getGzhUserInfo } from '@/api/partent-api'
import { message } from '@/utils/singeMessage.js'

export default {
  data () {
    return {
      code: this.getQueryString('code'),
      state: this.getQueryString('state'),
      appid: 'wxfa4a06f2648b3c54'
    }
  },
  created () {
    this.check()
  },
  mounted () {
    this.$bus.$on('popWechatClose', () => {
      this.checkGoPath()
    })
  },
  beforeDestroy () {
    this.$bus.$off('popWechatClose')
  },
  methods: {
    getQueryString (name) {
      if (this.$route.query && this.$route.query[name]) {
        return this.$route.query[name]
      }
      const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
      const result = window.location.search.substring(1).match(reg)
      if (result != null) {
        return decodeURIComponent(result[2])
      }
      return null
    },
    handleGetWxCode (state = 1) {
      const baseUrl = window.location.origin
      const isActive = location.href.indexOf('parent') === -1
      let url
      if (isActive) {
        url = `${baseUrl}/#/h5/checkLogin`
      } else {
        url = `${baseUrl}/#/parent/checkLogin`
      }
      window.location.href = `${process.env.VUE_APP_WEB_URL}get-weixin-code.html?appid=${this.appid}&scope=snsapi_userinfo&state=${state}&redirect_uri=${encodeURIComponent(url)}`
    },
    async checkGoPath () {
      const isActive = location.href.indexOf('parent') === -1
      const { data } = await getParentChildren()
      if (data.length === 0) {
        if (isActive) {
          const h5ScanPath = localStore.read('h5ScanPath')
          if (h5ScanPath) {
            localStore.clear('h5ScanPath')
            this.$router.push({ path: h5ScanPath })
          } else {
            this.$router.push({ path: '/h5/index' })
          }
        } else {
          const scanPath = localStore.read('scanPath')
          if (scanPath) {
            localStore.clear('scanPath')
            this.$router.push({ path: scanPath })
          } else {
            this.$router.push({ path: '/parent/home' })
          }
        }
      } else if (data.length > 1) {
        // 多个学生
        const currChild = localStore.read('currChild')
        if (isActive) {
          if (currChild) {
            const curr = JSON.parse(currChild)
            let flag = false
            data.forEach((val) => {
              if (val.id + '' === curr.id + '') {
                flag = true
              }
            })
            if (flag) {
              const h5ScanPath = localStore.read('h5ScanPath')
              if (h5ScanPath) {
                localStore.clear('h5ScanPath')
                this.$router.push({ path: h5ScanPath })
              } else {
                this.$router.push({ path: '/h5/index' })
              }
              return
            }
          }
          localStore.save('currChild', JSON.stringify(data[0]))
          this.$router.push({ path: '/h5/index' })
        } else {
          const scanPath = localStore.read('scanPath')
          if (scanPath && scanPath.indexOf('/parent/hassClassInfo') > -1) {
            localStore.clear('scanPath')
            this.$router.push({ path: scanPath })
            return
          }
          if (currChild) {
            const curr = JSON.parse(currChild)
            let flag = false
            data.forEach((val) => {
              if (val.id + '' === curr.id + '') {
                flag = true
              }
            })
            if (flag) {
              if (scanPath) {
                localStore.clear('scanPath')
                this.$router.push({ path: scanPath })
              } else {
                this.$router.push({ path: '/parent/home' })
              }
              // this.$router.push({ path: '/parent/home' })
              return
            }
          }
          // 默认选择第一个学生并去主页
          localStore.save('currChild', JSON.stringify(data[0]))
          this.$router.push({ path: '/parent/home' })
          // this.$router.push({ path: '/parent/change' })
        }
      } else {
        // 只有一个学生
        localStore.save('currChild', JSON.stringify(data[0]))
        if (isActive) {
          const h5ScanPath = localStore.read('h5ScanPath')
          if (h5ScanPath) {
            localStore.clear('h5ScanPath')
            this.$router.push({ path: h5ScanPath })
          } else {
            this.$router.push({ path: '/h5/index' })
          }
        } else {
          const scanPath = localStore.read('scanPath')
          if (scanPath) {
            localStore.clear('scanPath')
            this.$router.push({ path: scanPath })
          } else {
            this.$router.push({ path: '/parent/home' })
          }
        }
        // this.$router.push({ path: this.redirect || '/parent/home' })
      }
    },
    async check () {
      const code = this.code || this.getQueryString('code')
      if (!code) {
        this.handleGetWxCode()
        return
      }
      try {
        await this.$store.dispatch('user/partentLogin', {
          mobileOrEmail: '',
          password: '',
          loginType: 'WECHAT_GZH',
          code
        })
        const { data } = await getGzhUserInfo()
        if (!data.subscribe) {
          this.$bus.$emit('popWechatOpen')
        } else {
          await this.checkGoPath()
        }
      } catch (error) {
        console.log(error)
        console.log(JSON.stringify(error))
        const isActive = location.href.indexOf('parent') === -1
        if (+error.code === 602) {
          if (isActive) {
            this.$router.push({ path: '/h5/login', query: { code }})
          } else {
            this.$router.push({ path: '/parent/login', query: { code: code, isScan: '1' }})
          }
        } else {
          message({
            message: '登录失败',
            type: 'error',
            duration: 3 * 1000
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
