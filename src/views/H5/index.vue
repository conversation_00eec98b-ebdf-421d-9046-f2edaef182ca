<template>
  <div class="activity-container">

    <div class="content">
      <home />
    </div>

    <div class="bottom-footer">
      <div class="footer-box">
        <img :src="house" alt="" />
        <span style="color:#2F80ED">首页</span>
      </div>
      <div class="footer-box" @click="toMy">
        <img :src="mine" alt="" />
        <span style="color:#BDBDBD">我的</span>
      </div>
    </div>
  </div>
</template>

<script>
import house from '@/assets/H5/house.svg'
import dfHouse from '@/assets/H5/house-default.svg'
import mine from '@/assets/H5/mine.svg'
import dfMine from '@/assets/H5/mine-default.svg'
import Home from './components/home.vue'
import { isApp, isWeChatBrowser } from '@/utils/index'
import { getPartentToken } from '@/utils/auth'
export default {
  components: { Home },
  data () {
    return {
      house,
      dfHouse,
      mine,
      dfMine,
      isApp: false
    }
  },
  async mounted() {
    console.log(isWeChatBrowser())
    window['setBingoToken'] = async(val) => {
      await this.$store.dispatch('user/partentLoginApp', 'Bearer ' + val)
      await this.$store.dispatch('user/GetInfo')
      this.$router.push({ path: '/h5/my' })
    }
    if (this.$route.query && this.$route.query.token) {
      await this.$store.dispatch('user/partentLoginApp', 'Bearer ' + this.$route.query.token)
      await this.$store.dispatch('user/GetInfo')
    }
    this.isApp = isApp()
  },
  methods: {
    toMy() {
      if (!isApp()) {
        this.$router.push({ path: '/h5/my' })
      } else {
        if (getPartentToken()) {
          this.$router.push({ path: '/h5/my' })
        } else {
          if (window.webkit && window.webkit.messageHandlers) {
            window.webkit.messageHandlers.bingo_action.postMessage('event_no_login')
          } else if (window.bingo_action) {
            window.bingo_action.postMessage('event_no_login')
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-container {
    width: 100%;
    height: 100%;
}

.content {
    width: 100%;
    height: 100%;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 85px); /*兼容 IOS<11.2*/
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px); /*兼容 IOS>11.2*/
    box-sizing: border-box;
}

.bottom-footer {
    position: fixed;
    bottom: 0;
    background: #ffffff;
    width: 100%;
    padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
    display: flex;
    height: 85px;
    box-sizing: content-box;

    .footer-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 7px;
        flex: 1;
        padding: 15px 0;

        img {
            width: 36px;
            height: 36px;
            object-fit: contain;
        }

        span {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            color: #FFFFFF;
        }
    }
}
</style>
