<template>
  <div class="h5-content">
    <div v-if="list" class="head">
      <div class="title">
        {{ list.name }}
      </div>
      作品提交时间：{{ list.startTime | formateTime }}-{{
        list.endSubmit | formateTime
      }}
    </div>

    <div class="info-title">
      <img src="@/assets/H5/title-tip.png" />
      作品信息
    </div>
    <div class="info-box">
      <div class="form-box form-bottom">
        <div class="form-required">
          <img src="@/assets/H5/require.png" />
        </div>
        <div class="form-name">作品名称：</div>
        <div class="form-content">
          <van-field
            v-model="activityForm.name"
            placeholder="请输入"
            @blur="handleInputBlur"
          />
        </div>
      </div>

      <div class="form-box">
        <div class="form-required">
          <!-- <img src="@/assets/H5/require.png" /> -->
        </div>
        <div class="form-name">作品简介：</div>
      </div>
      <div style="border: 1px solid #F8F8F8;">
        <van-field
          v-model="activityForm.introduce"
          type="textarea"
          :rows="4"
          placeholder="输入内容"
          maxlength="300"
          show-word-limit
          @blur="handleInputBlur"
        />
      </div>

      <div class="form-box">
        <div class="form-required">
          <img src="@/assets/H5/require.png" />
        </div>
        <div class="form-name">上传作品：</div>
      </div>
      <div>
        <el-upload
          class="upload"
          :data="dataObj"
          :action="ossUrl"
          :multiple="false"
          :show-file-list="true"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :on-success="handleUploadSuccess"
          :on-exceed="handleUploadExceed"
          :limit="10"
        >
          <van-button type="default">上传文件</van-button>
          <div slot="tip">
            <van-cell :border="false" value="支持格式：jpg、jpeg、png、mp4、pdf" />
            <van-cell :border="false" value="若手机不支持上传时，请电脑打开此链接" />
          </div>
        </el-upload>
      </div>
    </div>

    <div class="info-title">
      <img src="@/assets/H5/title-tip.png" />
      参赛信息
    </div>

    <div class="info-box">
      <div v-if="formShowListMap.has('mobile')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('mobile') &&
                formShowListMap.get('mobile').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('mobile').title }}：</div>
        <div class="form-content">
          <van-field
            v-model="activityForm.mobile"
            placeholder="请输入"
            @blur="handleInputBlur"
          />
        </div>
      </div>

      <div v-if="formShowListMap.has('author')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('author') &&
                formShowListMap.get('author').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('author').title }}：</div>
        <div class="form-content">
          <van-field
            v-model="activityForm.author"
            placeholder="请输入"
            @blur="handleInputBlur"
          />
        </div>
      </div>

      <div v-if="formShowListMap.has('gender')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('gender') &&
                formShowListMap.get('gender').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('gender').title }}：</div>
        <div class="form-content">
          <van-radio-group v-model="activityForm.gender" direction="horizontal">
            <van-radio name="MALE" icon-size="14px">男</van-radio>
            <van-radio name="FEMALE" icon-size="14px">女</van-radio>
          </van-radio-group>
        </div>
      </div>

      <div v-if="formShowListMap.has('age')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('age') && formShowListMap.get('age').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('age').title }}：</div>
        <div class="form-content">
          <van-field
            v-model="activityForm.age"
            placeholder="请输入"
            type="digit"
            @blur="handleInputBlur"
          />
        </div>
      </div>

      <div v-if="formShowListMap.has('schoolId')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('schoolId') &&
                formShowListMap.get('schoolId').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('schoolId').title }}：</div>
        <div class="form-content">
          <el-select
            v-model="activityForm.schoolId"
            placeholder="请选择"
            @change="handleInputBlur"
          >
            <el-option
              v-for="item in schoolList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <!-- <el-input v-model="activityForm.schoolId" placeholder="请输入" /> -->
        </div>
      </div>

      <div v-if="formShowListMap.has('part')" class="form-box form-bottom">
        <div
          class="form-required"
        >
          <img v-if="formShowListMap.get('part') && formShowListMap.get('part').required" src="@/assets/H5/require.png" />
        </div>
        <div class="form-name">{{ formShowListMap.get('part').title }}：</div>
        <div class="form-content">
          <el-select
            v-model="activityForm.part"
            placeholder="请选择"
            @change="handleInputBlur"
          >
            <el-option
              v-for="(item, index) in formShowListMap.get('part').option"
              :key="index"
              :label="item"
              :value="index"
            />
          </el-select>
        </div>
      </div>

      <div v-if="formShowListMap.has('email')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('email') &&
                formShowListMap.get('email').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('email').title }}：</div>
        <div class="form-content">
          <van-field
            v-model="activityForm.email"
            placeholder="请输入"
            @blur="handleInputBlur"
          />
        </div>
      </div>

      <div v-if="formShowListMap.has('adviser')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('adviser') &&
                formShowListMap.get('adviser').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('adviser').title }}：</div>
        <div class="form-content">
          <van-field
            v-model="activityForm.adviser"
            placeholder="请输入"
            @blur="handleInputBlur"
          />
        </div>
      </div>
    </div>

    <div class="btn-box">
      <div class="btn" @click="beforeSubmitCheck">保存</div>
    </div>
    <popup v-if="showPop" @cancle="showPop = false" @submit="submit">
      <template #title> 确认提交吗 </template>
    </popup>
  </div>
</template>

<script>
import popup from './components/popup.vue'
import moment from 'moment'
import { validEmail, validMobile } from '@/utils/validate.js'
import { getFileUploadAuthor } from '@/api/user-api'
import { getActivityInfo, submitActivityWorks, getActivitySchoolList } from '@/api/activity-api.js'

export default {
  components: {
    popup
  },
  filters: {
    formateTime (date) {
      return moment(date).format('YYYY.MM.DD HH:mm')
    }
  },
  data () {
    return {
      showPop: false,
      activityID: 0,
      list: null,
      ossUrl: '',
      fileList: [],
      beforeUp: new Map(),
      dataObj: {
        key: '',
        policy: '',
        OSSAccessKeyId: '',
        'success_action_status': '200',
        callback: '',
        signature: ''
      },
      formShowList: null,
      formShowListMap: new Map(),
      submitFileList: null,
      requireList: [],
      schoolList: [],
      activityForm: {
        name: '',
        part: '',
        introduce: '',
        author: '',
        gender: null,
        age: '',
        email: '',
        schoolId: '',
        mobile: '',
        adviser: ''
      }
    }
  },
  watch: {
    fileList: {
      handler: function (val) {
        if (val) {
          this.handleInputBlur()
        }
      },
      deep: true
    }
  },
  mounted () {
    this.activityID = this.$route.query.id
    if (this.activityID) {
      this._getActivityInfo()
      this._getActivitySchoolList()
    }
    if (window.localStorage.getItem('actived')) {
      const obj = JSON.parse(window.localStorage.getItem('actived'))
      const currActive = obj[this.activityID]
      if (currActive) {
        this.activityForm = currActive.activityForm
        this.fileList = currActive.fileList

        const keys = Object.keys(currActive.beforeUp)
        keys.map(val => {
          this.beforeUp.set(val, currActive.beforeUp[val])
        })
      }
    }

    const height = document.documentElement.clientHeight
    window.onresize = () => { // 在页面大小发生变化时调用
    // 把获取到的高度赋值给根div
      document.getElementById('app').style.height = height + 'px'
    }
  },
  methods: {
    async _getActivitySchoolList () {
      const { data } = await getActivitySchoolList({ activityId: this.activityID })
      this.schoolList = data
    },
    async _getActivityInfo () {
      const { data } = await getActivityInfo({ activityId: this.activityID })
      this.list = data
      this.formShowList = JSON.parse(data.worksForm)
      this.formShowListMap = new Map()
      this.requireList = ['name']
      this.formShowList.map(val => {
        if (val.required) {
          if (val.type === 'school_id') {
            this.requireList.push({
              type: 'schoolId',
              title: '所在学校'
            })
          } else {
            this.requireList.push({
              type: val.type,
              title: val.title
            })
          }
        }
        switch (val.type) {
          case 'author':
            this.formShowListMap.set('author', val)
            break
          case 'mobile':
            this.formShowListMap.set('mobile', val)
            break
          case 'school_id':
            this.formShowListMap.set('schoolId', val)
            break
          case 'age':
            this.formShowListMap.set('age', val)
            break
          case 'email':
            this.formShowListMap.set('email', val)
            break
          case 'part':
            this.formShowListMap.set('part', val)
            break
          case 'gender':
            this.formShowListMap.set('gender', val)
            break
          case 'adviser':
            this.formShowListMap.set('adviser', val)
            break
        }
      })
    },
    beforeSubmitCheck () {
      let str = []
      this.requireList.map(val => {
        if (this.activityForm[val.type] === null || this.activityForm[val.type] === '') {
          str.push(val.title)
        }
      })
      if (this.beforeUp.size === 0) {
        str.push('作品')
      }
      if (str.length > 0) {
        str = str.join(',') + '未填写'
        this.$message.error(str)
        return
      }

      if (this.activityForm.email && !validEmail(this.activityForm['email'])) {
        this.$message.error('邮箱填写错误')
        return
      }
      if (this.activityForm.mobile && !validMobile(this.activityForm['mobile'])) {
        this.$message.error('手机号填写错误')
        return
      }

      let isUpDone = true
      for (const val of this.beforeUp.values()) {
        if (!val.hasUpSuccese) {
          isUpDone = false
        }
      }

      if (!isUpDone) {
        this.$message.error('有文件还未上传完成')
        return
      }
      this.showPop = true
    },
    async submit () {
      const resourceList = []
      for (const val of this.beforeUp.values()) {
        resourceList.push(val)
      }
      this.activityForm.resourceList = resourceList
      try {
        if (!this.activityForm.schoolId) {
          this.activityForm.schoolId = 0
        }
        const { data } = await submitActivityWorks({ activityId: this.activityID, ...this.activityForm })
        window.localStorage.setItem('h5token', data)
        const obj = JSON.parse(window.localStorage.getItem('actived'))
        delete obj[this.activityID]
        window.localStorage.setItem('actived', JSON.stringify(obj))
        this.$message.success('提交成功')
        this.showPop = false
        this.$router.push({ path: '/h5/check?id=' + this.activityID })
      } catch (error) {
        // this.$message.error(error)
        this.showPop = false
      }
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    async beforeUpload (file) {
      const mimeType = file.name.split('.').pop().toLowerCase()
      if (['jpg', 'jpeg', 'png', 'pdf', 'mp4'].indexOf(mimeType) === -1) {
        this.$message.error('请上传jpg、jpeg、png、mp4、pdf格式文件')
        this.dataObj = {
          key: '',
          policy: '',
          OSSAccessKeyId: '',
          'success_action_status': '200',
          callback: '',
          signature: ''
        }
        return Promise.reject()
      }
      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }
      const { data } = await this.getOssSign(mediaType, file.name)
      this.ossUrl = data[0].ossConfig.host
      this.dataObj.key = data[0].fileName
      this.dataObj.policy = data[0].policy
      this.dataObj.OSSAccessKeyId = data[0].ossConfig.accessKeyId
      this.dataObj.signature = data[0].signature
      const filename = file.name
      const uid = file.uid
      this.beforeUp.set(uid, {
        type: mediaType,
        hasUpSuccese: false,
        fileName: filename,
        url: data[0].fileName,
        expendType: filename.substring(filename.lastIndexOf('.') + 1)
      })
    },
    handleUploadExceed (file) {
      this.$message.error('最多上传10个文件')
    },
    handleRemove (file, fileList) {
      if (this.beforeUp.has(file.uid)) {
        this.beforeUp.delete(file.uid)
      }
      this.fileList = fileList
    },
    handleUploadSuccess (res, file, fileList) {
      if (file.status === 'success') {
        const files = this.beforeUp.get(file.uid)
        if (files) {
          files.hasUpSuccese = true
          this.beforeUp.set(file.uid, files)
          this.fileList = fileList
        } else {
          this.fileList = fileList.slice(0, -1)
        }
      }
      // this.fileList = fileList
    },
    handleInputBlur () {
      const entries = this.beforeUp.entries()
      const beforeUp = {}
      for (const [key, value] of entries) {
        beforeUp[key] = value
      }
      const obj = {
        [this.activityID]: { activityForm: this.activityForm, fileList: this.fileList, beforeUp }
      }
      window.localStorage.setItem('actived', JSON.stringify(obj))
    }
  }
}
</script>

<style lang="scss" scoped>
.h5-content {
  width: 100%;
  height: 100%;
  min-height: 100%;
  background: #f0f8ff;

  .head {
    background: #fff;
    margin-bottom: 5px;
    font-size: 12px;
    color: #151414;
    padding: 20px;
    text-align: center;

    .title {
      font-weight: 500;
      font-size: 14px;
      color: #151414;
      line-height: 22px;
      text-align: center;
      @include ellipses(2);
      margin-bottom: 10px;
    }
  }
  .info-box {
    background: #fff;
    margin-bottom: 5px;
    padding: 20px;
  }

  .form-box {
    display: flex;
    padding: 10px 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .form-required {
      width: 14px;
      height: 14px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .form-name {
      min-width: 80px;
      font-size: 14px;
      color: #151414;
    }
    .form-content {
      flex: 1;
      min-width: 0;
      .el-select {
        width: 100%;
      }
      ::v-deep .el-input__inner {
        border: none;
      }
    }
  }
  .form-bottom {
    border-bottom: 1px solid #f8f8f8;
  }
}

.upload {
  width: 100%;
  min-height: 90px;
  border: 1px solid #f2f1f1;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #717171;

  ::v-deep .el-upload-list {
    align-self: flex-start;
    width: 100%;
  }
}

.info-title {
  height: 40px;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  color: #151414;
  background: #fff;
  border-bottom: 1px dashed rgba(54, 115, 169, 0.11);
  img {
    width: 20px;
    height: 10px;
    margin-right: 10px;
  }
}
.f12 {
  font-size: 12px;
}

.f14 {
  font-size: 14px;
}

.btn-box {
  background: #f0f8ff;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  .btn {
    border-radius: 15px;
    width: 80%;
    min-width: 200px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    background: #1f66ff;
    color: #fff;
  }
}
</style>
