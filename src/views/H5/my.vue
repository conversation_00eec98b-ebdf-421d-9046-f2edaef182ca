<template>
  <div class="my">
    <div v-if="isApp" class="btn arrow-left" @click="close">
      <van-icon name="arrow-left" color="white" size="22" />
    </div>
    <div class="change-name mt10">孩子信息</div>
    <div class="relative w flex justify-center">
      <img class="change-person" src="../../assets/H5/changePerson.svg" @click="show = true" />
      <el-upload
        :action="uploadUrl"
        :show-file-list="false"
        :headers="handleHeader"
        :accept="handleAccept"
        :on-error="handleError"
        :on-progress="handleProgress"
        :on-success="handleAvatarSuccess"
        :before-upload="beforeAvatarUpload"
      >
        <div class="relative">
          <el-avatar
            v-if="userInfo && userInfo.headUrl"
            class="avatar"
            :size="60"
            :src="userInfo && userInfo.headUrl"
          />
          <el-avatar v-else class="avatar" :size="60" :src="profile" />
          <img class="edit-avatar" src="../../assets/images/dashboard/editAvatar.png" alt="" />
        </div>
      </el-upload>

    </div>
    <div class="change-name mt10">{{ currChild && currChild.displayName }}</div>
    <div v-if="!isApp" class="change-phone mt10" @click="showPhone = true">
      {{ $store.getters.mobile || '绑定手机' }}
      <i class="el-icon-arrow-right"></i>
    </div>

    <div class="list-box">
      <div class="my-list mt20" @click="$router.push({ path: `/h5/my-info` })">
        <div>孩子信息</div>
        <i class="el-icon-arrow-right"></i>
      </div>
      <div class="my-list" @click="$router.push({ path: `/h5/my-jion` })">
        <div>孩子参赛</div>
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>

    <div class="bottom-footer">
      <div class="footer-box hilight" @click="$router.push({ path: '/h5/index' })">
        <img :src="dfHouse" alt="" />
        <span style="color:#BDBDBD">首页</span>
      </div>
      <div class="footer-box">
        <img :src="dfMine" />
        <span style="color:#2F80ED">我的</span>
      </div>
    </div>

    <van-popup
      v-model="show"
      round
      closeable
      close-icon="clear"
      position="bottom"
      :style="{ 'min-height': '40%', 'max-height': '90%' }"
    >
      <div class="pop-chang">
        <div class="title">选择学生或绑定</div>
        <div class="p-content">
          <div
            v-for="item in childsList"
            :key="item.id"
            class="p-btn flex justify-center items-center"
            @click="selectCurr(item)"
          >{{ item.displayName }}</div>
        </div>
        <div
          class="p-btn flex justify-center items-center"
          style="color: #828282;"
          @click="$router.push({ path: '/h5/info' })"
        ><van-icon size="20" name="plus" /> 新增</div>
        <div v-if="!isApp" class="p-btn flex justify-center items-center" style="color: #828282;" @click="quite">退出登录</div>
      </div>
    </van-popup>

    <van-popup
      v-model="showPhone"
      round
      closeable
      close-icon="clear"
      position="bottom"
      :style="{ 'min-height': '40%', 'max-height': '90%' }"
    >
      <div class="pop-chang-phone">
        <div class="title">更换手机号</div>
        <div class="p-login-input">
          <van-field v-model="phone" type="number" clearable placeholder="输入手机号" />
        </div>
        <div class="p-login-input">
          <van-field v-model="sms" center clearable placeholder="输入验证码">
            <template #button>
              <div class="sms" @click="getCode">
                {{
                  smsDisabled
                    ? countdown > 0
                      ? countdown + 's后重新获取'
                      : '获取验证码'
                    : '获取验证码'
                }}
              </div>
            </template>
          </van-field>
        </div>
        <div class="p-btn" @click="submitMobile">确定</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import house from '@/assets/H5/house.svg'
import dfHouse from '@/assets/H5/house-default.svg'
import mine from '@/assets/H5/mine.svg'
import dfMine from '@/assets/H5/mine-default.svg'
import localStore from '@/utils/local-storage.js'
import { getParentChildren, getChildInfo, unBindUser, updateMobileOrEmail } from '@/api/partent-api'
import { validMobile } from '@/utils/validate'
import { verifyCodeForWeb } from '@/api/user-api'
import profile from '../../assets/images/profile.png'
import { getPartentToken } from '@/utils/auth'
import { isApp } from '@/utils/index.js'
export default {
  data () {
    return {
      profile,
      house,
      dfHouse,
      mine,
      dfMine,
      childsList: [],
      show: false,
      showPhone: false,
      currChild: null,
      phone: '',
      sms: '',
      smsDisabled: false,
      countdown: 0,
      userInfo: null,
      uploading: false,
      handleAccept: 'image/*',
      handleHeader: {
        Authorization: getPartentToken()
      },
      isApp: isApp()
    }
  },
  computed: {
    uploadUrl: function () {
      return `${process.env.VUE_APP_BASE_API}/api/v1/students/avatar?childUserId=${this.currChild && this.currChild.id}`
    }
  },
  mounted () {
    const currChild = JSON.parse(localStore.read('currChild'))
    this.currChild = currChild
    this._getParentChildren()
  },
  methods: {
    close() {
      if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.bingo_download) {
        window.webkit.messageHandlers.bingo_action.postMessage('event_back')
      } else if (window.bingo_action) {
        window.bingo_action.postMessage('event_back')
      }
    },
    async _getParentChildren () {
      const { data } = await getParentChildren()
      const name = this.$route.query.name
      this.childsList = data
      if (name) {
        data.forEach((val) => {
          // 从已绑定的学生中筛选
          if (val.displayName + '' === name) {
            this.currChild = val
            localStore.save('currChild', JSON.stringify(val))
          }
        })
      } else if (this.currChild) {
        let flag = false
        data.forEach((val) => {
          if (val.id + '' === this.currChild.id + '') {
            flag = true
          }
        })
        if (!flag) {
          if (data && data.length > 0) {
            localStore.save('currChild', JSON.stringify(data[0]))
            this.currChild = data[0]
          }
        }
      } else {
        if (data && data.length > 0) {
          localStore.save('currChild', JSON.stringify(data[0]))
          this.currChild = data[0]
        }
      }
      this._getChildInfo()
    },
    async _getChildInfo () {
      if (!this.currChild) return
      const { data } = await getChildInfo({ childId: this.currChild.id })
      this.userInfo = data
    },
    selectCurr (item) {
      this.currChild = item
      localStore.save('currChild', JSON.stringify(item))
      this._getChildInfo()
      this.show = false
    },
    async quite () {
      const params = {
        loginType: 'WECHAT_GZH'
      }
      try {
        await unBindUser(params)
        await this.$store.dispatch('user/FedLogOut')
        localStore.clear('currChild')
        this.$router.push({ path: `/h5/index` })
      } catch (error) {
        console.log(error)
      }
    },
    getCode () {
      if (this.smsDisabled) return
      const mobile = this.phone
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this._verifyCodeForWeb()
    },
    _verifyCodeForWeb () {
      verifyCodeForWeb({ mobile: this.phone }).then(res => {
        this.countdown = 60
        this.smsDisabled = true
        setTimeout(this.tick, 1000)
      })
    },
    tick () {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    },
    async submitMobile () {
      try {
        if (this.phone && this.sms) {
          await updateMobileOrEmail({ mobileOrEmailNew: this.phone, code: this.sms })
          // setPartentToken('Bearer ' + data.access_token)
          // this.$store.commit('user/SET_MOBILE', this.popPhone)
          // this.$store.commit('user/SET_PARTENT_TOKEN', 'Bearer ' + data.access_token)
          location.reload()
          this.showPhone = false
        }
      } catch (error) {
        console.log(error)
      }
    },
    handleProgress () {
      this.uploading = true
    },
    handleError (e) {
      this.$message.error(e)
      this.uploading = false
    },
    handleAvatarSuccess (response, file) {
      this.uploading = false
      if (response.code === '200') {
        this._getChildInfo()
      } else {
        this.$message.error(response.message)
      }
    },
    beforeAvatarUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 10MB!')
        this.uploading = false
      }
      return isLt2M
    }
  }
}
</script>

<style lang="scss" scoped>
   .btn {
        position: fixed;
        background: rgba(0, 0, 0, 0.3);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        z-index: 11;
    }

    .arrow-left {
      display: flex;
      align-items: center;
      justify-content: center;
      top: 10px;
      left: 10px;
    }
.my {
  width: 100%;
  height: 100%;
  background: #FFFFFF;

  .mt10 {
    margin-top: 10px;
  }

  .mt20 {
    margin-top: 20px;
  }

  .avatar {
    margin-top: 10px;
    height: 60px !important;
    width: 60px !important;
    line-height: 60px !important;
    ::v-deep img{
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  .change-person {
    position: absolute;
    width: 35px;
    height: 35px;
    top: 5px;
    right: 10px;
    cursor: pointer;
  }

  .change-name {
    width: 100%;
    display: flex;
    justify-content: center;
    font-weight: 500;
    font-size: 14px;
    color: #000;
  }

  .change-phone {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
  }

  .list-box {
    padding: 0 15px;
  }

  .my-list {
    width: 100%;
    height: 90px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 18px;
    border-bottom: 1px solid #F2F2F2;
    padding: 0 5px;

    // &:last-child {
    //   border-bottom: none;
    // }
  }

  .bottom-footer {
    position: fixed;
    bottom: 0;
    background: #FFFFFF;
    width: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom);
    /*兼容 IOS>11.2*/
    display: flex;
    height: 85px;
    box-sizing: content-box;

    .footer-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 7px;
      flex: 1;
      padding: 15px 0;

      .hilight {
        color: #FFFFFF;
      }

      img {
        width: 36px;
        height: 36px;
        object-fit: contain;
      }

      span {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        color: #FFFFFF;
      }
    }
  }
}

.pop-chang {
  height: calc(100% - 60px);
  margin-top: 50px;
  padding: 0 90px 10px 90px;
  box-sizing: border-box;
  overflow-y: auto;

  .title {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
  }

  .p-content {
    max-height: 55vh;
    overflow-y: auto;
    padding-bottom: 20px;
    box-sizing: border-box;
  }

  .p-btn {
    width: 100%;
    height: 50px;
    font-weight: 500;
    font-size: 20px;
    color: #000000;
    background: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 10px;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.pop-chang-phone {
  height: calc(100% - 60px);
  margin-top: 30px;
  padding: 0 20px 10px 20px;
  box-sizing: border-box;
  overflow-y: auto;

  .title {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
  }

  .p-btn {
    width: 100%;
    height: 50px;
    font-size: 16px;
    color: #FFFFFF;
    background: #2F80ED;
    border-radius: 10px;
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.p-login-input {
  border: 1px solid #E0E0E0;
  margin-top: 20px;
  border-radius: 5px;
  overflow: hidden;

  ::placeholder {
    color: #828282;
    font-size: 12px;
  }

  .sms {
    color: #000000;
    font-size: 12px;
  }
}
.edit-avatar {
  position: absolute;
  right: 0;
  bottom: 0;
}
</style>
