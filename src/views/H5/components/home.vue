<template>
  <div ref="home" class="activity-home">
    <div v-if="isApp" class="btn arrow-left" @click="close">
      <van-icon name="arrow-left" color="white" size="22" />
    </div>
    <swiper
      v-if="bannerList"
      ref="swiper"
      class="swiper"
      :options="swiperOption"
    >
      <swiper-slide v-for="banner in bannerList" :key="banner.id">
        <div class="banner">
          <img :src="banner.image" alt="" />
        </div>
      </swiper-slide>
      <div slot="pagination" class="swiper-pagination"></div>
    </swiper>

    <div class="header" :class="{ sticky: isHeaderSticky }">
      <div class="title">活动列表</div>
      <!-- 开会的时候讨论出来是时间筛选，开完会后突然说不要然后装失忆的产品经理定制的需求，如果你看不明白的功能不要惊讶，因为我也不知道，说不定产品自己都不知道 -->
      <!-- <div class="time" @click="showDatePicker = true">
        {{ selectStartDate ? `${formatYYYYMMDD(selectStartDate)}-${formatYYYYMMDD(selectEndDate)}` : '时间' }}
        <div :class="[!showDatePicker ? 'trigle-three' : 'trigle-three-reverse']"></div>
      </div> -->
      <div class="time" @click="showStatusPicker = true">
        {{ status.text === '全部' ? '状态' : status.text }}
        <div :class="[!showStatusPicker ? 'trigle-three' : 'trigle-three-reverse']"></div>
      </div>
      <!-- <div class="more">更多<van-icon name="arrow" /></div> -->
    </div>

    <div v-if="workList && workList.length > 0" class="grid-cols-2" :class="{ pt48: isHeaderSticky }">
      <div v-for="item in workList" :key="item.id" class="grid-box" @click="toDetail(item)">
        <div class="cover">
          <img class="w-full h-full item-cover" :src="item.cover" alt="" />
          <div
            class="status"
            :class="{
              'status-ongoing': item.progressStatus === 'GOING',
              'status-incoming': item.progressStatus === 'COMING'
            }"
          >{{ statusJson[item.progressStatus] }}</div>
        </div>
        <div class="activity-name">{{ item.name || '' }}</div>
        <div class="activity-time">{{ formatDot(item.startTime) }}-{{ formatDot(item.endTime) }}</div>
      </div>
    </div>
    <div v-else class="empty-box">
      <img :src="IconEmpty" alt="" />
      <span>暂无数据</span>
    </div>

    <!-- <van-popup v-model="showDatePicker" position="bottom">
      <div class="flex">
        <van-datetime-picker
          v-model="startDate"
          style="flex: 1"
          type="date"
          title="开始时间"
          :max-date="endDate"
          confirm-button-text=" "
          @cancel="showDatePicker = false"
        />
        <van-datetime-picker
          v-model="endDate"
          style="flex: 1"
          type="date"
          title="结束时间"
          :max-date="maxDate"
          cancel-button-text=" "
          @confirm="selectTime"
        />
        <div class="all-btn" @click="chongzhi">全部</div>
      </div>
    </van-popup> -->

    <van-popup v-model="showStatusPicker" position="bottom">
      <van-picker
        title="状态筛选"
        show-toolbar
        :columns="statusList"
        @confirm="statusConfirm"
        @cancel="showStatusPicker = false"
      />
    </van-popup>

  </div>
</template>

<script>
import IconEmpty from '@/assets/H5/icon-empty.svg'
import 'swiper/css/swiper.css'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import { formatYYYYMMDD, formatDot } from '@/utils/date.js'
import { getActivityList, getBannerList } from '@/api/activity-api.js'
import { isApp } from '@/utils/index'
export default {
  components: {
    Swiper,
    SwiperSlide
  },
  data () {
    return {
      IconEmpty,
      formatYYYYMMDD,
      formatDot,
      swiperOption: {
        slidesPerView: 'auto',
        spaceBetween: 30,
        loop: true,
        centeredSlides: true,
        loopFillGroupWithBlank: true,
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        autoplay: {
          disableOnInteraction: false, // 手动拖拽轮播图后不关闭自动轮播
          delay: 2000
        },
        on: {
          click: (e) => {
            const index = this.bannerList.findIndex((i) => i.image === e.target.attributes.src.nodeValue)
            this.toBannerDetail(this.bannerList[index])
          }
        },
        observer: true,
        observeParents: true
      },
      // showDatePicker: false,
      statusList: [{ text: '全部', value: undefined }, { text: '进行中', value: 'GOING' }, { text: '已结束', value: 'FINISHED' }, { text: '未开始', value: 'COMING' }],
      status: { text: '全部', value: undefined },
      maxDate: new Date(),
      startDate: new Date(),
      endDate: new Date(),
      selectStartDate: undefined,
      selectEndDate: undefined,
      showStatusPicker: false,
      workList: undefined,
      statusJson: {
        'GOING': '进行中',
        'FINISHED': '已结束',
        'COMING': '未开始'
      },
      bannerList: undefined,
      isHeaderSticky: false,
      isApp: isApp()
    }
  },
  created () {
    this._getActivityList()
    this._getBannerList()
    window.addEventListener('scroll', this.handleScroll, true)
  },
  beforeDestroy () {
    // 移除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll, true)
  },

  methods: {
    close() {
      if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.bingo_download) {
        window.webkit.messageHandlers.bingo_action.postMessage('event_back')
      } else if (window.bingo_action) {
        window.bingo_action.postMessage('event_back')
      }
    },
    // selectTime (date) {
    //   this.showDatePicker = false
    //   this.selectStartDate = this.startDate
    //   this.selectEndDate = this.endDate
    // },
    // chongzhi () {
    //   this.showDatePicker = false
    //   this.startDate = new Date()
    //   this.endDate = new Date()
    //   this.selectStartDate = undefined
    //   this.selectEndDate = undefined
    // }
    statusConfirm (status) {
      this.showStatusPicker = false
      if (this.status.value === status.value) return
      this.status = status
      this._getActivityList()
    },
    async _getBannerList () {
      const params = {
        'homePageRecommendType': 'ACTIVITY'
      }
      const res = await getBannerList(params)
      this.bannerList = res.data
    },
    async _getActivityList () {
      const params = {
        'activityType': 'WORK_REVIEW',
        'status': this.status.value
      }
      const res = await getActivityList(params)
      this.workList = res.data
      const container = this.$refs.home
      if (container.scrollTop > 170) container.scrollTop = 170
      this.$nextTick(() => {
        const activityNames = document.querySelectorAll('.activity-name')
        activityNames.forEach(activityName => {
          const textLength = activityName.textContent.length
          if (textLength < 15) {
            activityName.style.paddingTop = '20px'
          }
        })
      })
    },
    toBannerDetail (banner) {
      switch (banner.redirectType) {
        case 'H5':
          if (!banner.url) return
          location.href = banner.url
          break
        case 'ACTIVITY':
          this.$router.push({
            'path': '/h5/activity',
            'query': {
              'activityId': banner.redirectId
            }
          })
          break
      }
    },
    toDetail (item) {
      this.$router.push({
        'path': '/h5/activity',
        'query': {
          'activityId': item.id
        }
      })
    },
    handleScroll () {
      const container = this.$refs.home
      this.isHeaderSticky = container.scrollTop > 170
    }
  }
}

</script>

<style lang="scss" scoped>
.activity-home {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background: white;
    overflow: scroll;
    overflow-x: hidden;
    @include noScrollBar;
    .btn {
        position: fixed;
        background: rgba(0, 0, 0, 0.3);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        z-index: 11;
    }

    .arrow-left {
      display: flex;
      align-items: center;
      justify-content: center;
      top: 10px;
      left: 10px;
    }
    .swiper {
        width: 100%;
        height: 170px;

        .banner {
          width: 100%;
          height: 100%;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
    }

    .header {
        display: flex;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        padding: 0 10px;
        height: 48px;
        background: white;

        .title {
            position: relative;
            padding-left: 10px;
            line-height: 48px;

            &::after {
                content: '';
                width: 5px;
                height: 18px;
                background: #3479FF;
                border-radius: 1px;
                position: absolute;
                left: 0;
                transform: translate(0, -50%);
                top: 50%;
            }
        }

        .time {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 2px;
            // margin-right: 18px;
        }

        .trigle-three {
          width: 0;
          height: 0;
          border-top: 4px solid #4A4A4A;
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
        }

        .trigle-three-reverse {
          width: 0;
          height: 0;
          border-bottom: 4px solid #4A4A4A;
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
        }
    }

    .pt48 {
      padding-top: 48px !important;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 999;
      /* Additional styles for the sticky header */
    }

    .grid-cols-2 {
        width: 100%;
        display: grid;
        gap: 12px;
        padding: 0 10px 10px;

        .grid-box {
            width: 100%;
            box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            position: relative;
            .cover {
                width: 100%;
                height: 120px;
                position: relative;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  border-radius: 10px;
                }
            }

            .status {
              position: absolute;
              width: 55px;
              height: 25px;
              top: 4px;
              right: 4px;
              font-weight: 400;
              font-size: 12px;
              line-height: 24px;
              color: #FFFFFF;
              text-align: center;
              background: rgba(51, 51, 51, 0.6);
              border: 1px solid rgba(255, 255, 255, 0.6);
              border-radius: 6px;
            }

            .status-ongoing {
              background: rgba(39, 174, 96, 0.7);
              border: 1px solid rgba(255, 255, 255, 0.6);
            }

            .status-incoming {
              background: rgba(242, 153, 74, 0.8);
              border: 1px solid rgba(255, 255, 255, 0.6);
            }

            .activity-name {
                font-weight: 400;
                font-size: 12px;
                color: #000000;
                height: 33px;
                margin: 10px 0;
                vertical-align: bottom;
                @include ellipses(2);
                padding: 5px;
                margin-bottom: 25px;
            }

            .activity-time {
                font-family: 'Inter';
                font-style: normal;
                font-weight: 400;
                font-size: 12px;
                line-height: 15px;
                color: #4F4F4F;
                padding: 0 5px 5px;
                position: absolute;
                bottom: 0;
                left: 0;
            }
        }
    }

    .empty-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;
      margin-top: 100px;

      img {
        width: 65px;
        height: 65px;
        object-fit: contain;
      }

      span {
        font-weight: 300;
        font-size: 14px;
        line-height: 20px;
      }
    }
}
</style>

<style lang="scss">
.activity-home {

  .all-btn {
    position: absolute;
    transform: translate(-50%, 0);
    left: 50%;
    top: 0;
    font-size: 14PX;
    background-color: transparent;
    border: none;
    cursor: pointer;
    line-height: 44PX;
    color: #576b95;
  }

  .swiper-pagination-bullet {
    background: #333333;
  }

  .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: #FFFFFF;
  }
}
</style>
