<template>
  <div class="pop">
    <div class="pop-content">
      <div class="title">
        <slot name="title"></slot>
      </div>
      <div v-if="tipsShow" class="tips">
        <slot name="tips"></slot>
      </div>
      <div class="btns">
        <div class="btn1" @click="cancle">取消</div>
        <div class="btn2" @click="submit">确定</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tipsShow: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {}
  },
  methods: {
    cancle () {
      this.$emit('cancle')
    },
    submit () {
      this.$emit('submit')
    }
  }
}
</script>

<style lang="scss" scoped>
.pop {
  position: fixed;
  z-index: 9;
  background: rgba(0, 0, 0, 0.65);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .pop-content {
    background: #fff;
    width: 85%;
    min-height: 100px;
    border-radius: 11px;
    padding: 20px;
    box-sizing: border-box;
    .title {
      font-weight: 500;
      font-size: 16px;
      color: #161515;
      padding: 10px 0;
      display: flex;
      justify-content: center;
    }
    .tips {
      font-weight: 400;
      font-size: 14px;
      color: #929292;
      padding: 10px 0;
      display: flex;
      justify-content: center;
    }
    .btns {
      display: flex;
      padding: 10px 0;
      justify-content: space-around;

      .btn1,
      .btn2 {
        width: 100px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .btn2 {
        background: #1f66ff;
        border-radius: 18px;
        color: #fff;
        font-size: 14px;
      }
      .btn1 {
        background: #ffffff;
        border: 1px solid #e9e9e9;
        border-radius: 18px;
        color: #222020;
        font-size: 14px;
      }
    }
  }
}
</style>
