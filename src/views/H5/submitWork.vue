<template>
  <div class="apply" :style="pdb">
    <toolbar @prev="prev" />
    <div class="active-name">
      {{ list && list.name }}
    </div>
    <div class="active-time">
      提交截止时间：{{ (list && list.endSubmit) | formateTime }}
    </div>

    <div class="a-tag-name">
      作品信息
    </div>

    <div class="form-box">
      <div class="form-required">
        <img src="../../assets/H5/require.png" />
      </div>
      <div class="form-name">作品名称：</div>
      <div class="form-content limt-box">
        <van-field v-model="activityForm.name" placeholder="请输入" maxlength="10" show-word-limit @blur="handleInputBlur" />
      </div>
    </div>

    <div v-if="requireListFromBack.adviser" class="form-box">
      <div class="form-required">
        <img v-show="requireListFromBack.adviser.required" src="../../assets/H5/require.png" />
      </div>
      <div class="form-name">指导老师：</div>
      <div class="form-content limt-box">
        <van-field v-model="activityForm.adviser" placeholder="请输入" @blur="handleInputBlur" />
      </div>
    </div>

    <div class="form-box" style="margin-bottom: 0;">
      <div class="form-required">
        <img src="../../assets/H5/require.png" />
      </div>
      <div class="form-name">上传作品：</div>
    </div>

    <div class="upload-tips">（支持格式jpg\jpeg\png\mp4\mov）</div>

    <div>
      <el-upload
        class="upload"
        :data="dataObj"
        :action="ossUrl"
        list-type="picture"
        :multiple="false"
        :show-file-list="true"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-exceed="handleUploadExceed"
        :limit="10"
      >
        <van-button class="upload-btn" type="default">上传文件</van-button>
      </el-upload>
    </div>

    <template v-if="requireListFromBack.introduce">
      <div class="form-box" style="margin-bottom: 0; margin-top: 10px;">
        <div class="form-required">
          <img v-show="requireListFromBack.introduce.required" src="../../assets/H5/require.png" />
        </div>
        <div class="form-name">作品简介：<span class="desc">（支持文字、视频描述您的作品）</span></div>
      </div>
      <div class="jieshao">
        <van-field
          v-model="activityForm.introduce"
          type="textarea"
          :rows="5"
          placeholder="输入内容"
          maxlength="300"
          show-word-limit
          @blur="handleInputBlur"
        />
      </div>
    </template>

    <div v-if="requireListFromBack.introduce_video" class="add-video">
      <div class="form-required">
        <img v-show="requireListFromBack.introduce_video.required" src="../../assets/H5/require.png" />
      </div>
      <el-upload
        class="w"
        :data="dataObj"
        :action="ossUrl"
        :multiple="false"
        :show-file-list="introVideo.hasUpSuccese !== 'success'"
        :before-upload="beforeIntroUpload"
        :on-change="handleIntroChange"
        :file-list="fileListIntro"
      >
        <div class="flex items-center">
          <img src="../../assets/H5/video-icon.svg" />
          <div>添加讲解视频</div>
        </div>
      </el-upload>

    </div>

    <div class="form-box">
      <div v-if="introVideo.hasUpSuccese === 'success'" class="intro-video">
        <img :src="ossCDN + '/' + introVideo.url + '?x-oss-process=video/snapshot,f_png,w_300,t_0'" />
        <i class="el-icon-error" @click="clearIntroVideo"></i>
      </div>
    </div>

    <div class="a-tag-name">
      报名信息
    </div>

    <div v-if="list && list.registered" class="form-box">
      <!-- <div class="form-required">
        <img src="../../assets/H5/require.png" />
      </div> -->
      <div class="form-name">参赛组别：</div>
      <div class="form-content-show flex justify-between">
        <div class="check-name">{{ list && list.registeredPart }}</div>
      </div>
    </div>

    <div class="form-box">
      <div class="form-required">
        <img src="../../assets/H5/require.png" />
      </div>
      <div class="form-name">提交人：</div>
      <div class="form-content-show flex justify-between">
        <div class="check-name">{{ currChild && currChild.displayName }}</div>
        <div class="check" @click="$router.push({path: '/h5/info', query: { edit: 2 }})">查看</div>
      </div>
    </div>

    <div class="form-box">
      <div class="form-name">合作学生：</div>
      <div class="form-content hezuo relative">
        <div class="check-name">
          <template v-if="memberLength">
            无合作学生，则无需填写
          </template>
          <span
            v-for="item in memberIds"
            v-show="item.id !== (currChild && currChild.id)"
            :key="item.id"
            :class="currPartentChilds[item.id] ? 'blue' : ''"
            class="na"
          >
            {{ item.displayName }}
          </span>
        </div>
        <div class="add-other" @click="showOther = true">+添加</div>
      </div>
    </div>

    <!-- <div class="add-other" @click="showOther = true">添加其他参赛人</div> -->
    <van-popup
      v-model="showOther"
      round
      position="bottom"
      :style="{ 'height': '550px' }"
    >
      <div class="other_name">
        <div class="title">添加其他参赛人</div>
        <van-icon class="close" name="clear" color="#DDDDDD" size="30" @click.stop="showOther= false" />
        <div class="input-box-box">
          <van-field
            clickable
            :value="otherNum"
            placeholder="输入参赛人手机号"
            @input="numChange"
            @click="showOther = true"
          />
        </div>
        <div class="o-tips">支持已报名的参赛人</div>

        <div class="member-box">
          <div
            v-for="item in otherMenberList"
            v-show="item.id !== (currChild && currChild.id)"
            :key="item.id"
            class="person-btn"
            @click="selectMenber(item)"
          >
            <div class="flex items-center">
              <el-avatar
                class="avatar"
                :size="60"
                :src="(item && item.avatar) || dfAvatar"
              />
              {{ item.displayName }}
            </div>
            <div>
              <van-icon v-show="selectMList.indexOf(item.id) > -1" name="passed" color="#6FCF97" size="30" />
            </div>
          </div>
        </div>
        <div class="sure-btn" @click="sureMenber">确定</div>
      </div>
    </van-popup>

    <div class="rule-tips">
      <i class="el-icon-circle-check"></i>
      <div>
        提交报名即同意
        <span style="text-decoration: underline;" @click="checkXieyi">《用户报名协议》</span>
      </div>
    </div>
    <div v-if="peviewXe" class="iframe-box">
      <van-icon class="close" name="clear" color="#DDDDDD" size="20" @click="peviewXe= ''" />
      <iframe
        :src="peviewXe"
        width="100%"
        height="100%"
        frameborder="0"
        scrolling="auto"
      ></iframe>
    </div>

    <div ref="submitBottom" class="submit-btn-box">
      <div class="submit-btn" @click="beforeSubmitCheck">
        提交
      </div>
    </div>
  </div>
</template>

<script>
import { getActivityInfo, submitActivityWorks, getActivityWorkInfo } from '@/api/activity-api.js'
import { getParentChildren } from '@/api/partent-api'
import { getFileUploadAuthor } from '@/api/user-api'
import Toolbar from '@/components/H5/toolbar.vue'
import moment from 'moment'
import localStore from '@/utils/local-storage.js'
import dfAvatar from '@/assets/images/dashboard/defaultAvatar.png'

export default {
  components: { Toolbar },
  filters: {
    formateTime (date) {
      return moment(date).format('YYYY.MM.DD')
    }
  },
  data () {
    return {
      dfAvatar,
      value: '',
      dataObj: {
        key: '',
        policy: '',
        OSSAccessKeyId: '',
        'success_action_status': '200',
        callback: '',
        signature: ''
      },
      ossUrl: '',
      beforeUp: new Map(),
      fileList: [],
      pdb: { paddingBottom: '60px' },
      activityForm: {
        name: '',
        adviser: '',
        introduce: '',
        resourceList: [],
        userId: '',
        memberIds: []
      },
      activityId: '',
      list: null,
      currChild: null,
      ossCDN: '',
      introVideo: {
        type: '',
        hasUpSuccese: '',
        fileName: '',
        url: '',
        expendType: ''
      },
      fileListIntro: [],
      requireListFromBack: [],
      requireList: [
        {
          type: 'name',
          title: '作品名称'
        }
        // {
        //   type: 'adviser',
        //   title: '指导老师'
        // },
        // {
        //   type: 'introduce',
        //   title: '作品简介'
        // }
      ],
      peviewXe: '',
      showOther: false,
      otherNum: '',
      editType: '',
      otherMenberList: [],
      selectMList: [],
      selectMListObj: {},
      memberIds: {},
      currPartentChilds: null
    }
  },
  computed: {
    memberLength () {
      // 判断时剔除自身 来确定是否有合作者
      if (Object.keys(this.memberIds).length === 0) {
        return true
      } else if (Object.keys(this.memberIds).length === 1) {
        const arr = Object.values(this.memberIds)
        let index = -1
        arr.forEach((e, i) => {
          if (+e.id === +(this.currChild && this.currChild.id)) {
            index = i
          }
        })
        return index > -1
      }
      return false
    }
  },
  watch: {
    fileList: {
      handler: function (val) {
        if (val) {
          this.handleInputBlur()
        }
      },
      deep: true
    },
    fileListIntro: {
      handler: function (val) {
        if (val) {
          this.handleInputBlur()
        }
      },
      deep: true
    }
  },
  mounted () {
    this.activityId = this.$route.query.id
    // 1: 编辑
    this.editType = this.$route.query.edit
    const currChild = JSON.parse(localStore.read('currChild'))
    this.currChild = currChild
    if (this.activityId) {
      this._getActivityInfo()
      this._getParentChildren()
    }
    this.$nextTick(() => {
      this.pdb = { paddingBottom: `${this.$refs.submitBottom.clientHeight}px` }
    })
  },
  methods: {
    prev () {
      this.$router.push({
        'path': '/h5/apply',
        'query': {
          'id': this.activityId
        }
      })
    },
    async _getParentChildren () {
      const { data } = await getParentChildren()
      if (data) {
        const obj = {}
        data.map((val) => {
          obj[val.id] = val
        })
        this.currPartentChilds = obj
      }
    },
    async _getActivityInfo () {
      const obj = { activityId: this.activityId }
      if (this.currChild) {
        obj.childUserId = this.currChild.id
      }
      const { data } = await getActivityInfo(obj)
      this.list = data
      // 显示判断
      if (data.worksForm) {
        const list = JSON.parse(data.worksForm)
        const obj = {}
        list.forEach(e => {
          obj[e.type] = e
        })

        this.requireListFromBack = obj
      }
      const { data: ossData } = await this.getOssSign('IMAGE', 'file_name.png')
      this.ossCDN = ossData[0].ossConfig.ossCDN

      // 编辑模式 从后台拿数据
      if (this.editType === '1' && data.works) {
        this._getActivityWorkInfo(data.works.id)
      } else {
        // 从本地拿数据
        if (window.localStorage.getItem('actived')) {
          const obj = JSON.parse(window.localStorage.getItem('actived'))
          const currActive = obj[[this.activityId + '-' + this.currChild.id]]
          if (currActive) {
            this.activityForm = currActive.activityForm
            this.fileListIntro = []
            this.introVideo = currActive.introVideo
            this.fileList = []
            this.memberIds = currActive.memberIds

            const keys = Object.keys(currActive.beforeUp)
            keys.map(val => {
              this.beforeUp.set(val, currActive.beforeUp[val])
              this.fileList.push({
                uid: val,
                name: currActive.beforeUp[val].fileName,
                url: this.ossCDN + '/' + currActive.beforeUp[val].url
              })
            })
          }
        }
      }
    },
    async _getActivityWorkInfo (workId) {
      const obj = { activityId: this.activityId, activityWorksId: workId, childUserId: this.currChild.id }

      const { data } = await getActivityWorkInfo(obj)
      data.resourceList.map(val => {
        this.fileList.push({ uid: val.id, url: val.url, name: val.fileName })
        this.beforeUp.set(val.id, {
          uid: val.id,
          type: val.type,
          hasUpSuccese: true,
          fileName: val.fileName,
          url: val.url.split(`${this.ossCDN}/`)[1],
          expendType: val.expendType
        })
      })

      this.activityForm.name = data.name
      this.activityForm.adviser = data.adviser
      this.activityForm.introduce = data.introduce
      if (data.introduceVideo) {
        this.introVideo = {
          type: '',
          hasUpSuccese: 'success',
          fileName: '',
          url: data.introduceVideo.split(`${this.ossCDN}/`)[1],
          expendType: ''
        }
      }
      this.activityForm.memberIds = data.memberIds
      this.memberIds = data.members
    },
    beforeSubmitCheck () {
      let str = []
      if (this.beforeUp.size === 0) {
        str.push('作品')
      }
      this.requireList.map(val => {
        if (!this.activityForm[val.type]) {
          str.push(val.title)
        }
      })
      if (this.requireListFromBack.adviser && this.requireListFromBack.adviser.required && !this.activityForm.adviser) {
        str.push('指导老师')
      }
      if (this.requireListFromBack.introduce && this.requireListFromBack.introduce.required && !this.activityForm.introduce) {
        str.push('作品简介')
      }
      if (this.requireListFromBack.introduce_video && this.requireListFromBack.introduce_video.required && !this.introVideo.url) {
        str.push('讲解视频')
      }
      if (str.length > 0) {
        str = str.join(',') + '未填写'
        this.$message.error(str)
        return
      }

      let isUpDone = true
      for (const val of this.beforeUp.values()) {
        if (!val.hasUpSuccese) {
          isUpDone = false
        }
      }

      if (!isUpDone) {
        this.$message.error('有文件还未上传完成')
        return
      }

      if (this.introVideo.hasUpSuccese && this.introVideo.hasUpSuccese !== 'success') {
        this.$message.error('有文件还未上传完成')
        return
      }
      this.submit()
      // this.showPop = true
    },
    async submit () {
      const resourceList = []
      for (const val of this.beforeUp.values()) {
        resourceList.push(val)
      }
      if (this.introVideo.url) {
        this.activityForm.introduceVideo = this.introVideo.url
      }
      this.activityForm.resourceList = resourceList
      this.activityForm.userId = this.currChild.id

      try {
        const parmObj = { activityId: this.activityId, ...this.activityForm }
        if (this.list.works && this.list.works.id) {
          parmObj.id = this.list.works.id
        }
        // if (this.currChild.id && this.activityForm.memberIds.indexOf(this.currChild.id) !== -1) {
        //   this.activityForm.memberIds.splice(this.activityForm.memberIds.indexOf(this.currChild.id), 1)
        // }
        const { data } = await submitActivityWorks(parmObj)
        const obj = JSON.parse(window.localStorage.getItem('actived'))
        delete obj[[this.activityId + '-' + this.currChild.id]]
        window.localStorage.setItem('actived', JSON.stringify(obj))
        this.$message.success('提交成功')
        this.$router.push({ path: '/h5/check-work?id=' + this.activityId + '&workId=' + data.id })
      } catch (error) {
        // this.$message.error(error)
        // this.showPop = false
      }
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    async beforeUpload (file) {
      const mimeType = file.name.split('.').pop().toLowerCase()
      if (['jpg', 'jpeg', 'png', 'mp4', 'mov'].indexOf(mimeType) === -1) {
        this.$message.error('请上传jpg、jpeg、png、mp4、mov格式文件')
        this.dataObj = {
          key: '',
          policy: '',
          OSSAccessKeyId: '',
          'success_action_status': '200',
          callback: '',
          signature: ''
        }
        return Promise.reject()
      }
      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }
      const { data } = await this.getOssSign(mediaType, file.name)
      this.ossUrl = data[0].ossConfig.host
      this.dataObj.key = data[0].fileName
      this.dataObj.policy = data[0].policy
      this.dataObj.OSSAccessKeyId = data[0].ossConfig.accessKeyId
      this.dataObj.signature = data[0].signature
      this.ossCDN = data[0].ossConfig.ossCDN
      const filename = file.name
      const uid = file.uid
      this.beforeUp.set(uid, {
        type: mediaType,
        hasUpSuccese: false,
        fileName: filename,
        url: data[0].fileName,
        expendType: filename.substring(filename.lastIndexOf('.') + 1)
      })
    },
    handleUploadExceed (file) {
      this.$message.error('最多上传10个文件')
    },
    handleRemove (file, fileList) {
      if (this.beforeUp.has(file.uid)) {
        this.beforeUp.delete(file.uid)
      }
      this.fileList = fileList
    },
    handleUploadSuccess (res, file, fileList) {
      if (file.status === 'success') {
        const files = this.beforeUp.get(file.uid)
        if (files) {
          if (files.type === 'VIDEO') {
            file.url = this.ossCDN + '/' + files.url + '?x-oss-process=video/snapshot,f_png,w_300,t_0'
          }
          files.hasUpSuccese = true
          this.beforeUp.set(file.uid, files)
          this.fileList = fileList
        } else {
          this.fileList = fileList.slice(0, -1)
        }
      }
      // this.fileList = fileList
    },
    handleUploadError (err, file, fileList) {
      console.log(err)
      this.$message.error(`${file.name}上传失败，请重新上传`)
      if (this.beforeUp.has(file.uid)) {
        this.beforeUp.delete(file.uid)
      }
    },
    async beforeIntroUpload (file) {
      const mimeType = file.name.split('.').pop().toLowerCase()
      if (['mp4'].indexOf(mimeType) === -1) {
        this.$message.error('请上传mp4格式文件')
        this.dataObj = {
          key: '',
          policy: '',
          OSSAccessKeyId: '',
          'success_action_status': '200',
          callback: '',
          signature: ''
        }
        return Promise.reject()
      }
      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }
      const { data } = await this.getOssSign(mediaType, file.name)
      this.ossUrl = data[0].ossConfig.host
      this.dataObj.key = data[0].fileName
      this.dataObj.policy = data[0].policy
      this.dataObj.OSSAccessKeyId = data[0].ossConfig.accessKeyId
      this.dataObj.signature = data[0].signature
      this.ossCDN = data[0].ossConfig.ossCDN
      const filename = file.name
      this.introVideo = {
        type: mediaType,
        hasUpSuccese: 'ready',
        fileName: filename,
        url: data[0].fileName,
        expendType: filename.substring(filename.lastIndexOf('.') + 1)
      }
    },
    handleIntroChange (file) {
      if (file.status === 'success') {
        this.introVideo.hasUpSuccese = 'success'
        this.fileListIntro = []
      }
    },
    clearIntroVideo () {
      this.introVideo = {
        type: '',
        hasUpSuccese: '',
        fileName: '',
        url: '',
        expendType: ''
      }
    },
    // 本地数据记忆
    handleInputBlur () {
      const entries = this.beforeUp.entries()
      const beforeUp = {}
      for (const [key, value] of entries) {
        beforeUp[key] = value
      }
      const obj = {
        [this.activityId + '-' + this.currChild.id]: { activityForm: this.activityForm, fileList: this.fileList, beforeUp, introVideo: this.introVideo, memberIds: this.memberIds }
      }
      window.localStorage.setItem('actived', JSON.stringify(obj))
    },
    checkXieyi () {
      this.peviewXe = window.location.origin + '/xieyi.html'
    },
    async numChange (val) {
      this.otherNum = val
      if (val.length === 11) {
        const { data } = await getParentChildren({
          mobile: val,
          scene: 'ACTIVITY_MEMBER',
          sourceId: this.activityId
        })
        this.otherMenberList = data
      } else {
        this.otherMenberList = []
      }
    },
    selectMenber (val) {
      if (val.invalidType) {
        this.$toast(val.invalidType)
        return
      }
      const index = this.selectMList.indexOf(val.id)
      if (index > -1) {
        this.selectMList.splice(index, 1)
        delete this.selectMListObj[val.id]
      } else {
        this.selectMList.push(val.id)
        this.selectMListObj[val.id] = val
      }
    },
    sureMenber () {
      this.activityForm.memberIds.push(...this.selectMList)
      this.memberIds = { ...this.memberIds, ...this.selectMListObj }
      this.selectMList = []
      this.selectMListObj = {}
      this.otherMenberList = []
      this.otherNum = ''
      this.showOther = false
    }
  }
}
</script>

<style lang="scss" scoped>
.apply {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  box-sizing: border-box;
  padding-top: 50px;
  padding-bottom: 60px;
  overflow-y: auto;

  .mt10 {
    margin-top: 10px;
  }

  .active-name {
    font-size: 14px;
    line-height: 20px;
    height: 40px;
    display: -webkit-box;
    word-break: break-all;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; //需要显示的行数
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 12px;
    box-sizing: border-box;
    text-align: center;
  }

  .active-time {
    font-size: 12px;
    color: #828282;
    text-align: center;
    margin: 0 12px;
    padding: 10px 12px;
    box-sizing: border-box;
    border-bottom: 1px solid #E0E0E0;
  }

  .a-tag-name {
    height: 70px;
    display: flex;
    align-items: center;
    color: #000000;
    margin: 0 12px;
    font-size: 16px;

    &::before {
      width: 4px;
      height: 20px;
      margin-right: 5px;
      background: #2F80ED;
      border-radius: 17px;
      content: "";
    }
  }

  .upload {
    width: 100%;
    padding: 0 12px;
    box-sizing: border-box;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    font-size: 12px;
    color: #717171;

    ::v-deep .el-upload-list {
      align-self: flex-start;
      width: 100%;
    }

    .upload-btn {
      margin-top: 10px;
    }
  }

  .upload-tips {
    font-size: 14px;
    margin: 0 12px;
    color: #828282;
    padding-top: 5px;
  }

  .desc {
    font-size: 14px;
    color: #828282;
  }

  .jieshao {
    border: 1px solid #E0E0E0;
    border-radius: 10px;
    margin: 10px 12px 5px 12px;
  }

  .add-video {
    margin: 0 12px;
    font-size: 14px;
    color: #000000;
    display: flex;
    align-items: center;

    img {
      width: 21px;
      margin-right: 5px;
    }
  }

  .form-box {
    display: flex;
    align-items: center;
    margin: 0 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;

    .form-required {
      width: 14px;
      height: 14px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .form-name {
      min-width: 80px;
      font-size: 14px;
      color: #151414;
    }

    .form-content {
      flex: 1;
      min-width: 0;
      border: 1px solid #E0E0E0;
      border-radius: 3px;

      .el-select {
        width: 100%;
      }
    }

    .form-content-show {
      flex: 1;
      min-width: 0;
      font-weight: 400;
      font-size: 14px;
    }

    .check-name {
      min-height: 20px;
      color: #828282;

      .blue {
        color: #2F80ED;
      }
      .na {
        &::after {
          content: ',';
          color: #828282;
        }
        &:last-child {
          &::after {
            content: '';
            color: #828282;
          }
        }
      }
    }

    .check {
      color: #2F80ED;
    }

    .hezuo {
      padding: 5px 10px;
      line-height: 20px;

      .check-name {
        width: calc(100% - 50px);
        font-size: 14px;
      }
    }

    .limt-box {
      ::v-deep .van-field__word-limit {
        position: absolute;
        right: 0;
        top: 0;
      }
    }
  }

  .add-other {
    position: absolute;
    right: 5px;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  .rule-tips {
    padding: 10px 12px 25px 12px;
    font-weight: 300;
    font-size: 14px;
    color: #000000;
    display: flex;
    justify-content: center;
    align-items: center;

    .el-icon-circle-check {
      color: #27AE60;
      font-size: 20px;
      margin-right: 5px;
    }
  }
}

.submit-btn-box {
  position: fixed;
  width: 100%;
  box-sizing: border-box;
  left: 0;
  right: 0;
  bottom: 0;
  background: #FFFFFF;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 20px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  .submit-btn {
    width: 150px;
    height: 42px;
    background: #2F80ED;
    border-radius: 50px;
    color: #FFFFFF;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
  }
}

.intro-video {
  border-radius: 10px;
  width: 100%;
  height: 200px;
  position: relative;

  .el-icon-error {
    position: absolute;
    right: 5px;
    top: 5px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 30px;
  }

  img {
    object-fit: cover;
    border-radius: 10px;
    width: 100%;
    height: 100%;
  }
}
.iframe-box {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99999;
  background: #fff;

  .close {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

.other_name {
  height: 400px;
  padding: 10px;

  .title {
    color: #000;
    font-weight: 700;
    font-size: 18px;
    text-align: center;
    width: 100%;
  }

  .input-box-box {
    margin-top: 40px;
    height: 50px;
    border-radius: 10px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
  }

  .close {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 999;
  }

  .person-btn {
    margin-top: 10px;
    width: 100%;
    font-size: 20px;
    height: 80px;
    border-radius: 10px;
    background-color: #F2F2F2;
    color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    box-sizing: border-box;
  }
  .avatar {
    margin-right: 10px;
    flex-shrink: 0;
  }

  .member-box {
    height: 200px;
    overflow-y: auto;
  }

  .sure-btn {
    margin-top: 40px;
    width: 100%;
    font-size: 20px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    border-radius: 10px;
    background-color: #2F80ED;
    color: #fff;
  }
}

.o-tips {
  color: #4F4F4F;
  font-size: 14px;
  padding: 5px 0;
}
</style>
