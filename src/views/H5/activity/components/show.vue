<template>
  <div class="show-container">
    <toolbar class="my-toolbar" :need-home="true" @prev="prev">
      <div slot="center" class="tabbar">
        <div class="tab" @click="changeTabIndex(0)">
          <span>作品列表</span>
          <div v-show="tabIndex === 0" class="line"></div>
        </div>
        <div v-if="activityInfo.praisePerWork !== 0" class="tab" @click="changeTabIndex(1)">
          <span>排行榜</span>
          <div v-show="tabIndex === 1" class="line"></div>
        </div>
        <div class="tab" @click="changeTabIndex(2)">
          <span>优秀作品</span>
          <div v-show="tabIndex === 2" class="line"></div>
        </div>
      </div>
    </toolbar>

    <div class="content">

      <div class="header">
        <div v-if="tabIndex === 0" class="input">
          <van-field v-model="inputValue" placeholder="输入作品名字或姓名" />
          <van-icon name="search" size="24" @click="search" />
        </div>
        <!-- <div v-if="tabIndex === 2" class="label-list">
          <div
            v-for="item in awardList"
            :key="item"
            class="label"
            :class="{'select-label': currentAward === item}"
            @click="changeAward(item)"
          >{{ item }}</div>
        </div> -->
        <div v-if="tabIndex === 2 && awardList.length > 0" class="award" @click="showAwardPicker = true">
          <div class="award-name">{{ currentAward && currentAward.text || '全部分组' }}</div>
          <div :class="[!showAwardPicker ? 'trigle-three' : 'trigle-three-reverse']"></div>
        </div>

        <div v-if="partList.length > 0" class="part" @click="showPicker = true">
          <div class="part-name">{{ part && part.text || '' }}</div>
          <div :class="[!showPicker ? 'trigle-three' : 'trigle-three-reverse']"></div>
        </div>
      </div>

      <template v-if="tabIndex === 0">

        <div class="work-list">
          <van-list
            v-if="workList.length > 0"
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="_loadMoreActivityWorkList"
          >
            <div class="grid-cols-2">
              <div v-for="item in workList" :key="item.id" class="grid-box" @click="toDetail(item)">
                <div class="cover">
                  <img class="w-full h-full item-cover" :src="makeCover(item.resourceList)" alt="" />
                </div>
                <div class="activity-name">{{ item.name || '' }}</div>
                <div class="flex">
                  <div class="activity-people">
                    <div
                      class="activity-author"
                      :class="{'maxW70': item.members.length > 1}"
                    >{{ item.members[0].displayName }}</div>
                    <template v-if="item.members.length > 1">等{{ item.members.length }}人</template>
                  </div>
                  <div v-if="activityInfo.praisePerWork !== 0" class="activity-vote">投票:{{ item.praiseNum || 0 }}</div>
                </div>
                <div v-if="isInData !== 'close'" class="button-group">
                  <div class="button" :class="{'button-disable': !canVote}" @click.stop="helpPraise(item)">拉票</div>
                  <div class="button vote" :class="{'button-disable': !canVote}" @click.stop="praise(item)">立即投票</div>
                </div>
              </div>
            </div>
          </van-list>
          <div v-else class="empty-box">
            <img :src="IconEmpty" alt="" />
            <span>暂无数据</span>
          </div>
        </div>
      </template>

      <template v-if="activityInfo.praisePerWork !== 0 && tabIndex === 1">
        <van-list
          v-if="workList.length > 0"
          v-model="loading"
          class="pb30"
          :finished="finished"
          finished-text="没有更多了"
          @load="_loadMoreActivityWorkList"
        >
          <div class="rank-list">
            <div class="rank-top-box"></div>
            <div v-for="(item, index) in workList" :key="item.id" @click="toDetail(item)">
              <div
                v-if="index < 3"
                :class="{
                  'rank-top-1': index === 0,
                  'rank-top-2': index === 1,
                  'rank-top-3': index === 2
                }"
              >
                <img class="rank-icon" :src="index === 0 ? IconRank1 : index === 1 ? IconRank2 : IconRank3" alt="" />
                <img class="work" :src="makeCover(item.resourceList)" alt="" />
                <span class="fw500">{{ item.name || '' }}</span>
                <span
                  class="fw400 flex"
                >
                  <div class="author" :class="{'maxW70': item.members.length > 1}">{{ item.members[0].displayName }}</div>
                  <template v-if="item.members.length > 1">等{{ item.members.length }}人</template>
                </span>
                <span v-if="activityInfo.praisePerWork !== 0" class="fw500">{{ item.praiseNum || 0 }}票</span>
              </div>
              <div
                v-else
                class="rank-item"
              >
                <div class="rank-number">{{ index + 1 }}</div>
                <img class="work" :src="makeCover(item.resourceList)" alt="" />
                <div class="work-content">
                  <span class="fw500">{{ item.name || '' }}</span>
                  <span class="work-author">{{ item.members[0].displayName }}<template v-if="item.members.length > 1">等{{ item.members.length }}人</template></span>
                  <span v-if="activityInfo.praisePerWork !== 0" class="work-vote">{{ item.praiseNum || 0 }}票</span>
                </div>
              </div>
            </div>
          </div>
        </van-list>
        <div v-else class="empty-box">
          <img :src="IconEmpty" alt="" />
          <span>暂无数据</span>
        </div>
        <div v-if="myRankInfo && (!part || myRankInfo.part === part.text )" class="my-rank" @click="toDetail(myRankInfo)">
          <span style="flex: 1">我的排名</span>
          <span v-if="activityInfo.praisePerWork !== 0" class="score">{{ myRankInfo.praiseNum || 0 }}票</span>
          <span class="rank-number">排行{{ myRankInfo.rank }}</span>
          <van-icon name="arrow" />
        </div>
      </template>

      <template v-if="tabIndex === 2">
        <!-- <div class="label-list">
          <div
            v-for="item in awardList"
            :key="item"
            class="label"
            :class="{'select-label': currentAward === item}"
            @click="changeAward(item)"
          >{{ item }}</div>
        </div> -->

        <van-list
          v-if="currentAward && workList.length > 0"
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="_loadMoreActivityWorkList"
        >
          <div class="work-list">
            <div class="grid-cols-2">
              <div v-for="item in workList" :key="item.id" class="grid-box" @click="toDetail(item)">
                <div class="cover">
                  <img class="w-full h-full item-cover" :src="makeCover(item.resourceList)" alt="" />
                </div>
                <div class="activity-name">{{ item.name || '' }}</div>
                <div class="activity-people">
                  <div
                    class="activity-author"
                    :class="{'maxW70': item.members.length > 1}"
                  >{{ item.members[0].displayName }}</div>
                  <template v-if="item.members.length > 1">等{{ item.members.length }}人</template></div>
              </div>
            </div>
          </div>
        </van-list>
        <div v-else class="empty-box">
          <img :src="IconEmpty" alt="" />
          <span>暂无数据</span>
        </div>
      </template>

    </div>

    <van-dialog v-model="tipsShow" :show-confirm-button="false">
      <div class="praise-box">
        <template v-if="praiseSuccess">
          <van-icon class="icon" name="checked" color="#52C41A" />
          <div class="tips1">投票成功</div>
        </template>
        <template v-else>
          <van-icon class="icon" name="clear" color="#4F4F4F" />
          <div class="tips1">投票失败</div>
        </template>
        <div class="tips2">{{ tipsText }}</div>
        <div class="btn" @click="tipsShow= false">关闭</div>
      </div>
    </van-dialog>

    <van-popup v-model="showPicker" position="bottom">
      <van-picker
        title="分组筛选"
        show-toolbar
        :columns="partList"
        @confirm="partConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>

    <van-popup v-model="showAwardPicker" position="bottom">
      <van-picker
        title="奖项筛选"
        show-toolbar
        :columns="awardList"
        @confirm="awardConfirm"
        @cancel="showAwardPicker = false"
      />
    </van-popup>

  </div>
</template>

<script>
import Toolbar from '@/components/H5/toolbar.vue'
import IconRank1 from '@/assets/H5/icon-rank1.svg'
import IconRank2 from '@/assets/H5/icon-rank2.svg'
import IconRank3 from '@/assets/H5/icon-rank3.svg'
import IconEmpty from '@/assets/H5/icon-empty.svg'
import { debounce } from '@/utils/index'
import { getActivityWorkList, getUserActivityWorkRank, praiseActivityWorks } from '@/api/activity-api.js'
import { getTimestamp } from '@/utils/time.js'
import localStore from '@/utils/local-storage.js'
import { getPartentToken } from '@/utils/auth'
import { isApp } from '@/utils/index'
import moment from 'moment'
export default {
  components: { Toolbar },
  props: {
    activityInfo: {
      type: Object,
      default: undefined
    }
  },
  data () {
    return {
      IconRank1,
      IconRank2,
      IconRank3,
      IconEmpty,
      tabIndex: 0,
      inputValue: '',
      searchValue: '',
      page: 1,
      limit: 15,
      activityId: this.$route.query.activityId,
      canVote: false,
      workList: [],
      myRankInfo: undefined,
      currChild: JSON.parse(localStore.read('currChild')),
      awardList: [],
      currentAward: undefined,
      loading: false,
      finished: false,
      imagesShow: false,
      tipsShow: false,
      praiseSuccess: true,
      tipsText: '',
      showPicker: false,
      showAwardPicker: false,
      part: undefined,
      partList: []
    }
  },
  computed: {
    isInData () {
      if (this.activityInfo.praisePerWork === 0) {
        // 不支持投票
        return 'close'
      } else if (moment().isAfter(this.activityInfo && this.activityInfo.endReview)) {
        // 过了投票时间
        return 'over'
      }
      return ''
    }
  },
  async created () {
    if (this.$route.query.tabIndex && this.$route.query.tabIndex.length > 1) this.tabIndex = +this.$route.query.tabIndex.charAt(1)
    this.canVote = getTimestamp(Date()) < this.activityInfo.endTime
    if (this.activityInfo.awardList) {
      const tempAwardList = this.activityInfo.awardList.split('>')
      tempAwardList.forEach(award => {
        this.awardList.push({ text: award, value: award })
      })
      this.currentAward = this.awardList[0]
    }
    this._initPartList()
    await this._getUserActivityWorkRank()
    if (this.$route.query.part) {
      this.part = this.partList.find((part) => part.text === this.$route.query.part)
    } else if (this.tabIndex === 1 && this.partList.length > 0) {
      const tempPart = this.myRankInfo && this.partList.find((part) => part.text === this.myRankInfo.part)
      this.part = tempPart || this.partList[0]
    }
    this._getActivityWorkList()
    window['setBingoToken'] = async(val) => {
      await this.$store.dispatch('user/partentLoginApp', 'Bearer ' + val)
      await this.$store.dispatch('user/GetInfo')
    }
  },
  methods: {
    prev () {
      if (sessionStorage.getItem('from') === 'app') {
        sessionStorage.removeItem('from')
        if (window.webkit && window.webkit.messageHandlers) {
          window.webkit.messageHandlers.bingo_action.postMessage('event_back')
        } else if (window.bingo_action) {
          window.bingo_action.postMessage('event_back')
        }
      }
      this.$router.push({
        'path': '/h5/index'
      })
    },
    makeCover (resrouces) {
      const resrouce = resrouces[0]
      switch (resrouce.type) {
        case 'IMAGE':
          return resrouce.url
        case 'VIDEO':
          return resrouce.url + '?x-oss-process=video/snapshot,t_1000,m_fast'
      }
    },
    async changeTabIndex (tabIndex) {
      if (this.tabIndex === tabIndex) return
      this.$router.push({
        'path': '/h5/activity',
        'query': {
          'activityId': this.activityId,
          'tabIndex': '1' + tabIndex
        }
      })
      this.tabIndex = tabIndex
      this.inputValue = ''
      this.searchValue = ''
      this.loading = false
      this.finished = false
      this.workList = []
      this._initPartList()
      if (tabIndex === 1 && this.partList.length > 0) {
        await this._getUserActivityWorkRank()
        const tempPart = this.myRankInfo && this.partList.find((part) => part.text === this.myRankInfo.part)
        this.part = tempPart || this.partList[0]
      }
      this._getActivityWorkList()
    },
    async _getActivityWorkList () {
      if (this.loading || this.finished) return
      this.loading = true
      this.workList = []
      this.page = 1
      const params = {
        'pageNumber': this.page,
        'pageSize': this.limit,
        'activityId': this.activityId
      }
      if (this.part && this.part.value > -1) {
        params['part'] = this.part.text
      }
      switch (this.tabIndex) {
        case 0:
          params['listType'] = 'COMMON'
          params['keyword'] = this.searchValue
          break
        case 1:
          params['listType'] = 'RANK'
          break
        case 2:
          params['listType'] = 'AWARD'
          params['keyword'] = this.currentAward && this.currentAward.value
          break
      }
      const res = await getActivityWorkList(params)
      this.loading = false
      if (res.data.length < this.limit) {
        this.finished = true
      }
      this.workList = res.data
    },
    search: debounce(function () {
      this.searchValue = this.inputValue
      this.loading = false
      this.finished = false
      this._getActivityWorkList()
    }, 500, true),
    async _getUserActivityWorkRank () {
      if (!getPartentToken()) return
      const params = {
        'activityId': this.activityId,
        'childUserId': (this.currChild && this.currChild.id) || 0
      }
      const res = await getUserActivityWorkRank(params)
      this.myRankInfo = res.data
    },
    changeAward (award) {
      if (this.currentAward === award) return
      this.currentAward = award
      this.loading = false
      this.finished = false
      this._getActivityWorkList()
    },
    async _loadMoreActivityWorkList () {
      this.page += 1
      const params = {
        'pageNumber': this.page,
        'pageSize': this.limit,
        'activityId': this.activityId
      }
      if (this.part && this.part.value > -1) {
        params['part'] = this.part.text
      }
      switch (this.tabIndex) {
        case 0:
          params['keyword'] = this.searchValue
          break
        case 1:
          params['listType'] = 'RANK'
          break
        case 2:
          params['listType'] = 'AWARD'
          params['keyword'] = this.currentAward.value
          break
      }
      const res = await getActivityWorkList(params)
      this.loading = false
      if (res.data.length < this.limit) {
        this.finished = true
      }
      this.workList = this.workList.concat(res.data)
    },
    toDetail (item) {
      this.$router.push({
        path: '/h5/check-work',
        'query': {
          'id': item.activityId,
          'workId': item.id
        }
      })
    },
    helpPraise (item) {
      if (this.isInData === 'over') {
        this.$toast.fail('投票已结束')
        return
      }
      if (!this.canVote) {
        this.$toast.fail('投票已结束')
        return
      }
      localStore.save('helpPraise', 1)
      this.toDetail(item)
    },
    praise: debounce(async function (item) {
      if (this.isInData === 'over') {
        this.$toast.fail('投票已结束')
        return
      }
      if (!this.canVote) {
        this.$toast.fail('投票已结束')
        return
      }
      const token = getPartentToken()
      if (token) {
        const { data } = await praiseActivityWorks({ activityWorksId: item.id })
        if (data.success) {
          item.praiseNum += 1
          this.praiseSuccess = true
        } else {
          this.praiseSuccess = false
        }
        this.tipsText = data.msg
        this.tipsShow = true
      } else {
        if (isApp()) {
          if (window.webkit && window.webkit.messageHandlers) {
            window.webkit.messageHandlers.bingo_action.postMessage('event_no_login')
          } else if (window.bingo_action) {
            window.bingo_action.postMessage('event_no_login')
          }
          return
        }
        var hash = window.location.hash.split('#')[1]
        localStore.save('acticegoto', hash)
        this.handleGetWxCode()
      }
    }, 1000, true),
    handleGetWxCode (state = 1) {
      const appid = 'wxfa4a06f2648b3c54'
      const baseUrl = window.location.origin
      const url = `${baseUrl}/#/h5/checkPraise`
      window.location.href = `${process.env.VUE_APP_WEB_URL}get-weixin-code.html?appid=${appid}&scope=snsapi_userinfo&state=${state}&redirect_uri=${encodeURIComponent(url)}`
    },
    partConfirm (part) {
      this.showPicker = false
      if (this.part === part) return
      this.part = part
      this.loading = false
      this.finished = false
      this._getActivityWorkList()
      this._getUserActivityWorkRank()
    },
    awardConfirm (award) {
      this.showAwardPicker = false
      if (this.currentAward === award) return
      this.currentAward = award
      this.loading = false
      this.finished = false
      this._getActivityWorkList()
    },
    _initPartList () {
      if (!this.activityInfo.worksForm) return
      const worksForm = JSON.parse(this.activityInfo.worksForm)
      worksForm.map(val => {
        if (val.type === 'part') {
          if (+this.tabIndex !== 1) {
            this.partList = [{ text: '全部分组', value: -1 }]
            this.part = { text: '全部分组', value: -1 }
          } else {
            this.partList = []
          }
          const list = val.option
          for (var i = 0; i < list.length; i++) {
            this.partList.push({ text: list[i], value: i })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.show-container {
    width: 100%;
    height: 100%;
    padding-top: 50px;
    background: linear-gradient(180.36deg, #FDDB92 17.33%, #D1FDFF 91.41%);
    display: flex;
    flex-direction: column;
    position: relative;

    .my-toolbar {
        background: rgba(255, 255, 255, 0.3);

        .tabbar {
          width: 100%;
          display: flex;
          justify-content: center;
          gap: 28px;
          margin-top: 15px;
        }

        .tab {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-items: center;
        }

        span {
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          margin-bottom: 2px;
        }

        .line {
          width: 30px;
          height: 3px;
          background: #333333;
          border-radius: 18px;
        }
    }

    .content {
      flex: 1;
      width: 100%;
      box-sizing: border-box;
      padding: 0 10px 10px;
      display: flex;
      flex-direction: column;
      overflow: auto;
      overflow-x: hidden;
    }

    .header {
      display: flex;
      height: 60px;
      box-sizing: border-box;
      padding: 10px 0;
    }

    .input {
      background: rgba(255, 255, 255, 0.6);
      border: 1px solid #333333;
      border-radius: 4px;
      height: 37px;
      padding: 0 14px;
      display: flex;
      align-items: center;
      flex: 1;
    }

    .part,
    .award {
      width: 100px;
      height: 40px;
      display: flex;
      align-items: center;
    }

    .part {
      margin-left: auto;
    }

    .part-name {
      color: #4F4F4F;
      font-size: 16px;
      width: 90px;
      text-align: end;
      @include ellipses(1);
    }

    .trigle-three {
      width: 0;
      height: 0;
      border-top: 4px solid #4A4A4A;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
    }

    .trigle-three-reverse {
      width: 0;
      height: 0;
      border-bottom: 4px solid #4A4A4A;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
    }

    .empty-box {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;

      img {
        width: 65px;
        height: 65px;
        object-fit: contain;
      }

      span {
        font-weight: 300;
        font-size: 14px;
        line-height: 20px;
      }
    }

    .work-list {
      flex: 1;
      overflow: scroll;
      @include noScrollBar;
    }

    .grid-cols-2 {
      width: 100%;
      display: grid;
      gap: 10px;

      .grid-box {
          width: 100%;

          .cover {
              width: 100%;
              height: 140px;
              position: relative;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 10px;
              }
          }

          .activity-name {
              font-weight: 400;
              font-size: 14px;
              color: #000000;
              line-height: 20px;
              margin: 7px 0;
              @include ellipses(1);
          }

          .activity-people,
          .activity-vote {
              font-family: 'Inter';
              font-style: normal;
              font-weight: 400;
              font-size: 14px;
              line-height: 15px;
              color: #4F4F4F;
          }

          .button-group {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
          }

          .button {
            width: 80px;
            height: 26px;
            background: #6FCF97;
            color: #000000;
            font-weight: 400;
            font-size: 14px;
            line-height: 26px;
            text-align: center;
          }

          .vote {
            background: #F2C94C;
          }

          .button-disable {
            background: grey;
            color: white;
          }

          .activity-people {
            display: flex;
            flex: 1;
          }

          .activity-author {
            @include ellipses(1);
          }
      }
    }

    .rank-list {
      // overflow: scroll;
      // height: calc(100% - 35px);
      position: relative;
      @include noScrollBar;

      .rank-top-box {
        position: relative;
        height: 275px;
      }

      .rank-top-1,
      .rank-top-2,
      .rank-top-3 {
        width: 110px;
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      span {
        font-size: 14px;
        line-height: 20px;
        color: #000000;
      }

      .fw500 {
        font-weight: 500;
        @include ellipses(1);
      }

      .fw400 {
        font-weight: 400;
      }

      .author {
        @include ellipses(1);
      }

      .rank-top-1 {
        transform: translate(-50%, 0);
        left: 50%;
        top: 0;
      }

      .rank-top-2 {
        left: 0;
        top: 40px;
      }

      .rank-top-3 {
        right: 0;
        top: 40px;
      }

      .rank-item {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
      }

      .rank-icon {
        width: 48px;
        height: 48px;
        object-fit: contain;
      }

      .work {
        width: 110px;
        height: 100px;
        border-radius: 10px;
        object-fit: cover;
      }

      .work-content {
        display: flex;
        flex-direction: column;
        padding: 5px 0;
        box-sizing: border-box;
        justify-content: space-between;
        height: 100px;
        flex: 1;

        .fw500 {
          font-weight: 500;
          line-height: 20px;
          @include ellipses(2);
        }

        .work-author {
          color: #4F4F4F;
          @include ellipses(1);
        }

        .work-vote {
          color: #000000;
        }
      }
    }

    .my-rank {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 35px;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      padding: 0 12px;
      box-sizing: border-box;
      z-index: 11;

      span {
        color: #000000;
        font-weight: 400;
        font-size: 14px;
        line-height: 35px;
      }

      .score {
        margin-right: 17px;
      }

      .rank-number {
        margin-right: 20px;
      }
    }

    .label-list {
      display: flex;
      flex-wrap: wrap;
      row-gap: 10px;
      column-gap: 20px;
      padding: 12px 0;

      .label {
        height: 34px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 16px;
        line-height: 34px;
        text-align: center;
        padding: 0 10px;
      }

      .select-label {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    .maxW70 {
      max-width: 70px;
    }

    .maxW2 {
      max-width: 125px !important;
    }
}
</style>

<style lang="scss">
.my-toolbar .center {
  width: 100%;
}

.show-container .input .van-cell {
  background: transparent;
  padding: 0;

  ::placeholder {
    color: #4F4F4F;
    font-size: 12px;
  }
}

.show-container {

  .pb30 {
    padding-bottom: 30px;
  }
}
.praise-box {
  padding: 20px 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;

  .tips1 {
    font-size: 26px;
    color: #000000;
    margin-top: 10px;
  }

  .tips2 {
    font-size: 16px;
    margin-top: 20px;
    line-height: 30px;
  }
  .icon {
    font-size: 70px;
  }
  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 11px 10px;
    gap: 10px;
    width: 120px;
    height: 40px;
    background: #2F80ED;
    color: #FFFFFF;
    font-size: 20px;
    margin-top: 20px;
  }
}
</style>
