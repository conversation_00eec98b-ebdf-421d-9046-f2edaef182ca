<template>
  <div class="intro-container">
    <template v-if="activityInfo">
      <toolbar :need-home="true" @prev="prev">
        <template slot="right">
          <img class="share" src="@/assets/H5/share-icon.svg" alt="" @click="openSharePage" />
        </template>
      </toolbar>
      <!-- <div class="btn share" @click="openSharePage"><img :src="iconShare" alt="" /></div> -->
      <div
        v-if="activityInfo.resourceList && activityInfo.resourceList.length > 0"
        class="attachment"
        @click="toAttachment"
      >活动附件<van-icon name="arrow" color="white" />
      </div>
      <div
        class="join"
        :class="{'join-disable': isOutDate}"
        @click="join"
      ><img v-if="!isOutDate" :src="iconJoin" alt="" />{{ isOutDate ? '活动\n结束' : '参赛' }}</div>
      <div class="content">
        <img v-for="poster in posterList" :key="poster.id" class="poster" :src="poster" alt="" />
      </div>
      <div v-if="showShare" class="share-page">
        <div class="share-poster">
          <div class="share-img">
            <van-icon class="close" name="clear" size="26" @click="showShare = false" />
            <div class="title-box"><div class="title">分享给好友一起参与</div></div>
            <div v-if="!dataURL" ref="poster" class="poster-box">
              <!-- <img class="cover" :src="activityInfo.cover" alt="" /> -->
              <div class="cover" :style="`background: url(${activityInfo.cover}) center center / cover no-repeat;`"></div>
              <div class="name">{{ activityInfo.name }}</div>
              <div class="time">时间：{{ formatDot(activityInfo.startTime) }}-{{ formatDot(activityInfo.endTime) }}</div>
              <!-- <div class="flex items-center mb30">
                <div class="circle1"></div>
                <div class="dashd"></div>
                <div class="circle2"></div>
              </div> -->
              <div class="qr-box">
                <div ref="qrCode" class="qrcode"></div>
                <div class="flex flex-col items-center">
                  <span class="f1">长按识别查看活动</span>
                  <span class="f2">一起为活动助力</span>
                </div>
              </div>
            </div>
            <img v-else class="poster-img" :src="dataURL" alt="" />
          </div>
        </div>
        <div v-if="!isApp" class="bottom-dialog">
          <div class="share-box" @click="share(0)">
            <img :src="iconWechatShare" alt="" />
            <span>分享到微信</span>
          </div>
          <div class="share-box" @click="share(1)">
            <img :src="iconDownloadShare" alt="" />
            <span>保存到相册</span>
          </div>
        </div>
        <share-guide ref="shareGuide" />
      </div>
    </template>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import iconJoin from '@/assets/H5/icon-join.svg'
import iconShare from '@/assets/H5/icon-share.svg'
import iconWechatShare from '@/assets/H5/icon-wechat-share.svg'
import iconDownloadShare from '@/assets/H5/icon-download-share.svg'
import { getTimestamp } from '@/utils/time.js'
import { formatDot } from '@/utils/date.js'
import { getWxGzhShareSignature } from '@/api/activity-api.js'
import { initWechatShare } from '@/utils/index.js'
import ShareGuide from '@/components/H5/share-guide.vue'
import { isApp } from '@/utils/index.js'
import { getPartentToken } from '@/utils/auth'
import Toolbar from '@/components/H5/toolbar.vue'
export default {
  components: { ShareGuide, Toolbar },
  props: {
    activityInfo: {
      type: Object,
      default: undefined
    }
  },
  data () {
    return {
      iconJoin,
      iconShare,
      iconWechatShare,
      iconDownloadShare,
      formatDot,
      showShare: false,
      activityId: this.$route.query.activityId,
      posterList: [],
      canApply: false,
      canSubmit: false,
      inApply: false,
      inSubmit: false,
      outSubmit: false,
      isOutDate: false,
      inComing: false,
      showBack: true,
      showPoster: false,
      dataURL: undefined,
      drawPoster: false
    }
  },
  created () {
    this.posterList = this.activityInfo.posters.split('|')
    // this.canApply = getTimestamp(Date()) > this.activityInfo.startTime && getTimestamp(Date()) < this.activityInfo.endApply && !this.activityInfo.registered
    // this.canSubmit = this.activityInfo.registered && getTimestamp(Date()) < this.activityInfo.endSubmit
    this.inComing = this.activityInfo.progressStatus === 'COMING'
    this.inApply = getTimestamp(Date()) > this.activityInfo.startTime && getTimestamp(Date()) < this.activityInfo.endApply
    this.inSubmit = getTimestamp(Date()) > this.activityInfo.startTime && getTimestamp(Date()) < this.activityInfo.endSubmit
    this.outSubmit = getTimestamp(Date()) > this.activityInfo.endSubmit && getTimestamp(Date()) < this.activityInfo.endTime
    this.isOutDate = this.activityInfo.progressStatus === 'FINISHED'
    this.canApply = this.inApply && !this.activityInfo.registered
    this.canSubmit = this.inSubmit && this.activityInfo.registered
  },
  async mounted () {
    const url = location.href.split('#')[0]
    const params = {
      'url': url
    }
    const res = await getWxGzhShareSignature(params)
    // 配置分享的内容
    const shareOptions = {
      title: this.activityInfo.name,
      desc: this.activityInfo.description ?? '',
      link: location.href,
      imgUrl: this.activityInfo.cover,
      success: () => {
        // 分享成功回调
        this.$refs.shareGuide.close()
      },
      cancel: () => {
        // 分享取消回调
        console.log('分享取消')
      }
    }
    initWechatShare(res, shareOptions)
    window['setBingoToken'] = async(val) => {
      await this.$store.dispatch('user/partentLoginApp', 'Bearer ' + val)
      await this.$store.dispatch('user/GetInfo')
      this.join()
    }
  },
  methods: {
    isApp () {
      return isApp()
    },
    prev () {
      if (sessionStorage.getItem('from') === 'app') {
        sessionStorage.removeItem('from')
        if (window.webkit && window.webkit.messageHandlers) {
          window.webkit.messageHandlers.bingo_action.postMessage('event_back')
        } else if (window.bingo_action) {
          window.bingo_action.postMessage('event_back')
        }
      }
      this.$router.push({
        'path': '/h5/index'
      })
    },
    toAttachment () {
      this.$router.push({
        'path': '/h5/attachment',
        query: {
          'activityId': this.activityId
        }
      })
    },
    toSubmit () {
      this.$router.push({
        'path': '/h5/apply',
        'query': {
          'id': this.activityInfo.id
        }
      })
    },
    removeTokenFromUrl(url) {
      const urlObj = new URL(url)
      urlObj.searchParams.delete('token')
      return urlObj.toString()
    },
    async openSharePage () {
      if (isApp()) {
        const obj = JSON.stringify({
          link: this.removeTokenFromUrl(window.location.href),
          title: this.activityInfo.name,
          cover: this.activityInfo.cover,
          content: this.activityInfo.description
        })
        if (window.webkit && window.webkit.messageHandlers) {
          window.webkit.messageHandlers.share_action.postMessage(obj)
        } else if (window.share_action) {
          window.share_action.postMessage(obj)
        }
        return
      }
      this.showShare = true
      this.$nextTick(() => {
        new QRCode(this.$refs.qrCode, {
          text: window.location.href, // 需要转换为二维码的内容
          width: 100,
          height: 100,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        })
        this.$refs.qrCode.removeAttribute('title')
      })
    },
    share (type) {
      switch (type) {
        case 0:
          this.$refs.shareGuide.open()
          break
        case 1:
          this.$toast({
            message: '长按图片分享好友或保存到本地',
            position: 'center'
          })
          if (!this.drawPoster) {
            this.drawPoster = true
            var poster = this.$refs.poster
            setTimeout(() => {
              html2canvas(poster, { useCORS: true, backgroundColor: null }).then(canvas => {
              // 将 canvas 转为图片的 DataURL
                this.dataURL = canvas.toDataURL('image/png')
                const img = new Image()
                img.src = this.dataURL
                img.onload = () => {
                  this.showPoster = true
                }
              })
            })
          }
          break
      }
    },
    join () {
      if (isApp() && !getPartentToken()) {
        if (window.webkit && window.webkit.messageHandlers) {
          window.webkit.messageHandlers.bingo_action.postMessage('event_no_login')
        } else if (window.bingo_action) {
          window.bingo_action.postMessage('event_no_login')
        }
        return
      }
      this.$router.push({
        'path': '/h5/apply',
        'query': {
          'id': this.activityInfo.id
        }
      })
      // if (this.activityInfo.works) {
      //   this.$router.push({
      //     path: '/h5/check-work',
      //     'query': {
      //       'id': this.activityInfo.id,
      //       'workId': this.activityInfo.works.id
      //     }
      //   })
      // } else if (this.canApply) {
      //   this.$router.push({
      //     'path': '/h5/apply',
      //     'query': {
      //       'id': this.activityInfo.id
      //     }
      //   })
      // } else if (this.canSubmit) {
      //   this.$router.push({
      //     'path': '/h5/submit-work',
      //     query: {
      //       'id': this.activityInfo.id
      //     }
      //   })
      // } else if (this.inComing) {
      //   this.$toast({
      //     message: `活动未开始\n报名时间:${formatDot(this.activityInfo.startTime)}-${formatDot(this.activityInfo.endApply)}`
      //   })
      // } else if (this.isOutDate) {
      //   this.$toast({
      //     message: `活动结束`
      //   })
      // } else if (!this.activityInfo.registered && !this.inApply) {
      //   this.$toast({
      //     message: `报名时间结束\n报名时间:${formatDot(this.activityInfo.startTime)}-${formatDot(this.activityInfo.endApply)}`
      //   })
      // } else if (this.activityInfo.registered && !this.inSubmit) {
      //   this.$toast({
      //     message: `已过参赛时间\n参赛截止时间:${formatDot(this.activityInfo.endSubmit)}`
      //   })
      // }
    }
  }
}
</script>

<style lang="scss" scoped>
.content{
  width: 100%;
  height: 100%;
  overflow: scroll;
  overflow-x: hidden;
  @include noScrollBar;
}
.intro-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: scroll;
    overflow-x: hidden;
    @include noScrollBar;

    .btn {
        position: fixed;
        background: rgba(0, 0, 0, 0.3);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        z-index: 11;
    }

    .arrow-left {
      display: flex;
      align-items: center;
      justify-content: center;
      top: 10px;
      left: 10px;
    }

    .share {
        top: 10px;
        right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            width: 22px;
            height: 22px;
            object-fit: cover;
        }
    }

    .attachment {
        position: fixed;
        display: flex;
        align-items: center;
        background: rgba(0, 0, 0, 0.46);
        border-radius: 6px;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        padding: 5px;
        left: 10px;
        bottom: calc(constant(safe-area-inset-bottom) + 100px); /*兼容 IOS<11.2*/
        bottom: calc(env(safe-area-inset-bottom) + 100px); /*兼容 IOS>11.2*/
        z-index: 11;
    }

    .join {
        position: fixed;
        width: 67px;
        height: 67px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #27AE60;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        border-radius: 50%;
        gap: 1px;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        right: 10px;
        bottom: 140px;
        z-index: 11;
        white-space: break-spaces;

        img {
            width: 27px;
            height: 27px;
            object-fit: contain;
        }
    }

    .join-disable {
      background: rgba(0, 0, 0, 1);
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    }

    .poster {
        width: 100%;
        object-fit: contain;
    }

    .share-page {
      position: fixed;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.2);
      top: 0;
      left: 0;
      z-index: 11;
      display: flex;
      flex-direction: column;

      .share-poster {
        flex: 1;
        padding-bottom: calc(100px + constant(safe-area-inset-bottom));
        padding-bottom: calc(100px + env(safe-area-inset-bottom));
        box-sizing: border-box;
        overflow: auto;
        overflow-x: hidden;
        margin-bottom: 30px;
        @include noScrollBar;
      }

      .share-img {
        width: 340px;
        height: 593px;
        // background: #FFFFFF;
        margin: 37px auto 0;
        position: relative;
      }

      .poster-box {
        overflow: hidden;
        width: 100%;
        height: 500px;
        box-sizing: border-box;
        padding: 17px 0 37px;
        background: url('../../../../assets/H5/bg-share.png') center center no-repeat;
        background-size: contain;
      }

      .close {
        position: absolute;
        width: 26px;
        height: 26px;
        color: #575B66;
        top: 6px;
        right: 3px;
      }

      .title-box {
        background: white;
      }

      .title {
        font-weight: 500;
        font-size: 30px;
        line-height: 42px;
        background: linear-gradient(159.06deg, #085078 15.73%, #85D8CE 88.31%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-align: center;
        padding-top: 27px;
      }

      .cover {
        width: 300px;
        height: 200px;
        margin: 0 20px;
        margin-bottom: 17px;
      }

      .name {
        height: 38px;
        font-size: 16px;
        line-height: 19px;
        color: #000000;
        margin: 0 20px 17px;
        @include ellipses(2)
      }

      .time {
        font-size: 12px;
        line-height: 15px;
        color: #4F4F4F;
        padding: 0 20px;
        margin-bottom: 62px;
      }

      .circle1,
      .circle2 {
        width: 20px;
        height: 40px;
        border-radius: 0 200px 200px 0;
        background: #D9D9D9;
      }

      .mb30 {
        margin-bottom: 30px;
      }

      .dashd {
        flex: 1;
        border-bottom: 1px dashed #000000;
        margin: 0 2px;
      }

      .circle2 {
        border-radius: 200px 0 0 200px;
      }

      .qr-box {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .qrcode {
        width: 100px;
        height: 100px;
        margin-right: 34px;
      }

      .f1 {
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        color: #000000;
        margin-bottom: 6px;
      }

      .f2 {
        font-weight: 300;
        font-size: 12px;
        line-height: 17px;
        color: #000000;
      }

      .poster-img {
        width: 100%;
        object-fit: contain;
      }

      .bottom-dialog {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 100px;
        background: white;
        box-sizing: content-box;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 40px;
        padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
        padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
      }

      .share-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 60px;
          height: 60px;
          object-fit: contain;
          margin-bottom: 6px;
        }

        span {
          color: #000000;
          font-weight: 300;
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
}
</style>

<style lang="scss">
.share-page {

  .qrcode {

    img {
      width: 100%;
      height: 100%;
    }

  }
}
</style>
