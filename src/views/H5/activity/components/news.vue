<template>
  <div class="news-container">
    <toolbar :need-home="true" @prev="prev" />
    <div v-if="newsList.length > 0" class="news-list">
      <div
        v-for="item in newsList"
        :key="item.id"
        class="news-item"
        @click="toDetail(item)"
      >
        <img class="cover" :src="item.cover" alt="" />
        <div class="news-content">
          <span class="title">{{ item.title || '' }}</span>
          <span class="author">{{ item.source || '' }}</span>
          <span class="time">{{ formatYYYYMMDD(item.createdAt) }}</span>
        </div>
      </div>
    </div>
    <div v-else class="empty-box">
      <img :src="IconEmpty" alt="" />
      <span>暂无数据</span>
    </div>

  </div>
</template>

<script>
import IconEmpty from '@/assets/H5/icon-empty.svg'
import Toolbar from '@/components/H5/toolbar.vue'
import { getInformationList } from '@/api/activity-api.js'
import { formatYYYYMMDD } from '@/utils/date.js'
export default {
  components: { Toolbar },
  data () {
    return {
      activityId: this.$route.query.activityId,
      formatYYYYMMDD,
      IconEmpty,
      newsList: []
    }
  },
  created () {
    this._getInformationList()
  },
  methods: {
    prev () {
      if (sessionStorage.getItem('from') === 'app') {
        sessionStorage.removeItem('from')
        if (window.webkit && window.webkit.messageHandlers) {
          window.webkit.messageHandlers.bingo_action.postMessage('event_back')
        } else if (window.bingo_action) {
          window.bingo_action.postMessage('event_back')
        }
      }
      this.$router.push({
        'path': '/h5/index'
      })
    },
    async _getInformationList () {
      const params = {
        'useFor': 'ACTIVITY',
        'sourceId': this.activityId
      }
      const res = await getInformationList(params)
      this.newsList = res.data
    },
    toDetail (news) {
      if (!news.url) {
        this.$toast.fail('暂无配置链接地址')
        return
      }
      location.href = news.url
    }
  }
}
</script>

<style lang="scss" scoped>
.news-container {
    width: 100%;
    height: 100%;
    padding-top: 50px;
    background: white;
    position: relative;
}

.news-list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0 10px 10px;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    overflow-x: hidden;
    @include noScrollBar;
    .news-item {
        display: flex;
        gap: 6px;
        height: 92px;
        margin-bottom: 15px;

        .cover {
            width: 120px;
            height: 100%;
            border-radius: 6px;
            object-fit: cover;
        }

        .news-content {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .title {
            font-size: 16px;
            line-height: 22px;
            color: #000000;
            height: 44px;
            @include ellipses(2)
        }

        .author,
        .time {
            font-size: 12px;
            line-height: 17px;
            color: #828282;
            @include ellipses(1)
        }
    }
}

.empty-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;

  img {
    width: 65px;
    height: 65px;
    object-fit: contain;
  }

  span {
    font-weight: 300;
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
