<template>
  <div class="attachment-container">
    <toolbar @prev="prev" />
    <div class="list">
      <div v-for="(item, index) in resourceList" :key="item.id" class="item">
        <div class="name">附件{{ index + 1 }}: {{ item.fileName }}.{{ item.expendType }}</div>
        <div class="download" @click="download(item)">查看下载</div>
      </div>
    </div>
    <share-guide ref="shareGuide" title="下载附件" subtitle="点开外部浏览器下载附件" />
  </div>
</template>

<script>
import Toolbar from '@/components/H5/toolbar.vue'
import ShareGuide from '@/components/H5/share-guide.vue'
import { getActivityInfo } from '@/api/activity-api.js'
import { isApp } from '@/utils/index'
export default {
  components: { Toolbar, ShareGuide },
  data () {
    return {
      activityId: this.$route.query.activityId,
      resourceList: []
    }
  },
  created () {
    document.title = '活动附件'
    this._getActivityInfo()
  },
  methods: {
    prev () {
      this.$router.push({
        'path': '/h5/activity',
        query: { 'activityId': this.activityId }
      })
    },
    async _getActivityInfo () {
      const params = {
        'activityId': this.activityId
      }
      const res = await getActivityInfo(params)
      this.resourceList = res.data.resourceList
    },
    download (item) {
      if (isApp()) {
        this.$copyText(item.url)
        this.$toast({
          message: '链接已复制，请前往默认浏览器打开下载',
          duration: 5000
        })
        return
      }
      var u = window.navigator.userAgent
      var isIOS = u.indexOf('Android') === -1 && u.indexOf('Linux') === -1 // g
      // var ua = window.navigator.userAgent.toLowerCase()
      if (isIOS && /(micromessenger)/i.test(navigator.userAgent)) {
        this.$refs.shareGuide.open()
      } else {
        window.open(item.url, '_blank')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.attachment-container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding-top: 50px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .list {
        height: 100%;
        width: 100%;
        overflow: scroll;
        box-sizing: border-box;
        padding: 10px 15px;
        @include noScrollBar;

        .item {
            margin-bottom: 20px;
        }

        .name {
            color: #000000;
            font-size: 16px;
            line-height: 22px;
        }

        .download {
            color: #2F80ED;
            line-height: 22px;
        }
    }
}
</style>
