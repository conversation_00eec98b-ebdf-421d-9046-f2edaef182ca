<template>
  <!-- 分享作品 -->
  <div class="share-works" :style="`background: url(${cover}) center center / cover no-repeat;`">
    <!-- <div class="w flex justify-center items-center share-title">
      分享作品
      <i class="el-icon-error" @click="close"></i>
    </div> -->

    <i class="el-icon-error" @click="close"></i>

    <div class="shadow"></div>

    <div v-if="!showPoster" ref="poster" class="share-box">
      <div class="work-name">{{ info.name }}</div>
      <div class="work-person">

        <template v-if="info.members.length < 2">
          <!-- <el-avatar class="avatar" :size="46" :src="info.members[0].avatar" /> -->
          <el-avatar
            v-if="info && info.members[0] && info.members[0].avatar"
            class="avatar"
            :size="46"
            :src="info.members[0].avatar"
          />
          <el-avatar v-else class="avatar" :size="60" :src="profile" />
          <div class="person-name">{{ info.members[0].displayName }}</div>
        </template>

        <div v-else class="person-name">
          <span v-for="member in info.members" :key="member.id">{{ member.displayName }}</span>
        </div>

      </div>
      <div class="work-cover" :style="`background: url(${cover}) center center / cover no-repeat;`"></div>
      <div class="work-qr">
        <!-- <img src="https://via.placeholder.com/100" /> -->
        <div ref="qrCode" class="qrCode"></div>
        <div class="tips">长按识别查看作品</div>
      </div>
    </div>
    <img v-else :src="dataURL" class="share-img" alt="" />

    <div v-if="!isApp" class="footer">

      <div class="flex flex-col items-center" @click="share(0)">
        <div class="download-share">
          <img src="@/assets/H5/icon-wechat-share.svg" />
        </div>
        <div class="download-text">分享到微信</div>
      </div>

      <div class="flex flex-col items-center" @click="share(1)">
        <div class="download-share">
          <img src="@/assets/H5/icon-download-share.svg" />
        </div>
        <div class="download-text">保存到相册</div>
      </div>
    </div>

    <share-guide ref="shareGuide" />

  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import ShareGuide from '@/components/H5/share-guide.vue'
import profile from '../../../../assets/images/profile.png'
import { isApp } from '@/utils/index.js'
export default {
  components: { ShareGuide },
  props: {
    info: {
      type: Object,
      default: () => null
    }
  },
  data () {
    return {
      profile,
      cover: undefined,
      dataURL: undefined,
      showPoster: false,
      drawPoster: false
    }
  },
  async mounted () {
    const resrouce = this.info.resourceList[0]
    switch (resrouce.type) {
      case 'IMAGE':
        this.cover = resrouce.url
        break
      case 'VIDEO':
        this.cover = resrouce.url + '?x-oss-process=video/snapshot,t_1000,m_fast'
        break
    }
    new QRCode(this.$refs.qrCode, {
      text: window.location.href, // 需要转换为二维码的内容
      width: 100,
      height: 100,
      colorDark: '#000000',
      colorLight: '#ffffff',
      correctLevel: QRCode.CorrectLevel.H
    })
    this.$refs.qrCode.removeAttribute('title')
  },
  methods: {
    isApp () {
      return isApp()
    },
    close () {
      this.$emit('close')
    },
    share (type) {
      switch (type) {
        case 0:
          this.$refs.shareGuide.open()
          break
        case 1:
          this.$toast({
            message: '长按图片以保存',
            position: 'center'
          })
          if (!this.drawPoster) {
            this.drawPoster = true
            var poster = this.$refs.poster
            setTimeout(() => {
              html2canvas(poster, { useCORS: true, backgroundColor: null }).then(canvas => {
                // 将 canvas 转为图片的 DataURL
                this.dataURL = canvas.toDataURL('image/png')
                const img = new Image()
                img.src = this.dataURL
                img.onload = () => {
                  this.showPoster = true
                }
              })
            })
          }
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.share-works {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  // background: linear-gradient(180.36deg, #FDDB92 17.33%, #D1FDFF 91.41%);
  border-radius: 10px 10px 0px 0px;
  box-sizing: border-box;
  // padding-bottom: calc(constant(safe-area-inset-bottom) + 10px); /*兼容 IOS<11.2*/
  // padding-bottom: calc(env(safe-area-inset-bottom) + 10px); /*兼容 IOS>11.2*/;
  overflow: auto;
  z-index: 100;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .h40 {
    height: 40px;
  }

  // .share-title {
  //   font-size: 28px;
  //   color: #000;
  //   height: 50px;
  //   .el-icon-error {
  //     position: absolute;
  //     right: 5px;
  //     top: 5px;
  //     color: rgba(0,0,0,.5);
  //     font-size: 30px;
  //     cursor: pointer;
  //   }
  // }

  .el-icon-error {
    position: fixed;
    right: 5px;
    top: 10px;
    color: #FFFFFF;
    font-size: 35px;
    cursor: pointer;
    z-index: 10;
  }

  .shadow {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 0;
    background: rgba(0, 0, 0, 0.5);
    left: 0;
    top: 0;
  }

  .share-box {
    margin-top: 50px;
    // background: #FFFFFF;
    // border: 4px solid #000000;
    // border-radius: 21px;
    box-sizing: border-box;
    min-height: 100px;
    padding: 17px 13px;
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    background: linear-gradient(136deg, #FFF1EB 0%, #ACE0F9 100%);
    z-index: 10;
    width: 330px;
    margin: 50px auto 6px;

    .work-name {
      font-weight: 500;
      font-size: 18px;
    }

    .work-cover {
      // margin: 25px 0;
      // background: #FFFFFF;
      // border: 4px solid #000000;
      border-radius: 21px;
      box-sizing: border-box;
      // height: 270px;
      width: 100%;
      flex: 1;

      img {
        width: 100%;
        //height: 100%;
        //object-fit: cover;
        border-radius: 21px;
      }
    }

    .work-person {
      display: flex;
      align-items: center;
      padding: 10px 0;

      .avatar {
        height: 46px !important;
        width: 46px !important;
        line-height: 46px !important;
      }
      .person-name {
        margin-left: 10px;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
      }
    }

    .work-qr {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 8px;
      .tips {
        margin-top: 5px;
        font-weight: 300;
        font-size: 10px;
      }
    }
  }

  .share-img {
    box-sizing: border-box;
    min-height: 100px;
    z-index: 10;
    width: 330px;
    margin: 50px auto 6px;
  }

  .footer {
    display: flex;
    border-radius: 10px 10px 0px 0px;
    background: #FFF;
    width: 100%;
    padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
    z-index: 10;
    justify-content: center;
    align-items: center;
    gap: 40px;
  }

  .download-share {
    margin-top: 20px;
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.12);
    border-radius: 21px;
  }

  .download-text {
    margin-top: 5px;
    font-weight: 300;
    font-size: 14px;
  }
}
</style>

<style lang="scss">
.share-works {
  .qrCode {
    width: 100px;
    height: 100px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
