<template>
  <div class="activity-list">
    <toolbar :need-home="false" @prev="prev">
      <div slot="right" class="time" @click="showDatePicker = true">
        {{ selectStartDate ? `${formatYYYYMMDD(selectStartDate)}-${formatYYYYMMDD(selectEndDate)}` : '时间' }}
        <div :class="[!showDatePicker ? 'trigle-three' : 'trigle-three-reverse']"></div>
      </div>
    </toolbar>

    <van-pull-refresh
      v-model="isRefresh"
      success-text="刷新成功"
      class="list"
      @refresh="onRefresh"
    >
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div v-for="item in 10" :key="item" class="item">
          <div class="cover">
            <img class="w-full h-full item-cover" src="" alt="" />
            <div class="status">已结束</div>
          </div>
          <div style="flex: 1;">
            <div class="activity-name">第二十七届“成长·守护·筑梦”全国中小学生绘画书法作品比赛第二十七届“成长·守护·筑梦”全国中小学生绘画书法作品比赛</div>
            <div class="activity-time">2023.12.03—2023.01.04</div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>

    <van-popup v-model="showDatePicker" position="bottom">
      <div class="flex">
        <van-datetime-picker
          v-model="startDate"
          style="flex: 1"
          type="date"
          title="开始时间"
          :max-date="endDate"
          confirm-button-text=" "
          @cancel="showDatePicker = false"
        />
        <van-datetime-picker
          v-model="endDate"
          style="flex: 1"
          type="date"
          title="结束时间"
          :max-date="maxDate"
          cancel-button-text=" "
          @confirm="selectTime"
        />
        <div class="all-btn" @click="chongzhi">全部</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Toolbar from '@/components/H5/toolbar.vue'
import { formatYYYYMMDD } from '@/utils/date.js'
export default {
  components: { Toolbar },
  data () {
    return {
      formatYYYYMMDD,
      showDatePicker: false,
      maxDate: new Date(),
      startDate: new Date(),
      endDate: new Date(),
      selectStartDate: undefined,
      selectEndDate: undefined,
      isRefresh: false,
      loading: false,
      finished: false
    }
  },
  methods: {
    prev () {},
    selectTime (date) {
      this.showDatePicker = false
      this.selectStartDate = this.startDate
      this.selectEndDate = this.endDate
    },
    chongzhi () {
      this.showDatePicker = false
      this.startDate = new Date()
      this.endDate = new Date()
      this.selectStartDate = undefined
      this.selectEndDate = undefined
    },
    onRefresh () {
      this.isRefresh = false
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding-top: 50px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .list {
      height: 100%;
      width: 100%;
      padding: 0 10px 10px;
      overflow: scroll;
      overflow-x: hidden;
      @include noScrollBar;
    }

    .item {
        width: 100%;
        display: flex;
        align-items: flex-start;
        gap: 10px;
        box-sizing: border-box;

        .cover {
          width: 170px;
          height: 120px;
          border-radius: 10px;
          position: relative;
        }

        .status {
          position: absolute;
          width: 55px;
          height: 25px;
          top: 4px;
          right: 4px;
          font-weight: 400;
          font-size: 12px;
          line-height: 24px;
          color: #FFFFFF;
          text-align: center;
          background: rgba(51, 51, 51, 0.6);
          border: 1px solid rgba(255, 255, 255, 0.6);
          border-radius: 6px;
        }

        .status-ongoing {
          background: rgba(39, 174, 96, 0.7);
          border: 1px solid rgba(255, 255, 255, 0.6);
        }

        .status-incoming {
          background: rgba(242, 153, 74, 0.8);
          border: 1px solid rgba(255, 255, 255, 0.6);
        }

        .activity-name {
            font-weight: 400;
            font-size: 12px;
            color: #000000;
            height: 30px;
            line-height: 15px;
            margin-bottom: 10px;
            @include ellipses(2);
        }

        .activity-time {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 15px;
            color: #4F4F4F;
        }
    }

    .time {
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 2px;
      font-weight: 400;
      font-size: 14px;

      .trigle-three {
        width: 0;
        height: 0;
        border-top: 4px solid #4A4A4A;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
      }

      .trigle-three-reverse {
        width: 0;
        height: 0;
        border-bottom: 4px solid #4A4A4A;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
      }
    }
}
</style>

<style lang="scss">
.activity-list {
  .all-btn {
    position: absolute;
    transform: translate(-50%, 0);
    left: 50%;
    top: 0;
    font-size: 14PX;
    background-color: transparent;
    border: none;
    cursor: pointer;
    line-height: 44PX;
    color: #576b95;
  }
}
</style>
