<template>
  <div class="activity-container">

    <div v-if="activityInfo" class="content">
      <intro v-if="tabIndex === 0" :activity-info="activityInfo" />
      <show v-if="tabIndex === 1" :activity-info="activityInfo" />
      <news v-if="tabIndex === 2" />
    </div>

    <div class="bottom-footer">
      <div class="footer-box" @click="changeTabIndex(0)">
        <img :src="tabIndex === 0 ? intro : dfIntro" alt="" />
        <span :class="{'select': tabIndex === 0}">活动介绍</span>
      </div>
      <div class="footer-box" @click="changeTabIndex(1)">
        <img :src="tabIndex === 1 ? show : dfShow" alt="" />
        <span :class="{'select': tabIndex === 1}">作品展示</span>
      </div>
      <div class="footer-box" @click="changeTabIndex(2)">
        <img :src="tabIndex === 2 ? news : dfNews" alt="" />
        <span :class="{'select': tabIndex === 2}">活动资讯</span>
      </div>
    </div>
  </div>
</template>

<script>
import intro from '@/assets/H5/intro.svg'
import dfIntro from '@/assets/H5/intro-default.svg'
import show from '@/assets/H5/show.svg'
import dfShow from '@/assets/H5/show-default.svg'
import news from '@/assets/H5/news.svg'
import dfNews from '@/assets/H5/news-default.svg'
import Intro from './components/intro.vue'
import Show from './components/show.vue'
import News from './components/news.vue'
// import localStore from '@/utils/local-storage.js'
import { getActivityInfo } from '@/api/activity-api.js'
export default {
  components: { Intro, Show, News },
  data () {
    return {
      intro,
      dfIntro,
      show,
      dfShow,
      news,
      dfNews,
      tabIndex: 0,
      activityId: 0,
      activityInfo: undefined
    }
  },
  watch: {
    tabIndex (val) {
      console.log(this.$route.query)
      if (this.$route.query && this.$route.query.from === 'app') {
        sessionStorage.setItem('from', 'app')
      }
    }
  },
  async mounted () {
    this.activityId = this.$route.query.activityId
    if (this.$route.query.tabIndex) this.tabIndex = +this.$route.query.tabIndex.charAt(0)
    if (this.$route.query && this.$route.query.token) {
      await this.$store.dispatch('user/partentLoginApp', 'Bearer ' + this.$route.query.token)
      await this.$store.dispatch('user/GetInfo')
    }
    if (this.$route.query && this.$route.query.from === 'app') {
      sessionStorage.setItem('from', 'app')
    }
    this._getActivityInfo()
  },
  methods: {
    async _getActivityInfo () {
      // const currChild = JSON.parse(localStore.read('currChild'))
      const params = {
        'activityId': this.activityId
      }
      // if (currChild) params['childUserId'] = currChild.id
      const res = await getActivityInfo(params)
      this.activityInfo = res.data
    },
    changeTabIndex (tabIndex) {
      if (this.tabIndex === tabIndex) return
      this.tabIndex = tabIndex
      this.$router.push({
        'path': 'activity',
        'query': {
          'activityId': this.activityId,
          'tabIndex': tabIndex,
          'from': sessionStorage.getItem('from') || 'h5'
        }
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .activity-container {
      width: 100%;
      height: 100%;
  }

  .content {
      width: 100%;
      height: 100%;
      padding-bottom: calc(constant(safe-area-inset-bottom) + 85px); /*兼容 IOS<11.2*/
      padding-bottom: calc(env(safe-area-inset-bottom) + 85px); /*兼容 IOS>11.2*/
      box-sizing: border-box;
  }

  .bottom-footer {
      position: fixed;
      bottom: 0;
      background: white;
      width: 100%;
      padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
      padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
      display: flex;
      height: 85px;
      box-sizing: content-box;
      box-shadow: 0px -4px 4px rgba(0, 0, 0, 0.03);

      .footer-box {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 7px;
          flex: 1;
          padding: 15px 0;

          img {
              width: 40px;
              height: 40px;
              object-fit: contain;
          }

          span {
              font-family: 'Inter';
              font-style: normal;
              font-weight: 400;
              font-size: 12px;
              color: #4F4F4F;
          }

          .select {
            color: #2F80ED;
          }
      }
  }
  </style>

