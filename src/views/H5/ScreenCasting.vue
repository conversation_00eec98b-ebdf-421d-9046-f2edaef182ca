<template>
  <div class="homework flex flex-col">
    <template>
      <div class="content">
        <div class="title">{{titleType}}</div>
        <template>
          <div class="course-content submit-container">
            <div class="file-container">
              <!-- 图片展示 -->
              <div v-for="(file, index) in fileList" :key="`${file.name}-${index}`" class="file-box">
                <div v-if="file.status" class="shadow"></div>
                <div v-if="!file.status && file.type === 'VIDEO'" class="video-shadow" @click="showUploadPreview(1, file.url)">
                  <svg-icon
                    icon-class="video-player"
                    class-name="video-player"
                  />
                </div>
                <img
                  v-if="file.type === 'VIDEO'"
                  :key="file.image"
                  :src="file.image"
                />
                <img
                  v-else
                  :src="file.content || file.url"
                  :alt="file.file.name"
                  @click="showUploadPreview(0, file.url)"
                />
                <!-- 进度条 -->
                <van-circle
                  v-if="file.status === 'uploading'"
                  v-model="file.uploadProgress"
                  :text="`${file.uploadProgress}%`"
                  stroke-width="160"
                  size="40"
                  color="#FFFFFF"
                  fill="rgba(0, 0, 0, 0)"
                  layer-color="rgba(0, 0, 0, 0)"
                  stroke-linecap="square"
                />
                <div v-if="file.status === 'failed'" class="close-container">
                  <van-icon name="close" color="#ffffff" size="30" />
                  <span>上传失败</span>
                </div>
                <svg-icon
                  icon-class="homework-delete"
                  class-name="homework-delete"
                  @click.stop="deleteImage(index)"
                />
              </div>
              <div v-if="fileList.length < 9" class="van-uploader">
                <div class="van-uploader__wrapper">
                  <div class="homework-add-box" @click="chooseFile">
                    <svg-icon icon-class="homework-add" class-name="homework-add" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </template>

    <!-- 上传按钮 -->
    <van-uploader
      ref="uploader"
      v-model="fileList"
      max-count="9"
      :accept="fileAccept"
      :multiple="multiple"
      :preview-image="false"
      :before-read="beforeUpload"
      :after-read="uploadFile"
      style="display: none"
    />
    <van-popup
      v-model="showFilePop"
      position="bottom"
      :round="true"
      :style="{ width: '100%', height: '30%' }"
    >
      <div class="file-pop">
        <div class="select" @click="chooseImage">图片</div>
        <div class="line1"></div>
        <div class="select" @click="chooseVideo">视频</div>
        <div class="line2"></div>
        <div class="select" @click="closeFilePop">取消</div>
      </div>
    </van-popup>

    <!-- <pop-change ref="changePop" @selectCurr="selectCurr" /> -->

    <div v-if="showVideoPreview" class="video-preview">
      <video-js :options="videoOptions" />
      <svg-icon
        icon-class="homework-delete"
        class-name="video-close"
        @click.stop="closeVideoPreview"
      />
    </div>
  </div>
</template>

<script>
import VideoJs from '@/components/classPro/video'
import axios from 'axios'
import { getFileUploadAuthor, castingShareFile } from '@/api/user-api'
import { ImagePreview } from 'vant'
export default {
  components: { VideoJs },
  data () {
    return {
      fileList: [],
      filtDataList: [],
      showCriteria: false,
      aiWorkImgList: [],
      standardImg: [],
      userWorks: undefined,
      uploading: false,
      childInfo: undefined,
      showVideoPreview: false,
      showFilePop: false,
      multiple: false,
      fileAccept: '',
      key: this.$route.query.key,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false, //  x5内核浏览器不允许自动播放，需要手动播放
        fileShow: false,
        loop: false,
        fluid: false,
        language: 'zh-CN',
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      },
      type: this.$route.query && this.$route.query.type === 'image' ? 'image' : '',
      titleType: this.$route.query && this.$route.query.title === 'upload' ? '上传' : '投屏',
      maxLength: this.$route.query && this.$route.query.maxLength ? Number(this.$route.query.maxLength) : 9
    }
  },
  created () {
  },
  methods: {
    async chooseFile () {
      this.showFilePop = true
    },
    closeFilePop () {
      this.showFilePop = false
    },
    chooseImage () {
      this.multiple = true
      this.fileAccept = 'image/*'
      this.$nextTick(() => {
        this.showFilePop = false
        this.$refs.uploader.chooseFile()
      })
    },
    chooseVideo () {
      if (this.type === 'image') {
        this.$toast('暂只支持上传图片')
        return
      }
      this.multiple = false
      this.fileAccept = 'video/*'
      this.$nextTick(() => {
        this.showFilePop = false
        this.$refs.uploader.chooseFile()
      })
    },
    async beforeUpload (files) {
      return new Promise((resolve, reject) => {
        if (this.titleType === '上传' && this.maxLength === 1) {
          if (files.length && files.length > 1) {
            this.$toast(`图片可传${this.maxLength}个`)
            reject()
          } else {
            resolve(files)
          }
        } else {
          if (files.length && files.length > 0) {
            if (files.length + this.fileList.length > this.maxLength) {
              this.$toast(`图片/视频共可传${this.maxLength}个`)
              reject()
            }
            resolve(files)
          } else {
            if (this.fileList.length >= this.maxLength) {
              this.$toast(`图片/视频共可传${this.maxLength}个`)
              reject()
            }
            if (files.type.indexOf('video') > -1) {
              const url = URL.createObjectURL(files)
              const video = new Audio(url)
              // //  让移动端视频开始缓冲
              if (window.WeixinJSBridge) {
                window.WeixinJSBridge.invoke('getNetworkType', {}, () => {
                  video.play()
                  video.pause()
                }, false)
              } else {
                video.play()
                video.pause()
              }
              video.addEventListener('loadedmetadata', () => {
                const time = Math.round(video.duration * 100) / 100
                if (time > 300) {
                  this.$toast('上传的视频不能超过5分钟')
                  reject()
                } else {
                  resolve(files)
                }
              })
            } else {
              resolve(files)
            }
          }
        }
      })
    },
    uploadFile (files) {
      const currentIndex = this.fileList.length - 1
      if (this.titleType === '上传' && this.maxLength === 1) {
        debugger
        if (this.fileList.length > 1) {
          this.deleteImage(0)
        }
        this.getFile(files, currentIndex)
      } else {
        if (files.length && files.length > 0) {
          for (let i = 0; i < files.length; i++) {
            this.getFile(files[i], currentIndex + i)
          }
        } else {
          this.getFile(files, currentIndex)
        }
      }
    },
    async getFile (files, index) {
      files.status = 'uploading'
      files.type = files.file.type.indexOf('image') > -1 ? 'IMAGE' : 'VIDEO'
      files.uploadProgress = 0
      const file = files.file
      const mediaType = files.type
      const filename = file.name
      const { data } = await this.getOssSign(mediaType, file.name)
      const postData = data[0]
      const url = postData.ossConfig.host
      const formData = new FormData()
      formData.append('key', postData.fileName)
      formData.append('policy', postData.policy)
      formData.append('OSSAccessKeyId', postData.ossConfig.accessKeyId)
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('signature', postData.signature)
      formData.append('file', file)
      try {
        await axios({
          url: url,
          method: 'post',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' },
          onUploadProgress: (progressEvent) => {
            const uploadProgress = Number(
              ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)
            )
            files.uploadProgress = uploadProgress
            this.fileList = this.fileList.reverse().reverse()
          }
        })
        const imgFullUrl = `${postData.ossConfig.ossCDN}/${postData.fileName}`
        const imgUrl = `${postData.fileName}`
        files.url = imgFullUrl
        files.postUrl = imgUrl
        if (files.type === 'VIDEO') files.image = `${files.url}?x-oss-process=video/snapshot,t_1000,m_fast`
        files.status = ''
        const mediaFile = {
          size: Math.floor(file.size / 1024),
          type: mediaType,
          fileName: filename.substring(0, filename.lastIndexOf('.')),
          url: data[0].fileName,
          expendType: filename.substring(filename.lastIndexOf('.') + 1)
        }
        this.filtDataList.push(mediaFile)
        castingShareFile(mediaFile, {
          key: this.key,
          apiType: 'create',
          childUserId: 0
        })
      } catch (error) {
        console.log(error)
        files.status = 'failed'
      }
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    deleteImage (index) {
      castingShareFile(this.filtDataList[index], {
        key: this.key,
        apiType: 'delete',
        childUserId: 0
      })
      this.filtDataList.splice(index, 1)
      let newFileList = []
      for (let i = 0; i < this.fileList.length; i++) {
        const newFile = this.fileList[i]
        if (index !== i) {
          newFileList = [...newFileList, newFile]
        }
      }
      this.fileList = newFileList
    },
    showPreview (type, index) {
      const imgs = []
      const showImgs = type === 0 ? this.aiWorkImgList : this.userWorks.resourceList
      for (const img of showImgs) {
        imgs.push(type === 0 ? img.mediaFile.url : img.url)
      }
      ImagePreview({
        images: imgs,
        startPosition: index,
        beforeClose: () => false
      })
    },
    showUploadPreview (type, url) {
      if (type === 0) {
        ImagePreview({
          images: [url],
          beforeClose: () => false,
          showIndex: false
        })
      } else {
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: url,
            type: 'video/mp4'
          }]
        }
        this.showVideoPreview = true
      }
    },
    async openPop () {
      this.$refs.changePop.openPop()
    },
    selectCurr (item) {
      this.childInfo = item
      this.$refs.changePop.closePop()
      this._getUserAiCourseWorks()
    },
    beforeClick () {
      this.openPop()
    },
    closeVideoPreview () {
      this.showVideoPreview = false
    },
    loadJs (src) {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.type = 'text/javascript'
        script.src = src
        document.body.appendChild(script)

        script.onload = () => {
          resolve()
        }
        script.onerror = () => {
          reject()
        }
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .homework {
    .course-title {
        margin-bottom: 6px;
        font-size: 16px;
    }

    .course-subtitle {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        color: #FFFFFF;
        margin-left: 17px;
    }

    .student {
      color: #FFFFFF;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
    }

    .arrow-student {
      width: 12px;
      height: 12px;
      object-fit: contain;
      transform: rotate(180deg)
    }

    .content {
      width: 100%;
      flex: 1;
      overflow: scroll;
      display: flex;
      flex-direction: column;
      @include noScrollBar;
      .title{
        width: 100%;
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 20px;
      }
    }

    .submit-container {
        padding: 14px;
        margin-bottom: 30px;
        display: flex;
        flex-direction: column;
        flex: 1;

        .homework-add-box {
            width: 100%;
            height: 170px;
            background: #FFFFFF;
            border: 1px solid #BDBDBD;
            position: relative;
        }

        .file-container {
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            row-gap: 6px;
            column-gap: 8px;
            margin-bottom: 10px;
        }

        .file-box {
            position: relative;
            width: 100%;
            height: 0px;
            padding-top: 100%;

            img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: 10;
            }

            .shadow {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.5);
              z-index: 11;
            }

            .video-shadow {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.5);
              z-index: 11;
            }

            .video-player {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 33px;
              height: 33px;
              object-fit: contain;
            }

            .close-container {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              z-index: 12;
              gap: 10px;

              span {
                size: 20PX;
                color: #FFFFFF;
                font-weight: bold;
              }
            }

            .homework-delete {
                width: 30px;
                height: 30px;
                object-fit: contain;
                position: absolute;
                right: 2px;
                top: 4px;
                z-index: 13;
            }
        }

        .homework-add {
            width: 42px;
            height: 42px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .preview-cover {
            position: absolute;
            bottom: 0;
            box-sizing: border-box;
            width: 100%;
            padding: 4px;
            color: #fff;
            font-size: 12px;
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
        }

        .hint {
            display: inline-block;
            margin-top: auto !important;
            margin-left: auto;
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            color: #BDBDBD;
            margin-top: 5px;
        }
    }

    .video-preview {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: black;
      z-index: 20;
      padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
      padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/

      .video-close {
        position: absolute;
        right: 20px;
        top: 20px;
        width: 40px;
        height: 40px;
      }
    }

    .file-pop {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: white;
      padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
      padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/

      .select {
        height: 31%;
        width: 100%;
        font-size: 22px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        color: black;
      }
      .line1 {
        width: 100%;
        height: 1px;
        background: rgba(0, 0, 0, 0.1);
      }

      .line2 {
        width: 100%;
        flex: 1;
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
  </style>

  <style lang="scss">
  .homework {
    .video-js.vjs-paused .vjs-big-play-button{ /* 视频暂停时显示播放按钮 */
      display: block;
      position: absolute;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50%;
    }
  }

  .submit-container {
      .el-textarea {
          margin-bottom: 20px;
      }

      .el-textarea__inner {
          font-size: 14px;
      }

      .van-uploader {
          width: 100%;
          flex: 1;
      }

      .van-uploader__input-wrapper {
        width: 100%;
      }

      .van-circle {
        position: absolute;
        transform: translate(-50%, -50%);
        left: 50%;
        top: 50%;
        background: rgba(0, 0, 0, 0.45);
        border-radius: 50%;
        z-index: 12;
      }

      .van-circle__text {
        font-size: 10PX;
        color: white;
        font-weight: 600;
      }
  }
  </style>
