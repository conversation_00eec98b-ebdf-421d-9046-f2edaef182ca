<template>
  <div class="my-info" :style="pdb">
    <toolbar :need-home="true" @prev="prev">
      <template slot="right">
        <img class="share" src="@/assets/H5/share-icon.svg" alt="" @click="shareActivity" />
      </template>
    </toolbar>

    <template v-if="list">

      <div class="box mt10">
        <div class="box-inside w">
          <div>
            {{ list.name }}
          </div>

          <div class="mt10">
            <van-swipe ref="swipe" :show-indicators="false" @change="onChange">
              <van-swipe-item v-for="item in list.resourceList" :key="item.id">
                <div class="work-img" style="overflow: hidden" @click="peview(item)">
                  <van-icon v-if="item.type === 'VIDEO'" class="play-btn" color="#FFF" size="50" name="play-circle" />
                  <img v-if="item.type === 'IMAGE'" :src="item.url" />
                  <img v-else-if="item.type === 'VIDEO'" :src="item.url + '?x-oss-process=video/snapshot,t_1000,m_fast'" />
                </div>
              </van-swipe-item>
            </van-swipe>
          </div>

          <div class="p-img">
            <div v-for="(item, index) in list.resourceList" :key="item.id" class="work-p-img relative" @click="jumoto(index)">
              <div v-if="index == current" class="triangle"></div>
              <div v-else class="triangle-h"></div>
              <van-icon v-if="item.type === 'VIDEO'" class="play-btn" color="#FFF" size="25" name="play-circle" />
              <img v-if="item.type === 'IMAGE'" :src="item.url" />
              <img v-else-if="item.type === 'VIDEO'" :src="item.url + '?x-oss-process=video/snapshot,t_1000,m_fast'" />
            </div>
          </div>

          <div class="p-tip">
            可左右滑动查看{{ current + 1 }}/{{ list.resourceList.length }}
          </div>

        </div>
      </div>

      <div v-if="activityInfo.praisePerWork !== 0" class="box mt10">
        <div class="box-inside w flex justify-between items-center h60">
          <div class="f16">累积票数</div>
          <div class="pick">{{ list.praiseNum }}票</div>
        </div>
      </div>

      <div class="box mt10">
        <div class="box-inside w">
          <div class="a-tag-name">
            基本信息
          </div>

          <div class="base-t-box">
            <div class="title">作者姓名：</div>
            <div class="des">
              <template v-for="stu in list.members">
                {{ stu.displayName }}
              </template>
            </div>
          </div>
          <div v-if="list.part" class="base-t-box">
            <div class="title">参赛组：</div>
            <div class="des">{{ list.part }}</div>
          </div>
          <div v-if="list.adviser" class="base-t-box">
            <div class="title">指导老师：</div>
            <div class="des">{{ list.adviser }}</div>
          </div>
          <div v-if="list.area" class="base-t-box">
            <div class="title">所属地区：</div>
            <div class="des">{{ list.area }}</div>
          </div>
          <div v-if="list.school" class="base-t-box">
            <div class="title">学 校：</div>
            <div class="des">{{ list.school }}</div>
          </div>
          <div v-if="list.code" class="base-t-box">
            <div class="title">作品编号：</div>
            <div class="des">{{ list.code }}</div>
          </div>
          <div class="base-t-box">
            <div class="title">区块链地址：</div>
            <div class="des">{{ list.blockChainAddr || '生成中' }}</div>
          </div>
          <div class="base-t-box">
            <div class="title">作品哈希值：</div>
            <div class="des">{{ list.hashCode || '生成中' }}</div>
          </div>
          <div class="base-t-box">
            <div class="title">生成时间：</div>
            <div class="des">{{ list.createdAt }}</div>
          </div>
          <div class="base-t-box">
            <div class="title">区块链：</div>
            <div class="des">长安链</div>
          </div>
          <div v-if="activityInfo && activityInfo.praisePerWork !== 0" class="base-t-box" @click="$router.push({ path: '/h5/activity', query: { activityId: activityId, tabIndex: 11, part: list.part} })">
            <div class="title">作品排行：</div>
            <div v-show="list.rank" class="des underline">{{ list.rank || '-' }}名</div>
          </div>
        </div>
      </div>

      <div class="box mt10 mb30">
        <div class="box-inside w">
          <div class="a-tag-name">
            作品介绍
          </div>

          <div class="intro">
            {{ list.introduce }}
          </div>

          <div v-if="list.introduceVideo" class="work-img" @click="peviewVideo(list.introduceVideo)">
            <img :src="list.introduceVideo + '?x-oss-process=video/snapshot,f_png,w_300,t_0'" />
            <van-icon class="play-btn" color="#FFF" size="40" name="play-circle" />
          </div>

          <div class="f12 h40 w flex justify-center items-center">作品介绍</div>
          <div
            class="f12 h40 w flex justify-center items-center"
            @click="$router.push({ path: '/h5/activity', query: { activityId: activityId, tabIndex: 10 } })"
          >查看更多作品></div>
        </div>
      </div>

      <share-works v-if="showShare" :info="list" @close="showShare = false" />

    </template>

    <div v-show="!isOutDate" ref="submitBottom" class="submit-btn-box">
      <div>
        <div
          v-show="canApply"
          class="submit-btn mr20"
          style="background-color: #2D9CDB;"
          @click="apply"
        >
          我要报名
        </div>
      </div>
      <template v-if="activityInfo && activityInfo.praisePerWork !== 0">
        <!-- <div v-if="isMyJion && isInData" class="submit-btn" @click="$router.push({ path: '/h5/submit-work', query: {id: activityId, edit: 1} })">
          编辑
        </div> -->
        <div class="submit-btn" @click="praise()">
          立即投票
        </div>
      </template>
      <template v-else>
        <div v-if="isMyJion && isInData" class="submit-btn" @click="$router.push({ path: '/h5/submit-work', query: {id: activityId, edit: 1} })">
          编辑
        </div>
      </template>

      <van-dialog v-model="tipsShow" :show-confirm-button="false">
        <div class="praise-box">
          <template v-if="praiseSuccess">
            <van-icon class="icon" name="checked" color="#52C41A" />
            <div class="tips1">投票成功</div>
          </template>
          <template v-else>
            <van-icon class="icon" name="clear" color="#4F4F4F" />
            <div class="tips1">投票失败</div>
          </template>
          <div class="tips2">{{ tipsText }}</div>
          <div class="btn" @click="tipsShow= false">关闭</div>
        </div>
      </van-dialog>
    </div>
    <div v-if="peviewUrl" class="iframe-box">
      <van-icon class="close" name="clear" color="#DDDDDD" size="20" @click.stop="peviewUrl= false" />
      <video-js :options="videoOptions" @player="videoPlay" />
    </div>
    <van-image-preview v-model="imagesShow" :images="images" closeable @close="images=[]" />
  </div>
</template>

<script>
import Toolbar from '@/components/H5/toolbar.vue'
import ShareWorks from './activity/shareWorks'
import localStore from '@/utils/local-storage.js'
import { initWechatShare } from '@/utils/index.js'
import { getWxGzhShareSignature } from '@/api/activity-api.js'
import { getActivityWorkInfo, getActivityInfo, praiseActivityWorks } from '@/api/activity-api.js'
import { getPartentToken } from '@/utils/auth'
import { debounce } from '@/utils/index'
import VideoJs from '@/components/classPro/h5Video'
import { getTimestamp } from '@/utils/time.js'
import moment from 'moment'
import { isApp } from '@/utils/index.js'
export default {
  components: { Toolbar, ShareWorks, VideoJs },
  data () {
    return {
      appid: 'wxfa4a06f2648b3c54',
      current: 0,
      pdb: { paddingBottom: '60px' },
      currChild: null,
      activityId: '',
      activityWorksId: '',
      list: null,
      activityInfo: null,
      showShare: false,
      peviewUrl: false,
      images: [],
      imagesShow: false,
      tipsShow: false,
      praiseSuccess: true,
      tipsText: '',
      canApply: false,
      canSubmit: false,
      isOutDate: false,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false, //  x5内核浏览器不允许自动播放，需要手动播放
        fileShow: false,
        loop: false,
        fluid: false,
        language: 'zh-CN',
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    }
  },
  computed: {
    isInData () {
      return moment().isBefore(this.activityInfo && this.activityInfo.endReview)
    },
    isMyJion () {
      if (this.currChild && this.list && +this.list.userId === +this.currChild.id) {
        return true
      } else {
        return false
      }
    }
  },
  async mounted () {
    document.title = '作品详情'
    this.activityId = this.$route.query.id
    this.activityWorksId = this.$route.query.workId
    const currChild = JSON.parse(localStore.read('currChild'))
    this.currChild = currChild
    if (this.activityId) {
      await this._getActivityInfo()
      await this._getActivityWorkInfo()
      const helpPraise = localStore.read('helpPraise')
      if (+helpPraise === 1 && !isApp()) {
        this.showShare = true
        localStore.save('helpPraise', 0)
      } else if (+helpPraise === 1 && isApp()) {
        this.shareApp()
        localStore.save('helpPraise', 0)
      }
    }
    this.$nextTick(() => {
      this.pdb = { paddingBottom: `${this.$refs.submitBottom.clientHeight}px` }
    })
    window['setBingoToken'] = async(val) => {
      await this.$store.dispatch('user/partentLoginApp', 'Bearer ' + val)
      await this.$store.dispatch('user/GetInfo')
    }
  },
  methods: {
    shareActivity() {
      if (isApp()) {
        this.shareApp()
      } else {
        this.showShare = true
      }
    },
    removeTokenFromUrl(url) {
      const urlObj = new URL(url)
      urlObj.searchParams.delete('token')
      return urlObj.toString()
    },
    shareApp () {
      const obj = JSON.stringify({
        link: this.removeTokenFromUrl(window.location.href),
        title: this.list.name,
        cover: this.list.resourceList[0].type === 'IMAGE' ? this.list.resourceList[0].url : this.list.resourceList[0].url + '?x-oss-process=video/snapshot,t_1000,m_fast',
        content: this.list.introduce
      })
      if (window.webkit && window.webkit.messageHandlers) {
        window.webkit.messageHandlers.share_action.postMessage(obj)
      } else if (window.share_action) {
        window.share_action.postMessage(obj)
      }
    },
    prev () {
      if (window.history.length <= 1) {
        this.$router.push({ 'path': '/h5/index' })
      } else {
        this.$router.go(-1)
      }
    },
    onChange (index) {
      this.current = index
    },
    async _getActivityInfo () {
      const token = getPartentToken()
      const obj = { activityId: this.activityId }
      if (token && this.currChild) {
        obj.childUserId = this.currChild.id
      }
      const { data } = await getActivityInfo(obj)
      this.activityInfo = data
      this.canApply = getTimestamp(Date()) > this.activityInfo.startTime && getTimestamp(Date()) < this.activityInfo.endApply && !this.activityInfo.registered
      this.canSubmit = this.activityInfo.registered && getTimestamp(Date()) < this.activityInfo.endSubmit
      this.isOutDate = this.activityInfo.progressStatus === 'FINISHED'
    },
    async _getActivityWorkInfo () {
      const obj = { activityId: this.activityId, activityWorksId: this.activityWorksId }
      const token = getPartentToken()
      if (token && this.currChild) {
        obj.childUserId = this.currChild.id
      }
      const { data } = await getActivityWorkInfo(obj)
      this.list = data
      this.images = []
      data.resourceList.map(val => {
        this.images.push(val.url)
      })
      this._initWechatShare()
    },
    apply () {
      const token = getPartentToken()
      if (token) {
        this.$router.push({ path: '/h5/apply', query: { id: this.activityId }})
      } else {
        if (isApp()) {
          if (window.webkit && window.webkit.messageHandlers) {
            window.webkit.messageHandlers.bingo_action.postMessage('event_no_login')
          } else if (window.bingo_action) {
            window.bingo_action.postMessage('event_no_login')
          }
          return
        }
        this.$router.push({ path: '/h5/apply', query: { id: this.activityId }})
      }
    },
    praise: debounce(async function () {
      if (!this.isInData) {
        this.$toast.fail('投票已结束')
        return
      }
      const token = getPartentToken()
      if (token) {
        const { data } = await praiseActivityWorks({ activityWorksId: this.activityWorksId })
        if (data.success) {
          this.list.praiseNum++
          this.praiseSuccess = true
        } else {
          this.praiseSuccess = false
        }
        this.tipsShow = true
        this.tipsText = data.msg
      } else {
        if (isApp()) {
          if (window.webkit && window.webkit.messageHandlers) {
            window.webkit.messageHandlers.bingo_action.postMessage('event_no_login')
          } else if (window.bingo_action) {
            window.bingo_action.postMessage('event_no_login')
          }
          return
        } else {
          localStore.save('acticegoto', `/h5/check-work?id=${this.activityId}&workId=${this.activityWorksId}`)
          this.handleGetWxCode()
        }
      }
    }, 1000, true),
    jumoto (index) {
      this.$refs.swipe.swipeTo(index)
    },
    peview (val) {
      if (val.type === 'IMAGE') {
        this.imagesShow = true
        this.images = [val.url]
      } else if (val.type === 'VIDEO') {
        // this.peviewUrl = val.url
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: val.url,
            type: 'video/mp4'
          }]
        }
        this.peviewUrl = true
      }
    },
    peviewVideo (url) {
      this.videoOptions = {
        ...this.videoOptions,
        sources: [{
          src: url,
          type: 'video/mp4'
        }]
      }
      this.peviewUrl = true
    },
    handleGetWxCode (state = 1) {
      const baseUrl = window.location.origin
      const url = `${baseUrl}/#/h5/checkPraise`
      window.location.href = `${process.env.VUE_APP_WEB_URL}get-weixin-code.html?appid=${this.appid}&scope=snsapi_userinfo&state=${state}&redirect_uri=${encodeURIComponent(url)}`
    },
    async _initWechatShare () {
      const url = location.href.split('#')[0]
      const params = {
        'url': url
      }
      const res = await getWxGzhShareSignature(params)
      var cover
      const resrouce = this.list.resourceList[0]
      switch (resrouce.type) {
        case 'IMAGE':
          cover = resrouce.url
          break
        case 'VIDEO':
          cover = resrouce.url + '?x-oss-process=video/snapshot,t_1000,m_fast'
          break
      }
      // 配置分享的内容
      const shareOptions = {
        title: this.list.name,
        desc: this.list.introduce ?? '',
        link: location.href,
        imgUrl: cover,
        success: () => {
          // 分享成功回调
          console.log('分享成功')
        },
        cancel: () => {
          // 分享取消回调
          console.log('分享取消')
        }
      }
      initWechatShare(res, shareOptions)
    },
    videoPlay (player) {
      player.play()
    }
  }
}
</script>

<style lang="scss" scoped>
.my-info {
  width: 100%;
  height: 100%;
  background: #F2FAFD;
  box-sizing: border-box;
  padding-top: 50px;
  overflow-y: auto;
  // padding-bottom: constant(safe-area-inset-bottom);
  // padding-bottom: env(safe-area-inset-bottom);

  .share {
    width: 24px;
    height: 24px;
    margin-left: 10px;
  }

  .mt10 {
    margin-top: 10px;
  }

  .mb30 {
    margin-bottom: 30px;
  }

  .box {
    width: 100%;
    padding: 0 12px;
    box-sizing: border-box;

    .box-inside {
      background: #FFFFFF;
      border-radius: 10px;
      box-sizing: border-box;
      padding: 5px;
    }
  }

  .active-name {
    font-size: 16px;
    line-height: 20px;
    color: #000000;
  }

  .a-tag-name {
    height: 40px;
    display: flex;
    align-items: center;
    color: #000000;
    font-size: 16px;

    &::before {
      width: 4px;
      height: 20px;
      margin-right: 5px;
      background: #2F80ED;
      border-radius: 17px;
      content: "";
    }
  }

  .work-img {
    width: 100%;
    height: 250px;
    border-radius: 10px;
    position: relative;

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
  }

  .play-btn {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .p-img {
    width: 100%;
    margin-top: 10px;
    display: flex;
    justify-content: flex-start;
    flex-shrink: 0;
    overflow-x: auto;

    .work-p-img {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 5px;

      &:last-child {
        margin-right: 0;
      }

      .triangle {
        width: 0;
        height: 0;
        border-right: 5px solid transparent;
        border-left: 5px solid transparent;
        border-bottom: 5px solid #DDDDDD;
      }
      .triangle-h {
        height: 5px;
      }

      img {
        width: 70px;
        height: 70px;
        object-fit: cover;
      }
    }
  }

  .p-tip {
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #000000;
  }

  .base-t-box {
    width: 100%;
    padding: 8px 0;
    display: flex;

    .title {
      font-size: 14px;
      line-height: 20px;
      color: #4F4F4F;
      width: 100px;
      flex-shrink: 0;
    }

    .des {
      font-size: 14px;
      line-height: 20px;
      color: #000000;
      flex: 1;
      word-break: break-all;
    }
  }

  .intro {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #4F4F4F;
    padding-bottom: 20px;
  }

  .submit-btn-box {
    position: fixed;
    width: 100%;
    box-sizing: border-box;
    left: 0;
    right: 0;
    bottom: 0;
    background: #FFFFFF;
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 20px 0 20px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .submit-btn,
    .submit-btn-end {
      width: 150px;
      height: 42px;
      // border-radius: 50px;
      color: #FFFFFF;
      font-size: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .submit-btn {
      background: #F2994A;
      margin-bottom: 20px;
    }

    .submit-btn-end {
      background: #828282;
    }
  }

  .pick {
    font-size: 20px;
    color: #000000;
  }

  .f12 {
    font-size: 12px;
  }

  .f16 {
    font-size: 16px;
  }

  .h40 {
    height: 30px;
  }

  .h60 {
    height: 60px;
  }

  .underline {
    text-decoration: underline;
  }

  .mb20 {
    margin-bottom: 20px;
  }

  .mr20 {
    margin-right: 20px;
  }
}

.iframe-box {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99999;

  .close {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 999;
  }
}

.praise-box {
  padding: 20px 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;

  .tips1 {
    font-size: 26px;
    color: #000000;
    margin-top: 10px;
  }

  .tips2 {
    font-size: 16px;
    margin-top: 20px;
    line-height: 30px;
  }
  .icon {
    font-size: 70px;
  }
  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 11px 10px;
    gap: 10px;
    width: 120px;
    height: 40px;
    background: #2F80ED;
    color: #FFFFFF;
    font-size: 20px;
    margin-top: 20px;
  }
}
</style>
