<template>
  <div class="my-jion">
    <toolbar :need-home="false" @prev="prev" />

    <div class="w h content">
      <div v-for="item in list" :key="item.id" class="card-list">
        <div class="cover">
          <img :src="item.cover" />
          <div v-if="item.progressStatus === 'GOING'" class="start">
            {{ statusJson[item.progressStatus] }}
          </div>
          <div v-else class="end">{{ statusJson[item.progressStatus] }}</div>
        </div>
        <div class="detail">
          <div class="detail-name">{{ item.name }}</div>
          <div class="detail-date">
            <template v-if="!item.works">参赛截止：{{ item.endSubmit | formateTime }}</template>
            <template v-else>活动截止：{{ item.endTime | formateTime }}</template>
          </div>
          <div class="detail-status">
            <template v-if="!item.works">已报名</template>
          </div>
          <div class="detail-btns">
            <div class="work-btn" @click="jion(item)">
              <template v-if="!item.works">立即参赛</template>
              <template v-else>我的作品</template>
            </div>
          </div>
        </div>
      </div>
      <div v-if="list.length === 0" class="empty-box">
        <img :src="IconEmpty" alt="" />
        <span>暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import IconEmpty from '@/assets/H5/icon-empty.svg'
import Toolbar from '@/components/H5/toolbar.vue'
import { registeredActivityList } from '@/api/activity-api.js'
import localStore from '@/utils/local-storage.js'
import moment from 'moment'

export default {
  components: { Toolbar },
  filters: {
    formateTime(date) {
      return moment(date).format('YYYY.MM.DD')
    },
    canApply(item) {
      if (moment(new Date()).isAfter(item.endSubmit)) {
        return false
      }
      return true
    }
  },
  data() {
    return {
      IconEmpty,
      currChild: null,
      list: [],
      statusJson: {
        GOING: '进行中',
        FINISHED: '已结束',
        COMING: '未开始'
      }
    }
  },
  mounted() {
    const currChild = JSON.parse(localStore.read('currChild'))
    this.currChild = currChild
    this._registeredActivityList()
  },
  methods: {
    prev() {
      this.$router.go(-1)
    },
    async _registeredActivityList() {
      const { data } = await registeredActivityList({
        childUserId: this.currChild.id
      })
      this.list = data
    },
    jion(item) {
      if (item.works) {
        this.$router.push({
          path: '/h5/check-work',
          query: { id: item.id, workId: item.works && item.works.id }
        })
      } else {
        if (moment(new Date()).isAfter(item.endSubmit)) {
          this.$toast('参赛时间已过')
          return
        }
        this.$router.push({ path: '/h5/submit-work', query: { id: item.id } })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.my-jion {
  width: 100%;
  height: 100%;
  background: #ffffff;
  box-sizing: border-box;
  padding-top: 50px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .content {
    .card-list {
      padding: 0 10px;
      display: flex;
      margin-bottom: 10px;

      .cover {
        width: 150px;
        height: 120px;
        border-radius: 10px;
        margin-right: 10px;
        flex-shrink: 0;
        position: relative;

        .start,
        .end {
          position: absolute;
          top: 5px;
          right: 5px;
          border: 1px solid rgba(255, 255, 255, 0.6);
          border-radius: 6px;
          color: #ffffff;
          width: 54px;
          height: 25px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .start {
          background: rgba(39, 174, 96, 0.7);
        }

        .end {
          background: rgba(51, 51, 51, 0.6);
        }

        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
          border-radius: 10px;
        }
      }

      .detail {
        flex: 1;
        position: relative;

        .detail-name {
          font-size: 14px;
          line-height: 20px;
          height: 40px;
          display: -webkit-box;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; //需要显示的行数
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .detail-date {
          font-size: 12px;
          color: #4f4f4f;
          height: 25px;
          display: flex;
          align-items: center;
        }

        .detail-status {
          font-size: 12px;
          color: #4f4f4f;
          height: 25px;
          display: flex;
          align-items: center;
        }

        .detail-btns {
          position: absolute;
          bottom: 0;
        }

        .work-btn {
          width: 56px;
          height: 25px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #2f80ed;
          border-radius: 5px;
          color: #fff;
          font-size: 12px;
        }
      }
    }
  }
}

.empty-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;

  img {
    width: 65px;
    height: 65px;
    object-fit: contain;
  }

  span {
    font-weight: 300;
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
