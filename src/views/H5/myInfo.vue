<template>
  <div class="my-info">
    <toolbar :need-home="false" @prev="prev">
      <template slot="right">
        <i class="el-icon-edit-outline" @click="$router.push({path: '/h5/info', query: {edit: 2}})"></i>
      </template>
    </toolbar>

    <div v-if="userInfo" class="w content">
      <van-cell :border="false" title="姓名" :value="userInfo.name" value-class="desc" title-class="info-title" />
      <van-cell :border="false" title="性别" :value="formateGender(currChild.gender)" value-class="desc" title-class="info-title" />
      <van-cell :border="false" title="地区" :value="area" value-class="desc" title-class="info-title" />
      <van-cell :border="false" title="学校" :value="school" value-class="desc" title-class="info-title" />
      <van-cell :border="false" title="班级" :value="userInfo.className" value-class="desc" title-class="info-title" />
    </div>
  </div>
</template>

<script>
import Toolbar from '@/components/H5/toolbar.vue'
import localStore from '@/utils/local-storage.js'
import { getChildInfo } from '@/api/partent-api'

export default {
  components: { Toolbar },
  data () {
    return {
      currChild: null,
      userInfo: null
    }
  },
  computed: {
    area () {
      if (this.userInfo) {
        if (this.userInfo.school) {
          return `${this.userInfo.school.province}${this.userInfo.school.city}${this.userInfo.school.district}`
        } else {
          if (this.userInfo.userInfo.province || this.userInfo.userInfo.city || this.userInfo.userInfo.district) {
            return `${this.userInfo.userInfo.province}${this.userInfo.userInfo.city}${this.userInfo.userInfo.district}`
          } else {
            return ''
          }
        }
      }
      return ''
    },
    school () {
      if (this.userInfo) {
        if (this.userInfo.school && this.userInfo.school.name) {
          return this.userInfo.school.name
        } else if (this.userInfo.studentInfo && this.userInfo.studentInfo.school) {
          return this.userInfo.studentInfo.school
        } else {
          return ''
        }
      }
      return ''
    }
  },
  mounted () {
    const currChild = JSON.parse(localStore.read('currChild'))
    this.currChild = currChild
    this._getChildInfo()
  },
  methods: {
    prev () {
      this.$router.go(-1)
    },
    async _getChildInfo () {
      if (!this.currChild) return
      const { data } = await getChildInfo({ childId: this.currChild.id })
      this.userInfo = data
    },
    formateGender (val) {
      if (val) {
        return {
          MALE: '男',
          FEMALE: '女'
        }[val]
      } else {
        return '未知'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.my-info {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  box-sizing: border-box;
  padding-top: 50px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .el-icon-edit-outline {
    font-size: 23px;
    color: #000;
  }

  .content {
    margin-top: 20px;
  }

  .info-title {
    color: #4F4F4F;
    font-size: 16px;
    flex: none;
  }
  .desc {
    font-size: 16px;
    color: #000;
  }
}
</style>
