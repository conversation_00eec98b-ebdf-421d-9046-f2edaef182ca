<template>
  <div class="apply" :style="pdb">
    <toolbar @prev="prev" />
    <!-- <div class="active-name">
      {{ activityInfo && activityInfo.name }}
    </div> -->
    <div class="a-tag-name">
      填写报名信息
    </div>
    <div class="info-card">
      <div>
        <div v-if="requireList.part" class="flex items-center mt20 mb10">
          <div class="input-lable">
            <span class="red-icon">*</span>参赛组别:
          </div>
          <div class="p-input">
            <van-field
              readonly
              clickable
              :value="selectPart.text"
              placeholder="选择组别"
              @click="showPart = true"
            >
              <template #button>
                <div class="down"></div>
              </template>
            </van-field>
            <van-popup v-model="showPart" round position="bottom">
              <van-picker
                show-toolbar
                :columns="requireList.part.option"
                @cancel="showPart = false"
                @confirm="onPartNameConfirm"
              />
            </van-popup>
          </div>
        </div>

        <div class="flex items-center mt20 mb10">
          <div class="input-lable">
            <span class="red-icon">*</span>姓名:
          </div>
          <div class="p-input">
            <van-field
              readonly
              clickable
              :value="selectStuName.text"
              placeholder="选择学生"
              @click="showStuName = true"
            >
              <template #button>
                <div class="down"></div>
              </template>
            </van-field>
            <van-popup v-model="showStuName" round position="bottom">
              <van-picker
                show-toolbar
                :columns="childList"
                @change="childListChange"
                @cancel="showStuName = false"
                @confirm="onChildNameConfirm"
              >
                <template #columns-top>
                  <div class="custom-input">
                    <div class="p-input">
                      <van-field v-model="customStuName" :disabled="stuNameCustom" placeholder="添加学生" />
                    </div>
                  </div>
                </template>
              </van-picker>
            </van-popup>
          </div>
        </div>
        <div v-if="requireList.gender" class="flex items-center mt20 mb10">
          <div class="input-lable">
            <span v-show="requireList.gender.required" class="red-icon">*</span>学生性别:
          </div>
          <div class="p-input">
            <van-field readonly clickable :value="selectGender.text" placeholder="选择性别" @click="showGender = true">
              <template #button>
                <div class="down"></div>
              </template>
            </van-field>
            <van-popup v-model="showGender" round position="bottom">
              <van-picker show-toolbar :columns="genderList" @cancel="showGender = false" @confirm="onGenderConfirm" />
            </van-popup>
          </div>
        </div>
        <div v-if="requireList.area" class="flex items-center mt20 mb10">
          <div class="input-lable mt20 mb10">
            <span v-show="requireList.area.required" class="red-icon">*</span>
            所在地区:
          </div>
          <div class="p-input">
            <van-field readonly clickable :value="fieldValue" placeholder="选择所在地区" @click="show = true">
              <template #button>
                <div class="down"></div>
              </template>
            </van-field>
            <van-popup v-model="show" round position="bottom">
              <van-area title="选择所在地区" :area-list="areaList" @cancel="show = false" @confirm="onFinish" />
            </van-popup>
          </div>
        </div>
      </div>

      <div v-if="requireList.school_id" class="flex items-center mt20 mb10">
        <div class="input-lable mt20 mb10">
          <span v-show="requireList.school_id.required" class="red-icon">*</span>
          学校名称:
        </div>
        <div class="p-input">
          <van-field
            readonly
            clickable
            :disabled="!fieldValue"
            :value="selectSchoolName.text"
            placeholder="选择学校"
            @click="fieldValue ? showSchoolName = true : ''"
          >
            <template #button>
              <div class="down"></div>
            </template>
          </van-field>
          <van-popup v-model="showSchoolName" round position="bottom">
            <van-picker
              show-toolbar
              :columns="schoolList"
              @change="schoolListChange"
              @cancel="showSchoolName = false"
              @confirm="onSchoolNameConfirm"
            >
              <template #columns-top>
                <div class="custom-input">
                  <div class="p-input">
                    <van-field v-model="customSchoolName" :disabled="schoolCustom" placeholder="如未找到所在学校，请手动输入" />
                  </div>
                </div>
              </template>
            </van-picker>
          </van-popup>
        </div>
      </div>

      <div v-if="requireList.class_name" class="flex items-center mt20 mb10">
        <div class="input-lable mt20 mb10">
          <span v-show="requireList.class_name.required" class="red-icon">*</span>
          班级:
        </div>
        <div class="p-input">
          <van-field
            readonly
            clickable
            :disabled="!selectSchoolName.text"
            :value="selectClass.text"
            placeholder="选择班级"
            @click="selectSchoolName.text ? showPicker = true : ''"
          >
            <template #button>
              <div class="down"></div>
            </template>
          </van-field>
          <van-popup v-model="showPicker" round position="bottom">
            <van-picker
              show-toolbar
              :columns="classList"
              @change="classListChange"
              @cancel="showPicker = false"
              @confirm="onConfirm"
            >
              <template #columns-top>
                <div class="custom-input">
                  <div class="p-input">
                    <PopClassName :disable-input="classNameCustom" @onConfirm="customClassName" />
                  </div>
                </div>
              </template>
            </van-picker>
          </van-popup>
        </div>
      </div>
      <div class="flex items-center mt20 mb10">
        <div class="input-lable mt20 mb10">
          <span class="red-icon">*</span>
          手机号:
        </div>
        <div class="p-input">
          <van-field
            readonly
            clickable
            :disabled="true"
            :value="phone"
          />
        </div>
      </div>
    </div>

    <div class="rule-tips">
      <i class="el-icon-circle-check"></i>
      <div>
        提交报名即同意
        <span style="text-decoration: underline;" @click="checkXieyi">《用户报名协议》</span>
      </div>
    </div>
    <div v-if="peviewUrl" class="iframe-box">
      <van-icon class="close" name="clear" color="#DDDDDD" size="20" @click="peviewUrl= ''" />
      <iframe
        :src="peviewUrl"
        width="100%"
        height="100%"
        frameborder="0"
        scrolling="auto"
      ></iframe>
    </div>
    <div ref="submitBottom" class="submit-btn-box">
      <div v-if="!(activityInfo && activityInfo.registered)" class="submit-btn" @click="bind">
        提交报名
      </div>
      <div v-else class="submit-btn" @click="nextStep">
        下一步
      </div>
    </div>
    <div class="active-time">
      报名截止时间：{{ (activityInfo && activityInfo.endApply) | formateTime }}
    </div>
    <div>
      <div class="a-tag-name">
        报名信息
      </div>
      <template>
        <table class="apply-table">
          <thead class="apply-table-head">
            <tr class="apply-table-row">
              <th class="apply-table-header">孩子姓名</th>
              <th class="apply-table-header">报名状态</th>
              <th class="apply-table-header">作品状态</th>
              <th class="apply-table-header">作品</th>
            </tr>
          </thead>
          <tbody class="apply-table-body">
            <tr v-for="(item, index) in childApplyList" :key="index" class="apply-table-row">
              <td class="apply-table-cell">{{ item.displayName }}</td>
              <td class="apply-table-cell">{{ item.invalidType === '活动未报名' ? '未报名' : '已报名' }}</td>
              <td class="apply-table-cell">{{ item.invalidType === '活动已参赛' ? '已提交' : '未提交' }}</td>
              <td class="apply-table-cell">
                <div v-if="item.invalidType === '活动已参赛'" class="apply-table-action" @click="submitWork(item,1)">
                  查看作品
                </div>
                <div v-else-if="item.invalidType !== '活动未报名'" class="apply-table-action" @click="submitWork(item)">提交作品</div>
              </td>
            </tr>
          </tbody>
        </table>
      </template>
      <div class="active-time" style="margin-top: 15px;">
        提交作品截止时间：{{ (activityInfo && activityInfo.endSubmit) | formateTime }}
      </div>
    </div>
    <van-popup
      v-model="showPhone"
      :get-container="'body'"
      :close-on-click-overlay="false"
      round
      position="bottom"
      :style="{ 'min-height': '40%', 'max-height': '90%' }"
    >
      <div class="pop-chang-phone">
        <div class="title">绑定手机号</div>
        <div class="p-login-input">
          <van-field v-model="popPhone" type="number" clearable placeholder="输入手机号" />
        </div>
        <div class="p-login-input">
          <van-field v-model="sms" center clearable placeholder="输入验证码">
            <template #button>
              <div class="sms" @click="getCode">
                {{
                  smsDisabled
                    ? countdown > 0
                      ? countdown + 's后重新获取'
                      : '获取验证码'
                    : '获取验证码'
                }}
              </div>
            </template>
          </van-field>
        </div>
        <div class="p-btn" @click="submitMobile">确定</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import localStore from '@/utils/local-storage.js'
import Toolbar from '@/components/H5/toolbar.vue'
import { getActivityInfo, registerActivity, registeredActivityList } from '@/api/activity-api.js'
import moment from 'moment'

import { getParentChildren, getChildInfo, getSchoolList, getSchoolClassList, parentBindChild, bindMobile } from '@/api/partent-api'
import { areaList } from '@vant/area-data'
import PopClassName from '../parent/components/PopClassName.vue'
import { Toast } from 'vant'

import { validMobile } from '@/utils/validate'
import { verifyCodeForWeb } from '@/api/user-api'
import { setPartentToken } from '@/utils/auth'
import { getTimestamp } from '@/utils/time.js'
export default {
  components: { Toolbar, PopClassName },
  filters: {
    formateTime (date) {
      return moment(date).format('YYYY.MM.DD')
    }
  },
  data () {
    return {
      showPhone: false,
      popPhone: '',
      sms: '',
      smsDisabled: false,
      countdown: 0,
      phone: this.$store.getters.mobile,
      activityId: '',
      currChild: null,
      activityInfo: null,
      requireList: {},
      pdb: { paddingBottom: '60px' },
      childList: [{ text: '+添加', value: -1 }],
      classList: [{ text: '其他', value: -1 }],
      schoolList: [{ text: '其他', value: -1 }],
      selectPart: {
        text: '',
        value: ''
      },
      selectStuName: {
        text: '',
        value: ''
      },
      selectClass: {
        text: '',
        value: ''
      },
      selectSchoolName: {
        text: '',
        value: ''
      },
      selectGender: {
        text: '',
        value: ''
      },
      genderList: [
        {
          text: '男',
          value: 'MALE'
        },
        {
          text: '女',
          value: 'FEMALE'
        },
        {
          text: '保密',
          value: 'SECRET'
        }
      ],
      fieldValue: '',
      cascaderValue: '',
      show: false,
      showPicker: false,
      showPart: false,
      showStuName: false,
      showSchoolName: false,
      showGender: false,
      stuName: '',
      subLoading: false,
      userInfo: null,
      isActive: false,
      areaList,
      areaObj: {
        province: '',
        city: '',
        district: ''
      },
      customSchoolName: '',
      schoolCustom: false,
      classNameCustom: false,
      customClassNameValue: null,
      customStuName: '',
      stuNameCustom: false,
      peviewUrl: '',
      inApply: false,
      childApplyList: []
    }
  },
  computed: {
    isInData () {
      return moment().isBefore(this.activityInfo && this.activityInfo.endSubmit)
    },
    isInApplyDate() {
      return moment().isBefore(this.activityInfo && this.activityInfo.endApply)
    }
  },
  mounted () {
    this.activityId = this.$route.query.id
    // const currChild = JSON.parse(localStore.read('currChild'))
    // this.currChild = currChild
    if (this.activityId) {
      this._getActivityInfo()
      this._getChildInfo()
      this._getParentChildren()
    }
    this.$nextTick(() => {
      this.pdb = { paddingBottom: `${this.$refs.submitBottom.clientHeight}px` }
    })
    if (!this.$store.getters.mobile) {
      this.showPhone = true
    }
  },
  methods: {
    submitWork(item, edit = 0) {
      localStore.save('currChild', JSON.stringify(item))
      if (this.isInData) {
        this.$router.push({ path: '/h5/submit-work', query: { id: this.activityId, edit }})
      } else {
        if (!edit) {
          this.$toast('提交日期已结束')
          return
        }
        registeredActivityList({ childUserId: item.id }).then(res => {
          const obj = res.data.find(e => e.id === Number(this.activityId))
          if (obj) {
            this.$router.push({ path: '/h5/check-work', query: { id: this.activityId, workId: obj.works.id }})
          }
        })
      }
    },
    prev () {
      this.$router.push({
        'path': '/h5/activity',
        'query': {
          'activityId': this.activityId
        }
      })
    },
    async _getActivityInfo (id) {
      const obj = { activityId: this.activityId, childUserId: this.currChild && this.currChild.id }
      if (id) {
        obj.childUserId = id
      }
      const { data } = await getActivityInfo(obj)
      this.activityInfo = data
      this.inApply = getTimestamp(Date()) > this.activityInfo.startTime && getTimestamp(Date()) < this.activityInfo.endApply
      // if (this.activityInfo && this.activityInfo.registered) {
      //   this.$router.push({ path: '/h5/submit-work', query: { id: this.activityId }})
      //   return
      // }
      // 显示判断
      if (data.worksForm) {
        const list = JSON.parse(data.worksForm)
        const obj = {}
        list.forEach(e => {
          obj[e.type] = e
        })

        this.requireList = obj
      }
    },
    async _getParentChildren () {
      const { data } = await getParentChildren({
        sourceId: this.activityId,
        scene: 'ACTIVITY_MEMBER'
      })
      this.childApplyList = data
      const arr = [{ text: '+添加', value: -1 }]
      if (data) {
        data.map((val) => {
          arr.push({ text: val.displayName, value: val.id })
        })
        this.childList = arr
      }
    },
    async _getChildInfo (id = 0) {
      const currChild = JSON.parse(localStore.read('currChild'))
      if (!currChild && !id) return
      const obj = {}
      if (id) {
        // 切换用户
        obj.childId = id
      } else {
        obj.childId = currChild.id
      }
      const { data } = await getChildInfo(obj)
      if (id) {
        localStore.save('currChild', JSON.stringify(data.userInfo))
      }
      this.userInfo = data
      if (data.school) {
        const school = data.school
        const obj = {
          province: school.province,
          city: school.city,
          district: school.district
        }
        this.areaObj = { ...obj }
        if (school.province || school.city || school.district) {
          this.fieldValue = `${school.province ? school.province + '/' : ''}${school.city ? school.city + '/' : ''}${school.district}`
        } else {
          this.fieldValue = ''
        }
        this._getSchoolList(obj)
        this.selectSchoolName = {
          text: school.name,
          value: data.school.id
        }
        if (data.school.id) {
          this._getSchoolClassList(data.school.id)
        }
      } else if (data.userInfo) {
        const school = data.userInfo
        const obj = {
          province: school.province,
          city: school.city,
          district: school.district
        }
        this.areaObj = { ...obj }
        if (school.province || school.city || school.district) {
          this.fieldValue = `${school.province ? school.province + '/' : ''}${school.city ? school.city + '/' : ''}${school.district}`
        } else {
          this.fieldValue = ''
        }
        this._getSchoolList(obj)
        this.selectSchoolName = {
          text: (data.studentInfo && data.studentInfo.school) || '',
          value: ''
        }
        this.customClassNameValue = []
        this.customClassNameValue[0] = {
          value: (data.studentInfo && data.studentInfo.grade) || ''
        }
        this.customClassNameValue[1] = {
          value: (data.studentInfo && data.studentInfo.classroom) || ''
        }
      }
      if (data.belongClassId) {
        this.selectClass = {
          text: data.className,
          value: data.belongClassId
        }
      } else {
        this.selectClass = {
          text: data.className,
          value: ''
        }
      }

      this.selectStuName = {
        text: data.name,
        value: data.userId
      }

      switch (data.userInfo.gender) {
        case 'MALE':
          this.selectGender = this.genderList[0]
          break
        case 'FEMALE':
          this.selectGender = this.genderList[1]
          break

        default:
          this.selectGender = this.genderList[2]
          break
      }
    },
    async _getSchoolList (obj) {
      const { data } = await getSchoolList(obj)
      const arr = [{ text: '其他', value: -1 }]
      if (data) {
        data.map((val) => {
          arr.push({ text: val.name, value: val.id })
        })
        this.schoolList = arr
      }
    },
    async _getSchoolClassList (schoolId) {
      const { data } = await getSchoolClassList({ schoolId })
      this.classList = []
      const arr = [{ text: '其他', value: -1 }]
      if (data) {
        data.map((val) => {
          arr.push({ text: val.name, value: val.userId })
        })
        this.classList = arr
      }
    },
    onPartNameConfirm (val, index) {
      if (!val) return
      this.selectPart = {
        text: val,
        value: val
      }
      this.showPart = false
    },
    onSchoolNameConfirm (value) {
      if (!value) return
      if (value.value === -1) {
        if (!this.customSchoolName) {
          Toast('未填写学校名称')
          return
        }
        this.selectSchoolName = {
          text: this.customSchoolName,
          value: ''
        }
      } else {
        this.selectSchoolName = value
      }
      this.showSchoolName = false
      this._getSchoolClassList(value.value)
      this.selectClass = {
        text: '',
        value: ''
      }
    },
    schoolListChange (ref, val) {
      if (val.value === -1) {
        this.schoolCustom = false
      } else {
        this.customSchoolName = ''
        this.schoolCustom = true
      }
    },
    onConfirm (value) {
      if (!value) return
      if (value.value === -1) {
        if (!this.customClassNameValue) {
          Toast('未填写班级')
          return
        }
        this.selectClass = {
          text: this.customClassNameValue[0].text + this.customClassNameValue[1].text,
          value: ''
        }
      } else {
        this.selectClass = value
      }

      this.showPicker = false
    },
    classListChange (ref, val) {
      if (val.value === -1) {
        this.classNameCustom = false
      } else {
        this.classNameCustom = true
        this.customClassNameValue = null
      }
    },
    customClassName (val) {
      this.customClassNameValue = val
    },
    onGenderConfirm (value) {
      if (!value) return
      this.selectGender = value
      this.showGender = false
    },
    onFinish (val) {
      this.show = false
      this.fieldValue = val.map((option, index) => {
        switch (index) {
          case 0:
            this.areaObj.province = option.name
            break
          case 1:
            this.areaObj.city = option.name
            break
          case 2:
            this.areaObj.district = option.name
            break
        }
        return option.name
      }).join('/')
      this._getSchoolList(this.areaObj)
      this.selectSchoolName = {
        text: '',
        value: ''
      }
      this.selectClass = {
        text: '',
        value: ''
      }
    },
    childListChange (ref, val) {
      if (val.value === -1) {
        this.stuNameCustom = false
      } else {
        this.stuNameCustom = true
        this.customStuName = ''
      }
    },
    restData () {
      this.fieldValue = ''
      this.selectClass = {
        text: '',
        value: ''
      }
      this.selectSchoolName = {
        text: '',
        value: ''
      }
      this.selectGender = {
        text: '',
        value: ''
      }
      this.areaObj = {
        province: '',
        city: '',
        district: ''
      }
      this.customClassNameValue = null
    },
    onChildNameConfirm (value) {
      if (!value) return
      if (value.value === -1) {
        if (!this.customStuName) {
          Toast('未填写作者姓名')
          return
        }
        this.selectStuName = {
          text: this.customStuName,
          value: -1
        }
        if (this.activityInfo) {
          this.activityInfo.registered = false
        }
        this.restData()
        this.childApplyList.push({
          id: -1,
          displayName: this.customStuName,
          invalidType: '活动未报名'
        })
      } else {
        this.selectStuName = value
        this._getChildInfo(value.value)
        this._getActivityInfo(value.value)
      }
      this.showStuName = false
    },
    nextStep () {
      this.$router.push({ path: '/h5/submit-work', query: { id: this.activityId }})
    },
    async bind () {
      if (this.subLoading) return
      this.subLoading = true
      try {
        if (!this.isInApplyDate) {
          this.$toast('报名日期已截止')
          this.subLoading = false
          return
        }
        if (this.requireList.part && this.requireList.part.required && !this.selectPart.text) {
          this.$toast('参赛组别未填写')
          this.subLoading = false
          return
        }
        if (!this.selectStuName.text) {
          this.$toast('作者姓名未填写')
          this.subLoading = false
          return
        }
        if (this.requireList.gender && this.requireList.gender.required && !this.selectGender.text) {
          this.$toast('性别未填写')
          this.subLoading = false
          return
        }
        if (this.requireList.area && this.requireList.area.required && !this.fieldValue) {
          this.$toast('所在地区未填写')
          this.subLoading = false
          return
        }
        if (this.requireList.school_id && this.requireList.school_id.required && !this.selectSchoolName.text) {
          this.$toast('学校名称未填写')
          this.subLoading = false
          return
        }

        if (this.requireList.class_name && this.requireList.class_name.required && !this.selectClass.text) {
          this.$toast('未选择班级')
          this.subLoading = false
          return
        }
        const gender = this.selectGender.value === 'SECRET' ? '' : this.selectGender.value
        const obj = {
          schoolId: this.selectSchoolName.value || 0,
          classId: this.selectClass.value || 0,
          gender,
          childName: this.selectStuName.text
        }

        const objData = { ...this.areaObj }

        // if (this.selectSchoolName.text && !this.selectSchoolName.value) {
        // }
        objData.school = this.selectSchoolName.text

        if (this.selectClass.text && !this.selectClass.value && this.customClassNameValue) {
          objData.grade = this.customClassNameValue[0].value
          objData.level = this.customClassNameValue[0].level
          objData.classroom = this.customClassNameValue[1].value
        }

        const paramObj = {
          activityId: this.activityId
        }

        if (this.selectPart.text) {
          paramObj.part = this.selectPart.value
        }

        if (this.selectStuName.value === -1) {
          const { data } = await parentBindChild(obj, objData)
          localStore.save('currChild', JSON.stringify(data))
          paramObj.childUserId = data.id
          await registerActivity(paramObj)
        } else {
          obj.childId = this.selectStuName.value
          await parentBindChild(obj, objData)
          paramObj.childUserId = this.selectStuName.value
          await registerActivity(paramObj)
        }

        this.$router.push({ path: '/h5/submit-work', query: { id: this.activityId }})

        this.subLoading = false
      } catch (error) {
        this.subLoading = false
      }
    },
    checkXieyi () {
      this.peviewUrl = window.location.origin + '/xieyi.html'
    },
    getCode () {
      if (this.smsDisabled) return
      const mobile = this.popPhone
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this._verifyCodeForWeb()
    },
    _verifyCodeForWeb () {
      verifyCodeForWeb({ mobile: this.popPhone }).then(res => {
        this.countdown = 60
        this.smsDisabled = true
        setTimeout(this.tick, 1000)
      })
    },
    tick () {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    },
    async submitMobile () {
      if (this.popPhone && this.sms) {
        const { data } = await bindMobile({ mobile: this.popPhone, smsCode: this.sms })
        setPartentToken('Bearer ' + data.access_token)
        this.$store.commit('user/SET_MOBILE', this.popPhone)
        this.$store.commit('user/SET_PARTENT_TOKEN', 'Bearer ' + data.access_token)
        this.showPhone = false
        location.reload()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.apply-table {
  width: 90%;
  border-collapse: collapse;
  margin-top: 10px;
  margin: 0 auto;
  font-size: 14px;
  text-align: center;
}

.apply-table-head {
  background-color: #f5f5f5;
}

.apply-table-row {
  border-bottom: 1px solid #ddd;
}

.apply-table-header {
  padding: 10px;
  font-weight: bold;

}

.apply-table-cell {
  padding: 10px;
}

.apply-table-action {
  color: #2F80ED;
  cursor: pointer;
}
.apply {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  box-sizing: border-box;
  padding-top: 50px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  overflow-y: auto;

  .active-name {
    font-size: 14px;
    line-height: 20px;
    height: 40px;
    display: -webkit-box;
    word-break: break-all;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; //需要显示的行数
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 12px;
    box-sizing: border-box;
    text-align: center;
  }

  .active-time {
    font-size: 12px;
    color: #828282;
    text-align: center;
    // margin: 0 12px;
    // padding: 10px 12px;
    box-sizing: border-box;
    // border-bottom: 1px solid #E0E0E0;
  }

  .a-tag-name {
    height: 40px;
    display: flex;
    align-items: center;
    color: #000000;
    margin: 10px 12px 0 12px;
    font-size: 16px;

    &::before {
      width: 4px;
      height: 20px;
      margin-right: 5px;
      background: #2F80ED;
      border-radius: 17px;
      content: "";
    }
  }
}

.submit-btn-box {
  width: 100%;
  box-sizing: border-box;
  background: #FFFFFF;
  // box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  // padding-bottom: constant(safe-area-inset-bottom);
  // padding-bottom: env(safe-area-inset-bottom);

  .submit-btn {
    width: 150px;
    height: 42px;
    background: #2F80ED;
    border-radius: 50px;
    color: #FFFFFF;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
  }
}

.info-card {
  min-height: 100px;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;

  .down {
    width: 0;
    height: 0;
    margin-top: 5px;
    border: 5px solid transparent;
    border-top: 6px solid #575B66;
  }

  .input-lable {
    color: #4F4F4F;
    font-weight: 500;
    font-size: 16px;
    flex-shrink: 0;
    min-width: 80px;
    position: relative;

    .red-icon {
      color: red;
      position: absolute;
      left: -10px;
    }
  }

  .custom-input {
    padding: 10px 20px;
  }

  .mt20 {
    margin-top: 15px;
  }

  .mt40 {
    margin-top: 20px;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .p-city-input,
  .p-input {
    border: 1px solid #E0E0E0;
    border-radius: 8px;
    overflow: hidden;

    ::placeholder {
      color: #828282;
      font-size: 12px;
    }

    ::v-deep .van-field__control {
      color: #000 !important;
    }

    ::v-deep .van-cell::after {
      border: none !important;
    }
  }

  .p-input {
    width: 100%;
  }

}
.rule-tips {
    padding: 10px 12px 10px 12px;
    font-weight: 300;
    font-size: 14px;
    color: #000000;
    display: flex;
    justify-content: center;
    align-items: center;

    .el-icon-circle-check {
      color: #27AE60;
      font-size: 20px;
      margin-right: 5px;
    }
  }
  .iframe-box {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99999;
  background: #fff;

  .close {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}

.pop-chang-phone {
  height: calc(100% - 60px);
  margin-top: 30px;
  padding: 0 20px 10px 20px;
  box-sizing: border-box;
  overflow-y: auto;

  .title {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
  }

  .p-btn {
    width: 100%;
    height: 50px;
    font-size: 16px;
    color: #FFFFFF;
    background: #2F80ED;
    border-radius: 10px;
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .p-login-input {
  border: 1px solid #E0E0E0;
  margin-top: 20px;
  border-radius: 5px;
  overflow: hidden;

  ::placeholder {
    color: #828282;
    font-size: 12px;
  }

  .sms {
    color: #000000;
    font-size: 12px;
  }
}
}
</style>
