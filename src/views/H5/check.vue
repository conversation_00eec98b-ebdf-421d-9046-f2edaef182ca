<template>
  <div class="h5-content">
    <div v-if="list" class="head">
      <div class="title">
        {{ list.name }}
      </div>
      作品提交时间：{{ list.startTime | formateTime }}-{{
        list.endTime | formateTime
      }}
    </div>

    <div class="info-title">
      <img src="@/assets/H5/title-tip.png" />
      作品信息
    </div>
    <div class="info-box">
      <div class="form-box form-bottom">
        <div class="form-required">
          <img src="@/assets/H5/require.png" />
        </div>
        <div class="form-name">作品名称：</div>
        <div class="form-content">
          {{ activityForm.name }}
        </div>
      </div>

      <div class="form-box">
        <div class="form-required">
          <!-- <img src="@/assets/H5/require.png" /> -->
        </div>
        <div class="form-name">作品简介：</div>
      </div>
      <div class="form-box">
        <div class="form-required">
          <!-- <img src="@/assets/H5/require.png" /> -->
        </div>
        <div class="form-content">
          {{ activityForm.introduce }}
        </div>
      </div>

      <div class="form-box">
        <div class="form-required">
          <img src="@/assets/H5/require.png" />
        </div>
        <div class="form-name">上传作品：</div>
      </div>
      <div>
        <div v-for="item in activityForm.resourceList" :key="item.id" class="file-list">
          <div class="file-img">
            <img src="@/assets/H5/links.png" />
          </div>
          <div class="file-name">
            {{ item.fileName }}
          </div>
        </div>
      </div>
    </div>

    <div class="info-title">
      <img src="@/assets/H5/title-tip.png" />
      参赛信息
    </div>

    <div class="info-box">
      <div v-if="formShowListMap.has('mobile')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('mobile') &&
                formShowListMap.get('mobile').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('mobile').title }}：</div>
        <div class="form-content">
          {{ activityForm.mobile }}
        </div>
      </div>

      <div v-if="formShowListMap.has('author')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('author') &&
                formShowListMap.get('author').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('author').title }}：</div>
        <div class="form-content">
          {{ activityForm.author }}
        </div>
      </div>

      <div v-if="formShowListMap.has('gender')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('gender') &&
                formShowListMap.get('gender').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('gender').title }}：</div>
        <div class="form-content">
          {{ activityForm.gender | genderFilter }}
        </div>
      </div>

      <div v-if="formShowListMap.has('age')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('age') && formShowListMap.get('age').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('age').title }}：</div>
        <div class="form-content">
          {{ activityForm.age }}
        </div>
      </div>

      <div v-if="formShowListMap.has('schoolId')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('schoolId') &&
                formShowListMap.get('schoolId').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('schoolId').title }}：</div>
        <div class="form-content">
          {{ activityForm.schoolName }}
        </div>
      </div>

      <div v-if="formShowListMap.has('part')" class="form-box form-bottom">
        <div
          class="form-required"
        >
          <img v-if=" formShowListMap.get('part') && formShowListMap.get('part').required " src="@/assets/H5/require.png" />
        </div>
        <div class="form-name">{{ formShowListMap.get('part').title }}：</div>
        <div class="form-content">
          {{ formShowListMap.get('part').option[activityForm.part] }}
        </div>
      </div>

      <div v-if="formShowListMap.has('email')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('email') &&
                formShowListMap.get('email').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('email').title }}：</div>
        <div class="form-content">
          {{ activityForm.email }}
        </div>
      </div>

      <div v-if="formShowListMap.has('adviser')" class="form-box form-bottom">
        <div class="form-required">
          <img
            v-if="
              formShowListMap.get('adviser') &&
                formShowListMap.get('adviser').required
            "
            src="@/assets/H5/require.png"
          />
        </div>
        <div class="form-name">{{ formShowListMap.get('adviser').title }}：</div>
        <div class="form-content">
          {{ activityForm.adviser }}
        </div>
      </div>
    </div>

    <div class="btn-box">
      <div class="btn2" @click="$router.push({ path: '/h5/index?id=' + activityID })">返回</div>
      <div class="btn" @click="showPop = true">取消报名</div>
    </div>
    <popup v-if="showPop" :tips-show="true" @cancle="showPop = false" @submit="cancle">
      <template #title> 确认取消吗 </template>
      <template #tips> 取消后，您所上传的资料将全部清空 </template>
    </popup>
  </div>
</template>

<script>
import popup from './components/popup.vue'
import moment from 'moment'
import { getActivityInfo, getUserActivityWorks, cancelActivityWorks } from '@/api/activity-api.js'
export default {
  components: {
    popup
  },
  filters: {
    formateTime (date) {
      return moment(date).format('YYYY.MM.DD HH:mm')
    },
    genderFilter (data) {
      if (data) {
        return data === 'FEMALE' ? '女' : '男'
      } else {
        return ''
      }
    }
  },
  data () {
    return {
      showPop: false,
      activityID: 0,
      list: null,
      ossUrl: '',
      fileList: [],
      beforeUp: new Map(),
      dataObj: {
        key: '',
        policy: '',
        OSSAccessKeyId: '',
        'success_action_status': '200',
        callback: '',
        signature: ''
      },
      formShowList: null,
      formShowListMap: new Map(),
      submitFileList: null,
      requireList: [],
      schoolList: [],
      activityForm: {
        name: '',
        part: '',
        introduce: '',
        author: '',
        gender: null,
        age: '',
        email: '',
        schoolId: '',
        mobile: '',
        adviser: ''
      },
      partName: []
    }
  },
  watch: {
    fileList: {
      handler: function (val) {
        if (val) {
          this.handleInputBlur()
        }
      },
      deep: true
    }
  },
  mounted () {
    this.activityID = this.$route.query.id
    if (this.activityID) {
      this._getActivityInfo()
      this._getUserActivityWorks()
    }
  },
  methods: {
    async _getUserActivityWorks () {
      const token = window.localStorage.getItem('h5token')

      if (token) {
        const { data } = await getUserActivityWorks({ activityId: this.activityID }, token)
        this.activityForm = data
      } else {
        this.$message.error('登录失效')
      }
    },
    async _getActivityInfo () {
      const { data } = await getActivityInfo({ activityId: this.activityID })
      this.list = data
      this.formShowList = JSON.parse(data.worksForm)
      this.formShowListMap = new Map()
      this.requireList = ['name']
      this.formShowList.map(val => {
        if (val.required) {
          if (val.type === 'school_id') {
            this.requireList.push({
              type: 'schoolId',
              title: '所在学校'
            })
          } else {
            this.requireList.push({
              type: val.type,
              title: val.title
            })
          }
        }
        switch (val.type) {
          case 'author':
            this.formShowListMap.set('author', val)
            break
          case 'mobile':
            this.formShowListMap.set('mobile', val)
            break
          case 'school_id':
            this.formShowListMap.set('schoolId', val)
            break
          case 'age':
            this.formShowListMap.set('age', val)
            break
          case 'email':
            this.formShowListMap.set('email', val)
            break
          case 'part':
            this.formShowListMap.set('part', val)
            break
          case 'gender':
            this.formShowListMap.set('gender', val)
            break
          case 'adviser':
            this.formShowListMap.set('adviser', val)
            break
        }
      })
    },
    async cancle () {
      const token = window.localStorage.getItem('h5token')
      if (token) {
        try {
          const { data } = await cancelActivityWorks({ activityWorksId: this.activityForm.id }, token)
          console.log(data)
          window.localStorage.removeItem('h5token')
          this.showPop = false
          this.$message.success('取消成功')
          this.$router.push({ path: '/h5/index?id=' + this.activityID })
        } catch (error) {
          // this.$message.error('取消失败')
          this.showPop = false
        }
      } else {
        this.$message.error('登录失效')
        this.showPop = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.h5-content {
  width: 100%;
  height: 100%;
  background: #f0f8ff;

  .head {
    background: #fff;
    margin-bottom: 5px;
    font-size: 12px;
    color: #151414;
    padding: 20px;
    text-align: center;

    .title {
      font-weight: 500;
      font-size: 14px;
      color: #151414;
      line-height: 22px;
      text-align: center;
      @include ellipses(2);
      margin-bottom: 10px;
    }
  }
  .info-box {
    background: #fff;
    margin-bottom: 5px;
    padding: 20px;
  }

  .form-box {
    display: flex;
    padding: 10px 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .form-required {
      width: 14px;
      height: 14px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .form-name {
      min-width: 80px;
      font-size: 14px;
      color: #151414;
    }
    .form-content {
      flex: 1;
      color: #929292;
      .el-select {
        width: 100%;
      }
      ::v-deep .el-input__inner {
        border: none;
      }
    }
  }
  .form-bottom {
    border-bottom: 1px solid #f8f8f8;
  }
}

.upload {
  width: 100%;
  min-height: 90px;
  border: 1px solid #f2f1f1;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #717171;

  ::v-deep .el-upload-list {
    align-self: flex-start;
    width: 100%;
  }
}

.info-title {
  height: 40px;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  color: #151414;
  background: #fff;
  border-bottom: 1px dashed rgba(54, 115, 169, 0.11);
  img {
    width: 20px;
    height: 10px;
    margin-right: 10px;
  }
}

.file-list {
  width: 100%;
  padding: 5px 0;
  display: flex;

  .file-img {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .file-name {
    min-width: 80px;
    font-size: 12px;
    color: #929292;
    @include ellipses(1);
  }
}

.f12 {
  font-size: 12px;
}

.f14 {
  font-size: 14px;
}

.btn-box {
  background: #f0f8ff;
  height: 80px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .btn {
    border-radius: 15px;
    width: 40%;
    // min-width: 200px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    border: 1px solid #1f66ff;
    color: #1f66ff;
  }
  .btn2 {
    border-radius: 15px;
    width: 40%;
    // min-width: 200px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #fff;
    background: #1f66ff;
  }
}
</style>
