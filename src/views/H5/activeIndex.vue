<template>
  <div class="h5-content">
    <div v-if="list" class="content-box">
      <img class="banner" :src="list.cover" />
      <div class="a-title-box">
        <div class="a-title">
          <div class="title">
            {{ list.name }}
          </div>
          <div class="sub">
            <div class="item">
              <div>活动时间：</div>
              <div class="detail">
                {{ list.startTime | formateTime }}-{{ list.endTime | formateTime }}
              </div>
            </div>
            <div class="item">
              <div>提交时间：</div>
              <div class="detail">
                {{ list.startTime | formateTime }}-{{ list.endSubmit | formateTime }}
              </div>
            </div>
            <div class="item">
              <div>主办单位：</div>
              <div class="detail">{{ list.host }}</div>
            </div>
            <div class="item">
              <div>承办单位：</div>
              <div class="detail">{{ list.undertake }}</div>
            </div>
            <div class="item">
              <div>协办单位：</div>
              <div class="detail">{{ list.assisting }}</div>
            </div>
          </div>
        </div>

        <div class="a-detail-box">
          <div class="link-btn-box">
            <img src="@/assets/H5/btn1.png" />
            <img src="@/assets/H5/btn1.png" />
          </div>
          <div class="tips">
            <img src="@/assets/H5/star1.png" />
            活动指南
            <img src="@/assets/H5/star2.png" />
          </div>
          <div class="actives flex flex-col">
            <img v-for="item in posters" :key="item" :src="item" />
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <div v-if="showHistory" class="flex flex-col justify-center items-center h" @click="my">
        <div><img src="@/assets/H5/apply.png" /></div>
        <div>我的报名</div>
      </div>
      <div class="flex flex-col justify-center items-center h" @click="copyData">
        <div><img src="@/assets/H5/share.png" /></div>
        <div>分享</div>
      </div>
      <div :class="submitState ? 'btn' : 'btn-disable'" @click="apply">
        {{ submitText }}
      </div>
    </div>
    <!-- <popup v-if="showPop" :tips-show="true" @cancle="showPop = false">
      <template #title> 确认提交吗 </template>
      <template #tips> 取消后，您所上传的资料将全部清空111 </template>
    </popup> -->
  </div>
</template>

<script>
import moment from 'moment'
// import popup from './components/popup.vue'
import { getActivityInfo, getUserActivityWorks } from '@/api/activity-api.js'
export default {
  components: {
    // popup
  },
  filters: {
    formateTime (date) {
      return moment(date).format('YYYY.MM.DD HH:mm')
    }
  },
  data () {
    return {
      showPop: true,
      activityID: 0,
      list: null,
      posters: null,
      showHistory: false
    }
  },
  computed: {
    submitState () {
      if (this.list) {
        if (moment(new Date()).isBetween(this.list.startTime, this.list.endSubmit, null, '[]')) {
          return true
        } else {
          return false
        }
      }
      return false
    },
    submitText () {
      if (this.list) {
        if (moment(new Date()).isBefore(this.list.startTime)) {
          return '活动未开始'
        } else if (moment(new Date()).isAfter(this.list.endSubmit)) {
          return '已截止'
        } else {
          return '我要报名'
        }
      }
      return ''
    }
  },
  mounted () {
    // document.title = '111'
    this.activityID = this.$route.query.id
    if (this.activityID) {
      this._getActivityInfo()
      this._getUserActivityWorks()
    }
  },
  methods: {
    async _getActivityInfo () {
      const { data } = await getActivityInfo({ activityId: this.activityID })
      this.list = data
      this.posters = data.posters.split('|')
    },
    async _getUserActivityWorks () {
      const token = window.localStorage.getItem('h5token')
      if (token) {
        const { data } = await getUserActivityWorks({ activityId: this.activityID }, token)
        if (data) {
          this.showHistory = true
        } else {
          this.showHistory = false
        }
      } else {
        this.showHistory = false
      }
    },
    apply () {
      if (this.submitState) {
        this.$router.push({ path: '/h5/submit?id=' + this.activityID })
      }
    },
    my () {
      this.$router.push({ path: '/h5/check?id=' + this.activityID })
    },
    copyData () {
      const url = window.location.href
      this.$copyText(url).then(e => {
        this.$message({
          message: '链接已复制',
          center: true
        })
      }, function (e) {
        this.$message({
          message: '复制失败',
          center: true
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.h5-content {
  width: 100%;
  height: 100%;
  background: #f0f8ff;

  .content-box {
    width: 100%;
    height: 167px;
    position: relative;
    .banner {
      width: 100%;
      height: 167px;
      object-fit: cover;
    }

    .a-title-box {
      position: absolute;
      top: 155px;
      width: 100%;
      padding: 0 15px;
      padding-bottom: 60px;
      .a-title {
        padding: 20px;
        box-sizing: border-box;
        background: #fff;
        box-shadow: 0 -2px 6px 0 rgba(31, 102, 255, 0.05);
        border-radius: 10px;
        min-height: 170px;
        color: #151414;

        .title {
          font-weight: 500;
          font-size: 14px;
          color: #151414;
          line-height: 22px;
          @include ellipses(2);
        }
        .sub {
          margin-top: 15px;
          font-size: 12px;
          .item {
            line-height: 22px;
            padding: 2.5px 0;
            display: flex;
            flex-wrap: wrap;

            .detail {
              flex: 1;
            }
          }
        }
      }

      .a-detail-box {
        position: relative;
        margin-top: 15px;
        background: #FFFFFF;
        box-shadow: 0 -2px 6px 0 rgba(31,102,255,0.05);
        border-radius: 10px;
        padding: 10px;
        .link-btn-box {
          position: absolute;
          top: -23px;
          left: 0;
          display: flex;
          justify-content: space-between;
          padding: 0 30px;
          width: 100%;
          img {
            width: 15px;
            height: 29px;
          }
        }
        .tips {
          padding: 5px 0;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          font-weight: 500;
          font-size: 14px;
          color: #151414;
          border-bottom: 1px dashed rgba(54, 115, 169, .11);
          img {
            width: 13px;
            height: 13px;
            margin: 0 8px;
          }
        }

        .actives {
          margin-top: 10px;
          img {
            width: 100%;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  .footer {
    position: fixed;
    width: 100%;
    bottom: 0;
    height: 60px;
    display: flex;
    align-items: center;
    background: #fff;
    display: flex;
    justify-content: space-around;
    img {
      width: 16px;
      height: 16px;
    }
    .btn, .btn-disable {
      border-radius: 15px;
      width: 200px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
    }
    .btn {
      background: #1F66FF;
      color: #fff;
    }
    .btn-disable {
      background: #B9B9B9;
      color: #5D5D5D;
    }
  }
}
.f12 {
  font-size: 12px;
}

.f14 {
  font-size: 14px;
}
</style>
