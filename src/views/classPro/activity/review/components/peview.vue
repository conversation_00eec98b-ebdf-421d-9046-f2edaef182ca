<template>
  <div class="preview">
    <iframe
      v-if="previewFile && previewFile.type === 'FILE'"
      ref="iframe"
      :src="previewFile.url"
      width="100%"
      height="100%"
      frameborder="0"
      scrolling="auto"
    ></iframe>

    <div v-if="previewFile && previewFile.type === 'VIDEO'" style="width:100%;height:100%">
      <videoJs :options="videoOptions" />
    </div>

    <img v-else-if="previewFile && previewFile.type === 'IMAGE'" class="image" :src="previewFile.url" alt="" />

    <div v-else class="empty-container" style="width:100%;height:100%">
      <img src="@/assets/images/empty3.png" alt="ai课堂为空" />
      <div class="hint">暂时还没有学生作品</div>
    </div>
  </div>
</template>

<script>
import VideoJs from '@/components/classPro/video/index.vue'
export default {
  components: {
    VideoJs
  },
  props: {
    previewFile: {
      type: Object,
      require: false,
      default: () => undefined
    }
  },
  data () {
    return {
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: true,
        fileShow: false,
        loop: false,
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    }
  },
  watch: {
    previewFile: {
      handler: function (file) {
        if (file && ['VIDEO'].indexOf(file.type) > -1) {
          this.videoOptions = {
            ...this.videoOptions,
            sources: [{
              src: file.url,
              type: 'video/mp4'
            }]
          }
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.preview {
    width: 100%;
    height: 100%;

    .image {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .empty-container {

      width: 100%;
      height: 100%;
      background: white;

      img {
        width: 128px;
        height: 128px;
      }

      .hint {
        font-weight: 400;
        font-size: 14px;
        color: #8C8C8C;
        letter-spacing: 0;
        margin-top: 10px;
      }
    }
}
</style>
