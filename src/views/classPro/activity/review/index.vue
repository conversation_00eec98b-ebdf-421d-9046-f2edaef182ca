<template>
  <div class="activity-review">

    <!-- 内容 -->
    <div class="content flex">
      <div class="preview-left mr-20">
        <div class="flex justify-between mb-40">
          <div class="btn-rate" :class="{'btn-rate-active': showType === 0}" @click="changeShowType(0)">未评</div>
          <div class="btn-rate" :class="{'btn-rate-active': showType === 1}" @click="changeShowType(1)">已评</div>
        </div>
        <div class="rate-list">
          <div class="rate-title grid-cols-3">
            <span>ID</span>
            <span>姓名</span>
            <span>得分</span>
          </div>
          <div class="line"></div>
          <div v-if="curentWorkList.length > 0" class="member-list">
            <div
              v-for="item in curentWorkList"
              :key="item.id"
              class="member-item grid-cols-3"
              :class="{
                'member-item-active': currentScoreWork && currentScoreWork.id === item.id
              }"
              @click="chooseWork(item)"
            >
              <span class="flex items-center justify-center">{{ item.id || '' }}</span>
              <span class="flex items-center justify-center">{{ item.members | authorName }}</span>
              <span class="member-item-red flex items-center justify-center">{{ item.worksMarks ? item.worksMarks[0].score : '未评分' }}</span>
            </div>
          </div>
          <div v-else class="member-list">
            <div class="empty-container">
              <img src="@/assets/images/empty.png" alt="" />
              <div class="hint">评分列表为空</div>
            </div>
          </div>
        </div>
      </div>
      <div class="preview-center mr-20">
        <div class="works-title mb-30">{{ currentScoreWork ? currentScoreWork.name || '' : '' }}</div>
        <div class="preview mb-20">
          <perview :preview-file="previewFile" />
        </div>
        <div class="intro">
          <div class="activity-title mb-10">作品介绍</div>
          <div v-if="currentScoreWork" class="intro-text">{{ currentScoreWork.introduce || '' }}</div>
        </div>
      </div>
      <div class="preview-right">
        <div class="author mb-20">
          <div class="activity-title mb-20">作者详情</div>
          <div v-if="currentScoreWork" class="info">
            <div class="flex items-center botoom-border" style="flex: 1">
              <div class="label">所在学校</div>
              <div class="label-content">{{ currentScoreWork.schoolName || '' }}</div>
            </div>
            <div class="flex items-center botoom-border" style="flex: 1">
              <div class="label">组别</div>
              <div class="label-content">{{ currentScoreWork.groupName || '' }}</div>
            </div>
            <!-- <div class="flex items-center" style="flex: 1">
              <div class="label">年级</div>
              <div class="label-content">成都市东城根街小学</div>
            </div> -->
          </div>
        </div>
        <div class="works mb-20">
          <div class="activity-title">作品资料</div>
          <div v-if="currentScoreWork" class="works-list">
            <div
              v-for="item in currentScoreWork.resourceList"
              :key="'resource' + item.id"
              class="works-item mb-20"
              :class="{
                'works-item-active': previewFile && previewFile.id === item.id
              }"
            >
              <div class="name" @click="choosePreview(item)">{{ item.fileName }}</div>
              <div class="button" @click="choosePreview(item)">预览</div>
            </div>
          </div>
        </div>
        <div class="score">
          <div class="activity-title mb-20">作品得分・奖项</div>
          <template v-if="currentScoreWork">
            <div class="award-list">
              <div
                v-for="award in awardList"
                :key="award"
                class="award-box"
                :class="{'award-select': currentScoreWork && currentScoreWork.award === award }"
                @click="selectAward(award)"
              >{{ award }}</div>
            </div>
            <div class="flex">
              <el-input v-model="scorInput" placeholder="请打分" :disabled="!canInput" />
              <div class="submit" @click="submit">{{ canInput ? '提交' : '编辑' }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import Perview from './components/peview.vue'
import { getActivityWorkReviewList, markActivityWorks, getActivityInfo, getActivityWorkInfo } from '@/api/activity-api.js'
import { isNumber } from '@/utils/validate.js'
import { debounce } from '@/utils/index'
export default {
  filters: {
    authorName (members) {
      var member = ''
      for (var i = 0; i < members.length; i++) {
        if (i === 0) {
          member += members[i].displayName
        } else {
          member += `、${members[i].displayName}`
        }
      }
      return member
    }
  },
  components: {
    Perview
  },
  data () {
    return {
      isNumber,
      scorInput: '',
      canInput: true,
      expertToken: this.$route.params.expertToken,
      activityId: this.$route.params.activityId,
      scoreWorkList: [],
      noScoreWorkList: [],
      curentWorkList: [],
      awardList: [],
      currentScoreWork: undefined,
      showType: 0,
      previewFile: undefined
    }
  },
  created () {
    this._getActivityWorkReviewList()
    this._getActivityInfo()
  },
  methods: {
    _getActivityWorkReviewList () {
      var params = {
        'token': this.expertToken
      }
      getActivityWorkReviewList(params).then(
        response => {
          this.scoreWorkList = response.data.filter(item => item.worksMarks)
          this.noScoreWorkList = response.data.filter(item => !item.worksMarks)
          this.curentWorkList = this.showType === 0 ? this.noScoreWorkList : this.scoreWorkList
          if (this.noScoreWorkList.length > 0) {
            this.changeShowType(0)
            this.chooseWork(this.noScoreWorkList[0])
            if (this.currentScoreWork.resourceList && this.currentScoreWork.resourceList.length > 0) {
              this.choosePreview(this.currentScoreWork.resourceList[0])
            }
          } else {
            this.changeShowType(1)
          }
        }
      )
    },
    //  切换评分
    changeShowType (showType) {
      if (this.showType === showType) return
      this.showType = showType
      this.curentWorkList = this.showType === 0 ? this.noScoreWorkList : this.scoreWorkList
      this.chooseWork(this.curentWorkList[0])
    },
    //  选择作品
    async chooseWork (work) {
      // this.currentScoreWork = work
      this.currentScoreWork = undefined
      this.previewFile = undefined

      const obj = { activityId: this.activityId, activityWorksId: work.id }
      const { data } = await getActivityWorkInfo(obj)
      this.currentScoreWork = data

      if (this.currentScoreWork && this.currentScoreWork.worksMarks) {
        this.scorInput = this.currentScoreWork.worksMarks[0].score
        this.canInput = false
      } else {
        this.canInput = true
      }
      this.previewFile = undefined
      if (this.currentScoreWork && this.currentScoreWork.resourceList && this.currentScoreWork.resourceList.length > 0) {
        this.choosePreview(this.currentScoreWork.resourceList[0])
      }
    },
    //  预览资料
    choosePreview (file) {
      if (this.previewFile === file) return
      this.previewFile = file
    },
    //  提交打分
    submit: debounce(function () {
      const that = this
      if (!that.currentScoreWork) {
        that.$message.error('请选择作品')
        return
      }
      if (!that.canInput) {
        that.canInput = true
        return
      }
      if ((!that.scorInput || !isNumber(that.scorInput)) && !that.currentScoreWork.award) {
        that.$message.error('请输入正确的分数或选择奖项')
        return
      }
      if (+that.scorInput > 100) {
        that.$message.error('满分为100分')
        return
      }
      var params = {
        'token': that.expertToken,
        'activityWorksId': that.currentScoreWork.id
      }
      if (that.scorInput) params['score'] = that.scorInput
      if (that.currentScoreWork.award) params['award'] = that.currentScoreWork.award
      markActivityWorks(params).then(
        response => {
          that.scorInput = ''
          that.currentScoreWork = undefined
          that.previewFile = undefined
          that._getActivityWorkReviewList()
        }
      )
    }, 1000, true),
    _getActivityInfo () {
      var params = {
        'activityId': this.activityId
      }
      getActivityInfo(params).then(
        response => {
          const activityInfo = response.data
          if (activityInfo.awardList !== '') this.awardList = activityInfo.awardList.split('>')
        }
      )
    },
    selectAward (award) {
      if (!this.canInput) return
      if (this.currentScoreWork.award === award) {
        this.currentScoreWork.award = ''
        return
      }
      this.currentScoreWork.award = award
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-review {
    height: calc(100% - 62px);
    width: 100%;
    position: relative;
    background: #F8FAFF;
}

.navbar {
    width: 100%;
    height: 62px;
    background: url('../../../../assets/images/bg-navbar.png') center center no-repeat;
    background-size: cover;
    padding-left: 28px;
    display: flex;
    align-items: center;

    .logo {
        width: 135px;
        height: 50px;
        margin-right: 71px;
        object-fit: contain;
        line-height: 62px;
        margin: auto 0;
    }
}

.content {
    padding: 10px 10px 10px;
    background: #F1F8FF;
}

.preview-left {
    width: 218px;
    height: 100%;
    background: #FFFFFF;
    border-radius: 5px;
    padding: 20px 14px 0;
    font-weight: 400;
    font-size: 14px;

    .btn-rate {
        width: 124px;
        height: 25px;
        border: 1px solid #E2E2E2;
        border-radius: 15px;
        color: #8C8C8C;
        letter-spacing: 0;
        text-align: center;
        line-height: 25px;
        cursor: pointer;
    }

    .btn-rate-active {
        background: #DFEBFF;
        border: 1px solid #468AFF;
        color: #3479FF;
    }

    .rate-list {
        width: 100%;
        height: 680px;
        border: 1px solid #D4E3FC;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
    }

    .rate-title {
        display: grid;
        font-weight: 500;
        text-align: center;
        height: 35px;
        line-height: 35px;
    }

    .line {
        width: 100%;
        height: 1px;
        background: #DAE7FC;
    }

    .member-list {
      flex: 1;
      overflow: scroll;
      overflow-x: hidden;
      @include scrollBar;
    }

    .member-item {
        display: grid;
        font-weight: 400;
        text-align: center;
        padding: 7px 0;
        cursor: pointer;

        span {
          word-break: break-all;
        }
    }

    .member-item-active {
        background: #F1F8FF;
    }

    .member-item-red {
        color: #F44949;
    }
}

.preview-center {
    flex: 1;
    min-width: 500px;
    height: 100%;
    font-weight: 500;
    font-size: 16px;
    color: #1C1B1A;
    display: flex;
    flex-direction: column;

    .works-title {
        line-height: 22px;
    }

    .preview {
        width: 100%;
        height: 495px;
    }

    .intro {
        width: 100%;
        flex: 1;
        background: #FFFFFF;
        border-radius: 5px;
        padding: 10px 0;

        .activity-title {
            margin: 0 22px 10px;
        }

        .intro-text {
            height: calc(100% - 32px);
            font-weight: 400;
            font-size: 14px;
            color: #191818;
            letter-spacing: 0;
            line-height: 20px;
            overflow: scroll;
            overflow-x: hidden;
            padding: 0 20px;
            @include scrollBar;
        }
    }
}

.preview-right {
    height: 100%;
    width: 322px;
    display: flex;
    flex-direction: column;

    .author {
        width: 100%;
        height: 204px;
        background: #FFFFFF;
        border-radius: 5px;
        padding: 10px 20px;

        .info {
            width: 245px;
            border: 1px solid #F1F8FF;
            display: flex;
            flex-direction: column;
            background: #F1F8FF;
        }

        .label {
            width: 80px;
            line-height: 20px;;
            font-weight: 500;
            font-size: 14px;
            color: #1C1B1A;
            letter-spacing: 0;
            text-align: center;
            background: #F1F8FF;
            min-height: 36px;
            line-height: 36px;
        }

        .label-content {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            line-height: 20px;
            font-weight: 400;
            font-size: 14px;
            color: #1C1B1A;
            letter-spacing: 0;
            text-align: center;
            background: white;
            min-height: 36px;
        }

        .botoom-border {
            border-bottom: 1px solid #F1F8FF;
        }

    }

    .works {
        width: 100%;
        height: 200px;
        background: #FFFFFF;
        border-radius: 5px;
        display: flex;
        flex-direction: column;

        .activity-title {
            margin: 10px 20px 15px;
        }

        .works-list {
            flex: 1;
            overflow: scroll;
            overflow-x: hidden;
            padding: 0 20px;
            @include scrollBar;
        }

        .works-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 7px;
            line-height: 20px;
        }

        .works-item-active {
            background: #F1F8FF;
        }

        .name {
            font-weight: 400;
            font-size: 14px;
            color: #1C1B1A;
            margin-right: 10px;
            word-break: break-all;
            cursor: pointer;
            flex: 1;
        }

        .button {
            width: 50px;
            text-align: center;
            font-weight: 500;
            font-size: 14px;
            color: #3479FF;
            cursor: pointer;
        }
    }

    .score {
        width: 100%;
        flex: 1;
        background: #FFFFFF;
        border-radius: 5px;
        padding: 10px 20px;
        @include scrollBar;
        overflow-x: hidden;

        .award-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-bottom: 20px;
        }

        .award-box {
          height: 40px;
          padding: 0 20px;
          border-radius: 20px;
          font-weight: 400;
          font-size: 14px;
          background: #E1E1E1;
          color: #818181;
          text-align: center;
          line-height: 40px;
          cursor: pointer;
        }

        .award-select {
          background: #3479FF;
          color: #FFFFFF;
        }

        .submit {
            width: 83px;
            height: 40px;
            background: #3479FF;
            border-radius: 20px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            text-align: center;
            line-height: 40px;
            margin-left: 15px;
            cursor: pointer;
        }
    }
}

.activity-title {
    font-weight: 500;
    font-size: 16px;
    color: #1C1B1A;
    letter-spacing: 0;
    position: relative;
    padding-left: 7px;
    line-height: 22px;

    &::after {
        content: '';
        width: 2px;
        height: 14px;
        background: #3479FF;
        border-radius: 1px;
        position: absolute;
        transform: translate(0, -50%);
        left: 0;
        top: 50%;
    }
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-15 {
    margin-bottom: 15px;
}

.mb-20 {
    margin-bottom: 20px;
}
.mb-30 {
    margin-bottom: 30px;
}
.mb-40 {
    margin-bottom: 40px;
}

.mr-20 {
    margin-right: 20px;
}
</style>
