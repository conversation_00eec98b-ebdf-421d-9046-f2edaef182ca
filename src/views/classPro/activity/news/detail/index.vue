<template>
  <div class="news-detail">
    <div class="wrap">

      <!-- 头部 -->
      <div class="header">
        <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="back" />
        <span class="back" @click="back">返回</span>
      </div>

      <!-- 内容 -->
      <div v-if="noticeInfo" class="content mb-40">
        <div class="title mb-15">{{ noticeInfo.title || '' }}</div>
        <div class="flex justify-center mb-15">
          <div class="subtitle mr-45">来源：{{ noticeInfo.source || '' }}</div>
          <div class="subtitle">发布时间：{{ formatDot(noticeInfo.createdAt) }} {{ formatHHmm(noticeInfo.createdAt) }}</div>
        </div>
        <img class="line mb-15" src="@/assets/images/activity/line.png" alt="" />
        <div class="html" v-html="noticeInfo.content"></div>
      </div>

    </div>
  </div>
</template>

<script>
import { getNoticeInfo } from '@/api/system-api.js'
import { formatDot, formatHHmm } from '@/utils/date.js'
export default {
  data () {
    return {
      noticeId: this.$route.params.noticeId,
      noticeInfo: undefined,
      formatDot,
      formatHHmm
    }
  },
  created () {
    this._getNoticeInfo()
  },
  methods: {
    back () {
      this.$router.push({ path: '/classpro/activity' })
    },
    _getNoticeInfo () {
      var params = {
        'noticeId': this.noticeId
      }
      getNoticeInfo(params).then(
        response => {
          this.noticeInfo = response.data
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.news-detail {
  height: calc(100% - 1px);
  // height: calc(100% - 62px);
  width: 100%;
}

.wrap {
  height: 100%;
  padding: 0;
  overflow: hidden;

  .header {
    display: flex;
    align-items: center;
    height: 40px;

    img {
      width: 14px;
      height: 14px;
      object-fit: contain;
      margin-right: 5px;
      cursor: pointer;
    }

    span {
      font-weight: 400;
      font-size: 14px;
      color: #1C1B1A;
      line-height: 20px;
      cursor: pointer;
    }
  }

  .content {
    height: calc(100% - 30px);
    width: 100%;
    background: #FFFFFF;
    border-radius: 5px;
    padding: 30px 40px;
    overflow: hidden;
    overflow-y: auto;
    @include scrollBar;
    margin: 0 auto;

    .title {
      font-weight: 500;
      font-size: 16px;
      color: #1C1B1A;
      text-align: center;
      line-height: 22px;
    }

    .subtitle {
      font-weight: 400;
      font-size: 12px;
      color: #565656;
      text-align: center;
      line-height: 17px;
    }

    .line {
      width: 100%;
    }

    .html {
      white-space: pre-wrap;
    }
  }

  .mb-15 {
    margin-bottom: 15px;
  }

  .mb-40 {
    margin-bottom: 40px;
  }

  .mr-45 {
    margin-right: 45px;
  }
}
</style>
