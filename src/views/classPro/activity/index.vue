<template>
  <div class="activity-box">
    <div class="wrap">

      <div class="card-title-box">
        <div class="t-title">活动</div>
      </div>

      <div class="flex swiper-box">

        <swiper v-if="bannerList" ref="swiper" class="swiper" :options="swiperOption">
          <swiper-slide v-for="banner in bannerList" :key="banner.id">
            <div class="banner">
              <img :src="banner.image" alt="" />
            </div>
          </swiper-slide>
          <div slot="pagination" class="swiper-pagination"></div>
        </swiper>
        <img v-else class="swiper" :src="dfActBanner" alt="" />

        <div class="news flex flex-col">
          <div class="act-title">活动通知</div>
          <div v-if="noticeList.length > 0" class="news-list">
            <div v-for="item in noticeList" :key="item.id" class="news-item" @click="toNewsDetail(item)">
              <div class="circle"></div>
              <span style="flex: 1">{{ item.title }}</span>
              <span>{{ formatYYYYMMDD(item.createdAt) }}</span>
            </div>
          </div>
          <div v-else class="flex flex-col justify-center items-center" style="flex:1">
            <img class="empty mb-10" src="@/assets/images/empty2.png" alt="占位图2" />
            <div class="empty-text">暂时还没有活动哦～</div>
          </div>
        </div>
      </div>

      <div class="bottom flex flex-col">
        <div class="flex">
          <div class="act-title ml-20 mb-10 mr-10">活动列表</div>
          <div
            class="status-btn mr-10"
            :class="{ 'status-btn-active': !progressStatus }"
            @click="changeProgressStatus(undefined)"
          >全部</div>
          <div
            class="status-btn mr-10"
            :class="{ 'status-btn-active': progressStatus === 'GOING' }"
            @click="changeProgressStatus('GOING')"
          >进行中</div>
          <div
            class="status-btn mr-10"
            :class="{ 'status-btn-active': progressStatus === 'FINISHED' }"
            @click="changeProgressStatus('FINISHED')"
          >已结束</div>
          <div
            class="status-btn mr-10"
            :class="{ 'status-btn-active': progressStatus === 'COMING' }"
            @click="changeProgressStatus('COMING')"
          >未开始</div>
        </div>
        <div v-if="activityList.length > 0" class="activity-list activity-grid">
          <div v-for="item in activityList" :key="item.id" class="activity-item" @click="toActivityDetail(item)">
            <img class="mb-5" :src="item.cover" alt="" />
            <div class="name mb-5">{{ item.name }}</div>
            <div class="time">活动时间：{{ formatDot(item.startTime) }}-{{ formatDot(item.endTime) }}</div>
            <div class="label">
              <img v-if="item.progressStatus === 'GOING'" class="label-img" :src="greenLabel" alt="" />
              <img v-else-if="item.progressStatus === 'FINISHED'" class="label-img" :src="greyLabel" alt="" />
              <img v-else-if="item.progressStatus === 'COMING'" class="label-img" :src="orangeLabel" alt="" />
              <span :class="{ 'color-grey': item.progressStatus === 'FINISHED' }">{{ formatProgress[item.progressStatus] }}
              </span>
            </div>
          </div>
        </div>
        <div v-else class="activity-list-empty">
          <img class="empty mb-10" src="@/assets/images/empty.png" alt="占位图2" />
          <div class="empty-text">暂时还没有活动哦～</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import 'swiper/css/swiper.css'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import dfActBanner from '@/assets/images/activity/df-act-banner.png'
import greenLabel from '@/assets/images/activity/label-green.svg'
import greyLabel from '@/assets/images/activity/label-grey.svg'
import orangeLabel from '@/assets/images/activity/label-orange.svg'
import { getNoticeList } from '@/api/system-api.js'
import { getActivityList, getBannerList } from '@/api/activity-api.js'
import { getConfig } from '@/api/config.js'
import { formatDot, formatYYYYMMDD } from '@/utils/date.js'
export default {
  components: {
    Swiper,
    SwiperSlide
  },
  data () {
    return {
      dfActBanner,
      greenLabel,
      greyLabel,
      orangeLabel,
      noticeList: [],
      activityList: [],
      formatDot,
      formatYYYYMMDD,
      progressStatus: undefined, // "COMING", "GOING", "FINISHED", "SETTLED"
      formatProgress: {
        'COMING': '未开始',
        'GOING': '进行中',
        'FINISHED': '已结束'
      },
      poster: undefined,
      bannerList: undefined,
      swiperOption: {
        slidesPerView: 'auto',
        spaceBetween: 30,
        loop: true,
        centeredSlides: true,
        loopFillGroupWithBlank: true,
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        autoplay: {
          disableOnInteraction: false, // 手动拖拽轮播图后不关闭自动轮播
          delay: 2000
        },
        on: {
          click: (e) => {
            if (!e.target.attributes.src) return
            const index = this.bannerList.findIndex((i) => i.image === e.target.attributes.src.nodeValue)
            this.toBannerDetail(this.bannerList[index])
          }
        },
        observer: true,
        observeParents: true
      }
    }
  },
  created () {
    this._getNoticeList()
    this._getActivityList()
    // this._getConfig()
    this._getBannerList()
  },
  methods: {
    toNewsDetail (notice) {
      this.$router.push({ path: `/activity/news/${notice.id}` })
    },
    toActivityDetail (activity) {
      this.$router.push({ path: `/activity/detail/${activity.id}` })
    },
    //  获取信息列表
    _getNoticeList () {
      const param = {
        'type': 'ACTIVITY',
        'pageNo': 1,
        'pageSize': 7
      }
      getNoticeList(param).then(
        response => {
          this.noticeList = response.data.content
        }
      )
    },
    //  获取活动列表
    _getActivityList () {
      const param = {
        'activityType': 'WORK_REVIEW',
        'status': this.progressStatus
      }
      getActivityList(param).then(
        response => {
          this.activityList = response.data
        }
      )
    },
    //  获取活动列表
    async _getConfig () {
      const param = {
        'configType': 'ACTIVITY_POSTER'
      }
      const res = await getConfig(param)
      this.poster = res.data[0].keyValue
    },
    //  切换筛选状态
    changeProgressStatus (progressStatus) {
      this.progressStatus = progressStatus
      this._getActivityList()
    },
    toBannerDetail (banner) {
      switch (banner.redirectType) {
        case 'H5':
          if (!banner.url) return
          location.href = banner.url
          break
        case 'ACTIVITY':
          this.$router.push({
            'path': `/activity/detail/${banner.redirectId}`
          })
          break
      }
    },
    async _getBannerList () {
      const params = {
        'homePageRecommendType': 'ACTIVITY'
      }
      const res = await getBannerList(params)
      this.bannerList = res.data
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-box {
  height: 100%;

  .card-title-box {
    padding: 20px;
    //margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .t-title {
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
    }

    .t-more {
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 300;
      cursor: pointer;
    }
  }
}

.wrap {
  width: 100%;
  height: 100%;
  overflow: scroll;
  // padding: 20px 40px 0;
  font-weight: 400;
  border-radius: 10px;
  background: #FFF;
  overflow-x: hidden;
  @include scrollBar;
  // .banner {
  //     width: 802px;
  //     height: 278px;
  //     margin-right: 20px;
  //     object-fit: cover;
  // }

  .swiper-box {
    padding: 0 20px;
    box-sizing: border-box;
  }

  .swiper {
    width: 300px;
    height: 150px;
    object-fit: cover;
    border-radius: 10px;

    .banner {
      width: 100%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .news {
    flex: 1;
    background: #FFFFFF;
    border-radius: 5px;
    //height: 200px;
    padding: 10px 20px;
  }

  .bottom {
    width: 100%;
    min-height: calc(100% - 308px);
    background: #FFFFFF;
    border-radius: 5px;
    padding: 10px 0;
    margin: 10px 0 10px;
  }

  .news-list {
    padding-top: 10px;
    overflow: scroll;
    height: calc(100% - 20px);
    @include noScrollBar;
  }

  .news-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    //line-height: 20px;
    cursor: pointer;

    .circle {
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background: #C8DBFF;
      margin-right: 5px;
    }

    span {
      max-lines: 1;
      font-size: var(--font-size-L);
      color: #191818;
      @include ellipses(1);
    }
  }

  .status-btn {
    width: 70px;
    height: 20px;
    border: 1px solid #E2E2E2;
    border-radius: 15px;
    font-weight: 400;
    font-size: var(--font-size-L);
    color: #8C8C8C;
    //justify-content: center;
    flex-direction: row;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
  }

  .status-btn-active {
    background: #DFEBFF;
    color: #3479FF;
    border: none;
  }

  .activity-list {
    height: calc(100% - 25px);
    display: grid;
    padding: 0 20px;
    gap: 20px;

    .activity-item {
      width: 100%;
      //height: 250px;
      position: relative;
      cursor: pointer;
    }

    img {
      width: 100%;
      height: 110px;
      border-radius: 5px;
      object-fit: cover;
    }

    .name {
      //height: 40px;
      font-weight: 500;
      font-size: var(--font-size-M);
      color: #0B0B0B;
      line-height: 20px;
      @include ellipses(2);
    }

    .time {
      font-weight: 400;
      font-size: var(--font-size-M);
      color: #191818;
      line-height: 17px;
    }

    .label {
      width: 60px;
      height: 22px;
      position: absolute;
      top: 10px;
      right: -5.4px;

      .label-img {
        width: 100%;
        height: 100%;
      }

      span {
        position: absolute;
        font-size: var(--font-size-M);
        color: white;
        left: 14px;
        top: 3px;
      }
    }
  }

  .activity-list-empty {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .act-title {
    font-weight: 500;
    font-size: var(--font-size-L);
    color: #1C1B1A;
    position: relative;
    padding-left: 7px;
    line-height: 22px;

    &::after {
      content: '';
      width: 2px;
      height: 14px;
      background: #3479FF;
      border-radius: 1px;
      position: absolute;
      left: 0;
      transform: translate(0, -50%);
      top: 50%;
    }
  }

  .empty {
    width: 126px;
    height: 126px;
  }

  .empty-text {
    font-weight: 400;
    font-size: 14px;
    color: #8C8C8C;
  }

  .color-grey {
    color: #545454 !important;
  }

  .mr-10 {
    margin-right: 10px;
  }

  .ml-20 {
    margin-left: 20px;
  }

  .mb-24 {
    margin-bottom: 24px;
  }

  .mb-5 {
    margin-bottom: 5px;
  }

  .mb-10 {
    margin-bottom: 10px;
  }
}

@media screen and (min-width: 0px) {
  .activity-grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

//@media screen and (min-width: 1441px) {
//  .activity-grid {
//    grid-template-columns: repeat(6, minmax(0, 1fr));
//  }
//}
//
//@media screen and (min-width: 1921px) {
//  .activity-grid {
//    grid-template-columns: repeat(7, minmax(0, 1fr));
//  }
//}
</style>

<style lang="scss">
.activity-box {

  .wrap {
    .swiper-pagination-bullet {
      background: #333333;
    }

    .swiper-pagination-bullet.swiper-pagination-bullet-active {
      background: #FFFFFF;
    }
  }

}
</style>
