<template>
  <div class="activity-detail-box">
    <div ref="detail" class="wrap">

      <!-- 头部 -->
      <div class="header mb-10">
        <img v-if="!isExpert" src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="back" />
        <span v-if="!isExpert" class="back" @click="back">返回</span>
      </div>

      <div class="contents">
        <!-- 活动信息 -->
        <div v-if="activityInfo" class="info-box mb-10">
          <img class="cover mr-30" :src="activityInfo.cover" alt="活动封面" />
          <div class="info">
            <div class="name">{{ activityInfo.name || '' }}</div>
            <div>
              <div
                class="text mb-5"
                style="margin-top: auto"
              >活动时间：{{ formatDot(activityInfo.startTime) }}-{{ formatDot(activityInfo.endTime) }}</div>
              <div class="text mb-5">主办单位：{{ activityInfo.host || '' }}</div>
              <div v-if="activityInfo.undertake" class="text mb-5">承办单位：{{ activityInfo.undertake || '' }}</div>
              <div v-if="activityInfo.assisting" class="text mb-5">协办单位：{{ activityInfo.assisting || '' }}</div>
            </div>
          </div>
          <div v-if="isExpert && canReview && expertToken" class="review-btn" @click="review">作品评审</div>
          <div v-if="!isExpert" class="share" @click="openShare">分享</div>
          <div class="label">
            <img v-if="activityInfo.progressStatus === 'GOING'" :src="greenLabel" alt="" />
            <img v-else-if="activityInfo.progressStatus === 'FINISHED'" :src="greyLabel" alt="" />
            <img v-else-if="activityInfo.progressStatus === 'COMING'" :src="orangeLabel" alt="" />
            <span>{{ formatProgress[activityInfo.progressStatus] }}</span>
          </div>
        </div>

        <div v-if="activityInfo" class="content">
          <div class="flex">
            <div class="label" :class="{'label-select': tabIndex === 0}" @click="tabIndex = 0">活动详情</div>
            <div class="label" :class="{'label-select': tabIndex === 1}" @click="tabIndex = 1">参赛作品</div>
          </div>

          <div class="box">
            <template v-if="tabIndex === 0">
              <div class="flex flex-col mb-40">
                <img v-for="item in posterList" :key="item" class="poster mb-10" :src="item" alt="活动详情" />
              </div>
              <template v-if="activityInfo.resourceList && activityInfo.resourceList.length > 0">
                <div class="acitivty-title mb-20">活动附件</div>
                <div class="file-list">
                  <div
                    v-for="(item, index) in activityInfo.resourceList"
                    :key="item.id"
                    class="file-item"
                    :class="[index === activityInfo.length ? 'mb-10' : 'mb-20']"
                  >
                    <a :href="item.url" target="_blank"><span class="name">附件{{ index + 1 }}:{{ item.fileName }}.{{ item.expendType }}</span></a>
                    <!-- <a :href="item.url" class="download" target="_blank"><span class="download">下载</span></a> -->
                    <span class="download" @click="downloadFile(item.url,`${item.fileName}.${item.expendType}`)">下载</span>
                  </div>
                </div>
              </template>
            </template>
            <activity-work v-show="tabIndex === 1" ref="works" :activity-info="activityInfo" />
          </div>

        </div>
      </div>

    </div>

    <activity-share ref="share" />
  </div>
</template>

<script>
import greenLabel from '@/assets/images/activity/label-green.svg'
import greyLabel from '@/assets/images/activity/label-grey.svg'
import orangeLabel from '@/assets/images/activity/label-orange.svg'
import ActivityShare from './components/activity-share.vue'
import ActivityWork from './components/activity-work.vue'
import { getActivityInfo } from '@/api/activity-api.js'
import { formatDot, formatHHmm } from '@/utils/date.js'
import { getTimestamp } from '@/utils/time.js'
import { throttle } from '@/utils/index'
export default {
  components: {
    ActivityShare,
    ActivityWork
  },
  data () {
    return {
      greenLabel,
      greyLabel,
      orangeLabel,
      formatDot,
      formatHHmm,
      getTimestamp,
      activityId: this.$route.params.activityId,
      expertToken: this.$route.query.expertToken,
      activityInfo: undefined,
      isExpert: false,
      formatProgress: {
        'COMING': '未开始',
        'GOING': '进行中',
        'FINISHED': '已结束',
        '': ''
      },
      posterList: [],
      canReview: false,
      tabIndex: 0
    }
  },
  beforeRouteEnter (to, from, next) {
    const fetchData = async (vm) => {
      if (from.name !== 'workDetail' || !vm.activityInfo) {
        vm.activityInfo = undefined
        vm.activityId = to.params.activityId
        vm.expertToken = to.query.expertToken
        vm.posterList = []
        vm.isExpert = window.location.hash.indexOf('activity/expert') > -1
        vm.tabIndex = 0
        await vm.getActivityInfo()
      }
      if (from.name === 'workDetail' && to.query.rank === '1') {
        vm.tabIndex = 1
        vm.$refs.works.changeRankAward(to.query.part)
      }
    }

    next(vm => fetchData(vm))
  },
  mounted () {
    window.addEventListener('scroll', this.handleScroll, true)
  },
  beforeDestroy () {
    // 移除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll, false)
  },
  methods: {
    handleScroll () {
      // 获取滚动容器元素
      const container = this.$refs.detail
      if (!container) {
        return
      }
      // 判断是否滑动到了最底部
      if (container.scrollTop + container.clientHeight >= container.scrollHeight - 10) {
        // 滑动到了最底部，执行加载更多数据的操作
        this.$refs.works.loadMore()
      }
    },
    back () {
      if (this.$route.query && this.$route.query.f === 'recom') {
        this.$router.push({ path: '/classpro' })
      } else {
        this.$router.push({ path: '/classpro/activity' })
      }
    },
    openShare () {
      this.$refs.share.openDialog()
    },
    review () {
      this.$router.push(
        { path: `/activity/expert/review/${this.activityId}/${this.expertToken}` }
      )
    },
    async getActivityInfo () {
      var params = {
        'activityId': this.activityId
      }
      await getActivityInfo(params).then(
        response => {
          this.activityInfo = response.data
          this.posterList = this.activityInfo.webPosters.split('|')
          console.log(this.posterList)
          this.canReview = getTimestamp(Date()) > this.activityInfo.startTime && getTimestamp(Date()) < this.activityInfo.endReview
        }
      )
    },
    downloadFile (path, name) {
      throttle(function () {
        const xhr = new XMLHttpRequest()
        xhr.open('get', path)
        xhr.responseType = 'blob'
        xhr.send()
        xhr.onload = function () {
          if (this.status === 200 || this.status === 304) {
            const fileReader = new FileReader()
            fileReader.readAsDataURL(this.response)
            fileReader.onload = function () {
              const a = document.createElement('a')
              a.style.display = 'none'
              a.href = this.result
              a.download = name
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
            }
          }
        }
      }, 2000)
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-detail-box {
    // height: calc(100% - 62px);
    height: calc(100% - 1px);
    width: 100%;
}

.wrap {
    height: 100%;
    padding: 0;

    .contents {
      height: calc(100% - 30px);
      overflow: hidden;
      overflow-y: auto;
      padding-right: 10px;
      @include scrollBar;
    }

    .header {
        display: flex;
        align-items: center;

        img {
            width: 14px;
            height: 14px;
            object-fit: contain;
            margin-right: 5px;
            cursor: pointer;
        }

        span {
            font-weight: 400;
            font-size: 14px;
            color: #1C1B1A;
            line-height: 20px;
            cursor: pointer;
        }
    }

    .info-box {
        padding: 15px;
        height: 150px;
        display: flex;
        background: white;
        border-radius: 5px;
        position: relative;
        .cover {
            width: 200px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
        }

        .info {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .name {
            font-weight: 500;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            margin-bottom: auto;
        }

        .text {
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #686868;
            line-height: 20px;
        }

        .share {
            width: 60px;
            //height: 25px;
            background: #06BD06;
            border-radius: 3px;
            line-height: 25px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            text-align: center;
            margin-left: auto;
            margin-top: auto;
            cursor: pointer;
        }

        .review-btn {
            width: 100px;
            height: 35px;
            background: #3479FF;
            border-radius: 5px;
            line-height: 32px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            text-align: center;
            margin-left: auto;
            margin-top: auto;
            cursor: pointer;
        }

        .label {
            position: absolute;
            width: 70px;
            height: 30px;
            top: 20px;
            right: -5.5px;

            img {
                width: 100%;
                height: 100%;
            }

            span {
                position: absolute;
                left: 14px;
                top: 3px;
                font-weight: 400;
                font-size: var(--font-size-XL);
                color: #FFFFFF;
            }

        }
    }

    .content {
        height: calc(100% - 240px);
        .label {
          width: 110px;
          height: 32px;
          background: #F2F2F2;
          border-radius: 5px 25px 0 0;
          font-weight: 500;
          font-size: var(--font-size-L);
          color: #828282;
          text-align: center;
          line-height: 32px;
          cursor: pointer;
        }

        .label-select {
          color: #3479FF;
          background: #FFFFFF;
        }

        .box {
            min-height: calc(100% - 32px);
            width: 100%;
            box-sizing: border-box;
            background: #FFFFFF;
            border-radius: 5px;
            padding: 40px;
        }

        .poster {
            width: 690px;
            object-fit: contain;
            margin: 0 auto;
        }

        .file-list {
            width: 100%;
            padding: 10px 20px 1px;
            background: #F8FAFF;
            border-radius: 5px;

            .file-item {
                display: flex;
            }

            span {
                font-weight: 400;
                font-size: var(--font-size-L);
                color: #1C1B1A;
                cursor: pointer;
            }

            .download {
                margin-left: auto;
                color: #3479FF;
            }
        }
    }

    .acitivty-title {
        font-weight: 500;
        font-size: var(--font-size-L);
        color: #1C1B1A;
        position: relative;
        padding-left: 7px;
        line-height: 22px;

        &::after {
            content: '';
            width: 2px;
            height: 14px;
            background: #3479FF;
            border-radius: 1px;
            position: absolute;
            left: 0;
            transform: translate(0, -50%);
            top: 50%;
        }
    }

    .mb-5 {
        margin-bottom: 5px;
    }

    .mb-10 {
        margin-bottom: 10px;
    }

    .mb-20 {
        margin-bottom: 20px;
    }

    .mb-40 {
        margin-bottom: 40px;
    }

    .mr-30 {
        margin-right: 30px;
    }
}
</style>
