<template>
  <div class="activity-work">

    <div class="tabbar">
      <div class="ac-tab" :class="{'ac-tab-select': tabIndex === 0 }" @click="changeTabIndex(0)">作品列表</div>
      <div v-if="+activityInfo.praisePerWork !== 0" class="ac-tab" :class="{'ac-tab-select': tabIndex === 1 }" @click="changeTabIndex(1)">作品排行</div>
      <div class="ac-tab" :class="{'ac-tab-select': tabIndex === 2 }" @click="changeTabIndex(2)">优秀作品</div>
      <div class="right-part">
        <div v-if="tabIndex === 0" class="input">
          <el-input v-model="input" placeholder="输入作品名字或姓名" />
          <i class="el-icon-search" @click="_searchWorks"></i>
        </div>
        <el-dropdown v-if="partList.length > 0" trigger="click" @command="partConfirm">
          <span class="el-dropdown-link">
            <span>{{ part.text || '' }}</span>
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in partList"
              :key="item.text"
              :command="item.value"
            >{{ item.text }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <div class="content">

      <template v-if="tabIndex === 0">
        <div class="work-list">
          <div v-for="item in workList" :key="item.id" class="work-item" @click="gotoDetail(item)">
            <img class="cover" :src="makeCover(item.resourceList)" alt="" />
            <span class="name">{{ item.name || '' }}</span>
            <div class="flex">
              <span class="member">
                <div class="member-example" :class="{'maxW60': item.members.length > 1}">
                  {{ item.members[0].displayName }}</div>
                <template v-if="item.members.length > 1">等{{ item.members.length - 1 }}人</template>
              </span>
              <span v-if="activityInfo.praisePerWork !== 0" class="vote">{{ item.praiseNum || 0 }}票</span>
            </div>
          </div>
        </div>
        <span v-if="workList.length > 0" class="more-data">{{ finished ? '-数据加载完了-' : '-加载中-' }}</span>
      </template>

      <div v-if="tabIndex === 1" class="rank-list">
        <div v-for="(item, index) in workList" :key="item.id" class="rank-item" @click="gotoDetail(item)">
          <img v-if="index === 0" class="rank-number" :src="IconRank1" alt="" />
          <img v-else-if="index === 1" class="rank-number" :src="IconRank2" alt="" />
          <img v-else-if="index === 2" class="rank-number" :src="IconRank3" alt="" />
          <div v-else class="bg-rank">{{ index + 1 }}</div>
          <img class="cover" :src="makeCover(item.resourceList)" alt="" />
          <div class="work-content" style="flex: 1">
            <span class="name">{{ item.name || '' }}</span>
            <span class="member">{{ item.members | membersName }}</span>
            <div class="time">{{ formatYYYYMMDD(item.createdAt) }}</div>
          </div>
          <span v-if="activityInfo.praisePerWork !== 0" class="vote">{{ item.praiseNum || 0 }}票</span>
        </div>
        <span v-if="workList.length > 0" class="more-data">{{ finished ? '-数据加载完了-' : '-加载中-' }}</span>
      </div>

      <template v-if="tabIndex === 2">
        <div class="label-list">
          <div
            v-for="item in awardList"
            :key="item"
            class="label"
            :class="{'label-select': item === currentAward}"
            @click="changeAward(item)"
          >{{ item }}</div>
        </div>
        <div class="work-list">
          <div v-for="item in workList" :key="item.id" class="work-item" @click="gotoDetail(item)">
            <img class="cover" :src="makeCover(item.resourceList)" alt="" />
            <span class="name">{{ item.name || '' }}</span>
            <div class="flex">
              <span class="member">
                <div
                  class="member-example"
                  :class="{'maxW60': item.members.length > 1}"
                >{{ item.members[0].displayName }}</div>
                <template v-if="item.members.length > 1">等{{ item.members.length - 1 }}人</template>
              </span>
              <span v-if="activityInfo.praisePerWork !== 0" class="vote">{{ item.praiseNum || 0 }}票</span>
            </div>
          </div>
        </div>
        <span v-if="workList.length > 0" class="more-data">{{ finished ? '-数据加载完了-' : '-加载中-' }}</span>
      </template>

      <div v-if="workList.length === 0 && !loading" class="activity-list-empty">
        <img class="empty" src="@/assets/images/empty.png" alt="占位图2" />
        <div class="empty-text">暂无数据</div>
      </div>

    </div>

  </div>
</template>

<script>
import IconRank1 from '@/assets/H5/icon-rank1.svg'
import IconRank2 from '@/assets/H5/icon-rank2.svg'
import IconRank3 from '@/assets/H5/icon-rank3.svg'
import { getActivityWorkList } from '@/api/activity-api.js'
import { formatYYYYMMDD } from '@/utils/time.js'
export default {
  filters: {
    membersName (members) {
      var name = ''
      for (var i = 0; i < members.length; i++) {
        if (i === 0) {
          name = members[0].displayName
        } else {
          name += ('、' + members[0].displayName)
        }
      }
      return name
    }
  },
  props: {
    activityInfo: {
      type: Object,
      default: undefined
    }
  },
  data () {
    return {
      IconRank1,
      IconRank2,
      IconRank3,
      formatYYYYMMDD,
      tabIndex: +this.$route.query.tabIndex || 0,
      input: '',
      page: 1,
      limit: 50,
      activityId: this.$route.params.activityId,
      workList: [],
      awardList: [],
      searchText: '',
      currentAward: undefined,
      loading: false,
      finished: false,
      partList: [],
      part: undefined
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      if (this.activityInfo.awardList !== '') {
        this.awardList = this.activityInfo.awardList.split('>')
        if (this.awardList.length > 0) this.currentAward = this.awardList[0]
      }
      this._initPartList()
      this._getActivityWorkList()
    },
    makeCover (resrouces) {
      console.log(resrouces)
      const resrouce = resrouces[0]
      switch (resrouce.type) {
        case 'IMAGE':
          return resrouce.url
        case 'VIDEO':
          return resrouce.url + '?x-oss-process=video/snapshot,t_1000,m_fast'
      }
    },
    changeTabIndex (tabIndex) {
      if (this.tabIndex === tabIndex) return
      this.loading = false
      this.finished = false
      this.tabIndex = tabIndex
      this._initPartList()
      this._getActivityWorkList()
    },
    changeAward (award) {
      if (this.currentAward === award) return
      this.loading = false
      this.finished = false
      this.currentAward = award
      this._getActivityWorkList()
    },
    changeRankAward (award) {
      const newPart = this.partList.find((part) => part.text === award)
      if (this.tabIndex === 1 && this.part === newPart) return
      this.loading = false
      this.finished = false
      this.tabIndex = 1
      this._initPartList()
      if (newPart) this.part = newPart
      this._getActivityWorkList()
    },
    _searchWorks () {
      this.searchText = this.input
      this.loading = false
      this.finished = false
      this._getActivityWorkList()
    },
    async _getActivityWorkList () {
      if (this.loading || this.finished) return
      this.loading = true
      this.workList = []
      this.page = 1
      const params = {
        'pageNumber': this.page,
        'pageSize': this.limit,
        'activityId': this.activityId
      }
      if (this.part && this.part.value > -1) {
        params['part'] = this.part.text
      }
      switch (this.tabIndex) {
        case 0:
          params['keyword'] = this.searchText
          break
        case 1:
          params['listType'] = 'RANK'
          break
        case 2:
          params['listType'] = 'AWARD'
          params['keyword'] = this.currentAward
          break
      }
      const res = await getActivityWorkList(params)
      this.loading = false
      if (res.data.length < this.limit) {
        this.finished = true
      }
      this.workList = res.data
    },
    async loadMore () {
      if (this.loading || this.finished) return
      this.loading = true
      this.page += 1
      const params = {
        'pageNumber': this.page,
        'pageSize': this.limit,
        'activityId': this.activityId
      }
      if (this.part && this.part.value > -1) {
        params['part'] = this.part.text
      }
      switch (this.tabIndex) {
        case 0:
          params['keyword'] = this.searchText
          break
        case 1:
          params['listType'] = 'RANK'
          break
        case 2:
          params['listType'] = 'AWARD'
          params['keyword'] = this.currentAward
          break
      }
      const res = await getActivityWorkList(params)
      this.loading = false
      if (res.data.length < this.limit) {
        this.finished = true
      }
      this.workList = this.workList.concat(res.data)
    },
    gotoDetail (item) {
      this.$router.push({ path: `/activity/work/${item.activityId}/${item.id}` })
    },
    partConfirm (part) {
      if (this.part === part) return
      if (this.tabIndex === 1) {
        this.part = this.partList[part]
      } else {
        this.part = this.partList[part + 1]
      }
      this.loading = false
      this.finished = false
      this._getActivityWorkList()
    },
    _initPartList () {
      if (!this.activityInfo.worksForm) return
      const worksForm = JSON.parse(this.activityInfo.worksForm)
      worksForm.map(val => {
        if (val.type === 'part') {
          if (+this.tabIndex !== 1) {
            this.partList = [{ text: '全部分组', value: -1 }]
            this.part = { text: '全部分组', value: -1 }
          } else {
            this.partList = []
          }
          const list = val.option
          for (var i = 0; i < list.length; i++) {
            this.partList.push({ text: list[i], value: i })
          }
          if (list.length > 0 && +this.tabIndex === 1) {
            this.part = this.partList[0]
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-work {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tabbar {
    display: flex;
    //justify-content: center;
    gap: 35px;
    font-size: var(--font-size-L);
    line-height: 22px;
    color: #000000;
    font-weight: 500;
    position: relative;

    .ac-tab {
        position: relative;
        cursor: pointer;
    }

    .ac-tab-select {
        &::after {
            content: '';
            width: 20px;
            height: 5px;
            background: #2F80ED;
            border-radius: 1px;
            position: absolute;
            bottom: -4px;
            left: 50%;
            transform: translate(-50%, 0);
            border-radius: 11px;
        }
    }

    .right-part {
      position: absolute;
      transform: translate(0, -50%);
      top: 50%;
      right: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .input {
        display: flex;
        align-items: center;
        border: 1px solid #333333;
        border-radius: 4px;
        padding-right: 10px;

        .el-icon-search {
          cursor: pointer;
        }
    }

    .el-dropdown-link {
      cursor: pointer;
      display: flex;
      align-items: center;

      span {
        width: 80px;
        text-align: end;
        @include ellipses(1);
      }
    }
}

.content {
    width: 100%;
    flex: 1;
}

.work-list {
    display: grid;
    column-gap: 20px;
    row-gap: 20px;
    padding: 22px 0;

    .work-item {
        width: 100%;
        cursor: pointer;

        .cover {
            width: 100%;
            height: 130px;
            border-radius: 12px;
            object-fit: cover;
        }

        span {
            font-style: normal;
            font-weight: 400;
            font-size: var(--font-size-L);
            line-height: 20px;
            color: #000000;
        }

        .member {
            color: #4F4F4F;
            display: flex;
            height: 20px;
            flex: 1;
        }

        .member-example {
            @include ellipses(1);
        }

        .maxW60 {
          max-width: 60%;
        }
    }
}

.rank-list {
    column-gap: 20px;
    row-gap: 10px;
    padding: 22px 0;

    .rank-item {
        width: 100%;
        display: flex;
        margin-bottom: 23px;
        align-items: center;
        gap: 10px;
        cursor: pointer;

        .rank-number,
        .bg-rank {
            width: 48px;
            height: 48px;
            object-fit: contain;
        }

        .bg-rank {
            position: relative;
            background: url('../../../../../assets/H5/icon-rank.svg') center center no-repeat;
            background-size: contain;
            color: #000000;
            font-weight: 500;
            font-size: 20px;
            line-height: 48px;
            text-align: center;
        }

        .cover {
            width: 150px;
            height: 110px;
            border-radius: 12px;
            object-fit: cover;
        }

        .work-content {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .name {
            color: #000000;
            font-weight: 500;
            font-size: var(--font-size-L);
            line-height: 22px;
            @include ellipses(1);
        }

        .member {
            color: #4F4F4F;
            display: flex;
            height: 20px;
            flex: 1;
            font-size: var(--font-size-L);
            @include ellipses(1);
        }

        .time {
            color: #4F4F4F;
            font-size: var(--font-size-L);
            line-height: 20px;
        }

        .vote {
            font-weight: 500;
            font-size: var(--font-size-L);
            line-height: 29px;
            color: #000000;
        }
    }
}

.label-list {
    display: flex;
    flex-wrap: wrap;
    column-gap: 20px;
    row-gap: 10px;
    padding-top: 10px;

    .label {
        width: 83px;
        height: 40px;
        background: #F2F2F2;
        border-radius: 10px;
        font-weight: 400;
        font-size: var(--font-size-L);
        line-height: 40px;
        color: #000000;
        text-align: center;
        //font-size: 14px;
        cursor: pointer;
    }

    .label-select {
        background: #2F80ED;
        color: #FFFFFF;
    }
}

.more-data {
  display: flex;
  justify-content: center;
  font-size: var(--font-size-L);
}

.activity-list-empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  margin-top: 110px;

  .empty {
    width: 126px;
    height: 126px;
    margin-bottom: 10px;
  }

  .empty-text {
    font-weight: 400;
    font-size: 14px;
    color: #8C8C8C;
  }
}

@media screen and (min-width: 0px) {
  .work-list  {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

//@media screen and (min-width: 1441px) {
//  .work-list  {
//    grid-template-columns: repeat(6, minmax(0, 1fr));
//  }
//}
//
//@media screen and (min-width: 1921px) {
//  .work-list  {
//    grid-template-columns: repeat(7, minmax(0, 1fr));
//  }
//}
</style>

<style lang="scss">
.activity-work {
  .input{
    .el-input__inner {
        border: none;
      height: 25px;

        &::placeholder {
            font-weight: 400;
            font-size: 12px;
            line-height: 17px;
            color: #4F4F4F;
        }
    }
  }

}
</style>
