<template>
  <div class="activity-box">
    <div class="header mb-10">
      <div class="flex items-center" @click="back">
        <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" />
        <span>返回</span>
      </div>
    </div>

    <div class="contents">

      <div class="box">
        <div class="box-inside w flex">
          <div style="width: 50%;">
            <div v-if="list && list.resourceList">
              <div v-for="(item, index) in list.resourceList" v-show="index === current" :key="item.id" class="show-work-img" @click="peview(item, index)">
                <van-icon v-if="item.type === 'VIDEO'" class="play-btn" color="#FFF" size="50" name="play-circle" />
                <img v-if="item.type === 'IMAGE'" :src="item.url" />
                <img v-else-if="item.type === 'VIDEO'" :src="item.url + '?x-oss-process=video/snapshot,t_1000,m_fast'" />
              </div>
            </div>

            <div v-if="list && list.resourceList" class="p-img">
              <div v-for="(item, index) in list.resourceList" :key="item.id" class="work-p-img relative" @click="jumoto(index)">
                <div v-if="index == current" class="triangle"></div>
                <div v-else class="triangle-h"></div>
                <van-icon v-if="item.type === 'VIDEO'" class="play-btn" color="#FFF" size="25" name="play-circle" />
                <img v-if="item.type === 'IMAGE'" :src="item.url" />
                <img v-else-if="item.type === 'VIDEO'" :src="item.url + '?x-oss-process=video/snapshot,t_1000,m_fast'" />
              </div>
            </div>

            <div class="p-tip">
              <div class="step-box" @click="handleSetp('-')">
                <i class="el-icon-arrow-left"></i>
              </div>
              {{ current + 1 }}/{{ list && list.resourceList.length }}
              <div class="step-box" @click="handleSetp('+')">
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>

          <div style="width: 50%;">
            <div class="list-box">
              <div class="name">
                {{ list && list.name }}
              </div>

              <div v-if="activityInfo.praisePerWork !== 0" class="prise">
                投票数：{{ list && list.praiseNum }}
              </div>

              <div class="share" @click="openShare">分享作品</div>
              <activity-share ref="share" />
            </div>
          </div>

        </div>
      </div>

      <div class="a-tag-name mt10">
        基本信息
      </div>

      <div class="box">
        <div v-if="list" class="box-inside w">

          <div class="base-t-box">
            <div class="title">作者姓名：</div>
            <div class="des">
              <template v-for="stu in (list && list.members)">
                {{ stu.displayName }}
              </template>
            </div>
          </div>
          <div v-if="list.part" class="base-t-box">
            <div class="title">参赛组：</div>
            <div class="des">{{ list.part }}</div>
          </div>
          <div v-if="list.adviser" class="base-t-box">
            <div class="title">指导老师：</div>
            <div class="des">{{ list.adviser }}</div>
          </div>
          <div v-if="list.area" class="base-t-box">
            <div class="title">所属地区：</div>
            <div class="des">{{ list.area }}</div>
          </div>
          <div v-if="list.school" class="base-t-box">
            <div class="title">学 校：</div>
            <div class="des">{{ list.school }}</div>
          </div>
          <div v-if="list.code" class="base-t-box">
            <div class="title">作品编号：</div>
            <div class="des">{{ list.code }}</div>
          </div>
          <div class="base-t-box">
            <div class="title">区块链地址：</div>
            <div class="des">{{ list.blockChainAddr || '生成中' }}</div>
          </div>
          <div class="base-t-box">
            <div class="title">作品哈希值：</div>
            <div class="des">{{ list.hashCode || '生成中' }}</div>
          </div>
          <div class="base-t-box">
            <div class="title">生成时间：</div>
            <div class="des">{{ list.createdAt }}</div>
          </div>
          <div class="base-t-box">
            <div class="title">区块链：</div>
            <div class="des">长安链</div>
          </div>
          <div v-if="activityInfo.praisePerWork !== 0" class="base-t-box">
            <div class="title">作品排行：</div>
            <div v-show="list.rank" class="des underline pointer" @click="goRank">{{ list.rank || '-' }}名</div>
          </div>
        </div>
      </div>

      <div class="a-tag-name">
        作品介绍
      </div>

      <div class="box">
        <div v-if="list" class="box-inside w">

          <div class="intro">
            {{ list.introduce }}
          </div>

          <div v-if="list.introduceVideo" class="work-img" @click="peviewVideo(list.introduceVideo)">
            <img :src="list.introduceVideo + '?x-oss-process=video/snapshot,f_png,w_300,t_0'" />
            <van-icon class="play-btn" color="#FFF" size="50" name="play-circle" />
          </div>

          <div class="f12 h40 w flex justify-center items-center">作品介绍</div>
        </div>
      </div>
      <div v-if="peviewUrl" class="iframe-box">
        <van-icon class="close" name="clear" color="#DDDDDD" size="20" @click.stop="peviewUrl= false" />
        <video-js :options="videoOptions" @player="videoPlay" />
      </div>
      <van-image-preview v-model="imagesShow" :images="images" closeable @close="images=[]" />
    </div>

  </div>
</template>

<script>
import VideoJs from '@/components/classPro/h5Video'
import ActivityShare from './components/activity-share.vue'
import { getActivityWorkInfo, getActivityInfo } from '@/api/activity-api.js'
export default {
  components: {
    ActivityShare,
    VideoJs
  },
  data () {
    return {
      activityId: this.$route.params.activityId,
      workId: this.$route.params.workId,
      current: 0,
      activityInfo: null,
      list: null,
      peviewUrl: false,
      images: [],
      imagesShow: false,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: false, //  x5内核浏览器不允许自动播放，需要手动播放
        fileShow: false,
        loop: false,
        fluid: false,
        language: 'zh-CN',
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    }
  },
  mounted () {
    this._getActivityInfo()
    this._getActivityWorkInfo()
  },
  methods: {
    back () {
      this.$router.go(-1)
    },
    goRank () {
      this.$router.push(
        { path: `/activity/detail/${this.activityId}?rank=1&part=${this.list.part}` }
      )
    },
    async _getActivityInfo () {
      const { data } = await getActivityInfo({
        activityId: this.activityId
      })
      this.activityInfo = data
    },
    async _getActivityWorkInfo () {
      const obj = { activityId: this.activityId, activityWorksId: this.workId }
      const { data } = await getActivityWorkInfo(obj)
      this.list = data
      this.images = []
      data.resourceList.map(val => {
        this.images.push(val.url)
      })
    },
    jumoto (index) {
      this.current = index
    },
    handleSetp (type) {
      switch (type) {
        case '+':
          if (!(this.current + 1 === this.list.resourceList.length)) {
            this.current++
          }
          break
        case '-':
          if (!(this.current === 0)) {
            this.current--
          }
          break
      }
    },
    peview (val, index) {
      console.log(index)
      this.current = index
      if (val.type === 'IMAGE') {
        this.imagesShow = true
        this.images = [val.url]
      } else if (val.type === 'VIDEO') {
        // this.peviewUrl = val.url
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: val.url,
            type: 'video/mp4'
          }]
        }
        this.peviewUrl = true
      }
    },
    peviewVideo (url) {
      // this.peviewUrl = url
      this.videoOptions = {
        ...this.videoOptions,
        sources: [{
          src: url,
          type: 'video/mp4'
        }]
      }
      this.peviewUrl = true
    },
    videoPlay (player) {
      player.play()
    },
    openShare () {
      this.$refs.share.openDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-box {
  // height: calc(100% - 62px);
  height: calc(100% - 1px);
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  padding: 0;

  .header {
    display: flex;
    align-items: center;

    img {
      width: 14px;
      height: 14px;
      object-fit: contain;
      margin-right: 5px;
      cursor: pointer;
    }

    span {
      font-weight: 400;
      font-size: 14px;
      color: #1C1B1A;
      line-height: 20px;
      cursor: pointer;
    }
  }

  .contents {
    height: calc(100% - 10px);
    overflow: hidden;
    overflow-y: auto;
    @include scrollBar;
  }

  .mt10 {
    margin-top: 10px;
  }

  .mb30 {
    margin-bottom: 30px;
  }

  .a-tag-name {
    padding: 0 12px;
    height: 40px;
    display: flex;
    align-items: center;
    color: #000000;
    font-size: var(--font-size-XL);

    &::before {
      width: 4px;
      height: 20px;
      margin-right: 5px;
      background: #2F80ED;
      border-radius: 17px;
      content: "";
    }
  }

  .base-t-box {
    width: 100%;
    padding: 8px 0;
    display: flex;

    .title {
      font-size: var(--font-size-XL);
      line-height: 20px;
      color: #4F4F4F;
      width: 100px;
      flex-shrink: 0;
    }

    .des {
      font-size: var(--font-size-XL);
      line-height: 20px;
      color: #000000;
      flex: 1;
      word-break: break-all;
    }
  }

  .box {
    width: 100%;
    padding: 0 12px;
    box-sizing: border-box;

    .box-inside {
      background: #FFFFFF;
      border-radius: 10px;
      box-sizing: border-box;
      padding: 10px;
    }
  }

  .intro {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #4F4F4F;
    padding-bottom: 20px;
  }

  .work-img {
    width: 300px;
    height: 300px;
    border-radius: 10px;
    position: relative;

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
  }
  .show-work-img {
    width: 100%;
    height: 300px;
    border-radius: 10px;
    position: relative;

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
  }

  .play-btn {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .f12 {
    font-size: 12px;
  }

  .f16 {
    font-size: 16px;
  }

  .h40 {
    height: 30px;
  }

  .h60 {
    height: 60px;
  }

  .underline {
    text-decoration: underline;
  }

  .list-box {
    padding: 0 20px;
    box-sizing: border-box;

    .name {
      font-weight: 500;
      font-size: var(--font-size-XL);
      line-height: 28px;
      color: #000000;
      height: 230px;
    }

    .prise {
      font-weight: 600;
      font-size: var(--font-size-XL);
    }

    .share {
      margin-top: 20px;
      background: #27AE60;
      border-radius: 3px;
      padding: 5px 0;
      font-size: var(--font-size-XL);
      width: 106px;
      text-align: center;
      color: #FFFFFF;
      cursor: pointer;
    }
  }

  .p-img {
    width: 100%;
    margin-top: 10px;
    display: flex;
    justify-content: flex-start;
    flex-shrink: 0;
    overflow-x: auto;

    .work-p-img {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 5px;

      &:last-child {
        margin-right: 0;
      }

      .triangle {
        width: 0;
        height: 0;
        border-right: 5px solid transparent;
        border-left: 5px solid transparent;
        border-bottom: 5px solid #DDDDDD;
      }
      .triangle-h {
        height: 5px;
      }

      img {
        width: 70px;
        height: 70px;
        object-fit: cover;
      }
    }
  }

  .p-tip {
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #000000;

    .step-box {
      width: 15px;
      height: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      cursor: pointer;
      margin-right: 10px;
      margin-left: 10px;
      &:hover {
        background: #2F80ED;
        color: #FFFFFF;
      }
    }
  }
}

.iframe-box {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99999;

  .close {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 999;
  }
}
</style>
