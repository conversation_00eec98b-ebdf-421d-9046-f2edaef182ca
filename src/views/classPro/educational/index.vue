<template>
  <div class="edu">
    <div class="wrap">
      <div class="tag-box flex">
        <div
          :class="tagIndex === 1 ? 'tag-item__active' : 'tag-item'"
          @click="changeTag(1)"
        >
          学校课程
        </div>
        <div
          :class="tagIndex === 2 ? 'tag-item__active' : 'tag-item'"
          @click="changeTag(2)"
        >
          班级管理
        </div>
      </div>
      <div v-show="tagIndex === 1" class="class-box">
        <div class="select-box">
          <el-select
            v-model="gradeValue"
            size="mini"
            placeholder="请选择"
            @change="_getCourseLessonPlanList"
          >
            <el-option :label="'全部'" :value="''" />
            <el-option
              v-for="item in gradeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div v-if="list.length === 0" style="height: calc(100% - 72px);" class="w flex justify-center items-center">
          <el-empty :image="empty3" description="暂无数据" />
        </div>
        <div v-else v-loading="loading" class="detil-box activity-grid">
          <div v-for="item in list" :key="item.title" class="card-box" @click="toAiDetail(item)">
            <div class="img-box">
              <div v-if="item.len" class="len">
                {{ item.len }}课时
              </div>
              <img class="img" :src="item.cover || defaultCover" />
            </div>
            <div class="item">
              <div class="e-title">
                {{ item.title }}
              </div>
              <div class="time">
                <template v-if="item.time">
                  <!-- <i style="color: #3479ff;margin-right:5px;" class="el-icon-time"></i> -->
                  有效期:
                  <span class="mr10">{{ item.time }}</span>
                </template>
              </div>
              <div class="des">
                {{ item.subTitle }}
              </div>
            </div>
          </div>
          <!-- <i></i><i></i><i></i><i></i><i></i> -->
        </div>
      </div>
      <div v-show="tagIndex === 2" class="grade-box">
        <grade />
      </div>
    </div>
  </div>
</template>

<script>
import defaultCover from '@/assets/images/default-cover.jpg'
import { getCourseLessonPlanList } from '@/api/educational-api.js'
import grade from './components/grade.vue'
import moment from 'moment'
import empty3 from '@/assets/images/empty3.png'
export default {
  components: { grade },
  data () {
    return {
      empty3: empty3,
      tagIndex: 1,
      gradeValue: '',
      list: [],
      loading: false,
      defaultCover: defaultCover
    }
  },
  computed: {
    gradeOption () {
      const arr = []
      const arr2 = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '七年级', '八年级', '九年级']
      for (let i = 0; i < 9; i++) {
        arr.push({
          label: arr2[i],
          value: i + 1
        })
      }
      return arr
    }
  },
  mounted () {
    const fromClassInfo = window.sessionStorage.getItem('classinfoback')
    if (fromClassInfo) {
      this.tagIndex = 2
    }
    setTimeout(() => {
      window.sessionStorage.removeItem('classinfoback')
    }, 2000)
    this._getCourseLessonPlanList()
  },
  methods: {
    async _getCourseLessonPlanList () {
      try {
        this.loading = true
        const { data } = await getCourseLessonPlanList({
          grade: this.gradeValue
        })
        const list = []
        data.map(element => {
          const obj = {}
          let time = ''
          if (element.effectStart) {
            const startTime = moment(element.effectStart).format('YYYY.MM.DD')
            const endTime = moment(element.effectEnd).format('YYYY.MM.DD')
            time = startTime + '-' + endTime
          }
          if (element.courseType === 'AI_COURSE') {
            obj.id = element.id
            obj.courseId = element.courseId
            obj.cover = (element.aiCourse && element.aiCourse.coverUrl) || ''
            obj.title = (element.aiCourse && element.aiCourse.title) || ''
            obj.subTitle = (element.aiCourse && element.aiCourse.subTitle) || ''
            obj.time = time
            obj.len = (element.aiCourse && element.aiCourse.len) || 0
            list.push(obj)
          }
          // else if (element.courseType === 'COURSE') {
          //   obj.cover = element.liveCourse.cover
          //   obj.title = element.liveCourse.name
          //   obj.subTitle = element.liveCourse.subtitle
          //   obj.time = time
          //   obj.len = element.liveCourse.number
          // }
          // list.push(obj)
        })
        this.list = list
        this.loading = false
      } catch (error) {
        this.loading = false
        console.log(error)
      }
    },
    changeTag (index) {
      this.tagIndex = index
    },
    //  双师AI课程包详情页
    toAiDetail (item) {
      this.$router.push(`/classpro/course/package/0/${item.courseId}/1`)
    }
  }
}
</script>

<style lang="scss" scoped>
.edu {
  width: 100%;
  height: calc(100% - 65px);
}
.wrap {
  padding: 20px 28px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;

  .mr10 {
    margin-right: 10px;
  }

  .tag-box {
    width: 100%;
    height: 32px;

    .tag-item,
    .tag-item__active {
      border-radius: 8px 25px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      height: 100%;
      width: 110px;
      cursor: pointer;
    }

    .tag-item {
      background: #f4f4f4;
      color: #717171;
    }

    .tag-item__active {
      background: #ffffff;
      font-weight: 500;
      color: #3479ff;
    }
  }

  .class-box {
    height: calc(100% - 32px);
    background: #fff;
  }

  .select-box {
    height: 50px;
    display: flex;
    align-items: flex-end;
    padding: 0 20px;
  }

  // .detil-box > i {
  //   width: 250px;
  //   margin-right: 10px;
  //   margin-left: 10px;
  // }

  .detil-box {
    // width: 100%;
    height: calc(100% - 72px);
    overflow-y: auto;
    background: #fff;
    // box-sizing: border-box;
    // display: flex;
    // flex-wrap: wrap;
    // justify-content: space-between;
    display: grid;
    padding: 20px;
    gap: 20px;

    .card-box {
      // width: 250px;
      width: 100%;
      // margin: 10px;
      height: 220px;
      box-shadow: 0 3px 12px 0 rgba(233, 240, 255, 0.66);
      border-radius: 5px;
      cursor: pointer;

      .img-box {
        width: 100%;
        height: 110px;
        width: 100%;
        height: 110px;
        object-fit: cover;
        border-radius: 5px 5px 0 0;
        position: relative;
        .img {
          width: 100%;
          height: 110px;
          object-fit: cover;
          border-radius: 5px 5px 0 0;
        }
        .len {
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 25px;
          background: rgba(0,0,0,0.41);
          font-size: 14px;
          color: #FFFFFF;
          padding: 0 10px;
          display: flex;
          align-items: center;
        }
      }

      .item {
        padding: 10px;
        box-sizing: border-box;
        color: #0b0b0b;
        font-size: 12px;

        .e-title {
          font-weight: 500;
          font-size: 14px;
          @include ellipses(1);
        }
        .time {
          padding: 10px 0;
        }
        .des {
          line-height: 15px;
          @include ellipses(2);
        }
      }
    }
  }

  .grade-box {
    height: calc(100% - 32px);
    background: #fff;
    padding: 20px;
  }
}

// @media screen and (min-width: 1280px) {
//   .wrap {
//     .detil-box {
//       .card-box {
//         width: 300px;
//       }
//     }

//     .detil-box > i {
//       width: 300px;
//       margin-right: 10px;
//       margin-left: 10px;
//     }
//   }
// }

// @media screen and (min-width: 1440px) {
//   .wrap {
//     .detil-box {
//       .card-box {
//         width: 300px;
//       }
//     }

//     .detil-box > i {
//       width: 300px;
//       margin-right: 10px;
//       margin-left: 10px;
//     }
//   }
// }

// @media screen and (min-width: 1536px) {
//   .wrap {
//     .detil-box {
//       .card-box {
//         width: 330px;
//       }
//     }

//     .detil-box > i {
//       width: 330px;
//       margin-right: 10px;
//       margin-left: 10px;
//     }
//   }
// }

// @media screen and (min-width: 1728px) {
//   .wrap {
//     .detil-box {
//       .card-box {
//         width: 360px;
//       }
//     }

//     .detil-box > i {
//       width: 360px;
//       margin-right: 10px;
//       margin-left: 10px;
//     }
//   }
// }

// @media screen and (min-width: 1920px) {
//   .wrap {
//     .detil-box {
//       .card-box {
//         width: 400px;
//       }
//     }

//     .detil-box > i {
//       width: 400px;
//       margin-right: 10px;
//       margin-left: 10px;
//     }
//   }
// }

@media screen and (min-width: 0px) {
  .activity-grid {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

@media screen and (min-width: 1441px) {
  .activity-grid {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}

@media screen and (min-width: 1921px) {
  .activity-grid {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
}
</style>
