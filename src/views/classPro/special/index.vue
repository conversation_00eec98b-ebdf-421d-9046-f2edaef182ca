<template>
  <div v-loading="showLoading" class="special classpro-loadding" element-loading-text="加载中" element-loading-background="#fff">
    <img class="bg-corner" src="../../../assets/images/bg-corner.png" alt="" />
    <div v-show="!showLoading" class="session-container">
      <img class="goBack" src="../../../assets/images/special/back-arrow.png" alt="goBack" @click="goBack()" />
      <div class="session my">
        <div class="specialTitle">我的专用教室</div>
        <div v-if="myClassroomList.length > 0" class="my-special-list">
          <div v-for="item in myClassroomList" :key="item.id" class="item" @click="toDetail(item)">
            <div class="item-container">
              <img class="item-cover" :src="item.cover || defaultImage" alt="" />
              <p>{{ item.title }}</p>
            </div>
          </div>
        </div>
        <div v-else class="list-empty">
          <img src="../../../assets/images/dashboard/no-ai-course.png" alt="ai课堂为空" />
          <div class="hint hint-padding">暂时没有课程哦～</div>
          <div class="hint">点击<div class="hint-blue" @click="openExchange">课程兑换</div>去激活课程吧！</div>
        </div>
      </div>
      <div class="session more">
        <div class="specialTitle">更多专用教室</div>
        <div v-if="moreClassroomList.length > 0" class="other-special-list">
          <div v-for="item in moreClassroomList" :key="item.id" class="item">
            <div class="item-container">
              <div v-if="true" class="lock-cover" @click="triggerMessage()">
                <img draggable="false" class="lock" src="../../../assets/images/special/lock.png" alt="" />
              </div>
              <img class="item-cover" :src="item.cover || defaultImage" alt="" />
              <p>{{ item.title }}</p>
            </div>
          </div>
        </div>
        <div v-else class="list-empty">
          <img src="../../../assets/images/dashboard/no-ai-course.png" alt="ai课堂为空" />
          <div class="hint hint-padding">暂时没有更多课程了哦</div>
        </div>
      </div>
    </div>
    <!-- 课程兑换dialog -->
    <exchange-dialog class="classpro-dialog" :dialog-visible="dialogExchange" @closeExchange="closeExchange" />
  </div>
</template>

<script>
import { getStuCourseList } from '@/api/lesson-api.js'
import { getCourseCommList } from '@/api/coursecomm-api.js'
import ExchangeDialog from '@/layout/classPro/components/exchangeDialog.vue'
import defaultImage from '../../../assets/images/default-cover.jpg'

export default {
  components: {
    ExchangeDialog
  },
  data () {
    return {
      defaultImage,
      myClassroomList: [],
      pageNoMy: 0,
      pageLoadedMy: false,
      pageLoadingMy: false,
      moreClassroomList: [],
      pageNoMore: 1,
      pageSize: 20,
      pageLoadedMore: true,
      pageLoadingMore: false,
      dialogExchange: false,
      showMyClassLoading: true,
      showMoreClassLoading: true,
      isScroll: true // 是否可以滚动
    }
  },
  computed: {
    showLoading () {
      return this.showMyClassLoading || this.showMoreClassLoading
    }
  },
  created () {
    this.getMySpecialClassroom()
    this.getMoreSpecialClassroom()
  },
  mounted () {
    document.addEventListener('scroll', this.scrollMoreData, true)
  },
  destroyed () {
    document.removeEventListener('scroll', this.scrollMoreData, true)
  },
  methods: {
    getMySpecialClassroom () {
      const params = {
        studentCourseListType: 'ENERGIZE'
      }
      this.getStuCourseList(params)
    },
    async getStuCourseList (params) {
      if (!this.pageLoadedMy) {
        this.pageLoadingMy = true
      }

      try {
        const { data } = await getStuCourseList(params, false)
        const content = data.filter((item) => {
          return item.coursecomm
        })
          .map((item) => {
            return item.coursecomm
          })
        this.myClassroomList = this.myClassroomList.concat(content || [])
        this.pageNoMy += 1
        this.pageLoadingMy = false
        if (!content || content.length < 1) {
          this.pageLoadedMy = true
          return true
        }
      } catch (error) {
        return error
      } finally {
        this.showMyClassLoading = false
      }
    },
    scrollMoreData () {
      const el = document.querySelector('.special')
      const offsetHeight = el.offsetHeight
      el.onscroll = () => {
        const scrollTop = el.scrollTop
        const scrollHeight = el.scrollHeight
        if (offsetHeight + scrollTop - scrollHeight >= -1 &&
          this.isScroll &&
          this.pageLoadedMore) {
          console.log('滚动到了底部')
          this.pageNoMore++
          this.getMoreSpecialClassroom()
        }
      }
    },
    getMoreSpecialClassroom () {
      this.isScroll = false
      const params = {
        pageNumber: this.pageNoMore,
        pageSize: this.pageSize,
        type: 'ENERGIZE_COURSE'
      }
      this.getCourseCommList(params)
    },
    async getCourseCommList (params) {
      try {
        const { data } = await getCourseCommList(params)
        const content = data.content
        this.moreClassroomList = this.moreClassroomList.concat(content || [])
        this.pageLoadingMore = false
        this.isScroll = true
        if (!content || content.length < 1) {
          console.log('没有更多数据了')
          this.pageLoadedMore = false
          return true
        }
      } catch (error) {
        return error
      } finally {
        this.showMoreClassLoading = false
      }
    },
    openExchange () {
      this.dialogExchange = true
    },
    closeExchange () {
      this.dialogExchange = false
    },
    toDetail (item) {
      this.$router.push(`/classpro/classroom/${item.id}?from=/classpro/special`)
    },
    goBack () {
      this.$router.push('/classpro')
    },
    triggerMessage () {
      this.$message({
        message: '老师您所在的学校还没有购买该课程哦～',
        iconClass: 'iconTip',
        customClass: 'lockTip',
        center: true,
        duration: 3000
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/mixin';
$vw_design_width: 965;
$vw_design_height: 650;
@function ui_w($px) {
  @return $px / $vw_design_width * 100vw;
}
@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}

.special {
  position: relative;
  height: calc(100% - 62px);
  background: #F8FAFF;
  padding: 22px 28px 20px 30px;
  overflow: scroll;
}

.bg-corner {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 116px;
  height: 82px;
}

.goBack {
  cursor: pointer;
}

.session-container {
  height: 100%;
  .session {
    padding: 22px 0 7px 0;
    z-index: 1;
  }
}

.specialTitle {
  font-size: 16px;
  font-weight: 500;
  color: #0B0B0B !important;
  line-height: 22px;
}

.my-special-list,
.other-special-list {
  flex-wrap: wrap;
  position: relative;
  display: flex;
  width: 100%;
  margin-top: 12px;
  padding: 10px 0 10px 0;
  // gap: ui_w(30) ui_w(40);  //不适配低版本浏览器
  flex-flow: row wrap;

  .item{
    flex: 0 0 25%;
    margin-bottom: 30px;

    .item-container {
      width: ui_w(194);
      height: ui_w(153);
      background: #FFFFFF;
      box-shadow: 0px 3px 6px 0px rgba(62, 89, 253, 0.1);
      border-radius: 10px;
      cursor: pointer;
    }

    .item-cover {
      width: ui_w(194);
      height: ui_w(120);
      object-fit: cover;
      border-radius: 10px;
    }

    p {
      font-size: ui_h(14);
      font-weight: 400;
      color: #4B4B4B;
      line-height: ui_h(20);
      margin-top: 10px;
      padding: 0 9px;
      @include ellipses(1)
    }

    .lock-cover {
      position: absolute;
      width: ui_w(194);
      height: ui_w(120);
      background: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      .lock {
        position: absolute;
        width: ui_w(28);
        height: ui_w(28);
        top: 50%;
        left: 50%;
        border-radius: 50%;
        box-shadow: 1px 1px 1px 0px rgba(0, 0, 0, 0.23),  -1px -1px 1px 0px rgba(0, 0, 0, 0.15);
        opacity: 0.55;
        transform:  translateX(-50%) translateY(-50%);
      }
    }
  }
}

.list-empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: ui_h(90);
      height: ui_h(90);
    }

    .hint {
      display: flex;
      font-size: ui_h(12);
      font-weight: 400;
      color: #8C9399;
      line-height: ui_h(17);

      .hint-blue {
        color: rgba(31, 102, 255, 1);
        cursor: pointer;
      }
    }

    .hint-padding {
      padding:ui_h(8) 0 ui_h(4)
    }
  }
</style>

<style lang="scss">
.lockTip {
  top: 50% !important;
  width: 321px;
  min-width: 100px !important;
  height: 40px;
  padding: 12px 17px;
  background: rgba(0, 0, 0, 0.5) !important;
  border-radius: 10px;
  border-color: transparent !important;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 20px;
}
.iconTip {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  background: url('../../../assets/images/special/tip.png');
}
</style>
