<template>
  <div class="my-course">
    <div class="tag-box flex">
      <div :class="tagIndex === 1 ? 'tag-item__active' : 'tag-item'" @click="changeTag(1)">
        我是老师
      </div>
      <div :class="tagIndex === 2 ? 'tag-item__active' : 'tag-item'" @click="changeTag(2)">
        我是学生
      </div>
    </div>
    <div class="my-course-body">
      <Tech v-if="tagIndex === 1" />
      <Stu v-if="tagIndex === 2" />
    </div>
  </div>
</template>

<script>
import Tech from './components/tech.vue'
import Stu from './components/stu.vue'
export default {
  components: {
    Tech,
    Stu
  },
  data () {
    return {
      tagIndex: 1
    }
  },
  mounted () {
    if (this.$route.query && this.$route.query.type === 'student') {
      this.tagIndex = 2
    }
  },
  methods: {
    changeTag (index) {
      this.tagIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.my-course {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  border-radius: 0 0 10px 10px;

  .tag-box {
    width: 100%;
    height: 32px;

    .tag-item,
    .tag-item__active {
      border-radius: 8px 25px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: var(--font-size-L);
      font-weight: 500;
      height: 100%;
      width: 110px;
      cursor: pointer;
    }

    .tag-item {
      background: #f4f4f4;
      color: #717171;
    }

    .tag-item__active {
      background: #ffffff;
      font-weight: 500;
      color: #3479ff;
    }
  }

  .my-course-body {
    height: calc(100% - 32px);
    background: #fff;
    border-radius: 0 10px 10px 10px;
    padding: 10px;
    box-sizing: border-box;
    @include scrollBar;
  }
}
</style>
