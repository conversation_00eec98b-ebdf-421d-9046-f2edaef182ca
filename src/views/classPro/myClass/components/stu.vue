<template>
  <div class="index-box">
    <div class="card-box">
      <div class="card-title-box">
        <div v-show="tableData.length !== 0" class="t-title">我的班级</div>
        <div v-show="tableData.length > 0" class="exchange-btn" @click="jionClassShow = true">加入班级</div>
      </div>
      <div class="w h overflow-y-auto carlist">
        <!-- 无数据时占位 -->
        <div v-if="tableData.length === 0" class="empty-container">
          <div class="hint">该功能提供给学生使用，学生请先绑定班级</div>
          <div class="exchange-btn mt50" @click="jionClassShow = true">加入班级</div>
        </div>

        <template v-else>
          <el-table
            :data="tableData"
            :header-row-style="{ background: '#F8FAFF' }"
            :header-cell-style="{
              background: '#F8FAFF',
              color: '#3479FF',
              border: 'none',
              'font-weight': '400',
            }"
            :header-row-class-name="tableHeader"
            :row-class-name="rowClass"
            :cell-class-name="cellClass"
            :height="'calc(100% - 40px)'"
            style="width: 100%"
          >
            <el-table-column prop="name" align="left" label="班级名称">
              <template slot-scope="scope">
                <div class="flex items-center">
                  <div>{{ scope.row.name }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="left" label="学生名单">
              <template slot-scope="scope">
                <div class="pointer" @click="showStuLists(scope.row.userExtra)">{{ scope.row.userExtra | stuList }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="" align="left" label="操作">
              <template slot-scope="scope">
                <span class="item-handle-del" @click="quiteClass(scope.row)">退出班级</span>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
    </div>

    <NormalDialog
      v-if="jionClassShow"
      width="30vw"
      :title="'加入班级'"
      :dialog-visible="jionClassShow"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="closeJionClass"
    >
      <div class="w flex-col">
        <div class="flex w items-center mb10">
          <el-input v-model="classCode" style="flex: 1" class="w" placeholder="请输入班级邀请码" />
        </div>
        <div class="mt20">
          <template v-if="className">
            班级：{{ className }}
          </template>
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="addClass">确定</div>
      </template>
    </NormalDialog>

    <el-drawer
      title=""
      :visible.sync="drawer"
      :direction="'btt'"
      :show-close="true"
      :with-header="false"
      :size="'100%'"
      :custom-class="'grade-drawer'"
      :close-on-press-escape="false"
    >

      <div class="top">
        <svg-icon icon-class="close" class-name="close-svg" @click="drawer = false" />
      </div>
      <div class="bottom">
        <div class="drawer-title">
          <div class="line"></div>
          <div>学生名单</div>
          <div class="stu-len">
            共{{ stuLists.length }}人
          </div>
          <!-- <div class="btn-active" @click="drawer = false; inputStuListShow = true;">导入名单</div> -->
        </div>
        <div class="drawer-detail">
          <div v-for="(item, index) in stuLists" :key="index" class="stu-item">
            {{ item }}
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 退出班级 -->
    <ComponentDialog
      :width="'700px'"
      :title="'退出'"
      :dialog-visible="removeVisible"
      :is-center="true"
      @closeDialog="removeVisible = false"
    >
      <div class="flex flex-col w items-center">
        <div class="f14" style="margin-bottom: 30px; line-height: 40px">
          退出班级后，该班级相关信息将全部清除不可找回， 确认退出吗？
        </div>
        <div class="flex justify-around w">
          <div
            class="edu-btn-opacity f14"
            @click="removeVisible = false"
          >
            放弃
          </div>
          <div class="edu-btn f14" @click="remove">确定退出</div>
        </div>
      </div>
    </ComponentDialog>
  </div>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import ComponentDialog from '@/components/classPro/ComponentDialog'
import { joinClass, getUserClassList, quitClass, getClassInfo } from '@/api/digital-api.js'
import { debounce } from '@/utils/index'
import _ from 'lodash'
import { MessageBox } from 'element-ui'

export default {
  components: { NormalDialog, ComponentDialog },
  filters: {
    stuList (val) {
      if (val) {
        if (val.studentList) {
          const arr = val.studentList.split(',')
          return arr.length + '人>'
        }
      }
      return ''
    }
  },
  data () {
    return {
      removeVisible: false,
      nowEditRow: null,
      drawer: false,
      stuLists: [],
      inputInfo: [],
      tableData: [],
      jionClassShow: false,
      classCode: '',
      className: ''
    }
  },
  watch: {
    classCode: {
      handler: function (val) {
        if (val.length > 4) {
          this.getCodeClassName()
        }
      },
      immediate: true
    }
  },
  mounted () {
    this._getUserClass()
  },
  methods: {
    tableHeader (row, rowIndex) {
      return 'table-header'
    },
    rowClass (row, rowIndex) {
      return 'row-class'
    },
    cellClass (row, rowIndex) {
      return 'cell-class'
    },
    getCodeClassName: _.debounce(async function () {
      if (this.classCode.length < 4) return
      const { data } = await getClassInfo({
        inviteCode: this.classCode
      })
      console.log(data)
      if (data) {
        let className = ''

        // 添加学校名称
        if (data.school && data.school.name) {
          className += data.school.name
        }
        console.log(className)

        // 添加教师名称
        // if (data.assistantList && data.assistantList.length > 0) {
        //   const teacherName = data.assistantList[0].displayName
        //   if (teacherName) {
        //     className += teacherName + '老师的'
        //   }
        // }
        console.log(className)
        // 添加班级名称
        if (data.name) {
          className += data.name
        }
        console.log(className)
        // 如果只有班级名称，直接使用
        this.className = className || data.name || ''
      }
    }, 1000),
    closeJionClass () {
      this.jionClassShow = false
      this.className = ''
      this.classCode = ''
    },
    async _getUserClass () {
      const { data } = await getUserClassList({
        scene: 'STUDENT_CLASS'
      })
      this.tableData = data
    },
    showStuLists (row) {
      this.drawer = true
      this.stuLists = []
      this.inputInfo = row
      if (row) {
        if (row.studentList) {
          const arr = row.studentList.split(',')
          this.stuLists = arr
        }
      }
    },
    quiteClass (row) {
      this.nowEditRow = row
      this.removeVisible = true
    },
    remove: debounce(async function () {
      try {
        await quitClass({ classUserId: this.nowEditRow.userId })
        this.removeVisible = false
        this._getUserClass()
      } catch (error) {
        // this.removeVisible = false
      }
    }, 3000, true),
    async addClass () {
      if (this.classCode) {
        try {
          await MessageBox.confirm('确认加入该班级?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          await joinClass({
            inviteCode: this.classCode
          })
          this.jionClassShow = false
          this.classCode = ''
          this._getUserClass()
        } catch (error) {
          // 用户点击取消时不执行操作
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.index-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  background: #FFF;
  border-radius: 10px;

  .exchange-btn {
    padding: 10px 15px;
    border-radius: 28.327px;
    background: #266BFB;
    font-size: 12px;
    color: #FFF;
    cursor: pointer;
  }

  .empty-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .mt50 {
      margin-top: 50px;
    }

    .hint {
      color: #000;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .carlist {
    height: calc(100% - 50px);
    @include scrollBar;
  }

  .card-box {
    padding: 5px 15px;
    margin-bottom: 10px;
    box-sizing: border-box;
    height: calc(100% - 40px);

    &:last-child {
      margin-bottom: 0;
    }

    .card-title-box {
      margin: 10px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .t-title {
        color: #000;
        font-size: var(--font-size-XXL);
      }
    }

  }
}

.mt20 {
  margin-top: 20px;
}

.top {
  // height: 400px;
  // display: flex;
  // padding: 338px 30px 0 0;
  position: fixed;
  bottom: 210px;
  right: 10px;

  .close-svg {
    width: 36PX !important;
    height: 36PX !important;
    margin-left: auto;
    cursor: pointer;
  }
}

.bottom {
  position: fixed;
  bottom: 0px;
  // height: calc(100% - 400px);
  height: 200px;
  width: 100%;
  background: #FFFFFF;
  border-radius: 40px 40px 0px 0px;
  padding: 20px 20px 0 20px;

  .drawer-title {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 16px;
    color: #1C1B1A;
    box-sizing: border-box;

    .line {
      width: 2px;
      height: 14px;
      background: #3479FF;
      margin-right: 10px;
    }

    .stu-len {
      margin-left: 20px;
      margin-right: 50px;
      font-weight: 500;
      font-size: 16px;
      color: #3479FF;
    }
  }

  .drawer-detail {
    margin-top: 20px;
    width: 100%;
    height: calc(100% - 60px);
    overflow-y: auto;

    .stu-item {
      padding: 20px 20px;
      box-sizing: border-box;
      font-size: 18px;
      color: #19191A;
      display: inline-block;
    }
  }
}

.grade-drawer {
  width: 100%;
  height: 100%;
}

.item-handle-del {
  color: #ff3434;
}

.item-handle-del,
.item-handle-dis,
.item-handle {
  font-size: var(--font-size-L);
  cursor: pointer;
  margin-right: 10px;
  text-decoration: underline;
}
</style>
