<template>
  <div class="w h">
    <grade from-type="classSetting" />
    <!-- <div v-else class="w h flex flex-col items-center">
      <div class="w tl">我的班级</div>
      <div class="my-tips">该功能提供给<span class="my-tips-b">老师使用</span>，请老师先<span class="my-tips-b">激活账号</span>并<span class="my-tips-b">创建班级</span></div>
      <div class="my-btn" @click="bindSchoolShow = true">输入学校激活码</div>
      <bindSchool
        v-if="bindSchoolShow"
        :show="bindSchoolShow"
        :append-to-body="true"
        @close="bindSchoolShow = false"
      />
    </div> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import bindSchool from '@/components/classPro/ClassBind/bindSchool.vue'
import grade from '@/views/classPro/educational/components/grade.vue'
export default {
  components: {
    grade
    // bindSchool
  },
  data () {
    return {
      bindSchoolShow: false
    }
  },
  computed: {
    ...mapGetters([
      'school',
      'id'
    ])
  }
}
</script>

<style lang="scss" scoped>

.my-tips {
  margin-top: 200px;
  font-size: 14px;
}

.my-tips-b {
  font-weight: 500;
}
.my-btn {
  padding: 8px 10px;
  color: #fff;
  background: #2F80ED;
  border-radius: 5px;
  margin-top: 40px;
  font-size: 12px;
  cursor: pointer;
}
</style>
