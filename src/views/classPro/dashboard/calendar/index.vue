<template>
  <div class="course-calendar">
    <div class="wrap">
      <div class="header">
        <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="backToHome" />
        <div class="back" @click="backToHome">返回</div>
      </div>
      <div class="calendar-box">
        <div class="button-group">
          <div class="button" :class="{'button-active': weekType === -1}" @click="changeWeekType(-1)">上周</div>
          <div class="button" :class="{'button-active': weekType === 0}" @click="changeWeekType(0)">本周</div>
          <div class="button" :class="{'button-active': weekType === 1}" @click="changeWeekType(1)">下周</div>
        </div>
        <div class="calander-title">课程日历</div>
        <div class="calendar-content">
          <div class="calendar-box">
            <div class="week-list grid-cols-7">
              <div v-for="index in 7" :key="index" class="week-item">
                <div v-if="isToday(index)" class="today">今</div>
                <div v-else class="week mb-5">{{ weekName[index - 1] }}</div>
                <div class="date">{{ weekCheckingIn(index) }}</div>
              </div>
            </div>
            <div v-if="orginScheduleList.length > 0" class="schedule-list grid-cols-7">
              <div v-for="(item, index) in scheduleList" :key="item.weekDay">
                <div
                  v-for="item2 in item.weekList"
                  :key="item2.courseName"
                  class="schedile-item mb-10 flex flex-col"
                  :class="{
                    'bg-blue': item2.courseType === 'COURSE' && !courseFinished(item2),
                    'bg-orange': item2.courseType === 'AI_COURSE' && !courseFinished(item2),
                    'bg-grey': courseFinished(item2)
                  }"
                >
                  <div class="flex mb-10">
                    <div v-if="item2.courseType === 'COURSE'" class="time">{{ formatHHmm(item2.startTime) }}</div>
                    <div
                      class="course-type"
                      :class="[item2.courseType === 'COURSE' ? 'course-live' : 'course-ai']"
                    >{{ item2.courseType === 'COURSE' ? '直播课' : '双师AI课堂' }}</div>
                  </div>
                  <div class="mb-5 fw-500 schedule-name">{{ item2.courseName || '' }}</div>
                  <div class="mb-5 schedule_period">{{ item2.courseType === 'COURSE' ? `课时${item2.unitNo} ${item2.liveLessonInfo.name}` : '' }}</div>
                  <div class="mb-5">班级：{{ item2.lessonStudent.name || '' }}</div>
                  <div
                    class="schedule-btn"
                    :class="{
                      'btn-blue': item2.liveLessonInfo && item2.liveLessonInfo.lessonStatus === 'Opening',
                      'btn-orange': !courseFinished(item2) && item2.courseType === 'AI_COURSE',
                      'btn-grey': +currentTime < +item2.startTime && item2.courseType === 'COURSE'
                    }"
                    @click="attendClass(item2)"
                  >{{ courseFinished(item2)
                    ? '已完成'
                    : ((item2.liveLessonInfo && item2.liveLessonInfo.lessonStatus === 'Opening')
                      || item2.courseType === 'AI_COURSE') ? '上课' : '未开始' }}</div>
                  <div v-if="isToday(index + 1) && !courseFinished(item2)" class="line" :class="[item2.courseType === 'COURSE' ? 'btn-blue' : 'btn-orange']"></div>
                </div>
              </div>
            </div>
            <div v-else class="empty-schedule" style="flex: 1">
              <img src="@/assets/images/empty4.png" alt="" />
              <div class="hint">今日暂无排课哦～</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getLessonScheduleList } from '@/api/lesson-api.js'
import { formatMMDDDot, formatHHmm } from '@/utils/date.js'
import { throttle } from '@/utils/index'
import { getAiConfig } from '@/api/dictionary-api.js'
export default {
  data () {
    return {
      weekType: 0,
      scheduleList: undefined,
      weekName: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      formatMMDDDot,
      formatHHmm,
      oneDayTime: 24 * 60 * 60 * 1000,
      currentTime: parseInt(new Date().getTime()),
      aiSite: undefined,
      orginScheduleList: [],
      canGoToClass: true
    }
  },
  created () {
    this._getLessonScheduleList()
    this._getAiConfig()
    this.weekCheckingIn()
  },
  methods: {
    backToHome () {
      this.$router.push({ path: '/classpro/myCourse' })
    },
    //  切换周
    changeWeekType (weekType) {
      this.scheduleList = undefined
      this.weekType = weekType
      this._getLessonScheduleList()
    },
    //  获取当前周几
    weekCheckingIn (index) {
      var now = new Date()
      var nowTime = now.getTime()
      var day = now.getDay() || 7 // 不加 || 7.周末会变成下周一
      this.MondayTime = nowTime - (day - index - this.weekType * 7) * this.oneDayTime // 显示周一
      // 调用方法
      return formatMMDDDot(new Date(this.MondayTime))
    },
    isToday (index) {
      var now = new Date()
      var day = now.getDay()
      var _isToday = false
      if (this.weekType !== 0) {
        _isToday = false
      } else {
        _isToday = index === 7 ? day === 0 : day === index
      }
      return _isToday
    },
    //  获取课程日历
    _getLessonScheduleList () {
      const params = {
        'listType': 'WEEK',
        'param': this.weekType
      }
      getLessonScheduleList(params).then(
        response => {
          this.scheduleList = [
            { 'weekDay': 1, 'weekList': [] },
            { 'weekDay': 2, 'weekList': [] },
            { 'weekDay': 3, 'weekList': [] },
            { 'weekDay': 4, 'weekList': [] },
            { 'weekDay': 5, 'weekList': [] },
            { 'weekDay': 6, 'weekList': [] },
            { 'weekDay': 0, 'weekList': [] }
          ]
          this.orginScheduleList = response.data
          for (var item of this.orginScheduleList) {
            const myDate = new Date(item.startTime).getDay()
            for (var schedule of this.scheduleList) {
              if (+myDate === schedule.weekDay) {
                schedule.weekList.push(item)
                break
              }
            }
          }
        }
      )
    },
    //  课程是否完成
    courseFinished (item) {
      return (
        item.courseType === 'COURSE' &&
        (item.liveLessonInfo.lessonStatus === 'Completed' ||
        item.liveLessonInfo.lessonStatus === 'UnderReview' ||
        item.liveLessonInfo.lessonStatus === 'Unqualified')
      ) ||
        (
          item.courseType === 'AI_COURSE' && item.studentsCourse.total === item.studentsCourse.finishedProgress
        )
    },
    //  去上课
    async attendClass (item) {
      const aiSite = this.aiSite
      const that = this
      switch (item.courseType) {
        case 'COURSE':
          // if (item.liveLessonInfo.lessonInfo2.roomUrl && !this.courseFinished(item)) {
          //   window.open(item.liveLessonInfo.lessonInfo2.roomUrl, '_blank')
          // }
          if (!this.canGoToClass) {
            return
          }
          if (item.liveLessonInfo.lessonInfo2.roomUrl) {
            this.canGoToClass = false
            setTimeout(() => {
              this.canGoToClass = true
            }, 8000)
            this.$message.warning({
              message: '正在进入教室请勿重复点击',
              duration: 8000
            })
            window.open(item.liveLessonInfo.lessonInfo2.roomUrl, '_blank')
          }
          break
        case 'ENERGIZE_COURSE':
          this.$router.push(`/classpro/classroom/${item.courseId}`)
          break
        case 'AI_COURSE':
          // if (!this.courseFinished(item)) {
          //   throttle(function () {
          //     const id = that.$store.getters.id
          //     if (item.lessonStudent && item.lessonStudent.token) {
          //       var token = item.lessonStudent.token
          //       window.open(aiSite + `/aiPackage/${item.studentsCourse.courseId}/${item.studentsCourse.id}?token=Bearer ${token}&assistantId=${id}`, '_blank')
          //     }
          //   }, 3000)
          // }
          throttle(function () {
            const id = that.$store.getters.id
            if (item.lessonStudent && item.lessonStudent.token) {
              var token = item.lessonStudent.token
              window.open(aiSite + `/aiPackage/${item.studentsCourse.courseId}/${item.studentsCourse.id}/${id}?token=Bearer ${token}&assistantId=${id}`, '_blank')
            }
          }, 3000)
          break
      }
    },
    // 获取ai课地址
    async _getAiConfig () {
      getAiConfig({ configType: 'AI_PROJECT_DNS' })
        .then(response => {
          if (response != null) {
            this.aiSite = response.data[0].keyValue || null
            if (this.aiSite.substring(this.aiSite.length - 1, this.aiSite.length) === '/') {
              this.aiSite = this.aiSite.substring(0, this.aiSite.length - 1)
            }
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.course-calendar {
    // height: calc(100% - 62px);
    height: calc(100% - 1px);
    width: 100%;
    overflow: hidden;
}

.wrap {
    width: 100%;
    height: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;

    .header {
        display: flex;
        align-items: center;
        height: 40px;

        img {
            width: 14px;
            height: 14px;
            margin-right: 8px;
            cursor: pointer;
        }

        .back {
            line-height: 20px;
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0;
            cursor: pointer;
        }
    }

    .calendar-box {
      //width: 100vw;
        height: calc(100% - 40px);
        //width: calc(100% - 180px);
        background: #FFFFFF;
        box-shadow: 0 3px 14px 0 rgba(233,240,255,0.50);
        border-radius: 10px 10px 0 0;
        padding: 20px 5px 0;
        position: relative;
        display: flex;
        flex-direction: column;

        .calander-title {
            font-weight: 500;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0;
            padding-left: 7px;
            position: relative;
            line-height: 22px;

            &::before {
                content: ' ';
                position: absolute;
                left: 0;
                transform: translate(0, -50%);
                top: 50%;
                height: 100%;
                width: 2px;
                height: 14px;
                background: #3479FF;
                border-radius: 1px;
            }
        }

        .button-group {
            position: absolute;
            transform: translate(-50%, 0);
            left: 50%;
            top: 20px;
            display: flex;
            gap: 10px;
            z-index: 10;

            .button {
                width: 80px;
                height: 30px;
                border: 1px solid #999999;
                border-radius: 15px;
                line-height: 30px;
                text-align: center;
                font-weight: 400;
                font-size: var(--font-size-L);
                color: #8C8C8C;
                letter-spacing: 0;
                cursor: pointer;
            }

            .button-active {
                background: #DFEBFF;
                border-radius: 15px;
                border: none;
                color: #3479FF;
            }
        }

        .calendar-content {
            //padding: 0 26px;
            width: 100%;
            height: calc(100% - 20px);
            //padding-top: 44px;
            display: flex;
            flex-direction: column;
            overflow-x: auto;
            @include scrollBar;

            .calendar-box {
              width: 110vw;
              //width: 100%;
              //width: calc(100% - 180px);
              //min-width: 1000px;
              height: 100%;
            }

            .week-list {
                display: grid;
                gap: 24px;
                //height: 70px;
                background: rgba(52, 121, 255, 0.04);
            }

            .week-item {
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                font-weight: 400;
                font-size: var(--font-size-L);
                color: #1C1B1A;
                letter-spacing: 0;
                margin-bottom: 10px;
            }

            .today {
                width: 27px;
                height: 27px;
                background: #3479FF;
                border-radius: 100px;
                font-weight: 500;
                font-size: var(--font-size-L);
                color: #FFFFFF;
                letter-spacing: 0;
                text-align: center;
                line-height: 27px;
                margin: 0 auto;
            }

            .week {
                font-weight: 500;
            }

            .schedule-list {
                display: grid;
                //gap: 24px;
                gap: 10px;
                padding-top: 5px;
                height: calc(100% - 70px);
                overflow: scroll;
                overflow-x: hidden;
                @include scrollBar;
            }

            .schedile-item {
                width: 100%;
                //height: 120px;
                min-height: 120px;
                padding: 5px;
                font-weight: 400;
                font-size: var(--font-size-L);
                color: #0F0F0F;
                letter-spacing: 0;
                position: relative;
            }

            .schedule_period {
              @include ellipses(1);
            }

            .schedule-name {
              @include ellipses(2);
            }

            .bg-grey {
                background: #F7F7F7;
            }

            .bg-blue {
                background: #f5faff;
            }

            .bg-orange {
                background: #fefaf6;
            }

            .time {
                font-weight: 500;
                font-size: var(--font-size-L);
                color: #7F8182;
                letter-spacing: 0;
                line-height: 22px;
            }

            .course-type {
                height: 20px;
                padding: 0 6px;
                border-radius: 4px;
                font-weight: 400;
                font-size: var(--font-size-L);
                letter-spacing: 0;
                line-height: 20px;
                margin-left: auto;
            }

            .course-live {
                border: 1px solid #3479FF;
                color: #3479FF;
            }

            .course-ai {
                border: 1px solid #FF9000;
                color: #FF9000;
            }

            .schedule-btn {
                margin-top: auto;
                margin-left: auto;
                margin-bottom: auto;
                width: 50px;
                height: 20px;
                border-radius: 10px;
                font-weight: 400;
                font-size: 12px;
                color: #757779;
                letter-spacing: 0;
                text-align: center;
                line-height: 20px;
                cursor: pointer;
            }

            .btn-blue {
                background: #3479FF;
                color: #FFFFFF;
                cursor: pointer;
            }

            .btn-orange {
                background: #FF9000;
                color: #FFFFFF;
                cursor: pointer;
            }

            .btn-grey {
                background: #E1E1E1;
                color: #818181;
            }

            .line {
                height: 100%;
                width: 2px;
                position: absolute;
                left: 0;
                top: 0;
            }
        }

        .empty-schedule {
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 102px;
            height: 105px;
            object-fit: contain;
            margin-right: 15px;
          }

          .hint {
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #161515;
            letter-spacing: 0;
            text-align: center;
          }
        }
    }

    .fw-500 {
        font-weight: 500;
    }

    .mb-5 {
        margin-bottom: 5px;
    }

    .mb-10 {
        margin-bottom: 10px;
    }

    .mb-20 {
        margin-bottom: 20px;
    }
}
</style>
