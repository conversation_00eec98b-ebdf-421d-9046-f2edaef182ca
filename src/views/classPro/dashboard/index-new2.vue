<template>
  <div class="index-box">
    <div
      v-if="activeList.length === 0 && aiList.length === 0 && liveCourseList.length === 0"
      class="card-box w empty-box"
    >
      <img class="empty" src="@/assets/images/empty.png" alt="占位图2" />
      <div class="empty-text">暂无数据</div>
    </div>
    <!-- 活动推荐 -->
    <div v-if="activeList.length > 0" class="card-box">
      <div class="card-title-box">
        <div class="t-title">活动推荐</div>
        <div class="t-more" @click="$router.push({ path: '/classpro/activity' })">
          更多
          <img class="nav-icon" src="../../../assets/images/dashboard/new/more.svg" />
        </div>
      </div>
      <div class="w">
        <el-row :gutter="20">
          <!-- xs<768px， sm≥768px， md≥992px， lg≥1200px， xl≥1920px -->
          <template v-for="(item, index) in activeList">
            <el-col
              v-if="index < size"
              :key="item.id"
              :xs="12"
              :sm="12"
              :md="12"
              :lg="8"
              :xl="6"
              class="course-box pointer"
            >
              <div @click="toActivityDetail(item.activity)">
                <div class="r-container">
                  <div class="img-box">
                    <img v-if="item.activity.cover" :src="item.activity.cover" />
                    <img v-else src="../../../assets/images/default-cover.jpg" />
                  </div>
                </div>
                <div class="course_title article-singer-container">
                  {{ item.activity.name }}
                </div>
                <div class="course_des article-singer-container">活动时间：{{ formatDot(item.activity.startTime) }}-{{
                  formatDot(item.activity.endTime) }}</div>
              </div>
            </el-col>
          </template>
        </el-row>
      </div>
    </div>
    <!-- 数字教材 -->
    <div v-if="digList.length > 0" class="card-box">
      <div class="card-title-box">
        <div class="t-title">数字教材</div>
        <div class="t-more" @click="$router.push({ path: '/classpro/digitalbooks' })">
          更多
          <img class="nav-icon" src="../../../assets/images/dashboard/new/more.svg" />
        </div>
      </div>
      <div class="w">
        <!-- <el-row :gutter="20">
          <el-col v-for="item in digList" :key="item.id" :span="6" class="course-box">
            <div class="w h" @click="toDigDetail(item.digitalBook)">
              <div class="r-container2">
                <div class="img-box">
                  <img
                    v-if="item.digitalBook.cover"
                    :src="item.digitalBook.cover"
                  />
                  <img v-else src="../../../assets/images/default-cover.jpg" />
                </div>
              </div>
              <div class="course_title article-singer-container">
                {{ item.digitalBook.title }}
              </div>
            </div>
          </el-col>
        </el-row> -->
        <div class="flex flex-wrap" style="margin-left: -10px; margin-right: -10px;">
          <div v-for="item in digList" :key="item.id" class="dig-box">
            <div class="w h" @click="toDigDetail(item.digitalBook)">
              <div class="r-container2">
                <div class="img-box">
                  <!-- <img class="ronghe" src="../../../assets/images/ronghe.png" alt="" /> -->
                  <img
                    v-if="item.digitalBook.cover"
                    :src="item.digitalBook.cover"
                  />
                  <img v-else src="../../../assets/images/default-cover.jpg" />
                </div>
              </div>
              <div class="course_title article-singer-container">
                <span v-if="item.digitalBook.digitalBookType === 'PAPER_DIGITAL'" class="ronghe-title">纸数融合</span>
                {{ item.digitalBook.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 双师AI课堂 -->
    <div v-if="aiList.length > 0" class="card-box">
      <div class="card-title-box">
        <div class="t-title">双师AI课堂</div>
        <div class="t-more" @click="$router.push({ path: '/classpro/aiCourse' })">
          更多
          <img class="nav-icon" src="../../../assets/images/dashboard/new/more.svg" />
        </div>
      </div>
      <div class="w">
        <el-row :gutter="20">
          <el-col v-for="item in aiList" :key="item.id" :span="6" class="course-box pointer">
            <div class="w h" @click="toDetail(item.aiCourse)">
              <div class="r-container">
                <div class="img-box">
                  <img v-if="item.aiCourse.coverUrl" :src="item.aiCourse.coverUrl" />
                  <img v-else src="../../../assets/images/default-cover.jpg" />
                </div>
              </div>
              <div class="course_title article-singer-container">
                {{ item.aiCourse.title }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 空中课堂 -->
    <div v-if="liveCourseList.length > 0" class="card-box">
      <div class="card-title-box">
        <div class="t-title">空中课堂</div>
        <div class="t-more" @click="$router.push({ path: '/classpro/skyCourse' })">
          更多
          <img class="nav-icon" src="../../../assets/images/dashboard/new/more.svg" />
        </div>
      </div>
      <div class="w">
        <el-row :gutter="20">
          <el-col v-for="item in liveCourseList" :key="item.id" :span="6" class="course-box pointer">
            <div class="w h" @click="toLiveDetail(item.liveCourse)">
              <div class="r-container">
                <div class="img-box">
                  <img v-if="item.liveCourse.coverUrl" :src="item.liveCourse.coverUrl" />
                  <img v-else src="../../../assets/images/default-cover.jpg" />
                </div>
              </div>
              <div class="course_title article-singer-container">
                {{ item.liveCourse.name }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { getRecommend } from '@/api/activity-api.js'
import { getAiConfig } from '@/api/dictionary-api.js'
// import { getToken } from '@/utils/auth.js'
import { formatDot } from '@/utils/date.js'
import { throttle } from '@/utils/index'
export default {
  data () {
    return {
      formatDot,
      aiList: [],
      activeList: [],
      liveCourseList: [],
      digList: [],
      aiSite: null,
      size: 2
    }
  },
  mounted () {
    this._getAiConfig()
    this._getRecommend()
    this.getWidth()

    window.addEventListener('resize', this.getWidth)
  },
  destroyed () {
    window.removeEventListener('resize', this.getWidth)
  },
  methods: {
    async _getRecommend () {
      const { data } = await getRecommend({
        recommendType: 'HOME_ALL'
      })

      if (data && data.length > 0) {
        this.aiList = data.filter((v) => {
          return v.type === 'HOME_AI_COURSE'
        })

        this.activeList = data.filter((v) => {
          return v.type === 'HOME_ACTIVITY'
        })

        this.liveCourseList = data.filter((v) => {
          return v.type === 'HOME_LIVE_COURSE'
        })
        this.digList = data.filter((v) => {
          return v.type === 'HOME_DIGITAL_BOOK'
        })
      }
    },
    // 获取ai课地址
    async _getAiConfig () {
      getAiConfig({ configType: 'AI_PROJECT_DNS' })
        .then(response => {
          if (response != null) {
            // this.aiSite = 'http://localhost:8080'
            this.aiSite = response.data[0].keyValue || null
            if (this.aiSite.substring(this.aiSite.length - 1, this.aiSite.length) === '/') {
              this.aiSite = this.aiSite.substring(0, this.aiSite.length - 1)
            }
          }
        })
    },
    // 去详情页面
    async toDetail (item) {
      // const aiSite = this.aiSite
      const that = this
      throttle(function () {
        that.$router.push(`/classpro/course/package/0/${item.id}/1?f=recom&h=0`)
        // const id = that.$store.getters.id
        // // 推荐课程 按照预览模式逻辑走
        // const token = getToken()
        // window.open(aiSite + `/aiPackage/${item.id}/0/${id}?token=Bearer ${token}&assistantId=${id}&opt=2`, '_blank')
      }, 3000)
    },
    // 去空中课堂详情页面
    async toLiveDetail (item) {
      const that = this
      throttle(function () {
        that.$router.push(`/classpro/course/package/0/${item.id}/0?f=recom`)
      }, 3000)
    },
    // 数字教材详情页
    async toDigDetail (item) {
      const that = this
      throttle(function () {
        that.$router.push(`/classpro/digitalbooks/detail?id=${item.id}&f=recom`)
      }, 3000)
    },
    // 去活动详情页面
    toActivityDetail (activity) {
      this.$router.push({ path: `/activity/detail/${activity.id}?f=recom` })
    },
    // 获取屏幕大小
    getWidth () {
      const width = document.body.offsetWidth
      if (width >= 1920) {
        this.size = 4
      } else if (width >= 1200) {
        this.size = 3
      } else {
        this.size = 2
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.index-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  @include scrollBar;

  .dig-box {
    width: 20%;
    padding: 10px;
  }

  .card-box {
    border-radius: 10px;
    background: #FFF;
    padding: 5px 15px;
    margin-bottom: 10px;
    box-sizing: border-box;

    &:last-child {
      margin-bottom: 0;
    }

    .card-title-box {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .t-title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
      }

      .t-more {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 300;
        cursor: pointer;
      }
    }

    // 16:9 图片
    .r-container {
      position: relative;
      min-height: 0;
      padding-bottom: 56.25%;
      //background-color: #eee;

      .img-box {
        position: absolute;
        //top: 0;
        //left: 0;
        //right: 0;
        //bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 5px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    // 3:4 图片
    .r-container2 {
      position: relative;
      min-height: 0;
      padding-bottom: 133.33%;
      //background-color: #eee;

      .img-box {
        position: absolute;
        //top: 0;
        //left: 0;
        //right: 0;
        //bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 5px;
        overflow: hidden;
        .ronghe {
          position: absolute;
          top: 0;
          right: 0;
          width: 50px;
          height: 50px;
        }
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .course_title {
      color: #000;
      font-size: var(--font-size-L);
      margin: 10px 0;
      .ronghe-title {
        display: inline-block;
        background: linear-gradient(90deg, #00C6FB 0%, #005BEA 100%);
        color: #fff;
        font-size: var(--font-size-M);
        padding: 3px 2px;
        border-radius: 4px;
      }
    }

    .course_des {
      color: #000;
      font-size: var(--font-size-M);
      font-weight: 300;
      margin-bottom: 10px;
    }

  }

  .empty-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;

    .empty {
      width: 126px;
      height: 126px;
      margin-bottom: 10px;
    }

    .empty-text {
      font-weight: 400;
      font-size: 14px;
      color: #8C8C8C;
    }
  }
}
</style>
