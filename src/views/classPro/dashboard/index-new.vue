<template>
  <!-- 作废 保留避免改回原来的 -->
  <div class="home-web">
    <div class="wrap flex">
      <!-- left -->
      <div class="left">
        <div class="content">
          <div v-if="school" class="school">
            <div class="school-name">{{ school.name }}</div>
            <div class="trigle-three"></div>
          </div>
          <div class="word">
            <img class="avatar" :src="avatar || DefaultAvatar" alt="头像" />
            <div class="speech">{{ welcomeWord }}
              <template v-if="name && name !== ''">，{{ name }}</template>
              <template v-else-if="mobile && mobile !== ''">，{{ mobile }}</template>
            </div>
          </div>
          <div v-if="assistantInfo" class="flex justify-center items-center">
            <div class="info-box flex flex-col items-center" style="cursor:pointer" @click="handleClick('0')">
              <div class="info-num">{{ assistantInfo.schoolCourseNum }}</div>
              <div class="info-name">学校课程</div>
            </div>
            <div class="line">
              <div v-for="item in 4" :key="item" class="line-item"></div>
            </div>
            <div class="info-box flex flex-col items-center">
              <div class="info-num">{{ assistantInfo.courseNum }}</div>
              <div class="info-name">我的课程</div>
            </div>
            <div class="line">
              <div v-for="item in 4" :key="item" class="line-item"></div>
            </div>
            <div class="info-box flex flex-col items-center" style="cursor:pointer" @click="handleClick('4')">
              <div class="info-num">{{ assistantInfo.classNum }}</div>
              <div class="info-name">班级管理</div>
            </div>
          </div>
          <div class="guide-box">
            <img class="icon-guide" :src="iconGuide" alt="引导图标" />
            <div class="home-title">双师AI课堂开课指引</div>
            <el-timeline style="padding-inline-start:0px">
              <el-timeline-item>
                <div class="flex justify-between items-center">
                  <div class="guide-intro">激活学校课程</div>
                  <div class="classpro-btn" @click="handleClick('1')">激活课程</div>
                  <!-- 功能作废 -->
                  <!-- <div class="guide-intro">{{ !school ? '输激活码获取课程' : '查看学校课程' }}</div>
                  <div v-if="!school" class="classpro-btn" @click="handleClick('1')">激活课程</div>
                  <div v-else class="classpro-btn" @click="handleClick('0')">学校课程</div> -->
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <div class="flex justify-between items-center">
                  <div class="guide-intro">创建班级</div>
                  <div class="classpro-btn" @click="handleClick('2')">添加班级</div>
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <div class="flex justify-between items-center">
                  <div class="guide-intro">添加班级课程</div>
                  <div class="classpro-btn" @click="handleClick('3')">添加课程</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-if="showHelp" class="help-box flex items-center" @click="handleHelp">
            <img src="../../../assets/images/dashboard/help.svg" />
            帮助指南
          </div>
        </div>
      </div>
      <!-- right -->
      <div class="right">
        <div class="top-part">
          <div class="classroom-container classroom-left" @click="toClass(0)">
            <div class="classroom-container-in">
              <div>
                <div class="classroom-title">双师AI课堂</div>
                <div class="classroom-des flex">虚拟教师<div style="color:#1F66FF">AI教学</div></div>
              </div>
              <img class="classroom-icon" src="../../../assets/images/dashboard/ai-icon.png" />
            </div>
          </div>
          <div
            class="classroom-container classroom-center"
            @click="toClass(1)"
          >
            <div class="classroom-container-in">
              <div>
                <div class="classroom-title">空中课堂</div>
                <div class="classroom-des flex">线上线下<div style="color:#1F66FF">双师教学</div></div>
              </div>
              <img class="classroom-icon" src="../../../assets/images/dashboard/sky-icon.png" />
            </div>
          </div>
          <div class="classroom-container classroom-right" @click="toClass(2)">
            <div class="classroom-container-in">
              <div>
                <div class="classroom-title">专用教室</div>
                <div class="classroom-des flex"><div style="color:#1F66FF">虚拟现实</div>融合教学</div>
              </div>
              <img class="classroom-icon" src="../../../assets/images/dashboard/special-icon.png" />
            </div>
          </div>
        </div>
        <div class="center-part">
          <div class="home-title">今日课表</div>
          <div v-if="scheduleList.length > 0" class="schedule-list grid-cols-5">
            <div
              v-for="(item2) in scheduleList"
              :key="item2.courseName"
              class="pointer"
              :class="{
                'schedule-finish': courseFinished(item2),
                'schedule-live': item2.courseType === 'COURSE' && !courseFinished(item2),
                'schedule-ai': item2.courseType === 'AI_COURSE' && !courseFinished(item2),
              }"
              @click="attendClass(item2)"
            >
              <div class="flex justify-between">
                <div v-if="item2.courseType === 'COURSE'" class="time">{{ formatHHmm(item2.startTime) }}</div>
                <div
                  class="course-type mb-10"
                  :class="[item2.courseType === 'COURSE' ? 'border-blue' : 'border-orange']"
                >{{ item2.courseType === 'COURSE' ? '直播课' : '双师AI课堂' }}</div>
              </div>
              <div class="course-name">{{ item2.courseName || '' }}</div>
              <div class="course-period">{{ item2.courseType === 'COURSE' ? `课时${item2.unitNo} ${item2.liveLessonInfo.name}` : '' }}</div>
              <div class="course-class" :class="[item2.courseType === 'COURSE' ? 'color-blue' : 'color-orange']">班级：{{ (item2.lessonStudent && item2.lessonStudent.name) || '' }}</div>
              <div
                class="course-btn"
                :class="{
                  'btn-blue': item2.liveLessonInfo && item2.liveLessonInfo.lessonStatus === 'Opening',
                  'btn-orange': !courseFinished(item2) && item2.courseType === 'AI_COURSE',
                  'btn-grey': +currentTime < +item2.startTime && item2.courseType === 'COURSE'
                }"
              >{{ courseFinished(item2)
                ? '已完成' : ((item2.liveLessonInfo && item2.liveLessonInfo.lessonStatus === 'Opening')
                  || item2.courseType === 'AI_COURSE') ? '上课' : '未开始' }}</div>
            </div>
            <div v-for="index in 4 - scheduleList.length" :key="'empty'+ index" class=""></div>
            <div class="calendar-box flex flex-col justify-center items-center">
              <img class="calendar" :src="calendar" alt="日历" />
              <div class="calendar-text">课程日历</div>
              <div class="classpro-btn" @click="toCalendar">查看更多</div>
            </div>
          </div>
          <div v-else class="flex items-center">
            <div class="empty-schedule" style="flex: 1">
              <img src="@/assets/images/empty4.png" alt="" />
              <div class="hint">今日暂无排课哦～</div>
            </div>
            <div class="calendar-box flex flex-col justify-center items-center" style="width: 20%">
              <img class="calendar" :src="calendar" alt="日历" />
              <div class="calendar-text">课程日历</div>
              <div class="classpro-btn" @click="toCalendar">查看更多</div>
            </div>
          </div>
        </div>

        <div
          v-loading="showLoading"
          class="bottom-part classpro-loadding"
          element-loading-text="加载中"
          element-loading-spinner=""
          element-loading-background="#fff"
        >
          <div class="list-title mb-20">
            <div class="home-title mr-20">我的课程</div>
            <div v-if="aiCourseList.length > 0" class="section-title" :class="{'section-title-active': showState === 0}" @click="changeShowState(0)">双师AI课堂</div>
            <div v-if="liveCourseList.length > 0" class="section-title" :class="{'section-title-active': showState === 1}" @click="changeShowState(1)">空中课堂</div>
            <div v-if="specialCourseList.length > 0" class="section-title" :class="{'section-title-active': showState === 2}" @click="changeShowState(2)">专用教室</div>
          </div>
          <template v-if="!isEmpty">
            <div class="show-list">
              <!-- ai课堂列表 -->
              <template v-if="showState === 0">
                <div v-for="item in aiCourseList" :key="`${item.id}-${item.courseId}`" class="course-item">
                  <div class="mb-10 lab-img">
                    <img :src="item.aicourse.coverUrl || defaultImage" :alt="item.name || ''" @click="toDetail(item)" />
                    <div v-if="item.id === 0" class="label-tiyan">体验课</div>
                  </div>
                  <div class="flex mb-5">
                    <div class="course-name" @click="toDetail(item)">{{ item.aicourse.title || '' }}</div>
                    <el-popover
                      placement="bottom"
                      width="100"
                      trigger="click"
                      :popper-class="'home-course-pop'"
                    >
                      <div class="pop-text mb-20" @click="handleEditCourse(item)">编辑</div>
                      <div class="pop-text" @click="handleRemove(item)">删除</div>
                      <div v-if="item.id !== 0" slot="reference" style="width:20px; height: 20px; text-align: end;">
                        <img class="course-more" :src="iconMore" alt="" />
                      </div>
                    </el-popover>
                  </div>
                  <div class="class-name mb-5" @click="toDetail(item)">上课班级：{{ (item.userClass && item.userClass.name) || '' }}</div>
                  <div class="class-time" @click="toDetail(item)">计划授课：{{ formatPlan(item) }}</div>
                  <div
                    class="label"
                    :class="{
                      'label-finish': item.finishedProgress === item.total,
                      'label-onGoing': item.finishedProgress > 0 && item.finishedProgress < item.total,
                      'label-coming': item.finishedProgress === 0,
                    }"
                    @click="toDetail(item)"
                  >{{ item.finishedProgress === 0 ? '未开始' : item.finishedProgress === item.total ? '已完成' : '进行中' }}</div>
                  <!-- <div class="course-operation">
                    <div class="btn bg-blue">备课</div>
                    <div class="btn bg-orange" @click="toDetail(item)">上课</div>
                  </div> -->
                </div>
              </template>
              <!-- 空中课堂列表 -->
              <template v-else-if="showState === 1">
                <!-- <div v-for="item in liveCourseList" :key="item.id" class="course-item" @click="toDetail(item)">
                  <img :src="item.course.coverUrl || defaultImage" :alt="item.name || ''" />
                  <span>{{ item.course.name || '' }}</span>
                </div> -->
                <div v-for="item in liveCourseList" :key="item.id" class="course-item" @click="toDetail(item)">
                  <img class="mb-10" :src="item.course.coverUrl || defaultImage" :alt="item.name || ''" />
                  <div class="flex mb-5">
                    <div class="course-name mr-5">{{ item.course.name || '' }}</div>
                    <!-- 空中课堂没有该功能 -->
                    <!-- <el-popover
                      placement="bottom"
                      width="100"
                      trigger="hover"
                      :popper-class="'home-course-pop'"
                    >
                      <div class="pop-text mb-20">编辑</div>
                      <div class="pop-text">删除</div>
                      <img slot="reference" class="course-more" :src="iconMore" alt="" />
                    </el-popover> -->
                  </div>
                  <div
                    v-if="item.userClass"
                    class="class-name mb-5"
                  >上课班级：{{ item.userClass.name || '' }}</div>
                  <div
                    class="label"
                    :class="{
                      'label-finish': item.finishedProgress === item.total,
                      'label-onGoing': item.finishedProgress > 0 && item.finishedProgress < item.total,
                      'label-coming': item.finishedProgress === 0,
                    }"
                  >{{ item.finishedProgress === 0 ? '未开始' : item.finishedProgress === item.total ? '已完成' : '进行中' }}</div>
                </div>
              </template>
              <!-- 专用教室列表 -->
              <template v-else>
                <div v-for="item in specialCourseList" :key="item.id" class="course-item" @click="toDetail(item)">
                  <img :src="item.coursecomm.cover || defaultImage" :alt="item.name || ''" />
                  <span>{{ item.coursecomm.title || '' }}</span>
                </div>
              </template>
            </div>
          </template>
          <template v-else>
            <div v-show="!showLoading" class="ai-list-none">
              <img src="../../../assets/images/empty.png" alt="ai课堂为空" />
              <div class="hint hint-padding">暂时没有课程哦～</div>
              <!-- <div class="hint">点击<div class="hint-blue" @click="openExchange">课程兑换</div>去激活课程吧！</div> -->
            </div>
          </template>
        </div>
      </div>
      <!-- 课程兑换dialog -->
      <exchange-dialog class="classpro-dialog" :dialog-visible="dialogExchange" @closeExchange="closeExchange" />

      <NormalDialog
        v-if="editCourseShow"
        width="550px"
        :title="'编辑授课'"
        :dialog-visible="editCourseShow"
        :is-center="true"
        @closeDialog="
          editCourseShow = false
          clearItem()
        "
      >
        <div class="w flex-col">
          <div class="flex items-start" style="padding-top: 10px">
            <div>计划授课：</div>
            <div class="flex flex-col" style="min-height: 40px; flex: 1">
              <el-switch
                v-model="hasWeeks"
                active-color="#3479FF"
                inactive-color="#e9eef5"
              />
              <div v-if="hasWeeks" style="padding-top: 20px;">
                <el-checkbox-group v-model="weeksList">
                  <el-checkbox label="每周一" />
                  <el-checkbox label="每周二" />
                  <el-checkbox label="每周三" />
                  <el-checkbox label="每周四" />
                  <el-checkbox label="每周五" />
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="edu-btn" @click="editCourse">确定</div>
        </template>
      </NormalDialog>

      <ComponentDialog
        :width="'400px'"
        :title="'删除'"
        :dialog-visible="removeVisible"
        :is-center="true"
        @closeDialog="removeVisible = false; clearItem()"
      >
        <div class="flex flex-col w items-center">
          <div style="margin-bottom: 20px;font-size: 14px;">确认删除该课程吗？</div>
          <div class="flex justify-around w">
            <div class="edu-btn-opacity" @click="removeVisible = false; clearItem()">放弃</div>
            <div class="edu-btn" @click="remove">确定删除</div>
          </div>
        </div>
      </ComponentDialog>
    </div>
  </div>
</template>

<script>
import ComponentDialog from '@/components/classPro/ComponentDialog'
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import DefaultAvatar from '@/assets/images/profile.png'
import { mapGetters } from 'vuex'
import defaultImage from '../../../assets/images/default-cover.jpg'
import iconGuide from '../../../assets/images/home/<USER>'
import calendar from '../../../assets/images/home/<USER>'
import iconMore from '../../../assets/images/home/<USER>'
import ExchangeDialog from '../../../layout/classPro/components/exchangeDialog.vue'
import { setChildName, setChildId, setChildToken, removeChildName, removeChildId } from '@/utils/auth'
import { getStuCourseList, getLessonScheduleList } from '@/api/lesson-api.js'
import { getAiConfig } from '@/api/dictionary-api.js'
import { isElectron } from '@/utils/validate.js'
import { getToken } from '@/utils/auth.js'
import localStore from '@/utils/local-storage.js'
import { throttle } from '@/utils/index'
import { switchAccount, getAssistantInfo } from '@/api/user-api'
import { formatHHmm } from '@/utils/date.js'
import {
  updateStudentCoursePlan,
  removeStudentCourse
} from '@/api/educational-api.js'
import { debounce } from '@/utils/index'
export default {
  components: {
    ExchangeDialog,
    ComponentDialog,
    NormalDialog
  },
  data () {
    return {
      DefaultAvatar,
      defaultImage,
      isElectron,
      iconGuide,
      calendar,
      iconMore,
      formatHHmm,
      dialogExchange: false,
      aiCourseList: [],
      specialCourseList: [],
      liveCourseList: [],
      aiSite: null,
      aiSwiperPreShow: false,
      aiSwiperNextShow: false,
      specialSwiperPreShow: false,
      specialSwiperNextShow: false,
      liveSwiperPreShow: false,
      liveSwiperNextShow: false,
      showLoading: true,
      guideProgress: 4,
      showState: 0,
      assistantInfo: undefined,
      scheduleList: [],
      currentTime: parseInt(new Date().getTime()),
      removeVisible: false,
      editCourseShow: false,
      hasWeeks: false,
      weeksList: [],
      studentCourseId: '',
      removeId: '',
      showHelp: false,
      canGoToClass: true
    }
  },
  computed: {
    welcomeWord () {
      const h = new Date().getHours()
      const word = `${
        h <= 12
          ? '上午好'
          : (h > 12) && (h < 18)
            ? '下午好'
            : '晚上好'
      }`
      return word
    },
    isEmpty () {
      if (this.aiCourseList.length > 0) return false
      if (this.liveCourseList.length > 0) return false
      if (this.specialCourseList.length > 0) return false
      return true
    },
    ...mapGetters([
      'id',
      'name',
      'school',
      'mobile',
      'avatar'
    ])
  },
  created () {
    this._getStuCourseList()
    this._getAiConfig()
    this._getAssistantInfo()
    this._getLessonScheduleList()
    this._getHelpConfig()
  },
  mounted () {
    this.guideProgress = localStore.read('guideProgress')
    if ((this.guideProgress === null || this.guideProgress === 1) && this.$store.getters.userRelations.length < 2) {
      this.guideProgress = 2
    }
    this.$bus.$on('getStuCourseList', () => {
      this._getStuCourseList()
    })
    this.$bus.$on('setHomeGuideProgress', (progress) => {
      this.setHomeGuideProgress(progress)
    })
  },
  destroyed () {
    this.$bus.$off('getStuCourseList')
    this.$bus.$off('setHomeGuideProgress')
  },
  methods: {
    openExchange () {
      this.dialogExchange = true
    },
    closeExchange () {
      this.dialogExchange = false
    },
    // 绑定学校1，添加班级2,添加课程3
    handleClick (type) {
      window.localStorage.setItem('eduType', type)
      this.$router.push({ path: '/educational' })
    },
    handleEditCourse (row) {
      this.hasWeeks = false
      this.studentCourseId = row.id
      const arr = []
      if (row.weeklyPlan) {
        this.hasWeeks = true
        const srtObj = {
          1: '每周一',
          2: '每周二',
          3: '每周三',
          4: '每周四',
          5: '每周五',
          6: '每周六',
          7: '每周日'
        }
        for (let i = 0; i < row.weeklyPlan.length; i++) {
          arr.push(srtObj[row.weeklyPlan[i].weekday])
        }
      }
      this.weeksList = arr
      this.editCourseShow = true
    },
    editCourse: debounce(async function () {
      const obj = {
        studentCourseId: this.studentCourseId
      }
      if (this.hasWeeks) {
        if (this.weeksList.length === 0) {
          this.$message.error('有内容未填写')
          return
        }
        const srtObj = {
          '每周一': 1,
          '每周二': 2,
          '每周三': 3,
          '每周四': 4,
          '每周五': 5,
          '每周六': 6,
          '每周日': 7
        }
        const arr = []
        for (let i = 0; i < this.weeksList.length; i++) {
          arr.push(srtObj[this.weeksList[i]])
        }
        obj.weekdays = arr.join(',')
      }
      await updateStudentCoursePlan(obj)
      this._getStuCourseList()
      this.clearItem()
      this.editCourseShow = false
    }, 3000, true),
    handleRemove (row) {
      this.removeId = row.id
      this.removeVisible = true
    },
    remove: debounce(async function () {
      try {
        await removeStudentCourse({ studentCourseId: this.removeId })
        this.$message.success('删除成功')
        this._getStuCourseList()
        this.removeVisible = false
      } catch (error) {
        this.removeVisible = false
        this.clearItem()
      }
    }, 3000, true),
    clearItem () {
      this.hasWeeks = false
      this.weeksList = []
      this.studentCourseId = ''
      this.removeId = ''
    },
    // 获取课程列表
    async _getStuCourseList () {
      // const params = { 'assistantUserId': this.$store.getters.id, 'studentCourseListType': 'ASSISTANT' }
      const params = { 'studentCourseListType': 'ASSISTANT' }
      getStuCourseList(params, false)
        .then(response => {
          this.showLoading = false
          if (response.data !== null && response.data.length > 0) {
            /*  COURSE 直播课程包
                COURSE_GOODS 小班课课程包或者名额
                VIDEO_GOODS 录播课课程包
                AI_COURSE AI课课程包
                ENERGIZE_COURSE 赋能课程包 */
            this.aiCourseList = response.data.filter(item => item.courseType === 'AI_COURSE')
            this.specialCourseList = response.data.filter(item => item.courseType === 'ENERGIZE_COURSE')
            this.liveCourseList = response.data.filter(item => item.courseType === 'COURSE')
            if (this.aiCourseList.length > 0) {
              this.showState = 0
              return
            }
            if (this.liveCourseList.length > 0) {
              this.showState = 1
              return
            }
            if (this.specialCourseList.length > 0) {
              this.showState = 2
              return
            }
            // dom渲染完毕后执行以下操作，否则会取到空
            // this.$nextTick(function () {
            //   this.showSwiperButton()
            // })
          }
        })
    },
    // 获取ai课地址
    async _getAiConfig () {
      getAiConfig({ configType: 'AI_PROJECT_DNS' })
        .then(response => {
          if (response != null) {
            // this.aiSite = 'http://localhost:8080'
            this.aiSite = response.data[0].keyValue || null
            if (this.aiSite.substring(this.aiSite.length - 1, this.aiSite.length) === '/') {
              this.aiSite = this.aiSite.substring(0, this.aiSite.length - 1)
            }
          }
        })
    },
    // 去详情页面
    async toDetail (item) {
      const aiSite = this.aiSite
      const that = this
      switch (item.courseType) {
        case 'COURSE':
          // window.open(this.aiSite + `/live/${item.courseId}/${item.id}?token=${token}`, '_blank')
          if (item.studentId === +this.$store.getters.id) {
            removeChildName()
            removeChildId()
            setChildToken(getToken())
          } else {
            var param = {
              'touserId': item.studentId,
              'parentUserId': this.$store.getters.id
            }
            await switchAccount(param)
              .then(response => {
                const list = this.$store.getters.userRelations.filter(
                  relation => +relation.toUserId === +item.studentId
                )
                setChildName(list[0].toUserDisplayName)
                setChildId(item.studentId)
                setChildToken('Bearer ' + response.data.access_token)
              })
          }
          this.$router.push(`/classpro/skyClass/allCourse/${item.id}`)
          break
        case 'ENERGIZE_COURSE':
          this.$router.push(`/classpro/classroom/${item.courseId}`)
          break
        case 'AI_COURSE':
          // window.open(this.aiSite + `/aiPackage/${item.courseId}/${item.id}?token=${token}`, '_blank')
          throttle(function () {
            const id = that.$store.getters.id
            if (item.userClass && item.userClass.token) {
              var token = item.userClass.token
              window.open(aiSite + `/aiPackage/${item.courseId}/${item.id}/${id}?token=Bearer ${token}&assistantId=${id}&opt=2`, '_blank')
            } else if (item.id === 0) {
              // getStudentCourseList 中id为0 则为体验课
              const token = getToken()
              window.open(aiSite + `/aiPackage/${item.courseId}/${item.id}/${id}?token=Bearer ${token}&assistantId=${id}&opt=2`, '_blank')
            }
            // var token = getToken()
            // window.open(aiSite + `/aiPackage/${item.courseId}/${item.id}?token=${token}`, '_blank')
          }, 3000)
          break
      }
    },
    // 去不同的课堂
    toClass (type) {
      if (!this.aiSite) return
      const aiSite = this.aiSite
      const that = this
      switch (type) {
        case 0 :
          throttle(function () {
            const token = getToken()
            const id = that.$store.getters.id
            window.open(aiSite + `/aiInteract?token=${token}&assistantId=${id}`, '_blank')
          }, 3000)
          break
        case 1 :
          this.$router.push({ path: '/classpro/skyClass' })
          break
        case 2 :
          this.$router.push({ path: '/classpro/special' })
          break
      }
    },
    setHomeGuideProgress (progress) {
      localStore.save('guideProgress', progress)
      this.guideProgress = progress
    },
    changeShowState (state) {
      if (this.showState === state) return
      this.showState = state
    },
    //  去日历叶敏啊
    toCalendar () {
      this.$router.push({ path: '/classpro/calendar' })
    },
    //  获取助教带班信息
    _getAssistantInfo () {
      getAssistantInfo().then(response => {
        this.assistantInfo = response.data
      })
    },
    //  获取今日课表
    _getLessonScheduleList () {
      const params = {
        'listType': 'TODAY'
      }
      getLessonScheduleList(params).then(response => {
        const list = response.data
        if (list.length > 4) {
          this.scheduleList = list.slice(0, 4)
        } else {
          this.scheduleList = response.data
        }
      })
    },
    //  课程是否完成
    courseFinished (item) {
      return (
        item.courseType === 'COURSE' &&
        (item.liveLessonInfo.lessonStatus === 'Completed' ||
        item.liveLessonInfo.lessonStatus === 'UnderReview' ||
        item.liveLessonInfo.lessonStatus === 'Unqualified')
      ) ||
        (
          item.courseType === 'AI_COURSE' && item.studentsCourse.total === item.studentsCourse.finishedProgress
        )
    },
    //  去上课
    async attendClass (item) {
      const aiSite = this.aiSite
      const that = this
      switch (item.courseType) {
        case 'COURSE':
          // if (item.liveLessonInfo.lessonInfo2.roomUrl && !this.courseFinished(item)) {
          //   window.open(item.liveLessonInfo.lessonInfo2.roomUrl, '_blank')
          // }
          if (!this.canGoToClass) {
            return
          }
          if (item.liveLessonInfo.lessonInfo2.roomUrl) {
            this.canGoToClass = false
            setTimeout(() => {
              this.canGoToClass = true
            }, 8000)
            this.$message.warning({
              message: '正在进入教室请勿重复点击',
              duration: 8000
            })
            window.open(item.liveLessonInfo.lessonInfo2.roomUrl, '_blank')
          }
          break
        case 'ENERGIZE_COURSE':
          this.$router.push(`/classpro/classroom/${item.courseId}`)
          break
        case 'AI_COURSE':
          // throttle(function () {
          //   if (item.lessonStudent && item.lessonStudent.token) {
          //     var token = item.lessonStudent.token
          //     window.open(aiSite + `/aiPackage/${item.studentsCourse.courseId}/${item.studentsCourse.id}?token=Bearer ${token}`, '_blank')
          //   }
          // }, 3000)

          // if (!this.courseFinished(item)) {
          //   const id = that.$store.getters.id
          //   throttle(function () {
          //     if (item.lessonStudent && item.lessonStudent.token) {
          //       var token = item.lessonStudent.token
          //       window.open(aiSite + `/aiPackage/${item.studentsCourse.courseId}/${item.studentsCourse.id}?token=Bearer ${token}&assistantId=${id}`, '_blank')
          //     }
          //   }, 3000)
          // }
          throttle(function () {
            const id = that.$store.getters.id
            if (item.lessonStudent && item.lessonStudent.token) {
              var token = item.lessonStudent.token
              window.open(aiSite + `/aiPackage/${item.studentsCourse.courseId}/${item.studentsCourse.id}/${id}?token=Bearer ${token}&assistantId=${id}`, '_blank')
            }
          }, 3000)
          break
      }
    },
    //  转换为计划授课
    formatPlan (item) {
      const formWeek = {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日'
      }
      if (item.weeklyPlan) {
        let plan = ''
        for (var i = 0; i < item.weeklyPlan.length; i++) {
          plan += i === 0 ? formWeek[+item.weeklyPlan[i].weekday] : `、${formWeek[+item.weeklyPlan[i].weekday]}`
        }
        return plan
      } else {
        return '暂无计划'
      }
    },
    handleHelp () {
      const url = `${window.location.origin}/#/help`
      window.open(url, '_blank', 'width=600,height=800,left=200,top=100,menubar=0,scrollbars=1,resizable=1,status=1,titlebar=0,toolbar=0,location=1')
    },
    async _getHelpConfig () {
      const prams = {
        'configType': 'AI_GUIDE_PDF'
      }
      const { data } = await getAiConfig(prams)
      if (data && data.length > 0) {
        this.showHelp = true
      } else {
        this.showHelp = false
      }
    }
  }
}
</script>

<style scoped lang="scss" c>
@import '@/styles/mixin';

.home-web {
  background: #F8FAFF;
  height: calc(100% - 62px);
  overflow-x: hidden;
  overflow-y: hidden;
  padding: 20px 0 0 40px;

  .left {
    height: 100%;
    width: 305px;
    position: relative;

    .school {
      width: 280px;
      min-width: 127px;
      height: 40px;
      left: -4px;
      top: 30px;
      position: absolute;

      .school-name {
        position: absolute;
        top: 3px;
        background: #FFFFFF;
        border-radius: 0 40px 40px 0;
        max-width: 280px;
        font-weight: 500;
        padding: 8px 11px 8px 15px;
        color: #0B0B0B;
        text-align: start;
        word-break: break-all;
        font-size: 14px;
        @include ellipses(2);
      }

      .trigle-three {
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        border-bottom: 4px solid rgba(109, 153, 250, 1);
        border-left: 4px solid transparent;
      }
    }

    .word {
      padding-top: 146px;
      text-align: center;
      font-weight: 500;
      color: #161515;
      letter-spacing: 0;
    }

    .avatar {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 50%;
      margin-bottom: 15px;
    }

    .speech {
      margin-bottom: 22px;
      font-size: 16px;
    }

    .info-num {
      line-height: 25px;
      font-weight: 500;
      font-size: 18px;
      color: #3479FF;
      letter-spacing: 0;
      margin-bottom: 10px;
    }

    .info-name {
      font-weight: 400;
      font-size: 14px;
      color: #161515;
      letter-spacing: 0;
    }

    .line {
      margin: 0 20px;
      height: 22px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .line-item {
        width: 1px;
        height: 4px;
        background: #9FBCF3;
      }
    }

    .guide-box {
      width: 285px;
      height: 390px;
      margin: 40px auto 0;
      background: url('../../../assets/images/home/<USER>') center center no-repeat;
      background-size: contain;
      position: relative;
      padding: 30px 10px 0;
    }

    .icon-guide {
      width: 40px;
      height: 48px;
      object-fit: contain;
      position: absolute;
      transform: translate(-50%, 0);
      left: 50%;
      top: -10px;
    }

    .guide-intro {
      width: 140px;
      line-height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #1C1B1A;
      letter-spacing: 0;
    }

    .classpro-btn, .classpro-btn-disable {
      width: 70px;
      height: 30px;
      font-weight: 400;
      font-size: 12px;
      color: #FFFFFF;
      letter-spacing: 0;
    }

    .home-title {
      margin-bottom: 25px;
      font-size: 14px !important;
    }
  }

  .wrap {
    width: 100%;
    height: 100%;

    .left {
      padding-bottom: 20px;
      .content {
        height: 100%;
        border-radius: 15px;
        background: #cae0f9;
        position: relative;
      }
    }

    .home-title {
        font-weight: 500;
        font-size: 16px;
        color: #1C1B1A;
        letter-spacing: 0;
        line-height: 22px;
        position: relative;
        padding-left: 7px;

        &::before {
          content: ' ';
          position: absolute;
          left: 0;
          transform: translate(0, -50%);
          top: 50%;
          height: 100%;
          width: 2px;
          height: 14px;
          background: #3479FF;
          border-radius: 1px;
        }
    }

    .help-box {
      position: absolute;
      bottom: 20px;
      left: 15px;
      // padding: 15px 0 0 0;
      color: #4F4F4F;
      font-size: 16px;
      cursor: pointer;
      img {
        width: 25px;
        height: 25px;
        margin-right: 5px;
      }
    }

    .right {
      flex: 1;
      height: 100%;
      min-width: 50px;
      padding-left: 19px;
      overflow: scroll;
      overflow-x: hidden;
      padding-right: 20px;
      @include scrollBar;

      .top-part {
        height: 91px;
        min-height: 29px;
        position: relative;
        margin-bottom: 20px;

        .classroom-container {
          position: absolute;
          width: 32%;
          height: 100%;
          box-shadow: 0px 3px 5px 2px rgba(62, 89, 253, 0.08);
          border-radius: 15px;
          padding: 1px;
          cursor: pointer;

          .classroom-container-in {
            width: 100%;
            height: 100%;
            background: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 15px;
            padding: 0 20px;
          }

          .classroom-title {
            font-size: 21px;
            color: #0B0B0B;
            line-height: 27px;
            margin-bottom: 6px;
            font-weight: normal;
          }

          .classroom-des {
            font-size:14px;
            font-weight: 400;
            color: #595959;
            line-height: 20px;
          }

          .classroom-icon {
            width: 80px;
            height: 80px;
            object-fit: contain;
          }
        }

        .classroom-left {
          left: 0;
        }

        .classroom-center {
          transform: translate(-50%,0);
          left: 50%;
        }

        .classroom-right {
          right: 0;
        }

        .classroom-right-top {
          z-index: 12;
        }

        .guide-container {
          position: absolute;
          width: 42%;
          height: 120%;
          background: #F8FAFF 100%;
          z-index: 11;
          border-radius: 100px;
          right: 30%;
          top: -10%;
        }

        .classroom-container:hover {
          background-image: -webkit-linear-gradient(130deg, rgba(150, 235, 255, 1), rgba(31, 102, 255, 1));

          .classroom-icon {
            position: absolute;
            top: -9px;
            right: 20px;
          }
        }
      }

      .center-part {
        width: 100%;
        height: 235px;
        background: #FFFFFF;
        border-radius: 10px;
        margin-bottom: 20px;
        padding: 10px 20px;

        .home-title {
          margin-bottom: 20px;
        }

        .schedule-list {
          display: grid;
          gap: 20px;
        }

        .schedule-finish,
        .schedule-ai,
        .schedule-live {
          height: 164px;
          border-radius: 5px;
          padding: 10px;
          position: relative;

          .time {
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 0;
          }

          .course-type {
            border-radius: 4px;
            font-weight: 400;
            font-size: 12px;
            letter-spacing: 0;
            line-height: 17px;
            padding: 0 6px;
            margin-left: auto;
          }

          .course-name {
            line-height: 20px;
            font-weight: 500;
            font-size: 14px;
            color: #0F0F0F;
            letter-spacing: 0;
            margin-bottom: 5px;
            @include ellipses(2);
          }

          .course-period,
          .course-class {
            line-height: 20px;
            font-weight: 400;
            font-size: 14px;
            letter-spacing: 0;
            margin-bottom: 3px;
          }

          .course-btn {
            width: 55px;
            height: 20px;
            border-radius: 10px;
            font-weight: 400;
            font-size: 14px;
            letter-spacing: 0;
            line-height: 20px;
            text-align: center;
            position: absolute;
            right: 10px;
            bottom: 10px;
            cursor: pointer;
          }
        }

        .schedule-finish {
          background: #F2F2F2;
        }

        .schedule-live {
          background: #f5faff;
        }

        .schedule-ai {
          background: #fefaf6;
        }

        .border-orange {
          border: 1px solid #FF9000;
          color: #FF9000;
        }

        .border-blue {
          border: 1px solid #3479FF;
          color: #3479FF;
        }

        .color-grey {
          color: #7F8182;
        }

        .color-blue {
          color: #3479FF;
        }

        .color-orange {
          color: #FF9000;
        }
        .btn-blue {
            background: #3479FF;
            color: #FFFFFF;
            cursor: pointer;
        }

        .btn-orange {
            background: #FF9000;
            color: #FFFFFF;
            cursor: pointer;
        }

        .btn-grey {
            background: #E1E1E1;
            color: #818181;
        }

        .calendar-box {
          background: #f5faff;
          height: 164px;
          border-radius: 5px;
          padding: 10px;
          position: relative;
        }

        .calendar {
          width: 32px;
          height: 31px;
          object-fit: contain;
        }

        .calendar-text {
          line-height: 20px;
          font-weight: 400;
          font-size: 14px;
          color: #1C1B1A;
          letter-spacing: 0;
          text-align: center;
          margin: 5px 0 10px;
        }

        .classpro-btn, .classpro-btn-disable {
          width: 80px;
          height: 20px;
          font-size: 14px;
        }

      }

      .bottom-part {
        width: 100%;
        min-height: calc(100% - 386px);
        background: rgba(255, 255, 255, 1);
        border-radius: 15px;
        box-shadow: 0px 3px 8px 5px rgba(62, 89, 253, 0.05);
        padding: 15px;
        position: relative;
        display: flex;
        flex-direction: column;

        .list-title {
          display: flex;
          align-items: center;
          height: 5%;

          .section-title {
            width: 90px;
            height: 25px;
            border: 1px solid #E2E2E2;
            border-radius: 15px;
            line-height: 25px;
            font-weight: 400;
            font-size: 14px;
            text-align: center;
            color: #8C8C8C;
            cursor: pointer;
            margin-right: 10px;
          }

          .section-title-active {
            background: #DFEBFF;
            border: none;
            color: #3479FF;
          }
        }

        .show-list {
          padding-top: 10px;
          width: 100%;
          height: 100%;
          display: grid;
          overflow-x: hidden;
          @include scrollBar;

          .course-item {
              height: 230px;
              background: #FFFFFF;
              border-radius: 10px;
              position: relative;

              img {
                  width: 100%;
                  height: 106px;
                  border-radius: 10px;
                  object-fit: cover;
                  cursor: pointer;
              }

              .course-name {
                  line-height: 20px;
                  font-weight: 500;
                  font-size: 14px;
                  color: #0B0B0B;
                  flex: 1;
                  cursor: pointer;
                  @include ellipses(2);
              }

              .course-more {
                width: 16px;
                height: 20px;
                object-fit: contain;
                cursor: pointer;
              }

              .class-name {
                line-height: 20px;
                font-weight: 400;
                font-size: 14px;
                color: #3479FF;
                letter-spacing: 0;
                cursor: pointer;
              }

              .class-time {
                line-height: 17px;
                font-weight: 400;
                font-size: 12px;
                color: #383838;
                letter-spacing: 0;
                cursor: pointer;
              }

              .label {
                position: absolute;
                right: 0;
                top: 14px;
                width: 70px;
                height: 24px;
                border-radius: 100px 0 0 100px;
                font-weight: 400;
                font-size: 14px;
                text-align: center;
                line-height: 24px;
                cursor: pointer;
              }
              .lab-img {
                border-radius: 10px;
                position: relative;
                overflow: hidden;
              }
              .label-tiyan {
                position: absolute;
                left: -50px;
                top: -50px;
                background: #D8C08E;
                color: #fff;
                font-weight: 500;
                width: 100px;
                height: 100px;
                transform: rotate(-45deg);
                display: flex;
                align-items: flex-end;
                justify-content: center;
                padding-bottom: 6px;
              }

              .label-finish {
                background: #D6D6D6;
                color: #545454;
              }

              .label-onGoing {
                background: #52C41A;
                color: #FFFFFF;
              }

              .label-coming {
                background: #FF922B;
                color: #FFFFFF;
              }

              .course-operation {
                display: flex;
                position: absolute;
                gap: 10px;
                right: 0;
                bottom: 0;

                .btn {
                  width: 55px;
                  height: 24px;
                  border-radius: 12px;
                  font-weight: 400;
                  font-size: 14px;
                  color: #FFFFFF;
                  letter-spacing: 0;
                  text-align: center;
                  line-height: 24px;
                  cursor: pointer;
                }

                .bg-blue {
                  background: #2673FF;
                }

                .bg-orange {
                  background: #FF922B;
                }
              }
          }
        }

        .ai-list-none {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          justify-content: center;

          img {
            width: 90px;
            height: 90px;
          }

          .hint {
            display: flex;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8C9399;
            line-height: 17px;

            .hint-blue {
              color: rgba(31, 102, 255, 1);
              cursor: pointer;
            }
          }

          .hint-padding {
            padding:8px 0 4px
          }
        }
      }

      .empty-schedule {

        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 102px;
          height: 105px;
          object-fit: contain;
          margin-right: 15px;
        }

        .hint {
          font-weight: 400;
          font-size: 14px;
          color: #161515;
          letter-spacing: 0;
          text-align: center;
        }

      }
    }

    .mb-5 {
      margin-bottom: 5px;
    }
    .mb-10 {
      margin-bottom: 10px;
    }

    .mb-20 {
      margin-bottom: 20px;
    }

    .mr-5 {
      margin-right: 5px;
    }

    .mr-20 {
      margin-right: 20px;
    }

    @media screen and (min-width: 0px) {
      .show-list {
        row-gap: 20px;
        column-gap: 28px;
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }
    }
    @media screen and (min-width: 1281px) {
      .show-list {
        grid-template-columns: repeat(4, minmax(0, 1fr));
        grid-row-gap: 20px;
        grid-column-gap: 38px;
      }
    }
    @media screen and (min-width: 1441px) {
      .show-list {
        grid-row-gap: 20px;
        column-gap: 38px;
        grid-template-columns: repeat(6, minmax(0, 1fr));
      }
    }
  }
}
</style>

<style lang="scss">
.home-web {
  .el-timeline-item__wrapper {
    top: -10px;
  }

  .el-timeline-item__node {
    background: #3479FF;
  }

  .el-timeline-item__tail {
    border-left: 2px solid #3479FF;
  }

  .el-timeline-item {
    padding-bottom: 40px;
  }
}

.home-course-pop {
  .pop-text {
    line-height: 22PX;
    font-weight: 400;
    font-size: 14PX;
    color: rgba(0,0,0,0.90);
    line-height: 22PX;
    cursor: pointer;

    &:hover {
      color: #3479FF;
    }
  }

  .mb-20 {
    margin-bottom: 20PX;
  }
}
</style>
