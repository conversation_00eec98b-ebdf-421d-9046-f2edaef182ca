<template>
  <!-- 作废 保留避免改回原来的 -->
  <div class="home-web flex">
    <!-- left -->
    <div class="left">
      <div class="welcome">
        <img class="bg" src="@/assets/images/dashboard/bg-left.png" alt="" />
        <div v-if="school" class="school">
          <div class="school-name">{{ school.name }}</div>
          <div class="trigle-three"></div>
        </div>
        <div class="word">
          <div class="speech">{{ welcomeWord }}
            <template v-if="name && name !== ''">，{{ name }}!</template>
            <template v-else-if="mobile && mobile !== ''">，{{ mobile }}</template>
          </div>
          <div class="wisdom">学而时习之，不亦说乎</div>
        </div>
      </div>
    </div>
    <!-- right -->
    <div class="right">
      <div class="top-part">
        <div class="classroom-container classroom-left" @click="toClass(0)">
          <div class="classroom-container-in">
            <div>
              <div class="classroom-title">AI课堂</div>
              <div class="classroom-des flex">虚拟教师<div style="color:#1F66FF">AI教学</div></div>
            </div>
            <img class="classroom-icon" src="../../../assets/images/dashboard/ai-icon.png" />
          </div>
        </div>
        <div
          class="classroom-container classroom-center"
          @click="toClass(1)"
        >
          <div class="classroom-container-in">
            <div>
              <div class="classroom-title">空中课堂</div>
              <div class="classroom-des flex">线上线下<div style="color:#1F66FF">双师教学</div></div>
            </div>
            <img class="classroom-icon" src="../../../assets/images/dashboard/sky-icon.png" />
          </div>
        </div>
        <div class="classroom-container classroom-right" @click="toClass(2)">
          <div class="classroom-container-in">
            <div>
              <div class="classroom-title">专用教室</div>
              <div class="classroom-des flex"><div style="color:#1F66FF">虚拟现实</div>融合教学</div>
            </div>
            <img class="classroom-icon" src="../../../assets/images/dashboard/special-icon.png" />
          </div>
        </div>
        <!-- <div v-if="guideProgress == 2" class="guide-container"></div>
        <div v-if="guideProgress == 2" class="guide-des guide-progress-2">
          <div class="guide-des__top">
            <div class="line"></div>
            <div class="circle"></div>
          </div>
          <div class="name">空中课堂</div>
          <div class="explain">直播课更名为空中课堂，点击这里直接进入</div>
          <div class="guide-btns">
            <div class="btn classpro-btn-disable left-btn" @click="setHomeGuideProgress(4)">跳过</div>
            <div class="btn classpro-btn right-btn" @click="setHomeGuideProgress(3)">下一步</div>
          </div>
        </div> -->
      </div>
      <div class="bottom-part">
        <!-- <div class="my-class">我的课程</div>
          <div v-if="guideProgress == 3" class="my-guide-class">我的课程</div>
          <div v-if="guideProgress == 3" class="guide-class-container"></div>
          <div v-if="guideProgress == 3" class="guide-des">
            <div class="guide-des__top">
              <div class="line"></div>
              <div class="circle"></div>
            </div>
            <div class="name">我的课程</div>
            <div class="explain">这里可以选择进入对应的课程包～</div>
            <div class="guide-btns">
              <div class="btn classpro-btn right-btn" @click="setHomeGuideProgress(4)">完成</div>
            </div>
          </div> -->
        <div
          v-loading="showLoading"
          class="list classpro-loadding"
          element-loading-text="加载中"
          element-loading-spinner=""
          element-loading-background="#fff"
        >
          <div class="list-title">
            <div class="list-title__mark"></div>
            <div class="list-title__text">我的课程</div>
            <div v-if="aiCourseList.length > 0" class="section-title" :class="{'section-title-active': showState === 0}" @click="changeShowState(0)">AI课堂</div>
            <div v-if="liveCourseList.length > 0" class="section-title" :class="{'section-title-active': showState === 1}" @click="changeShowState(1)">空中课堂</div>
            <div v-if="specialCourseList.length > 0" class="section-title" :class="{'section-title-active': showState === 2}" @click="changeShowState(2)">专用教室</div>
          </div>
          <template v-if="!isEmpty">
            <!-- ai课堂列表 -->
            <!-- <div v-if="aiCourseList.length > 0" style="height:52%">
                <div class="list-title">
                  <div class="list-title__mark"></div>
                  <div class="list-title__text">AI课堂</div>
                </div>
                <div class="ai-list">
                  <swiper ref="aiSwiper" class="swiper" :options="swiperOptionAi" @slideChange="showSwiperButton">
                    <swiper-slide v-for="item in aiCourseList" :key="item.id" @click.native="toDetail(item)">
                      <div class="label">
                        <div class="circle" :class="[item.finishedProgress === item.total ? 'green' : 'blue']"></div>
                        <div class="label-text">{{ item.finishedProgress === item.total ? '已完成' : '进行中' }}</div>
                      </div>
                      <img :src="item.aicourse === null ? defaultImage : item.aicourse.coverUrl || defaultImage" alt="ai课封面" />
                      <p>{{ item.aicourse === null ? '' : item.aicourse.title || '' }}</p>
                    </swiper-slide>
                  </swiper>
                  <div v-show="aiSwiperPreShow" slot="button-prev" class="swiper-ai-prev"></div>
                  <div v-show="aiSwiperNextShow" slot="button-next" class="swiper-ai-next"></div>
                </div>
              </div> -->
            <!-- 专用教室列表 -->
            <!-- <div v-if="specialCourseList.length > 0" style="height:52%">
                <div class="list-title">
                  <div class="list-title__mark"></div>
                  <div class="list-title__text">专用教室</div>
                </div>
                <div class="special-list">
                  <swiper ref="specialSwiper" class="swiper" :options="swiperOptionSpecial" @slideChange="showSwiperButton">
                    <swiper-slide v-for="item in specialCourseList" :key="item.id" @click.native="toDetail(item)">
                      <img :src="item.coursecomm === null ? defaultImage : item.coursecomm.cover || defaultImage" alt="专用教室封面" />
                      <p>{{ item.coursecomm === null ? '' : item.coursecomm.title || '' }}</p>
                    </swiper-slide>
                  </swiper>
                  <div v-show="specialSwiperPreShow" slot="button-prev" class="swiper-special-prev"></div>
                  <div v-show="specialSwiperNextShow" slot="button-next" class="swiper-special-next"></div>
                </div>
              </div> -->
            <!-- 直播课列表 -->
            <!-- <div v-if="liveCourseList.length > 0" style="height:52%">
                <div class="list-title">
                  <div class="list-title__mark"></div>
                  <div class="list-title__text">空中课堂</div>
                </div>
                <div class="special-list">
                  <swiper ref="liveSwiper" class="swiper" :options="swiperOptionLive" @slideChange="showSwiperButton">
                    <swiper-slide v-for="item in liveCourseList" :key="item.id" @click.native="toDetail(item)">
                      <div class="label">
                        <div class="circle" :class="[item.finishedProgress === item.total ? 'green' : 'blue']"></div>
                        <div class="label-text">{{ item.finishedProgress === item.total ? '已完成' : '进行中' }}</div>
                      </div>
                      <img :src="item.course === null ? defaultImage : item.course.coverUrl || defaultImage" alt="直播课封面" />
                      <p>{{ item.course === null ? '' : item.course.name || '' }}</p>
                    </swiper-slide>
                  </swiper>
                  <div v-show="liveSwiperPreShow" slot="button-prev" class="swiper-live-prev"></div>
                  <div v-show="liveSwiperNextShow" slot="button-next" class="swiper-live-next"></div>
                </div>
              </div> -->
            <div class="show-list">
              <!-- ai课堂列表 -->
              <template v-if="showState === 0">
                <div v-for="item in aiCourseList" :key="item.id" class="course-item" @click="toDetail(item)">
                  <img :src="item.aicourse.coverUrl || defaultImage" :alt="item.name || ''" />
                  <span>{{ item.aicourse.title || '' }}</span>
                </div>
              </template>
              <!-- 空中课堂列表 -->
              <template v-else-if="showState === 1">
                <div v-for="item in liveCourseList" :key="item.id" class="course-item" @click="toDetail(item)">
                  <img :src="item.course.coverUrl || defaultImage" :alt="item.name || ''" />
                  <span>{{ item.course.name || '' }}</span>
                </div>
              </template>
              <!-- 专用教室列表 -->
              <template v-else>
                <div v-for="item in specialCourseList" :key="item.id" class="course-item" @click="toDetail(item)">
                  <img :src="item.coursecomm.cover || defaultImage" :alt="item.name || ''" />
                  <span>{{ item.coursecomm.title || '' }}</span>
                </div>
              </template>
            </div>
          </template>
          <template v-else>
            <div v-show="!showLoading" class="ai-list-none">
              <img src="../../../assets/images/dashboard/no-ai-course.png" alt="ai课堂为空" />
              <div class="hint hint-padding">暂时没有课程哦～</div>
              <div class="hint">点击<div class="hint-blue" @click="openExchange">课程兑换</div>去激活课程吧！</div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <!-- 课程兑换dialog -->
    <exchange-dialog class="classpro-dialog" :dialog-visible="dialogExchange" @closeExchange="closeExchange" />
    <!-- <div v-if="guideProgress === null || guideProgress < 4" class="shadow"></div> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import defaultImage from '../../../assets/images/default-cover.jpg'
// import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import ExchangeDialog from '../../../layout/classPro/components/exchangeDialog.vue'
import { setChildName, setChildId, setChildToken, removeChildName, removeChildId } from '@/utils/auth'
import { getStuCourseList } from '@/api/lesson-api.js'
import { getAiConfig } from '@/api/dictionary-api.js'
import { isElectron } from '@/utils/validate.js'
import { getToken } from '@/utils/auth.js'
import localStore from '@/utils/local-storage.js'
import { throttle } from '@/utils/index'
import { switchAccount } from '@/api/user-api'
export default {
  components: {
    // Swiper,
    // SwiperSlide,
    ExchangeDialog
  },
  data () {
    return {
      defaultImage,
      isElectron,
      dialogExchange: false,
      swiperOptionAi: {
        slidesPerView: 3,
        spaceBetween: 30,
        slidesPerGroup: 3,
        loop: false,
        loopFillGroupWithBlank: true,
        navigation: {
          nextEl: '.swiper-ai-next',
          prevEl: '.swiper-ai-prev'
        }
      },
      swiperOptionSpecial: {
        slidesPerView: 3,
        spaceBetween: 30,
        slidesPerGroup: 3,
        loop: false,
        loopFillGroupWithBlank: true,
        navigation: {
          nextEl: '.swiper-special-next',
          prevEl: '.swiper-special-prev'
        }
      },
      swiperOptionLive: {
        slidesPerView: 3,
        spaceBetween: 30,
        slidesPerGroup: 3,
        loop: false,
        loopFillGroupWithBlank: true,
        navigation: {
          nextEl: '.swiper-live-next',
          prevEl: '.swiper-live-prev'
        }
      },
      aiCourseList: [],
      specialCourseList: [],
      liveCourseList: [],
      aiSite: null,
      aiSwiperPreShow: false,
      aiSwiperNextShow: false,
      specialSwiperPreShow: false,
      specialSwiperNextShow: false,
      liveSwiperPreShow: false,
      liveSwiperNextShow: false,
      showLoading: true,
      guideProgress: 4,
      showState: 0
    }
  },
  computed: {
    welcomeWord () {
      const h = new Date().getHours()
      const word = `${
        h <= 12
          ? '上午好'
          : (h > 12) && (h < 18)
            ? '下午好'
            : '晚上好'
      }`
      return word
    },
    isEmpty () {
      if (this.aiCourseList.length > 0) return false
      if (this.liveCourseList.length > 0) return false
      if (this.specialCourseList.length > 0) return false
      return true
    },
    ...mapGetters([
      'name',
      'school',
      'mobile'
    ])
  },
  created () {
    this._getStuCourseList()
    this._getAiConfig()
  },
  mounted () {
    this.guideProgress = localStore.read('guideProgress')
    if ((this.guideProgress === null || this.guideProgress === 1) && this.$store.getters.userRelations.length < 2) {
      this.guideProgress = 2
    }
    this.$bus.$on('getStuCourseList', () => {
      this._getStuCourseList()
    })
    this.$bus.$on('setHomeGuideProgress', (progress) => {
      this.setHomeGuideProgress(progress)
    })
  },
  destroyed () {
    this.$bus.$off('getStuCourseList')
    this.$bus.$off('setHomeGuideProgress')
  },
  methods: {
    openExchange () {
      this.dialogExchange = true
    },
    closeExchange () {
      this.dialogExchange = false
    },
    // 获取课程列表
    async _getStuCourseList () {
      const params = { 'assistantUserId': this.$store.getters.id, 'studentCourseListType': 'ASSISTANT' }
      getStuCourseList(params, false)
        .then(response => {
          this.showLoading = false
          if (response.data !== null && response.data.length > 0) {
            /*  COURSE 直播课程包
                COURSE_GOODS 小班课课程包或者名额
                VIDEO_GOODS 录播课课程包
                AI_COURSE AI课课程包
                ENERGIZE_COURSE 赋能课程包 */
            this.aiCourseList = response.data.filter(item => item.courseType === 'AI_COURSE')
            this.specialCourseList = response.data.filter(item => item.courseType === 'ENERGIZE_COURSE')
            this.liveCourseList = response.data.filter(item => item.courseType === 'COURSE')
            if (this.aiCourseList.length > 0) {
              this.showState = 0
              return
            }
            if (this.liveCourseList.length > 0) {
              this.showState = 1
              return
            }
            if (this.specialCourseList.length > 0) {
              this.showState = 2
              return
            }
            // dom渲染完毕后执行以下操作，否则会取到空
            // this.$nextTick(function () {
            //   this.showSwiperButton()
            // })
          }
        })
    },
    // 获取ai课地址
    async _getAiConfig () {
      getAiConfig({ configType: 'AI_PROJECT_DNS' })
        .then(response => {
          if (response != null) {
            // this.aiSite = 'http://localhost:8080'
            this.aiSite = response.data[0].keyValue || null
            if (this.aiSite.substring(this.aiSite.length - 1, this.aiSite.length) === '/') {
              this.aiSite = this.aiSite.substring(0, this.aiSite.length - 1)
            }
          }
        })
    },
    // 去详情页面
    async toDetail (item) {
      const aiSite = this.aiSite
      switch (item.courseType) {
        case 'COURSE':
          // window.open(this.aiSite + `/live/${item.courseId}/${item.id}?token=${token}`, '_blank')
          if (item.studentId === +this.$store.getters.id) {
            removeChildName()
            removeChildId()
            setChildToken(getToken())
          } else {
            var param = {
              'touserId': item.studentId,
              'parentUserId': this.$store.getters.id
            }
            await switchAccount(param)
              .then(response => {
                const list = this.$store.getters.userRelations.filter(
                  relation => +relation.toUserId === +item.studentId
                )
                setChildName(list[0].toUserDisplayName)
                setChildId(item.studentId)
                setChildToken('Bearer ' + response.data.access_token)
              })
          }
          this.$router.push(`/classpro/skyClass/allCourse/${item.id}`)
          break
        case 'ENERGIZE_COURSE':
          this.$router.push(`/classpro/classroom/${item.courseId}`)
          break
        case 'AI_COURSE':
          // window.open(this.aiSite + `/aiPackage/${item.courseId}/${item.id}?token=${token}`, '_blank')
          throttle(function () {
            var token = getToken()
            window.open(aiSite + `/aiPackage/${item.courseId}/${item.id}/${this.$store.getters.id}?token=${token}`, '_blank')
          }, 3000)
          break
      }
    },
    // 去不同的课堂
    toClass (type) {
      if (!this.aiSite) return
      const aiSite = this.aiSite
      switch (type) {
        case 0 :
          throttle(function () {
            var token = getToken()
            window.open(aiSite + `/aiInteract?token=${token}`, '_blank')
          }, 3000)
          // var token = getToken()
          // window.open(this.aiSite + `/aiInteract?token=${token}`, '_blank')
          // window.open(`http://localhost:8080/aiInteract?token=${token}`, '_blank')
          // window.location.href = this.aiSite + `http://localhost:8080/aiInteract?token=${token}&url=${window.location.href}`
          break
        case 1 :
          this.$router.push({ path: '/classpro/skyClass' })
          break
        case 2 :
          this.$router.push({ path: '/classpro/special' })
          break
      }
    },
    // 展示swiper的按钮
    showSwiperButton () {
      if (this.aiCourseList.length > 0) {
        this.aiSwiperPreShow = !this.$refs.aiSwiper.$swiper.isBeginning
        this.aiSwiperNextShow = !this.$refs.aiSwiper.$swiper.isEnd
      }
      if (this.specialCourseList.length > 0) {
        this.specialSwiperPreShow = !this.$refs.specialSwiper.$swiper.isBeginning
        this.specialSwiperNextShow = !this.$refs.specialSwiper.$swiper.isEnd
      }
      if (this.liveCourseList.length > 0) {
        this.liveSwiperPreShow = !this.$refs.liveSwiper.$swiper.isBeginning
        this.liveSwiperNextShow = !this.$refs.liveSwiper.$swiper.isEnd
      }
    },
    setHomeGuideProgress (progress) {
      localStore.save('guideProgress', progress)
      this.guideProgress = progress
    },
    changeShowState (state) {
      if (this.showState === state) return
      this.showState = state
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/mixin';
$vw_design_web_width: 1440;
$vw_design_web_height: 900;
@function ui_w($px) {
  @return $px + px;
}
@function ui_h($px) {
  @return $px / $vw_design_web_height * 100vh;
}

.home {
  background: #F8FAFF;
  height: calc(100% - 62px);
  padding: 17px 27px 20px 34px;
  display: flex;

  .left {
    height: 100%;
    width: ui_h(212);
    background: url('../../../assets/images/dashboard/bg-left.png') center center no-repeat;
    background-size: contain;
    box-shadow: 0px 4px 8px 5px rgba(14, 37, 171, 0.01);
    border-radius: 15px;

    .welcome {
      position: relative;
      height: ui_h(420);
      width: ui_h(212);

      .school {
        position: absolute;
        left: ui_h(-4);
        top: 22px;

        .school-name {
          background: #FFFFFF;
          box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.28);
          border-radius: 6px 23px 8px 0px;
          max-width: ui_h(200);
          font-size: ui_h(14);
          font-weight: 500;
          color: #0B0B0B;
          line-height: ui_h(20);
          text-align: center;
          padding: ui_h(6) ui_h(14) ui_h(3) ui_h(12);
          text-align: start;
          word-break: break-all;
          @include ellipses(2)
        }

        .trigle-three {
          width: 0;
          height: 0;
          border-top: ui_h(4) solid rgba(109, 153, 250, 1);
          border-left: ui_h(4) solid transparent;
        }
      }

      .word {
        padding-top: ui_h(82);
        padding-left: ui_h(21);

        .speech {
          font-size: ui_h(18);
          font-weight: 500;
          color: #0B0B0B;
          line-height: ui_h(25);
          word-break: break-all;
        }

        .wisdom {
          margin-top: ui_h(15);
          font-size: ui_h(14);
          font-weight: 400;
          color: #0B0B0B;
          line-height: ui_h(20);
        }
      }
    }
  }

  .right {
    width: calc(100% - 212 / 650 * 100vh);
    height: 100%;
    padding-left: ui_h(19);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .top-part {
      height: ui_w(87);
      display: flex;
      justify-content: space-between;

      .classroom-container {
        width: ui_w(199);
        height: 100%;
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 2px rgba(62, 89, 253, 0.08);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 ui_h(20);
        cursor: pointer;

        .classroom-title {
          font-size: ui_w(14);
          font-family: DOUYUFont;
          color: #0B0B0B;
          line-height: ui_w(18);
        }

        .classroom-icon {
          width: ui_w(80);
          height: ui_w(80);
          object-fit: contain;
        }
      }
    }

    .bottom-part {
      width: 100%;
      height: 75%;
      .my-class {
        font-size: ui_h(16);
        font-weight: 500;
        color: #0B0B0B;
        text-align: start;
        height: ui_h(40);
      }

      .list {
        width: 100%;
        height: calc(100% - 40 / 695 * 100vh);
        background: rgba(255, 255, 255, 1);
        border-radius: 15px;
        box-shadow: 0px 3px 8px 5px rgba(62, 89, 253, 0.05);
        padding: ui_h(22);
        overflow: scroll;

        .list-title {
          display: flex;
          align-items: center;
          &__mark {
            width: ui_h(3);
            height: ui_h(12);
            background: #1F66FF;
            border-radius: 2px;
          }
          &__text {
            font-size: ui_h(14);
            font-weight: 500;
            color: #0B0B0B;
            line-height: ui_h(20);
            padding-left: ui_h(6);
          }
        }

        .ai-list,
        .special-list {
          position: relative;
        }

        .swiper {
          padding: ui_w(13) ui_h(6) ui_h(21);

          .swiper-slide {
            background: #FFFFFF;
            box-shadow: 0px 3px 6px 0px rgba(62, 89, 253, 0.1);
            border-radius: 20px;
            cursor: pointer;
          }

          img {
            width: 100%;
            height: ui_w(109);
            object-fit: cover;
            border-radius: 20px;
          }

          p {
            font-size: ui_h(14);
            font-weight: 400;
            color: #4B4B4B;
            line-height: ui_h(20);
            padding: 0 9px;
            @include ellipses(1)
          }
        }

        .swiper-ai-prev,
        .swiper-special-prev,
        .swiper-live-prev {
          position: absolute;
          background: url('../../../assets/images/dashboard/arrow.png') center center no-repeat;
          background-size: contain;
          width: ui_w(30);
          height: ui_w(30);
          top: ui_w(50);
          left: ui_w(-9);
          z-index: 9;
          cursor: pointer;
          transform: scaleX(-1);
        }

        .swiper-ai-next,
        .swiper-special-next,
        .swiper-live-next {
          position: absolute;
          background: url('../../../assets/images/dashboard/arrow.png') center center no-repeat;
          background-size: contain;
          width: ui_w(30);
          height: ui_w(30);
          top: ui_w(50);
          right: ui_w(-9);
          z-index: 9;
          cursor: pointer;
        }
      }

      .ai-list-none {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
          width: ui_h(90);
          height: ui_h(90);
        }

        .hint {
          display: flex;
          font-size: ui_h(12);
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8C9399;
          line-height: ui_h(17);

          .hint-blue {
            color: rgba(31, 102, 255, 1);
            cursor: pointer;
          }
        }

        .hint-padding {
          padding:ui_h(8) 0 ui_h(4)
        }
      }
    }
  }
}

.home-web {
  background: #F8FAFF;
  height: calc(100% - 62px);
  overflow-x: hidden;
  overflow-y: hidden;
  padding: 23px 33px 23px 45px;

  .left {
    height: 100%;
    background: url('../../../assets/images/dashboard/bg-left.png') center center no-repeat;
    background-size: contain;
    box-shadow: 0px 4px 8px 5px rgba(14, 37, 171, 0.01);
    border-radius: 15px;
    position: relative;

    .welcome {
      position: relative;
      height: 100%;
      width: 100%;

      .bg {
        height: 100%;
        object-fit: contain;
      }

      .school {
        position: absolute;
        left: -4px;
        top: 52px;

        .school-name {
          background: #FFFFFF;
          box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.28);
          border-radius: 9px 13px 6px 0px;
          font-size: 20px;
          font-weight: 500;
          color: #0B0B0B;
          text-align: center;
          padding: 10px 27px 10px 18px;
          text-align: start;
          word-break: break-all;
          @include ellipses(2)
        }

        .trigle-three {
          width: 0;
          height: 0;
          border-top: 6px solid rgba(109, 153, 250, 1);
          border-left: 6px solid transparent;
        }
      }

      .word {
        position: absolute;
        top: 181px;
        left: 30px;

        .speech {
          font-size: 27px;
          font-weight: 500;
          color: #0B0B0B;
          line-height: 38px;
          word-break: break-all;
        }

        .wisdom {
          margin-top: 23px;
          font-size: 20px;
          font-weight: 400;
          color: #0B0B0B;
          line-height: 28px;
        }
      }
    }
  }

  .right {
    flex: 1;
    height: 100%;
    min-width: 600px;
    min-height: 247px;
    padding-left: 19px;

    .top-part {
      height: 91px;
      min-height: 29px;
      position: relative;
      margin-bottom: 20px;

      .classroom-container {
        position: absolute;
        width: 32%;
        height: 100%;
        box-shadow: 0px 3px 5px 2px rgba(62, 89, 253, 0.08);
        border-radius: 15px;
        padding: 1px;
        cursor: pointer;

        .classroom-container-in {
          width: 100%;
          height: 100%;
          background: #FFFFFF;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-radius: 15px;
          padding: 0 20px;
        }

        .classroom-title {
          font-size: 21px;
          color: #0B0B0B;
          line-height: 27px;
          margin-bottom: 6px;
          font-weight: normal;
        }

        .classroom-des {
          font-size:14px;
          font-weight: 400;
          color: #595959;
          line-height: 20px;
        }

        .classroom-icon {
          width: 80px;
          height: 80px;
          object-fit: contain;
        }
      }

      .classroom-left {
        left: 0;
      }

      .classroom-center {
        transform: translate(-50%,0);
        left: 50%;
      }

      .classroom-right {
        right: 0;
      }

      .classroom-right-top {
        z-index: 12;
      }

      .guide-container {
        position: absolute;
        width: 42%;
        height: 120%;
        background: #F8FAFF 100%;
        z-index: 11;
        border-radius: 100px;
        right: 30%;
        top: -10%;
      }

      .classroom-container:hover {
        background-image: -webkit-linear-gradient(130deg, rgba(150, 235, 255, 1), rgba(31, 102, 255, 1));

        .classroom-icon {
          position: absolute;
          top: -9px;
          right: 20px;
        }
      }
    }

    .bottom-part {
      width: 100%;
      height: calc(100% - 111px);
      min-height: 218px;
      position: relative;
      .my-guide-class {
        position: absolute;
        font-size: 24px;
        font-weight: 500;
        color: #0B0B0B;
        text-align: start;
        height: 68px;
        padding-top: 20px;
        top: 0;
        z-index: 12;
      }

      .guide-class-container {
        position: absolute;
        width: 136px;
        height: 48px;
        background: #F8FAFF 100%;
        z-index: 11;
        border-radius: 100px;
        top: 10px;
        left: -16px;
      }

      .list {
        width: 100%;
        height:100%;
        background: rgba(255, 255, 255, 1);
        border-radius: 15px;
        box-shadow: 0px 3px 8px 5px rgba(62, 89, 253, 0.05);
        padding: 15px;

        .list-title {
          display: flex;
          align-items: center;
          height: 5%;
          &__mark {
            width: 2px;
            height: 14px;
            background: #3479FF;
            border-radius: 2px;
          }
          &__text {
            font-size: 16px;
            font-weight: 500;
            color: #0B0B0B;
            padding-left: 5px;
            margin-right: 15px;
          }

          .section-title {
            width: 90px;
            height: 25px;
            border: 1px solid #E2E2E2;
            border-radius: 15px;
            line-height: 25px;
            font-weight: 400;
            font-size: 14px;
            text-align: center;
            color: #8C8C8C;
            cursor: pointer;
            margin-right: 10px;
          }

          .section-title-active {
            background: #DFEBFF;
            border: none;
            color: #3479FF;
          }
        }

        .show-list {
          padding-top: 10px;
          width: 100%;
          height: 95%;
          display: flex;
          flex-wrap: wrap;
          overflow-x: hidden;
          align-content: flex-start;
          @include scrollBar;

          .course-item {
              height: 190px;
              background: #FFFFFF;
              box-shadow: 0 3px 14px 0 rgba(233,240,255,0.50);
              border-radius: 10px;
              cursor: pointer;

              img {
                  width: 100%;
                  height: 130px;
                  border-radius: 10px 10px 0 0;
                  object-fit: cover;
              }

              span {
                  font-family: PingFangSC-Medium;
                  font-weight: 500;
                  font-size: 16px;
                  color: #0B0B0B;
                  word-break: break-all;
                  padding: 10px 20px 0;
                  @include ellipses(2);
              }
          }
        }

        .ai-list,
        .special-list {
          position: relative;
          height: 95%;
        }

        .swiper {
          padding: 10px 10px 21px;
          height: 100%;

          .swiper-slide {
            background: #FFFFFF;
            box-shadow: 0px 3px 6px 0px rgba(62, 89, 253, 0.1);
            border-radius: 15px;
            height: 100%;
            position: relative;
            cursor: pointer;
          }

          .label {
            position: absolute;
            width: 68px;
            height: 27px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 0px 15px 0px 15px;
            right: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .circle {
              width: 6px;
              height: 6px;
              border-radius: 100px;
              margin-right: 3px;
            }

            .blue {
              background: #A2C0FF;
            }

            .green {
              background: #20EB5E;
            }

            .label-text {
              font-size: 12px;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 17px;
            }
          }

          img {
            width: 100%;
            height: 80%;
            object-fit: cover;
            border-radius: 15px 15px 0 0;
          }

          p {
            font-size: 21px;
            font-weight: 400;
            color: #4B4B4B;
            padding: 0 9px;
            margin: 12px 0;
            @include ellipses(1)
          }
        }

        .swiper-ai-prev,
        .swiper-special-prev,
        .swiper-live-prev {
          position: absolute;
          background: url('../../../assets/images/dashboard/arrow.png') center center no-repeat;
          background-size: contain;
          width: 45px;
          height: 45px;
          top: 25%;
          left: -9px;
          z-index: 9;
          cursor: pointer;
          transform: scaleX(-1);
        }

        .swiper-ai-next,
        .swiper-special-next,
        .swiper-live-next {
          position: absolute;
          background: url('../../../assets/images/dashboard/arrow.png') center center no-repeat;
          background-size: contain;
          width: 45px;
          height: 45px;
          top: 25%;
          right: -9px;
          z-index: 9;
          cursor: pointer;
        }
      }

      .ai-list-none {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
          width: 90px;
          height: 90px;
        }

        .hint {
          display: flex;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8C9399;
          line-height: 17px;

          .hint-blue {
            color: rgba(31, 102, 255, 1);
            cursor: pointer;
          }
        }

        .hint-padding {
          padding:8px 0 4px
        }
      }
    }
  }

  @media screen and (min-width: 0px) {
    .show-list {
      gap:  1.3%;
      .course-item {
        width: 32%;
      }
    }
  }
  @media screen and (min-width: 1281px) {
    .show-list {
      gap:  2.6%;
      .course-item {
        width: 23%;
      }
    }
  }
  @media screen and (min-width: 1441px) {
    .show-list {
      gap:  1.25%;
      .course-item {
        width: 19%;
      }
    }
  }
  // @media screen and (min-width: 1921px) {
  //   .show-list {
  //     gap:  0.8%;
  //     .course-item {
  //       width: 16%;
  //     }
  //   }
  // }
}

.shadow {
  z-index: 10;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .7;
  background: #000;
}

.guide-des {
  position: absolute;
  z-index: 12;

  &__top {
    width: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 0 12px 35px;
  }

  .line {
    width: 4px;
    height: 89px;
    background: linear-gradient(62deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
  }

  .circle {
    width: 12px;
    height: 12px;
    border-radius: 100px;
    background: #FFFFFF;
  }

  .name {
    font-size: 20px;
    color: #FFFFFF;
    line-height: 28px;
  }

  .explain {
    font-size: 18px;
    color: #FFFFFF;
    line-height: 25px;
    margin: 13px 0 20px;
  }

  .guide-btns {
    display: flex;
    .btn {
      width: 130px;
      height: 30px;
      margin-right: 18px;
      cursor: pointer;
    }
  }
}

.guide-progress-2 {
  top: 150%;
  right: 40%;
  width: 27%;
}
</style>
