<template>
  <transition
    name="dialog-fade"
    @after-enter="afterEnter"
    @after-leave="afterLeave"
  >
    <div v-show="visible">
      <div class="md-wapper"></div>
      <div class="md-content" @click.self="handleOut">
        <slot></slot>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'GuideDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    closeOnClickModal: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
    }
  },
  methods: {
    afterEnter () {

    },
    afterLeave () {

    },
    handleOut () {
      // this.visible =false
      if (this.closeOnClickModal) {
        this.$emit('closeMyDialog')
      }
    }
  }

}
</script>

<style scoped lang="scss">
    .md-wapper{
        z-index: 10;
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: .5;
        background: #000;
    }
    .md-content{
        z-index: 11;
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }
</style>
