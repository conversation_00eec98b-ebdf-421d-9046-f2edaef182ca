<template>
  <div class="courseware-container">
    <div v-show="courseWare" id="whiteboard" class="whiteboard-canvas"></div>
    <div v-show="courseWare" class="button-group">
      <img src="@/assets/images/classroom/pre.png" alt="上一页" @click="pageChange('-')" />
      <div ref="page" class="page" @click="handlePageChangeBox">
        {{ currPage + 1 }} / {{ total }}
        <div v-show="pageList.show" class="page-list">
          <div
            v-for="item in total"
            :key="item"
            class="page-item"
            :class="{active: item === currPage + 1}"
            @click.stop="handlePageChange(item)"
          >
            {{ item }}
          </div>
        </div>
        <div v-show="pageList.show" class="triangle"></div>
      </div>
      <img src="@/assets/images/classroom/next.png" alt="下一页" @click="pageChange('+')" />
    </div>
    <div v-show="!courseWare" class="list-none">
      <img src="@/assets/images/classroom/nofile.png" alt="课件为空" />
      <div class="hint hint-padding">暂时没有课件哦～</div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import WhiteClient from 'sdk/whiteBoard'
import { getGenerateToken } from '@/api/classroom-api'
export default {
  props: {
    courseWare: {
      type: Object,
      require: true,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {
      currPage: 0,
      total: 10,
      boardClient: null,
      scenes: null,
      sceneUUid: null,
      token: {
        boardToken: '',
        boardUuid: ''
      },
      pageList: {
        show: false
      }
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  watch: {
    courseWare (val) {
      if (val === null) return
      console.log(val)
      this._getGenerateToken()
    }
  },
  mounted () {
    window.addEventListener('resize', this.refreshView)
    window.addEventListener('click', this.hiddenPageList)
  },
  destroyed () {
    window.removeEventListener('resize', this.refreshView)
    window.removeEventListener('click', this.hiddenPageList)
  },
  methods: {
    async _getGenerateToken () {
      const { data } = await getGenerateToken({ roomId: this.courseWare.roomId, uid: this.id, role: 'writer' })
      this.token = {
        ...this.token,
        ...data
      }
      this._getDocumentInfo(this.courseWare.mediaFile.documentScene && this.courseWare.mediaFile.documentScene.scenes)
    },
    _getDocumentInfo (scenes) {
      try {
        if (scenes) {
          const newScenes = JSON.parse(scenes)
          const pptScenes = []
          if (newScenes.progress.convertedFileList.length > 0) {
            newScenes.progress.convertedFileList.map((f, i) => {
              pptScenes.push({
                name: 'page' + i,
                ppt: {
                  src: f.conversionFileUrl,
                  width: f.width,
                  height: f.height,
                  previewURL: f.preview
                }
              })
            })
            this.scenes = pptScenes
            this.sceneUUid = newScenes.uuid
          }
        }
        this.joinRoom()
      } catch (error) {
        console.error(error)
      }
    },
    joinRoom () {
      const option = this.courseWare.useLocalFont ? {
        pptParams: {
          useServerWrap: false
        }
      } : {}
      this.boardClient = new WhiteClient(option)
      const joinRoomParams = {
        uuid: this.token.boardUuid,
        uid: this.id + '',
        roomToken: this.token.boardToken,
        disableCameraTransform: true
      }
      this.boardClient.joinRoom(joinRoomParams, {
        onPhaseChanged: (phase) => {
          if (phase === 'connected') {
            setTimeout(() => {
              this.$room.moveCamera({ centerX: 0, centerY: 0 })
              this.$room.refreshViewSize()
              this.$room.scalePptToFit('continuous')
            }, 1000)
          }
        },
        onRoomStateChanged: state => {
          if (state.sceneState) {
            this.currPage = state.sceneState.index
          }
        }
      }).then((room) => {
        if (this.scenes && this.scenes.length > 0) {
          if (room.state.sceneState.scenePath === '/init' || room.state.globalState.__pptState.uuid !== this.sceneUUid) {
            room.putScenes(`/${this.sceneUUid}`, this.scenes)
            room.setScenePath(`/${this.sceneUUid}/${this.scenes[0].name}`)
          }
        }
        room.bindHtmlElement(document.getElementById('whiteboard'))
        room.setMemberState({ currentApplianceName: 'selector' })
        this.total = room.state.sceneState.scenes.length
        room.setSceneIndex(0)
        this.currPage = room.state.sceneState.index
        this.$room = room
        this.refreshView()
      }).catch((err) => {
        console.error(err)
      })
    },
    pageChange (type) {
      switch (type) {
        case '-':
          // if (this.currPage === 0) return
          // this.currPage--
          this.$room.pptPreviousStep()

          break
        case '+':
          // if (this.currPage + 1 === this.total) return
          // this.currPage++
          this.$room.pptNextStep()
          break
        default:
          this.currPage = +type - 1
          this.$room.setSceneIndex(this.currPage)
      }
      this.currPage = this.$room.state.sceneState.index
    },
    refreshView () {
      this.$room.moveCamera({ centerX: 0, centerY: 0 })
      this.$room.refreshViewSize()
      this.$room.scalePptToFit('continuous')
    },
    handlePageChangeBox () {
      this.pageList.show = true
    },
    handlePageChange (index) {
      this.pageChange(index)
      this.pageList.show = false
    },
    hiddenPageList (e) {
      if (!this.$refs.page.contains(e.target)) {
        this.pageList.show = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
$vw_design_width: 1920;
$vw_design_height: 1080;
@function ui_w($px) {
  @return $px / $vw_design_width * 100vw;
}
@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}

@function ui_h2($px) {
  @return -($px / $vw_design_height * 100vh);
}

.courseware-container {
  width: 100%;
  height: 100%;
  position: relative;

  .whiteboard-canvas {
    width: 100%;
    height: 100%;
  }

  .button-group {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: ui_h(10);
    z-index: 40;

      img {
        cursor: pointer;
        width: ui_h(56);
        height: ui_h(56);
      }

      .page {
        cursor: pointer;
        width: ui_h(116);
        height: ui_h(40);
        background: rgba(255, 255, 255, 0.63);
        border-radius: 20px;
        border: 1px solid #6D8FAF;
        text-align: center;
        line-height: ui_h(40);
        font-size: ui_h(24);
        font-weight: 500;
        color: #6D8FAF;
        margin: 0 ui_h(10);
        position: relative;
        cursor: pointer;

        .page-list {
          position: absolute;
          width: 100%;
          height: ui_h(100);
          overflow-y: auto;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          top: ui_h2(112);

          .page-item {
            color: rgba(255,255,255,0.5);
            cursor: pointer;
            &:hover {
              color: #9de1f9;
            }
          }

          .active {
              color: #9de1f9;
          }
        }
        .triangle {
          position: absolute;
          width: 0;
          height: 0;
          border-width: ui_w(10);
          border-style: solid;
          border-color: rgba(0, 0, 0, 0.5) transparent transparent transparent;
          top: -10px;
          left: 45%;
        }
      }
  }

  .video-box {
    width: 100%;
    height: 100%;
    position: relative;
  }
  .list-none {
    padding: ui_h(19) 0 ui_h(32);
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: ui_h(90);
      height: ui_h(90);
    }

    .hint {
      display: flex;
      font-size: ui_h(12);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8C9399;
      line-height: ui_h(17);

      .hint-blue {
        color: rgba(31, 102, 255, 1);
        cursor: pointer;
      }
    }

    .hint-padding {
      padding:ui_h(8) 0 ui_h(4)
    }
  }
}
</style>
