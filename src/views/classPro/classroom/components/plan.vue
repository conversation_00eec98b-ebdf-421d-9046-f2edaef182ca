<template>
  <div class="plan-box">
    <div
      v-show="lessonPlan"
      ref="plan"
      v-loading="loading"
      class="plan-container classpro-loadding"
      element-loading-text=""
      element-loading-spinner=""
      element-loading-background="#fff"
    >
      <canvas id="the-canvas" class="pdf"></canvas>
      <div v-show="+pdf_pages > 1" class="button-group">
        <img src="@/assets/images/classroom/pre.png" alt="上一页" @click="pre()" />
        <div ref="page" class="page" @click="handlePageChangeBox">
          {{ pdf_current_page }} / {{ pdf_pages }}
          <div v-show="pageList.show" class="page-list">
            <div
              v-for="item in pdf_pages"
              :key="item"
              class="page-item"
              :class="{active:item===pdf_current_page}"
              @click.stop="handlePageChange(item)"
            >
              {{ item }}
            </div>
          </div>
          <div v-show="pageList.show" class="triangle"></div>
        </div>
        <img src="@/assets/images/classroom/next.png" alt="下一页" @click="next()" />
      </div>
    </div>
    <div v-show="!lessonPlan" class="list-none">
      <img src="../../../../assets/images/classroom/nofile.png" alt="课件为空" />
      <div class="hint hint-padding">暂时没有教案哦～</div>
    </div>
  </div>
</template>

<script>
import { imgScaling } from '@/utils/index.js'
const PDFJS = require('pdfjs-dist')
PDFJS.GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.entry.js')
export default {
  props: {
    isFullScreen: {
      type: Boolean,
      require: true,
      default: false
    },
    isFullAllScreen: {
      type: Boolean,
      require: true,
      default: false
    },
    lessonPlan: {
      type: Object,
      require: true,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {
      pageNumPending: null,
      pdf_scale: 1.0, // pdf放大系数
      pdf_current_page: 1,
      pdf_pages: 1,
      loading: false,
      lock: false,
      pageList: {
        show: false
      }
      // pdf_src: 'https://static.bingotalk.cn/courses/docs/********************************.pdf'
    }
  },
  watch: {
    isFullScreen () {
      this.$nextTick(() => {
        this._renderPage(Number(this.pdf_current_page))
      })
    },
    isFullAllScreen () {
      this.$nextTick(() => {
        this._renderPage(Number(this.pdf_current_page))
      })
    },
    lessonPlan (val) {
      this.loading = false
      if (val === null) return
      this.$nextTick(() => {
        this.pdf_current_page = 1
        this._loadFile(val.mediaFile.url)
      })
    }
  },
  mounted () {
    window.addEventListener('resize', this.changeResize)
    window.addEventListener('click', this.hiddenPageList)
  },
  destroyed () {
    window.removeEventListener('resize', this.changeResize)
    window.removeEventListener('click', this.hiddenPageList)
  },
  methods: {
    changeResize () {
      if (!this.pdfDoc) return
      this.$nextTick(() => {
        this._renderPage(Number(this.pdf_current_page))
      })
    },
    _loadFile (url) { // 初始化pdf
      if (!url) return
      this.loading = true
      const loadingTask = PDFJS.getDocument(url)
      loadingTask.promise
        .then((pdf) => {
          this.loading = false
          this.pdfDoc = pdf
          this.pdf_pages = this.pdfDoc.numPages
          this.$nextTick(() => {
            this._renderPage(1)
          })
        })
    },
    _renderPage (num = 1) { // 渲染pdf页
      if (this.lock) return
      this.lock = true
      this.loading = true
      this.pdfDoc.getPage(num)
        .then((page) => {
          const canvas = document.getElementById('the-canvas')
          const ctx = canvas.getContext('2d')
          const dpr = window.devicePixelRatio || 1
          const bsr = ctx.webkitBackingStorePixelRatio ||
              ctx.mozBackingStorePixelRatio ||
              ctx.msBackingStorePixelRatio ||
              ctx.oBackingStorePixelRatio ||
              ctx.backingStorePixelRatio || 1
          const ratio = dpr / bsr
          const viewport = page.getViewport({ scale: this.pdf_scale })
          const transform = this.pdf_scale !== 1
            ? [this.pdf_scale, 0, 0, this.pdf_scale, 0, 0]
            : null

          canvas.width = viewport.width * ratio
          canvas.height = viewport.height * ratio
          // 等比计算内容显示宽高
          const { tempWidth, tempHeight } = imgScaling(canvas.width, canvas.height, document.getElementById('plan').clientWidth, document.getElementById('plan').clientHeight)

          canvas.style.width = tempWidth + 'px'
          canvas.style.height = tempHeight + 'px'
          ctx.setTransform(ratio, 0, 0, ratio, 0, 0)
          const renderContext = {
            canvasContext: ctx,
            transform,
            viewport: viewport
          }
          page.render(renderContext).promise.then(() => {
            this.loading = false
            this.lock = false
            if (this.pageNumPending !== null) {
              this.$nextTick(() => {
                this._renderPage(this.pageNumPending)
              })
              this.pageNumPending = null
            }
          }).catch(() => {
            this.lock = false
            this.loading = false
          })
        })
    },
    pre () {
      if (this.pdf_current_page > 1) {
        this.pdf_current_page--
        this.queueRenderPage(this.pdf_current_page)
      }
    },
    next () {
      if (this.pdf_current_page < this.pdf_pages) {
        this.pdf_current_page++
        this.queueRenderPage(this.pdf_current_page)
      }
    },
    handlePageChangeBox () {
      this.pageList.show = true
    },
    handlePageChange (index) {
      this.pdf_current_page = index
      this.pageList.show = false
      this.$nextTick(() => {
        this._renderPage(Number(this.pdf_current_page))
      })
    },
    hiddenPageList (e) {
      if (!this.$refs.page.contains(e.target)) {
        this.pageList.show = false
      }
    },
    queueRenderPage (num) {
      const number = Number(num)
      if (this.lock) {
        this.pageNumPending = number
      } else {
        this.$nextTick(() => {
          this._renderPage(number)
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
$vw_design_width: 1920;
$vw_design_height: 1080;
@function ui_w($px) {
  @return $px / $vw_design_width * 100vw;
}
@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}

@function ui_h2($px) {
  @return -($px / $vw_design_height * 100vh);
}

.plan-box {
  width: 100%;
  height: 100%;

  .plan-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .pdf {
      width: ui_h(100);
    }

    .button-group {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: ui_h(10);
        left: 50%;
        transform: translate(-50%,0);
        z-index: 40;

        img {
            cursor: pointer;
            width: ui_h(56);
            height: ui_h(56);
        }

        .page {
            width: ui_h(116);
            height: ui_h(40);
            background: rgba(255, 255, 255, 0.63);
            border-radius: 20px;
            border: 1px solid #6D8FAF;
            text-align: center;
            line-height: ui_h(40);
            font-size: ui_h(24);
            font-weight: 500;
            color: #6D8FAF;
            margin: 0 ui_h(10);
            position: relative;
            cursor: pointer;
            .page-list {
              position: absolute;
              width: 100%;
              height: ui_h(100);
              overflow-y: auto;
              background: rgba(0, 0, 0, 0.5);
              border-radius: 5px;
              top: ui_h2(112);

              .page-item {
                color: rgba(255,255,255,0.5);
                cursor: pointer;
                &:hover {
                  color: #9de1f9;
                }
              }

              .active {
                  color: #9de1f9;
              }
            }
            .triangle {
              position: absolute;
              width: 0;
              height: 0;
              border-width: ui_w(10);
              border-style: solid;
              border-color: rgba(0, 0, 0, 0.5) transparent transparent transparent;
              top: -10px;
              left: 45%;
            }
        }
    }
  }
  .list-none {
    padding: ui_h(19) 0 ui_h(32);
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: ui_h(90);
      height: ui_h(90);
    }

    .hint {
      display: flex;
      font-size: ui_h(12);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8C9399;
      line-height: ui_h(17);

      .hint-blue {
        color: rgba(31, 102, 255, 1);
        cursor: pointer;
      }
    }

    .hint-padding {
      padding:ui_h(8) 0 ui_h(4)
    }
  }
}
</style>
