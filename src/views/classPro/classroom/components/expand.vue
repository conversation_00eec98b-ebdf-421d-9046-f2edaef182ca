<template>
  <div class="expand-box ">
    <div v-show="expend.length > 0" class="expand-container">
      <div v-show="!fileShow" class="grid-box">
        <div v-for="item in list" :key="item.id">
          <div class="centent-box">
            <div class="box" @click="checkFile(item)">
              <img v-if="['VIDEO'].indexOf(item.mediaFile.type) > -1" class="cover-image" :src="item.mediaFile.coverUrl ? item.mediaFile.coverUrl : defaultImg" />
              <img v-else-if="['IMAGE'].indexOf(item.mediaFile.type) > -1" class="cover-image" :src="item.mediaFile.url" />
              <div class="title-box">
                <div class="left article-singer-container">
                  {{ item.resourceName }}
                </div>
                <div v-show="['VIDEO'].indexOf(item.mediaFile.type) > -1" class="right">
                  {{ time(item.mediaFile.duration) }}
                </div>

              </div>
              <div v-if="['VIDEO'].indexOf(item.mediaFile.type) > -1" class="play">
                <img src="@/assets/images/classroom/play.png" alt="播放" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-show="!fileShow && expend.length > 4" class="button-group">
        <img src="@/assets/images/classroom/pre.png" alt="上一页" @click="pageChange('-')" />
        <div ref="page" class="page" @click="handlePageChangeBox">
          {{ current }} / {{ total }}
          <div v-show="pageList.show" class="page-list">
            <div
              v-for="item in total"
              :key="item"
              class="page-item"
              :class="{active:item===current}"
              @click.stop="handlePageChange(item)"
            >
              {{ item }}
            </div>
          </div>
          <div v-show="pageList.show" class="triangle"></div>
        </div>
        <img src="@/assets/images/classroom/next.png" alt="下一页" @click="pageChange('+')" />
      </div>
      <!-- 文件放大展示 -->
      <div v-if="fileShow" class="video-box">
        <videoJs v-if="['VIDEO'].indexOf(fileItem.mediaFile.type) > -1" :options="videoOptions" />
        <img v-else-if="['IMAGE'].indexOf(fileItem.mediaFile.type) > -1" class="cover-image" :src="fileItem.mediaFile.url" />
      </div>
    </div>
    <div v-show="expend.length === 0" class="list-none">
      <img src="@/assets/images/classroom/nofile.png" alt="资料为空" />
      <div class="hint hint-padding">暂时没有资料哦～</div>
    </div>
  </div>
</template>

<script>
import videoJs from '@/components/classPro/video/index.vue'
import defaultImg from '@/assets/images/classroom/bg-classroom.png'
import moment from 'moment'
export default {
  components: {
    videoJs
  },
  props: {
    expend: {
      type: Array,
      require: true,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      defaultImg: defaultImg,
      current: 1,
      total: 10,
      pageSize: 4,
      list: [],
      pageList: {
        show: false
      },
      fileShow: false,
      fileItem: null,
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: true,
        loop: false,
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    }
  },
  watch: {
    expend (val) {
      if (val.length === 0) return
      this.init()
    }
  },
  mounted () {
    window.addEventListener('click', this.hiddenPageList)
  },
  destroyed () {
    window.removeEventListener('click', this.hiddenPageList)
  },
  methods: {
    init () {
      this.total = Math.ceil(this.expend.length / this.pageSize)
      this.list = this.expend.slice((this.current - 1) * this.pageSize, this.current * this.pageSize)
      this.current = 1
    },
    pageChange (type) {
      switch (type) {
        case '-':
          if (this.current === 1) return
          this.current--
          break
        case '+':
          if (this.current === this.total) return
          this.current++
          break
        default:
          this.current = +type
          break
      }
      // 分页
      this.list = this.expend.slice((this.current - 1) * this.pageSize, this.current * this.pageSize)
    },
    checkFile (file) {
      this.fileItem = file
      if (['VIDEO'].indexOf(file.mediaFile.type) > -1) {
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: file.mediaFile.url,
            type: 'video/mp4'
          }]
        }
      }
      this.fileShow = true
      this.$emit('playVideo')
    },
    hiddenVideo () {
      this.fileShow = false
    },
    handlePageChangeBox () {
      this.pageList.show = true
    },
    handlePageChange (index) {
      this.pageChange(index)
      this.pageList.show = false
    },
    hiddenPageList (e) {
      if (!this.$refs.page.contains(e.target)) {
        this.pageList.show = false
      }
    },
    time (duration) {
      const hours = moment.duration(duration * 1000).hours()
      const minutes = moment.duration(duration * 1000).minutes()
      const seconds = moment.duration(duration * 1000).seconds()
      return `${hours ? hours < 10 ? '0' + hours + ':' : hours + ':' : ''}${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`
    }
  }
}
</script>

<style scoped lang="scss">
$vw_design_width: 1920;
$vw_design_height: 1080;
@function ui_w($px) {
  @return $px / $vw_design_width * 100vw;
}
@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}

@function ui_h2($px) {
  @return -($px / $vw_design_height * 100vh);
}

.expand-box {
  width: 100%;
  height: 100%;

  .expand-container {
    width: 100%;
    height: 100%;
    position: relative;
    .grid-box {
      padding: 17px 20px;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      display: grid;
      grid-template-columns: 50% 50%;
      grid-template-rows: 50% 50%;

      .centent-box {
        width: 100%;
        height: 100%;
        padding: 10px 16px;

        .box {
          width: 100%;
          height: 100%;
          border-radius: 15px;
          position: relative;
          cursor: pointer;

          .cover-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 15px;
          }

          &:hover {
            border: 2px solid #fff;
            .play {
              display: flex;
            }
          }
          .play {
            display: none;
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            top: 0;
            justify-content: center;
            align-items: center;
            img {
              width: ui_h(50);
              height: ui_h(50);
            }
          }

          .title-box {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 20%;
            min-height: 34px;
            display: flex;
            flex-direction: row;
            align-items: center;
            color: #fff;
            padding: 0 10px;
            background: linear-gradient(180deg, rgba(214, 232, 248, 0) 0%, rgba(0, 0, 0, 0.46) 100%);
            border-radius: 0 0 15px 15px;
            font-size: 14px;
            font-weight: 500;
            .left {
              justify-self: flex-start;
              flex: 1;
            }
            .right {
              width: 55px;
              justify-self: flex-end;
            }
          }
        }
      }
    }
    .button-group {
      display: flex;
      width: 100%;
      justify-content: center;
      align-items: center;
      position: absolute;
      bottom: ui_h(10);
      z-index: 40;

        img {
          cursor: pointer;
          width: ui_h(56);
          height: ui_h(56);
        }

        .page {
          cursor: pointer;
          width: ui_h(116);
          height: ui_h(40);
          background: rgba(255, 255, 255, 0.63);
          border-radius: 20px;
          border: 1px solid #6D8FAF;
          text-align: center;
          line-height: ui_h(40);
          font-size: ui_h(24);
          font-weight: 500;
          color: #6D8FAF;
          margin: 0 ui_h(10);
          position: relative;
          cursor: pointer;

          .page-list {
            position: absolute;
            width: 100%;
            height: ui_h(100);
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
            top: ui_h2(112);

            .page-item {
              color: rgba(255,255,255,0.5);
              cursor: pointer;
              &:hover {
                color: #9de1f9;
              }
            }

            .active {
                color: #9de1f9;
            }
          }
          .triangle {
            position: absolute;
            width: 0;
            height: 0;
            border-width: ui_w(10);
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.5) transparent transparent transparent;
            top: -10px;
            left: 45%;
          }
        }
    }
    .video-box {
      width: 100%;
      height: 100%;
      position: relative;
      .cover-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .list-none {
    padding: ui_h(19) 0 ui_h(32);
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: ui_h(90);
      height: ui_h(90);
    }

    .hint {
      display: flex;
      font-size: ui_h(12);
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8C9399;
      line-height: ui_h(17);

      .hint-blue {
        color: rgba(31, 102, 255, 1);
        cursor: pointer;
      }
    }

    .hint-padding {
      padding:ui_h(8) 0 ui_h(4)
    }
  }
}
</style>
