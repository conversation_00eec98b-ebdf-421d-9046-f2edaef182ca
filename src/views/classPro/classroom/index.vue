<template>
  <div class="container-classroom">
    <!-- 头部 -->
    <div v-if="!isFullScreen" class="header">
      <div class="back" @click="goBack">
        <img src="../../../assets/images/classroom/return.png" alt="返回" />
      </div>
      <div class="package-name">
        {{ unitTitle }}
      </div>
    </div>
    <!-- 内容 -->
    <div class="content">
      <div class="content-left">
        <div :class="[isFullScreen ? 'content-left-show-full-screen' : 'content-left-show']">
          <div class="bg-show"></div>
          <div id="plan" class="show" @mousemove="showBtn">
            <plan v-show="activeIndex === 1" :is-full-screen="isFullScreen" :is-full-all-screen="isFullAllScreen" :lesson-plan="lessonPlan" />
            <courseware v-show="activeIndex === 2" :course-ware="courseWare" />
            <expand v-show="activeIndex === 3" ref="expand" :expend="expend" @playVideo="playVideo" />
            <div v-if="isFullAllScreen && btnShow" class="footer-fullpage">
              <div class="button-full-screen-enlarge"><img src="@/assets/images/classroom/suofang.png" alt="全屏返回" @click="fullScreen('plan')" /></div>
            </div>
          </div>
          <div v-show="closeBtnShow" class="close" @click="closeVideo">
            <img src="@/assets/images/classroom/close.png" alt="关闭" />
          </div>
          <div class="menu">
            <div class="menu-item" :class="[activeIndex === 1 ? 'menu-checked-bg' : 'menu-unchecked-bg']" @click="activeChange(1)"><img :src="activeIndex === 1 ? planChecked : planUnChecked" alt="教案" /></div>
            <div class="menu-item" :class="[activeIndex === 2 ? 'menu-checked-bg' : 'menu-unchecked-bg']" @click="activeChange(2)"><img :src="activeIndex === 2 ? coursewareChecked : coursewareUnChecked" alt="课件" /></div>
            <div class="menu-item" :class="[activeIndex === 3 ? 'menu-checked-bg' : 'menu-unchecked-bg']" @click="activeChange(3)"><img :src="activeIndex === 3 ? resourceChecked : resourceUnChecked" alt="拓展资源" /></div>
          </div>
          <div class="class-number">{{ `第${unitActive.index + 1}讲` || '' }}</div>
        </div>
        <img class="button-full-screen" :class="{ 'full-screen-none':isFullScreen }" src="../../../assets/images/classroom/button-full-screen.png" alt="全屏上课" @click="changeFullScreenMode" />
      </div>
      <div class="content-right" :class="{ 'full-screen-none':isFullScreen }">
        <div class="rope">
          <img class="rope-long" src="../../../assets/images/classroom/rope-long.png" alt="长绳子" />
          <img class="rope-long" src="../../../assets/images/classroom/rope-long.png" alt="长绳子" />
        </div>
        <div class="unit-list">
          <div
            v-for="(item, index) in unitList"
            :key="item.id"
            class="unit"
            :class="{
              'unit-bg': index !== unitList.length - 1,
              'unit-bg-yellow': index !== unitList.length - 1 && index === unitActive.index,
              'unit-bg-yellow-end': index === unitList.length - 1 && index === unitActive.index,
              'unit-bg-end': index === unitList.length - 1
            }"
            @click="handleUnit(item, index)"
          >
            <div class="unit-title" :class="{ 'unit-checked' : index === unitActive.index }">{{ `第${index + 1}讲·` || '' }}{{ item.title }}</div>
            <div v-if="index !== unitList.length - 1" class="rope rope-short-position">
              <img class="rope-short" src="../../../assets/images/classroom/rope-short.png" alt="短绳子" />
              <img class="rope-short" src="../../../assets/images/classroom/rope-short.png" alt="短绳子" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 底部 -->
    <div v-if="!isFullScreen" class="footer">
      <img src="../../../assets/images/classroom/footer-left.png" alt="左下角" />
      <img src="../../../assets/images/classroom/footer-right.png" alt="右下角" />
    </div>
    <div v-else class="footer">
      <div class="button-full-screen-bottom" @click="changeFullScreenMode"><img src="../../../assets/images/classroom/back-bottomLeft.png" alt="全屏返回" /></div>
      <div v-show="activeIndex !== 3 || (activeIndex === 3 && closeBtnShow)" class="button-full-screen-enlarge" @click="fullScreen('plan')"><img src="@/assets/images/classroom/enlarge.png" alt="全屏返回" /></div>
    </div>
  </div>
</template>

<script>
import screenfull from 'screenfull'
import plan from './components/plan.vue'
import expand from './components/expand.vue'
import courseware from './components/courseware.vue'
import { getCoursecommUnitList, getCoursecommUnitDetail, getCoursecommInfo } from '@/api/coursecomm-api.js'
import { getUserExtra } from '@/api/user-api.js'
import planChecked from '@/assets/images/classroom/plan-checked.png'
import planUnChecked from '@/assets/images/classroom/plan-unchecked.png'
import coursewareChecked from '@/assets/images/classroom/courseware-checked.png'
import coursewareUnChecked from '@/assets/images/classroom/courseware-unchecked.png'
import resourceChecked from '@/assets/images/classroom/resource-checked.png'
import resourceUnChecked from '@/assets/images/classroom/resource-unchecked.png'

export default {
  components: {
    plan,
    expand,
    courseware
  },
  data () {
    return {
      planChecked: planChecked,
      planUnChecked: planUnChecked,
      coursewareChecked: coursewareChecked,
      coursewareUnChecked: coursewareUnChecked,
      resourceChecked: resourceChecked,
      resourceUnChecked: resourceUnChecked,
      btnShow: false,
      btnTimer: null,
      isFullScreen: false,
      activeIndex: 1,
      closeBtnShow: false,
      isFullAllScreen: false,
      useLocalFont: false,
      courseId: 0,
      unitList: [],
      unitActive: {
        title: ''
      },
      unitTitle: '',
      lessonPlan: null,
      courseWare: null,
      expend: []
    }
  },
  created () {
    const loading = this.$loading({
      lock: true,
      text: '',
      spinner: '',
      background: '#FFFFFF',
      customClass: 'classpro-loadding'
    })
    try {
      const img = new Image()
      img.src = require('@/assets/images/classroom/bg-classroom.png')
      img.onload = () => {
        loading.close()
      }
    } catch (error) {
      loading.close()
    }
  },
  mounted () {
    // window.addEventListener('fullscreenchange', () => {
    //   if (document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement) {
    //     this.isFullAllScreen = true
    //   } else {
    //     this.isFullAllScreen = false
    //   }
    // })
    if (screenfull.isEnabled) {
      screenfull.on('change', () => {
        if (screenfull.isFullscreen) {
          this.isFullAllScreen = true
        } else {
          this.isFullAllScreen = false
        }
      })
    }
    this.courseId = this.$route.params && this.$route.params.courseId
    if (isNaN(Number(this.courseId))) {
      this.$message.error('课程信息不正确，已为您跳回首页')
      this.$router.push('/classpro')
      return
    }
    this._getCoursecommUnitList()
    this._getCoursecommInfo()
  },
  methods: {
    async _getCoursecommInfo () {
      const { data } = await getCoursecommInfo({ coursecommId: this.courseId })
      this.unitTitle = data.title || ''
    },
    async _getCoursecommUnitList () {
      try {
        await this._getUserExtra()
        const { data } = await getCoursecommUnitList({ coursecommId: this.courseId })
        // console.log(data)
        if (data && data.length > 0) {
          this.unitList = data
          this.unitActive = { ...data[0], index: 0 }
          // 默认展示第一单元
          this._getCoursecommUnitDetail(data[0].id)
        } else {
          this.unitList = []
        }
      } catch (error) {
        console.log(error)
      }
    },
    async _getCoursecommUnitDetail (coursecommUnitId) {
      try {
        const { data } = await getCoursecommUnitDetail({ coursecommUnitId })
        // console.log(data)
        this.lessonPlan = null
        this.courseWare = null
        this.expend = []
        if (data && data.expandList && data.expandList.length > 0) {
          data.expandList.forEach(element => {
            if (element.resourceType === 'LESSON_PLAN') {
              // 教案
              this.lessonPlan = element
            } else if (element.resourceType === 'COURSE_WARE') {
              // 课件
              this.courseWare = { ...element, roomId: data.roomId, useLocalFont: this.useLocalFont }
            } else if (element.resourceType === 'EXPEND_RESOURCE') {
              // 拓展资源
              this.expend.push(element)
            }
          })
        }
      } catch (error) {
        console.log(error)
      }
    },
    async _getUserExtra () {
      const { data } = await getUserExtra()
      if (data) {
        this.useLocalFont = data.useLocalFont
      } else {
        this.useLocalFont = false
      }
    },
    handleUnit (data, index) {
      this.unitActive = { ...data, index }
      this.closeVideo()
      this._getCoursecommUnitDetail(data.id)
    },
    changeFullScreenMode () {
      this.isFullScreen = !this.isFullScreen
    },
    activeChange (index) {
      this.activeIndex = index
      this.closeVideo()
      if (index === 3) {
        this.$nextTick(() => {
          this.$refs.expand.hiddenVideo()
        })
      }
    },
    playVideo () {
      this.closeBtnShow = true
    },
    closeVideo () {
      this.$refs.expand.hiddenVideo()
      this.closeBtnShow = false
    },
    goBack () {
      const from = this.$route.query && this.$route.query.from
      if (from) {
        this.$router.push({ path: from })
      } else {
        this.$router.push({ path: '/classpro' })
      }
    },
    fullScreen (domName) {
      console.log(this.isFullAllScreen)
      if (this.isFullAllScreen) {
        // if (document.exitFullscreen) {
        //   document.exitFullscreen()
        // } else if (document.webkitExitFullscreen) {
        //   document.webkitExitFullscreen()
        // } else if (document.mozCancelFullScreen) {
        //   document.mozCancelFullScreen()
        // } else if (document.msExitFullscreen) {
        //   document.msExitFullscreen()
        if (screenfull.isEnabled && screenfull.isFullscreen) {
          screenfull.exit()
        }
        // }
      } else {
        const element = document.querySelector('#' + domName) // 获取dom
        // if (element.requestFullscreen) {
        //   element.requestFullscreen()
        // } else if (element.webkitRequestFullScreen) {
        //   element.webkitRequestFullScreen()
        // } else if (element.mozRequestFullScreen) {
        //   element.mozRequestFullScreen()
        // } else if (element.msRequestFullscreen) {
        //   element.msRequestFullscreen()
        // }
        if (screenfull.isEnabled) {
          screenfull.request(element)
        }
      }
    },
    showBtn () {
      if (!this.isFullAllScreen) return
      this.btnShow = true
      clearTimeout(this.btnTimer)
      this.btnTimer = setTimeout(() => {
        this.btnShow = false
      }, 1500)
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/mixin';
$vw_design_width: 1920;
$vw_design_height: 1080;
@function ui_w($px) {
  @return $px / $vw_design_width * 100vw;
}
@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}
.container-classroom {
  width: 100%;
  height: 100%;
  background: url('../../../assets/images/classroom/bg-classroom.png') center center no-repeat;
  background-size: cover;
  position: relative;

  .header {
    position: absolute;
    width: 100%;
    display: flex;
    justify-content: space-between;
    background: url('../../../assets/images/classroom/bg-classroom-top.png') top center no-repeat;
    background-size: contain;
    z-index: 10;

    .back {
      background: url('../../../assets/images/classroom/bg-classroom-topLeft.png') center center no-repeat;
      background-size: contain;
      width: ui_h(230);
      height: ui_h(75);
      cursor: pointer;
      padding-top: ui_h(6);
      padding-left: ui_h(40);

      img {
        width: ui_h(96);
        height: ui_h(62);
      }
    }

    .package-name {
      background: url('../../../assets/images/classroom/bg-classroom-topRight.png') center center no-repeat;
      background-size: contain;
      width: ui_h(1042);
      height: ui_h(75);
      font-size: ui_h(36);
      font-weight: 500;
      color: #FAFCFF;
      line-height: ui_h(75);
      padding-left: ui_h(164);
      padding-right: ui_h(82);
      word-break: break-all;
      @include ellipses(1)
    }
  }

  .content {
    display: flex;
    justify-content: center;

    .content-left {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .content-left-show {
      width: ui_h(1422);
      height: ui_h(842);
      position: relative;
      margin-top: ui_h(74);

      .menu {
        position: absolute;
        top: ui_h(220);
        left: ui_h(6);
        z-index: 11;
        .menu-item {
          width: ui_h(107);
          height: ui_h(107);
          text-align: center;
          padding-top: ui_h(15);
          cursor:pointer;

          img {
            height: ui_h(66);
            object-fit: contain;
          }
        }
        .menu-unchecked-bg {
          background: url('../../../assets/images/classroom/unchecked.png') center center no-repeat;
          background-size: contain;
        }
        .menu-checked-bg {
          background: url('../../../assets/images/classroom/checked.png') center center no-repeat;
          background-size: contain;
        }
      }

      .class-number {
        position: absolute;
        width: ui_h(160);
        top: ui_h(44);
        left: ui_h(585);
        font-size: ui_h(32);
        font-weight: 500;
        color: #2D4A6F;
        line-height: ui_w(45);
        text-shadow: 0px 0px 4px rgba(255, 255, 255, 0.75);
        text-align: center;
        @include ellipses(1);
        z-index: 11;
      }

      .bg-show {
        width: 100%;
        height: 100%;
        position: absolute;
        background: url('../../../assets/images/classroom/bg-content.png') center center no-repeat;
        background-size: contain;
        z-index: 10;
        pointer-events: none;
      }

      .show {
        position: absolute;
        left: ui_h(145);
        top: ui_h(145);
        background: linear-gradient(180deg, #B3CEE3 0%, #DAE9F3 100%);
        box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.18), 0px 0px 16px 0px rgba(255, 255, 255, 0.93);
        width: ui_h(1060);
        height: ui_h(600);
        z-index: 8;
      }

      .close {
        position: absolute;
        right: ui_h(155);
        top: ui_h(80);
        z-index: 11;
        img {
          cursor: pointer;
          width: ui_h(130);
          height: ui_h(120);
        }
      }
    }

    .content-left-show-full-screen {
      width: ui_h(1870);
      height: ui_h(1033);
      position: relative;
      margin-top: ui_h(30);

      .menu {
        position: absolute;
        top: ui_h(160);
        left: ui_h(40);
        z-index: 11;
        .menu-item {
          width: ui_h(107 * 1.1);
          height: ui_h(107 * 1.1);
          text-align: center;
          padding-top: ui_h(15 * 1.1);
          cursor:pointer;

          img {
            height: ui_h(66 * 1.1);
            object-fit: contain;
          }
        }
        .menu-unchecked-bg {
          background: url('../../../assets/images/classroom/unchecked.png') center center no-repeat;
          background-size: contain;
        }
        .menu-checked-bg {
          background: url('../../../assets/images/classroom/checked.png') center center no-repeat;
          background-size: contain;
        }
      }

      .class-number {
        display: none;
      }

      .bg-show {
        width: 100%;
        height: 100%;
        position: absolute;
        background: url('../../../assets/images/classroom/bg-full-screen.png') center center no-repeat;
        background-size: contain;
        z-index: 10;
        pointer-events: none;
      }

      .show {
        position: absolute;
        left: ui_h(205);
        top: ui_h(86);
        background: linear-gradient(180deg, #B3CEE3 0%, #DAE9F3 100%);
        box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.18), 0px 0px 16px 0px rgba(255, 255, 255, 0.93);
        width: ui_h(1447);
        height: ui_h(808);
        z-index: 8;
      }
    }

    .button-full-screen {
      height: ui_h(100);
      margin-right: ui_h(40);
      cursor: pointer;
    }

    .content-right {
      position: relative;
      height: 90vh;
      overflow: auto;
      border-radius: 0px 0px 20px 20px;

      .rope {
        display: flex;
        justify-content: space-between;
        position: absolute;
        left: ui_h(93);
        width: ui_h(266);
        z-index: 9;

        .rope-long {
          width: ui_h(20);
          height: ui_h(113);
        }

        .rope-short {
          width: ui_h(20);
          height: ui_h(53);
        }
      }

      .rope-short-position {
        bottom: ui_h(-32);
      }

      .unit-list {
        padding-top: ui_h(93);
        width: ui_h(455);
        height: 100%;

        .unit {
          position: relative;
          width: ui_h(455);
          height: ui_h(185);
          margin-bottom: ui_h(13);
          padding: ui_h(51) ui_h(40);
          cursor: pointer;
        }

        .unit-title {
          width: 100%;
          height: 100%;
          font-size: ui_h(28);
          font-weight: 500;
          line-height: ui_h(40);
          word-break: break-all;
          color: #2D4A6F;
          @include ellipses(2)
        }

        .unit-checked {
          color: #664F3F;
        }

        .unit-bg {
          background: url('../../../assets/images/classroom/bg-unit.png') center center no-repeat;
          background-size: contain;
        }

        .unit-bg-end {
          background: url('../../../assets/images/classroom/bg-unit-end.png') center center no-repeat;
          background-size: contain;
        }

        .unit-bg-yellow {
          background: url('../../../assets/images/classroom/bg-unit-yellow.png') center center no-repeat;
          background-size: contain;
        }

        .unit-bg-yellow-end {
          background: url('../../../assets/images/classroom/bg-unit-yellowEnd.png') center center no-repeat;
          background-size: contain;
        }

      }
    }
    .close {
      position: absolute;
      right: ui_h(135);
      top: ui_h(1);
      z-index: 11;
      img {
        cursor: pointer;
        width: ui_h(170);
        height: ui_h(150);
      }
    }

    .content-right::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
  }

  .full-screen-none {
    display: none;
  }

  .footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    background: url('../../../assets/images/classroom/bg-classroom-bottom.png') bottom center no-repeat;
    background-size: contain;
    z-index: 10;
    img {
      width: ui_h(136);
      height: ui_h(99);
    }

    .button-full-screen-bottom {
      background: url('../../../assets/images/classroom/bg-button-bottomLeft.png') bottom center no-repeat;
      background-size: contain;
      height: ui_h(99);
      width: ui_h(232);
      padding-top: ui_h(40);
      padding-left: ui_h(48);
      cursor: pointer;

      img {
        width: ui_h(58);
        height: ui_h(44);
      }
    }

    .button-full-screen-enlarge {
      background: url('../../../assets/images/classroom/bg-button-bottomRight.png') bottom center no-repeat;
      background-size: contain;
      height: ui_h(77);
      width: ui_h(231);
      padding-left: ui_h(108);
      cursor: pointer;

      img {
        width: ui_h(78);
        height: ui_h(77);
      }
    }
  }
}
.footer-fullpage {
  position: fixed;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  background-size: contain;
  cursor: pointer;
  z-index: 40;

  .button-full-screen-enlarge {
      background: url('../../../assets/images/classroom/bg-button-bottomRight.png') bottom center no-repeat;
      background-size: contain;
      height: ui_h(77);
      width: ui_h(231);
      padding-left: ui_h(68);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 999;

      img {
        width: ui_h(78);
        height: ui_h(64);
      }
    }
}
</style>
