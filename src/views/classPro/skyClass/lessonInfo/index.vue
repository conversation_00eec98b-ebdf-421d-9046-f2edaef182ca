<template>
  <div class="lesson-info">
    <div class="header">
      <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="prePage" />
      <div class="back" @click="prePage">我的课程</div>
    </div>
    <div class="content">
      <div class="content__top">
        <div class="title">
          <div
            class="label"
            :class="{
              'red': subjectName(lessonInfo) === '语',
              'orange': subjectName(lessonInfo) === '英',
              'yellow': subjectName(lessonInfo) === '文'}"
          >
            {{ subjectName(lessonInfo) }}
          </div>
          <div class="text">{{ lessonInfo.course !== null ? lessonInfo.course.name || '' : '' }}</div>
        </div>
        <div class="flex">
          <img class="cover" :src="lessonInfo.cover || DefaultCover" alt="课程封面" />
          <div class="course-info">
            <div class="course-info__content">
              <div class="unit">第{{ lessonInfo.lessonPlan === null ? '' : lessonInfo.lessonPlan.seq || '' }}讲·{{ lessonInfo.lessonPlan === null ? '' : lessonInfo.lessonPlan.name || '' }}</div>
              <div>
                <div class="teacher" @click.stop="_showTeacherInfo(lessonInfo.teacherId)">
                  <div class="flex items-center">
                    <img class="teacher__header" :src="lessonInfo.lessonInfo2 ? lessonInfo.lessonInfo2.teacherAvart || DefaultAvatar : DefaultAvatar" alt="教师头像" />
                    <div>
                      <div class="teacher__title">老师姓名</div>
                      <div class="flex items-center">
                        <div class="teacher__name">{{ lessonInfo.lessonInfo2 && lessonInfo.lessonInfo2.teacherName || '' }}</div>
                        <div class="f16">简介></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="time">上课时间：{{ formatDot(lessonInfo.startTime) }}  {{ formatWeek(lessonInfo.startTime) }}  {{ formatHHmm(lessonInfo.startTime) }}</div>
            </div>
          </div>
          <div v-if="statusName(lessonInfo.lessonStatus) === '未开始'" class="button-group">
            <div class="classpro-btn-opacity button-group__blue margin-right" @click.stop="cancelCourse(lessonInfo)">取消预约</div>
            <div class="classpro-btn button-group__blue" @click.stop="toClassroom(lessonInfo)">进入教室</div>
          </div>
          <div v-if="statusName(lessonInfo.lessonStatus) === '进行中'" class="button-group">
            <div class="classpro-btn-disable classpro-btn button-group__blue margin-right" @click.stop="cancelCourse(lessonInfo)">取消预约</div>
            <div class="classpro-btn button-group__blue" @click.stop="toClassroom(lessonInfo)">进入教室</div>
          </div>
          <div v-if="statusName(lessonInfo.lessonStatus) === '已完成'" class="button-group">
            <div class="classpro-btn button-group__blue margin-right" @click.stop="toClassroom(lessonInfo)">查看回放</div>
            <div class="classpro-btn button-group__blue" @click.stop="openCourseware(lessonInfo)">查看课件</div>
          </div>
          <div v-if="statusName(lessonInfo.lessonStatus) === '缺课'" class="button-group">
            <div class="button-group__blue margin-right" :class="lessonInfo.newLessonId || lessonInfo.lessonDetail.source === 'PLAN' || !lessonInfo.canPlan ? 'classpro-btn-disable' : 'classpro-btn'" @click.stop="_applyLesson()">申请补课</div>
            <div class="classpro-btn button-group__blue" @click.stop="openCourseware(lessonInfo)">查看课件</div>
          </div>
        </div>
        <div class="state" :style="{background: `url(${statusBg(lessonInfo.lessonStatus)}) center center no-repeat`}">{{ statusName(lessonInfo.lessonStatus) }}</div>
      </div>
      <div class="content__bottom">
        <div style="display:flex;align-items:center">
          <img class="icon" src="@/assets/images/icon-lesson.png" alt="课程介绍图标" />
          <div class="course-dec-title">课程介绍</div>
        </div>
        <div class="course-dec" v-html="lessonInfo.course.subtitle || '暂无课程介绍' "></div>
      </div>
    </div>
    <book :show-book="showBook" :book-id="lessonInfo.courseId" :book-info="bookInfo" @closeBook="closeBook" />
    <cancel-dialog :dialog-visible="showCancel" @closeDialog="closeCancelDialog" @rightBtnCallback="cancelLesson" />
    <courseware :show-drawer="showCourseware" :document-id="documentId" @closeDrawer="showCourseware = false" />
    <apply-dialog :dialog-visible="showApply" :msg="applyMsg" @closeDialog="showApply = false" @showBook="showBook = true" />
  </div>
</template>

<script>
import Book from '../Book'
import ApplyDialog from '../ApplyDialog'
import CancelDialog from '../CancelDialog'
import Courseware from '../courseware'
import DefaultCover from '@/assets/images/default-cover.jpg'
import DefaultAvatar from '@/assets/images/profile.png'
import labelBlue from '@/assets/images/skyclass/label-blue.png'
import labelGrey from '@/assets/images/skyclass/label-grey.png'
import labelRed from '@/assets/images/skyclass/label-red.png'
import labelGreen from '@/assets/images/skyclass/label-green.png'
import { formatDot, formatWeek, formatHHmm } from '@/utils/date.js'
import { getLessonInfo, applyLesson } from '@/api/lesson-api.js'
import { cancelScheduledLesson } from '@/api/course-api.js'
export default {
  components: {
    CancelDialog,
    Courseware,
    Book,
    ApplyDialog
  },
  data () {
    return {
      DefaultAvatar,
      DefaultCover,
      labelBlue,
      labelGrey,
      labelRed,
      labelGreen,
      lessonInfo: {
        'lessonPlan': {},
        'course': {}
      },
      formatDot,
      formatWeek,
      formatHHmm,
      lessonId: this.$route.params.lessonId,
      showCancel: false,
      cancelling: false,
      cancelLessonId: 0,
      showCourseware: false,
      documentId: 0,
      showBook: false,
      bookInfo: {},
      showApply: false,
      applyMsg: '',
      canGoToClass: true
    }
  },
  created () {
    this._getLessonInfo()
  },
  methods: {
    prePage () {
      this.$router.push({ path: '/classpro/skyClass' })
    },
    _getLessonInfo () {
      const params = { 'userType': 'STUDENT', 'lessonId': this.lessonId }
      getLessonInfo(params).then(response => {
        this.lessonInfo = response.data
      })
    },
    statusName (status) {
      switch (status) {
        case 'ComingSoon':
          return '未开始'
        case 'Opening':
          return '进行中'
        case 'UnderReview':
        case 'Completed':
        case 'Unqualified':
          return '已完成'
        case 'TeachersAbsenteeism':
        case 'StudentsAbsenteeism':
          return '缺课'
        default:
          return ''
      }
    },
    statusBg (status) {
      switch (status) {
        case 'ComingSoon':
          return labelGrey
        case 'Opening':
          return labelBlue
        case 'UnderReview':
        case 'Completed':
        case 'Unqualified':
          return labelGreen
        case 'TeachersAbsenteeism':
        case 'StudentsAbsenteeism':
          return labelRed
        default:
          return labelBlue
      }
    },
    subjectName (item) {
      if (!item.lessonPlan || !item.lessonPlan.subjectType) return ''
      switch (item.lessonPlan.subjectType) {
        case 'CHINESE':
          return '语'
        case 'CHINESE_CLASS':
          return '文'
        case 'ENGLISH':
          return '英'
        case 'MATH':
          return '数'
        case 'MUSIC':
          return '音'
        case 'ART':
          return '美'
        default:
          return '语'
      }
    },
    toClassroom () {
      if (!this.lessonInfo.lessonInfo2 || !this.lessonInfo.lessonInfo2.roomUrl || !this.canGoToClass) {
        return
      }
      this.canGoToClass = false
      setTimeout(() => {
        this.canGoToClass = true
      }, 8000)
      this.$message.warning({
        message: '正在进入教室请勿重复点击',
        duration: 8000
      })
      window.open(this.lessonInfo.lessonInfo2.roomUrl, '_blank')
    },
    cancelCourse (item) {
      if (this.statusName(item.lessonStatus) === '进行中') return
      this.cancelLessonId = item.id
      this.showCancel = true
    },
    closeCancelDialog () {
      this.showCancel = false
    },
    cancelLesson () {
      if (this.cancelling) return
      this.cancelling = true
      const params = {
        id: this.cancelLessonId
      }
      cancelScheduledLesson(params).then(
        response => {
          this.$message.success('取消课程成功')
          this.showCancel = false
          this.cancelling = false
          this._getLessonInfo()
        },
        error => {
          console.log(error)
          this.showCancel = false
          this.cancelling = false
        }
      )
    },
    openCourseware (item) {
      this.showCourseware = true
      this.documentId = item.documentId
    },
    _applyLesson () {
      if (this.lessonInfo.lessonDetail.source === 'PLAN') {
        this.$message.error('该课程属于运营人员额外为您添加的课程，不能申请补课哦')
        return
      }
      if (this.lessonInfo.newLessonId) {
        this.$message.error('已申请补课')
        return
      }
      if (!this.lessonInfo.canPlan) {
        this.$message.error('补课时限已过')
        return
      }
      const params = {
        'lessonId': this.lessonInfo.id
      }
      applyLesson(params).then(response => {
        if (response.data.canPlan) {
          this.bookInfo = {
            id: this.lessonInfo.studentLessonPlan.studentsCourseId,
            courseId: this.lessonInfo.courseId,
            cover: this.lessonInfo.cover || '',
            name: this.lessonInfo.course && this.lessonInfo.course.name || '',
            type: this.lessonInfo.lessonDetail.teamId !== 0 ? 'TEAM' : 'MIN',
            lessonPlanId: this.lessonInfo.lessonPlanId,
            lessonId: this.lessonInfo.id
          }
          this.showApply = true
          this.applyMsg = response.data.msg
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    closeBook () {
      this.showBook = false
      this._getLessonInfo()
    },
    _showTeacherInfo (teacherId) {
      this.$emit('showTeacherInfo', teacherId)
    }
  }
}
</script>

<style lang="scss" scoped>
.lesson-info{
    height: calc(100% - 62px);
    padding: 15px 25px 0;

    .header {
        display: flex;
        align-items: center;
        img {
            width: 15px;
            height: 12px;
            cursor: pointer;
        }

        .back {
            font-size: var(--font-size-XXL);
            font-weight: 500;
            color: #0B0B0B;
            line-height: 33px;
            //margin-left: 17px;
            padding-left: 5px;
            cursor: pointer;
        }
    }

    .content {
        margin-top: 30px;
        height: calc(100% - 162px);
        &__top {
            width: 100%;
            height: 160px;
            background: #FFFFFF;
            box-shadow: 0px 4px 12px 7px rgba(62, 89, 253, 0.05);
            border-radius: 22px;
            position: relative;
            padding: 21px 41px 0;

            .cover {
              width: 170px;
              height: 100px;
              object-fit: cover;
              border-radius: 15px;
              margin-left:30px
            }

            .recent {
                font-size: 20px;
                font-weight: 500;
                color: #0B0B0B;
                line-height: 28px;
                margin-bottom: 14px;
            }

            .state {
                right: -6px;
                top: 53px;
            }
        }

        &__bottom {
          width: 100%;
          //height: 378px;
          //height: calc(100% - 62px  - 160px);
          height: calc(100% - 35px);
          background: #FFFFFF;
          box-shadow: 0px 4px 12px 7px rgba(62, 89, 253, 0.05);
          border-radius: 22px;
          margin-top: 35px;
          padding: 26px 44px 0;

          .icon {
            width: 20px;
            height: 20px;
          }

          .course-dec-title {
            font-size: 20px;
            font-weight: 500;
            color: #0B0B0B;
            margin-left: 7px;
          }

          .course-dec {
            width: 100%;
            //height: 264px;
            //height: 100%;
            height: calc(100% - 60px);
            border-radius: 5px;
            border: 2px solid rgba(31, 102, 255, 0.17);
            margin-top: 25px;
            font-size: 16px;
            font-weight: 400;
            color: #0B0B0B;
            line-height: 26px;
            padding: 20px 28px;
            overflow: scroll;
          }

          .course-dec::-webkit-scrollbar{  display: none; }
        }

        .title {
            display: flex;
            align-items: center;
            margin-bottom: 9px;

            .label {
                width: 15px;
                height: 15px;
                border-radius: 6px;
                background: #2787FF;
                font-size: 12px;
                font-weight: 500;
                color: #FFFFFF;
                line-height: 20px;
                text-align: center;
            }

            .red {
              background: #FF675B;
            }

            .yellow {
              background: #AF7A40;
            }

            .orange {
              background: #FF8E44;
            }

            .text {
                font-size: var(--font-size-XL);
                font-weight: 500;
                color: #0B0B0B;
                margin-left: 10px;
                @include ellipses(1);
            }
        }

        .course-info {
          width: calc(100% - 160px - 150px);
          height: 100px;
            &__content {
                padding-left: 30px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 100%;
                .unit {
                  font-size: var(--font-size-L);
                  font-weight: 500;
                  color: #0B0B0B;
                }

                .time {
                  font-size: var(--font-size-L);
                  font-weight: 500;
                  color: #F97E56;
                }

                .teacher {
                    font-size: var(--font-size-L);
                    font-weight: 400;
                    color: #0B0B0B;
                    display: inline-block;
                    cursor: pointer;

                    &__header {
                        width: 45px;
                        height: 45px;
                        border-radius: 100px;
                        margin-right: 14px;
                        object-fit: cover;
                    }

                    &__title {
                      font-size: var(--font-size-L);
                      font-weight: 500;
                      color: #0B0B0B;
                      line-height: 22px;
                      margin-bottom: 5px;
                    }

                    &__name {
                        font-size: var(--font-size-L);
                        font-weight: 400;
                        color: #0B0B0B;
                        line-height: 22px;
                        flex: 1;
                        margin-right: 25px;
                        @include ellipses(1);
                    }

                    .f16 {
                        color: #0B0B0B;
                    }

                    &:hover {
                      .f16 {
                        color: blue
                      }
                    }
                }
            }
        }

        .state {
            position: absolute;
            width: 80px;
            height: 37px;
            font-size: 14px;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 27px;
            text-align: center;
            background-size: contain !important;
        }

        .button-group {
            display: flex;
            width: 150px;
            margin-top: auto;
            &__grey {
                width: 110px;
                height: 30px;
                background: #BFBFBF;
                box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), inset 1px 1px 1px 0px #E7E7E7, inset -1px -1px 1px 0px rgba(129, 129, 134, 0.32);
                border-radius: 22px;
                font-size: var(--font-size-L);
                font-weight: 500;
                color: #FFFFFF;
                line-height: 44px;
                text-align: center;
                cursor: pointer;
                margin-right: 25px;
            }

            &__blue {
                width: 108px;
                height: 30px;
                border-radius: 23px;
                font-size: var(--font-size-L);
                font-weight: 500;
                line-height: 45px;
            }

            .margin-right {
                margin-right: 25px;
            }
        }
    }

    .loading-text {
      text-align: center;
      padding-bottom: 10px;
      font-size: 16px;
    }
}
</style>
