<template>
  <el-drawer
    title=""
    :visible.sync="showDrawer"
    :with-header="false"
    :direction="'btt'"
    :show-close="true"
    :size="'100%'"
    :custom-class="'courseware-drawer'"
    :close-on-press-escape="false"
  >
    <div class="top">
      <svg-icon icon-class="close" class-name="close-svg" @click="$emit('closeDrawer')" />
    </div>
    <div class="bottom">
      <div class="menu">
        <div
          v-for="(item, index1) in coursewareList"
          :key="item.id"
          class="menu-courseware-item flex"
          :class="{'menu-courseware-item-click'
            :index1 === currentIndex}"
        >
          <p>{{ index1 + 1 }}</p>
          <div class="cover">
            <img :src="item.preview || defaultImg" alt="" @click="changeIndex(index1)" />
          </div>
        </div>
      </div>
      <div class="show-courseware">
        <div id="ppt" v-loading="iframeLoading" style="position:relative">
          <iframe
            id="iframe"
            ref="iframe"
            :src="iframeURL"
            width="100%"
            height="100%"
            frameborder="0"
            scrolling="auto"
          ></iframe>
          <img class="full-screen" :src="[isFullAllScreen ? narrow : enlarge]" alt="放大缩小" @click="fullScreen('ppt')" />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getLessonPrepare } from '@/api/lesson-api.js'
import narrow from '@/assets/images/skyclass/narrow.png'
import enlarge from '@/assets/images/skyclass/enlarge.png'
import screenfull from 'screenfull'
export default {
  props: {
    showDrawer: {
      type: Boolean,
      require: true,
      default: false
    },
    documentId: {
      type: Number,
      require: true,
      default: 0
    }
  },
  data () {
    return {
      coursewareList: [],
      currentIndex: 0,
      iframeLoading: true,
      iframeURL: '',
      isFullAllScreen: false,
      narrow,
      enlarge
    }
  },
  watch: {
    showDrawer: {
      handler: function (val) {
        if (val) {
          this.currentIndex = 0
          this.coursewareList = []
          this.iframeLoading = true
          this.iframeURL = ''
          this._getLessonPrepare()
        }
      },
      immediate: true
    }
  },
  mounted () {
    window.addEventListener('message', this.handleMessage)
    screenfull.on('change', this.screenfullChange)
  },
  destroyed () {
    window.removeEventListener('message', this.handleMessage)
    screenfull.off('change', this.screenfullChange)
  },
  methods: {
    _getLessonPrepare () {
      const params = {
        'documentId': this.documentId
      }
      getLessonPrepare(params).then(
        response => {
          if (response.data.length > 0) {
            const lessonPrepareList = response.data.filter(item => item.resourceType === 'COURSE_WARE')
            const coursewareStr = lessonPrepareList[0].mediaFile.documentScene.scenes
            this.coursewareList = JSON.parse(coursewareStr).progress.convertedFileList
            this.$nextTick(() => {
              this.iframeURL = `${process.env.VUE_APP_DOC_URL}/#/classroom/preview/1v1/${lessonPrepareList[0].mediaFileId}/54`
              this.pptTotal = 0
              this.pptIndex = -1
              const iframe = document.querySelector('#iframe')
              iframe.onload = () => {
                // iframe加载完毕以后执行操作
                this.iframeLoading = false
              }
            })
          }
        })
    },
    changeIndex (index) {
      this.currentIndex = index
      this.$refs.iframe.contentWindow.postMessage(JSON.stringify({ type: 'pagechange', data: { currentPage: this.currentIndex + 1 }}), '*')
    },
    handleMessage (event) {
      try {
        const data = JSON.parse(event.data)
        if (data.type === 'pageChange') {
          this.currentIndex = +data.data.currentPage
        }
      } catch (err) {
        console.log(err)
      }
    },
    fullScreen (domName) {
      if (this.isFullAllScreen) {
        if (screenfull.isEnabled && screenfull.isFullscreen) {
          screenfull.exit()
          this.isFullAllScreen = false
        }
      } else {
        const element = document.querySelector('#' + domName) // 获取dom
        if (screenfull.isEnabled) {
          this.isFullAllScreen = true
          screenfull.toggle(element)
        }
      }
    },
    screenfullChange () {
      this.isFullAllScreen = screenfull.isFullscreen
    }
  }
}
</script>

<style lang='scss' scoped>
.top {
    height: 100px;
    display: flex;
    padding: 48px 50px 0 0;
    .close-svg {
        width: 36px;
        height: 36px;
        margin-left: auto;
        cursor: pointer;
    }
}

.bottom {
    height: calc(100% - 101px);
    width: 100%;
    background: #FFFFFF;
    border-radius: 20px 20px 0px 0px;
    padding: 20px 24px 0 20px;
    display: flex;

    .menu {
        height: 100%;
        width: 200px;
        overflow: scroll;
        @include noScrollBar;

        .menu-courseware-item {
            width: 200px;
            margin-bottom: 17px;
            position: relative;

            p {
                font-size: 16px;
                font-weight: 500;
                color: black;
                padding-right: 14px;
                margin: 0px;
            }

            .cover {
                position: relative;
                width: 200px;
                height: 120px;

                img {
                    width: 100%;
                    height: 100%;
                    border-radius: 10px;
                    position: absolute;
                }

                .hint {
                    position: absolute;
                    background: rgba(0, 0, 0, 0.5);
                    border-radius: 10px 0px 9px 0px;
                    padding: 6px 6px 0px 8px;
                    right: 0px;
                    bottom: 0px;

                    .text {
                        font-size: 12px;
                        font-weight: 400;
                        color: #FFFFFF;
                        margin-bottom: 3px;
                    }

                    .circle {
                        width: 4px;
                        height: 4px;
                        background: #649FFD;
                        margin-right: 3px;
                        border-radius: 100px;
                    }
                }
            }
        }

        .menu-courseware-item-click {
            p {
                color: #1F66FF
            }

            img {
                border: 2px solid #1F66FF
            }
        }
    }

    .show-courseware {
        width: calc(100% - 200px);
        //height: 700px;
        padding-left: 10px;

        #ppt {
            width: 100%;
            height: 100%;
            border-radius: 10px;
        }
        .full-screen {
            width: 34px;
            height: 34px;
            position: absolute;
            right: 6px;
            bottom: 6px;
            text-align: center;
            object-fit: contain;
        }
    }
}
</style>

<style lang='scss'>
.courseware-drawer {
    width: 100%;
    height: 100%;
}
</style>
