<template>
  <div class="book">
    <custom-dialog
      :show.sync="showBook"
      :width="'60%'"
      :top="'2vh'"
      class="book-dialog"
      @dialogClosed="bookDialogClosed"
    >
      <template v-if="showBook" v-slot:content>
        <div class="header flex justify-center items-center">
          <div class="book-type-tab flex justify-between">
            <div
              class="book-tab"
              :class="[{ active: bookType === 0 }]"
              style="padding-left: 0;"
              @click="setBookType(0)"
            >
              <span>按时间</span>
            </div>
            <div
              class="book-tab"
              :class="[{ active: bookType === 1 }]"
              style="padding-left: 0;"
              @click="setBookType(1)"
            >
              <span>按老师</span>
            </div>
          </div>
          <div v-if="bookType === 0" class="month">
            {{ formatM(activeDay) }}月
          </div>
        </div>

        <div v-if="bookType === 0" class="book-calendar">
          <div class="calendar-container">
            <div class="book-calendar_weeks w-full flex justify-between">
              <div class="week flex-grow">周一</div>
              <div class="week flex-grow">周二</div>
              <div class="week flex-grow">周三</div>
              <div class="week flex-grow">周四</div>
              <div class="week flex-grow">周五</div>
              <div class="week flex-grow">周六</div>
              <div class="week flex-grow">周日</div>
            </div>

            <el-calendar
              class="calendar"
              :class="[{ expand: calExpand }]"
              :range="range"
            >
              <template slot="dateCell" slot-scope="{ data }">
                <div
                  class="calendar-day w-full h-full flex justify-center items-center"
                  :class="[{ inactive: isPassedDay(data.day) }]"
                  @click="selectDay(data)"
                >
                  <div
                    class="calendar-day__text"
                    :class="[{ active: activeDay === data.day }]"
                  >
                    {{ formatD(data.day) }}
                  </div>
                </div>
              </template>
            </el-calendar>
            <div
              class="toggle-bar flex justify-center items-center"
              :class="[{ expand: calExpand }]"
            >
              <div class="h-line"></div>
              <svg-icon
                icon-class="book-arrow"
                class-name="arrow"
                @click="calExpand = !calExpand"
              />
              <div class="h-line"></div>
            </div>
          </div>

          <div class="time-count">今日可约：{{ timesOfToday }}个时间段</div>

          <div class="times-container">
            <el-scrollbar
              v-if="timesList.length > 0"
              class="scrollbar-wrapper"
              style="height: 100%;"
            >
              <div class="times-content">
                <template v-if="timesAMList.length > 0">
                  <div class="time-tag am">上午</div>
                  <div class="time-list am-times">
                    <div
                      v-for="(item, index) in timesAMList"
                      :key="item.key + '-' + index"
                      class="time-item flex flex-col justify-center"
                      :class="[{ scheduled: item.scheduled }]"
                      @click="showTeacherList(item)"
                    >
                      <div class="time">{{ getClassTimePeriod(item.key) }}</div>
                      <div v-if="item.scheduled" class="scheduled">已约课</div>
                      <div v-if="!item.scheduled" class="count">
                        有{{ item.value }}位老师可选
                      </div>
                    </div>
                  </div>
                </template>

                <template v-if="timesPMList.length > 0">
                  <div class="time-tag pm">下午</div>
                  <div class="time-list pm-times">
                    <div
                      v-for="(item, index) in timesPMList"
                      :key="item.key + '-' + index"
                      class="time-item flex flex-col justify-center"
                      :class="[{ scheduled: item.scheduled }]"
                      @click="showTeacherList(item)"
                    >
                      <div class="time">{{ getClassTimePeriod(item.key) }}</div>
                      <div v-if="item.scheduled" class="scheduled">已约课</div>
                      <div v-if="!item.scheduled" class="count">
                        有{{ item.value }}位老师可选
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </el-scrollbar>

            <div
              v-else
              class="no-teacher flex flex-col justify-center items-center"
            >
              <svg-icon icon-class="no-data" class-name="icon" />
              <div class="tip">今天没有可约的时间段，换一天试试吧～</div>
            </div>
          </div>
        </div>

        <div v-if="bookType === 1" class="book-by-teacher">
          <div class="search-box">
            <el-input
              v-model="search"
              placeholder="搜索老师"
              class="search-input"
            >
              <svg-icon
                slot="prefix"
                icon-class="search"
                class-name="search-icon"
              />
            </el-input>
            <!-- <div><el-checkbox v-model="listType" @change="getTeacherListByCourseId">约过的老师</el-checkbox></div> -->
            <el-dropdown trigger="click" @command="changeListType">
              <div class="dropdown-choose">
                {{ listType === 'SCHEDULED'? '预约过的老师' : listType === 'COLLECTION' ? '收藏的老师' : '全部' }}<i class="el-icon-arrow-down el-icon--right"></i>
              </div>
              <el-dropdown-menu slot="dropdown" class="book-dropdown">
                <el-dropdown-item command="">全部</el-dropdown-item>
                <el-dropdown-item command="SCHEDULED">预约过的老师</el-dropdown-item>
                <el-dropdown-item command="COLLECTION">收藏的老师</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>

          <div class="teacher-container">
            <el-scrollbar v-if="teachersListByTeacher.length > 0" class="scrollbar-wrapper" style="height: 100%;">
              <div class="teacher-list">
                <div
                  v-for="item in teachersListByTeacher"
                  :key="item.id"
                  class="teacher-item shadow-box flex justify-between items-end"
                >
                  <div class="deco"></div>
                  <div class="left" @click="_showTeacherInfo(item)">
                    <div class="name">{{ item.displayName }}</div>
                    <div class="flex items-center" style="flex: 1;padding-right: 30px;">
                      <div class="avatar">
                        <img
                          :src="item.avatar || dfTAvatar"
                          alt="teacher-avatar"
                          class="h-full w-full object-cover"
                        />
                      </div>

                      <!-- <div class="flex flex-col justify-center">
                        <div class="flex items-center">
                          <svg-icon
                            icon-class="location"
                            class-name="location"
                          />
                          <div class="country">{{ country(item.country) }}</div>
                        </div>

                        <div class="flex  items-center">
                          <el-rate
                            v-model="item.avgStar"
                            disabled
                            allow-half
                            show-text
                            :texts="[]"
                            class="score"
                            disabled-void-color="#CDCACA"
                          />
                          <div class="f16">简介></div>
                        </div>
                      </div> -->
                      <div class="flex flex-col justify-center" style="flex: 1">
                        <el-rate
                          v-model="item.avgStar"
                          disabled
                          allow-half
                          show-text
                          :texts="[]"
                          class="score mb12"
                          disabled-void-color="#CDCACA"
                        />

                        <div v-if="item.tags" class="label-list mb12">
                          <div v-for="tag in item.tags" :key="tag.id" class="label">{{ tag.name || '' }}</div>
                        </div>

                        <div v-if="item.desc" class="flex">
                          <div class="describe">{{ item.desc }}</div>
                          <div class="describe-arrow">></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="right h-full flex flex-col items-end">
                    <el-button
                      class="secondary-btn choose-btn"
                      :loading="loadingTeacherTime"
                      @click="getTeacherTime(item)"
                    >选择</el-button>
                  </div>
                </div>
              </div>
            </el-scrollbar>

            <div
              v-else
              class="no-teacher w-full h-full flex flex-col justify-center items-center"
            >
              <svg-icon icon-class="no-data" class-name="icon" />
              <div class="tip">今天没有可约的老师，换一天试试吧～</div>
            </div>
          </div>
        </div>
      </template>
    </custom-dialog>

    <custom-dialog
      :show.sync="showTimeTeacher"
      :width="'56%'"
      :top="'10vh'"
      class="time-teacher-dialog"
      @dialogClosed="showTimeTeacher = false"
    >
      <template v-if="showTimeTeacher" v-slot:content>
        <div class="time-teacher-container">
          <el-scrollbar class="scrollbar-wrapper" style="height: 100%;">
            <div class="time-teacher-content">
              <div
                v-for="item in teacherListByTime"
                :key="item.id"
                class="time-teacher-item shadow-box flex justify-between"
              >
                <div class="deco"></div>
                <div class="left" @click="_showTeacherInfo(item)">
                  <div class="name">{{ item.displayName }}</div>
                  <div class="flex items-center">
                    <div class="avatar">
                      <img
                        :src="item.avatar || dfTAvatar"
                        alt="teacher-avatar"
                        class="h-full w-full object-cover"
                      />
                    </div>

                    <div class="flex flex-col justify-center" style="flex: 1;padding-right: 30px;">
                      <!-- <div class="flex items-center">
                        <svg-icon icon-class="location" class-name="location" />
                        <div class="country">{{ country(item.country) }}</div>
                      </div>

                      <div class="flex items-center mt15">
                        <el-rate
                          v-model="item.avgStar"
                          disabled
                          allow-half
                          show-text
                          :texts="[]"
                          class="score"
                          disabled-void-color="#CDCACA"
                        />
                      </div>

                      <div class="f16">简介></div> -->

                      <el-rate
                        v-model="item.avgStar"
                        disabled
                        allow-half
                        show-text
                        :texts="[]"
                        class="score mb12"
                        disabled-void-color="#CDCACA"
                      />

                      <div v-if="item.tags" class="label-list mb12">
                        <div v-for="tag in item.tags" :key="tag.id" class="label">{{ tag.name || '' }}</div>
                      </div>

                      <div v-if="item.desc" class="flex">
                        <div class="describe">{{ item.desc }}</div>
                        <div class="describe-arrow">></div>
                      </div>

                    </div>
                  </div>
                </div>

                <div class="right flex flex-col justify-between items-end">
                  <div class="time">
                    {{ formatMMDDDot(item.lessonStartTime) }}
                    {{ formatWeek(item.lessonStartTime) }}
                    {{ formatHHmm(item.lessonStartTime) }}
                  </div>
                  <el-button
                    class="secondary-btn choose-btn"
                    @click="handleConfirm(item)"
                  >选择</el-button>
                </div>
              </div>
            </div>
          </el-scrollbar>
          <svg-icon
            icon-class="back-arrow-d"
            class-name="arrow"
            @click="showTimeTeacher = false"
          />
        </div>
      </template>
    </custom-dialog>

    <custom-dialog
      :show.sync="showTeacherTime"
      :width="'30%'"
      :top="'9vh'"
      class="teacher-time-dialog"
      @dialogClosed="showTeacherTime = false"
    >
      <template v-if="showTeacherTime" v-slot:content>
        <div class="teacher-time-container">
          <div class="teacher-item shadow-box flex justify-between items-end">
            <div class="deco"></div>
            <div class="left" @click="_showTeacherInfo(selectedTeacher)">
              <div class="name">{{ selectedTeacher.displayName }}</div>
              <div class="flex">
                <div class="avatar">
                  <img
                    :src="selectedTeacher.avatar || dfTAvatar"
                    alt="teacher-avatar"
                    class="h-full w-full object-cover"
                  />
                </div>

                <div class="flex flex-col justify-center" style="flex: 1">
                  <!-- <div class="flex items-center">
                    <svg-icon
                      icon-class="location"
                      class-name="location"
                    />
                    <div class="country">{{ country(selectedTeacher.country) }}</div>
                  </div> -->

                  <!-- <div class="flex">
                    <el-rate
                      v-model="selectedTeacher.avgStar"
                      disabled
                      allow-half
                      show-text
                      :texts="[]"
                      class="score"
                      disabled-void-color="#CDCACA"
                    />
                    <div class="f16">简介></div>
                  </div> -->

                  <el-rate
                    v-model="selectedTeacher.avgStar"
                    disabled
                    allow-half
                    show-text
                    :texts="[]"
                    class="score mb12"
                    disabled-void-color="#CDCACA"
                  />

                  <div v-if="selectedTeacher.tags" class="label-list mb12">
                    <div v-for="tag in selectedTeacher.tags" :key="tag.id" class="teacher-label">{{ tag.name || '' }}</div>
                  </div>

                  <div v-if="selectedTeacher.desc" class="flex">
                    <div class="describe">{{ selectedTeacher.desc }}</div>
                    <div class="describe-arrow">></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="label">开放时间段：</div> -->
          <div class="times-container">
            <el-scrollbar class="scrollbar-wrapper" style="height: 100%;">
              <div class="times-list">
                <div
                  v-for="(item, index) in teacherAvailableTime"
                  :key="item.id + '-' + index"
                  class="time-item flex flex-col justify-center"
                  @click="handleConfirmBySelectTeacher(item)"
                >
                  <div class="time">{{ formatMMDDDot(item.startMillis) }} {{ formatWeek(item.startMillis) }} {{ getClassTimePeriod(item.startMillis) }}</div>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </template>
    </custom-dialog>

    <custom-dialog
      :show.sync="showConfirmBook"
      :width="'30%'"
      :top="'20vh'"
      title="确认约课信息"
      class="confirm-book-dialog"
      @dialogClosed="confirmDialogClosed"
    >
      <template v-if="showConfirmBook" v-slot:content>
        <div class="confirm-book-container">
          <img class="bg-top" src="@/assets/images/bg-top.png" />
          <div class="tip">请仔细核对上课信息后确认哦～</div>

          <course-item :course="confirmInfo" @timeChanged="handleUserChangeTime" />

          <div class="confirm-btn">
            <el-button
              class="secondary-btn choose-btn"
              @click="showConfirmBook=false"
            >取消</el-button>
            <el-button
              type="primary"
              class="primary-btn choose-btn classpro-btn"
              :loading="booking"
              @click="handleBook"
            >确定</el-button>
          </div>
        </div>
      </template>
    </custom-dialog>

    <custom-dialog
      :show.sync="showTimeAdjustment"
      :width="'280px'"
      :top="'20vh'"
      title="请调整约课时间"
      class="time-adj-dialog"
    >
      <template v-if="showTimeAdjustment" v-slot:content>
        <div class="time-adj-container">
          <div class="date">09.03 周五</div>

          <div class="time-picker-container">
            <el-time-select
              v-model="time"
              class="adj-time-picker"
              :picker-options="{
                start: '10:30',
                step: '00:05',
                end: '11:30'
              }"
              :editable="false"
              :clearable="false"
              placeholder=""
            />
            <i class="el-icon-arrow-down picker-arrow"></i>
          </div>

          <div class="flex justify-center">
            <el-button
              type="primary"
              class="primary-btn choose-btn"
              :loading="booking"
              @click="handleBook"
            >确认调整</el-button>
          </div>
        </div>
      </template>
    </custom-dialog>

    <notify-dialog
      :show.sync="showBookSuc"
      title="约课成功！"
      icon="dialog-suc"
      right-btn="知道了"
      @primary="handleBookSuc"
    >
      <template v-slot:content>
        <div class="container-suc">
          <img src="@/assets/images/networkcheck/complete.png" alt="" />
          <span>记得准时进入教室，跟缤果一起上课哦～</span>
        </div>
      </template>
    </notify-dialog>
  </div>
</template>

<script>
import {
  getStartWeek,
  addDays,
  formatD,
  getTimestamp,
  formatYYYYMMDD,
  getStartTimeOfDay,
  getEndTimeOfDay,
  formatA,
  formatHHmm,
  getClassTimePeriod,
  formatWeek,
  formatMMDDDot,
  formatM
} from '@/utils/time'
import {
  teacherList,
  scheduleTeacher,
  teacherListByCourseId,
  getTeacherAvailableTime
} from '@/api/course-api'
import CustomDialog from '../CustomDialog'
import NotifyDialog from '../CustomDialog/NotifyDialog'
import dfTAvatar from '@/assets/images/dashboard/defaultAvatar.png'
import CourseItem from './CourseItem'
export default {
  components: {
    CustomDialog,
    NotifyDialog,
    CourseItem
  },
  props: {
    showBook: {
      type: Boolean,
      require: true,
      default: () => false
    },
    bookInfo: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data () {
    return {
      bookType: 0,
      activeDay: formatYYYYMMDD(new Date()),
      calExpand: false,
      showTimeTeacher: false,
      showConfirmBook: false,
      showBookSuc: false,
      showTeacherTime: false,
      showTimeAdjustment: false,
      timesOfToday: 0,
      teachersListByTime: [],
      teachersListByTeacher: [],
      timesList: [],
      scheduledList: [],
      teacherAvailableTime: [],
      selectedTime: null,
      selectedTeacher: null,
      confirmInfo: null,
      booking: false,
      loadingTeacherTime: false,
      search: '',
      searchTimer: null,
      time: new Date(),
      formatD,
      getClassTimePeriod,
      formatWeek,
      formatHHmm,
      formatMMDDDot,
      formatM,
      dfTAvatar,
      listType: undefined
    }
  },
  computed: {
    range () {
      const now = new Date()
      const start = getStartWeek(now)
      const end = addDays(now, 14)
      console.log([start, end])
      return [start, end]
    },
    timesAMList () {
      return this.timesList.filter(item => formatA(item.key) === 'AM')
    },
    timesPMList () {
      return this.timesList.filter(item => formatA(item.key) === 'PM')
    },
    teacherListByTime () {
      return this.teachersListByTime.filter(
        item => item.lessonStartTime === this.selectedTime
      )
    }
  },
  watch: {
    showBook (val) {
      if (val) {
        this.getTeacherList()
        this.$bus.$on('updateData', () => {
          if (this.bookType === 0) {
            this.getTeacherList()
          } else if (this.bookType === 1) {
            this.search = ''
            this.getTeacherListByCourseId()
          }
        })
      }
      if (!val) {
        console.log('关闭约课')
        this.$bus.$off('updateData')
      }
    },
    search (val) {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.getTeacherListByCourseId()
      }, 500)
    }
  },
  methods: {
    closeBookDialog () {
      this.$emit('closeBook')
    },
    confirmDialogClosed () {
      this.confirmInfo = null
      this.showConfirmBook = false
    },
    bookDialogClosed () {
      this.bookType = 0
      this.activeDay = formatYYYYMMDD(new Date())
      this.calExpand = false
      this.search = ''
      this.closeBookDialog()
    },
    isPassedDay (date) {
      const now = new Date()
      const today = formatYYYYMMDD(now)
      return getTimestamp(date) - getTimestamp(today) < 0
    },
    setBookType (i) {
      if (this.bookType === i) return
      this.bookType = i
      if (i === 0) {
        this.getTeacherList()
      } else if (i === 1) {
        this.search = ''
        this.getTeacherListByCourseId()
      }
    },
    selectDay (data) {
      if (this.isPassedDay(data.day)) {
        return false
      }
      this.activeDay = data.day
      this.getTeacherList()
    },
    async getTeacherList () {
      const day = this.activeDay
      const startTime = getStartTimeOfDay(day)
      const endTime = getEndTimeOfDay(day)
      const params = {
        courseId: this.bookInfo.courseId,
        startTime,
        endTime,
        pageSize: 1000,
        pageNumber: 1
      }
      try {
        this.timesList = []
        this.timesOfToday = 0
        const { data } = await teacherList(params)

        for (var teacher of data.content) {
          if (teacher.teachLv && teacher.teachLv !== '') {
            let teachLvName
            if (teacher.teachLv.indexOf('1') > -1 && teacher.teachLv.indexOf('4') > -1) {
              teachLvName = '全龄段'
            } else if (teacher.teachLv.indexOf('1') > -1) {
              teachLvName = '低龄段'
            } else if (teacher.teachLv.indexOf('4') > -1) {
              teachLvName = '高龄段'
            }
            const teachLvJson = { 'id': 0, 'name': teachLvName }
            let newTeacherTags = [teachLvJson]
            if (teacher.tags && teacher.tags.length > 0) {
              newTeacherTags = newTeacherTags.concat(teacher.tags)
            }
            teacher.tags = newTeacherTags
          }
        }

        const teacherOfDay = data.content || []
        const teacherOfDaySort = teacherOfDay.sort(
          (a, b) => a.lessonStartTime - b.lessonStartTime
        )
        const teacherOfDayMap = new Map()

        for (const item of teacherOfDaySort) {
          const time = item.lessonStartTime
          if (teacherOfDayMap.has(time)) {
            teacherOfDayMap.set(time, teacherOfDayMap.get(time) + 1)
          } else {
            teacherOfDayMap.set(time, 1)
          }
        }

        this.teachersListByTime = teacherOfDaySort
        this.timesList = Array.from(teacherOfDayMap, ([key, value]) => ({
          key,
          value
        }))

        const scheduledList = teacherOfDay.filter(
          item => item.scheduled === true
        )

        this.timesList.forEach(item => {
          item.scheduled = false
          if (scheduledList.find(i => i.lessonStartTime === item.key)) {
            item.scheduled = true
          }
        })

        this.timesOfToday = this.timesList.filter(item => !item.scheduled).length
        this.scheduledList = scheduledList
        console.log(this.timesList)
      } catch (error) {
        return error
      }
    },
    async getTeacherListByCourseId () {
      const params = {
        courseId: this.bookInfo.courseId,
        searchName: this.search || null,
        pageSize: 1000,
        pageNumber: 1,
        listType: this.listType
      }
      try {
        const { data } = await teacherListByCourseId(params)
        const teacherList = data.content || []
        for (var teacher of teacherList) {
          if (teacher.teachLv && teacher.teachLv !== '') {
            let teachLvName
            if (teacher.teachLv.indexOf('1') > -1 && teacher.teachLv.indexOf('4') > -1) {
              teachLvName = '全龄段'
            } else if (teacher.teachLv.indexOf('1') > -1) {
              teachLvName = '低龄段'
            } else if (teacher.teachLv.indexOf('4') > -1) {
              teachLvName = '高龄段'
            }
            const teachLvJson = { 'id': 0, 'name': teachLvName }
            let newTeacherTags = [teachLvJson]
            if (teacher.tags && teacher.tags.length > 0) {
              newTeacherTags = newTeacherTags.concat(teacher.tags)
            }
            teacher.tags = newTeacherTags
          }
        }
        this.teachersListByTeacher = teacherList
      } catch (error) {
        return error
      }
    },
    async getTeacherTime (data) {
      const params = {
        teacherId: data.id
      }
      this.selectedTeacher = data
      try {
        const { data } = await getTeacherAvailableTime(params)
        this.teacherAvailableTime = data || []
        this.showTeacherTime = true
      } catch (error) {
        return error
      }
    },
    showTeacherList (data) {
      if (data.scheduled) return false
      this.selectedTime = data.key
      this.showTimeTeacher = true
    },
    handleConfirm (data) {
      const bookCourseInfo = {
        ...this.bookInfo,
        avatar: data.avatar || '',
        teacher: data.displayName || '',
        teacherId: data.id,
        time: this.selectedTime,
        availableTimeId: data.availableTimeId
      }
      this.confirmInfo = bookCourseInfo
      console.log(bookCourseInfo)
      this.showConfirmBook = true
    },
    handleConfirmBySelectTeacher (data) {
      const selectedTeacher = this.selectedTeacher
      const bookCourseInfo = {
        ...this.bookInfo,
        avatar: selectedTeacher.avatar || '',
        teacher: selectedTeacher.displayName || '',
        teacherId: selectedTeacher.id,
        time: data.startMillis,
        availableTimeId: data.id
      }
      this.confirmInfo = bookCourseInfo
      console.log(bookCourseInfo)
      this.showConfirmBook = true
    },
    handleUserChangeTime (data) {
      this.confirmInfo = {
        ...this.confirmInfo,
        adjFlag: 1,
        adjTime: data
      }

      console.log(this.confirmInfo)
    },
    async handleBook () {
      let time = `${formatYYYYMMDD(this.confirmInfo.time)} ${formatHHmm(this.confirmInfo.time)}`
      if (this.confirmInfo.adjFlag) {
        time = `${formatYYYYMMDD(this.confirmInfo.time)} ${this.confirmInfo.adjTime}`
      }
      this.booking = true
      var params = {}
      if (this.confirmInfo.lessonPlanId && this.confirmInfo.lessonId) {
        params = {
          teacherId: this.confirmInfo.teacherId,
          studentCourseId: this.confirmInfo.id,
          startTime: time,
          availableTimeId: this.confirmInfo.adjFlag ? this.confirmInfo.availableTimeId : null,
          lessonPlanId: this.confirmInfo.lessonPlanId,
          lessonId: this.confirmInfo.lessonId
        }
      } else {
        params = {
          teacherId: this.confirmInfo.teacherId,
          studentCourseId: this.confirmInfo.id,
          startTime: time,
          availableTimeId: this.confirmInfo.adjFlag ? this.confirmInfo.availableTimeId : null,
          lessonPlanId: this.confirmInfo.lessonPlanId
        }
      }
      try {
        await scheduleTeacher(params)
        this.getTeacherList()
        this.booking = false
        this.showConfirmBook = false
        this.showTimeTeacher = false
        this.showTeacherTime = false
        this.bookDialogClosed()
        this.confirmInfo = null
        this.showBookSuc = true
      } catch (error) {
        this.booking = false
        return error
      }
    },
    handleBookSuc () {
      this.showBookSuc = false
    },
    country (countryName) {
      switch (countryName) {
        case 'China':
          return '中国'
        default:
          return countryName
      }
    },
    _showTeacherInfo (item) {
      this.$emit('showTeacherInfo', item.id)
    },
    changeListType (listType) {
      this.listType = listType
      this.getTeacherListByCourseId()
    }
  }
}
</script>

<style lang="scss">
.book-dialog {
  .el-dialog__body {
    padding: 24px 0px !important;
    height: 480px;
  }
  .header {
    position: relative;
    padding: 0 24px;
    margin-bottom: 20px;
    width: 100%;
    font-size: var(--font-size-L);

    .book-type-tab {
      width: 270px;
      display: flex;
      justify-content: space-between;

      .book-tab {
        width: 100px;
        height: 28px;
        background: rgba($color: #1F66FF, $alpha: 0.2);
        border-radius: 18px;
        //font-size: var(--font-size-L);
        color: #0B0B0B;
        text-align: center;
        line-height: 28px;

        &.active {
          color: #ffffff;
          font-weight: bold;
          background: rgba($color: #1F66FF, $alpha: 1);
        }

        &:hover {
          cursor: pointer;
        }

        span {
          font-size: var(--font-size-L);
        }
      }
    }

    .month {
      position: absolute;
      right: 35px;
      top: 50%;
      color: #313131;
      font-weight: bold;
      transform: translate(0, -50%);
    }
  }
}

.calendar.el-calendar {
  .el-calendar__header {
    display: none;
    border: none;

    .el-calendar__title {
      display: none;
    }
  }

  .el-calendar__body {
    padding: 0;

    .el-calendar-table {
      // margin: 0 1px;
      thead {
        display: none;
      }

      td {
        border: none;

        // &.is-today {
        //   .calendar-day__text {
        //     color: #ffce00;
        //   }
        // }

        &.is-selected {
          background: none;
        }

        // &.prev {
        //   .my-calendar__text {
        //     color: #CDCACA;
        //   }
        // }

        // &.next {
        //   .my-calendar__text {
        //     color: #CDCACA;
        //   }
        // }
      }

      .el-calendar-day {
        padding: 0;
        height: 30px;
        text-align: center;

        &:hover {
          background: none !important;
          color: #ffce00;

          .my-calendar__text {
            color: #ffce00;
          }
        }
      }
    }
  }
}

.mt15 {
  margin-top: 15px;
}

.el-rate.score {
  height: 15px;
  margin-right: 15px;
  width: 100px;
  .el-rate__icon {
    margin-right: 0px;
    font-size: 17px;
  }
}

.time-teacher-dialog {
  .el-dialog__body {
    padding: 20px 10px !important;
    height: 412px;
  }
}

.teacher-time-dialog {
  .el-dialog__body {
    padding: 20px 0 !important;
  }
}

.time-adj-container {
  .adj-time-picker.el-input {
    width: 100%;

    .el-input__inner {
      padding: 0 20px 0 40px;
      height: 36px;
      line-height: 36px;
      border: none;
      background: transparent;
      font-size: 14px;
      color: #392E30;
      cursor: pointer;
    }

    .el-input__prefix {
      .el-input__icon {
        line-height: 36px;
      }
    }
  }

  .time-picker-container {
    position: relative;
    width: 100%;
    height: 36px;
    background: #F5F5F5;
    border-radius: 18px;

    .picker-arrow {
      position: absolute;
      right: 12px;
      top: 12px;
      font-size: 14px;
      color: #BEBEBE;
      font-weight: bolder;
      transition: transform 0.3s;
    }

    &:focus-within > .picker-arrow {
      transform: rotate(-180deg);
      color: #FFCE00;
    }
  }
}
</style>

<style lang="scss" scoped>

.book-calendar {
  padding: 0;

  .calendar-container {
    padding: 0 24px;
  }

  .week {
    font-size: var(--font-size-L);
    color: #0B0B0B;
    text-align: center;
  }

  .calendar {
    height: 40px;
    margin-top: 15px;
    overflow: hidden;

    &.expand {
      height: initial;
      overflow: auto;
    }
  }

  .calendar-day {
    position: relative;

    &__text {
      height: 36px;
      width: 36px;
      font-size: var(--font-size-L);
      font-weight: bold;
      color: #392e30;
      line-height: 36px;
      text-align: center;

      &.active {
        background: #1F66FF;
        border-radius: 100px;
        color: #ffffff;
      }
    }

    &.inactive {
      color: #d1cece;
      cursor: not-allowed;

      .calendar-day__text {
        color: #d1cece;
      }
    }
  }

  .toggle-bar {
    margin: 4px 10px 12px;

    .h-line {
      flex: 1 0 100px;
      height: 1px;
      background: rgba($color: #979797, $alpha: 0.2);
    }

    .arrow {
      flex-shrink: 0;
      margin: 0 8px;
      width: 22px;
      height: 8px;
      cursor: pointer;
    }

    &.expand {
      transform: rotateX(180deg);
    }
  }

  .time-count {
    padding-left: 34px;
    margin-bottom: 5px;
    font-size: var(--font-size-L);
    font-weight: bold;
    color: #392e30;
    line-height: 28px;
  }

  .times-container {
    height: 255px;
    //height: 100%;
    overflow: scroll;
    @include noScrollBar;

    .times-content {
      padding: 8px 34px;
    }

    .time-tag {
      font-size: 18px;
      font-weight: bold;
      color: #999999;
      line-height: 25px;
    }

    .time-list {
      padding: 23px 0 0;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-gap: 23px 30px;

      & + .time-tag {
        margin-top: 16px;
      }
    }

    .time-item {
      width: 100%;
      height: 57px;
      background: #ffffff;
      box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
      border-radius: 15px;
      font-size: 18px;
      font-weight: bold;
      color: #392e30;
      line-height: 25px;
      text-align: center;

      .count {
        display: none;
      }

      &:hover {
        background: #1F66FF;
        box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.09);
        color: white;
        cursor: pointer;

        .count {
          font-weight: normal;
          display: block;
        }
      }

      &.scheduled {
        background: #efefef;
        color: #717171;
        box-shadow: none;
        cursor: not-allowed;

        &:hover {
          background: #efefef;
          box-shadow: none;
          font-weight: bold;
          color: #392e30;
        }
      }
    }
  }
}

.time-teacher-container {
  position: relative;
  height: 100%;

  .arrow {
    position: absolute;
    left: 16px;
    top: 0;
    width: 16px;
    height: 14px;
    cursor: pointer;
  }

  .time-teacher-content {
    padding: 10px 16px;
  }

  .time-teacher-item {
    position: relative;
    margin-top: 18px;

    .deco {
      position: absolute;
      left: 0;
      top: 18px;
      width: 7px;
      height: 24px;
      background: #1F66FF;
      border-radius: 0px 2px 2px 0px;
    }

    .left {
      max-width: 600px;
      cursor: pointer;

      &:hover {
        .describe {
          color: #1F66FF
        }

        .describe-arrow {
          color: #1F66FF
        }
      }
    }

    .name {
      margin-bottom: 10px;
      font-size: 20px;
      font-weight: bold;
      color: #131b1f;
      line-height: 28px;
      @include ellipsis;
    }

    .avatar {
      margin-right: 10px;
      height: 74px;
      width: 74px;
      border-radius: 100px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .location {
      font-size: 19px;
    }

    .country {
      padding-left: 8px;
      font-size: 18px;
      color: #0B0B0B;
      line-height: 25px;
    }

    .time {
      font-size: 18px;
      font-weight: bold;
      color: #F9815A;
      line-height: 25px;
    }

    .label-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      height: 26px;
      overflow: hidden;
    }

    .label {
      height: 26px;
      background: #D6E3FF;
      padding: 0 17px;
      line-height: 26px;
      border-radius: 8px;
      color: #1F66FF;
      font-size: 14px;
    }

    .describe {
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      @include ellipses(1)
    }

    .describe-arrow {
      line-height: 20px;
    }

    .choose-btn {
      width: 162px;
      height: 44px;
      padding: 0;
      color: #1F66FF;
      border: 2px solid #1F66FF;
      font-size: 20px;
      font-weight: bold;
      border-radius: 25px;
    }
  }
}

.confirm-book-container {

  .bg-top {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    object-fit: cover;
  }

  .tip {
    margin: 56px 0 15px;
    font-size: 16px;
    color: #0B0B0B;
    line-height: 22px;
    text-align: center;
  }

  .choose-btn {
    margin-top: 25px;
    width: 130px;
    height: 28px;
    border-radius: 16px;
    padding: 0px;
    font-size: 16px;
  }

  .secondary-btn {
    color: #1F66FF;
    border: 1px solid #1F66FF;
  }

  .confirm-btn {
    display: flex;
    justify-content: space-between;
    width: 80%;
    margin: 0 auto;
  }
}

.no-teacher {
  height: 100%;
  .icon {
    width: 182px;
    height: 121px;
  }

  .tip {
    margin-top: 18px;
    font-size: 18px;
    color: #cdcaca;
    line-height: 28px;
  }
}

.time-adj-container {
  .date {
    margin: 20px 0 24px;
    font-size: 12px;
    color: #484848;
    line-height: 18px;
    text-align: center;
  }

  .choose-btn {
    margin-top: 28px;
    width: 98px;
    height: 36px;
    border-radius: 10px;
  }
}

.book-by-teacher {
  .search-box {
    display: flex;
    align-items: center;
    padding: 0 52px;
    box-sizing: border-box;
  }

  .search-input {
    // margin: 0 52px;
    // width: calc(100% - 52px * 2);
    background: rgba(0, 0, 0, 0.03);
    border-radius: 18px;
    margin-right: 10px;
    flex: 1;
    height: 30px;
    ::v-deep .el-input__inner{
      height: 30px;
    }
  }

  .dropdown-choose {
    font-size: var(--font-size-L);
    color: #0B0B0B;
    cursor: pointer;
  }

  .teacher-container {
    height: 355px;
    margin-top: 10px;
  }

  .teacher-list {
    padding: 30px 52px;
  }

  .teacher-item {
    position: relative;
    margin-top: 10px;

    &:nth-of-type(1) {
      margin-top: 0;
    }

    .deco {
      position: absolute;
      left: 0;
      top: 18px;
      width: 7px;
      height: 24px;
      background: #1F66FF;
      border-radius: 0px 2px 2px 0px;
    }

    .left {
      max-width: 600px;
      cursor: pointer;

      &:hover {
        .f16 {
          color: #1F66FF
        }
      }
    }

    .name {
      margin-bottom: 16px;
      font-size: 20px;
      font-weight: bold;
      color: #131b1f;
      line-height: 28px;
      @include ellipsis;
    }

    .avatar {
      margin-right: 10px;
      height: 74px;
      width: 74px;
      border-radius: 100px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .label-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      height: 26px;
      overflow: hidden;
    }

    .label {
      height: 26px;
      background: #D6E3FF;
      padding: 0 17px;
      line-height: 26px;
      border-radius: 8px;
      color: #1F66FF;
      font-size: 14px;
    }

    .describe {
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      @include ellipses(1)
    }

    .describe-arrow {
      line-height: 20px;
    }

    .location {
      font-size: 19px;
    }

    .country {
      padding-left: 4px;
      font-size: 18px;
      color: #0B0B0B;
      line-height: 25px;
    }

    .time {
      font-size: 14px;
      font-weight: bold;
      color: #775000;
      line-height: 20px;
    }

    .choose-btn {
      width: 162px;
      height: 44px;
      padding: 0;
      color: #1F66FF;
      border: 2px solid #1F66FF;
      font-size: 20px;
      font-weight: bold;
      border-radius: 25px;
    }
  }
}

.teacher-time-container {
  .teacher-item {
    margin: 0 24px;
    width: calc(100% - 24px * 2);
    position: relative;
    margin-top: 12px;

    .deco {
      position: absolute;
      left: 0;
      top: 18px;
      width: 4px;
      height: 15px;
      background: linear-gradient(174deg, #94ffa2 0%, #50a1ff 100%);
      border-radius: 0px 2px 2px 0px;
    }

    .left {
      max-width: 340px;
      cursor: pointer;

      &:hover {
        .f16 {
          color: #1F66FF
        }
      }
    }

    .name {
      margin-bottom: 10px;
      font-size: 14px;
      font-weight: bold;
      color: #131b1f;
      line-height: 20px;
      @include ellipsis;
    }

    .avatar {
      margin-right: 10px;
      height: 74px;
      width: 74px;
      border-radius: 100px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .location {
      font-size: 19px;
    }

    .country {
      padding-left: 9px;
      font-size: 18px;
      color: #ff8914;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 25px;
    }

    .label-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      height: 26px;
      overflow: hidden;
    }

    .teacher-label {
      height: 26px;
      background: #D6E3FF;
      padding: 0 17px;
      line-height: 26px;
      border-radius: 8px;
      color: #1F66FF;
      font-size: 14px;
    }

    .describe {
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      @include ellipses(1)
    }

    .describe-arrow {
      line-height: 20px;
    }

    .time {
      font-size: 14px;
      font-weight: bold;
      color: #775000;
      line-height: 20px;
    }
  }

  .label {
    margin: 24px 24px 10px;
    font-size: 14px;
    font-weight: bold;
    color: #313131;
    line-height: 20px;
  }

  .times-container {
    height: 236px;
    margin-top: 10px;
    .times-list {
      padding: 38px 24px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 35px 43px;

      .time-item {
        width: 100%;
        height: 56px;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
        border-radius: 10px;
        font-size: 12px;
        font-weight: bold;
        color: #392e30;
        line-height: 15px;
        text-align: center;

        &:hover {
          background: #1f66ff;
          box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.09);
          color: #fff;
          cursor: pointer;
        }
      }
    }
  }
}

.container-suc {
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 80px;
    height: 80px;
    margin-bottom: 32px;
  }

  span {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #0B0B0B;
    line-height: 22px;
    margin-bottom: 30px;
  }
}

.book-dropdown {
  background: #FFFFFF;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 17px;

  li {
    &:hover {
      background: #FFFFFF;
    }
  }
}

.mb12 {
  margin-bottom: 12px;
}
</style>
