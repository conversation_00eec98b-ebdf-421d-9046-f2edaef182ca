<template>
  <div class="course-item book shadow-box">
    <div class="flex">
      <div class="info flex flex-col">
        <div class="title">
          <div class="text">{{ course.name || '课程名称' }}</div>
        </div>
        <div class="teacher flex items-center">
          <div class="avatar">
            <img :src="course.avatar || dfTAvatar" alt="teacher-avatar" />
          </div>
          <div class="name">{{ course.teacher }}</div>
        </div>
        <div class="flex" style="margin-top:6px;">
          <div class="time flex">
            <div class="time-item month">{{ formatYYYYMMDDDot(course.time) }}</div>
            <div class="time-item week">{{ formatWeek(course.time) }}</div>
            <div class="time-item time-picker">
              <el-time-select
                v-model="time"
                class="adj-time-picker"
                :picker-options="pickerOptions"
                prefix-icon="false"
                :editable="false"
                :clearable="false"
                placeholder=""
                @change="handleTimeChange"
              />
              <div class="adj-time">调整时间</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatYYYYMMDDDot, formatWeek, getClassTimePeriod, formatHHmm, addMin, subMin } from '@/utils/time'
import dfCover from '@/assets/images/default-cover.jpg'
import dfTAvatar from '@/assets/images/dashboard/defaultAvatar.png'

export default {
  props: {
    course: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data () {
    return {
      time: '',
      pickerOptions: null,
      loading: false,
      dfCover,
      dfTAvatar,
      formatYYYYMMDDDot,
      formatWeek,
      getClassTimePeriod,
      formatHHmm
    }
  },
  watch: {
    // course: {
    //   handler(val) {
    //     this.time = formatHHmm(val.time)
    //     this.pickerOptions = {
    //       start: subMin(val.time, 25),
    //       step: '00:05',
    //       end: addMin(val.time, 25)
    //     }
    //   },
    //   deep: true,
    //   immediate: true
    // }
  },
  mounted () {
    this.time = formatHHmm(this.course.time)
    this.pickerOptions = {
      start: subMin(this.course.time, 25),
      step: '00:05',
      end: addMin(this.course.time, 25)
    }
  },
  methods: {
    handleTimeChange () {
      if (this.time === formatHHmm(this.course.time)) {
        return
      }

      this.$emit('timeChanged', this.time)
    }
  }
}
</script>

<style lang="scss">
.book.course-item {
  .time-picker {
    width: calc(100% - 142px);
    height: 20px;
    background: transparent;
    position: relative;

    .adj-time-picker.el-input {
      width: 100%;
      height: 20px;
      .el-input__inner {
        display: block;
        padding: 0;
        border: none;
        font-weight: bold;
        color: #F97E56;
        height: 20px;
        line-height: 16px;
        background: transparent;
        font-size: 16px;
        cursor: pointer;
      }

      .el-input__prefix {
        display: none;
        .el-input__icon {
          line-height: 20px;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>

.course-item {
  padding: 16px;
  background: #FFFFFF;
  border-radius: 15px;
  overflow: hidden;

  .title {
    margin-bottom: auto;
    width: 100%;

    .type {
      margin: 2px 8px 2px 0;
      width: 38px;
      height: 16px;
      border-radius: 5px;
      border: 1px solid #F75655;
      font-size: 12px;
      font-weight: bold;
      color: #F75655;
      line-height: 16px;
      text-align: center;

      &.type-2 {
        border: 1px solid #649FFD;
        color: #649FFD;
      }
    }

    .text {
      width: calc(100% - 46px);
      font-size: 14px;
      font-weight: bold;
      color: #392E30;
      line-height: 20px;
      @include ellipses(2);
    }
  }

  .info {
    width: 100%;
  }

  .teacher {
    padding: 10px 0;
    .avatar {
      width: 36px;
      height: 36px;
      border-radius: 100px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .name {
      max-width: calc(100% - 36px);
      padding-left: 14px;
      font-size: 16px;
      font-weight: bold;
      color: #131B1F;
      line-height: 22px;
      @include ellipsis;
    }
  }

  .time {
    width: 100%;
    font-size: 16px;
    font-weight: bold;
    color: #F97E56;
    line-height: 22px;

    .month {
      width: 90px;
    }

    .week {
      width: 40px;
    }

    .time-item + .time-item {
      margin-left: 6px;
    }
  }

  .adj-time {
    position: absolute;
    right: 10px;
    bottom: -2px;
    margin-left: 12px;
    border-bottom: 1px solid #649FFD;
    font-size: 12px;
    color: #649FFD;
    line-height: 19px;
    cursor: pointer;
    pointer-events: none;
  }

  .enter-btn {
    padding: 0;
    width: 98px;
    height: 36px;
  }
}
</style>
