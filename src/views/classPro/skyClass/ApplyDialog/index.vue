<template>
  <component-dialog :dialog-visible="dialogVisible" title="申请补课" @closeDialog="$emit('closeDialog')">
    <div class="apply-dialog">
      <div class="alert">{{ msg }}</div>
      <div class="btn-group">
        <div class="classpro-btn-opacity" @click="$emit('closeDialog')">取消</div>
        <div class="classpro-btn" @click="rightBtnCallback">确认申请</div>
      </div>
    </div>
  </component-dialog>
</template>

<script>
import ComponentDialog from '@/components/classPro/ComponentDialog'
export default {
  components: {
    ComponentDialog
  },
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: true
    },
    msg: {
      type: String,
      require: true,
      default: ''
    }
  },
  data () {
    return {

    }
  },
  methods: {
    rightBtnCallback () {
      this.$emit('closeDialog')
      this.$emit('showBook')
    }
  }
}
</script>

<style lang='scss' scoped>
.apply-dialog {
    .btn-group {
        display: flex;
        justify-content: center;
        margin-top: 30px;

        .classpro-btn-opacity,
        .classpro-btn {
            width: 130px;
        }

        .classpro-btn {
            margin-left: 20px;
        }
    }
}
</style>
