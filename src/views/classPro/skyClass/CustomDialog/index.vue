<template>
  <el-dialog
    :visible.sync="show"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :destroy-on-close="true"
    :width="width"
    :top="top"
    custom-class="classpro-book-dialog"
  >
    <div v-if="title" class="dialog-title">{{ title }}</div>
    <slot name="content"></slot>
    <svg-icon
      icon-class="close"
      class-name="close-icon"
      @click="closeDialog"
    />
  </el-dialog>
</template>

<script>
export default {
  name: 'CustomDialog',
  props: {
    show: {
      type: Boolean,
      require: true,
      default: false
    },
    title: {
      type: String,
      require: true,
      default: ''
    },
    width: {
      type: String,
      default: '360px'
    },
    top: {
      type: String,
      default: '25vh'
    }
  },
  data () {
    return {}
  },
  methods: {
    closeDialog () {
      this.$emit('dialogClosed')
    }
  }
}
</script>

<style lang='scss'>
.classpro-book-dialog {
    position: relative;
    background: #FFFFFF;
    border-radius: 15px;
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.classpro-book-dialog {

    .close-icon {
      position: absolute;
      transform: translate(-50%,0);
      left: 50%;
      bottom: -44px;
      width: 40px !important;
      height: 40px !important;
      cursor: pointer;
    }
}

.confirm-book-dialog {
  .dialog-title {
    font-size: 18px;
    color: #0B0B0B;
    line-height: 25px;
    text-align: start !important;
  }
}
</style>
