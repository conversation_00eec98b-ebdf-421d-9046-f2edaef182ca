<template>
  <customDialog :dialog-visible="show" :is-center="false" title="约课成功!" @closeDialog="primaryFunc">
    <div class="classpro-notify-dialog">
      <div class="dialog-content">
        <slot name="content"></slot>
      </div>
      <div class="dialog-footer flex justify-center">
        <el-button
          v-if="leftBtn"
          class="secondary-btn left-btn"
          @click.native.prevent="secondaryFunc"
        >{{ leftBtn }}</el-button>
        <el-button
          type="primary"
          class="classpro-btn right-btn"
          @click.native.prevent="primaryFunc"
        >{{ rightBtn }}</el-button>
      </div>
    </div>
  </customDialog>
</template>

<script>
import CustomDialog from '@/components/classPro/ComponentDialog'
export default {
  name: 'NotifyDialog',
  components: {
    CustomDialog
  },
  props: {
    show: {
      type: Boolean,
      require: true,
      default: false
    },
    icon: {
      type: String,
      require: true,
      default: ''
    },
    title: {
      type: String,
      require: true,
      default: ''
    },
    leftBtn: {
      type: String,
      require: true,
      default: ''
    },
    rightBtn: {
      type: String,
      require: true,
      default: '确定'
    }
  },
  data () {
    return {}
  },
  methods: {
    closeDialog () {
      this.$emit('update:show', false)
    },
    secondaryFunc () {
      this.$emit('secondary')
    },
    primaryFunc () {
      this.$emit('primary')
    }
  }
}
</script>

<style lang="scss" scoped>
.mian-icon {
  display: block;
  margin: 0 auto;
  width: 108px;
  height: 108px;
  font-size: 108px;
}

.dialog-title {
  margin: 12px 0 18px;
}

.dialog-content {
  padding: 0 16px;
  font-size: 12px;
  color: #484848;
  line-height: 18px;
  word-break: break-all;
  text-align: center;
}

.dialog-footer {
  margin-top: 26px;
}

.left-btn,
.right-btn {
  min-width: 130px;
  height: 30px;
  line-height: 30px;
}
</style>
