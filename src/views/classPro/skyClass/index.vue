<template>
  <div class="sky-class">
    <div class="wrap">
      <div class="header">
        <div class="flex items-center">
          <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="backToHome" />
          <div class="back" @click="backToHome">返回</div>
        </div>
        <div class="flex items-center">
          <div class="yueke" @click="toAllCourse">
            <svg-icon
              icon-class="appoint"
              class-name="appoint"
            />
            <div class="text">约课</div>
          </div>
          <div v-if="userRelations.length > 0" class="grade-btn" style="padding-left:12px" @click="showGradeDialog = true">
            <p>{{ getGradeName() }}</p>
            <i class="iconfont icon-xialabeifen"></i>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="content__top" @click="toLessonInfo(nearstLesson)">
          <div class="content__top__inner">
            <div class="recent">最近待上课</div>
            <template v-if="nearstLesson && nearstLesson.lessonPlan !== null">
              <div class="course-info">
                <div class="title">
                  <!-- <div
                    class="label"
                    :class="{
                      'red': subjectName(nearstLesson) === '语',
                      'orange': subjectName(nearstLesson) === '英',
                      'yellow': subjectName(nearstLesson) === '文'}"
                  >
                    {{ subjectName(nearstLesson) }}
                  </div> -->
                  <div class="text">{{ nearstLesson.course.name || '' }}</div>
                </div>
                <div class="course-info__content pl-10">
                  <div class="unit">第{{ nearstLesson.lessonPlan === null ? '' : nearstLesson.lessonPlan.seq || '' }}讲·{{ nearstLesson.lessonPlan === null ? '' : nearstLesson.lessonPlan.name || '' }}</div>
                  <div class="time">上课时间：{{ formatDot(nearstLesson.startTime) }}  {{ formatWeek(nearstLesson.startTime) }}  {{ formatHHmm(nearstLesson.startTime) }}</div>
                  <div class="teacher" @click.stop="_showTeacherInfo(nearstLesson.teacherId)">
                    <div class="flex">
                      上课教师：
                      <img class="teacher__header" :src="nearstLesson.lessonInfo2 ? nearstLesson.lessonInfo2.teacherAvart || DefaultAvatar : DefaultAvatar" alt="教师头像" />
                      <div class="teacher__name mr-26">{{ nearstLesson.lessonInfo2 ? nearstLesson.lessonInfo2.teacherName || '' : '' }}</div>
                      <div class="f16">简介></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="state" :style="{background: `url(${statusBg(nearstLesson.lessonStatus)}) no-repeat`}">{{ statusName(nearstLesson.lessonStatus) }}</div>
              <div v-if="statusName(nearstLesson.lessonStatus) === '未开始'" class="button-group">
                <div class="classpro-btn-opacity button-group__blue margin-right" @click.stop="cancelCourse(nearstLesson)">取消预约</div>
                <div class="classpro-btn button-group__blue" @click.stop="toClassroom(nearstLesson)">进入教室</div>
              </div>
              <div v-if="statusName(nearstLesson.lessonStatus) === '进行中'" class="button-group">
                <div class="classpro-btn-disable classpro-btn button-group__blue margin-right" @click.stop="cancelCourse(nearstLesson)">取消预约</div>
                <div class="classpro-btn button-group__blue" @click.stop="toClassroom(nearstLesson)">进入教室</div>
              </div>
            </template>
            <template v-else>
              <div class="empty-container">
                <!-- <img src="@/assets/images/dashboard/no-ai-course.png" alt="ai课堂为空" /> -->
                <div class="hint">最近没有待上课哦～</div>
              </div>
            </template>
          </div>
        </div>
        <div class="content__bottom">
          <div class="content__bottom__type">
            <div class="type-item" :class="{'type-item-select': type === 0}" @click="changeType(0)">最近课程</div>
            <div class="type-item" :class="{'type-item-select': type === 1}" @click="changeType(1)">待上课程</div>
            <div class="type-item" :class="{'type-item-select': type === 2}" @click="changeType(2)">已上课程</div>
            <div class="all-course" @click="toAllCourse">全部课程<img src="@/assets/images/arrow-next.png" /></div>
          </div>
          <div class="content__bottom__list">
            <template v-if="currentList.length > 0">
              <div v-for="item in currentList" :key="item.id" class="content__bottom__list__item" @click="toLessonInfo(item)">
                <div class="course-info mb-20">
                  <div class="title">
                    <div
                      class="label"
                      :class="{
                        'red': subjectName(item) === '语',
                        'orange': subjectName(item) === '英',
                        'yellow': subjectName(item) === '文'}"
                    >
                      {{ subjectName(item) }}
                    </div>
                    <div class="text">{{ item.lessonPlan !== null ? item.course.name || '' : '' }}</div>
                  </div>
                  <div class="course-info__content">
                    <div class="unit">第{{ item.lessonPlan === null ? '' : item.lessonPlan.seq || '' }}讲·{{ item.lessonPlan === null ? '' : item.lessonPlan.name || '' }}</div>
                    <div class="time">上课时间：{{ formatDot(item.startTime) }}  {{ formatWeek(item.startTime) }}  {{ formatHHmm(item.startTime) }}</div>
                    <div class="teacher" @click.stop="_showTeacherInfo(item.teacherId)">
                      <div class="flex">
                        上课教师：
                        <img class="teacher__header" :src="item.lessonInfo2 ? item.lessonInfo2.teacherAvart || DefaultAvatar : DefaultAvatar" alt="教师头像" />
                        <div class="mr-26" style="display: inline-block">
                          <div class="teacher__name">{{ item.lessonInfo2 ? item.lessonInfo2.teacherName || '' : '' }}</div>
                          <div v-if="item.reviews" class="reviews">
                            <svg-icon
                              v-for="reviewIndex in item.reviews.star"
                              :key="reviewIndex + 'yellow'"
                              icon-class="star"
                              class-name="arrow"
                            />
                            <svg-icon
                              v-for="reviewIndex2 in (5 - item.reviews.star)"
                              :key="reviewIndex2 + 'grey'"
                              icon-class="star-grey"
                              class-name="arrow"
                            />
                          </div>
                        </div>
                        <div class="f16">简介></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="line mb-10"></div>
                <div class="state" :style="{background: `url(${statusBg(item.lessonStatus)}) no-repeat`}">{{ statusName(item.lessonStatus) }}</div>
                <div v-if="statusName(item.lessonStatus) === '未开始'" class="button-group">
                  <div class="classpro-btn-opacity button-group__blue margin-right" @click.stop="cancelCourse(item)">取消预约</div>
                  <div class="classpro-btn button-group__blue" @click.stop="toClassroom(item)">进入教室</div>
                </div>
                <div v-if="statusName(item.lessonStatus) === '进行中'" class="button-group">
                  <div class="classpro-btn-disable classpro-btn button-group__blue margin-right" @click.stop="cancelCourse(item)">取消预约</div>
                  <div class="classpro-btn button-group__blue" @click.stop="toClassroom(item)">进入教室</div>
                </div>
                <div v-if="statusName(item.lessonStatus) === '已完成'" class="button-group">
                  <div class="classpro-btn button-group__blue margin-right" @click.stop="toClassroom(item)">查看回放</div>
                  <div class="classpro-btn button-group__blue" @click.stop="openCourseware(item)">查看课件</div>
                </div>
                <div v-if="statusName(item.lessonStatus) === '缺课'" class="button-group">
                  <div class="button-group__blue margin-right" :class="item.newLessonId || item.lessonDetail.source === 'PLAN' || !item.canPlan ? 'classpro-btn-disable' : 'classpro-btn'" @click.stop="_applyLesson(item)">申请补课</div>
                  <div class="classpro-btn button-group__blue" @click.stop="openCourseware(item)">查看课件</div>
                </div>
              </div>
              <!-- <div class="loading-text">{{ isScroll ? '数据加载中' : '数据加载完了' }}</div> -->
              <div v-show="isScroll" class="loading-text">数据加载中</div>
            </template>
            <template v-else>
              <div class="empty-container">
                <img src="@/assets/images/dashboard/no-ai-course.png" alt="ai课堂为空" />
                <div class="hint">暂时没有数据哦～</div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <!-- <div class="bubble" @click="toAllCourse">
        <svg-icon
          icon-class="appoint"
          class-name="appoint"
        />
        <div class="text">约课</div>
      </div> -->
      <book :show-book="showBook" :book-id="bookCourse.course.id" :book-info="bookInfo" @closeBook="closeBook" />
      <cancel-dialog :dialog-visible="showCancel" @closeDialog="closeCancelDialog" @rightBtnCallback="cancelLesson" />
      <courseware :show-drawer="showCourseware" :document-id="documentId" @closeDrawer="showCourseware = false" />
      <apply-dialog :dialog-visible="showApply" :msg="applyMsg" @closeDialog="showApply = false" @showBook="showBook = true" />
      <grade-dialog :dialog-visible="showGradeDialog" @closeDialog="showGradeDialog = false" />
    </div>
  </div>
</template>

<script>
import Book from './Book'
import ApplyDialog from './ApplyDialog'
import CancelDialog from './CancelDialog'
import Courseware from './courseware'
import DefaultAvatar from '@/assets/images/profile.png'
import labelBlue from '@/assets/images/skyclass/label-blue.png'
import labelGrey from '@/assets/images/skyclass/label-grey.png'
import labelRed from '@/assets/images/skyclass/label-red.png'
import labelGreen from '@/assets/images/skyclass/label-green.png'
import { getLessonListAllType, applyLesson } from '@/api/lesson-api.js'
import { cancelScheduledLesson } from '@/api/course-api.js'
import { formatDot, formatWeek, formatHHmm } from '@/utils/date.js'
import { mapGetters } from 'vuex'
import { getChildName, getChildToken } from '@/utils/auth'
import GradeDialog from '@/components/classPro/Navbar/GradeDialog'
import { setChildName, setChildId, setChildToken } from '@/utils/auth'
import { switchAccount } from '@/api/user-api'
export default {
  components: {
    CancelDialog,
    Courseware,
    Book,
    ApplyDialog,
    GradeDialog
  },
  data () {
    return {
      DefaultAvatar,
      labelBlue,
      labelGrey,
      labelRed,
      labelGreen,
      nearstLesson: {
        'course': null,
        'lessonPlan': null
      },
      formatDot,
      formatWeek,
      formatHHmm,
      type: 0,
      pageNo: 1,
      pageSize: 10,
      lessonNearList: [],
      lessonCommingSoonList: [],
      lessonCompletedList: [],
      isScroll: true,
      isLoading: false,
      showCancel: false,
      cancelling: false,
      cancelLessonId: 0,
      showCourseware: false,
      documentId: 0,
      showBook: false,
      bookCourse: {
        'course': {}
      },
      bookInfo: {},
      userId: this.$store.getters.id,
      showApply: false,
      applyMsg: '',
      showGradeDialog: false,
      canGoToClass: true
    }
  },
  computed: {
    currentList () {
      switch (this.type) {
        case 0:
          return this.lessonNearList
        case 1:
          return this.lessonCommingSoonList
        case 2:
          return this.lessonCompletedList
        default:
          return []
      }
    },
    ...mapGetters([
      'name',
      'avatar',
      'mobile',
      'school',
      'childName',
      'userRelations'
    ])
  },
  created () {
    this._getData()
  },
  mounted () {
    document.addEventListener('scroll', this.scrollMoreData, true)
    this.$bus.$on('getStuCourseList', () => {
      this.pageNo = 1
      this._getNearstLesson()
      this._getLessonNearList()
      this._getCommingSoonList()
      this._getCompletedList()
    })
  },
  destroyed () {
    document.removeEventListener('scroll', this.scrollMoreData, true)
    this.$bus.$off('getStuCourseList')
  },
  methods: {
    backToHome () {
      this.$router.push({ path: '/classpro/myCourse' })
    },
    async _getData () {
      await this.checkGrade()
      this._getNearstLesson()
      this._getLessonNearList()
      this._getCommingSoonList()
      this._getCompletedList()
    },
    _getNearstLesson () {
      const param = { 'lessonListType': 'NEARST', 'userType': 'STUDENT', 'param': this.userId }
      getLessonListAllType(param).then(response => {
        if (response.data !== null) this.nearstLesson = response.data[0]
      })
    },
    _getLessonNearList () {
      this.isLoading = true
      let param = {}
      param = { 'lessonListType': 'NEAREST_LIST', 'userType': 'STUDENT', 'pageNo': this.pageNo, 'pageSize': this.pageSize, 'param': this.userId }
      getLessonListAllType(param).then(response => {
        this.isLoading = false
        if (this.pageNo === 1) {
          this.lessonNearList = []
          this.lessonNearList = response.data
        } else {
          this.lessonNearList = this.lessonNearList.concat(response.data)
        }
        if (response.data.length < this.pageSize) {
          this.isScroll = false
        } else {
          this.isScroll = true
        }
      })
    },
    _getCommingSoonList () {
      this.isLoading = true
      const param = { 'lessonListType': 'ComingSoon', 'userType': 'STUDENT', 'pageNo': this.pageNo, 'pageSize': this.pageSize, 'param': this.userId }
      getLessonListAllType(param).then(response => {
        this.isLoading = false
        if (this.pageNo === 1) {
          this.lessonCommingSoonList = []
          this.lessonCommingSoonList = response.data
        } else {
          this.lessonCommingSoonList = this.lessonCommingSoonList.concat(response.data)
        }
        if (response.data.length < this.pageSize) {
          this.isScroll = false
        } else {
          this.isScroll = true
        }
      })
    },
    _getCompletedList () {
      this.isLoading = true
      const param = { 'lessonListType': 'Completed', 'userType': 'STUDENT', 'pageNo': this.pageNo, 'pageSize': this.pageSize, 'param': this.userId }
      getLessonListAllType(param).then(response => {
        this.isLoading = false
        if (this.pageNo === 1) {
          this.lessonCompletedList = []
          this.lessonCompletedList = response.data
        } else {
          this.lessonCompletedList = this.lessonCompletedList.concat(response.data)
        }
        if (response.data.length < this.pageSize) {
          this.isScroll = false
        } else {
          this.isScroll = true
        }
      })
    },
    statusName (status) {
      switch (status) {
        case 'ComingSoon':
          return '未开始'
        case 'Opening':
          return '进行中'
        case 'UnderReview':
        case 'Completed':
        case 'Unqualified':
          return '已完成'
        case 'TeachersAbsenteeism':
        case 'StudentsAbsenteeism':
          return '缺课'
        default:
          return ''
      }
    },
    statusBg (status) {
      switch (status) {
        case 'ComingSoon':
          return labelGrey
        case 'Opening':
          return labelBlue
        case 'UnderReview':
        case 'Completed':
        case 'Unqualified':
          return labelGreen
        case 'TeachersAbsenteeism':
        case 'StudentsAbsenteeism':
          return labelRed
        default:
          return ''
      }
    },
    changeType (type) {
      this.isScroll = false
      const el = document.querySelector('.sky-class')
      el.scrollTop = 0
      this.type = type
      this.pageNo = 1
      switch (this.type) {
        case 0 :
          this._getLessonNearList()
          break
        case 1 :
          this._getCommingSoonList()
          break
        case 2 :
          this._getCompletedList()
          break
        default:
          console.log('null')
      }
    },
    toClassroom (item) {
      if (!item.lessonInfo2 || !item.lessonInfo2.roomUrl || !this.canGoToClass) {
        return
      }
      this.canGoToClass = false
      setTimeout(() => {
        this.canGoToClass = true
      }, 8000)
      this.$message.warning({
        message: '正在进入教室请勿重复点击',
        duration: 8000
      })
      window.open(item.lessonInfo2.roomUrl, '_blank')
    },
    subjectName (item) {
      if (!item.lessonPlan) return ''
      switch (item.lessonPlan.subjectType) {
        case 'CHINESE':
          return '语'
        case 'CHINESE_CLASS':
          return '文'
        case 'ENGLISH':
          return '英'
        case 'MATH':
          return '数'
        case 'MUSIC':
          return '音'
        case 'ART':
          return '美'
        default:
          return ''
      }
    },
    scrollMoreData () {
      const el = document.querySelector('.sky-class')
      const offsetHeight = el.offsetHeight
      el.onscroll = () => {
        const scrollTop = el.scrollTop
        const scrollHeight = el.scrollHeight
        if (offsetHeight + scrollTop - scrollHeight >= -1) {
          console.log('滚动到了底部')
          if (!this.isScroll || this.isLoading) {
            return
          }
          this.pageNo++
          switch (this.type) {
            case 0 :
              this._getLessonNearList()
              break
            case 1 :
              this._getCommingSoonList()
              break
            case 2 :
              this._getCompletedList()
              break
            default:
              console.log('null')
          }
        }
      }
    },
    toLessonInfo (item) {
      if (!item.id) return
      this.$router.push(`skyClass/lessonInfo/${item.id}`)
    },
    toAllCourse () {
      this.$router.push(`skyClass/allCourse/0`)
    },
    cancelCourse (item) {
      if (this.statusName(item.lessonStatus) === '进行中') return
      this.cancelLessonId = item.id
      this.showCancel = true
    },
    closeCancelDialog () {
      this.showCancel = false
    },
    cancelLesson () {
      if (this.cancelling) return
      this.cancelling = true
      const params = {
        id: this.cancelLessonId
      }
      cancelScheduledLesson(params).then(
        response => {
          this.$message.success('取消课程成功')
          this._getNearstLesson()
          this._getLessonNearList()
          this._getCommingSoonList()
          this._getCompletedList()
          this.showCancel = false
          this.cancelling = false
        },
        error => {
          console.log(error)
          this.showCancel = false
          this.cancelling = false
        }
      )
    },
    openCourseware (item) {
      this.showCourseware = true
      this.documentId = item.documentId
    },
    _applyLesson (item) {
      if (item.lessonDetail.source === 'PLAN') {
        this.$message.error('该课程属于运营人员额外为您添加的课程，不能申请补课哦')
        return
      }
      if (item.newLessonId) {
        this.$message.error('已申请补课')
        return
      }
      if (!item.canPlan) {
        this.$message.error('补课时限已过')
        return
      }
      this.bookCourse = item
      const params = {
        'lessonId': this.bookCourse.id
      }
      applyLesson(params).then(response => {
        if (response.data.canPlan) {
          this.bookInfo = {
            id: this.bookCourse.studentLessonPlan.studentsCourseId,
            courseId: this.bookCourse.courseId,
            cover: this.bookCourse.coverUrl || '',
            name: this.bookCourse.course && this.bookCourse.course.name || '',
            type: this.bookCourse.lessonDetail.teamId !== 0 ? 'TEAM' : 'MIN',
            lessonPlanId: this.bookCourse.lessonPlanId,
            lessonId: this.bookCourse.id
          }
          this.showApply = true
          this.applyMsg = response.data.msg
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    closeBook () {
      this.showBook = false
      this.pageNo = 1
      this._getNearstLesson()
      this._getLessonNearList()
      this._getCommingSoonList()
      this._getCompletedList()
    },
    getGradeName () {
      if (getChildName()) return getChildName()
      // for (var grade of this.$store.getters.userRelations) {
      //   if (+grade.toUserId === +this.$store.getters.id) return grade.toUserDisplayName
      // }
      return this.$store.getters.name
    },
    async checkGrade () {
      if (getChildToken()) return
      const userRelationsList = this.$store.getters.userRelations
      if (userRelationsList.length > 0) {
        const grade = userRelationsList[0]
        if (+grade.toUserId !== this.$store.getters.id) {
          const param = {
            'touserId': grade.toUserId,
            'parentUserId': this.$store.getters.id
          }
          await switchAccount(param)
            .then(response => {
              setChildName(grade.toUserDisplayName)
              setChildId(grade.toUserId)
              setChildToken('Bearer ' + response.data.access_token)
            })
        }
      }
    },
    _showTeacherInfo (teacherId) {
      this.$emit('showTeacherInfo', teacherId)
    }
  }
}
</script>

<style lang="scss" scoped>
.sky-class {
    // height: calc(100% - 62px);
    height: calc(100% - 1px);
    // padding: 10px 40px 0;
    // padding: 10px;
    overflow: auto;

  .wrap {
    height: 100%;
    width: 100%;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 60px;
        img {
            width: 14px;
            height: 14px;
            cursor: pointer;
        }

        .back {
            font-size: var(--font-size-L);
            font-weight: 500;
            color: #0B0B0B;
            line-height: 20px;
            margin-left: 17px;
            cursor: pointer;
        }

        .grade-btn {
          z-index: 12;
          padding: 0 15px 0 0;
          height: 30px;
          background: #E9F0FF;
          border-radius: 15px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: var(--font-size-L);
          color: #1F66FF;
          line-height: 30px;
          display: flex;
          align-items: center;
          cursor: pointer;

          p {
            margin: 0;
            max-width: 300px;
            @include ellipsis;
          }

          img {
            width: 25px;
            height: 25px;
            object-fit: contain;
            margin-left: 6px;
            margin-right: 9px;
          }

          .icon-xialabeifen {
            margin-left: 13px;
            transform:rotate(90deg);
          }
        }
    }

    .content {
      height: calc(100% - 60px);
        &__top {
            width: 100%;
            height: 172px;
            background: #FFFFFF;
            box-shadow: 0px 4px 12px 7px rgba(62, 89, 253, 0.05);
            border-radius: 22px;

            &__inner {
                background: url('../../../assets/images/skyclass/bg-recent.png') center center no-repeat;
                background-size: cover;
                width: 100%;
                height: 100%;
                padding: 10px 20px;
                position: relative;
                display: flex;
                flex-direction: column;
                cursor: pointer;
                .recent {
                    font-size: var(--font-size-L);
                    font-weight: 500;
                    color: #0B0B0B;
                    line-height: 22px;
                    margin-bottom: 20px;
                    color: #1C1B1A;
                    padding-left: 7px;
                    position: relative;

                    &::before {
                      content: '';
                      position: absolute;
                      top: 50%;
                      left: 0;
                      transform: translate(0 , -50%);
                      width: 3px;
                      height: 16px;
                      background: #3479FF;
                      border-radius: 1px;
                    }
                }

                .state {
                    right: -6px;
                    top: 12px;
                }

                .button-group {
                    position:absolute;
                    right: 20px;
                    bottom: 50px;
                }
            }
        }

        &__bottom {
            margin-top: 20px;
            min-height: calc(100% - 192px);
            display: flex;
            flex-direction: column;

            &__type {
                display: flex;
                padding-left: 40px;
                height: 32px;
                .type-item {
                    width: 110px;
                    height: 32px;
                    background: #E0E7FF;
                    border-radius: 8px 25px 0 0;
                    margin-right: 4px;
                    cursor: pointer;
                    font-size: var(--font-size-L);
                    font-weight: 400;
                    color: #0B0B0B;
                    line-height: 32px;
                    text-align: center;
                }

                .type-item-select {
                    background: white;
                    color: #1F66FF;
                    box-shadow: 0px -2px 8px 5px rgba(62, 89, 253, 0.05);
                }

                .all-course {
                  margin-left: auto;
                  font-size: var(--font-size-L);
                  font-weight: 400;
                  color: #0B0B0B;
                  line-height: 17px;
                  display: flex;
                  align-items: center;
                  cursor: pointer;

                  img {
                    width: 7px;
                    height: 12px;
                    object-fit: contain;
                    margin-left: 5px;
                  }
                }
            }

            &__list {
                flex: 1;
                width: 100%;
                min-height: 350px;
                border-radius: 5px;
                background: #FFFFFF;
                padding: 20px 20px 0 30px;

                &__item {
                    width: 100%;
                    position: relative;
                    cursor: pointer;
                    padding-top: 20px;

                    .state {
                        position: absolute;
                        right: -6px;
                        top: 0px;
                    }

                    .button-group {
                        position: absolute;
                        right: 14px;
                        bottom: 35px;
                    }

                    .line {
                      width: 100%;
                      height: 1px;
                      background: rgba(52,121,255,0.08);
                    }
                }
            }

            .empty-container {
              height: 350px;
              justify-content: center;
            }
        }

        .course-info {
            .title {
                display: flex;
                align-items: center;
                margin-bottom: 5px;
                line-height: 22px;

                .label {
                    width: 20px;
                    height: 20px;
                    border-radius: 6px;
                    background: #2787FF;
                    font-size: var(--font-size-M);
                    font-weight: 500;
                    color: #FFFFFF;
                    line-height: 20px;
                    text-align: center;
                }

                .red {
                  background: #FF675B;
                }

                .yellow {
                  background: #AF7A40;
                }

                .orange {
                  background: #FF8E44;
                }

                .text {
                    font-size: var(--font-size-L);
                    font-weight: 500;
                    color: #0B0B0B;
                    margin-left: 10px;
                    @include ellipses(1);
                }
            }

            &__content {
                .unit {
                    font-size: var(--font-size-L);
                    font-weight: 400;
                    color: #0B0B0B;
                    line-height: 22px;
                }

                .time {
                    font-size: var(--font-size-M);
                    font-weight: 400;
                    color: #0B0B0B;
                    margin: 5px 0;
                }

                .teacher {
                  align-items: center;
                  font-size: var(--font-size-M);
                  font-weight: 400;
                  color: #0B0B0B;
                  display: inline-block;
                  line-height: 22px;

                  &__header {
                      width: 22px;
                      height: 22px;
                      border-radius: 100px;
                      margin-right: 5px;
                  }

                  &__name {
                      font-weight: 500;
                      color: #0B0B0B;
                      flex: 1;
                      @include ellipses(1);
                  }

                  .f16 {
                    color: #0B0B0B;
                  }

                  &:hover {
                    .f16 {
                      color: blue
                    }
                  }
                }
            }
        }

        .state {
            position: absolute;
            width: 80px;
            height: 37px;
            font-size: var(--font-size-L);
            font-weight: 500;
            color: #FFFFFF;
            line-height: 27px;
            text-align: center;
            background-size: contain !important;
        }

        .button-group {
            display: flex;
            &__grey {
                width: 80px;
                height: 25px;
                background: #BFBFBF;
                box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), inset 1px 1px 1px 0px #E7E7E7, inset -1px -1px 1px 0px rgba(129, 129, 134, 0.32);
                border-radius: 22px;
                font-size: var(--font-size-L);
                font-weight: 500;
                color: #FFFFFF;
                line-height: 35px;
                text-align: center;
                cursor: pointer;
            }

            &__blue {
                width: 80px;
                height: 25px;
                border-radius: 23px;
                font-size: var(--font-size-L);
                font-weight: 500;
                line-height: 35px;
            }

            .margin-right {
                margin-right: 25px;
            }
        }
    }

    .empty-container {
      height: auto;
      flex: 1;
      img {
        width: 90px;
        height: 90px;
      }

      .hint {
        font-size: var(--font-size-M);
      }
    }

    .loading-text {
      text-align: center;
      padding-bottom: 10px;
      font-size: var(--font-size-L);
    }

    .bubble {
      width: 62px;
      height: 62px;
      border-radius: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: fixed;
      background: #4387FF;
      box-shadow: 0px 2px 3px 0px rgba(21, 95, 255, 0.2);
      left: 216px;
      bottom: 182px;
      justify-content: center;
      cursor: pointer;

      .appoint {
        width: 22px;
        height: 20px;
      }

      .text {
        font-size: var(--font-size-L);
        font-weight: 500;
        color: #FFFFFF;
        line-height: 20px;
        margin-top: 2px;
      }
    }

    .yueke {
      margin-right: 10px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      cursor: pointer;
      border-radius: 5px;
      background: #2F80ED;
      padding: 6px 10px;
      color: #fff;
      font-weight: 500;
      font-size: var(--font-size-L);

      .appoint {
        width: 22px;
        height: 19px;
        margin-right: 8px;
      }
    }

    .pl-10 {
      padding-left: 10px;
    }

    .mb-10 {
      margin-bottom: 10px;
    }

    .mb-20 {
      margin-bottom: 20px;
    }

    .mr-26 {
      margin-right: 26px;
    }

    .mr-30 {
      margin-right: 30px;
    }
  }
}
</style>
