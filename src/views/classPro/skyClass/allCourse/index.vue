<template>
  <div class="all-container">
    <div class="wrap">
      <div class="header">
        <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="prePage" />
        <div class="back" @click="prePage">全部课程</div>
      </div>
      <div class="content">
        <el-menu
          :default-active="defautIndex"
          class="el-menu-vertical-course"
          :collapse="false"
        >
          <el-submenu index="1">
            <template slot="title">
              <div class="submenu">
                <div class="el-icon"></div>
                <span>学习中（{{ activeList.length }}）</span>
              </div>
            </template>
            <el-menu-item
              v-for="(item, index) in activeList"
              :key="item.id"
              :index="'1-' + index"
              :class="{'select':(selectCourse.id && selectCourse.id === item.id)}"
              @click="changeCourse(item)"
            >
              <div class="flex">
                <div
                  class="label"
                  :class="{
                    'red': subjectName(item.course.type) === '语',
                    'orange': subjectName(item.course.type) === '英',
                    'yellow': subjectName(item.course.type) === '文',
                    'green': subjectName(item.course.type) === '双'
                  }"
                >{{ subjectName(item.course.type) }}</div>
                <div class="name" :class="{'color-blue': selectCourse.id && selectCourse.id === item.id}" style="-webkit-box-orient: vertical;">{{ item.course ? item.course.name || '' : '' }}</div>
              </div>
              <div class="select-flat"></div>
            </el-menu-item>
          </el-submenu>
          <el-submenu index="2">
            <template slot="title">
              <div class="submenu">
                <div class="el-icon" style="background: #02CE40;"></div>
                <span>已结束（{{ finsihList.length }}）</span>
              </div>
            </template>
            <el-menu-item
              v-for="(item, index) in finsihList"
              :key="item.id"
              :index="'2-' + index"
              :class="{'select':(selectCourse.id && selectCourse.id === item.id)}"
              @click="changeCourse(item)"
            >
              <div class="flex">
                <div
                  class="label"
                  :class="{
                    'red': subjectName(item.course.type) === '语',
                    'orange': subjectName(item.course.type) === '英',
                    'yellow': subjectName(item.course.type) === '文',
                    'green': subjectName(item.course.type) === '双'
                  }"
                >{{ subjectName(item.course.type) }}</div>
                <div class="name" :class="{'color-blue': selectCourse.id && selectCourse.id === item.id}" style="-webkit-box-orient: vertical;">{{ item.course ? item.course.name || '' : '' }}</div>
              </div>
              <div class="select-flat"></div>
            </el-menu-item>
          </el-submenu>
          <el-submenu index="3">
            <template slot="title">
              <div class="submenu">
                <div class="el-icon" style="background: red;"></div>
                <span>运营添加（{{ missList.length }}）</span>
              </div>
            </template>
            <el-menu-item
              v-for="(item, index) in missList"
              :key="item.id"
              :index="'3-' + index"
              :class="{'select':(selectMissCourse && selectMissCourse.id && selectMissCourse.id === item.id)}"
              @click="changeMissCourse(item)"
            >
              <div class="flex">
                <div
                  class="label"
                  :class="{
                    'red': subjectName(item.type) === '语',
                    'orange': subjectName(item.type) === '英',
                    'yellow': subjectName(item.type) === '文'
                  }"
                >{{ subjectName(item.type) }}</div>
                <div class="name" :class="{'color-blue': selectMissCourse && selectMissCourse.id && selectMissCourse.id === item.id}" style="-webkit-box-orient: vertical;">{{ item.name || '' }}</div>
              </div>
              <div class="select-flat"></div>
            </el-menu-item>
          </el-submenu>
        </el-menu>
        <div v-if="selectCourse.course && selectCourse.course.id" class="content__right">
          <div class="course mb-10">
            <img :src="selectCourse.course ? selectCourse.course.coverUrl || DefaultCover : DefaultCover" alt="封面" />
            <div class="course-info">
              <div class="flex mb-10">
                <div
                  class="label"
                  :class="{
                    'red': subjectName(selectCourse.type || selectCourse.course.type) === '语',
                    'orange': subjectName(selectCourse.type || selectCourse.course.type) === '英',
                    'yellow': subjectName(selectCourse.type || selectCourse.course.type) === '文',
                    'green': subjectName(selectCourse.type || selectCourse.course.type) === '双'
                  }"
                >
                  {{ subjectFullName(selectCourse.course.type) }}
                </div>
                <div class="course-info-title">{{ selectCourse.course ? selectCourse.course.name : '' }}</div>
              </div>
              <div class="des" style="-webkit-box-orient: vertical;">{{ selectCourse.course ? selectCourse.course.subtitle || '' : '' }}</div>
              <div class="progress">
                <div class="text">当前学习进度：第{{ selectCourse.finishedProgress || 0 }}/{{ selectCourse.total || 0 }}讲</div>
                <el-progress :text-inside="true" :stroke-width="14"  :percentage="progress" />
              </div>
            </div>
            <!-- <div
              v-if="selectCourse.scheduledProgress < selectCourse.total"
              class="classpro-btn"
              @click="openBook"
            >
              约课
            </div> -->
          </div>
          <div class="lesson-list">
            <div v-for="(item, index) in selectLessonList" :key="item.id" class="lesson-item">
              <div class="lesson-item-left">
                <div class="number-bg"><div class="number">{{ index + 1 || 0 }}</div></div>
              </div>
              <div class="lesson-item-right">
                <div class="lesson-info">
                  <div class="lesson-name">{{ item.name || 0 }}</div>
                  <div v-if="lessonStatus(item) !== -1" class="lesson-normal-text lesson-time">上课时间：{{ formatDot(item.lesson2.startTime) }}  {{ formatWeek(item.lesson2.startTime) }}  {{ formatHHmm(item.lesson2.startTime) }}</div>
                  <div v-if="lessonStatus(item) !== -1" class="teacher" @click="$emit('showTeacherInfo', item.lesson2.teacherId)">
                    <div class="lesson-normal-text flex">
                      上课教师：
                      <div style="font-weight: 500; margin-right: 25px;">{{ item.lesson2.lessonInfo2.teacherName }}</div>
                      <div class="f16">简介></div>
                    </div>
                  </div>
                  <div v-if="lessonStatus(item) === -1" class="lesson-normal-text lesson-des">{{ item.subtitle || '' }}</div>
                </div>
                <div v-if="lessonStatus(item) === 0" class="lesson-operation3">
                  <div class="lesson-operation2">
                    <div class="classpro-btn-2" @click="handleXueqing(item)">学情沟通</div>
                  </div>
                  <div class="lesson-operation2">
                    <div class="classpro-btn" @click="toClassroom(item)">进入教室</div>
                    <div v-if="showDiffer(item)" class="lesson-normal-text limit-time flex">距离上课还有 <div style="color: #F97E56;">{{ formatdiffer(item.lesson2.startTime) }}</div> 分钟</div>
                  </div>
                </div>
                <div v-if="lessonStatus(item) === 1" class="lesson-operation3">
                  <div class="lesson-operation2">
                    <div class="classpro-btn-2" @click="handleXueqing(item)">学情沟通</div>
                  </div>
                  <div class="lesson-operation2">
                    <div class="classpro-btn" @click="toClassroom(item)">进入教室</div>
                    <div v-if="showStart(item)" class="lesson-normal-text limit-time flex">已经开始上课 <div style="color: #F97E56;">{{ formatDifferUp(item.lesson2.startTime) }}</div> 分钟</div>
                  </div>
                </div>
                <div v-if="lessonStatus(item) === 2" class="lesson-operation">
                  <div class="lesson-operation-item" @click="handleXueqing(item)">
                    <img :src="iconXueqing" />
                    <div class="lesson-normal-text" style="color: #1F66FF;">学情沟通</div>
                  </div>
                  <div v-if="item.lesson2 && item.lesson2.lessonDetail" class="lesson-operation-item" @click="enterClassroom(item)">
                    <img :src="item.lesson2.lessonDetail.hasRecorder ? iconVideo : iconVideoGrey" />
                    <div class="lesson-normal-text" style="color: #1F66FF;">{{ item.lesson2.lessonDetail.hasRecorder ? '查看回放' : '无回放' }}</div>
                  </div>
                  <div class="lesson-operation-item" @click="openCourseware(item)">
                    <img :src="iconSourceware" />
                    <div class="lesson-normal-text" style="color: #1F66FF;">查看课件</div>
                  </div>
                </div>
                <div v-if="lessonStatus(item) === 3" class="lesson-operation">
                  <div class="lesson-operation-item" @click="handleXueqing(item)">
                    <img :src="iconXueqing" />
                    <div class="lesson-normal-text" style="color: #1F66FF;">学情沟通</div>
                  </div>
                  <div class="lesson-operation-item" @click="_applyLesson(item)">
                    <img :src="iconMissedLesson" />
                    <div class="lesson-normal-text" style="color: #FBB32D;">申请补课</div>
                  </div>
                  <div class="lesson-operation-item" @click="openCourseware(item)">
                    <img :src="iconSourceware" />
                    <div class="lesson-normal-text" style="color: #1F66FF;">查看课件</div>
                  </div>
                </div>
                <div v-if="!isLock(index) && lessonStatus(item) === -1" class="lesson-operation3">
                  <div class="lesson-operation2">
                    <div class="classpro-btn-2" @click="openBook(item)">预约</div>
                  </div>
                  <div class="lesson-operation2">
                    <div class="classpro-btn" @click="openCourseware2(item)">查看课件</div>
                  </div>
                </div>
                <div v-if="isLock(index)" class="lesson-lock">
                  <img :src="iconLock" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="selectMissCourse && selectMissCourse.id" class="content__right">
          <div class="course mb-10">
            <img :src="selectMissCourse.coverUrl || DefaultCover" alt="封面" />
            <div class="course-info">
              <div class="flex mb-10">
                <div
                  class="label"
                  :class="{
                    'red': subjectName(selectMissCourse.type) === '语',
                    'orange': subjectName(selectMissCourse.type) === '英',
                    'yellow': subjectName(selectMissCourse.type) === '文',
                    'green': subjectName(selectMissCourse.type) === '双'
                  }"
                >
                  {{ subjectFullName(selectMissCourse.type) }}
                </div>
                <div class="course-info-title">{{ selectMissCourse.name || '' }}</div>
              </div>
              <div class="des" style="-webkit-box-orient: vertical;">{{ selectMissCourse.subtitle || '' }}</div>
            </div>
          </div>
          <div class="lesson-list">
            <div v-for="item in selectMissLessonList" :key="item.id" class="lesson-item">
              <div class="lesson-item-left"></div>
              <div class="lesson-item-right">
                <div class="lesson-info">
                  <div class="lesson-name">{{ item.name || 0 }}</div>
                  <div class="lesson-normal-text lesson-time">上课时间：{{ formatDot(item.lesson2.startTime) }}  {{ formatWeek(item.lesson2.startTime) }}  {{ formatHHmm(item.lesson2.startTime) }}</div>
                  <!-- <div>
                    <div class="lesson-normal-text flex">上课教师：<div style="font-weight: 500;">{{ item.lesson2.lessonInfo2.teacherName }}</div></div>
                  </div> -->
                  <div class="teacher" @click="$emit('showTeacherInfo', item.lesson2.teacherId)">
                    <div class="lesson-normal-text flex">
                      上课教师：
                      <div style="font-weight: 500; margin-right: 25px;">{{ item.lesson2.lessonInfo2.teacherName }}</div>
                      <div class="f16">简介></div>
                    </div>
                  </div>
                </div>
                <div v-if="missLessonStatus(item) === 0" class="lesson-operation2">
                  <div class="classpro-btn" @click="toClassroom(item)">进入教室</div>
                  <div v-if="showDiffer(item)" class="lesson-normal-text limit-time flex">距离上课还有 <div style="color: #F97E56;">{{ formatdiffer(item.lesson2.startTime) }}</div> 分钟</div>
                  <div v-if="showStart(item)" class="lesson-normal-text limit-time flex">已经开始上课 <div style="color: #F97E56;">{{ formatDifferUp(item.lesson2.startTime) }}</div> 分钟</div>
                </div>
                <div v-else class="lesson-operation">
                  <div v-if="item.lesson2 && item.lesson2.lessonDetail" class="lesson-operation-item" @click="enterClassroom(item)">
                    <img :src="item.lesson2.lessonDetail.hasRecorder ? iconVideo : iconVideoGrey" />
                    <div class="lesson-normal-text" style="color: #1F66FF;">{{ item.lesson2.lessonDetail.hasRecorder ? '查看回放' : '无回放' }}</div>
                  </div>
                  <div class="lesson-operation-item" @click="openCourseware(item)">
                    <img :src="iconSourceware" />
                    <div class="lesson-normal-text" style="color: #1F66FF;">查看课件</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="content__right-empty">
          <img src="@/assets/images/dashboard/no-ai-course.png" alt="ai课堂为空" />
          <p>没有选择课程哦~</p>
        </div>
      </div>
      <book :show-book="showBook" :book-id="(selectCourse && selectCourse.course && selectCourse.course.id) || (selectMissCourse && selectMissCourse.id)" :book-info="bookInfo" @closeBook="closeBook" @showTeacherInfo="_showTeacherInfo" />
      <courseware :show-drawer="showCourseware" :document-id="documentId" @closeDrawer="showCourseware = false" />
      <apply-dialog :dialog-visible="showApply" :msg="applyMsg" @closeDialog="showApply = false" @showBook="showBook = true" />
      <XueqingDialog v-if="showXueqing" :dialog-visible="showXueqing" :msg="xqInfo" @closeDialog="showXueqing = false" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Book from '../Book'
import ApplyDialog from '../ApplyDialog'
import XueqingDialog from '../Xueqing'
import Courseware from '../courseware'
import DefaultCover from '@/assets/images/default-cover.jpg'
import iconMissedLesson from '@/assets/images/skyclass/icon-missed-lesson.png'
import iconSourceware from '@/assets/images/skyclass/icon-sourceware.png'
import iconXueqing from '@/assets/images/skyclass/icon-xueqing.png'
import iconVideo from '@/assets/images/skyclass/icon-video.png'
import iconVideoGrey from '@/assets/images/skyclass/icon-video-grey.png'
import iconLock from '@/assets/images/skyclass/icon-lock.png'
import { formatDot, formatWeek, formatHHmm, formatdiffer, formatDifferUp } from '@/utils/date.js'
import { getStuCourseList } from '@/api/lesson-api.js'
import { getCourseLessonPlanList, getPlanLessonCourse, getPlanLessonCourseUnit } from '@/api/course-api.js'
import { applyLesson } from '@/api/lesson-api.js'
export default {
  components: {
    Book,
    Courseware,
    ApplyDialog,
    XueqingDialog
  },
  data () {
    return {
      DefaultCover,
      formatDot,
      formatWeek,
      formatHHmm,
      formatdiffer,
      formatDifferUp,
      iconMissedLesson,
      iconSourceware,
      iconXueqing,
      iconVideo,
      iconVideoGrey,
      iconLock,
      activeList: [],
      finsihList: [],
      missList: [],
      defautIndex: '',
      selectCourse: {
        'course': {}
      },
      selectMissCourse: {},
      selectId: '0',
      selectLessonList: [],
      selectMissLessonList: [],
      showBook: false,
      bookInfo: {},
      showCourseware: false,
      showXueqing: false,
      xqInfo: null,
      documentId: 0,
      showApply: false,
      applyMsg: '',
      canGoToClass: true
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ]),
    progress () {
      if (this.selectCourse.finishedProgress && this.selectCourse.total) {
        return Math.floor((this.selectCourse.finishedProgress / this.selectCourse.total) * 100)
      }
      return 0
    }
  },
  async created () {
    this.selectId = this.$route.params && this.$route.params.id
    await this._getStuCourseList()
    this.initSelectCourse()
  },
  mounted () {
    this.$bus.$on('getStuCourseList', async () => {
      this.selectCourse = {
        'course': {}
      }
      this.selectMissCourse = {}
      this.bookInfo = {}
      this.selectLessonList = []
      this.selectMissLessonList = []
      await this._getStuCourseList()
      this.selectId = '0'
      this.initSelectCourse()
    })
  },
  destroyed () {
    this.$bus.$off('getStuCourseList')
  },
  methods: {
    prePage () {
      this.$router.push({ path: '/classpro/skyClass' })
    },
    // 获取课程列表
    async _getStuCourseList () {
      this.activeList = []
      this.finsihList = []
      this.missList = []
      const params1 = {
        'studentCourseListType': 'COMPLETED_COURSE',
        'assistantUserId': this.$store.getters.id
      }
      const params2 = {
        'studentCourseListType': 'UNCOMPLETED_COURSE',
        'assistantUserId': this.$store.getters.id
      }
      await getStuCourseList(params1)
        .then(response => {
          if (response.data !== null) this.finsihList = response.data
        })
      await getStuCourseList(params2)
        .then(response => {
          if (response.data !== null) this.activeList = response.data
        })
      await getPlanLessonCourse({
        assistantUserId: this.id
      }).then(response => {
        if (response.data != null) this.missList = response.data
      })
    },
    // 获取课程计划列表
    async _getCourseLessonPlanList () {
      const params = { 'studentCourseId': this.selectCourse.id }
      getCourseLessonPlanList(params).then(response => {
        this.selectLessonList = response.data
      })
    },
    // 获取补课课程包单元列表
    async _getPlanLessonCourseUnit () {
      const params = { courseId: this.selectMissCourse.id, assistantUserId: this.id }
      getPlanLessonCourseUnit(params).then(response => {
        this.selectMissLessonList = response.data
      })
    },
    // 初始化首个课程
    initSelectCourse () {
      if (this.selectId === '0') {
        if (this.activeList.length > 0) {
          this.selectCourse = this.activeList[0]
          this.defautIndex = '1-0'
          this._getCourseLessonPlanList()
          return
        } else if (this.finsihList.length > 0) {
          this.selectCourse = this.finsihList[0]
          this.defautIndex = '2-0'
          this._getCourseLessonPlanList()
          return
        } else if (this.missList.length > 0) {
          this.selectMissCourse = this.missList[0]
          this.defautIndex = '3-0'
          this._getPlanLessonCourseUnit()
          return
        }
      } else {
        for (var i = 0; i < this.activeList.length; i++) {
          if (this.activeList[i].id.toString() === this.selectId) {
            this.selectCourse = this.activeList[i]
            this.defautIndex = '1-' + i
            this._getCourseLessonPlanList()
            return
          }
        }
        for (var i2 = 0; i2 < this.finsihList.length; i2++) {
          if (this.finsihList[i2].id.toString() === this.selectId) {
            this.selectCourse = this.finsihList[i2]
            this.defautIndex = '2-' + i2
            this._getCourseLessonPlanList()
            return
          }
        }
      }
    },
    lessonStatus (lessonStatus) {
      if (!lessonStatus.lesson2 || !lessonStatus.lesson2.lessonStatus) {
        return -1
      }
      switch (lessonStatus.lesson2.lessonStatus) {
        case 'ComingSoon':
          return 0
        case 'Opening':
          return 1
        case 'UnderReview':
        case 'Completed':
        case 'Unqualified':
          return 2
        case 'TeachersAbsenteeism':
        case 'StudentsAbsenteeism':
          return 3
        default:
          return -1
      }
    },
    isLock (index) {
      // if (!this.selectLessonList[index].lesson2 || !this.selectLessonList[index].lesson2.lessonStatus) {
      //   if (index === 0) return false
      //   for (let i = 0; i < index; i++) {
      //     if (!this.selectLessonList[i].lesson2) return true
      //   }
      //   return false
      // }
      return false
    },
    missLessonStatus (lessonStatus) {
      switch (lessonStatus.lesson2.lessonStatus) {
        case 'ComingSoon':
        case 'Opening':
          return 0
        case 'UnderReview':
        case 'Completed':
        case 'TeachersAbsenteeism':
        case 'StudentsAbsenteeism':
        case 'Unqualified':
          return 1
        default:
          return -1
      }
    },
    subjectName (type) {
    // ENGLISH(0, "英语"),
    // CHINESE_CLASS(1, "传统文化"),
    // CHINESE(2, "大语文"),
    // MATH(3, "数学"),
    // MUSIC(4, "音乐"),
    // ART(5, "美术"),
    // MIX(6, "双科"),
    // PROGRAMMING(7, "少儿编程"),
    // CRAFT(8, "手工拓展"),
      switch (type) {
        case 2:
          return '语'
        case 1:
          return '文'
        case 0:
          return '英'
        case 3:
          return '数'
        case 4:
          return '音'
        case 5:
          return '美'
        case 6:
          return '双'
        case 7:
          return '程'
        case 8:
          return '手'
        default:
          return ''
      }
    },
    subjectFullName (type) {
    // ENGLISH(0, "英语"),
    // CHINESE_CLASS(1, "传统文化"),
    // CHINESE(2, "大语文"),
    // MATH(3, "数学"),
    // MUSIC(4, "音乐"),
    // ART(5, "美术"),
    // MIX(6, "双科"),
    // PROGRAMMING(7, "少儿编程"),
    // CRAFT(8, "手工拓展"),
      switch (type) {
        case 2:
          return '大语文'
        case 1:
          return '传统文化'
        case 0:
          return '英语'
        case 3:
          return '数学'
        case 4:
          return '音乐'
        case 5:
          return '美术'
        case 6:
          return '双科'
        case 7:
          return '少儿编程'
        case 8:
          return '手工拓展'
        default:
          return ''
      }
    },
    toClassroom (item) {
      if (!item.lesson2.lessonInfo2 || !item.lesson2.lessonInfo2.roomUrl || !this.canGoToClass) {
        return
      }
      this.canGoToClass = false
      setTimeout(() => {
        this.canGoToClass = true
      }, 8000)
      this.$message.warning({
        message: '正在进入教室请勿重复点击',
        duration: 8000
      })
      window.open(item.lesson2.lessonInfo2.roomUrl, '_blank')
    },
    changeCourse (item) {
      this.selectMissCourse = {}
      this.selectCourse = item
      this.selectId = item.id
      this.selectLessonList = []
      this.selectMissLessonList = []
      this._getCourseLessonPlanList()
    },
    changeMissCourse (item) {
      this.selectCourse = {
        'course': {}
      }
      this.selectMissCourse = item
      this.selectId = item.id
      this.selectLessonList = []
      this.selectMissLessonList = []
      this._getPlanLessonCourseUnit()
    },
    updateShowBook (val) {
      this.showBook = val
    },
    openBook (item) {
      this.bookInfo = {
        id: this.selectCourse.id,
        courseId: this.selectCourse.course.id,
        cover: this.selectCourse.course && this.selectCourse.course.coverUrl || '',
        name: this.selectCourse.course && this.selectCourse.course.name || '',
        type: this.selectCourse.teamId !== 0 ? 'TEAM' : 'MIN',
        lessonPlanId: item.id
      }
      this.showBook = true
    },
    async closeBook () {
      this.showBook = false
      await this._getStuCourseList()
      const allCourseList = this.activeList.concat(this.finsihList)
      this.selectCourse = allCourseList.filter(item => item.id === this.selectCourse.id)[0]
      this._getCourseLessonPlanList()
    },
    showDiffer (item) {
      const now = new Date().getTime()
      const time = (item.lesson2.startTime - now) / 1000 / 60
      if (time < 60 && time > 0) return true
      return false
    },
    showStart (item) {
      const now = new Date().getTime()
      const time = (item.lesson2.startTime - now) / 1000 / 60
      if (time <= 0) return true
      return false
    },
    _applyLesson (item) {
      if (!item.lesson2 || !item.lesson2.lessonDetail) {
        this.$message.error('数据有误')
        return
      }
      if (item.lesson2.lessonDetail.source === 'PLAN') {
        this.$message.error('该课程属于运营人员额外为您添加的课程，不能申请补课哦')
        return
      }
      if (item.lesson2.newLessonId) {
        this.$message.error('已申请补课')
        return
      }
      if (!item.lesson2.canPlan) {
        this.$message.error('补课时限已过')
        return
      }
      const params = {
        'lessonId': item.lesson2.id
      }
      applyLesson(params).then(response => {
        if (response.data.canPlan) {
          this.bookInfo = {
            id: this.selectCourse.id,
            courseId: this.selectCourse.course.id,
            cover: this.selectCourse.course && this.selectCourse.course.coverUrl || '',
            name: this.selectCourse.course && this.selectCourse.course.name || '',
            type: this.selectCourse.teamId !== 0 ? 'TEAM' : 'MIN',
            lessonPlanId: item.lesson2.lessonPlanId,
            lessonId: item.lesson2.id
          }
          this.showApply = true
          this.applyMsg = response.data.msg
        } else {
          this.$message.error(response.data.msg)
        }
      })
    },
    openCourseware (item) {
      this.documentId = item.lesson2.documentId
      this.showCourseware = true
    },
    openCourseware2 (item) {
      this.documentId = item.documentId
      this.showCourseware = true
    },
    handleXueqing (item) {
      this.xqInfo = item
      this.showXueqing = true
    },
    enterClassroom (item) {
      if (item.lesson2.lessonInfo2.roomUrl && item.lesson2.lessonDetail.hasRecorder) {
        window.open(item.lesson2.lessonInfo2.roomUrl, '_blank')
        setTimeout(() => {
          this.loading = false
        }, 2000)
      }
    },
    _showTeacherInfo (id) {
      this.$emit('showTeacherInfo', id)
    }
  }
}
</script>

<style lang='scss' scoped>
.all-container {
  width: 100%;
  // height: calc(100% - 62px);
  height: calc(100% - 1px);
}
.wrap {
  height: 100%;
  width: 100%;
  .header {
    display: flex;
    align-items: center;
    height: 40px;
    font-weight: 400;
    padding-left: 40px;
    img {
        width: 14px;
        height: 14px;
        object-fit: contain;
        cursor: pointer;
    }

    .back {
        font-size: var(--font-size-L);
        color: #1C1B1A;
        line-height: 33px;
        margin-left: 5px;
        cursor: pointer;
    }
  }

  .content {
    height: calc(100% - 40px);
    display: flex;

    .el-menu-vertical-course {
      width: 224px;
      height: 100%;
      overflow: scroll;
      background: #FFFFFF;
      border-radius: 0px 20px 20px 0px;
      height: 100%;
      border-right: 0px;
      @include noScrollBar;

      .submenu {
        display: flex;
        align-items: center;

        span {
          font-size: var(--font-size-L);
          font-weight: 500;
          color: #0B0B0B;
        }
      }

      .el-icon {
        width: 6px;
        height: 6px;
        border-radius: 100px;
        margin-right: 15px;
        background: #1F66FF;
      }

      .name {
        width: calc(100% - 20px);
        font-size: var(--font-size-L);
        font-weight: 400;
        color: #0B0B0B;
        padding-left: 8px;
        @include ellipses(1);
        display: block;
      }

      .color-blue {
        color: #1F66FF;
      }

      .select {
        width: 224px;
        height: 100%;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(31, 102, 255, 0.14) 100%);
        position: relative;

        .select-flat {
          width: 3px;
          height: 100%;
          background: #1F66FF;
          position: absolute;
          right: 0px;
          top: 0px;
        }
      }
    }

    &__right {
      width: calc(100% - 224px);
      height: 100%;
      padding-right: 20px;
      padding-left: 20px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .course {
          width: 100%;
          display: flex;
          background: #FFFFFF;
          box-shadow: 0px 4px 12px 7px rgba(62, 89, 253, 0.05);
          border-radius: 5px;
          padding: 20px;
          margin-left: auto;

          img {
              width: 289px;
              height: 139px;
              object-fit: cover;
              border-radius: 15px;
          }

          .course-info {
              width: calc(100% - 289px - 160px);
              height: 139px;
              padding-left: 20px;
              display: flex;
              flex-direction: column;
              justify-content: center;

              .course-info-title {
                  font-size: var(--font-size-L);
                  font-weight: 500;
                  color: #0B0B0B;
                  line-height: 22px;
                  margin-left: 10px;
              }

              .des {
                  width: 100%;
                  font-size: var(--font-size-L);
                  font-weight: 400;
                  color: #6D6D6D;
                  line-height: 20px;
                  word-break: break-all;
                  @include ellipses(3);
                  margin-bottom: auto;
              }

              .progress {
                  width: 100%;
                  font-size: var(--font-size-M);
                  font-weight: 400;
                  color: #0B0B0B;
                  line-height: 17px;
                  display: flex;
                  align-items: center;

                  .text {
                      width: 157px;
                  }
              }
          }

          .classpro-btn {
              margin-top: auto;
          }
      }

      .classpro-btn {
          width: 80px;
          height: 25px;
          padding: 8px 0;
          font-size: var(--font-size-L);
          font-weight: 500;
          color: #FFFFFF;
          border-radius: 23px;
          margin-left: auto;
      }

      .ml10 {
        margin-left: 10px;
      }

      .classpro-btn-2 {
          width: 80px;
          height: 25px;
          line-height: 8px;
          padding: 8px 0;
          font-size: var(--font-size-L);
          color: #3479FF;
          background: #CCDDFF;
          border-radius: 23px;
          text-align: center;
          cursor: pointer;
      }

      .lesson-list {
        flex: 1;
        padding-top: 24px;
        background: #FFFFFF;
        border-radius: 5px;
        .lesson-item {
          display: flex;
          align-items: center;
          margin-bottom: 22px;

          .lesson-item-left {
            margin: 0 20px;
            .number-bg {
                width: 33px;
                height: 33px;
                // opacity: 0.3;
                border: 2px solid rgba(31, 102, 255, 0.2);
                border-radius: 100px;
                padding: 4px;

                .number {
                  width: 100%;
                  height: 100%;
                  background: rgba(31, 102, 255, 0.2);
                  border-radius: 100px;
                  text-align: center;
                  font-size: var(--font-size-L);
                  font-weight: 500;
                  color: #1F66FF;
                  line-height: 22px;
                }
              }
          }

          .lesson-item-right {
            flex: 1;
            height: 130px;
            background: #FFFFFF;
            box-shadow: 0 4px 12px 7px rgba(62,89,253,0.02);
            border-radius: 22px;
            display: flex;
            justify-content: space-between;
            padding-left: 36px;
            position: relative;

            .lesson-info {
              width: calc(100% - 160px - 80px - 80px);
            }

            .lesson-name {
              font-size: var(--font-size-L);
              font-weight: 500;
              color: #0B0B0B;
              margin: 7px 0 13px;
            }

            .lesson-time {
              margin-bottom: 14px;
            }

            .lesson-des {
              width: 100%;
              line-height: 22px;
              font-size: var(--font-size-L);
              @include ellipses(3);
            }

            .lesson-operation,
            .lesson-operation2,
            .lesson-operation3 {
              img {
                width: 48px;
                height: 48px;
                margin-bottom: 8px;
              }

              .lesson-operation-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                cursor: pointer;
                width: 84px;
                margin-right: 36px;
              }
            }

            .lesson-operation {
              display: flex;
              margin-top: 38px;
            }

            .lesson-operation3 {
              display: flex;
            }

            .lesson-operation2 {
              margin: 55px 20px 0 0;
              display: flex;
              flex-direction: column;
              align-items: center;

              .limit-time {
                margin-top: 8px;
              }
            }

            .lesson-lock {
              width: 100%;
              height: 100%;
              position: absolute;
              background: rgba(0, 0, 0, 0.35);
              border-radius: 22px;
              left: 0;
              top: 0;

              img {
                width: 48px;
                height: 48px;
                position: absolute;
                right: 40px;
                top: 44px;
              }
            }
          }
        }
      }
    }

    &__right-empty {
      width: calc(100% - 224px);
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 150px;

      img {
        width: 200px;
        height: 200px;
      }
    }

    .label {
      background: #1F66FF;
      border-radius: 6px;
      margin: auto 0;
      line-height: 20px;
      text-align: center;
      font-size: var(--font-size-M);
      font-weight: 500;
      color: #FFFFFF;
      padding: 1px 4px;
    }

    .red {
      background: #FF675B;
    }

    .yellow {
      background: #AF7A40;
    }

    .orange {
      background: #FF8E44;
    }

    .green {
      background: green;
    }

    .lesson-normal-text {
      font-size: var(--font-size-L);
      font-weight: 400;
      color: #0B0B0B;
    }

    .teacher {
      display: inline-block;
      cursor: pointer;

      &:hover {
        .f16 {
          color: blue
        }
      }
    }
  }

  .mb-10 {
    margin-bottom: 10px;
  }
}
</style>

<style lang='scss'>
.all-container {
  .el-submenu__title i {
    color: #0B0B0B;
  }

  .el-submenu .el-menu-item {
    padding: 0px;
  }

  .el-progress {
        width: calc(100% - 210px);
    }

    .el-progress-bar__inner {
      line-height: 14px;
      background-color: #1F66FF;
    }

    .el-progress-bar__innerText {
        line-height: 14px;
        font-size: var(--font-size-M);
    }
}

</style>
