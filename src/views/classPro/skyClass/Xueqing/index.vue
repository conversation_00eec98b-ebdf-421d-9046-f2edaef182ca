<template>
  <NormalDialog
    :dialog-visible="dialogVisible"
    title="学情沟通"
    :width="'60%'"
    @closeDialog="$emit('closeDialog')"
  >
    <div class="x-content-box">
      <div v-if="chatList.length === 0">暂无内容</div>
      <div v-for="item in chatList" :key="item.id" class="chat-item">
        <div class="chat-item-title">
          <div v-show="item.userType === 'ASSISTANT'" class="tag-1">助教老师</div>
          <div v-show="item.userType === 'TEACHER'" class="tag-2">授课老师</div>
          <div>{{ item.createdAt }}</div>
        </div>
        <div>{{ item.content }}</div>
      </div>
    </div>
    <div slot="footer" class="w">
      <div class="w flex justify-center items-center">
        <el-input v-model="input" placeholder="输入和助教老师沟通内容" />
        <div class="classpro-btn" @click="submit">留言</div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog'
import { communicateLearning, learningCommunicationList } from '@/api/course-api.js'
import { debounce } from '@/utils/index'
export default {
  components: {
    NormalDialog
  },
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: true
    },
    msg: {
      type: Object,
      require: true,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {
      input: '',
      chatList: []
    }
  },
  mounted () {
    this._learningCommunicationList()
  },
  methods: {
    submit: debounce(async function () {
      if (!this.input) {
        this.$message.error('请输入内容')
        return
      }
      await communicateLearning({
        userType: 'ASSISTANT',
        lessonId: this.msg.lesson2.id,
        content: this.input
      })
      this.input = ''
      this._learningCommunicationList()
    }, 3000, true),
    async _learningCommunicationList () {
      const { data } = await learningCommunicationList({
        userType: 'ASSISTANT',
        lessonId: this.msg.lesson2.id
      })
      this.chatList = data
    }
  }
}
</script>

<style lang='scss' scoped>
.classpro-btn {
  width: 140px;
  height: 35px;
  padding: 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
  border-radius: 23px;
  margin-left: auto;
  margin-left: 15px;
}

.x-content-box {
  width: 100%;
  max-height: 280px;
  overflow: hidden;
  overflow-y: auto;

  .tag-1, .tag-2 {
    padding: 3px;
    border-radius: 7px;
    font-size: 14px;
  }

  .tag-1 {
    color: #2D9CDB;
    background: #E5F4FD;
  }

  .tag-2 {
    color: #F2994A;
    background: #FEF4EB;
  }

  .chat-item {
    width: 100%;
    color: #4F4F4F;
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 30px;
    .chat-item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      color: #828282;
      font-size: 12px;
    }
  }
}
</style>
