<template>
  <com-dialog :dialog-visible="dialogVisible" :title="title" @closeDialog="closeDialog">
    <div class="cancel-dialg">
      <div class="des">确定取消课程吗？</div>
      <div class="button-group">
        <div class="classpro-btn-opacity left-btn" @click="closeDialog">再想想</div>
        <div class="classpro-btn right-btn" @click="rightBtnCallback">确定取消</div>
      </div>
    </div>
  </com-dialog>
</template>

<script>
import ComDialog from '@/components/classPro/ComponentDialog'
export default {
  components: {
    ComDialog
  },
  props: {
    dialogVisible: {
      type: Boolean,
      require: true,
      default: false
    }
  },
  data () {
    return {
      title: '取消约课'
    }
  },
  methods: {
    closeDialog () {
      this.$emit('closeDialog')
    },
    rightBtnCallback () {
      this.$emit('rightBtnCallback')
    }
  }
}
</script>

<style lang='scss' scoped>
.des {
    font-size: 16px;
    color: #0B0B0B;
    line-height: 10px;
    text-align: center;
    height: 40px;
}

.button-group {
    width: 270px;
    display: flex;
    justify-content: space-between;
    margin-top: 25px;

    .left-btn {
        width: 130px;
        height: 28px;
        border-radius: 16px;
        line-height: 28px;
        text-align: center;
        cursor: pointer;
    }

    .right-btn {
        width: 130px;
        height: 28px;
        border-radius: 16px;
        border: 1px solid #1F66FF;
    }
}
</style>
