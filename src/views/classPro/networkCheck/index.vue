<template>
  <div class="network-check">
    <img class="right-bottom" src="@/assets/images/networkcheck/right-bottom-bg.png" />
    <div class="title">
      <i class="iconfont el-icon-arrow-left pointer" @click="back">返回</i>
      设备检测
    </div>
    <div class="main">
      <template v-if="checkIndex !== '检测结果'">
        <div class="left">
          <div
            v-for="(item) in listItems"
            :key="item.index"
            :class="{'check-list-text-active':checkIndex === item.name}"
            class="check-list"
            @click="handleList(item)"
          >
            <div v-show="checkIndex === item.name" class="check-list-active"></div>
            <i class="iconfont" :class="item.icon"></i>
            {{ item.name }}
            <template v-if="report[item.name] && report[item.name].isCheck">
              <i v-if="!report[item.name].isErr" class="iconfont icon-icon-complete" style="color: #02CE40;"></i>
              <i v-else class="iconfont icon-yichang" style="color: #FF4141;"></i>
            </template>
            <i v-else class="iconfont icon-yichang" style="color: #FF4141;visibility: hidden;"></i>
          </div>
        </div>
        <div class="right">
          <Network v-if="checkIndex === '网络检测'" :devices="devices" @next="next" />
          <Media v-else-if="checkIndex === '摄像头检测'" @next="next" />
          <Voi v-else-if="checkIndex === '扬声器检测'" @next="next" />
          <Mic v-else-if="checkIndex === '麦克风检测'" :devices="devices" @next="next" />
        </div>
      </template>
      <template v-else>
        <Result :report="report" @reCheck="reCheck" />
      </template>
    </div>
    <com-dialog :dialog-visible="dialogVisible" :title="'提示'" @closeDialog="dialogVisible = false">
      <div class="cancel-dialg flex flex-col">
        <div class="des">你确定要离开吗？设备检测尚未完成，可能会影响上 课效果。</div>
        <div class="button-group flex justify-between">
          <div class="classpro-btn-opacity" @click="dialogVisible = false">否</div>
          <div class="classpro-btn" @click="$router.push({ path: '/classpro' })">是</div>
        </div>
      </div>
    </com-dialog>
  </div>
</template>

<script>
import Network from './components/network.vue'
import Media from './components/media.vue'
import Voi from './components/voi.vue'
import Mic from './components/mic.vue'
import Result from './components/result.vue'
import { getAiConfig } from '@/api/dictionary-api.js'
import { logEquipmentStatu } from '@/api/classroom-api.js'
import ComDialog from '@/components/classPro/ComponentDialog'

export default {
  components: {
    Network,
    Media,
    Voi,
    Mic,
    Result,
    ComDialog
  },
  data () {
    return {
      dialogVisible: false,
      checkIndex: '摄像头检测',
      report: {
        '摄像头检测': {
          isCheck: false,
          isErr: true
        },
        '扬声器检测': {
          isCheck: false,
          isErr: true
        },
        '麦克风检测': {
          isCheck: false,
          isErr: true
        },
        '网络检测': {
          isCheck: false,
          isErr: true
        }
      },
      listItems: {
        '摄像头检测': {
          name: '摄像头检测',
          icon: 'icon-shexiangtou',
          typeName: 'CAMERA',
          index: 1
        },
        '扬声器检测': {
          name: '扬声器检测',
          icon: 'icon-yangshengqibeifen',
          typeName: 'LOUDSPEAKER',
          index: 2
        },
        '麦克风检测': {
          name: '麦克风检测',
          icon: 'icon-a-maikefeng2',
          typeName: 'MICROPHONE',
          index: 3
        },
        '网络检测': {
          name: '网络检测',
          icon: 'icon-wangluofuwu',
          typeName: 'NETWORK',
          index: 0
        }
      },
      devices: {}
    }
  },
  mounted () {
    this.getDevices()
  },
  methods: {
    handleList (item) {
      this.checkIndex = item.name
      if (this.report[item.name]) {
        this.report[item.name].isCheck = false
        this.report[item.name].isErr = true
      }
    },
    async getDevices () {
      const { data: camData } = await getAiConfig({ configType: 'DEVICE_CAMERA_NAME' })
      const { data: micDta } = await getAiConfig({ configType: 'DEVICE_MICROPHONE_NAME' })
      this.devices = {
        camData,
        micDta
      }
    },
    next (info) {
      this.report[info.name] = info.report
      const obj = { equipmentType: this.listItems[info.name].typeName, status: info.report.isErr ? 'FAID' : 'OK' }
      if (info.info) {
        obj.info = info.info
      }
      logEquipmentStatu(obj)
      switch (info.name) {
        case '网络检测':
          this.checkIndex = '检测结果'
          break
        case '摄像头检测':
          this.checkIndex = '扬声器检测'
          break
        case '扬声器检测':
          this.checkIndex = '麦克风检测'
          break
        case '麦克风检测':
          this.checkIndex = '网络检测'
          break
      }
    },
    reCheck () {
      this.reset()
      this.checkIndex = '摄像头检测'
    },
    back () {
      if (this.checkIndex !== '检测结果') {
        this.dialogVisible = true
        // this.$confirm('你确定要离开吗？设备检测尚未完成，可能会影响上 课效果。', '提示', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {
        //   this.$router.push({ path: '/classpro' })
        // }).catch(() => {
        // })
      } else {
        // this.$router.push({ path: '/classpro' })
        this.$route.go(-1)
      }
    },
    reset () {
      this.report = {
        '摄像头检测': {
          isCheck: false,
          isErr: true
        },
        '扬声器检测': {
          isCheck: false,
          isErr: true
        },
        '麦克风检测': {
          isCheck: false,
          isErr: true
        },
        '网络检测': {
          isCheck: false,
          isErr: true
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.network-check {
  position: relative;
  height: calc(100% - 62px);
  background: #F8FAFF;
  position: relative;

  .right-bottom {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 174px;
  }

  .title {
    height: 65px;
    width: 100%;
    display: flex;
    align-items: center;
    color: #0B0B0B;
    font-size: var(--font-size-XXL);
    font-weight: 500;
    padding-left: 46px;

    .iconfont {
      font-size: var(--font-size-XXL);
      margin-right: 17px;
    }

  }

  .main {
    display: flex;
    width: 100%;
    height: calc(100% - 85px);

    .left {
      width: 200px;
      border-radius: 0px 20px 20px 0px;
      background: #FFFFFF;
      padding: 57px 0 0 51px;
      box-sizing: border-box;
      font-size: 18px;

      .check-list {
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        position: relative;
        font-size: var(--font-size-XL);
        color: #0B0B0B;
        margin: 10px 0;
        cursor: pointer;

        .iconfont {
          margin-right: 5px;
          font-size: var(--font-size-XL);
          &:last-child {
            margin-left: 10px;
          }
        }
      }

      .check-list-text-active {
        color: #1F66FF;
      }

      .check-list-active {
        position: absolute;
        border-right: 3px solid #1F66FF;
        right: 0;
        width: 60%;
        height: 40px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(31, 102, 255, 0.14) 100%);
      }
    }

    .right {
      width: calc(100% - 224px);;
      overflow-y: auto;
      display: flex;
      justify-content: center;
      //padding-top: 45px;
      box-sizing: border-box;
    }
  }
}

.cancel-dialg {
  align-items: center;
  .des {
    font-size: 16px;
    color: #0B0B0B;
    text-align: center;
    margin-bottom: 20px;
  }
  .button-group {
    width: 100%;
    .classpro-btn-opacity, .classpro-btn {
      width: 100px;
      height: 30px;
    }
  }
}
</style>

