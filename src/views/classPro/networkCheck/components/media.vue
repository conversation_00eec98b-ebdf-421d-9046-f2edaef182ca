<template>
  <div class="check-detail">
    <div class="check-title">
      摄像头：
    </div>
    <div class="check-right">
      <el-select
        v-model="value"
        style="width: 100%;"
        :popper-class="'check-popper'"
        placeholder="请选择"
        @change="deviceChange"
      >
        <el-option
          v-for="item in options"
          :key="item.deviceId"
          :label="item.label"
          :value="item.deviceId"
        />
      </el-select>
      <div id="play" class="w media-box"></div>
      <div class="check-text">

        <el-popover
          placement="top-start"
          width="440"
          trigger="click"
          :popper-class="'check-popover-tip'"
        >
          <div class="check-tips">
            <i class="iconfont icon-dengpao"></i>
            <div>

              <p>1.检查摄像头指示灯是否正常，正常工作时指示灯为绿灯 </p>

              <p>2.检查电源是否开启，插座是否正常连接</p>

              <p>3.检查USB连接线是否松动，重新插拔摄像头</p>

              <p>4.检查是否选择了正确的摄像头</p>
            </div>
          </div>

          <span slot="reference">
            <i class="iconfont icon-a-wenhao1"></i>
            看不到视频画面？
            点击这里
          </span>
        </el-popover>
      </div>
      <div class="check-btn-box">
        <div class="classpro-btn-opacity check-btn" @click="handleClickType(false)">
          可以看到
        </div>
        <div class="classpro-btn-opacity check-btn" @click="handleClickType(true)">
          不可以看到
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RtcClient from '@/sdk/rtc'
import { getAiConfig } from '@/api/dictionary-api.js'

export default {
  data () {
    return {
      options: [],
      value: '',
      rtc: null,
      originValue: '',
      devices: {},
      report: {
        isCheck: true,
        isErr: true
      }
    }
  },
  async mounted () {
    try {
      await this.getDevices()
      this.rtc = new RtcClient({ mode: 'rtc', codec: 'vp8' })
      await this.rtc.mediaDeviceTest('play', this.devices.camData)
      this.options = this.rtc.cams
      this.value = this.rtc.currentCam.deviceId
      this.originValue = this.rtc.currentCam.deviceId
    } catch (error) {
      this.options = []
      this.value = ''
      this.rtc
    }
  },
  async beforeDestroy () {
    if (this.rtc && this.rtc.localTracks.videoTrack) {
      this.rtc.localTracks.videoTrack.stop()
      this.rtc.localTracks.videoTrack.close()
    }
  },
  methods: {
    async getDevices () {
      const { data: camData } = await getAiConfig({ configType: 'DEVICE_CAMERA_NAME' })
      const { data: micDta } = await getAiConfig({ configType: 'DEVICE_MICROPHONE_NAME' })
      this.devices = {
        camData,
        micDta
      }
    },
    async deviceChange (val) {
      console.log(val)
      try {
        await this.rtc.localTracks.videoTrack.setDevice(val)
        this.originValue = val
      } catch (err) {
        console.log(err)
        this.value = this.originValue
        this.$message.error('切换失败')
      }
    },
    handleClickType (type) {
      this.report = {
        isCheck: true,
        isErr: type
      }
      this.next()
    },
    next () {
      this.$emit('next', { name: '摄像头检测', report: this.report })
    }
  }
}
</script>

<style scoped lang="scss">
.check-detail {
  width: 70%;
  height: 90%;
  display: flex;
  flex-direction: row;

  .check-title {
    width: 140px;
    color: #0B0B0B;
    font-size: var(--font-size-XL);
    display: flex;
    justify-content: flex-start;
  }

  .check-right {
    width: 676px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .media-box {
      height: 380px;
      border: 2px solid rgba(140, 147, 153, 0.19);
      background: #FFFFFF;
      margin-top: 30px;
      margin-bottom: 30px;
    }

    .check-text {
      color: #1F66FF;
      font-size: var(--font-size-L);
      cursor: pointer;
      margin-bottom: 20px;

      .iconfont {
        margin-right: 5px;
        font-size: var(--font-size-L);
      }
    }

    .check-btn-box {
      display: flex;
      width: 100%;
      justify-content: space-around;
    }

    ::v-deep .el-input__inner {
      background: transparent;
      color: #0B0B0B;
      font-size: var(--font-size-XL);
      height: 30px;
      &::placeholder {
        font-size: var(--font-size-XL);
        color: #0B0B0B;
      }
    }
    ::v-deep .el-select .el-input__inner {
      border-color: rgba(140, 147, 153, 0.19) !important;
    }
    ::v-deep .el-select .el-input .el-select__caret {
      color: #0B0B0B;
      font-size: 24px;
      display: flex;
      align-items: center;
    }
  }

  .check-btn {
    width: 30vh;
    height: 30px;
    font-size: var(--font-size-L);
  }
}
.check-tips {
  display: flex;

  .iconfont {
    color: #f7c466;
    font-size: 20px;
    width: 40px;
  }
  .tips-title {
    font-size: 18px;
    margin-bottom: 20px;
  }
  div {
    width: calc(100% - 40px);
    color: #fff;

    p {
      margin: 0;
      margin-bottom: 20px;
      font-size: 14px;
      &:last-child {
        margin: 0;
      }
    }
  }
}
</style>

