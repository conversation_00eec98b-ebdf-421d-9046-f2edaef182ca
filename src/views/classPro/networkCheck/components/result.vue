<template>
  <div class="result-box">
    <el-card class="result-card">
      <div class="result-title">
        <div class="result-title-left">
          <template v-if="isFail">
            <i class="iconfont icon-yichang err-color"></i>
            <div class="flex flex-col justify-around h">
              <div class="err-color"><span>设备状况异常</span></div>
              <div class="sub">完成设备检测，请重新检测设备</div>
            </div>
          </template>
          <template v-else>
            <i class="iconfont icon-icon-complete success-color"></i>
            <div class="flex flex-col justify-around h">
              <div class="success-color"><span>设备状况正常</span></div>
              <div class="sub">完成设备检测，当前设备运转良好</div>
            </div>
          </template>
        </div>
        <div>
          <div class="classpro-btn check-btn" @click="handleClick">
            {{ isFail ? '重新检测' : '确定' }}
          </div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="result">
        <div v-for="(item, key) in report" :key="key" class="result-list">
          <div>{{ key }}</div>
          <div v-show="!item.isErr" class="success-color">正常</div>
          <div v-show="item.isErr" class="err-color">异常</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  props: {
    report: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {

    }
  },
  computed: {
    isFail () {
      let flag = false
      if (this.report) {
        for (const key in this.report) {
          if (this.report[key].isErr) {
            flag = true
            break
          }
        }
      }
      return flag
    }
  },
  methods: {
    handleClick () {
      if (this.isFail) {
        this.$emit('reCheck')
      } else {
        this.$router.push({ path: '/classpro' })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.result-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .err-color {
    color: #FF4141;
    > span {
      font-size: 30px;
      font-weight: 500;
    }
  }

  .success-color {
    color: #02CE40;
    > span {
      font-size: 30px;
      font-weight: 500;
    }
  }

  .divider {
    width: 100%;
    height: 1px;
    background: rgba(31, 102, 255, 0.44);
    margin: 60px 0;
  }

  .result-card {
    width: 1337px;
    height: 655px;
    padding: 70px 90px;

    .result-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .sub {
        font-size: 20px;
        color: #0B0B0B;
      }

      .icon-icon-complete {
        font-size: 80px;
      }

      .icon-yichang {
        font-size: 80px;
      }

      .result-title-left {
        display: flex;
        justify-content: space-around;
        align-items: center;
        // width: 320px;
        height: 80px;

        .iconfont {
          padding-right: 40px;
        }
      }

      .check-btn {
        height: 45px;
        width: 160px;
        font-size: 21px;
      }
    }

    .result {
      padding-left: 120px;
      padding-right: 60px;
      font-weight: 500;
      color: #0B0B0B;
      font-size: 20px;

      .result-list {
        margin-bottom: 40px;
        display: flex;
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}
</style>

