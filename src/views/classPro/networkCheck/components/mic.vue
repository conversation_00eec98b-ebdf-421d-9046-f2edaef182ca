<template>
  <div class="check-detail">
    <div class="check-title">
      麦克风：
    </div>
    <div class="check-right">
      <el-select
        v-model="value"
        style="width: 100%;"
        :popper-class="'check-popper'"
        placeholder="请选择"
        @change="deviceChange"
      >
        <el-option
          v-for="item in options"
          :key="item.deviceId"
          :label="item.label"
          :value="item.deviceId"
        />
      </el-select>

      <div class="w mic-box">
        <div class="img-box">
          <div class="mic-img">
            <img src="@/assets/images/networkcheck/play-mic.png" alt="" />
          </div>
          <div class="volume-box">
            <div class="volumeMeter">
              <div class="volumeMeterBg"></div>
              <div ref="volumeMeterInner" class="volumeMeterInner"></div>
              <div v-for="item in VolumeMeterList" :key="item.key" class="volumeMeterDivider" :style="item.style"></div>
            </div>
          </div>
        </div>

        <div class="text-content">
          按下“录音”按钮开始录音：请对着麦克风从1数到10，并查看绿色音 量条是否波动
        </div>
      </div>
      <div class="check-text">

        <el-popover
          placement="top-start"
          width="440"
          trigger="click"
          :popper-class="'check-popover-tip'"
        >
          <div class="check-tips">
            <i class="iconfont icon-dengpao"></i>
            <div>

              <p>1.检查麦克风指示灯是否正常，正常工作时指示灯为绿灯</p>

              <p>2.检查电源是否开启，插座是否正常连接</p>

              <p>3.检查USB连接线是否松动，重新插拔麦克风</p>

              <p>4.检查是否选择了正确的麦克风</p>
            </div>
          </div>

          <span slot="reference">
            <i class="iconfont icon-a-wenhao1"></i>
            看不到波动？
            点击这里
          </span>
        </el-popover>
      </div>
      <div class="check-btn-box">
        <div class="classpro-btn-opacity check-btn" @click="handleClickType(false)">
          可以看到
        </div>
        <div class="classpro-btn-opacity check-btn" @click="handleClickType(true)">
          不可以看到
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RtcClient from '@/sdk/rtc'

export default {
  props: {
    devices: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data () {
    return {
      options: [],
      value: '',
      rtc: null,
      VolumeMeterList: [],
      max: 19,
      time: null,
      originValue: '',
      report: {
        isCheck: true,
        isErr: true
      }
    }
  },
  async mounted () {
    try {
      const max = this.max
      this.createVolumeMeterDivider(max)
      this.rtc = new RtcClient({ mode: 'rtc', codec: 'vp8' })
      await this.rtc.micDeviceTest(this.devices.micDta)
      this.options = this.rtc.mics
      this.value = this.rtc.currentMic.deviceId
      this.originValue = this.rtc.currentMic.deviceId
      this.time = setInterval(this.volum, 500)
    } catch (error) {
      this.options = []
      this.value = ''
      this.rtc = null
    }
  },
  async beforeDestroy () {
    clearInterval(this.time)
    if (this.rtc && this.rtc.localTracks.audioTrack) {
      this.rtc.localTracks.audioTrack.stop()
      this.rtc.localTracks.audioTrack.close()
    }
  },
  methods: {
    createVolumeMeterDivider (max = 10) {
      const list = []
      const width = 100 / (max * 2)
      for (let i = 0; i < max; i += 1) {
        list.push({
          key: i,
          style: `left: ${(2 * i + 1) * width}%; width: ${width}%;`
        })
      }
      this.VolumeMeterList = list
    },
    volum () {
      const volume = this.rtc.localTracks.audioTrack.getVolumeLevel() * 100
      const width = Math.ceil((volume * this.max) / 100) / this.max
      this.$refs.volumeMeterInner.style.transform = `scaleX(${width})`
    },
    async deviceChange (val) {
      try {
        await this.rtc.localTracks.audioTrack.setDevice(val)
        this.originValue = val
      } catch (err) {
        this.value = this.originValue
        this.$message.error('切换失败')
      }
    },
    handleClickType (type) {
      this.report = {
        isCheck: true,
        isErr: type
      }
      this.next()
    },
    next () {
      this.$emit('next', { name: '麦克风检测', report: this.report })
    }
  }
}
</script>

<style scoped lang="scss">
.check-detail {
  width: 70%;
  height: 90%;
  display: flex;
  flex-direction: row;

  .check-title {
    width: 140px;
    color: #0B0B0B;
    font-size: var(--font-size-XL);
    display: flex;
    justify-content: flex-start;
  }

  .check-right {
    width: 676px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .mic-box {
      height: 380px;
      border: 2px solid rgba(140, 147, 153, 0.19);
      background: #FFFFFF;
      box-sizing: border-box;
      padding: 30px 60px;
      margin-top: 30px;
      margin-bottom: 30px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .img-box {
        width: 100%;
        height: 60%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .mic-img {
          cursor: pointer;
          img {
            width: 88px;
            height: 88px;
          }
        }

        .volume-box {
          width: calc(100% - 208px);
        }

        .volumeMeter {
          position: relative;
          width: 100%;
          height: 32px;
          overflow: hidden;
          background-color: #FFFFFF;

          .volumeMeterBg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ECECEC;
          }

          .volumeMeterInner {
            background-color: #02CE40;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform-origin: 0 50%;
            transition: transform 0.1s ease-out;
            transform: scaleX(0);
          }

          .volumeMeterDivider {
            position: absolute;
            top: 0;
            left: 0;
            width: 5%;
            height: 100%;
            background-color: inherit;
          }
        }
      }

      .text-content {
        color: #8C9399;
        font-size: var(--font-size-L);
        line-height: 25px;
        padding: 0 0 0 20px;
      }
    }

    .check-text {
      color: #1F66FF;
      font-size: var(--font-size-L);
      cursor: pointer;
      margin-bottom: 20px;

      .iconfont {
        margin-right: 5px;
        font-size: var(--font-size-L);
      }
    }

    .check-btn-box {
      display: flex;
      width: 100%;
      justify-content: space-around;
    }

    ::v-deep .el-input__inner {
      background: transparent;
      color: #0B0B0B;
      font-size: var(--font-size-XL);
      height: 30px;
      &::placeholder {
        font-size: var(--font-size-XL);
        color: #0B0B0B;
      }
    }
    ::v-deep .el-select .el-input__inner {
      border-color: rgba(140, 147, 153, 0.19) !important;
    }
    ::v-deep .el-select .el-input .el-select__caret {
      color: #0B0B0B;
      font-size: var(--font-size-XL);
    }
  }

  .check-btn {
    width: 30vh;
    height: 30px;
    font-size: var(--font-size-XL);
  }
}
.check-tips {
  display: flex;

  .iconfont {
    color: #f7c466;
    font-size: var(--font-size-XL);
    width: 40px;
  }
  .tips-title {
    font-size: 18px;
    margin-bottom: 20px;
  }
  div {
    width: calc(100% - 40px);
    color: #fff;

    p {
      margin: 0;
      margin-bottom: 20px;
      font-size: var(--font-size-L);
      &:last-child {
        margin: 0;
      }
    }
  }
}
</style>

