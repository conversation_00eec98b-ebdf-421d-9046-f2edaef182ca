<template>
  <div class="check-detail">
    <div class="progress-box">
      <template v-if="isProgress">
        <div class="progress-background">
          <el-progress
            color="#1F66FF"
            :stroke-width="10"
            type="circle"
            :percentage="progress"
            :show-text="false"
          />
          <div class="progress-text">{{ progress }}%</div>
        </div>
        <div>
          检测中...
        </div>
      </template>
      <template v-else>
        <div class="wifi-box">
          <img v-if="report.isErr" width="100%" height="100%" src="@/assets/images/networkcheck/wifi-lock.png" />
          <img v-else width="100%" height="100%" src="@/assets/images/networkcheck/wifi-success.png" />
        </div>
        <!-- <div class="wifi-err">1</div> -->
        <div :class="report.isErr ? 'wifi-err' : 'wifi-success'">
          <div>{{ report.isErr ? '网络不给力，请检查网络设置' : '太棒了！网络良好' }}</div>
          <div v-if="videoStats" class="wifi-text">
            <div>
              <span>网络延迟</span>
              {{ videoStats.end2EndDelay }}ms
            </div>
            <div>
              <span>丢包率</span>
              {{ videoStats.currentPacketLossRate }}%
            </div>
          </div>
          <div v-else-if="isDownloadImg" class="wifi-text">
            <div>
              <span>平均速率</span>
              {{ speedArray | aveSpeedFilter }}
            </div>
            <div>
              <span>最大速率</span>
              {{ speedArray | maxSpeedFilter }}
            </div>
          </div>
        </div>
      </template>
    </div>

    <div class="check-line"></div>
    <div class="check-tips">
      <p>网络不好？</p>
      <p>1.建议使用有线网络，比无线网络(WiFi)更加稳定流畅</p>
      <p>2.网络高峰时段可能会出现音视频卡顿、客户端掉线等情况</p>
      <p>3.如果检查结果异常，请调整设置并重新测试</p>
    </div>
    <div :class="isProgress ? 'classpro-btn-disable' : 'classpro-btn'" class="check-btn" @click="next">
      下一步
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getGenerateToken } from '@/api/classroom-api'
import RtcClient from '@/sdk/rtc'
import axios from 'axios'
export default {
  filters: {
    /**
       * 平均速率过滤器：
       *      先得到速率之和，然后用和除以数量
       * @param speedArray
       * @returns {string}
       */
    aveSpeedFilter (speedArray) {
      if (speedArray.length === 0) return
      let totalSpeed = 0
      const number = speedArray.length
      for (const speed of speedArray) {
        totalSpeed += speed
      }
      const aveSpeed = totalSpeed / number
      return aveSpeed.toFixed(2) + 'Mbps'
    },
    /**
       * 最大速率过滤器：
       *       先得到速率之和，然后依次筛选出最大速率
       * @param speedArray
       * @returns {string}
       */
    maxSpeedFilter (speedArray) {
      if (speedArray.length === 0) return
      let maxSpeed = 0
      for (const speed of speedArray) {
        if (speed > maxSpeed) maxSpeed = speed
      }
      return maxSpeed.toFixed(2) + 'Mbps'
    }
  },
  data () {
    return {
      report: {
        isCheck: true,
        isErr: true
      },
      uplinkClient: null,
      downlinkClient: null,
      videoStats: null,
      progress: 0,
      progressTime: null,
      isProgress: true,
      isError: false,
      count: 0,
      speedArray: [],
      maxCount: 60,
      waitTime: 300,
      flag: false,
      isDownloadImg: false
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  mounted () {
    if (navigator.onLine) {
      this._getGenerateToken()
    } else {
      this.isProgress = false
      this.report.isErr = true
    }
  },
  async beforeDestroy () {
    if (this.uplinkClient) {
      await this.uplinkClient.leave()
    }
    if (this.downlinkClient) {
      await this.downlinkClient.leave()
    }
  },
  methods: {
    creatRoomId () {
      return 'test' + new Date().valueOf() + Math.floor(Math.random() * 10) + Math.floor(Math.random() * 10)
    },
    async _getGenerateToken () {
      try {
        this.progress = 0
        this.isProgress = true
        const quality = { 0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0 }
        let count = 0
        const roomId = this.creatRoomId()
        const { data: uplinkData } = await getGenerateToken({ roomId, uid: +this.id, role: 'writer' })
        const { data: downlinkData } = await getGenerateToken({ roomId, uid: +this.id + 100000000, role: 'writer' })
        this.uplinkClient = new RtcClient({ mode: 'rtc', codec: 'vp8' })
        this.downlinkClient = new RtcClient({ mode: 'rtc', codec: 'vp8' })
        await this.uplinkClient.join({
          channel: roomId,
          token: uplinkData.rtcToken,
          uid: +this.id
        })
        await this.uplinkClient.publish()

        await this.downlinkClient.join({
          channel: roomId,
          token: downlinkData.rtcToken,
          uid: +this.id + 100000000
        })

        let time = 0
        this.progressTime = setInterval(() => {
          this.progress = this.progress + Math.floor(Math.random() * 10) + 5
          time++
          if (time > 9 || this.progress > 85) {
            clearInterval(this.progressTime)
          }
        }, 1000)

        setTimeout(() => {
          this.downlinkClient.on('network-quality', async (evt) => {
            if (count > 3) {
              this.progress = 100
              setTimeout(() => {
                this.downlinkClient.client.removeAllListeners('network-quality')
                if (quality[5] >= 2 || quality[6] >= 2 || quality[6] + quality[5] >= 2) {
                  this.report.isErr = true
                } else {
                  this.report.isErr = false
                }

                // 获取下行统计数据
                const downlinkVideoStats = this.downlinkClient.client.getRemoteVideoStats()[+this.id]
                const { currentPacketLossRate, end2EndDelay } = downlinkVideoStats
                this.videoStats = { currentPacketLossRate, end2EndDelay }
                this.isProgress = false
              }, 1000)
            }
            quality[evt.downlinkNetworkQuality]++
            count++
          })
        }, 3000)
      } catch (error) {
        this.uplinkClient = null
        this.downlinkClient = null
        this.progress = 0
        this.isDownloadImg = true
        this.isError = false
        this.count = 0
        this.speedArray = []
        this.flag = true
        this.useImageSize()
      }
    },
    // 备用网络测速 通过下载图片的方式
    async useImageSize () {
      try {
        const startTime = new Date().getTime()
        const url = process.env.VUE_APP_DOWNLOAD_IMG_URL + '?n=' + Math.random()
        const imageSize = process.env.VUE_APP_DOWNLOAD_IMG_SIZE
        await axios.get(url, { responseType: 'arraybuffer' })

        const endTime = new Date().getTime()
        const diffSeconds = (endTime - startTime) / 1000 // 差时间转为秒
        const bps = imageSize / diffSeconds
        const speedBps = bps * 8 // 每秒下载多少B的资源
        const speedKbps = speedBps / 1024 // 每秒下载多少KB（千B）的资源
        const speedMbps = speedKbps / 1024 // 每秒下载多少MB（兆B）的资源
        // 将该次测速得到的速率追加到速率速组里
        this.speedArray.push(speedMbps)
        if (this.count < this.maxCount) { // 如果没有到达最大次数，则依然执行
          this.startDownload()
        } else {
          this.flag = false
          this.isProgress = false
          if (this.count === this.maxCount) {
            if (this.aveSpeedFilter(this.speedArray) > 2) {
              this.report.isErr = false
            } else {
              this.report.isErr = true
            }
          } else {
            this.report.isErr = true
          }
        }
      } catch (error) {
        this.isProgress = false
        this.report.isErr = true
      }
    },
    /**
       * 平均速率过滤器：
       *      先得到速率之和，然后用和除以数量
       * @param speedArray
       * @returns {string}
       */
    aveSpeedFilter (speedArray) {
      if (speedArray.length === 0) return
      let totalSpeed = 0
      const number = speedArray.length
      for (const speed of speedArray) {
        totalSpeed += speed
      }
      const aveSpeed = totalSpeed / number
      return aveSpeed.toFixed(2)
    },
    startDownload () {
      setTimeout(() => {
        this.useImageSize()
      }, this.waitTime)
      this.count += 10
      this.progress = +(this.count / this.maxCount * 100).toFixed(0)
    },
    next () {
      if (!this.isProgress) {
        let info = ''
        if (this.videoStats) {
          info = `延迟: ${this.videoStats.end2EndDelay}ms,400ms内丢包率:${this.videoStats.currentPacketLossRate}%`
        } else if (this.isDownloadImg) {
          info = `平均速率: ${this.aveSpeedFilter(this.speedArray)},最大速率:${this.maxSpeedFilter(this.speedArray)}`
        }
        this.$emit('next', { name: '网络检测', report: this.report, info })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.check-detail {
  width: 70%;
  height: 90%;
  display: flex;
  flex-direction: column;

  .progress-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 3vh;
    font-weight: 500;
    color: #1F66FF;
    padding-bottom: 5vh;

    .progress-background {
      background: rgba(31, 102, 255, 0.09);
      width: 20vh;
      height: 20vh;
      border-radius: 50%;
      position: relative;
      margin-right: 111px;

      .progress-text {
        position: absolute;
        top: 18px;
        bottom: 18px;
        right: 18px;
        left: 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 4vh;
        font-weight: 500;
        color: #1F66FF;
      }

      ::v-deep svg path:first-child {
        stroke: #fff;
      }
      ::v-deep svg path:last-child {
        stroke-linecap: square;
      }

      ::v-deep .el-progress-circle {
        width: 20vh!important;
        height: 20vh!important;
      }
    }

    .wifi-box {
      width: 20vh;
      height: 20vh;
      margin-right: 111px;
    }

    .wifi-err {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 100%;
      color: #FF4141;
      font-size: 30px;

      .wifi-text {
        font-size: 20px;

        div {
          padding: 10px 0;
        }

        span {
          color: #0B0B0B;
          margin-right: 50px;
        }
      }
    }

    .wifi-success {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 100%;
      color: #02CE40;
      font-size: var(--font-size-XL);

      .wifi-text {
        font-size: var(--font-size-L);

        div {
          padding: 10px 0;
        }

        span {
          color: #0B0B0B;
          margin-right: 50px;
        }
      }
    }
  }

  .check-line {
    height: 1px;
    width: 100%;
    background: rgba(31, 102, 255, 0.44);
  }

  .check-tips {
    p {
      font-size: var(--font-size-L);
      font-weight: 400;
      line-height: 3vh;

      &:first-child {
        font-weight: 500;
        font-size: var(--font-size-XL);
        margin-top: 3.7vh;
      }
    }
  }

  .check-btn {
    width: 30vh;
    height: 30px;
    font-size: var(--font-size-XL);
    align-self: flex-end;
  }
}
</style>

