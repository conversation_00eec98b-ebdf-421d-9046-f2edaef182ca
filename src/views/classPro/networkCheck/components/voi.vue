<template>
  <div class="check-detail">
    <div class="check-title">
      扬声器：
    </div>
    <div class="check-right">
      <el-select
        v-model="value"
        style="width: 100%;"
        :popper-class="'check-popper'"
        placeholder="请选择"
        @change="deviceChange"
      >
        <el-option
          v-for="item in options"
          :key="item.deviceId"
          :label="item.label"
          :value="item.deviceId"
        />
      </el-select>

      <div class="w media-box">
        <div class="play-img" @click="playBtnClick">
          <div v-show="isPlay" parent="box">
            <div class="outer-circle">
              <div class="inner-circle">
                <img src="@/assets/images/networkcheck/play-stop.png" alt="" />
              </div>
            </div>
          </div>
          <audio id="audio-player" ref="buttonAudio" controls="controls" loop hidden :src="buttonAu"></audio>
          <img v-show="!isPlay" class="img-a" src="@/assets/images/networkcheck/play-voice.png" alt="" />
        </div>
        <div class="text-content">
          请点击“播放”按钮播放试听声音，确认扬声器是否正常
        </div>
      </div>
      <div class="check-text">
        <el-popover
          placement="top-start"
          width="440"
          trigger="click"
          :popper-class="'check-popover-tip'"
        >
          <div class="check-tips">
            <i class="iconfont icon-dengpao"></i>
            <div>

              <p>1.检查扬声器指示灯是否正常，正常工作时指示灯为绿灯 </p>

              <p>2.检查电源是否开启，插座是否正常连接</p>

              <p>3.检查USB连接线是否松动，重新插拔扬声器</p>

              <p>4.检查系统中的默认扬声器是否设置正确</p>
            </div>
          </div>

          <span slot="reference">
            <i class="iconfont icon-a-wenhao1"></i>
            听不到声音？点击这里
          </span>
        </el-popover>
      </div>
      <div class="check-btn-box">
        <div class="classpro-btn-opacity check-btn" @click="handleClickType(false)">
          可以听到
        </div>
        <div class="classpro-btn-opacity check-btn" @click="handleClickType(true)">
          不可以听到
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RtcClient from '@/sdk/rtc'
import buttonAu from '@/assets/images/networkcheck/background.mp3'
export default {
  data () {
    return {
      buttonAu: buttonAu,
      options: [],
      value: '',
      rtc: null,
      originValue: '',
      isPlay: false,
      report: {
        isCheck: true,
        isErr: true
      }
    }
  },
  async mounted () {
    try {
      this.rtc = new RtcClient({ mode: 'rtc', codec: 'vp8' })
      await this.rtc.playbackDeviceTest()
      this.options = this.rtc.playbacks
      this.value = this.rtc.currentPlayback.deviceId
      this.originValue = this.rtc.currentPlayback.deviceId
    } catch (error) {
      this.options = []
      this.value = ''
      this.rtc = null
    }
  },
  async beforeDestroy () {
    if (this.rtc && this.rtc.localTracks.audioTrack) {
      this.rtc.localTracks.audioTrack.stop()
      this.rtc.localTracks.audioTrack.close()
    }
  },
  methods: {
    async deviceChange (val) {
      const audioPlayer = document.getElementById('audio-player')
      audioPlayer && (await audioPlayer.setSinkId(val))
    },
    playBtnClick () {
      const audio = this.$refs.buttonAudio
      if (this.isPlay) {
        audio.pause()
      } else {
        audio.currentTime = 0
        audio.play()
      }
      this.isPlay = !this.isPlay
    },
    handleClickType (type) {
      this.report = {
        isCheck: true,
        isErr: type
      }
      this.next()
    },
    next () {
      this.$emit('next', { name: '扬声器检测', report: this.report })
    }
  }
}
</script>

<style scoped lang="scss">
.check-detail {
  width: 70%;
  height: 90%;
  display: flex;
  flex-direction: row;

  .check-title {
    width: 140px;
    color: #0B0B0B;
    font-size: var(--font-size-XL);
    display: flex;
    justify-content: flex-start;
  }

  .check-right {
    width: 676px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .media-box {
      height: 280px;
      border: 2px solid rgba(140, 147, 153, 0.19);
      background: #FFFFFF;
      margin-top: 30px;
      margin-bottom: 30px;
      box-sizing: border-box;
      padding: 30px 60px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;

      .play-img {
        cursor: pointer;
        .img-a {
          width: 88px;
          height: 88px;
        }
      }

      .text-content {
        color: #8C9399;
        font-size: var(--font-size-L);
        line-height: 25px;
        width: 200px;
      }
    }

    .check-text {
      color: #1F66FF;
      font-size: var(--font-size-L);
      cursor: pointer;
      margin-bottom: 20px;

      .iconfont {
        margin-right: 5px;
        font-size: var(--font-size-L);
      }
    }

    .check-btn-box {
      display: flex;
      width: 100%;
      justify-content: space-around;
    }

    ::v-deep .el-input__inner {
      background: transparent;
      color: #0B0B0B;
      font-size: var(--font-size-L);
      height: 30px;
      &::placeholder {
        font-size: var(--font-size-L);
        color: #0B0B0B;
      }
    }
    ::v-deep .el-select .el-input__inner {
      border-color: rgba(140, 147, 153, 0.19) !important;
    }
    ::v-deep .el-select .el-input .el-select__caret {
      color: #0B0B0B;
      font-size: 24px;
    }
  }

  .check-btn {
    width: 30vh;
    height: 30px;
    font-size: var(--font-size-L);
  }
}
.check-tips {
  display: flex;

  .iconfont {
    color: #f7c466;
    font-size: var(--font-size-L);
    width: 40px;
  }
  .tips-title {
    font-size: var(--font-size-L);
    margin-bottom: 20px;
  }
  div {
    width: calc(100% - 40px);
    color: #fff;

    p {
      margin: 0;
      margin-bottom: 20px;
      font-size: 14px;
      &:last-child {
        margin: 0;
      }
    }
  }
}

div[parent="box"] {
  position:relative;
  margin:50px auto;
  width:78px;
  height:78px;
}
.outer-circle {
  animation: circleAnimationOut 1s cubic-bezier(0.165, 0.84, 0.44, 1);
}
.inner-circle {
  animation: circleAnimationIn 1s cubic-bezier(0.165, 0.84, 0.44, 1);

  img {
    width: 78px;
    height: 78px;
  }
}
.outer-circle, .inner-circle {
  position: absolute;
  // z-index:;
  width: 78px;
  height: 78px;
  background: transparent;
  border-radius: 100%;
  animation-iteration-count: infinite;
}

@keyframes circleAnimationOut {
  0% {
    box-shadow: 0 0 0 0px rgba(31, 102, 255,0.5);
  }
  100% {
    box-shadow: 0 0 0 36px rgba(31, 102, 255, 0.2);
  }
}
@keyframes circleAnimationIn {
  0% {
    box-shadow: 0 0 0 0px rgba(31, 102, 255,0.5);
    background-color: rgba(31, 102, 255,1);
  }
  100% {
    box-shadow: 0 0 0 20px rgba(31, 102, 255,0.5);
    background-color: rgba(31, 102, 255,1);
  }
}
</style>
