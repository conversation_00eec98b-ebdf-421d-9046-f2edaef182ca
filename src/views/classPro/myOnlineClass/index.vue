<template>
  <div class="index-box">
    <div class="my-course">
      <div class="title">精品课程</div>
      <div>
        <template>
          <div
            v-for="item in courseList"
            :key="item.id"
            class="course-list"
          >
            <img class="course-img" :src="item.course.cover?item.course.cover:defaultCover" alt="" />
            <div class="flex flex-col course-content justify-around">
              <div class="c-title">
                {{ item.course.title }}
              </div>
              <div class="c-intro">
                {{ item.course.description }}
              </div>
            </div>
            <div v-if="item.course.hasAigc" class="button2" @click="toDetail(item.course)">AI实验室</div>
            <div class="button1" @click="toCoursePage(item.course)">立即学习</div>
          </div>
        </template>
      </div>
      <div v-if="courseList.length===0" class="empty-box">
        <img class="empty" src="@/assets/images/empty.png" alt="占位图2" />
        <div>暂无在线课堂</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getQingguoketangUserCourseList } from '@/api/qingguoketang.js'
export default {
  data() {
    return {
      courseList: [
      ]
    }
  },
  mounted() {
    this.getCourseList()
  },
  methods: {
    queryURLParams(URL) {
      const url = URL.split('?')[1]
      const urlSearchParams = new URLSearchParams(url)
      const params = Object.fromEntries(urlSearchParams.entries())
      console.log(params)
      return params
    },
    getCourseList() {
      getQingguoketangUserCourseList({ pageSize: 1000, pageNo: 1 }).then(res => {
        this.courseList = res.data
      })
    },
    async toCoursePage (item) {
      window.open(item.courseUrl)
    },
    toDetail (item) {
      console.log(item)
      window.open(item.aigcUrl)
    }
  }
}
</script>

  <style lang="scss" scoped>
  .button1{
      background: #FFFFFF70;
      border: 1px solid #000000;
      border-radius: 5px;
      width: 100px;
      height: 30px;
      font-size: var(--font-size-M);
      text-align: center;
      line-height: 30px;
      margin-right: 20px;
      margin-top: 40px;
      cursor: pointer;
  }
  .button1:hover{
      background: #000;
      color: #ffffff;
  }
  .button2{
      background: #000000;
      border: 1px solid #000000;
      border-radius: 5px;
      width: 100px;
      height: 30px;
      font-size: var(--font-size-M);
      text-align: center;
      line-height: 30px;
      position: absolute;
      right: 30px;
      top:100px;
      color: #fff;
      font-weight: 600;
      cursor: pointer;
  }
  .button2:hover{
      background: #443939;
      border: 1px solid #000000;
  }
  .index-box {
      width: 100%;
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      box-sizing: border-box;
      @include scrollBar;
      background: #FFF;
      border-radius: 10px;
      padding: 20px;
      .my-course{
          width: 100%;
          height: 100%;
          .course-list {
          padding: 10px;
          height: 170px;
          border-radius: 10px;
          margin-top: 20px;
          box-sizing: border-box;
          display: flex;
          position: relative;
          background: linear-gradient(270deg, rgba(132, 250, 176, 0.3) 0%, rgba(143, 211, 244, 0.3) 100%);
          .course-img {
              width: 280px;
              height: 150px;
              object-fit: cover;
              border-radius: 5px;
          }
          .course-content {
              flex: 1;
              justify-content:center;
              margin-left: 20px;
              margin-right: 50px;
              .c-title {
              font-weight: 500;
              font-size: 12px;
              color: #0b0b0b;
              @include ellipses(1);
              margin-bottom: 20px;
            }
            .c-intro {
              font-size: 10px;
              color: #0b0b0b;
              @include ellipses(5);
              line-height: 15px;
              min-height: 75px;
            }
            .c-state {
              font-size: 14px;
              color: #0b0b0b;
              .progress {
                color: #1cba76;
              }
              .img {
                width: 12px;
                height: 12px;
                margin: 0 5px;
              }
            }
          }
          .course-btn {
            width: 180px;
            position: relative;
          }}
      }
      .title{
          color: #000;
          font-size: var(--font-size-L);
          font-weight: 500;
      }
      .empty-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: calc(100% - 50px);
      .empty {
        width: 100px;
        height: 100px;
        margin-bottom: 20px;
      }
    }
  }
  </style>
