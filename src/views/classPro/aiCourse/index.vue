<template>
  <div class="index-box">
    <div class="card-box">
      <div class="card-title-box">
        <div class="t-title">双师AI课堂</div>
        <!-- <div class="t-more">
          <span class="fb">审核单位：</span>
          <img class="mr-5" :src="Institution" alt="机构" />
          中华文化数字研究院
        </div> -->
      </div>
      <div class="course-tags-box">
        <div
          class="tags-btn"
          :class="{ 'active-btn': selectSeries.id === -1 }"
          @click="changeSeries()"
        >全部</div>
        <div
          v-for="item in seriesList"
          :key="item.id"
          :class="{ 'active-btn': selectSeries.id === item.id }"
          class="tags-btn"
          @click="changeSeries(item)"
        >{{ item.name || '' }}</div>
      </div>

      <div v-if="selectSeries && selectSeries.childCategoryList" class="type-box">
        <!-- <el-select
          v-model="value"
          class="autoWidth"
          placeholder="请选择"
          @change="changeSubject"
        >
          <template slot="prefix">
            {{ (selectSeries.childCategoryList.find(item => item.id === value) || {name: ''}).name }}
          </template>
          <el-option label="全部" :value="''" />
          <el-option v-for="item in selectSeries && selectSeries.childCategoryList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select> -->
        <el-popover
          v-model="visible"
          placement="bottom"
          width="200"
          trigger="manual"
        >
          <div
            class="select-li"
            :class="{ 'select-li-active': selectSubject === '' }"
            @click="changeSubject('')"
          >
            全部
          </div>
          <div
            v-for="item in selectSeries && selectSeries.childCategoryList"
            :key="item.id"
            class="select-li"
            :class="{ 'select-li-active': item.id === selectSubject }"
            @click="changeSubject(item)"
          >
            {{ item.name }}
          </div>
          <div slot="reference" class="select-btn" @click="visible = !visible">{{ value }} <i class="el-icon-arrow-down ml8"></i></div>
        </el-popover>
      </div>
      <div class="w">
        <el-row :gutter="20">
          <el-col v-for="item in courseList" :key="item.id" :span="6" class="course-box">
            <div class="w h pointer" @click="toDetail(item.id)">
              <div class="r-container">
                <div class="img-box">
                  <img v-if="item.coverUrl" :src="item.coverUrl" />
                  <img v-else src="../../../assets/images/default-cover.jpg" />
                </div>
              </div>
              <div class="course_title article-singer-container">
                {{ item.name }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import Institution from '@/assets/images/course/institution.png'
import { getCourseSeriesList, getCoursePackageList } from '@/api/course-api'
export default {
  data () {
    return {
      Institution,
      seriesList: [],
      courseList: [],
      selectSeries: { 'id': -1 },
      selectSubject: '',
      number: 0,
      value: '全部',
      visible: false
    }
  },
  created () {
    this._getCourseSeriesList()
    this._getCoursePackageList()
  },
  methods: {
    _getCourseSeriesList () {
      getCourseSeriesList().then(
        response => {
          this.seriesList = response.data
        }
      )
    },
    _getCoursePackageList () {
      const params = {}
      if (this.selectSubject) {
        params['themeId'] = this.selectSubject
      }
      if (this.selectSeries.id !== -1) {
        params['seriesId'] = this.selectSeries.id
      }
      getCoursePackageList(params).then(
        response => {
          this.courseList = response.data
          if (!this.selectSubject) this.number = this.courseList.length
        }
      )
    },
    changeSeries (series) {
      if (!series) {
        this.selectSeries = { 'id': -1 }
      } else {
        this.selectSeries = series
      }
      this.courseList = []
      this.selectSubject = ''
      this.value = '全部'
      this._getCoursePackageList()
    },
    changeSubject (subject) {
      if (subject) {
        this.value = subject.name
        this.selectSubject = subject.id
      } else {
        this.value = '全部'
        this.selectSubject = ''
      }
      this.visible = false
      this.courseList = []
      this._getCoursePackageList()
    },
    toDetail (categoryId) {
      this.$router.push(`/classpro/course/detail/${categoryId}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.index-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  @include scrollBar;
  background: #FFF;
  border-radius: 10px;

  .course-box {
    margin-bottom: 20px;
  }

  .card-box {
    padding: 5px 15px;
    margin-bottom: 10px;
    box-sizing: border-box;

    &:last-child {
      margin-bottom: 0;
    }

    .card-title-box {
      margin: 10px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .t-title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
      }

      .t-more {
        color: #000;
        font-size: 12px;
        display: flex;
        align-items: center;

        img {
          width: 20px;
          height: 20px;
          margin: 0 8px;
        }
      }

      .fb {
        font-weight: 500;
      }
    }

    // 16:9 图片
    .r-container {
      position: relative;
      min-height: 0;
      padding-bottom: 56.25%;
      background-color: #eee;

      .img-box {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 5px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .course_title {
      color: #000;
      font-size: var(--font-size-L);
      margin: 10px 0;
    }

    .course_des {
      color: #000;
      font-size: 12px;
      font-weight: 300;
      margin-bottom: 10px;
    }

  }

  .course-tags-box {
    width: 100%;
    padding: 0 0 15px 0;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .tags-btn {
      display: flex;
      min-width: 100px;
      padding: 5px 10px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      border-radius: 5px;
      background: #F2F2F2;
      color: #4F4F4F;
      font-size: var(--font-size-L);
      margin: 0 10px 10px 0;
      cursor: pointer;
    }

    .active-btn {
      background: #DCF2FF;
      color: #2F80ED;
    }
  }

  .type-box {
    display: flex;
    justify-content: flex-start;
    padding: 10px 0;

    .select-btn {
      min-width: 50px;
      height: 30px;
      border: 1px solid #E0E0E0;
      color: #4F4F4F;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      font-size: 14px;
      padding: 0 10px;
      cursor: pointer;
    }
    .ml8 {
      margin-left: 8px;
    }
  }
}
.select-li {
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &:hover {
    background: #DCF2FF;
  }
}
.select-li-active {
  background: #DCF2FF;
}
</style>
