<template>
  <div class="w h">
    <iframe
      v-if="show"
      ref="iframe"
      :src="url"
      width="100%"
      height="100%"
      frameborder="0"
      scrolling="auto"
    ></iframe>
  </div>
</template>

<script>
import { getAiConfig } from '@/api/dictionary-api.js'
export default {
  data () {
    return {
      url: '',
      show: false
    }
  },
  mounted () {
    this._getConfig()
  },
  methods: {
    async _getConfig () {
      this.show = false
      const prams = {
        'configType': 'AI_GUIDE_PDF'
      }
      const { data } = await getAiConfig(prams)
      if (data) {
        this.url = data[0].keyValue
        this.show = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
