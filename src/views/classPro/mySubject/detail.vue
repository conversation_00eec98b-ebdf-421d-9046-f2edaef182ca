<template>
  <div class="course-detail">
    <div class="header">
      <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="backToHome" />
      <div class="back" @click="backToHome">返回</div>
    </div>

    <div class="container">
      <div class="section-title">
        <div class="line"></div>
        <span>示范课程视频</span>
      </div>

      <div class="video-content">
        <div class="video-list" :class="{ 'collapsed': !isVideoExpanded && videoList.length > 4 }">
          <div v-for="(video, index) in videoList" :key="index" class="video-item">
            <div class="video-cover">
              <img :src="video.coverUrl ? video.coverUrl : require('@/assets/images/default-cover.jpg')" alt="视频封面" />
              <div class="play-icon" @click="playTrainingVideo(video)">▶</div>
            </div>
            <div class="video-title">{{ video.fileName }}</div>
          </div>
        </div>
        <Empty v-if="videoList.length === 0" :msg="'暂无数据'" />
        <div v-if="videoList.length > 4" class="expand-btn" @click="toggleVideoSection">
          {{ isVideoExpanded ? '收起' : '展开' }}
          <i class="el-icon-arrow-up" :class="{ 'is-expanded': !isVideoExpanded }"></i>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="section-title">
        <div class="line"></div>
        <span>教师培训</span>
      </div>

      <div class="training-content">
        <div class="training-list" :class="{ 'collapsed': !isTrainingExpanded && trainingList.length > 4 }">
          <div v-for="(training, index) in trainingList" :key="index" class="training-item">
            <div class="training-cover">
              <img :src="training.coverUrl ? training.coverUrl : require('@/assets/images/default-cover.jpg')" alt="培训封面" />
              <div class="play-icon" @click="playTrainingVideo(training)">▶</div>
            </div>
            <div class="training-title">{{ training.fileName }}</div>
          </div>
        </div>
        <Empty v-if="trainingList.length === 0" :msg="'暂无数据'" />
        <div v-if="trainingList.length > 4" class="expand-btn" @click="toggleTrainingSection">
          {{ isTrainingExpanded ? '收起' : '展开' }}
          <i class="el-icon-arrow-up" :class="{ 'is-expanded': !isTrainingExpanded }"></i>
        </div>
      </div>
    </div>

    <!-- 添加视频播放遮罩层 -->
    <div v-if="showVideoModal" class="video-modal" @click.self="closeVideo">
      <div class="video-container">
        <div class="video-header">
          <span class="video-title">{{ currentVideo.fileName }}</span>
          <i class="el-icon-close" @click="closeVideo"></i>
        </div>
        <video
          ref="videoPlayer"
          :src="currentVideo.url"
          controls
          class="video-player"
          @ended="closeVideo"
        >
          您的浏览器不支持 video 标签
        </video>
      </div>
    </div>
  </div>
</template>

<script>
import { getAiCourseResourceList } from '@/api/course-api'
import Empty from '@/components/classPro/Empty/index.vue'
export default {
  components: {
    Empty
  },
  data () {
    return {
      isVideoExpanded: false,
      isTrainingExpanded: false,
      showVideoModal: false,
      currentVideo: {
        title: '',
        videoUrl: ''
      },
      videoList: [
      ],
      trainingList: [
      ]
    }
  },
  mounted () {
    this.getAiCourseResourceList()
  },
  methods: {
    backToHome() {
      this.$router.push('/classpro/mySubject')
    },
    toggleVideoSection() {
      this.isVideoExpanded = !this.isVideoExpanded
    },
    toggleTrainingSection() {
      this.isTrainingExpanded = !this.isTrainingExpanded
    },
    playTrainingVideo(video) {
      this.currentVideo = video
      this.showVideoModal = true
      // 等待 DOM 更新后自动播放
      this.$nextTick(() => {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.play()
        }
      })
    },
    closeVideo() {
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause()
      }
      this.showVideoModal = false
      this.currentVideo = { title: '', videoUrl: '' }
    },
    async getAiCourseResourceList() {
      const { data } = await getAiCourseResourceList({
        resourceId: this.$route.params.id,
        resourceType: 'TEACHING_RESEARCH_RESOURCE'
      })
      this.videoList = data[0].fileList
      this.trainingList = data[1].fileList
    }
  }
}
</script>

<style lang='scss' scoped>
.course-detail {
  height: 100%;
  padding: 10px;
  width: 100%;
  overflow-y: auto;
  @include scrollBar;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    img {
      width: 13px;
      height: 11px;
      cursor: pointer;
    }

    .back {
      font-size: var(--font-size-L);
      color: #1C1B1A;
      margin: 0 20px 0 8px;
      cursor: pointer;
    }
  }

  .container {
    width: 100%;
    padding: 20px;
    background: #FFFFFF;
    box-shadow: 0 3px 14px rgba(233,240,255,0.5);
    border-radius: 10px;
    margin-bottom: 10px;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .line {
      width: 3px;
      height: 16px;
      background: #3479FF;
      border-radius: 3.5px;
      margin-right: 8px;
    }

    span {
      font-weight: 500;
      font-size: var(--font-size-L);
      color: #1C1B1A;
    }
  }

  .video-content,
  .training-content {
    position: relative;
  }

  .video-list,
  .training-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    transition: max-height 0.3s ease-in-out;
    &.collapsed {
      max-height: 160px;
      overflow: hidden;
    }

    &:not(.collapsed) {
      max-height: 2000px;
    }
  }

  .video-item,
  .training-item {
    cursor: pointer;
    position: relative;
    width: 100%;
    margin-bottom: 40px;

    .video-cover,
    .training-cover {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%;
      border-radius: 8px;
      overflow: hidden;

      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        background: rgba(0,0,0,0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        z-index: 1;
      }
    }

    .video-title,
    .training-title {
      position: absolute;
      bottom: -30px;
      left: 0;
      width: 100%;
      height: 30px;
      line-height: 30px;
      font-size: var(--font-size-M);
      color: #1C1B1A;
      @include ellipses(1);
    }
  }

  .expand-btn {
    position: absolute;
    bottom: -15px;
    right: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #999;
    padding: 5px 10px;
    z-index: 1;
    background: #fff;

    i {
      margin-left: 5px;
      transition: transform 0.3s;

      &.is-expanded {
        transform: rotate(180deg);
      }
    }
  }

  /* 添加视频模态框样式 */
  .video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;

    .video-container {
      width: 80%;
      max-width: 1200px;
      background: #000;
      border-radius: 8px;
      overflow: hidden;

      .video-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: rgba(0, 0, 0, 0.8);

        .video-title {
          color: #fff;
          font-size: var(--font-size-L);
        }

        .el-icon-close {
          color: #fff;
          font-size: 24px;
          cursor: pointer;
          padding: 5px;
          transition: all 0.3s;

          &:hover {
            transform: scale(1.1);
            color: #f56c6c;
          }
        }
      }

      .video-player {
        width: 100%;
        aspect-ratio: 16/9;
        background: #000;
        outline: none;
      }
    }
  }
}
</style>
