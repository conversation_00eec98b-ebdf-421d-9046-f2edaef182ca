<template>
  <div class="data-center h">
    <div class="wrap h">
      <div v-if="adminRole" class="data-center-tag">
        <div class="btn" :class="{ actvie: tagIndex === 0 }" @click="handlerTag(0)">班级数据</div>
        <div v-if="adminRole === 'SCHOOL'" class="btn" :class="{ actvie: tagIndex === 1 }" @click="handlerTag(1)">学校数据</div>
        <div
          v-else-if="adminRole === 'ORGANIZATION'"
          class="btn"
          :class="{ actvie: tagIndex === 1 }"
          @click="handlerTag(1)"
        >区县数据</div>
      </div>
      <template v-if="tagIndex === 0">
        <div class="data-center-top">

          <div class="flex items-center">
            <div class="line"></div>
            <div class="title">班级详情</div>
            <el-popover
              v-if="userRelations.length > 0"
              v-model="classVisible"
              placement="bottom"
              trigger="hover"
              :popper-class="'data-center-class-pop'"
            >
              <div class="select-class-list">
                <div
                  v-for="item in userRelations"
                  :key="item.id"
                  class="select-class article-singer-container"
                  :class="{ 'select-class-active': item.toUserDisplayName === getGradeName() }"
                  @click="changeGrade(item)"
                >
                  {{ item.toUserDisplayName }}
                </div>
              </div>
              <span slot="reference" class="class flex">
                <div class="class-name">{{ getGradeName() }}</div>
                <i class="iconfont icon-xialabeifen"></i>
              </span>
            </el-popover>
            <span v-else class="class flex" style="cursor: default;">
              <div class="class-name">{{ getGradeName() }}</div>
            </span>
          </div>
          <div class="box-container">
            <div class="box">
              <div class="number">{{ statistics.totalCourse || '-' }}</div>
              <div class="box-name">课程数量</div>
            </div>
            <div class="three-line-box">
              <div v-for="item in 3" :key="item" class="three-line"></div>
            </div>
            <div class="box">
              <div class="number">{{ statistics.totalLesson || '-' }}</div>
              <div class="box-name">总课时数</div>
            </div>
            <div class="three-line-box">
              <div v-for="item in 3" :key="item" class="three-line"></div>
            </div>
            <div class="box">
              <div class="number">{{ statistics.finishedLesson || '-' }}</div>
              <div class="box-name">已上课时数</div>
            </div>
            <div class="three-line-box">
              <div v-for="item in 3" :key="item" class="three-line"></div>
            </div>
            <div class="box">
              <div class="number">{{ attendPer }}</div>
              <div class="box-name">上课率</div>
            </div>
            <div class="three-line-box">
              <div v-for="item in 3" :key="item" class="three-line"></div>
            </div>
            <div class="box">
              <div class="number">{{ statistics.firstLessonTime || statistics.firstLessonTime === '-' ?
                formatYYYYMMDD(statistics.firstLessonTime) : '-' }}</div>
              <div class="box-name">首次上课时间</div>
            </div>
          </div>
        </div>
        <div class="bottom" :class="adminRole ? 'bottom-a' : 'bottom-noa'">
          <div class="flex">
            <div class="bottom-title" :class="{ 'bottom-title-active': showState === 0 }" @click="changeShowState(0)">上课明细
            </div>
            <div class="bottom-title" :class="{ 'bottom-title-active': showState === 1 }" @click="changeShowState(1)">课程明细
            </div>
          </div>
          <div class="container">
            <attend v-show="showState === 0" ref="attend" />
            <course v-show="showState === 1" ref="course" />
          </div>
        </div>
      </template>
      <template v-else-if="tagIndex === 1">
        <div class="iframe-box">
          <iframe
            v-if="iframeUrl"
            ref="iframe"
            :src="iframeUrl"
            width="100%"
            height="100%"
            frameborder="0"
            scrolling="auto"
          ></iframe>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import Attend from './components/attend.vue'
import Course from './components/course.vue'
import { switchAccount } from '@/api/user-api'
import { userCourseStatistics } from '@/api/course-api'
import { mapGetters } from 'vuex'
import { getChildName, setChildName, setChildId, setChildToken, removeChildName, removeChildId, getToken, getChildToken, setAdminToken } from '@/utils/auth'
import { formatYYYYMMDD } from '@/utils/date'
import { dataCenterLogin } from '@/api/datacenter-api.js'

export default {
  components: {
    Attend,
    Course
  },
  data () {
    return {
      showState: 0,
      classVisible: false,
      statistics: {},
      formatYYYYMMDD,
      adminRole: '',
      tagIndex: 0,
      iframeUrl: ''
    }
  },
  computed: {
    attendPer () {
      if (this.statistics.totalLesson && this.statistics.finishedLesson && +this.statistics.totalLesson !== 0) {
        return (+this.statistics.finishedLesson / +this.statistics.totalLesson * 100).toFixed(0) + '%'
      }
      return '-'
    },
    ...mapGetters([
      'userRelations'
    ])
  },
  async created () {
    await this.checkGrade()
    this._userCourseStatistics()
  },
  mounted () {
    this.$refs.attend._getLessonListAllType()
    this.getAdminInfo()
  },
  methods: {
    getGradeName () {
      if (getChildName()) return getChildName()
      // for (var grade of this.$store.getters.userRelations) {
      //   if (+grade.toUserId === +this.$store.getters.id) return grade.toUserDisplayName
      // }
      return this.$store.getters.name
    },
    changeShowState (showState) {
      if (this.showState === showState) return
      this.showState = showState
      if (showState === 0) {
        this.$refs.attend.getData()
      } else {
        this.$refs.course._getStuCourseList()
        this.$refs.course._getUserPlanLessonList()
      }
    },
    changeGrade (grade) {
      if (grade.toUserId === this.$store.getters.id && grade.toUserDisplayName === this.$store.getters.name) {
        removeChildName()
        removeChildId()
        setChildToken(getToken())
        this.classVisible = false
        this._userCourseStatistics()
        this.$refs.attend.getData()
        this.$refs.course._getStuCourseList()
        this.$refs.course._getUserPlanLessonList()
        return
      }
      var param = {
        'touserId': grade.toUserId,
        'parentUserId': this.$store.getters.id
      }
      switchAccount(param)
        .then(response => {
          setChildName(grade.toUserDisplayName)
          setChildId(grade.toUserId)
          setChildToken('Bearer ' + response.data.access_token)
          this.classVisible = false
          this._userCourseStatistics()
          this.$refs.attend._getLessonListAllType()
          this.$refs.course._getStuCourseList()
          this.$refs.course._getUserPlanLessonList()
        })
    },
    // 课程统计
    _userCourseStatistics () {
      this.statistics = {}
      const param = {
        'assistantUserId': this.$store.getters.id
      }
      userCourseStatistics(param).then(
        response => {
          this.statistics = response.data
        }
      )
    },
    async checkGrade () {
      if (getChildToken()) return
      const userRelationsList = this.$store.getters.userRelations
      if (userRelationsList.length > 0) {
        const grade = userRelationsList[0]
        if (+grade.toUserId !== this.$store.getters.id) {
          const param = {
            'touserId': grade.toUserId,
            'parentUserId': this.$store.getters.id
          }
          await switchAccount(param)
            .then(response => {
              setChildName(grade.toUserDisplayName)
              setChildId(grade.toUserId)
              setChildToken('Bearer ' + response.data.access_token)
            })
        }
      }
    },
    async getAdminInfo () {
      const formData = new FormData()
      formData.append('from', 'app')
      formData.append('auth', getToken())
      const { data } = await dataCenterLogin(formData)
      if (data) {
        setAdminToken(data.token)
        this.adminRole = data.role
        this.iframeUrl = `${window.location.origin}/#/datacenter/login?token=${data.token}`
      } else {
        this.adminRole = ''
        this.iframeUrl = ''
      }
    },
    handlerTag (index) {
      this.tagIndex = index
    }
  }
}
</script>

<style lang='scss' scoped>
.data-center {
  height: 100%;
  // padding: 10px 40px;
  overflow: hidden;
  /*增加滚动条*/
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px;
    height: 1px;
  }

  .wrap {
    width: 100%;
    height: 100%;
  }

  .data-center-tag {
    width: 100%;
    height: 30px;
    border-radius: 5px;
    margin-bottom: 10px;
    // padding: 10px 20px;
    display: flex;

    .btn {
      width: 70px;
      height: 20px;
      border-radius: 25px;
      background: #D9E9FF;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: var(--font-size-L);
      //font-size: medium;
      color: #2F80ED;
      margin-right: 10px;
      cursor: pointer;
    }

    .actvie {
      background: #2F80ED !important;
      color: #FFF !important;
    }
  }

  .iframe-box {
    width: 100%;
    height: 100%;
    //height: calc(100% - 80px);
  }

  .data-center-top {
    width: 100%;
    height: 90px;
    background: #FFFFFF;
    border-radius: 5px;

    //margin-bottom: 10px;
    // padding: 10px 20px;
    .line {
      width: 2px;
      height: 14px;
      background: #3479FF;
      border-radius: 1px;
    }

    .title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: var(--font-size-L);
      color: #1C1B1A;
      letter-spacing: 0.26px;
      margin: 0 20px 0 5px;
    }

    .select-class {
      font-weight: 400;
      font-size: var(--font-size-L);
      color: red;
      line-height: 22px;
    }

    .class {
      display: flex;
      align-items: center;
      background: #F8FAFF;
      border-radius: 12px;
      padding: 4px 13px;
      cursor: pointer;
    }

    .class-name {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-L);
      color: #3479FF;
    }

    .icon-xialabeifen {
      transform: rotate(90deg);
      color: #3479FF;
      font-size: 8px;
      margin-left: 5px;
    }

    .box-container {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20px;
      padding: 0 20px;
    }

    .box {
      width: 205px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .number {
        font-family: PingFangSC-Semibold;
        font-weight: 500;
        font-size: var(--font-size-L);
        color: #3479FF;
        letter-spacing: 0.32px;
        margin-bottom: 10px;
      }

      .box-name {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-L);
        color: #1C1B1A;
        letter-spacing: 0.26px;
      }
    }

    .three-line-box {
      height: 27px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .three-line {
      width: 1px;
      height: 7px;
      background: #D9E6FF;
    }
  }

  //.bottom-a {
  //  height: calc(100% - 192px - 60px);
  //}
  //.bottom-noa {
  //  //height: calc(100% - 192px);
  //}

  .bottom {
    width: 100%;
    height: calc(100% - 200px);
    //min-height: 50vw;
    //height: 100vh;
    margin-top: 10px;

    .bottom-title {
      width: 110px;
      height: 32px;
      background: #FFFFFF;
      border-radius: 8px 25px 0 0;
      font-weight: 400;
      font-size: var(--font-size-L);
      color: #717171;
      letter-spacing: 0.22px;
      line-height: 32px;
      text-align: center;
      cursor: pointer;
    }

    .bottom-title-active {
      color: #3479FF;
      font-weight: 500;
    }

    .container {
      width: 100%;
      //height: calc(100% - 1px);
      height: 100vh;
      background: white;
      border-radius: 0 5px 5px 5px;

      .attend {
        min-height: 40px;
        margin-left: 20px;
      }
    }
  }
}
</style>

<style lang="scss">
.data-center-class-pop {
  padding: 8px;
  height: 250px;
  overflow: scroll;

  //@include scrollBar
  .select-class-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .select-class {
    font-weight: 400;
    font-size: var(--font-size-L);
    color: rgba(0, 0, 0, 0.90);
    line-height: 40px;
    height: 40px;
    min-width: 204px;
    padding: 0 8px;
    max-width: 400px;
    cursor: pointer;
    overflow-x: hidden;

    &:hover {
      background: #ECF2FE;
      border-radius: 3px;
      color: #0052D9;
    }
  }

  .select-class-active {
    background: #ECF2FE;
    border-radius: 3px;
    color: #0052D9;
  }
}
</style>
