<template>
  <div class="attend">
    <div class="wrap">
      <div class="flex items-center" style="margin-bottom: 15px">
        <div class="select-text">时间段选择</div>
        <el-date-picker
          v-model="dateValue"
          :editable="false"
          type="monthrange"
          start-placeholder="请选择月份范围"
          end-placeholder=""
          format="yyyy年/MM月"
          :range-separator="dateValue ? '-' : ''"
          @change="onChange"
        />
        <div
          class="current-month"
          :class="{ 'current-month-active' : currentMonthLight }"
          @click="selectCurrentMonth"
        >本月<i class="iconfont icon-rili"></i>
        </div>
      </div>
      <el-table
        :data="tableData"
        height="calc(100% - 67px)"
        style="width: 100%;"
        :header-row-class-name="tableHeader"
        :row-class-name="rowClass"
        :cell-class-name="cellClass"
      >
        <el-table-column
          prop="startTime"
          label="上课时间"
          align="center"
          :formatter="startTime"
        />
        <el-table-column
          prop="lessonPlan.name"
          label="课时名称"
          align="center"
          :formatter="coursePackageName"
        />
        <el-table-column
          prop="course.name"
          label="所属课程包"
          align="center"
        />
        <el-table-column
          prop="lessonInfo2.teacherName"
          label="主讲教师"
          align="center"
        />
        <el-table-column
          prop="lessonStatus"
          label="课程状态"
          align="center"
          :formatter="lessonStatus"
        />
        <div slot="empty" class="empty">
          <img src="@/assets/images/empty.png" alt="ai课堂为空" />
          <div class="hint">暂时没有数据哦～</div>
        </div>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getLessonListAllType } from '@/api/lesson-api.js'
import { formatYYYYMMDD2, formatYYYYMM } from '@/utils/date'
export default {
  data () {
    return {
      dateValue: null,
      currentMonthLight: false,
      pageNo: 1,
      pageSize: 20,
      formatYYYYMMDD2,
      formatYYYYMM,
      tableData: [],
      isScroll: true,
      isLoading: false
    }
  },
  mounted () {
    document.addEventListener('scroll', this.loadData, true)
  },
  destroyed () {
    document.removeEventListener('scroll', this.loadData, true)
  },
  methods: {
    onChange (values) {
      console.log(values)
      document.querySelector('.el-table__body-wrapper').scrollTo(0, 0)
      this.dateValue = values
      this.pageNo = 1
      this._getLessonListAllType()
    },
    selectCurrentMonth () {
      document.querySelector('.el-table__body-wrapper').scrollTo(0, 0)
      this.currentMonthLight = !this.currentMonthLight
      this.pageNo = 1
      if (this.currentMonthLight) {
        this.dateValue = [new Date(), new Date()]
      } else {
        this.dateValue = null
      }
      this._getLessonListAllType()
    },
    tableHeader (row, rowIndex) {
      return 'table-header'
    },
    rowClass (row, rowIndex) {
      return 'row-class'
    },
    getData () {
      this.pageNo = 1
      this._getLessonListAllType()
    },
    loadData () {
      const el = document.querySelector('.el-table__body-wrapper')
      const offsetHeight = el.offsetHeight
      el.onscroll = () => {
        const scrollTop = el.scrollTop
        const scrollHeight = el.scrollHeight
        if (offsetHeight + scrollTop - scrollHeight >= -1) {
          console.log('滚动到了底部')
          if (!this.isScroll || this.isLoading) {
            return
          }
          this.pageNo++
          this._getLessonListAllType()
        }
      }
    },
    // 获取各种类型的课程列表
    _getLessonListAllType () {
      this.isLoading = true
      if (this.pageNo === 1) this.tableData = []
      const params = {
        'lessonListType': 'ALL',
        'userType': 'STUDENT',
        'pageNo': this.pageNo,
        'pageSize': this.pageSize,
        'param': this.$store.getters.id,
        'startDate': this.dateValue ? formatYYYYMM(this.dateValue[0]) : null,
        'endDate': this.dateValue ? formatYYYYMM(this.dateValue[1]) : null
      }
      getLessonListAllType(params).then(
        response => {
          if (this.pageNo > 1) {
            this.tableData = this.tableData.concat(response.data)
          } else {
            this.tableData = response.data
          }
          if (response.data.length < this.pageSize) {
            this.isScroll = false
          } else {
            this.isScroll = true
          }
          this.isLoading = false
        }
      )
    },
    startTime (row, column, cellValue, index) {
      return formatYYYYMMDD2(cellValue)
    },
    coursePackageName (row, column, cellValue, index) {
      return cellValue || '-'
    },
    lessonStatus (row, column, cellValue, index) {
      switch (cellValue) {
        case 'ComingSoon':
          return '未开始'
        case 'Opening':
          return '进行中'
        case 'UnderReview':
        case 'Completed':
        case 'Unqualified':
          return '已完成'
        case 'TeachersAbsenteeism':
        case 'StudentsAbsenteeism':
          return '缺课'
        default:
          return ''
      }
    },
    cellClass (cell) {
      if (cell.columnIndex === 4) {
        switch (cell.row.lessonStatus) {
          case 'ComingSoon':
            return 'red'
          case 'Opening':
            return 'blue'
          case 'UnderReview':
          case 'Completed':
          case 'Unqualified':
            return 'green'
          case 'TeachersAbsenteeism':
          case 'StudentsAbsenteeism':
            return 'orange'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.attend {
    width: 100%;
    height: 100%;
    .wrap {
        width: 100%;
        height: 100%;
        padding: 20px;

        .select-text {
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #0D0E0E;
            letter-spacing: 0.22px;
        }

        .icon-rili {
            color: #3479FF;
            position: absolute;
            left: -33px;
        }

        .current-month {
            width: 52px;
            height: 32px;
            background: #f1f1f1;
            border-radius: 3px;
            font-weight: 400;
            font-size: 12px;
            color: #717171;
            letter-spacing: 0.19px;
            line-height: 32px;
            text-align: center;
            position: relative;
            cursor: pointer;
        }

        .current-month-active {
            background: #F8FAFF;
            border: 1px solid #3479FF;
            color: #3479FF;
        }
    }
    .empty {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
            width: 100px;
            height: 100px;
        }

        .hint {
            display: flex;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #8C8C8C;
            letter-spacing: 0.22px;

            .hint-blue {
                color: rgba(31, 102, 255, 1);
                cursor: pointer;
            }
        }

        .hint-padding {
            padding:15px 0 4px
        }
    }
}
</style>

<style lang="scss">
.attend {
    .wrap {
        .el-range-editor {
            margin: 0 10px
        }

        .el-input__inner {
            border: 1px solid #3479FF;
            height: 32px;
        }

        .el-range-separator {
            line-height: 22px;
        }

        .el-range__icon {
            display: none;
        }

        .el-range-input {
            font-weight: 400;
            font-size: 12px;
            color: rgba(125,125,125,0.90);
        }

        .el-range__close-icon {
            line-height: 22px;
            color: red;
        }

        .table-header {
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #3479FF;
            letter-spacing: 0.26px;

            th {
                background: #F8FAFF;
            }

            .el-table__cell {
                border-bottom: none;
            }
        }

        .row-class {
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #0E0E0E;
            letter-spacing: 0.22px;
        }

        .red {
          color: #FF5903 ;
        }

        .blue {
          color: #2700F0 ;
        }

        .green {
          color: #2DA800 ;
        }

        .orange {
          color: #F08804 ;
        }
    }
}
</style>
