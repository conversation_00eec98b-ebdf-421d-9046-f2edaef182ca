<template>
  <div class="my-course">
    <div class="tag-box flex">
      <div :class="tagIndex === 1 ? 'tag-item__active' : 'tag-item'" @click="changeTag(1)">
        课程
      </div>
      <div :class="tagIndex === 2 ? 'tag-item__active' : 'tag-item'" @click="changeTag(2)">
        教务管理
      </div>
      <div class="help" @click="handleHelp">
        <img class="nav-icon" src="../../../assets/images/dashboard/new/help.svg" />
        <div class="nav-text">帮助</div>
      </div>
    </div>
    <div class="my-course-body">
      <Course v-if="tagIndex === 1" ref="course" />
      <grade v-if="tagIndex === 2" />
    </div>
  </div>
</template>

<script>
import Course from './components/course.vue'
import grade from '@/views/classPro/educational/components/grade.vue'
export default {
  components: {
    Course,
    grade
  },
  data () {
    return {
      tagIndex: 1
    }
  },
  mounted () {
    const fromClassInfo = window.sessionStorage.getItem('classinfoback')
    if (fromClassInfo) {
      this.tagIndex = 2
    }
    setTimeout(() => {
      window.sessionStorage.removeItem('classinfoback')
    }, 2000)
  },
  methods: {
    changeTag (index) {
      this.tagIndex = index
    },
    handleHelp () {
      const url = `${window.location.origin}/#/help`
      window.open(url, '_blank', 'width=600,height=800,left=200,top=100,menubar=0,scrollbars=1,resizable=1,status=1,titlebar=0,toolbar=0,location=1')
    }
  }
}
</script>

<style lang="scss" scoped>
.my-course {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  border-radius: 0 0 10px 10px;
  .tag-box {
    width: 100%;
    height: 32px;
    position: relative;
    .help {
      width: 70px;
      height: 100%;
      cursor: pointer;
      position: absolute;
      right: 0px;
      padding-top: 10px;
      .nav-icon {
        width: 15px;
        height: 15px;
        position: absolute;
      }

      .nav-text {
        color: #000;
        //font-size: 12px;
        font-size: var(--font-size-L);
        margin-left: 22px;
        //position: relative;
      }
    }
    .tag-item,
    .tag-item__active {
      border-radius: 8px 25px 0 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: var(--font-size-L);
      font-weight: 400;
      height: 100%;
      width: 110px;
      cursor: pointer;
    }

    .tag-item {
      background: #f4f4f4;
      color: #717171;
    }

    .tag-item__active {
      background: #ffffff;
      font-weight: 500;
      color: #3479ff;
    }
  }

  .my-course-body {
    height: calc(100% - 32px);
    background: #fff;
    border-radius: 0 10px 10px 10px;
    //padding: 10px;
    box-sizing: border-box;
    @include scrollBar;
  }
}
</style>../../digitalbooks/myDigitalbooks/components/coursePop.vue
