<template>
  <div class="course-detail">
    <div class="header">
      <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="backToHome" />
      <div class="back" @click="backToHome">返回全部</div>
    </div>
    <template v-if="courseInfo">
      <div class="container operation">
        <img :src=" courseInfo.cover || DefaultCover" alt="" />
        <div class="right">
          <div class="flex">
            <div class="name">{{ courseInfo.name || '' }}</div>
            <i v-if="!isLike" class="iconfont icon-xinxiankuang" @click="_correlateCoursePackage"></i>
            <i v-else class="iconfont icon-xinshixin" @click="_correlateCoursePackage"></i>
          </div>
          <div class="line"></div>
          <div class="content">{{ courseInfo.subTitle || '' }}</div>
          <div class="btn-group flex">
            <div v-if="false" class="btn">前往空中课堂</div>
            <div v-if="false" class="btn">前往AI课堂</div>
          </div>
        </div>
      </div>
      <div v-if="courseInfo.readerList.length > 0" class="container">
        <div class="title">
          <div class="line"></div>
          <span>读本详情</span>
        </div>
        <swiper
          ref="aiSwiper"
          class="swiper"
          :options="swiperOption"
        >
          <swiper-slide v-for="item in courseInfo.readerList" :key="item.id">
            <div class="pointer" @click="showReadInfo(item)">
              <img :src="item.cover || DefaultCover" alt="" />
              <div class="ar-2">
                {{ item.name }}
              </div>
            </div>
          </swiper-slide>
          <div v-show="courseInfo.readerList.length > 5" slot="pagination" class="swiper-pagination"></div>
        </swiper>
      </div>
      <!-- 专家团队 -->
      <div v-if="courseInfo.expertList.length > 0" class="container">
        <div class="title">
          <div class="line"></div>
          <span>专家团队</span>
          <div class="tip" @click="toTeam">更多介绍</div>
          <i class="iconfont icon-a-bianzu6" @click="toTeam"></i>
        </div>
        <div class="team">
          <div v-for="item in courseInfo.expertList" :key="'expert' + item.id" class="member">
            <img :src="item.headUrl || DefaultAvatar" alt="" />
            <span>{{ item.name || '' }}</span>
          </div>
        </div>
      </div>
      <!-- 课程列表 -->
      <div class="container">
        <div class="title">
          <div class="line"></div>
          <span>课程列表</span>
        </div>
        <!-- 暂时不显示空中课堂了 所以先注释掉 -->
        <!-- <div class="type-list">
          <div class="type" :class="{'type-active': listType === 0}" @click="changeCourseList(0)">双师AI课堂</div>
          <div class="type" :class="{'type-active': listType === 1}" @click="changeCourseList(1)">空中课堂</div>
        </div> -->
        <!-- <el-timeline v-if="courseList.length > 0" :reverse="reverse" style="padding-inline-start:0px">
          <el-timeline-item
            v-for="(item, index) in courseList"
            :key="index"
            :timestamp="listType === 0 ? item.subTitle : item.subtitle"
          >
            <span class="timeline">{{ listType === 0 ? item.title : item.name }}</span>
          </el-timeline-item>
        </el-timeline> -->
        <div v-if="(liveCourseList.length > 0 && listType === 1) || (aiCourseList.length > 0 && listType === 0)" class="course-list">
          <template v-if="listType === 1">
            <div v-for="item in liveCourseList" :key="item.id" class="course-item" @click="toPackageDetail(item, 0)">
              <div class="cover">
                <img class="cover-img" :src="item.coverUrl || DefaultCover" alt="" />
                <span class="period">{{ item.number || '' }}课时</span>
              </div>
              <div class="course-info">
                <div class="course-name mb-5">{{ item.name || '' }}</div>
                <div class="flex items-center mb-5">
                  <!-- <img v-if="item.schoolCourse" class="clock" :src="iconClock" alt="时间" /> -->
                  <span v-if="item.schoolCourse" class="time">有效期：<font :class="{'red': !item.schoolCourse}">{{ item.schoolCourse
                    ? `${formatDot(item.schoolCourse.effectStart)-formatDot(item.schoolCourse.effectEnd)}`
                    : '未开通' }}</font></span>
                </div>
                <div class="course-intro">{{ item.subtitle || '' }}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div v-for="item in aiCourseList" :key="item.id" class="course-item" @click="toPackageDetail(item, 1)">
              <div class="cover">
                <img class="cover-img" :src="item.coverUrl || DefaultCover" alt="" />
                <span class="period">{{ item.len || '' }}课时</span>
              </div>
              <div class="course-info">
                <div class="course-name mb-5">{{ item.title || '' }}</div>
                <div class="flex items-center mb-5">
                  <!-- <img class="clock" :src="iconClock" alt="时间" /> -->
                  <span class="time">有效期：<font :class="{'red': !item.schoolCourse}">{{ item.schoolCourse
                    ? `${formatDot(item.schoolCourse.effectStart)}-${formatDot(item.schoolCourse.effectEnd)}`
                    : '未开通' }}</font></span>
                  <!-- <span class="period">{{ item.len || '' }}课时</span> -->
                </div>
                <div class="course-intro">{{ item.subTitle || '' }}</div>
              </div>
            </div>
          </template>
        </div>
        <div v-else class="empty">
          <img src="@/assets/images/empty.png" alt="empty" />
          <div class="hint hint-padding">我们正在努力生产课程，敬请期待～</div>
        </div>
      </div>
    </template>
    <readInfo v-if="showRead" :read-id="showReadId" @close="showRead = false" />
  </div>
</template>

<script>
import DefaultCover from '@/assets/images/default-cover.jpg'
import DefaultAvatar from '@/assets/images/profile.png'
import iconClock from '@/assets/images/course/icon-clock.svg'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import { getCoursePackageInfo, correlateCoursePackage } from '@/api/course-api'
import { formatDot } from '@/utils/date.js'
import readInfo from '../readInfo/index.vue'
export default {
  components: {
    readInfo,
    Swiper,
    SwiperSlide
  },
  data () {
    return {
      showRead: false,
      showReadId: '',
      DefaultCover,
      DefaultAvatar,
      iconClock,
      formatDot,
      swiperOption: {
        slidesPerView: 5,
        spaceBetween: 30,
        slidesPerGroup: 5,
        loop: false,
        loopFillGroupWithBlank: true,
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        }
      },
      categoryId: this.$route.params.categoryId,
      courseInfo: null,
      isLike: false,
      listType: 0,
      courseList: [],
      aiCourseList: [],
      liveCourseList: [],
      likeLoading: false
    }
  },
  mounted () {
    this._getCoursePackageInfo()
  },
  methods: {
    backToHome () {
      this.$router.push('/classpro/aiCourse')
    },
    _getCoursePackageInfo () {
      const params = {
        'categoryId': this.categoryId
      }
      getCoursePackageInfo(params).then(
        response => {
          this.courseInfo = response.data
          this.isLike = this.courseInfo.like === 'ACTIVE'
          // this.courseList = this.courseInfo.aiCourseList
          this.aiCourseList = this.courseInfo.aiCourseList
          this.liveCourseList = this.courseInfo.liveCourseList
        }
      )
    },
    // 喜欢/不喜欢课程
    async _correlateCoursePackage () {
      if (this.likeLoading) return
      this.likeLoading = true
      const params = {
        'categoryId': this.categoryId
      }
      await correlateCoursePackage(params).then(
        response => {
          this.isLike = !this.isLike
        }
      )
      this.likeLoading = false
    },
    changeCourseList (type) {
      this.listType = type
      this.courseList = this.listType === 0 ? this.courseInfo.aiCourseList : this.courseInfo.liveCourseList
    },
    toTeam () {
      this.$router.push(`/course/team/${this.categoryId}`)
    },
    //  跳转课程包详情
    toPackageDetail (item, type) {
      if (type === 0) {
        this.$router.push(`/classpro/course/package/${this.categoryId}/${item.id}/${type}`)
      } else {
        if (item.studentsCourse) {
          this.$router.push(`/classpro/course/package/${this.categoryId}/${item.id}/${type}`)
        } else {
          // this.$message.error('您所在的学校还未开通该课程哦！')
          // 改为跳转试用
          this.$router.push(`/classpro/course/package/${this.categoryId}/${item.id}/${type}?h=0`)
        }
      }
    },
    showReadInfo (info) {
      this.showReadId = info.aiCourseId
      this.showRead = true
    }
  }
}
</script>

<style lang='scss' scoped>
.course-detail {
    // height: calc(100% - 62px);
    height: calc(100% - 1px);
    padding: 10px;
    width: 100%;
    overflow: scroll;
    overflow-x: hidden;
    @include scrollBar;

    .container {
        width: 100%;
        padding: 20px 30px;
        background: #FFFFFF;
        box-shadow: 0 3px 14px 0 rgba(233,240,255,0.50);
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .line {
            width: 3px;
            height: 16px;
            background: #3479FF;
            border-radius: 3.5px;
            margin-right: 8px;
        }

        span {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0.26px;
        }

        .tip {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0.19px;
            margin-left: auto;
            margin-right: 5px;
            line-height: 17px;
            cursor: pointer;
        }

        i {
            transform: rotate(180deg);
            font-size: 12px;
            margin-bottom: 2px;
            cursor: pointer;
        }
    }

    .header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        img {
            width: 13px;
            height: 11px;
            cursor: pointer;
            object-fit: contain;
        }

        .back {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0.22px;
            margin: 0 20px 0 8px;
            cursor: pointer;
        }

        span {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 10px;
            color: #999999;
            letter-spacing: 0.16px;
        }
    }

    .operation {
        display: flex;

        img {
            height: 154px;
            width: 258px;
            object-fit: cover;
            border-radius: 10px;
        }

        .right {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 13px 20px 0 20px;
        }

        .name {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0.26px;
            margin-right: 15px;
            line-height: 22px;
        }

        .iconfont {
          cursor: pointer;
        }

        .icon-xinxiankuang,
        .icon-xinshixin {
          line-height: 22px;
        }

        .icon-xinshixin {
          color: red;
        }

        .line {
            width: 20px;
            height: 3px;
            background: #3479FF;
            border-radius: 1.5px;
            margin: 10px 0 15px;
        }

        .btn-group {
            margin-top: auto;
        }
        .btn {
            padding: 7.5px 7px;
            background: #3479FF;
            border-radius: 5px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            letter-spacing: 0.22px;
            margin-right: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
    }

    .content {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-L);
        color: #1C1B1A;
        letter-spacing: 0.22px;
        line-height: 20px;
        white-space: pre-wrap;
    }

    .swiper {
        height: 186px;
        img {
            width: 120px;
            height: 146px;
            object-fit: cover;
        }
    }

    .team {
        display: flex;
        flex-wrap: wrap;
        gap: 50px;

        .member {
            display: flex;
            align-items: center;
            img {
                width: 60px;
                height: 60px;
                border-radius: 100px;
                margin-right: 10px;
                object-fit: cover;
            }

            span {
                font-family: PingFangSC-Medium;
                font-weight: 500;
                font-size: 14px;
                color: #1C1B1A;
                letter-spacing: 0.22px;
            }
        }
    }

    .type-list {
        display: flex;
        margin-bottom: 20px;

        .type {
            width: 90px;
            height: 25px;
            border: 1px solid #999999;
            border-radius: 15px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            letter-spacing: 0.22px;
            text-align: center;
            line-height: 25px;
            margin-right: 10px;
            cursor: pointer;
        }

        .type-active {
            background: #DFEBFF;
            color: #3479FF;
            border: none;
        }
    }

    .timeline {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 14px;
        color: #1C1B1A;
        letter-spacing: 0.22px;
        white-space: pre-wrap;
    }

    .course-list {
      display: grid;
      column-gap: 20px;
      row-gap: 17px;
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .course-item {
      width: 100%;
      height: 220px;
      background: #FFFFFF;
      box-shadow: 0 3px 12px 0 rgba(233,240,255,0.66);
      border-radius: 5px;
      font-weight: 400;
      cursor: pointer;

      .cover {
        width: 100%;
        height: 110px;
        position: relative;
      }

      .cover-img {
        width: 100%;
        height: 100%;
        //height: 111px;
        object-fit: cover;
        border-radius: 5px 5px 0 0;
      }

      .period {
        font-size: var(--font-size-L);
        color: #FFFFFF;
        line-height: 25px;
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 25px;
        background: rgba(0,0,0,0.41);
        padding: 0 10px;
      }

      .course-info {
        padding: 6px 10px 10px;
        //padding: 5px;
      }

      .course-name {
        font-weight: 500;
        font-size: var(--font-size-L);
        color: #0B0B0B;
        @include ellipses(1);
      }

      .clock {
        width: 16px;
        height: 16px;
        object-fit: contain;
        margin-right: 3px;
      }

      .time {
        font-size: var(--font-size-M);
        color: #0B0B0B;
        line-height: 17px;
        margin-right: 3px;
        margin-right: 15px;
      }

      .red {
        color: red
      }

      .course-intro {
        font-size: var(--font-size-M);
        color: #1C1919;
        line-height: 17px;
        white-space: pre-wrap;
        @include ellipses(2)
      }
    }

    .empty {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
            width: 126px;
            height: 128px;
        }

        .hint {
            display: flex;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #8C8C8C;
            letter-spacing: 0.22px;

            .hint-blue {
                color: rgba(31, 102, 255, 1);
                cursor: pointer;
            }
        }

        .hint-padding {
            padding:15px 0 4px
        }
    }

    .mb-5 {
      margin-bottom: 5px;
    }

    .ar-2 {
      width: 120px;
      display: -webkit-box;
      word-break: break-all;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; //需要显示的行数
      overflow: hidden;
      text-overflow: ellipsis;
    }
}
</style>
