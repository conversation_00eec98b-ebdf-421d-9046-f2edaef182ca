<template>
  <div class="team">
    <div class="header">
      <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="backToHome" />
      <div class="back" @click="backToHome">返回全部</div>
      <span>专家团队</span>
    </div>
    <div v-if="courseInfo" class="content">
      <div v-for="item in courseInfo.expertList" :key="item" class="member">
        <img :src="item.headUrl || DefaultAvatar" alt="" />
        <div class="info">
          <div class="name">{{ item.name || '' }}</div>
          <div class="line"></div>
          <div class="subtitle">{{ item.introduction || '' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DefaultAvatar from '@/assets/images/profile.png'
import { getCoursePackageInfo } from '@/api/course-api'
export default {
  data () {
    return {
      categoryId: this.$route.params.categoryId,
      DefaultAvatar,
      courseInfo: null
    }
  },
  created () {
    this._getCoursePackageInfo()
  },
  methods: {
    backToHome () {
      this.$router.push({ 'path': `/classpro/course/detail/${this.categoryId}` })
    },
    _getCoursePackageInfo () {
      const params = {
        'categoryId': this.categoryId
      }
      getCoursePackageInfo(params).then(
        response => {
          this.courseInfo = response.data
        }
      )
    }
  }
}
</script>

<style lang='scss' scoped>
.team {
    width: 100%;
    height: calc(100% - 62px);
    padding: 0px 40px;

    .header {
        display: flex;
        align-items: center;
        height: 40px;
        img {
            width: 13px;
            height: 11px;
            cursor: pointer;
            object-fit: contain;
        }

        .back {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #1C1B1A;
            letter-spacing: 0.22px;
            margin: 0 20px 0 8px;
            cursor: pointer;
        }

        span {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 10px;
            color: #999999;
            letter-spacing: 0.16px;
        }
    }

    .content {
        height: calc(100% - 80px);
        width: 100%;
        background: #FFFFFF;
        box-shadow: 0 3px 14px 0 rgba(233,240,255,0.50);
        border-radius: 10px;
        padding: 30px 30px 0 30px;
        overflow: scroll;
        overflow-x: hidden;
        @include scrollBar;

        .member {
            display: flex;
            margin-bottom: 40px;

            img {
                width: 60px;
                height: 60px;
                border-radius: 100px;
                object-fit: cover;
            }

            .info {
                width: calc(100% - 60px);
                padding-left: 16px;
            }

            .name {
                font-weight: 500;
                font-size: 14px;
                color: #1C1B1A;
                letter-spacing: 0.22px;
            }

            .line {
                width: 20px;
                height: 3px;
                background: #3479FF;
                border-radius: 1.5px;
                margin: 5px 0 10px;
            }

            .subtitle {
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: 14px;
                color: #1C1B1A;
                letter-spacing: 0.22px;
                line-height: 20px;
                white-space: pre-wrap;
            }
        }
    }
}
</style>
