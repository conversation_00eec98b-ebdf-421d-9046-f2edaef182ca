<template>
  <div class="readerinfo">
    <div class="info-box">
      <div class="info-head">
        <div class="t-left">图书信息</div>
        <div class="t-right" @click="close"><i class="el-icon-close"></i></div>
      </div>

      <div v-if="info" class="info-content">
        <img class="i-img" :src="info.cover || DefaultCover" />
        <div class="i-c">
          <div class="i-c-title w">
            <div class="article-singer-container">
              {{ info.name }}
            </div>
          </div>
          <div class="flex mb10">
            <div class="d-title">出版：</div>
            <div class="d-content">{{ info.publisher && info.publisher.name || '-' }}</div>
          </div>
          <div class="flex mb10">
            <div class="d-title">发行：</div>
            <div class="d-content">{{ info.issuer && info.issuer.name || '-' }}</div>
          </div>
          <div class="flex mb10">
            <div class="d-title">ISBN：</div>
            <div class="d-content">{{ info.isbn || '-' }}</div>
          </div>
          <div class="flex mb10">
            <div class="d-title">CIP核字号：</div>
            <div class="d-content">{{ info.cip || '-' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DefaultCover from '@/assets/images/default-cover.jpg'
import { getAiCourseBook } from '@/api/course-api'
export default {
  props: {
    readId: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      DefaultCover,
      info: null
    }
  },
  watch: {
    readId: {
      handler (val) {
        if (val) {
          this._getList()
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    close () {
      this.$emit('close')
    },
    async _getList () {
      const { data } = await getAiCourseBook({ aiCourseId: this.readId })
      console.log(data)
      this.info = data
    }
  }
}
</script>

<style lang="scss" scoped>
.readerinfo {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: rgba(0, 0, 0, .3);
  display: flex;
  justify-content: center;
  align-items: center;

  .info-box {
    width: 460px;
    height: 280px;
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0px 4 4 0px rgba(0, 0, 0, 0.25);
    padding: 18px;
    box-sizing: border-box;
    .info-head {
      height: 30px;
      display: flex;
      justify-content: space-between;

      .t-left {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
      }

      .t-right {
        .el-icon-close {
          cursor: pointer;
          font-size: 18px;
          color: #575B66;
        }
      }
    }

    .info-content {
      display: flex;
      .i-img {
        width: 150px;
        height: 200px;
        object-fit: cover;
        flex-shrink: 0;
      }

      .i-c {
        width: calc(100% - 150px);
        padding: 0 0 0 15px;

        .i-c-title {
          color: #000;
          font-size: var(--font-size-L);
          font-weight: 500;
          margin-bottom: 15px;
        }

        .mb10 {
          margin-bottom: 10px;
        }

        .d-title {
          color: #828282;
          font-size: var(--font-size-L);
          font-weight: 500;
          line-height: 20px;
          flex-shrink: 0;
        }
        .d-content {
          color: #000;
          font-size: var(--font-size-L);
          font-weight: 500;
          line-height: 20px;
        }
      }
    }
  }

}
</style>
