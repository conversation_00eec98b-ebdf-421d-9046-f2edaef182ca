<template>
  <div class="course-center">
    <div class="wrap">
      <div class="left">
        <div class="institution mb-20">
          <div class="mb-20">内容认证机构：</div>
          <div class="flex items-center">
            <img :src="Institution" alt="机构" />
            <div class="fw-500">中华文化数字研究院</div>
          </div>
        </div>
        <div class="series-list">
          <div class="series" :class="{ 'series-active': selectSeries.id === -1 }" @click="changeSeries()">
            <div class="line"></div>
            全部
            <div class="series-num">{{ number }}</div>
          </div>
          <div
            v-for="item in seriesList"
            :key="item.id"
            class="series"
            :class="{ 'series-active': selectSeries.id === item.id }"
            @click="changeSeries(item)"
          >
            <div class="line"></div>
            {{ item.name || '' }}
            <div class="series-num">{{ number || 0 }}</div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="subject-list">
          <div
            v-for="item in selectSeries.childCategoryList"
            :key="item.id"
            class="subject"
            :class="{ 'subject-active': selectSubject && selectSubject.id === item.id }"
            @click="changeSubject(item)"
          >
            {{ item.name || '' }}
          </div>
        </div>
        <div v-if="courseList.length > 0" class="course-list">
          <div v-for="item in courseList" :key="item.id" class="course" @click="toDetail(item.id)">
            <img :src="item.coverUrl || DefaultCover" :alt="item.name || ''" />
            <span>{{ item.name }}</span>
          </div>
        </div>
        <div v-else class="empty">
          <img src="../../../assets/images/empty.png" alt="empty" />
          <div class="hint hint-padding">我们正在努力生产课程，敬请期待～</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DefaultCover from '@/assets/images/default-cover.jpg'
import Institution from '@/assets/images/course/institution.png'
import { getCourseSeriesList, getCoursePackageList } from '@/api/course-api'
export default {
  data () {
    return {
      DefaultCover,
      Institution,
      seriesList: [],
      courseList: [],
      selectSeries: { 'id': -1 },
      selectSubject: null,
      number: 0
    }
  },
  created () {
    this._getCourseSeriesList()
    this._getCoursePackageList()
  },
  methods: {
    _getCourseSeriesList () {
      getCourseSeriesList().then(
        response => {
          this.seriesList = response.data
        }
      )
    },
    _getCoursePackageList () {
      const params = {}
      if (this.selectSubject) {
        params['themeId'] = this.selectSubject.id
      }
      if (this.selectSeries.id !== -1) {
        params['seriesId'] = this.selectSeries.id
      }
      getCoursePackageList(params).then(
        response => {
          this.courseList = response.data
          if (!this.selectSubject) this.number = this.courseList.length
        }
      )
    },
    changeSeries (series) {
      if (!series) {
        this.selectSeries = { 'id': -1 }
      } else {
        this.selectSeries = series
      }
      this.courseList = []
      this.selectSubject = null
      this._getCoursePackageList()
    },
    changeSubject (subject) {
      console.log(subject)

      if (this.selectSubject === subject) {
        this.selectSubject = null
      } else {
        this.selectSubject = subject
      }
      this.courseList = []
      this._getCoursePackageList()
    },
    toDetail (categoryId) {
      this.$router.push(`/classpro/course/detail/${categoryId}`)
    }
  }
}
</script>

<style lang='scss' scoped>
.course-center {
  height: calc(100% - 62px);
}

.wrap {
  height: 100%;
  width: 100%;
  display: flex;

  .left {
    width: 200px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .institution {
      width: 100%;
      height: 105px;
      background: white;
      font-weight: 400;
      font-size: 12px;
      color: #343232;
      letter-spacing: 0;
      line-height: 17px;
      padding: 14px 0 14px 14px;

      img {
        width: 36px;
        height: 33px;
        object-fit: contain;
      }
    }

    .series-list {
      flex: 1;
      background: white;
      overflow: scroll;
      @include noScrollBar;
    }

    .series {
      width: 100%;
      height: 44px;
      padding-right: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #1C1B1A;
      letter-spacing: 0.26px;
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-bottom: 28px;

      .line {
        width: 2px;
        height: 100%;
        background: transparent;
        margin-right: 38px;
      }

      .series-num {
        font-weight: 400;
        font-size: 14px;
        color: transparent;
        letter-spacing: 0.22px;
        margin-left: auto;
      }
    }

    .series-active {
      color: #3479FF;
      background: #E7EFFF;

      .line {
        background: #3479FF;
      }

      .series-num {
        color: #4387FF;
      }
    }
  }

  .right {
    width: calc(100% - 200px);
    height: 100%;
    padding: 20px 0 20px 40px;

    .subject-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding-bottom: 20px;

      .subject {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: hsl(30, 4%, 11%);
        letter-spacing: 0.22px;
        background: transparent;
        border-radius: 15px;
        padding: 2.5px 16.5px;
        cursor: pointer;
      }

      .subject-active {
        color: #3479FF;
        background: #DFEBFF;
      }
    }

    .course-list {
      width: 100%;
      height: calc(100% - 20px);
      padding-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
      overflow: scroll;
      gap: 30px;
      column-gap: 30px;
      overflow-x: hidden;
      align-content: flex-start;
      @include scrollBar;

      .course {
        width: 265px;
        height: 200px;
        background: #FFFFFF;
        box-shadow: 0 3px 14px 0 rgba(233, 240, 255, 0.50);
        border-radius: 10px;
        cursor: pointer;

        img {
          width: 100%;
          height: 130px;
          border-radius: 10px 10px 0 0;
          object-fit: cover;
        }

        span {
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: 16px;
          color: #0B0B0B;
          word-break: break-all;
          padding: 10px 20px 0;
          @include ellipses(2);
        }
      }
    }

    .empty {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      img {
        width: 126px;
        height: 128px;
      }

      .hint {
        display: flex;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #8C8C8C;
        letter-spacing: 0.22px;

        .hint-blue {
          color: rgba(31, 102, 255, 1);
          cursor: pointer;
        }
      }

      .hint-padding {
        padding: 15px 0 4px
      }
    }
  }

  .mr-5 {
    margin-right: 5px;
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .fw-500 {
    font-weight: 500;
  }
}
</style>
