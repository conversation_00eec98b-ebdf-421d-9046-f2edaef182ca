<template>
  <div class="index-box">
    <div class="card-box">
      <div class="card-title-box">
        <div class="t-title">空中课堂</div>
      </div>

      <div class="course-tags-box">
        <div class="tags-btn" :class="{'active-btn': tagIndex === ''}" @click="handleClick('')">全部</div>
        <template v-if="subjectInfo.length > 0">
          <div
            v-for="sItem in subjectInfo"
            :key="sItem.strType"
            class="tags-btn"
            :class="{'active-btn': tagIndex === (sItem.intType)}"
            @click="handleClick(sItem.intType)"
          >{{ sItem.name }}</div>
        </template>
      </div>

      <div class="w">
        <el-row :gutter="20">
          <el-col v-for="item in courseList" :key="item.id" :span="6" class="course-box">
            <div class="w h pointer" @click="toDetail(item)">
              <div class="r-container">
                <div class="img-box">
                  <img v-if="item.coverUrl" :src="item.coverUrl" />
                  <img v-else src="../../../assets/images/default-cover.jpg" />
                </div>
              </div>
              <div class="course_title article-singer-container">
                {{ item.name }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import Institution from '@/assets/images/course/institution.png'
import { getCourseList, getSubjectConfig } from '@/api/course-api.js'
export default {
  data () {
    return {
      Institution,
      subjectObj: {},
      subjectInfo: [],
      subjectTemp: [],
      courseList: [],
      tagIndex: ''
    }
  },
  created () {
    this._getSubjectConfig()
  },
  methods: {
    async _getCourseList (count) {
      const obj = {
        pageNumber: 1,
        pageSize: 1000
      }
      if (this.tagIndex !== '') {
        obj['subjectType'] = this.subjectObj[+this.tagIndex].strType
      }
      const { data } = await getCourseList(obj)
      this.courseList = data.content
      // 第一次加载
      if (count === 1) {
        const set = new Set()
        if (data && data.content && data.content.length > 0) {
          data.content.map(val => {
            set.add(val.type)
          })
        }
        const arr = Array.from(set)
        this.subjectInfo = []
        arr.map(v => {
          if (this.subjectObj[+v]) {
            this.subjectInfo.push(this.subjectObj[+v])
          }
        })
      }
    },
    async _getSubjectConfig () {
      const { data } = await getSubjectConfig({
        subjectShowType: 'TEACHER_CHOSE'
      })
      if (data && data.length > 0) {
        data.map(val => {
          this.subjectObj[+val.intType] = val
        })
      }
      this._getCourseList(1)
    },
    handleClick (type) {
      this.tagIndex = type
      this._getCourseList()
    },
    // 去详情页面
    async toDetail (item) {
      console.log(item)
      this.$router.push(`/classpro/course/package/0/${item.id}/0`)
    }
  }
}
</script>

<style lang="scss" scoped>
.index-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  @include scrollBar;
  background: #FFF;
  border-radius: 10px;

  .course-tags-box {
    width: 100%;
    padding: 0 0 15px 0;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .tags-btn {
      display: flex;
      min-width: 100px;
      padding: 5px 10px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      border-radius: 5px;
      background: #F2F2F2;
      color: #4F4F4F;
      font-size: var(--font-size-L);
      margin: 0 10px 10px 0;
      cursor: pointer;
    }

    .active-btn {
      background: #DCF2FF;
      color: #2F80ED;
    }
  }

  .course-box {
    margin-bottom: 20px;
  }

  .card-box {
    padding: 5px 15px;
    margin-bottom: 10px;
    box-sizing: border-box;

    &:last-child {
      margin-bottom: 0;
    }

    .card-title-box {
      margin: 10px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .t-title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
      }
    }

    // 16:9 图片
    .r-container {
      position: relative;
      min-height: 0;
      padding-bottom: 56.25%;
      background-color: #eee;

      .img-box {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 5px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .course_title {
      color: #000;
      font-size: var(--font-size-L);
      margin: 10px 0;
    }

    .course_des {
      color: #000;
      font-size: var(--font-size-M);
      font-weight: 300;
      margin-bottom: 10px;
    }

  }
}
</style>
