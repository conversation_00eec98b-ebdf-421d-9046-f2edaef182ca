<template>
  <div class="login">
<!--    <img v-if="channel === 'digitalbook'" class="logo" src="@/assets/images/login/logo-digitalbook.png" alt="图标" />-->
    <img v-if="channel !== 'digitalbook'" class="logo" src="@/assets/images/login/logo.png" alt="图标" />
    <div class="form">
      <div class="left">
        <p class="title">{{ channel === 'digitalbook' ? '缤果数字教材' : '缤果课堂' }}</p>
        <div class="indicator"></div>
        <p class="subTitle" v-if="channel !== 'digitalbook'">用心传承文化，用行成就未来！</p>
        <template v-else>
          <div class="subTitle">新形态AI互动式教材</div>
          <div class="subTitle">让教学更简单，让学习更高效。</div>
        </template>
      </div>
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
        <div class="tab">
          <div class="account" @click="switchLoginType('SMS_CODE')">
            <span v-if="loginType==='SMS_CODE'" class="tab-title">短信登录</span>
            <span v-else class="tab-title-inactive">短信登录</span>
            <div v-if="loginType==='SMS_CODE'" class="indicator"></div>
            <div v-else class="indicator-null"></div>
          </div>
          <div class="account" @click="switchLoginType('PASSWORD')">
            <span v-if="loginType==='PASSWORD'" class="tab-title">账号登录</span>
            <span v-else class="tab-title-inactive">账号登录</span>
            <div v-if="loginType==='PASSWORD'" class="indicator"></div>
            <div v-else class="indicator-null"></div>
          </div>
        </div>
        <el-form-item prop="mobileOrEmail">
          <el-input
            v-model.trim="loginForm.mobileOrEmail"
            type="text"
            auto-complete="off"
            placeholder="请输入手机号"
            @input="mobileChange"
          >
            <img
              slot="prefix"
              class="el-input__icon input-icon"
              src="@/assets/images/login/mobile.png"
            />
          </el-input>
        </el-form-item>
        <el-form-item v-if="loginType=='SMS_CODE'" prop="code">
          <el-input
            v-model="loginForm.code"
            type="text"
            auto-complete="new-password"
            placeholder="请输入短信验证码"
          >
            <img
              slot="prefix"
              class="el-input__icon input-icon"
              src="@/assets/images/register/message.png"
            />
            <el-button
              slot="suffix"
              type="info"
              :disabled="smsDisabled"
              class="sms-btn"
              @click="getCode"
            >
              {{
                smsDisabled
                  ? countdown > 0
                    ? countdown + 's后重新获取'
                    : '发送验证码'
                  : '发送验证码'
              }}
            </el-button>
          </el-input>
        </el-form-item>
        <el-form-item v-if="loginType=='PASSWORD'" prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            auto-complete="new-password"
          >
            <img
              slot="prefix"
              class="el-input__icon input-icon"
              src="@/assets/images/login/password.png"
            />
          </el-input>
        </el-form-item>
        <el-form-item v-if="loginType=='PASSWORD'" style="width:100%;">
          <el-checkbox v-model="rememberMe" class="remember">
            记住登录状态
          </el-checkbox>
        </el-form-item>
        <el-form-item style="width:100%;">
          <div
            class="submit-btt"
            :class="[passwordFormValid ? 'button-blue' : 'button-grey']"
            @click="handleLogin"
            @keyup.enter="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </div>
          <div v-if="register" style="float: right;">
            <router-link class="link-type" :to="'/classpro/register'">立即注册</router-link>
          </div>
        </el-form-item>
        <div class="register-bottom">
          <span class="register-p">
            <router-link
              class="link-type"
              :to="'/classpro/register'"
            >注册账号</router-link></span>
          <span class="forget-p" @click="showForgetDialog()">忘记登录密码？</span>
        </div>
      </el-form>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <!-- 底部备案号 -->
      <div v-if="record" class="footer-icp">
        <a
          href="https://beian.miit.gov.cn"
          target="_blank"
        >ICP备案号：{{ record }}</a>
      </div>
    </div>
    <forget-dialog :forget-dialog-visible="forgetDialogVisible" class="classpro-dialog" @closeForgetDialog="closeForgetDialog" />

    <el-dialog
      title="获取验证码"
      :show-close="false"
      :center="true"
      :width="'350px'"
      :top="'35vh'"
      custom-class="nocaptcha-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      :visible.sync="nocaptchaVisible"
    >
      <i class="el-icon-error close-dialog" @click="nocaptchaVisible = false"></i>
      <div v-if="nocaptchaVisible" class="nocaptcha flex">
        <nocaptcha @callback="slideToGetCode" />
      </div>
    </el-dialog>
    <iframe
      v-show="showDowloadPage"
      ref="iframe"
      class="iframe-page"
      :src="`https://bingoclass.com.cn/downloadstudent?t=${Date.now()}`"
      width="100%"
      height="100%"
      frameborder="0"
      scrolling="auto"
    ></iframe>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { encrypt, decrypt } from '@/utils/jsencrypt'
import ForgetDialog from './components/forgetDialog'
import Nocaptcha from '@/components/classPro/Nocaptcha'
import { slideToGetSmsCode, verifyCodeForWeb } from '@/api/user-api'
import { validMobile, isElectron } from '@/utils/validate'

export default {
  name: 'Login',
  components: {
    ForgetDialog,
    Nocaptcha
  },
  data () {
    return {
      nocaptchaVisible: false,
      countdown: 0,
      smsDisabled: true,
      loginType: 'SMS_CODE',
      forgetDialogVisible: false,
      cookiePassword: '',
      rememberMe: false,
      loginForm: {
        mobileOrEmail: '',
        password: '',
        loginType: '',
        code: ''
      },
      loginRules: {
        mobileOrEmail: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入您的手机号'
          }
        ]
      },
      loading: false,
      // 验证码开关
      captchaOnOff: true,
      // 注册开关
      register: false,
      redirect: undefined,
      showDowloadPage: false,
      channel: ''
    }
  },
  computed: {
    passwordFormValid () {
      if (this.loginType === 'PASSWORD' && this.loginForm.mobileOrEmail !== '' && this.loginForm.password !== '') {
        return true
      } else if (this.loginType === 'SMS_CODE' && this.loginForm.mobileOrEmail !== '' && this.loginForm.code !== '') {
        return true
      } else {
        return false
      }
    },
    record () {
      const host = window.location.host
      if (host.indexOf('binguoketang.com') > -1) {
        return '蜀ICP备2021023754号-4'
      } else {
        return ''
      }
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    },
    'loginForm.mobileOrEmail'(val) {
      if (validMobile(val)) {
        this.smsDisabled = false
      } else {
        this.smsDisabled = true
      }
    }
  },
  created () {
    const mobileOrEmail = Cookies.get('mobileOrEmail')
    if (mobileOrEmail) {
      this.loginType = 'PASSWORD'
    }
    this.getChannelConfig()
    // console.log(window.location.host)
    this.getCookie()
    if (window.location.host === 'qa-book.cuiya.cn') {
      window.location.href = 'https://qa-book.cuiya.cn/#/loginweb?channel=digitalbook'
      return false
    }
    if (window.location.host === 'book.cuiya.cn') {
      window.location.href = 'https://book.cuiya.cn/#/loginweb?channel=digitalbook'
      return false
    }
    if (window.location.host === 'qa-book.binguoketang.com') {
      window.location.href = 'https://qa-book.binguoketang.com/#/loginweb?channel=digitalbook'
      return false
    }
    if (window.location.host === 'book.binguoketang.com') {
      window.location.href = 'https://book.binguoketang.com/#/loginweb?channel=digitalbook'
      return false
    }
    if (window.location.host === 'qa-book.bingomate.com') {
      window.location.href = 'https://qa-book.bingomate.com/#/loginweb?channel=digitalbook'
      return false
    }
    if (window.location.host === 'book.bingomate.com') {
      window.location.href = 'https://book.bingomate.com/#/loginweb?channel=digitalbook'
      return false
    }
    if (this.$route.path.indexOf('/classpro/login') > -1) {
      if (!isElectron()) {
        this.showDowloadPage = true
      }
    }
  },
  methods: {
    async getChannelConfig () {
      try {
        if (window.ipc) {
          const res = await window.ipc.invoke('getChannelConfig')
          this.channel = res
        } else {
          const channel = window.localStorage.getItem('channel')
          if (channel) {
            this.channel = channel
          }
        }
        if (this.$route.query && this.$route.query.channel) {
          this.channel = this.$route.query.channel
          localStorage.setItem('channel', this.channel)
        }
      } catch (error) {
        console.log(error)
      }
      if (this.channel === 'digitalbook') {
        const icon = document.querySelector('link[rel="icon"]')
        if (icon) {
          icon.href = '/digitalbookIcon/icon.ico'
        }
      }
    },
    switchLoginType (type) {
      this.loginType = type
      this.loginForm = {
        mobileOrEmail: '',
        password: '',
        loginType: '',
        code: ''
      }
      this.getCookie()
    },
    showForgetDialog () {
      console.log('showForgetDialog')
      this.forgetDialogVisible = true
    },
    closeForgetDialog () {
      this.forgetDialogVisible = false
    },
    getCode () {
      if (this.smsDisabled) return
      const mobile = this.loginForm.mobileOrEmail
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this._verifyCodeForWeb()
      // this.nocaptchaVisible = true
    },
    _verifyCodeForWeb () {
      verifyCodeForWeb({ mobile: this.loginForm.mobileOrEmail }).then(res => {
        this.countdown = 60
        this.smsDisabled = true
        setTimeout(this.tick, 1000)
      })
    },
    tick () {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    },
    mobileChange (val) {
      if (validMobile(val)) {
        this.smsDisabled = false
      } else {
        this.smsDisabled = true
      }
    },

    slideToGetCode (data) {
      console.log(data)
      slideToGetSmsCode({
        smsCodeType: 'LOGON',
        sessionId: data.sessionId,
        token: data.token,
        scene: data.scene,
        sig: data.sig,
        mobile: this.loginForm.mobileOrEmail
      }).then(response => {
        if (+response.code === 200) {
          this.countdown = 60
          this.smsDisabled = true
          setTimeout(this.tick, 1000)
          data.nc.reset()
          this.nocaptchaVisible = false
        } else {
          data.nc.reset()
          this.$message.error(response.data.message)
        }
      }).catch((e) => {
        data.nc.reset()
        console.log(e)
      })
    },
    getCookie () {
      const mobileOrEmail = Cookies.get('mobileOrEmail')
      const password = Cookies.get('password')
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        mobileOrEmail: mobileOrEmail === undefined ? this.loginForm.mobileOrEmail : mobileOrEmail,
        password: password === undefined ? this.loginForm.password : decrypt(password)
      }
      this.rememberMe = rememberMe === undefined ? false : Boolean(rememberMe)
    },
    handleLogin () {
      // console.log('login....', this.loginForm)
      if (!this.passwordFormValid) return
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          localStorage.removeItem('hasAutoUpdateChecked')
          if (this.loginType === 'PASSWORD') {
            if (this.rememberMe) {
              Cookies.set('mobileOrEmail', this.loginForm.mobileOrEmail, { expires: 30 })
              Cookies.set('password', encrypt(this.loginForm.password), { expires: 30 })
              Cookies.set('rememberMe', this.rememberMe, { expires: 30 })
            } else {
              Cookies.remove('mobileOrEmail')
              Cookies.remove('password')
              Cookies.remove('rememberMe')
            }
          }
          this.loginForm.loginType = this.loginType
          // console.log('login2....', this.loginForm)
          if (this.$route.query && this.$route.query.channel) {
            window.localStorage.setItem('channel', this.$route.query.channel)
          }
          this.$store.dispatch('user/Login', this.loginForm).then(() => {
            // this.$router.push({ path: this.redirect || '/' }).catch(() => {
            // })
            this.$router.push({ path: '/' }).catch(() => {
            })
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
  $vw_design_width: 965;
  $vw_design_height: 650;
  @function ui_w($px) {
    @return $px / $vw_design_width * 100vw;
  }

  @function ui_h($px) {
    @return $px / $vw_design_height * 100vh;
  }

  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    //width: 100%;
    background-image: url('../../../assets/images/login/login-bg.png');
    background-size: cover;
    overflow-y:auto;
    overflow-x: hidden;
    //&::-webkit-scrollbar {
    //  /*滚动条整体样式*/
    //  width: 5px;
    //  height: 1px;
    //}

    .logo {
      position: absolute;
      left: 33px;
      top: 21px;
      width: 135px;
      height: 50px;
    }

    .title {
      margin: 0px auto 30px auto;
      text-align: center;
      color: #707070;
    }

    .form {
      //height: ui_h(386);
      //width: ui_h(693);
      height: 300px;
      width: 560px;
      display: flex;
      justify-content: center;
      flex-direction: row;
      background-image: url('../../../assets/images/login/form.png');
      box-shadow: 0px 3px 8px 5px rgba(62, 89, 253, 0.05);
      border-radius: 15px;
      background-size: cover;

      .el-input {
        height: 28px;

        input {
          height: 28px;
          border: none;
          border-bottom: 1px solid rgba(151, 151, 151, 0.15);
        }

        .el-input__prefix {
          height: 39px;
          line-height: 39px;
        }
      }

      .input-icon {
        height: 14px;
        width: 14px;
        margin-left: 2px;
      }
    }

    .left {
      height: 300px;
      width: 260px;
      display: flex;
      flex-direction: column;
      padding-left: 20px;
      padding-top: 80px;

      .title {
        font-size: 20px;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 37px;
        text-align: left;
        margin: 0;
      }

      .subTitle {
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 28px;
        text-align: left;
      }

      .indicator {
        margin-top: 4px;
        margin-bottom: 16px;
        width: 40px;
        height: 2px;
        background: #FFFFFF;
        border-radius: 2px;
      }

    }

    .login-form {
      display: flex;
      flex-direction: column;
      background-size: cover;
      border-radius: 6px;
      height: 300px;
      width: 280px;
      padding-left: 55px;
      padding-right: 55px;

      .el-form-item{
        height: 50px;
        margin-bottom: 1px;
      }
      .el-form-item__error{
          font-size: 10px;
        }
      .el-input {
        // height: 20px;

        input {
          font-size: 10px;
          // height: 20px;
        }
      }
    }

    .login-tip {
      font-size: 13px;
      text-align: center;
      color: #bfbfbf;
    }

    .login-code {
      width: 33%;
      height: 38px;
      float: right;

      img {
        cursor: pointer;
        vertical-align: middle;
      }
    }

    .el-login-footer {
      height: 40px;
      line-height: 40px;
      position: fixed;
      bottom: 0;
      width: 100%;
      text-align: center;
      color: #fff;
      font-family: Arial;
      font-size: 12px;
      letter-spacing: 1px;
    }

    .login-code-img {
      height: 38px;
    }

    .tab {
      margin-top: 30px;
        margin-bottom: 30px;
      padding-left: 0px;
      display: flex;
      flex-direction: row;
      justify-content: space-around;

      .account {
        display: flex;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        align-items: center;
        width: 120px;

        .tab-title {
          text-align: center;
          width: 100%;
          height: 20px;
          font-size: 14px;
          font-weight: 500;
          color: #0B0B0B;
          line-height: 25px;
        }

        .tab-title-inactive {
          width: 90px;
          height: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #8C9399;
          line-height: 25px;
          text-align: center;
        }

        .indicator {
          margin-top: ui_h(4);
          width: ui_h(21);
          height: ui_h(4);
          background: #1F66FF;
          border-radius: ui_w(3);
        }

        .indicator-null {
          margin-top: ui_h(4);
          width: ui_w(21);
          height: ui_h(4);
          background: #FFFFFF;
          border-radius: ui_w(3);
        }
      }
    }

    .remember {
      width: 84px;
      height: 20px;
      font-size: 8px;
      font-weight: 400;
      color: #8C9399;
      line-height: 20px;
      .el-checkbox__inner {
        border-radius: 50%;
      }
    }

    .register-bottom {
      display: flex;
      margin-bottom: 20px;
      flex-direction: row;
      width: 100%;
      justify-content: space-between;

      .register-p,
      .forget-p {
         width: 120px;
        // width: 120px;
        // height: 20px;
        font-size: 8px;
        font-weight: 400;
        color: #1F66FF;
        line-height: 20px;
        cursor: pointer;
      }
      // .register-p {
      //   width: 90px;
      // }
      // .forget-p {
      //   width: 140px;
      // }
    }

    .button {
      width: 248px;
      height: 40px;
      background: url('../../../assets/images/login/login-btn-blue.png') center center no-repeat;
      background-size: contain;
      line-height: 40px;
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      cursor: pointer;
    }

    .code-button {
      background: #1F66FF;
      background-size: cover;
      width: 108px;
      height: 30px;
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      border-radius: 240px;
    }
    .sms-btn {
      position: absolute;
      top: 8px;
      right: -10px;
      margin: 3px;
      padding: 0;
      width: 55px;
      font-size: 10px;
      height: 20px;
      border-radius: 6px;
      font-weight: 500;
      background: #1F66FF;
      color: #FFFFFF;
      box-sizing: border-box !important;
      box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.16);

      &.is-disabled {
        border: #FFFFFF;
        background: #A1A1A1;
        color: #FFFFFF;
      }
      &.is-disabled:hover {
        border: #FFFFFF;
        background: #A1A1A1;
        color: #FFFFFF;
      }
    }

    .sms-btn:hover {
      background: #1F66FF;
      box-shadow: 1px 1px 1px #fff;
    }

    .submit-btt {
      width: 100%;
      height: 26px;
      background: #1F66FF;
      box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
      border-radius: 16px;
      font-size: 14px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 26px;
      text-align: center;
    }

    .button-blue {
        background: #1F66FF;
        box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41), 1px 1px 1px 0px #899AFF, -1px -1px 1px 0px rgba(11, 26, 119, 0.32);
        color: #FFFFFF;
        cursor: pointer;
        &:hover {
          background: #6193FF;
          box-shadow: 0px 3px 5px 0px rgba(62, 89, 253, 0.41);
        }
    }

    .button-grey {
        background: #BFBFBF;
        box-shadow: 0px 3px 5px 0px rgba(132, 132, 132, 0.41), 1px 1px 1px 0px #E7E7E7, -1px -1px 1px 0px rgba(129, 129, 134, 0.32);
        color: #FFFFFF;
    }

    .el-input__inner {
        border-radius: 0px;
        border: 0px solid transparent;
        border-bottom: 1px solid rgba(151, 151, 151, 0.15);
    }

    .el-input__prefix {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .el-input input {
      background: transparent;
    }

    .input-icon {
      width: 15px;
      height: 13px;
    }
  }
  .footer-icp {
    width: 100%;
    height: 40px;
    font-size: 14px;
    color: #000000;
    line-height: 40px;
    text-align: center;
  }

  .iframe-page {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 9999;
    background: linear-gradient(90deg,#f3e7e9,#e3eeff 99%,#e3eeff);
  }
</style>
