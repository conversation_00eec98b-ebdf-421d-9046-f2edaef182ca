<script>
import { jHub<PERSON>auth } from '@/api/jhub-api'

export default {
  name: '<PERSON><PERSON><PERSON>',
  created() {
    // 解析完整的 URL 获取查询参数
    const url = new URL(window.location.href)
    const clientId = url.searchParams.get('client_id')
    const redirectUri = url.searchParams.get('redirect_uri')
    const responseType = url.searchParams.get('response_type')
    const state = url.searchParams.get('state')
    console.log(clientId, redirectUri, state)
    // window.history.replaceState({}, document.title, window.location.pathname + window.location.hash)
    jHubOauth({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: responseType,
      state: state
      // userId: 1
    }).then(res => {
      console.log(res)
      window.location.href = res.data
    }).catch(err => {
      console.error(err)
    })
  }
}
</script>

<template>
  <div>
    <h1><PERSON>Auth</h1>
  </div>
</template>

<style scoped>

</style>
