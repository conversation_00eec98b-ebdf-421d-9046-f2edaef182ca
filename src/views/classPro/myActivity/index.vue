<template>
  <div class="index-box">
    <div class="card-box">
      <div class="card-title-box">
        <div class="t-title">我的活动</div>
      </div>
      <div class="w h overflow-y-auto carlist">
        <div v-for="item in list" :key="item.id" class="w">
          <div class="act-box">
            <div class="img-box">
              <div class="r-container">
                <div class="img-box">
                  <img v-if="item.cover" :src="item.cover" />
                  <img v-else src="../../../assets/images/default-cover.jpg" />
                </div>
              </div>
            </div>
            <div class="act-body-box">
              <div class="act-title article-singer-container">
                {{ item.name }}
              </div>
              <div class="act-time">
                <template v-if="!item.works">参赛截止：{{ (item.endSubmit) | formateTime }}</template>
                <template v-else>活动截止：{{ (item.endTime) | formateTime }}</template>
              </div>
              <div class="act-status">
                <template v-if="!item.works">已报名</template>
              </div>
              <div class="act-btns">
                <div class="btn" @click="jion(item)">
                  <template v-if="!item.works">立即参赛</template>
                  <template v-else>我的作品</template>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="list.length === 0" class="empty-container">
          <img src="@/assets/images/empty.png" />
          <div class="hint">暂时还没有数据</div>
        </div>
      </div>
    </div>
    <activity-share ref="share" />
  </div>
</template>

<script>
import { registeredActivityList } from '@/api/activity-api.js'
import ActivityShare from './components/activity-share.vue'
import moment from 'moment'
export default {
  components: {
    ActivityShare
  },
  filters: {
    formateTime (date) {
      return moment(date).format('YYYY.MM.DD')
    },
    canApply (item) {
      if (moment(new Date()).isAfter(item.endSubmit)) {
        return false
      }
      return true
    }
  },
  data () {
    return {
      list: [],
      statusJson: {
        'GOING': '进行中',
        'FINISHED': '已结束',
        'COMING': '未开始'
      }
    }
  },
  mounted () {
    this._registeredActivityList()
  },
  methods: {
    async _registeredActivityList () {
      const { data } = await registeredActivityList()
      this.list = data
      console.log(this.list)
    },
    jion (item) {
      let url = ''
      if (item.works) {
        // this.$router.push({ path: '/h5/check-work', query: { id: item.id, workId: item.works && item.works.id }})
        url = `/#/h5/check-work?id=${item.id}&workId=${item.works && item.works.id}`
      } else {
        if (moment(new Date()).isAfter(item.endSubmit)) {
          this.$message.error('参赛时间已过')
          return
        }
        url = `/#/h5/submit-work?id=${item.id}`
        // this.$router.push({ path: '/h5/submit-work', query: { id: item.id }})
      }
      this.$refs.share.openDialog(url)
    }
  }
}
</script>

<style lang="scss" scoped>
.index-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  background: #FFF;
  border-radius: 10px;

  .empty-container {
    width: 100%;
    height: calc(100% - 200px);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .carlist {
    @include scrollBar;
  }
  .card-box {
    padding: 5px 15px;
    margin-bottom: 10px;
    box-sizing: border-box;
    height: calc(100% - 40px);

    &:last-child {
      margin-bottom: 0;
    }

    .card-title-box {
      margin: 10px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .t-title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
      }
    }

    .act-body-box {
      width: calc(100% - 30% - 10px);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .act-box {
      width: 100%;
      margin-bottom: 20px;
      display: flex;
    }

    .img-box {
      width: 30%;
      margin-right: 10px;
    }

    .act-title {
      color: #000;
      font-size: var(--font-size-L)
    }

    .act-time {
      color: #4F4F4F;
      font-size: var(--font-size-L);
    }

    .act-status {
      color: #4F4F4F;
      font-size: var(--font-size-L);
    }

    .act-btns {
      .btn {
        width: 100px;
        display: flex;
        padding: 5px 4px;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: #2F80ED;
        color: #FFF;
        font-size: var(--font-size-L);
        cursor: pointer;
      }
    }

    // 16:9 图片
    .r-container {
      position: relative;
      min-height: 0;
      padding-bottom: 56.25%;
      background-color: #eee;

      .img-box {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 5px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}</style>
