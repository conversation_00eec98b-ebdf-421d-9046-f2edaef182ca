<template>
  <div class="register">
    <a href="/classpro/login"><img
      class="logo"
      src="@/assets/images/login/logo.png"
      alt="图标"
    /></a>
    <div class="form2">
      <div class="tab-register">
        <router-link class="link-type" :to="'/classpro/login'">
          <img src="@/assets/images/register/back.png" />
        </router-link>
        <div class="account">
          <span class="tab-title">注册账号</span>
        </div>
      </div>
      <el-form
        ref="registerForm"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
      >
        <el-form-item prop="mobileOrEmail">
          <el-input
            ref="mobileOrEmail"
            v-model.trim="registerForm.mobileOrEmail"
            name="mobileOrEmail"
            type="text"
            autocomplete="off"
            placeholder="请输入手机号码"
            @input="mobileChange"
          >
            <img
              slot="prefix"
              class="el-input__icon input-icon"
              src="@/assets/images/register/mobile.png"
            />
          </el-input>
        </el-form-item>
        <el-form-item prop="verifyCode">
          <el-input
            v-model="registerForm.verifyCode"
            type="text"
            auto-complete="off"
            placeholder="请输入短信验证码"
          >
            <img
              slot="prefix"
              class="el-input__icon input-icon"
              src="@/assets/images/register/message.png"
            />
            <el-button
              slot="suffix"
              type="info"
              :disabled="smsDisabled"
              class="sms-btn"
              @click="getCode"
            >
              {{
                smsDisabled
                  ? countdown > 0
                    ? countdown + 's后重新获取'
                    : '发送验证码'
                  : '发送验证码'
              }}
            </el-button>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            auto-complete="new-password"
            placeholder="请设置登录密码"
          >
            <img
              slot="prefix"
              class="el-input__icon input-icon"
              src="@/assets/images/register/password.png"
            />
          </el-input>
        </el-form-item>
        <el-form-item prop="password2">
          <el-input
            v-model="registerForm.password2"
            type="password"
            auto-complete="new-password"
            placeholder="请再次输入密码"
          >
            <img
              slot="prefix"
              class="el-input__icon input-icon"
              src="@/assets/images/register/code.png"
            />
          </el-input>
        </el-form-item>
        <!--        <el-form-item style="width:100%;">-->
        <!--          <el-checkbox v-model="checkFile" class="remember">-->
        <!--            勾选同意<a href="https://www.baidu.com" target="_blank">《用户服务协议》</a>-->
        <!--          </el-checkbox>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <div
            v-if="registerForm.mobileOrEmail!=''&&registerForm.password!=''
              &&registerForm.password2!=''&&registerForm.verifyCode!=''"
            class="button active"
            :loading="loading"
            size="medium"
            type="primary"
            @click="handleRegister"
          ><span v-if="!loading">确  定</span>
            <span v-else>登 录 中...</span>
          </div>
          <div
            v-else
            class="button disabled"
            :loading="loading"
            size="medium"
            type="primary"
          ><span v-if="!loading">确  定</span>
            <span v-else>登 录 中...</span>
          </div>
          <!-- <div style="float: right;" v-if="register">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div> -->
        </el-form-item>
      </el-form>
    </div>
    <!--  底部  -->
    <div class="el-login-footer"></div>

    <el-dialog
      title="获取验证码"
      :show-close="false"
      :center="true"
      :width="'350px'"
      :top="'35vh'"
      custom-class="nocaptcha-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      :visible.sync="nocaptchaVisible"
    >
      <i class="el-icon-error close-dialog" @click="nocaptchaVisible = false"></i>
      <div v-if="nocaptchaVisible" class="nocaptcha flex">
        <nocaptcha @callback="slideToGetCode" />
      </div>
    </el-dialog>
  </div>

</template>

<script>
import Nocaptcha from '@/components/classPro/Nocaptcha'
import { register, slideToGetSmsCode, verifyCode } from '@/api/user-api'
import { validMobile } from '@/utils/validate'

export default {
  name: 'Register',
  components: { Nocaptcha },
  data () {
    const validateUsername = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('请输入手机号'))
      }
      if (!validMobile(value)) {
        callback(new Error('手机号错误'))
      } else {
        callback()
      }
    }

    const validatePassword = (rule, value, callback) => {
      const passwordreg = /(?![A-Z]*$)(?![a-z]*$)(?![0-9]*$)(?![^a-zA-Z0-9]*$)/
      if (!value || value.length === 0) {
        callback(new Error('密码不能为空'))
      }
      if (!passwordreg.test(value)) {
        callback(new Error('密码长度在8-20之间,且必须包含数字、大写字母、小写字母、特殊字符中的两种'))
      } else {
        callback()
      }
    }
    const validateCode = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('验证码不能为空'))
      } else {
        callback()
      }
    }
    const validateCheckPass = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error('确认密码不能为空'))
      }

      if (value !== this.registerForm.password) {
        callback(new Error('确认密码与密码不匹配'))
      } else {
        callback()
      }
    }
    return {
      nocaptchaVisible: false,
      checkFile: false,
      registerForm: {
        mobileOrEmail: '',
        password: '',
        password2: '',
        userType: 'STUDENT',
        verifyCode: ''
      },
      registerRules: {
        mobileOrEmail: [{
          required: true,
          trigger: 'blur',
          validator: validateUsername
        }],
        password: [{
          required: true,
          trigger: 'blur',
          validator: validatePassword
        }],
        password2: [{
          required: true,
          trigger: 'blur',
          validator: validateCheckPass
        }],
        verifyCode: [{
          required: true,
          trigger: 'blur',
          validator: validateCode
        }]
      },
      loading: false,
      countdown: 0,
      smsDisabled: true
    }
  },
  methods: {
    getCode () {
      if (this.smsDisabled) return
      const mobile = this.registerForm.mobileOrEmail
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this.nocaptchaVisible = true
    },
    tick () {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    },
    mobileChange (val) {
      if (validMobile(val)) {
        this.smsDisabled = false
      } else {
        this.smsDisabled = true
      }
    },

    slideToGetCode (data) {
      console.log(data)
      const mobile = this.registerForm.mobileOrEmail
      if (data.codeKey) {
        verifyCode({
          mobile: mobile,
          codeKey: data.codeKey
        }).then(response => {
          if (+response.code === 200) {
            this.countdown = 60
            this.smsDisabled = true
            setTimeout(this.tick, 1000)
            this.nocaptchaVisible = false
            data.nc.reset()
          } else {
            data.nc.reset()
            this.$message.error(response.data.message)
          }
        })
      } else {
        slideToGetSmsCode({
          smsCodeType: 'RIGESTER',
          sessionId: data.sessionId,
          token: data.token,
          scene: data.scene,
          sig: data.sig,
          mobile: this.registerForm.mobileOrEmail
        }).then(response => {
          if (+response.code === 200) {
            this.countdown = 60
            this.smsDisabled = true
            setTimeout(this.tick, 1000)
            data.nc.reset()
            this.nocaptchaVisible = false
          } else {
            data.nc.reset()
            this.$message.error(response.data.message)
          }
        }).catch((e) => {
          data.nc.reset()
          console.log(e)
        })
      }
    },
    handleRegister () {
      this.$refs.registerForm.validate(async valid => {
        if (valid) {
          register(this.registerForm).then(res => {
            if (res.code === 200) {
              this.$router.push({ path: this.redirect || '/classpro/login' }).catch(() => {
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
$vw_design_width: 965;
$vw_design_height: 650;
@function ui_w($px) {
  @return $px / $vw_design_width * 100vw;
}

@function ui_h($px) {
  @return $px / $vw_design_height * 100vh;
}

.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url('../../../assets/images/login/login-bg.png');
  background-size: cover;

  .logo {
    position: absolute;
    left: 33px;
    top: 21px;
    width: 135px;
    height: 50px;
  }

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }

  .form2 {
    display: flex;
    justify-content: center;
    flex-direction: column;
    background-image: url('../../../assets/images/register/register-bg.png');
    background-size: cover;
    border-radius: 6px;
    height: ui_h(412);
    width: ui_h(719);

    .el-input {
      height: 38px;

      input {
        height: 38px;
        border: none;
        border-bottom: 1px solid rgba(151, 151, 151, 0.15);
      }

      .el-input__prefix {
        height: 39px;
        line-height: 39px;
      }
    }

    .input-icon {
      height: 14px;
      width: 14px;
      margin-left: 2px;
    }
  }

  .left {
    height: ui_h(412);
    width: ui_h(360);
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding-left: ui_w(27);

    .title {
      font-size: 26px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 37px;
      text-align: left;
      margin: 0;
    }

    .subTitle {
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 28px;
      text-align: left;
    }

    .indicator {
      width: ui_w(41);
      height: ui_h(4);
      background: #FFFFFF;
      border-radius: 2px;
      margin-top: ui_h(12);
    }
  }

  .register-form {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    background-size: cover;
    border-radius: 6px;
    height: ui_h(412);
    width: ui_h(719);
    padding-left: ui_h(147);
    padding-right: ui_h(147);
    padding-bottom: ui_h(27);;

    .el-input {
      height: 38px;

      input {
        height: 38px;
      }
    }
  }

  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    height: 38px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .login-code-img {
    height: 38px;
  }

  .tab-register {
    margin-top: ui_h(33);
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    padding-left: ui_w(38);

    img {
      position: absolute;
      width: 34px;
      height: 34px;
    }

    .account {
      display: flex;
      width: ui_w(700);
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .tab-title {
        text-align: left;
        width: ui_w(72);
        height: ui_h(25);
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #0B0B0B;
        line-height: ui_h(25);
      }

      .indicator {
        margin-top: ui_h(4);
        width: ui_w(21);
        height: ui_h(4);
        background: #1F66FF;
        border-radius: ui_w(3);
      }
    }
  }

  .remember {
    width: 84px;
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #8C9399;
    line-height: 20px;
  }

  .register-bottom {
    display: flex;
    margin-bottom: ui_h(20);
    flex-direction: row;
    width: 100%;
    justify-content: space-between;

    .register-p {
      width: 98px;
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #1F66FF;
      line-height: 20px;
    }
  }

  .button {
    width: ui_w(238);
    height: 40px;
    background-size: contain;
    line-height: 40px;
    font-size: 14px;
    font-weight: 500;
    color: #FFFFFF;
    text-align: center;
    cursor: pointer;
    border-radius: 40px;
    margin: 0 auto;
  }

  .active {
    background: url('../../../assets/images/btn.png') center center no-repeat;
  }

  .disabled {
    background: url('../../../assets/images/btn-grey.png') center center no-repeat;
  }

  .code-button {
    background: #1F66FF;
    background-size: cover;
    width: 108px;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    border-radius: 240px;
  }

  .sms-btn {
    position: absolute;
    top: 0;
    right: 0;
    margin: 3px;
    padding: 0;
    width: 92px;
    height: 30px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    background: #1F66FF;
    color: #FFFFFF;
    box-sizing: border-box !important;
    box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.16);

    &.is-disabled {
      border: #FFFFFF;
      background: #A1A1A1;
      color: #FFFFFF;
    }

    &.is-disabled:hover {
      border: #FFFFFF;
      background: #A1A1A1;
      color: #FFFFFF;
    }
  }

  .sms-btn:hover {
    background: #1F66FF;
    box-shadow: 1px 1px 1px #fff;
  }

  .el-input__inner {
    border-radius: 0px;
    border: 0px solid transparent;
    border-bottom: 1px solid rgba(151, 151, 151, 0.15);
  }

  .el-input__prefix {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .el-input input {
    background: transparent;
  }

  .input-icon {
    width: 15px;
    height: 13px;
  }
}

</style>
