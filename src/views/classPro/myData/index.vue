<template>
  <div class="index-box">
    <div class="card-box h">
      <div class="card-title-box">
        <div class="t-title">我的数据</div>
      </div>

      <div class="w body-h">
        <DataIndex />
      </div>
    </div>
  </div>
</template>

<script>
import DataIndex from '@/views/classPro/data/index.vue'
export default {
  components: {
    DataIndex
  },
  data () {
    return {
    }
  }
}
</script>

<style lang="scss" scoped>
.index-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  @include scrollBar;
  background: #FFF;
  border-radius: 10px;

  .body-h {
    height: calc(100% - 50px);
    /*增加滚动条*/
    overflow-y:auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 5px;
      height: 1px;
    }
  }

  .card-box {
    padding: 5px 15px;
    margin-bottom: 10px;
    box-sizing: border-box;

    &:last-child {
      margin-bottom: 0;
    }

    .card-title-box {
      margin: 10px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .t-title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
      }
    }
  }
}
</style>
