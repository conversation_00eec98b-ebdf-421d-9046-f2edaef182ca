<template>
  <div class="detail_main">
    <div class="header">
      <img
        src="@/assets/images/skyclass/arrow-back.png"
        alt="返回按钮"
        @click="backToHome"
      />
      <div class="back" @click="backToHome">返回</div>
    </div>
    <div class="content_main">
      <div class="right_card">
        <div class="right_head">
          <el-breadcrumb class="bread" separator="/">
            <el-breadcrumb-item v-for="item in breadList" :key="item">{{
              item
            }}</el-breadcrumb-item>
          </el-breadcrumb>
          <el-input
            v-model="searchValue"
            class="input"
            placeholder="根据关键字查询"
          >
            <i slot="prefix" class="el-icon-search icon"></i>
          </el-input>
        </div>
        <el-table
          ref="multipleTable"
          class="table"
          :row-class-name="rowClass"
          :data="tableData"
          height="50"
          :show-header="false"
          style="width: 100%"
        >
          <el-table-column label="文件名" show-overflow-tooltip>
            <template slot-scope="scope">
              <img class="img" :src="findTitleImg(scope.row)" alt="" />
              <span style="margin-left: 50px">{{
                scope.row.fileName +
                  '.' +
                  scope.row.expendType
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="大小">
            <template slot-scope="scope">
              <span>{{ formatBytes(scope.row.size) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="上传时间">
            <template slot-scope="scope">
              <p>
                上传时间： <span>{{ scope.row.createdAt }}</span>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="preview(scope.row)"
              >预览</el-button>
              <el-button
                type="text"
                size="small"
                @click="download(scope.row)"
              >下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div v-if="previewUserBox" class="preview-box">
      <div class="preview-content">
        <div class="preview-content-head">
          <div class="title">文件列表</div>
          <div @click.stop="previewUserBox = false">
            <i class="el-icon-close pointer"></i>
          </div>
        </div>
        <div class="preview-content-content">
          <resourceDetail
            :id="preCurrentId"
            :files-list="tableData.map(item => ({
              mediaFileId: item.id,
              id: item.id,
              mediaFile: {
                ...item,
              }
            }))"
            :has-del="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { getFileUploadAuthor } from '@/api/user-api'
import { getAiCourseResourceList } from '@/api/course-api'
import { throttle } from '@/utils/index'
import resourceDetail from '../../digitalbooks/resource/components/datas.vue'
import { saveAs } from 'file-saver'
import { Notification } from 'element-ui'
export default {
  components: {
    resourceDetail
  },
  data() {
    return {
      imgList: {
        word: require('@/assets/digitalbooks/resource/word.svg'),
        excel: require('@/assets/digitalbooks/resource/excel.svg'),
        ppt: require('@/assets/digitalbooks/resource/ppt.svg'),
        pdf: require('@/assets/digitalbooks/resource/pdf.svg'),
        unknown: require('@/assets/digitalbooks/resource/unknown.svg'),
        zip: require('@/assets/digitalbooks/resource/zip.svg')
      },
      resType: 'DIGITAL_RESOURCE',
      status: 0,
      bookId: '',
      preCurrentId: null,
      previewUserBox: false,
      unitId: null,
      progress: false,
      percent: 0,
      messageType: null,
      access: true,
      preInfo: {
        type: 'VIDEO'
      },
      breadList: [],
      searchValue: '',
      checkAll: false,
      multipleSelection: [],
      currChapter: null,
      token: '',
      editChapterShow: false,
      editMessageShow: false,
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      downloadInfo: [],
      bookTree: [],
      tableData: [],
      tableDataCopy: []
    }
  },
  computed: {
    findPercent: function () {
      return (val) => {
        const data = this.downloadInfo
        return Math.floor(
          (data.filter((item) => item.name === val)[0].loaded /
            data.filter((item) => item.name === val)[0].total) *
            100
        )
      }
    }
  },
  watch: {
    resType() {
      this._getBookCatalogue()
      this.tableDataCopy = []
      this.tableData = []
      this.unitId = null
      this.breadList = []
    },
    searchValue(val) {
      this.tableData = this.tableDataCopy.filter((item) => {
        return (
          item.expendType.includes(val) ||
          item.fileName.includes(val)
        )
      })
    },
    downloadInfo(val) {
      console.log(val)
    }
  },
  mounted() {
    this.token = this.$route.query.token
      ? 'Bearer ' + this.$route.query.token
      : getToken()
    this.getSourceList()
  },
  methods: {
    checkUpdate() {
      let data = JSON.parse(localStorage.getItem('resource'))
      if (!data) {
        data = [
          {
            id: this.unitId,
            time: new Date()
          }
        ]
        localStorage.setItem('resource', JSON.stringify(data))
        return
      }
      const flag = data.filter((item) => {
        return item.id === this.unitId
      })
      if (flag.length === 0) {
        data.push({
          id: this.unitId,
          time: new Date()
        })
        localStorage.setItem('resource', JSON.stringify(data))
        return
      }
      let tag = 0
      this.tableData.forEach((item) => {
        if (
          new Date(flag[0].time).getTime() < new Date(item.updatedAt).getTime()
        ) {
          tag += 1
        }
      })
      if (tag !== 0) {
        this.$message.info(`有${tag}条资源更新`)
      }
      data = data.map((item) => {
        if (item.id === this.unitId) {
          return {
            id: item.id,
            time: new Date()
          }
        }
        return item
      })
      localStorage.setItem('resource', JSON.stringify(data))
    },
    findTitleImg(row) {
      if (
        row.type === 'IMAGE' &&
        row.expendType !== 'svg'
      ) {
        return (
          row.url.split('?x-oss-process')[0] +
          '?x-oss-process=image/format,jpg/resize,w_30'
        )
      }
      if (row.expendType === 'svg') {
        return row.url
      }
      if (row.type === 'VIDEO') {
        return `${row.url}?x-oss-process=video/snapshot,t_1000,m_fast`
      }
      const type = this.getFileType(row.expendType)
      if (type === 'WORD') {
        return this.imgList.word
      }
      if (type === 'EXCEL') {
        return this.imgList.excel
      }
      if (type === 'PDF') {
        return this.imgList.pdf
      }
      if (type === 'PPT') {
        return this.imgList.ppt
      }
      if (type === 'ZIP') {
        return this.imgList.zip
      }
      return this.imgList.unknown
    },
    preview(row) {
      console.log(row)
      this.preCurrentId = row.id
      this.previewUserBox = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.checkAll = this.multipleSelection.length === this.tableData.length
    },
    handleCheckAllChange(val) {
      val
        ? this.$nextTick(() => {
          this.$refs.multipleTable.clearSelection()
          this.tableData.forEach((item) => {
            this.$refs.multipleTable.toggleRowSelection(item, true)
          })
        })
        : this.$refs.multipleTable.clearSelection()
    },
    backToHome() {
      if (
        this.$route.query.token &&
        this.$route.query.token.indexOf('Bearer') === -1
      ) {
        window.close()
        return
      }
      this.$router.go(-1)
    },

    getFileType(str) {
      var result = ''
      var imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'svg']
      // 进行图片匹配
      result = imglist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'IMAGE'
        return result
      }
      // 匹配txt
      var txtlist = ['txt']
      result = txtlist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'TXT'
        return result
      }
      // 匹配 excel
      var excelist = ['xls', 'xlsx']
      result = excelist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'EXCEL'
        return result
      }
      // 匹配 word
      var wordlist = ['doc', 'docx']
      result = wordlist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'WORD'
        return result
      }
      // 匹配 pdf
      var pdflist = ['pdf']
      result = pdflist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'PDF'
        return result
      }
      // 匹配 ppt
      var pptlist = ['ppt', 'pptx']
      result = pptlist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'PPT'
        return result
      }
      // 匹配 视频
      var videolist = ['mp4', 'm2v', 'mkv']
      result = videolist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'VIDEO'
        return result
      }
      // 匹配 音频
      var radiolist = ['mp3', 'wav', 'wmv']
      result = radiolist.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'RADIO'
        return result
      }
      // 匹配压缩包
      var zipList = ['zip', 'rar']
      result = zipList.some(function (item) {
        return item === str
      })
      if (result) {
        result = 'ZIP'
        return result
      }
      // 其他 文件类型
      result = 'OTHER'
      return result
    },
    async getSourceList() {
      const { data } = await getAiCourseResourceList(
        {
          resourceId: this.$route.params.id,
          resourceType: 'DIGITAL_RESOURCE'
        },
        {
          authorization: this.token
        }
      )
      this.tableData = data[0].fileList || []
      this.tableDataCopy = this.tableData
      this.checkUpdate()
    },
    getImgSrc(rich) {
      var imgList = []
      rich.replace(/ [^>]*src=['"]([^'"]+)[^>]*>/g, (match, capture) => {
        imgList.push(capture)
      })
      return imgList
    },

    getBread(treeData, nodeData) {
      const data = treeData
      const val = nodeData.id
      for (let i = 0; i < data.length; i++) {
        if (data[i] && data[i].id === val) {
          return [data[i].title]
        }
        if (data[i] && data[i].childCatalogue) {
          const d = this.getBread(data[i].childCatalogue, nodeData)
          if (d) {
            return d.concat(data[i].title)
          }
        }
      }
    },
    showAdd(data) {
      const arr = []
      this.bookTree.forEach((item) => {
        arr.push(item.id)
      })
      if (data.parentId !== 0 && arr.indexOf(data.parentId) === -1) {
        return false
      } else {
        return true
      }
    },
    treeAppend(node, data) {
      this.treeData = data

      if (node) {
        this.currChapter = {
          bookId: this.bookId,
          parentId: data.id,
          data: null
        }
      } else {
        this.currChapter = { bookId: this.bookId, parentId: 0, data: null }
      }
      this.editChapterShow = true
    },
    openMessageModal(row) {
      this.messageType = {
        id: row.id
      }
      this.editMessageShow = true
    },
    treeEdit(node, data) {
      this.treeData = data
      this.currChapter = { bookId: this.bookId, parentId: data.parentId, data }
      this.editChapterShow = true
    },
    eidtDone(data) {
      this.editChapterShow = false
      this._getBookCatalogue()
      // if (this.currChapter.data) {
      //   // 编辑
      //   this.treeData.title = data.title
      //   // this._getContent(this.treeData.id)
      // } else {
      //   // 新增
      //   if (!this.currChapter.parentId) {
      //     // 根目录
      //     this.bookTree.push(data)
      //   } else {
      //     const newChild = data
      //     this.treeData.childCatalogue.push(newChild)
      //   }
      // }
    },
    tiptDone() {
      this.editMessageShow = false
    },
    rowClass(row, rowIndex) {
      return 'row-class'
    },
    downloadFile(path, name, type) {
      const notif = Notification({
        title: name + '.' + type,
        dangerouslyUseHTMLString: true,
        message: '',
        duration: 0
      })
      throttle(function () {
        const xhr = new XMLHttpRequest()
        xhr.open('get', path)
        xhr.responseType = 'blob'
        xhr.addEventListener('progress', (e) => {
          const complete = Math.floor((e.loaded / e.total) * 100)
          notif.message = complete + '%'
          if (complete >= 100) {
            notif.close()
          }
        })
        xhr.send()

        xhr.onload = function () {
          if (this.status === 200 || this.status === 304) {
            const blob = new Blob([this.response], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
            })
            saveAs(blob, name + '.' + type)
          }
        }
      }, 2000)
    },
    async getOssSign(mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    formatBytes(bytes, decimals = 2) {
      if (bytes === 0) return '0 Bytes'

      const k = 1024
      const dm = decimals < 0 ? 0 : decimals
      const sizes = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

      const i = Math.floor(Math.log(bytes) / Math.log(k))

      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
    },
    download(row) {
      this.downloadFile(
        row.url,
        row.fileName,
        row.expendType
      )
    }
  }
}
</script>
<style lang="scss">
.el-notification__title {
  font-size: var(--font-size-L) !important;
}
</style>
<style lang="scss" scoped>
.progress {
  width: 300px;
}
.font_size {
  font-size: var(--font-size-S) !important;
}
.classpro-btn {
  padding: 5px 15px;
  font-size: var(--font-size-L) !important;
}
.progress {
  font-size: var(--font-size-L);
}
.row-class {
  font-weight: 800;
  font-size: var(--font-size-L);
  color: #0e0e0e;
  width: 100px;
  letter-spacing: 0.22px;
}
::v-deep .el-dropdown-menu__item {
  font-size: var(--font-size-M) !important;
}
::v-deep .el-button--mini {
  font-size: var(--font-size-M);
  padding: 5px 10px;
}
.detail_main {
  height: 100%;
  padding: 10px;
  width: 100%;
  overflow-y: auto;
  @include scrollBar;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    img {
      width: 13px;
      height: 11px;
      cursor: pointer;
    }

    .back {
      font-size: var(--font-size-L);
      color: #1c1b1a;
      margin: 0 20px 0 8px;
      cursor: pointer;
    }
  }
  .preview-box {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    background: rgba(51, 51, 51, 0.5);
  }
  .preview-content {
    width: 90%;
    height: 90%;
    position: absolute;
    left: 5%;
    top: 5%;
    background-color: #fff;
    border-radius: 5px;

    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
    @include scrollBar;

    .preview-content-head {
      height: 40px;
      display: flex;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: 500;
      }

      .el-icon-close {
        font-size: var(--font-size-XXL);
      }
    }

    .preview-content-content {
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }

  .head-box {
    height: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
    z-index: 8;
    .back {
      width: 20px;
      height: 20px;
      margin-left: 10px;
      object-fit: cover;
      cursor: pointer;
    }
    .title {
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
      margin-left: 10px;
    }
  }
  .status_change {
    width: 100%;
    height: 30px;
    position: absolute;
    left: 0px;
    top: 23px;
    .status_content {
      width: 200px;
      height: 30px;
      margin: 0 auto;
      display: flex;
      position: relative;
      z-index: 9;
      justify-content: space-between;
      .status_title {
        font-size: var(--font-size-L);
        cursor: pointer;
      }
      .selected {
        font-weight: 500;
        position: relative;
        .down_line {
          width: 50px;
          height: 3px;
          background: #2f80ed;
          position: absolute;
          top: 25px;
          left: 0px;
        }
      }
      .deselect {
        font-weight: 400;
      }
    }
  }
  .content_main {
    display: flex;
    margin: 0 auto;
    height: calc(100% - 25px);
    justify-content: space-between;
    border-radius: 6px;

    .left_card {
      width: 241px;
      height: 100%;
      flex-shrink: 0;
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.5);
      .dig-left {
        width: 241px;
        height: 100%;
        border-radius: 6px;
        background: #fff;
        .chapter-title {
          width: 100%;
          height: 40px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 5px;
          .icon1 {
            color: #000;
            font-size: var(--font-size-L);
            display: flex;
            align-items: center;
            img {
              width: 27px;
              height: 27px;
              margin-right: 5px;
            }
          }
          .add-btn {
            color: #2f80ed;
            font-size: var(--font-size-L);
            margin-right: 10px;
            cursor: pointer;
          }
        }

        .chapter-body {
          width: 100%;
          height: calc(100% - 40px);
          overflow: auto;
          @include scrollBar;
          ::v-deep .el-tree-node__content {
            height: 40px;
          }

          ::v-deep .el-button + .el-button {
            margin-left: 5px;
          }

          ::v-deep .el-button {
            font-size: var(--font-size-L);
          }

          ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
            padding: 5px;
          }

          .tree-body {
            width: calc(100% - 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: var(--font-size-L);
            padding-right: 8px;

            .chapter-name {
              flex: 1;
              overflow: hidden;
              @include scrollBar;
            }

            .chapter-option {
              display: flex;
              // align-items: center;
              justify-content: space-between;
              // flex-shrink: 0;
              ::v-deep .el-button--mini {
                padding: 0px;
              }
            }
          }
        }
      }
    }
    .right_card {
      display: flex;
      width: 100%;
      height: 100%;
      padding: 13px 20px 42px 21px;
      flex-direction: column;
      align-items: flex-start;
      flex-shrink: 0;
      gap: 5px;
      border-radius: 6px;
      background: #fff;
      .right_head {
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: space-between;
        .bread {
          font-size: var(--font-size-L);
        }
        .input {
          width: 200px;
          height: 18px;
          padding: 0;
          font-size: var(--font-size-S);
          ::v-deep .el-input__inner {
            height: 18px;
            padding-left: 10px;
          }
          .icon {
            position: absolute;
            top: 5px;
            right: -190px;
          }
        }
      }
      .table_title {
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        position: relative;
        .tips {
          font-size: var(--font-size-S);
          position: absolute;
          right: 0;
          top: -20px;
        }
        .cheack_box {
          font-size: var(--font-size-S);
          margin-left: 14px;
        }
        ::v-deep .el-checkbox__inner::after {
          width: 4px;
          height: 8px;
          top: 0px;
          border: 2px solid #ffffff;
          border-left: 0;
          border-top: 0;
        }
      }
      .table {
        margin-top: 5px;
        .img {
          display: inline-block;
          vertical-align: middle;
          // margin-bottom: 3px;
          width: 30px;
        }
        ::v-deep .el-table__body-wrapper .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        // ::v-deep.el-table__body-wrapper .cell:hover {
        //   overflow: visible;
        //   white-space: normal;
        // }
        font-size: var(--font-size-M);
        .drop_text {
          font-size: var(--font-size-S);
        }
        ::v-deep .el-button--text {
          font-size: var(--font-size-M);
        }
        ::v-deep .el-checkbox__inner::after {
          width: 4px;
          height: 8px;
          top: 0px;
          border: 2px solid #ffffff;
          border-left: 0;
          border-top: 0;
        }
        ::v-deep .el-table_1_column_1 {
          width: 100px;
        }
        ::v-deep
          .el-table__header
          .el-table-column--selection
          .cell
          .el-checkbox:after {
          content: '全选';
          font-size: var(--font-size-L);
          margin-left: 10px;
        }
        ::v-deep
          .el-table__header
          .el-table-column--selection
          .cell
          .el-checkbox {
          margin-left: 3px;
        }
        ::v-deep .el-table__body-wrapper {
          @include scrollBar;
        }
      }
    }
  }
}
</style>
