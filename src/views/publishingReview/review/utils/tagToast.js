import tinymce from 'tinymce/tinymce'
import { submitDigitalBug } from '@/api/publishing'
import { addTagModal, closeTag, setData } from '../components/addTag'
import router from '../../../../router'
import { EventBus } from './event'
import { Message } from 'element-ui'
import store from '@/store'
export function addTagToast(editor, url) {
  // 添加悬浮 上下文工具栏
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('addBug', {
    text: '添加备注',
    onAction: () => {
      const selectContent = tinymce.activeEditor.selection.getContent()
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      if (!selectContent) {
        return
      }
      if (selectContent.length > 10000) {
        Message.warning('请选择不超过10000个字符的文字')
        return
      }
      document.getElementsByClassName('tox-tinymce-aux')[0].style.display = 'none'
      setData({
        keyword: selectContent,
        content: ''
      })
      addTagModal({
        onSubmit(data) {
          tinymce.activeEditor.selection.moveToBookmark(bookmark)
          tinymce.activeEditor.getBody().setAttribute('contenteditable', true)
          const selectedContent = editor.selection.getContent()
          const uniqueId = 'custom-' + Date.now() // 创建一个基于时间戳的唯一ID
          if (selectedContent) {
            if (!data.keyword.includes('draggable-div')) {
              switch (store.state.user.reviewRole) {
                case 'PROOFREAD':
                  editor.formatter.register('customStyle', {
                    inline: 'span',
                    attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: red;' },
                    exact: true
                  })
                  editor.formatter.apply('customStyle')
                  break
                case 'REVIEW_1':
                  editor.formatter.register('customStyle', {
                    inline: 'span',
                    attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: #00C7BE;' },
                    exact: true
                  })
                  editor.formatter.apply('customStyle')
                  break
                case 'REVIEW_2':
                  editor.formatter.register('customStyle', {
                    inline: 'span',
                    attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: #FF9500;' },
                    exact: true
                  })
                  editor.formatter.apply('customStyle')
                  break
                case 'REVIEW_3':
                  editor.formatter.register('customStyle', {
                    inline: 'span',
                    attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: #9B51E0;' },
                    exact: true
                  })
                  editor.formatter.apply('customStyle')
                  break
                default:
                  editor.formatter.register('customStyle', {
                    inline: 'span',
                    attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: red;' },
                    exact: true
                  })
                  editor.formatter.apply('customStyle')
                  break
              }
            } else {
              editor.formatter.register('customStyle', {
                inline: 'span',
                attributes: { 'data-id': uniqueId, class: 'set_review' },
                exact: true
              })
              editor.formatter.apply('customStyle')
            }
            tinymce.activeEditor.getBody().setAttribute('contenteditable', false)
            submitDigitalBug({
              newDigitalContent: tinymce.activeEditor.getContent(),
              postion: JSON.stringify({
                id: uniqueId,
                text: data.keyword
              }),
              bugDescription: data.content,
              catalogueId: Number(localStorage.getItem('review_selectTreeId')),
              digtalBookId: Number(router.currentRoute.query.bookId),
              digtalBookReviewId: Number(router.currentRoute.query.id)
            }).then(res => {
              EventBus.$emit('reLoad', res)
            })
            closeTag()
            document.getElementsByClassName('tox-tinymce-aux')[0].style.display = 'block'
          }
          // 取消选中状态
        }
      })
    }
  })
  return editor.ui.registry.addContextToolbar('editcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      const selection = tinymce.activeEditor.selection
      var s1 = selection.getContent()
      console.log(s1)
      return s1.length > 0
    },
    items: 'addBug', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
  })
}
