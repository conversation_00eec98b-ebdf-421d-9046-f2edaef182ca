import tinymce from 'tinymce/tinymce'
import { submitDigitalBug } from '@/api/publishing'
import { addTagModal, closeTag, setData } from '../components/addTag'
import router from '../../../../router'
import { EventBus } from './event'
import store from '@/store'
export function addTag(editor, url) {
  return editor.ui.registry.addButton('addTag', {
    text: '标注',
    tooltip: '标注',
    onAction: function () {
      const selectContent = tinymce.activeEditor.selection.getContent()
      if (!selectContent) {
        return
      }
      setData({
        keyword: selectContent,
        content: ''
      })
      addTagModal({
        onSubmit(data) {
          tinymce.activeEditor.getBody().setAttribute('contenteditable', true)
          const selectedContent = editor.selection.getContent()
          const uniqueId = 'custom-' + Date.now() // 创建一个基于时间戳的唯一ID
          if (selectedContent) {
            // 使用formatter应用样式和ID
            console.log(store.getters.reviewRole)
            switch (store.getters.reviewRole) {
              case 'PROOFREAD':
                editor.formatter.register('customStyle', {
                  inline: 'span',
                  attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: #2F80ED;' },
                  exact: true
                })
                editor.formatter.apply('customStyle')
                break
              case 'REVIEW_1':
                editor.formatter.register('customStyle', {
                  inline: 'span',
                  attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: #00C7BE;' },
                  exact: true
                })
                editor.formatter.apply('customStyle')
                break
              case 'REVIEW_2':
                editor.formatter.register('customStyle', {
                  inline: 'span',
                  attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: #FFD700;' },
                  exact: true
                })
                editor.formatter.apply('customStyle')
                break
              case 'REVIEW_3':
                editor.formatter.register('customStyle', {
                  inline: 'span',
                  attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: #9B51E0;' },
                  exact: true
                })
                editor.formatter.apply('customStyle')
                break
              default:
                editor.formatter.register('customStyle', {
                  inline: 'span',
                  attributes: { 'data-id': uniqueId, class: 'set_review', style: 'color: red;' },
                  exact: true
                })
                editor.formatter.apply('customStyle')
                break
            }
            tinymce.activeEditor.getBody().setAttribute('contenteditable', false)
            submitDigitalBug({
              newDigitalContent: tinymce.activeEditor.getBody().innerHTML,
              postion: JSON.stringify({
                id: uniqueId,
                text: data.keyword
              }),
              bugDescription: data.content,
              catalogueId: Number(localStorage.getItem('review_selectTreeId')),
              digtalBookId: Number(router.currentRoute.query.bookId),
              digtalBookReviewId: Number(router.currentRoute.query.id)
            }).then(res => {
              EventBus.$emit('reLoad', res)
            })
            closeTag()
          }
        }
      })
    }
  })
}
