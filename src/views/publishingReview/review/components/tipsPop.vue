<template>
  <transition name="fade">
    <div
      v-show="showMenu"
      :style="{ top: position.top + 'px', left: position.left + 'px' }"
      class="dig-noteContent"
    >
      <p><span style="font-weight:700">{{ info.keyword }}：</span>{{ info.content }}</p>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'NoteMenu',
  props: {
    position: {
      type: Object,
      default: null
    },
    info: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      showMenu: false

    }
  },
  methods: {
    show () {
      this.showMenu = true
    },
    close () {
      this.showMenu = false
    }

  }

}
</script>

<style scoped lang='scss'>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.25s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}

.dig-noteContent {
  padding: 12px;
  position: fixed;
  z-index: 1000;
  flex-direction: column;
  align-items: flex-end;
  // gap: 10px;
  border-radius: 10px;
  border: 1px solid #f2f2f2;
  background: #fff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  width: 327px;
  height: 152px;
  overflow-x: hidden;
  overflow-y: scroll;
  @include scrollBar;
}
</style>
