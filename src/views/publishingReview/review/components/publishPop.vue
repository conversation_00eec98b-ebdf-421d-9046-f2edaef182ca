<template>
  <NormalDialog
    v-if="dialogShow"
    width="1200px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="main">
        <div class="img_content">
          <img :src="info.cover" alt="" />
          <!-- <div style="width: 100%;"><el-button style="margin-left: 100px;" type="text" @click="showImg()">查看出版证书</el-button></div> -->
        </div>
        <el-form
          label-width="11vw"
          size="mini"
        >
          <el-form-item label="书名：">
            {{ info.title }}
          </el-form-item>
          <el-form-item label="主编：">
            <p class="author">
              {{ info.author?info.author:'暂无' }}
            </p>
          </el-form-item>
          <el-form-item label="责任编辑：">
            <el-input v-model="info.publishEditor" placeholder="请输入责任编辑" style="width: 300px;"/>
          </el-form-item>
          <el-form-item label="书号（ISBN）：">
            <el-input v-model="info.isbn" placeholder="请输入ISBN" style="width: 300px;"/>
          </el-form-item>
          <el-form-item label="版次：">
            <el-input v-model="info.edition" placeholder="请输入版次" style="width: 300px;"/>
          </el-form-item>
          <el-form-item label="简介：">
            {{ info.intro?info.intro:'暂无' }}
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="subMit()">确定</el-button>
    </template>
    <NormalDialog
      v-if="dialogShow1"
      width="1200px"
      title="出版信息"
      :dialog-visible="dialogShow1"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog="dialogShow1=false"
    >
      <div style="width: 40%; margin: 0 auto;">
        <img style="width: 100%;" :src="popImg" alt="" />
      </div>
    </NormalDialog>
  </NormalDialog>
</template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { grantPublish, changeDigitalBookReviewStatus } from '@/api/publishing'
export default {
  components: { NormalDialog },
  props: {
    info: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      title: '出版信息确认',
      dialogShow: false,
      dialogShow1: false,
      popImg: ''
    }
  },
  mounted () {
  },
  methods: {
    showImg() {
      if (!this.info.publishingCertificate) {
        this.$message.warning('暂无出版证书')
        return
      }
      this.popImg = this.info.publishingCertificate
      this.dialogShow1 = true
    },
    subMit() {
      this.$confirm(`是否要出版`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeDigitalBookReviewStatus({ digitalBookReviewId: this.$route.query.id, reviewStatus: 'PASS' }).then(res => {
          if (res.code === 200) {
            grantPublish({ digitalBookId: Number(this.$route.query.bookId), digtalBookReviewId: Number(this.$route.query.id), isbn: this.info.isbn, publishEditor: this.info.publishEditor, edition: this.info.edition }).then(res1 => {
              if (res1.code === 200) {
                this.$message.success('审核通过')
                this.$router.go(-1)
              }
            })
          }
        })
      })
    },
    close () {
      this.dialogShow = false
    },
    show () {
      this.dialogShow = true
    }
  }
}
</script>
  <style lang="scss" scoped>
  .author{
    width: 230px;
    padding: 0 !important;
    margin: 0;
  }
  ::v-deep .el-form-item{
     margin-bottom: 25px !important;
  }
  ::v-deep  .el-form-item__content{
      font-size: 12px !important;

    }
  ::v-deep .el-button {
      font-size: 10px;
      padding: 5px;
      padding-left: 10px;
      padding-right: 10px;
      // color:red
    }
.main{
    width: 100%;
    height: 400px;
    height: auo;
    margin: 0 auto;
    position: relative;
    .img_content{
        position: absolute;
        width: 242px;
        height: 391px;
        right: 0px;
        z-index: 10;
        img{
            width: 180px;
            height: 240px;
            object-fit: cover;
        }
    }
    // p{
    //     font-size: 16px;
    //     font-weight: 700;
    //     color:#000000;
    //     display: flex;
    //     span{
    //         font-weight: 400;
    //     }
    // }
}
  </style>
