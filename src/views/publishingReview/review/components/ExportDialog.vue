<template>
  <NormalDialog
    v-if="dialogVisible"
    width="100%"
    title="导出选项"
    :dialog-visible="dialogVisible"
    :is-center="true"
    :show-close="false"
    @closeDialog="handleClose"
  >
    <div class="export-container">
      <div ref="content" class="rich-text-container">
        <div v-if="loading && !isExporting && !selectedContent" class="loading-container">
          <div class="loading-content">
            <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
            <p style="margin-top: 15px; color: #666;">
              {{ loadingText || '正在加载内容...' }}
            </p>
            <div v-if="totalRequests > 0" class="progress-info">
              <el-progress
                :percentage="loadingProgress"
                :stroke-width="6"
                :show-text="true"
              />
              <!-- <p style="margin-top: 10px; font-size: 12px; color: #999;">
                已完成 {{ completedRequests }}/{{ totalRequests }} 个请求
              </p> -->
            </div>
          </div>
        </div>

        <div v-else-if="selectedContent===''" class="emty">
          <Empty description="暂无数据" />
        </div>

        <div
          v-else
          ref="richText"
          class="rich-text-content"
          v-html="selectedContent"
        ></div>

        <div v-if="isExporting" class="export-overlay">
          <div class="export-loading">
            <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
            <p style="margin-top: 15px; color: #666;">正在加载...</p>
          </div>
        </div>
      </div>

      <div class="export-footer">
        <div class="export-actions">
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
          >
            <div slot="content" style="width:30vw;">
              <p>在macOS系统上，打印时中文文字丢失通常是由于系统字体设置问题导致的‌。解决方法：</p>
              <p>打开macOS系统的访达，搜索“字体册”，点击进入字体册，搜索“苹方-简”；右键点击该字体并选择“激活”（会提示下载）；</p>
              <p>关闭软件平台/浏览器后再打开即可。</p>
            </div>
            <span class="tips">文字丢失<i class="el-icon-question"></i></span>
          </el-tooltip>

          <div class="action-buttons">
            <el-button
              class="export-btn cancel-btn"
              type=""
              @click="close"
            >
              取消
            </el-button>
            <el-button
              class="export-btn"
              type="primary"
              :disabled="loading || selectedContent === '' || isExporting"
              @click="printContent"
            >
              导出
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import Empty from '@/components/classPro/Empty/index.vue'
import { getContent, getBookCatalogueByVersion } from '@/api/digital-api.js'
import { getDigitalBugList, getDigitalBugCommentList } from '@/api/publishing.js'

export default {
  name: 'ExportDialog',
  components: { NormalDialog, Empty },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bookId: {
      type: [String, Number],
      default: ''
    },
    digitalBookReviewId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      selectedContent: '',
      loading: false,
      loadingText: '正在加载目录...',
      isExporting: false,

      catalogueData: [],
      contentData: {},
      reviewData: {},
      allChapterIds: [],

      totalChapters: 0,
      loadedChapters: 0,
      totalRequests: 0,
      completedRequests: 0,

      errors: [],

      batchSize: 5,
      maxRetries: 3,

      exportFormat: 'pdf',
      includeReviews: true,
      includeImages: true
    }
  },
  computed: {
    dialogVisible() {
      return this.visible
    },
    loadingProgress() {
      if (this.totalRequests === 0) return 0
      return Math.round((this.completedRequests / this.totalRequests) * 100)
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.initializeData()
        })
      }
    }
  },
  methods: {
    async initializeData() {

      this.resetData()

      setTimeout(() => {
        this.loadCompleteBookContent()
      }, 100)
    },

    resetData() {
      this.selectedContent = ''
      this.loading = false
      this.isExporting = false
      this.loadingText = '正在加载目录...'
      this.catalogueData = []
      this.contentData = {}
      this.reviewData = {}
      this.totalChapters = 0
      this.loadedChapters = 0
      this.totalRequests = 0
      this.completedRequests = 0
      this.errors = []
    },

    async loadCompleteBookContent() {
      if (!this.bookId) {
        this.$message.error('缺少书籍ID，无法加载内容')
        return
      }

      try {
        this.loading = true
        this.loadingText = '正在加载目录结构...'

        await this.loadCatalogueData()

        await this.loadAllChapterData()

        await this.assembleCompleteContent()
      } catch (error) {
        console.error('加载完整内容失败:', error)
        this.$message.error('加载内容失败：' + error.message)
        this.selectedContent = this.generateErrorContent(error)
      } finally {
        this.loading = false
        this.loadingText = '加载完成'
      }
    },

    async loadCatalogueData() {
      const { data } = await getBookCatalogueByVersion({
        bookId: this.bookId,
        type: 'CHAPTER',
        approvedOnly: false
      })

      if (data && data.length) {
        this.catalogueData = data
        this.allChapterIds = this.collectAllChapterIds(data)
        this.totalChapters = this.allChapterIds.length
        this.totalRequests = this.totalChapters * (this.includeReviews ? 2 : 1)
        this.loadingText = `发现 ${this.totalChapters} 个章节，准备加载内容...`
      } else {
        throw new Error('未找到目录数据')
      }
    },

    collectAllChapterIds(catalogues, currentDepth = 0) {
      let chapterIds = []
      catalogues.forEach(item => {
        chapterIds.push({
          id: item.id,
          title: item.title,
          parentTitle: item.parentTitle || '',
          depth: currentDepth,
          hasChildren: item.childCatalogue && item.childCatalogue.length > 0
        })

        if (item.childCatalogue && item.childCatalogue.length > 0) {
          chapterIds = chapterIds.concat(this.collectAllChapterIds(item.childCatalogue, currentDepth + 1))
        }
      })
      return chapterIds
    },
    async loadAllChapterData() {
      this.loadingText = '正在批量加载章节内容...'

      const batches = this.createBatches(this.allChapterIds, this.batchSize)

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i]
        // this.loadingText = `正在加载第 ${i + 1}/${batches.length} 批章节数据...`
        this.loadingText = `正在加载章节数据...`

        await this.processBatch(batch)

        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200))
        }
      }
    },

    createBatches(items, batchSize) {
      const batches = []
      for (let i = 0; i < items.length; i += batchSize) {
        batches.push(items.slice(i, i + batchSize))
      }
      return batches
    },

    async processBatch(chapterBatch) {
      const promises = []

      chapterBatch.forEach(chapter => {
        promises.push(
          this.loadChapterContent(chapter.id)
            .then(content => {
              this.contentData[chapter.id] = content
              this.completedRequests++
              this.loadedChapters++
            })
            .catch(error => {
              console.error(`❌ 加载章节 ${chapter.id} 内容失败:`, error)
              this.contentData[chapter.id] = null
              this.completedRequests++
            })
        )

        if (this.includeReviews) {
          promises.push(
            this.loadChapterReviews(chapter.id)
              .then(reviews => {
                this.reviewData[chapter.id] = reviews
                this.completedRequests++
              })
              .catch(error => {
                console.error(`❌ 加载章节 ${chapter.id} 审核意见失败:`, error)
                this.errors.push(`章节 "${chapter.title}" 审核意见加载失败: ${error.message}`)
                this.reviewData[chapter.id] = []
                this.completedRequests++
              })
          )
        } else {
        }
      })

      await Promise.all(promises)
    },

    async loadChapterContent(catalogueId) {
      try {
        const { data } = await getContent({ catalogueId })
        if (data && data.length > 0 && data[0].data) {
          const content = data[0].data.trim()
          if (content && content !== '<p></p>' && content !== '<p><br></p>' && content !== '') {
            return content
          }
        }
        return null
      } catch (error) {
        console.error(`加载章节 ${catalogueId} 内容失败:`, error)
        return null
      }
    },

    async loadChapterReviews(catalogueId) {

      const response = await getDigitalBugList({
        digitalBookId: Number(this.bookId),
        catalogueId: catalogueId
      })

      const reviews = response.data || []

      if (reviews.length > 0) {
        for (const item of reviews) {
          if (item.postion) {
            try {
              const position = JSON.parse(item.postion)
              item.dataId = position.id
              item.text = position.text
            } catch (e) {
              console.warn('解析position数据失败:', e)
            }
          }

          try {
            const commentResponse = await getDigitalBugCommentList({ digitalBugId: item.id })
            item.commentList = commentResponse.data || []
          } catch (error) {
            console.warn(`❌ 加载审核意见 ${item.id} 留言失败:`, error)
            item.commentList = []
          }
        }
      }

      return reviews
    },

    async assembleCompleteContent() {
      this.loadingText = '正在组装完整文档...'

      let completeContent = ''

      if (this.catalogueData && this.catalogueData.length > 0) {
        completeContent += `<h2 style="text-align: center; margin: 20px 0;">目录</h2>`
        completeContent += this.generateSimpleTableOfContents()
        completeContent += `<div style="page-break-after: always;"></div>`
      }

      completeContent += this.generateAllChapterContents()

      this.selectedContent = completeContent

      await this.$nextTick()
      await window.MathJax.typesetPromise()
    },

    generateDocumentHeader() {
      const now = new Date()
      return `
        <div style="text-align: center; margin-bottom: 40px; padding: 20px; border-bottom: 2px solid #409EFF;">
          <h1 style="color: #409EFF; margin-bottom: 10px;">数字教材完整导出文档</h1>
          <p style="color: #666; font-size: 14px;">导出时间: ${now.toLocaleString()}</p>
          <p style="color: #666; font-size: 14px;">书籍ID: ${this.bookId}</p>
          <p style="color: #666; font-size: 14px;">总章节数: ${this.totalChapters}</p>
          <p style="color: #666; font-size: 14px;">包含审核意见: ${this.includeReviews ? '是' : '否'}</p>
        </div>
      `
    },

    generateTableOfContents() {
      let tocContent = `
        <div style="margin: 20px 0; page-break-after: always;">
          <div style="color: #000; font-size: 16px; font-weight: bold; margin-bottom: 20px;">目录</div>
          <div style="font-family: inherit;">
      `

      tocContent += this.generateTOCStructure(this.catalogueData, 0)

      tocContent += `
          </div>
        </div>
      `

      return tocContent
    },

    generateTOCStructure(catalogues, level = 0) {
      let tocContent = ''

      catalogues.forEach((item) => {
        const indentPx = level * 18
        tocContent += `
          <div style="
            height: 40px;
            display: flex;
            align-items: center;
            padding-left: ${indentPx}px;
            padding-right: 8px;
            font-size: 14px;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
          ">
            <div style="
              flex: 1;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            ">
              ${item.title}
            </div>
          </div>
        `

        if (item.childCatalogue && item.childCatalogue.length > 0) {
          tocContent += this.generateTOCStructure(item.childCatalogue, level + 1)
        }
      })

      return tocContent
    },

    generateAllChapterContents() {
      const allChapters = this.collectAllChapterIds(this.catalogueData)

      const results = allChapters.map(chapter => {
        const hasContent = this.contentData[chapter.id] && this.contentData[chapter.id] !== null
        const hasReviews = this.includeReviews && this.reviewData[chapter.id] && this.reviewData[chapter.id].length > 0

        let content = ''
        if (hasContent) {
          content = this.contentData[chapter.id]
        } else {
          content = '<div style="height: 20px; margin: 10px 0;"></div>'
        }

        const reviewSection = (this.includeReviews && this.reviewData[chapter.id] && this.reviewData[chapter.id].length > 0)
          ? this.generateReviewSection(this.reviewData[chapter.id])
          : ''

        const titleLevel = Math.min(chapter.depth + 1, 6) || 1
        const fontSize = Math.max(14, 24 - chapter.depth * 3)
        const marginTop = Math.max(10, 40 - chapter.depth * 8)
        const marginBottom = Math.max(8, 20 - chapter.depth * 3)
        const paddingLeft = chapter.depth * 15
        const titleStyle = `
          font-size: ${fontSize}px;
          margin: ${marginTop}px 0 ${marginBottom}px 0;
          padding-left: ${paddingLeft}px;
          font-weight: ${chapter.depth === 0 ? 'bold' : chapter.depth === 1 ? '600' : 'normal'};
          color: ${chapter.depth === 0 ? '#333' : chapter.depth === 1 ? '#555' : '#666'};
        `

        return {
          title: chapter.title,
          content: content + reviewSection,
          titleLevel: titleLevel,
          titleStyle: titleStyle
        }
      })

      const allContent = results.map(({ title, content, titleLevel, titleStyle }) =>
        `<h${titleLevel} style="width:100%;text-align: center; color: #333; ${titleStyle}">${title}</h${titleLevel}>${content}`
      ).join('')

      return allContent
    },

    generateSimpleTableOfContents() {
      const allChapters = this.collectAllChapterIds(this.catalogueData)

      return allChapters.map((chapter, index) => {
        const indentPx = chapter.depth * 20
        const fontSize = Math.max(12, 16 - chapter.depth * 1)
        const fontWeight = chapter.depth === 0 ? 'bold' : 'normal'
        const marginTop = chapter.depth === 0 ? '10px' : '3px'

        return `<p style="
          margin: ${marginTop} 0 3px 0;
          font-size: ${fontSize}px;
          font-weight: ${fontWeight};
          padding-left: ${indentPx}px;
          line-height: 1.4;
        ">${chapter.title}</p>`
      }).join('')
    },

    generateChapterContent(chapter, chapterNumber, titleLevel) {
      let chapterContent = ''

      chapterContent += `<h${titleLevel} style="color: #333; margin: 30px 0 20px 0; page-break-before: auto;">${chapterNumber} ${chapter.title}</h${titleLevel}>`

      const content = this.contentData[chapter.id] || '<p style="color: #999;">暂无内容</p>'
      chapterContent += `<div style="margin: 20px 0; line-height: 1.6;">${content}</div>`

      if (this.includeReviews && this.reviewData[chapter.id] && this.reviewData[chapter.id].length > 0) {
        chapterContent += this.generateReviewSection(this.reviewData[chapter.id])
      }

      chapterContent += '<hr style="margin: 40px 0; border: none; border-top: 1px solid #eee;">'

      return chapterContent
    },

    generateReviewSection(reviews) {
      let reviewContent = `
        <div style="
          margin: 40px 0 20px 0;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 20px;
        ">
          <h4 style="color: #409EFF; margin-bottom: 20px; font-size: 16px; font-weight: 600;">审核意见</h4>
      `

      reviews.forEach((review, index) => {
        const statusColor = this.getStatusColor(review.bugStatus)
        const statusTextColor = this.getStatusTextColor(review.bugStatus)
        const statusText = this.getStatusText(review.bugStatus)
        const reviewerName = review.user ? review.user.displayName || review.user.name || '未知用户' : '未知用户'
        const userRole = review.userRole || 'REVIEW'
        const roleText = this.formatAuthorRole(userRole)
        const roleColor = this.getRoleColor(userRole)

        reviewContent += `
          <div style="
            margin: 12px 0;
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          ">
            <div style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
            ">
              <div style="display: flex; align-items: center; gap: 5px;">
                <div style="
                  width: 20px;
                  height: 20px;
                  text-align: center;
                  border-radius: 10px;
                  font-size: 9px;
                  line-height: 20px;
                  color: white;
                  background: ${roleColor};
                ">${roleText}</div>
                <span style="color: ${roleColor} !important; font-size: 12px; font-weight: 500;">${reviewerName}</span>
                <span style="margin-left: 10px; font-size: 12px; color: #666;">${review.createdAt || review.createTime || '未知时间'}</span>
              </div>
              <div style="display: flex; align-items: center;">
                <span style="
                  background: ${statusColor};
                  color: ${statusTextColor};
                  padding: 0 8px;
                  height: 24px;
                  line-height: 22px;
                  font-size: 12px;
                  border-radius: 4px;
                  display: inline-block;
                  text-align: center;
                  border: 1px solid ${statusColor};
                  box-sizing: border-box;
                ">${statusText}</span>
              </div>
            </div>

            ${review.text ? `
            <div style="
              background: #f8fafe;
              border: 1px solid #e8f0fe;
              border-left: 4px solid #1976d2;
              border-radius: 4px;
              padding: 12px 16px;
              margin: 8px 0;
              font-size: 10px;
              color: #666;
              overflow: hidden;
              position: relative;
            ">
              引用：<div style="width: 100%; text-overflow: ellipsis; margin-left: 5px; display: inline-block;">${review.text}</div>
            </div>
            ` : ''}

            <div style="
              margin-top: 10px;
              padding: 8px 0;
              font-size: 13px;
              line-height: 1.5;
              color: #333;
            ">
              ${review.bugDescription || ' '}
            </div>

            ${review.commentList && review.commentList.length > 0 ? this.generateCommentSection(review.commentList) : ''}
          </div>
        `
      })

      reviewContent += '</div>'
      return reviewContent
    },

    getStatusColor(status) {
      switch (status) {
        case 'OPEN': return '#ecf5ff'
        case 'FIXED': return '#fef0f0'
        case 'CLOSED': return '#f0f9ff'
        default: return '#ecf5ff'
      }
    },

    getStatusTextColor(status) {
      switch (status) {
        case 'OPEN': return '#409eff'
        case 'FIXED': return '#f56c6c'
        case 'CLOSED': return '#67c23a'
        default: return '#409eff'
      }
    },

    formatType(type) {
      if (type === 'OPEN') {
        return 'info'
      }
      if (type === 'FIXED') {
        return 'danger'
      }
      return ''
    },

    getStatusText(status) {
      switch (status) {
        case 'OPEN': return '待修改'
        case 'FIXED': return '待复核'
        case 'CLOSED': return '已解决'
        default: return '未知状态'
      }
    },

    formatAuthorRole(role) {
      if (role === 'REVIEW_1') {
        return '一审'
      } else if (role === 'REVIEW_2') {
        return '二审'
      } else if (role === 'REVIEW_3') {
        return '三审'
      } else if (role === 'PROOFREAD') {
        return '校对'
      } else if (role === '作者') {
        return '作者'
      } else {
        return role || '审核'
      }
    },

    getRoleColor(role) {
      switch (role) {
        case 'REVIEW_1': return '#00C7BE'
        case 'REVIEW_2': return '#FF9500'
        case 'REVIEW_3': return '#9B51E0'
        case 'PROOFREAD': return '#2F80ED'
        case '作者': return '#E6A23C'
        case 'REVIEW': return '#2F80ED'
        default: return '#2F80ED'
      }
    },

    generateCommentSection(comments) {
      if (!comments || comments.length === 0) return ''

      let commentContent = `
        <div style="
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          margin-top: 8px;
          overflow: hidden;
        ">
          <div style="
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 11px;
            color: #666;
            font-weight: 500;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
          ">
            留言   共有${comments.length}条留言
          </div>
          <div style="
            border: 1px solid #e9ecef;
            border-top: none;
            border-radius: 0 0 4px 4px;
            background: #fff;
            padding: 4px 8px;
          ">
      `

      comments.forEach(comment => {
        const commentUserRole = comment.userRole || '作者'
        const commentRoleText = this.formatAuthorRole(commentUserRole)
        const commentRoleColor = commentUserRole === '作者' ? '#E6A23C' : this.getRoleColor(commentUserRole)

        commentContent += `
          <div style="
            background: #fafafa;
            border: 1px solid #f0f0f0;
            border-radius: 3px;
            padding: 6px 8px;
            margin: 3px 0;
          ">
            <div style="
              display: flex;
              align-items: center;
              font-size: 7px;
              margin-bottom: 1px;
              gap: 4px;
            ">
              <span style="
                background: ${commentRoleColor};
                color: white;
                padding: 1px 4px;
                border-radius: 2px;
                font-size: 6px;
                font-weight: 500;
              ">${commentRoleText}</span>
              <span style="color: #999; font-size: 7px;">${comment.createdAt || comment.createTime || '未知时间'}</span>
            </div>
            <div style="
              font-size: 9px;
              line-height: 1.1;
              color: #777;
              padding: 6px 8px;
            ">
              ${comment.content || '  '}
            </div>
          </div>
        `
      })

      commentContent += `
          </div>
        </div>
      `
      return commentContent
    },

    close() {
      this.selectedContent = ''
      this.handleClose()
    },

    generateErrorReport() {
      if (this.errors.length === 0) return ''

      let errorContent = `
        <div style="background: #fef0f0; border: 1px solid #fbc4c4; padding: 20px; border-radius: 8px; margin: 30px 0;">
          <h3 style="color: #f56c6c; margin-bottom: 15px;">⚠️ 加载过程中的错误</h3>
          <ul style="margin: 0; padding-left: 20px;">
      `

      this.errors.forEach(error => {
        errorContent += `<li style="color: #f56c6c; margin: 5px 0;">${error}</li>`
      })

      errorContent += `
          </ul>
          <p style="color: #999; font-size: 12px; margin-top: 15px;">
            注意：部分内容可能因网络或权限问题未能正常加载，请检查网络连接或联系管理员。
          </p>
        </div>
      `

      return errorContent
    },

    generateErrorContent(error) {
      return `
        <div style="text-align: center; padding: 50px; color: #f56c6c;">
          <h2>❌ 内容加载失败</h2>
          <p style="margin: 20px 0; font-size: 16px;">${error.message}</p>
          <p style="color: #999; font-size: 14px;">请检查网络连接或联系管理员</p>
        </div>
      `
    },

    async printContent() {
      if (!this.selectedContent) {
        this.$message.warning('暂无内容可导出')
        return
      }

      if (this.loading) {
        this.$message.warning('内容正在加载中，请稍候...')
        return
      }

      this.loading = true
      this.isExporting = true

      try {

        await this.$nextTick()
        if (window.MathJax && window.MathJax.typesetPromise) {
          await window.MathJax.typesetPromise()
        }

        await new Promise(resolve => setTimeout(resolve, 500))

        const richTextElement = this.$refs.richText
        let exportContent = richTextElement.innerHTML

        exportContent = exportContent.replace(/font-family:[^;"]*;?/gi, '')
        exportContent = exportContent.replace(/style="[^"]*font-family:[^;"]*;?[^"]*"/gi, '')

        this.$emit('export', {
          content: exportContent,
          format: this.exportFormat,
          totalChapters: this.totalChapters,
          includeReviews: this.includeReviews,
          errors: this.errors,
          bookId: this.bookId
        })

        if (this.exportFormat === 'pdf') {
          setTimeout(() => {
            this.loading = false
            this.isExporting = false
          }, 1500)
        } else {
          await new Promise(resolve => setTimeout(resolve, 1000))
          this.$message.success('Word导出成功！')
          setTimeout(() => {
            this.handleClose()
          }, 1500)
        }
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
        this.loading = false
        this.isExporting = false
      } finally {
        if (this.exportFormat !== 'pdf') {
          this.loading = false
          this.isExporting = false
        }
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    handleCancel() {
      this.handleClose()
    },
    async handleConfirm() {
      try {
        this.exporting = true

        this.$emit('export', {})

        await new Promise(resolve => setTimeout(resolve, 500))

        this.$message.success('导出成功！')
        this.handleClose()
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      } finally {
        this.exporting = false
      }
    },
    open() {
      this.$emit('update:visible', true)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .dialog-box {
  padding: 10px 10px 0 10px !important;
}

.export-container {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
}

.rich-text-container {
  width: 793.667px;
  flex: 1;
  padding: 0 10px 70px 10px;
  position: relative;
  overflow: hidden;
}

.loading-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  max-width: 400px;

  .progress-info {
    margin-top: 20px;

    .el-progress {
      margin-bottom: 10px;
    }
  }
}

.tips {
  cursor: pointer;
  font-size: 10px;
  margin-left: 10px;
  margin-top: 0;
}

.emty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rich-text-content {
  height: 100%;
  overflow: auto;
  @include scrollBar;

  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
  color: #222f3e;
  line-height: 1.4;

  ::v-deep * {
    max-width: 100%;
  }

  ::v-deep pre > code {
    display: block;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
  }

  ::v-deep p,
  ::v-deep div,
  ::v-deep span,
  ::v-deep h1,
  ::v-deep h2,
  ::v-deep h3,
  ::v-deep h4,
  ::v-deep h5,
  ::v-deep h6 {
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
    color: #222f3e !important;
  }
}

.export-footer {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 793.667px;
  background: #ffffff;
  padding: 15px 20px;
  z-index: 10;
  border-radius: 0 0 15px 15px;
  box-sizing: border-box;
}

.format-selector {
  display: flex;
  align-items: center;
  margin: 10px 0;

  .format-label {
    font-size: 13px;
    color: #666;
    margin-right: 10px;
    white-space: nowrap;
  }

  ::v-deep .el-radio-group {
    .el-radio {
      margin-right: 15px;

      .el-radio__label {
        font-size: 12px;
        color: #333;
      }
    }
  }
}

.export-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tips {
  cursor: pointer;
  font-size: 12px;
  color: #666;

  i {
    margin-left: 5px;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.export-btn {
  padding: 6px 16px;
  font-size: 13px;

  &.el-button--primary {
    background: #409eff;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(168, 178, 185, 0.2);

    &:hover {
      background: #66b1ff;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(168, 178, 185, 0.2);
    }

    &:disabled {
      background: #d5dbdb;
      box-shadow: none;
      transform: none;
      opacity: 0.6;
    }
  }

}

.cancel-btn.el-button {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  color: #6c757d !important;
  border-radius: 8px !important;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

  &:hover,
  &:hover:focus,
  &.hover {
    background: #e9ecef !important;
    border: 1px solid #dee2e6 !important;
    border-color: #dee2e6 !important;
    color: #495057 !important;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15) !important;
  }

  &:active,
  &:active:focus,
  &.active {
    background: #e9ecef !important;
    border: 1px solid #dee2e6 !important;
    border-color: #dee2e6 !important;
    color: #495057 !important;
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  &:focus {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-color: #e9ecef !important;
    color: #6c757d !important;
    outline: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }
}

.export-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.export-loading {
  text-align: center;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
