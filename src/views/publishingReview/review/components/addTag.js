
import addTag from './addTag.vue'
import Vue from 'vue'
let $vm, dialogConstructor

export function addTagModal (cbs) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTag)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().open(cbs)
}
export function closeTag () {
  $vm.$mount().close()
}
export function setData (params) {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addTag)
  } if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
  }
  $vm.$mount().setData(params)
}
