<template>
  <NormalDialog
    v-if="dialogShow"
    width="800px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div v-if="showInput" class="mb10">
        <el-input
          v-model="content"
          :rows="3"
          type="textarea"
          class="w"
          :placeholder="placeholder"
        />
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },
  data() {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '',
      content: '',
      showInput: true,
      placeholder: ''
    }
  },
  mounted() {
  },
  methods: {
    close() {
      this.dialogShow = false
      this.content = ''
    },
    open(options = {}) {
      this.dialogShow = true
      this.title = options.title || ''
      this.showInput = options.showInput !== false
      this.placeholder = options.placeholder || ''
      this.cbs = options.cbs || {}
    },
    onSubmit() {
      if (typeof this.cbs.onSubmit === 'function') {
        this.cbs.onSubmit(this.content)
        this.content = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-dig {
  ::v-deep .el-input__inner {
    transition: none;
  }
  .mb10 {
    margin-bottom: 10px;
  }
}
</style>
