<template>
  <NormalDialog
    v-if="dialogShow"
    width="1500px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="main">
        <div class="img_content">
          <img :src="info.digitalBook.cover" alt="" />
          <!-- <div style="width: 100%;"><el-button style="margin-left: 100px;" type="text" @click="showImg()">查看出版证书</el-button></div> -->
        </div>
        <el-form
          label-width="10vw"
          size="mini"
        >
          <el-form-item label="书名：">
            {{ info.digitalBook.title }}
          </el-form-item>
          <el-form-item label="主编：">
            <p class="author">
              {{ info.digitalBook.author?info.digitalBook.author:'暂无' }}
            </p>
          </el-form-item>
          <el-form-item label="责任编辑：">
            <el-input v-if="edit" v-model="info.digitalBook.publishEditor" placeholder="请输入责任编辑" style="width: 300px;"/>
            <span v-else>{{ info.digitalBook.publishEditor?info.digitalBook.publishEditor:'暂无' }}</span>
          </el-form-item>
          <el-form-item label="书号（ISBN）：">
            <el-input v-if="edit" v-model="info.digitalBook.isbn" placeholder="请输入ISBN" style="width: 300px;"/>
            <span v-else>{{ info.digitalBook.isbn?info.digitalBook.isbn:'暂无' }}</span>
          </el-form-item>
          <el-form-item label="版次：">
            <el-input v-if="edit" v-model="info.digitalBook.edition" placeholder="请输入版次" style="width: 300px;"/>
            <span v-else>{{ info.digitalBook.edition?info.digitalBook.edition:'暂无' }}</span>
          </el-form-item>
          <el-form-item label="简介：">
            {{ info.digitalBook.intro?info.digitalBook.intro:'暂无' }}
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="subMit()">{{ edit?'确定':'编辑' }}</el-button>
    </template>
    <NormalDialog
      v-if="dialogShow1"
      width="1200px"
      title="出版信息"
      :dialog-visible="dialogShow1"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog="dialogShow1=false"
    >
      <div style="width: 40%; margin: 0 auto;">
        <img style="width: 100%;" :src="popImg" alt="" />
      </div>
    </NormalDialog>
  </NormalDialog>
</template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { grantPublish } from '@/api/publishing'
export default {
  components: { NormalDialog },
  props: {
    info: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      title: '出版信息确认',
      dialogShow: false,
      dialogShow1: false,
      popImg: '',
      edit: false
    }
  },
  mounted () {
    console.log(this.info)
  },
  methods: {
    showImg() {
      if (!this.info.digitalBook.publishingCertificate) {
        this.$message.warning('暂无出版证书')
        return
      }
      this.popImg = this.info.digitalBook.publishingCertificate
      this.dialogShow1 = true
    },
    subMit() {
      if (!this.edit) {
        this.edit = true
        return
      }
      this.$confirm(`是否要修改出版信息`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        grantPublish({ digitalBookId: Number(this.info.digitalBook.id), digtalBookReviewId: Number(this.info.id), isbn: this.info.digitalBook.isbn, publishEditor: this.info.digitalBook.publishEditor, edition: this.info.digitalBook.edition }).then(res1 => {
          if (res1.code === 200) {
            this.$message.success('修改成功')
            this.close()
          }
        })
      })
    },
    close () {
      this.dialogShow = false
      this.edit = false
    },
    show () {
      this.dialogShow = true
    }
  }
}
</script>
<style lang="scss" scoped>
    .author{
    width: 250px;
    padding: 0 !important;
    margin: 0;
    }
    ::v-deep .el-form-item{
      margin-bottom: 20px !important;
    }
    ::v-deep  .el-form-item__content{
      font-size: 12px !important;
      margin-top: 3px;

    }
    ::v-deep .el-button {
        font-size: 10px;
        padding: 5px;
        padding-left: 10px;
        padding-right: 10px;
        // color:red
      }
  .main{
      width: 90%;
      height: 400px;
      height: auo;
      margin: 0 auto;
      position: relative;
      .img_content{
          position: absolute;
          width: 242px;
          height: 391px;
          right: 30px;
          z-index: 10;
          img{
              width: 180px;
              height: 240px;
              object-fit: cover;
          }
      }
      // p{
      //     font-size: 16px;
      //     font-weight: 700;
      //     color:#000000;
      //     display: flex;
      //     span{
      //         font-weight: 400;
      //     }
      // }
  }
    </style>
