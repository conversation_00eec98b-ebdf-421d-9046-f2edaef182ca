<template>
  <NormalDialog
    v-if="dialogShow"
    width="1000px"
    title="审核记录"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="review-history">
      <div class="export-btn">
        <el-button type="primary" size="small" @click="exportReviewHistory">导出审核记录</el-button>
      </div>

      <div class="process-flow">
        <div class="flow-item" :class="{ active: currentStep >= 1 }">
          <div class="circle">审核</div>
          <div class="text">{{ currentStep === 1 ? '审核中' : '审核完成' }}</div>
        </div>
        <div class="arrow"></div>
        <div class="flow-item" :class="{ active: currentStep >= 2 }">
          <div class="circle">校对</div>
          <div class="text">{{ currentStep === 2 ? '校对中' : currentStep === 1 ? '未开始' : '校对中' }}</div>
        </div>
        <div class="arrow"></div>
        <div class="flow-item" :class="{ active: currentStep >= 3 }">
          <div class="circle">已出版</div>
          <div class="text">{{ currentStep === 3 ? '已出版' : '未开始' }}</div>
        </div>
      </div>

      <div class="review-detail">
        <div v-for="(item, index) in reviewList" :key="index" class="review-item">
          <div class="reviewer-info">
            <div class="avatar" :style="{ backgroundColor: getAvatarColor(item.role) }">
              {{ item.user && item.user.displayName ? item.user.displayName.substring(0, 1) : '?' }}
            </div>
            <div class="info">
              <span class="name">{{ item.user && item.user.displayName }}</span>
              <span class="role">({{ fomatterAuthor(item.role) }})</span>
            </div>
            <div class="status" :class="getStatusClass(item.reviewStatus)">
              {{ formatStatus(item.reviewStatus) }}
            </div>
          </div>
          <div v-if="item.reviewOpinion" class="review-content">
            {{ item.reviewOpinion }}
          </div>
        </div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { getDigitalBookReviewUserByBook } from '@/api/publishing'

export default {
  components: { NormalDialog },
  data() {
    return {
      dialogShow: false,
      reviewList: [],
      currentStep: 1
    }
  },
  watch: {
    reviewList: {
      handler(newVal) {
        this.calculateCurrentStep(newVal)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    calculateCurrentStep(list) {
      // 检查所有审核是否通过
      const reviewStatuses = list.filter(item =>
        ['REVIEW_1', 'REVIEW_2', 'REVIEW_3'].includes(item.role)
      ).every(item => item.reviewStatus === 'PASS')

      // 检查所有校对是否通过
      const proofreadStatuses = list.filter(item =>
        item.role === 'PROOFREAD'
      ).every(item => item.reviewStatus === 'PASS')

      if (proofreadStatuses) {
        this.currentStep = 3 // 已出版
      } else if (reviewStatuses) {
        this.currentStep = 2 // 校对阶段
      } else {
        this.currentStep = 1 // 审核阶段
      }
    },
    close() {
      this.dialogShow = false
      this.reviewList = []
    },
    open(options = {}) {
      this.dialogShow = true
      getDigitalBookReviewUserByBook({ digitalBookReviewId: options.id }).then(res => {
        this.reviewList = res.data
      })
    },
    exportReviewHistory() {
      this.$message.warning('敬请期待')
    },
    getAvatarColor(role) {
      const colorMap = {
        'REVIEW_1': '#00C7BE',
        'REVIEW_2': '#FF9500',
        'REVIEW_3': '#9B51E0',
        'PROOFREAD': '#2F80ED'
      }
      return colorMap[role] || '#909399'
    },
    fomatterAuthor(role) {
      if (role === 'REVIEW_1') {
        return '一审'
      } else if (role === 'REVIEW_2') {
        return '二审'
      } else if (role === 'REVIEW_3') {
        return '三审'
      } else if (role === 'PROOFREAD') {
        return '校对'
      } else {
        return '审核'
      }
    },
    getStatusClass(status) {
      switch (status) {
        case 'PASS':
          return 'status-pass'
        case 'UNDER_REVIEW':
          return 'status-reviewing'
        case 'FAILED':
          return 'status-failed'
        default:
          return ''
      }
    },
    formatStatus(status) {
      switch (status) {
        case 'PASS':
          return '已通过'
        case 'UNDER_REVIEW':
          return '审核中'
        case 'FAILED':
          return '已返修'
        default:
          return '未知状态'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.review-history {
  padding: 20px;

  .export-btn {
    text-align: right;
    margin-bottom: 20px;
  }

  .process-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    padding: 20px 0;

    .flow-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      opacity: 0.5;

      &.active {
        opacity: 1;
        .circle {
          background-color: #2F80ED;
          color: white;
        }
      }

      .circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #E0E0E0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .text {
        font-size: 14px;
        color: #333;
      }
    }

    .arrow {
      width: 100px;
      height: 2px;
      background-color: #E0E0E0;
      margin: 0 20px;
      position: relative;
      top: -15px;

      &::after {
        content: '';
        position: absolute;
        right: -6px;
        top: -4px;
        width: 0;
        height: 0;
        border-left: 6px solid #E0E0E0;
        border-top: 5px solid transparent;
        border-bottom: 5px solid transparent;
      }
    }
  }

  .review-detail {
    .review-item {
      padding: 15px;
      border: 1px solid #EBEEF5;
      border-radius: 4px;
      margin-bottom: 15px;

      .reviewer-info {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 16px;
          margin-right: 12px;
        }

        .info {
          flex: 1;
          .name {
            font-size: 14px;
            font-weight: 500;
            margin-right: 8px;
          }
          .role {
            font-size: 12px;
            color: #909399;
          }
        }

        .status {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;

          &.status-pass {
            background-color: #F0F9EB;
            color: #67C23A;
          }
          &.status-reviewing {
            background-color: #F4F4F5;
            color: #909399;
          }
          &.status-failed {
            background-color: #FEF0F0;
            color: #F56C6C;
          }
        }
      }

      .review-content {
        margin-left: 48px;
        padding: 10px;
        background-color: #F8F9FA;
        border-radius: 4px;
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
      }
    }
  }
}
</style>
