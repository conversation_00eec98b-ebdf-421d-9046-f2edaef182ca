tem
<template>
  <div class="main">
    <div class="header">
      <img v-if="imgSrc" class="icon" :src="imgSrc" alt="" />
      <div class="avatar"><img :src="avatar?avatar:require('../../../assets/publishingReview/default_avator.png')" alt="" /></div>
      <div class="name" :title="name">{{ name }}</div>
      <el-dropdown class="setting">
        <span class="el-dropdown-link">
          设置<i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="dialogInfoVisible = true">修改头像</el-dropdown-item>
          <el-dropdown-item @click.native="openUserName">用户名</el-dropdown-item>
          <el-dropdown-item @click.native="dialogVisible = true">设置密码</el-dropdown-item>
          <el-dropdown-item @click.native="logout">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="content">
      <div class="left">
        <div class="item" :class="tabIndex === 'PASS' ? 'active' : ''">
          <svg class="svg_item" width="26" height="26" viewBox="0 0 26 26" :fill="tabIndex === 'PASS' ? '#2F80ED' : '#4F4F4F'" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.18475 6.42103C7.66913 6.42103 9.88923 6.60173 12.0964 8.37007L12.0706 17.4699C10.8186 17.0182 8.83081 16.5406 7.04956 16.5406C6.67524 16.5406 6.31383 16.5664 5.96532 16.6051V6.42103H6.18475ZM6.18475 5.14317C5.2554 5.14317 4.67456 5.25934 4.67456 5.25934V18.2186C5.43611 17.9475 6.24929 17.8314 7.04956 17.8314C10.1087 17.8314 13.0774 19.419 13.0774 19.419V7.53108C10.6121 5.50459 7.84983 5.14317 6.18475 5.14317ZM19.8668 6.27904H20.2153V16.4373C19.6087 16.2824 18.9762 16.205 18.3437 16.205C16.8077 16.205 14.7554 16.7342 13.697 17.2247V8.47334C16.1107 6.27904 18.3953 6.27904 19.8668 6.27904ZM19.8668 4.98828C18.1888 4.98828 15.4782 5.38842 13.0903 7.53108V19.419C13.0903 19.419 15.4137 17.4958 18.3308 17.4958C19.3376 17.4958 20.4089 17.7281 21.4932 18.3347V5.13026C21.4932 5.13026 20.8607 4.98828 19.8668 4.98828Z" :fill="tabIndex === 'PASS' ? '#2F80ED' : '#4F4F4F'" />
            <path d="M12.8709 21.0117C12.7934 21.0117 12.716 20.9988 12.6514 20.973C7.54002 19.0498 3.6161 20.689 3.57738 20.7148C3.38376 20.8052 3.15143 20.7794 2.97072 20.6632C2.87882 20.6029 2.80351 20.5204 2.75165 20.4235C2.69979 20.3265 2.67304 20.2181 2.67385 20.1082V6.81335C2.67385 6.46484 2.95781 6.16797 3.31923 6.16797C3.68064 6.16797 3.96461 6.45194 3.96461 6.81335V19.2176C5.4877 18.7916 8.79205 18.2108 12.8321 19.6564C13.9164 19.1014 18.0339 17.2685 22.0482 18.8045V6.81335C22.0482 6.46484 22.3321 6.16797 22.6936 6.16797C23.0421 6.16797 23.3389 6.45194 23.3389 6.81335V19.7984C23.3389 20.0178 23.2228 20.2244 23.0291 20.3534C22.8355 20.4696 22.6032 20.4825 22.3967 20.3663C18.1888 18.1591 13.2452 20.8956 13.2065 20.9214C13.0903 20.973 12.987 21.0117 12.8709 21.0117Z" :fill="tabIndex === 'PASS' ? '#2F80ED' : '#4F4F4F'" />
          </svg>
          <div class="title" @click="changeType('PASS')">
            {{ formatterTitle() }}
          </div>
        </div>
        <div class="item" :class="tabIndex === 'UNDER_REVIEW' ? 'active' : ''">
          <svg class="svg_item" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.34907 11.3426C2.34907 8.61453 2.34656 7.5906 2.34656 4.86253C2.34656 2.49779 3.65924 2.34375 4.92129 2.34375H15.2202C17.2041 2.34375 17.8176 3.01883 17.8176 4.91844V6.20579C17.8176 7.22097 16.5261 7.10464 16.5261 6.20579V4.91844C16.5261 3.96487 16.2159 3.6311 15.2202 3.6311H4.92127C3.85519 3.6311 3.63391 3.77566 3.63391 4.91844V17.7919C3.63391 18.4975 4.02011 19.0793 4.92127 19.0793C6.48275 19.0793 5.93459 19.0799 7.49602 19.0793C8.28301 19.0793 8.34085 20.3666 7.49602 20.3666C6.65119 20.3666 6.01128 20.3769 4.43602 20.3706C3.50695 20.3668 2.73753 19.8165 2.46348 18.9648C2.38676 18.7266 2.35409 18.4632 2.35409 18.2112C2.34656 15.4925 2.34907 14.0612 2.34907 11.3426Z" :fill="tabIndex === 'UNDER_REVIEW' ? '#2F80ED' : '#4F4F4F'" />
            <path d="M15.2077 21.6558C11.6575 21.6552 8.78352 18.7769 8.78352 15.221C8.78352 11.6651 11.6713 8.78295 15.2278 8.78801C18.7845 8.79302 21.6571 11.6783 21.6534 15.2405C21.6496 18.7882 18.7681 21.6565 15.2077 21.6558ZM15.2253 10.0766C12.403 10.0703 10.0835 12.3798 10.0772 15.2046C10.0709 18.0371 12.3691 20.3534 15.1939 20.3616C18.0301 20.3691 20.3559 18.0729 20.3622 15.2569C20.3698 12.3999 18.0729 10.0835 15.2253 10.0766ZM13.933 6.19969C14.9589 6.20032 14.8181 7.50424 13.933 7.50424H6.20879C5.24326 7.50424 5.28351 6.19969 6.20879 6.19969C8.74327 6.19906 12.9474 6.19906 13.933 6.19969ZM7.49614 10.0617C8.52202 10.0624 8.38122 11.3663 7.49614 11.3663H6.20876C5.24324 11.3663 5.28349 10.0617 6.20876 10.0617C8.74327 10.0611 6.51051 10.0611 7.49614 10.0617Z" :fill="tabIndex === 'UNDER_REVIEW' ? '#2F80ED' : '#4F4F4F'" />
            <path d="M18.3738 12.8161C18.0826 12.5248 17.6104 12.5248 17.3192 12.8161L14.1009 16.0343L13.1177 15.0511C12.8264 14.7599 12.3543 14.7599 12.063 15.0511C11.7718 15.3423 11.7718 15.8145 12.063 16.1058L13.5736 17.6163C13.7192 17.7619 13.91 17.8347 14.1009 17.8347C14.2917 17.8347 14.4826 17.7619 14.6282 17.6163C14.648 17.5964 14.6667 17.5755 14.6842 17.5535C14.7061 17.5361 14.727 17.5174 14.7469 17.4976L18.3738 13.8707C18.665 13.5795 18.665 13.1073 18.3738 12.8161Z" :fill="tabIndex === 'UNDER_REVIEW' ? '#2F80ED' : '#4F4F4F'" />
          </svg>

          <div class="title" @click="changeType('UNDER_REVIEW')">
            待审核
          </div>
          <div v-if="idotReview!==0" class="idot">{{ idotReview }}</div>
        </div>
        <div class="item" :class="tabIndex === 'FAILED' ? 'active' : ''">
          <svg class="svg_item" width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.88972 12.3102C2.88972 9.4522 2.88708 8.37952 2.88708 5.52153C2.88708 3.04419 4.26228 2.88281 5.58442 2.88281H16.3738C18.4521 2.88281 19.0948 3.59004 19.0948 5.5801V6.92876C19.0948 7.99229 17.7418 7.87041 17.7418 6.92876V5.5801C17.7418 4.58113 17.4169 4.23147 16.3738 4.23147H5.5844C4.46755 4.23147 4.23574 4.38291 4.23574 5.5801V19.0666C4.23574 19.8058 4.64033 20.4153 5.5844 20.4153C7.22024 20.4153 6.64597 20.4159 8.28176 20.4153C9.10623 20.4153 9.16682 21.7639 8.28176 21.7639C7.3967 21.7639 6.72632 21.7747 5.07605 21.7681C4.10273 21.7641 3.29668 21.1876 3.00957 20.2954C2.9292 20.0458 2.89497 19.7698 2.89497 19.5058C2.88709 16.6577 2.88972 15.1583 2.88972 12.3102Z" :fill="tabIndex === 'FAILED' ? '#2F80ED' : '#4F4F4F'" />
            <path d="M16.3603 23.1145C12.641 23.1138 9.6302 20.0985 9.6302 16.3732C9.6302 12.648 12.6555 9.62864 16.3814 9.63394C20.1074 9.63919 23.1168 12.6618 23.1129 16.3937C23.109 20.1104 20.0903 23.1152 16.3603 23.1145ZM16.3788 10.9839C13.422 10.9773 10.992 13.3967 10.9855 16.3561C10.9789 19.3234 13.3865 21.75 16.3459 21.7586C19.3171 21.7666 21.7537 19.361 21.7603 16.4108C21.7682 13.4178 19.3619 10.9911 16.3788 10.9839ZM15.0249 6.92237C16.0996 6.92303 15.9521 8.28904 15.0249 8.28904H6.93286C5.92136 8.28904 5.96353 6.92237 6.93286 6.92237C9.58804 6.92171 13.9923 6.92171 15.0249 6.92237ZM8.28152 10.9683C9.35625 10.969 9.20874 12.335 8.28152 12.335H6.93284C5.92134 12.335 5.9635 10.9683 6.93284 10.9683C9.58804 10.9676 7.24895 10.9676 8.28152 10.9683Z" :fill="tabIndex === 'FAILED' ? '#2F80ED' : '#4F4F4F'" />
            <path d="M17.4765 16.3758L18.956 14.8962C19.2611 14.5911 19.2611 14.0964 18.9561 13.7913C18.6509 13.4862 18.1563 13.4862 17.8512 13.7913L16.3716 15.2709L14.892 13.7913C14.5869 13.4862 14.0923 13.4862 13.7872 13.7913C13.4821 14.0964 13.4821 14.5911 13.7872 14.8962L15.2667 16.3758L13.7872 17.8553C13.4821 18.1604 13.4821 18.6551 13.7872 18.9602C13.9397 19.1128 14.1396 19.189 14.3396 19.189C14.5395 19.189 14.7395 19.1127 14.892 18.9602L16.3716 17.4806L17.8512 18.9602C18.0037 19.1128 18.2037 19.189 18.4036 19.189C18.6035 19.189 18.8035 19.1128 18.9561 18.9602C19.2611 18.6551 19.2611 18.1604 18.956 17.8553L17.4765 16.3758Z" :fill="tabIndex === 'FAILED' ? '#2F80ED' : '#4F4F4F'" />
          </svg>

          <div class="title" @click="changeType('FAILED')">
            已返修
          </div>
          <div v-if="idotFiald!==0" class="idot">{{ idotFiald }}</div>
        </div>
      </div>
      <div class="right">
        <div v-if="dataList.length === 0" class="w" style="height: 100%">
          <Empty :msg="'暂无数据'" style="transform: scale(0.7);" />
        </div>
        <div v-else class="book_content">
          <div v-for="(item, index) in dataList" :key="index" class="book_item">
            <img :src="item.digitalBook.cover" alt="" />
            <div class="title">书名：{{ item.digitalBook.title }}</div>
            <div class="info">
              <p v-if="item.digitalBook.author">
                主编：{{ item.digitalBook.author }}
              </p>
              <p v-if="item.digitalBook.edition">
                版次：{{ item.digitalBook.edition }}
              </p>
              <p v-if="item.digitalBook.isbn">
                ISBN：{{ item.digitalBook.isbn }}
              </p>
              <div v-if="tabIndex === 'PASS'&&type===1" class="publis_info" @click="showImg(item)">出版信息</div>
            </div>
            <el-button v-if="tabIndex === 'UNDER_REVIEW'&&type===1" type="text" class="review_button" @click="toReviewHistory(item)">审核记录</el-button>
            <el-button type="primary" class="read_button" @click="toPreView(item)">阅读</el-button>
            <el-button v-if="tabIndex === 'UNDER_REVIEW'" type="primary" class="button" @click="toReview(item)">开始审核</el-button>
            <el-button v-if="tabIndex === 'FAILED'" type="primary" class="change_button" @click="reviewStart(item)">更换审核状态</el-button>
            <!-- <el-button v-if="tabIndex === 'PASS'" type="primary" class="button" @click="reviewReStart(item)">重新审核</el-button> -->
          </div>
        </div>
      </div>
    </div>
    <NormalDialog title="修改密码" :dialog-visible="dialogVisible" width="30%" @closeDialog="dialogVisible=false">
      <!-- 表单部分 -->
      <el-form ref="form" style="width: 100%;" :model="form" label-width="6vw">
        <el-form-item label="原密码">
          <el-input v-model="form.oldPassword" placeholder="请输入原密码，如未设置可不输入" />
        </el-form-item>
        <el-form-item label="新密码">
          <el-input v-model="form.newPassword" type="password" placeholder="请输入新密码" show-password />
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="form.confirmPassword" type="password" placeholder="请确认新密码" show-password />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </NormalDialog>
    <NormalDialog title="修改头像" :dialog-visible="dialogInfoVisible" width="30%" @closeDialog="dialogInfoVisible=false">
      <!-- 表单部分 -->
      <div class="row1">
        <el-upload
          class="avatar-container"
          :action="uploadUrl"
          :show-file-list="false"
          :headers="handleHeader"
          :accept="handleAccept"
          :on-error="handleError"
          :on-progress="handleProgress"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <img :src="avatar?avatar:require('../../../assets/publishingReview/default_avator.png')" class="avatar" />
          <img class="edit-avatar" src="../../../assets/images/dashboard/editAvatar.png" alt="" />
        </el-upload>
      </div>
    </NormalDialog>
    <NormalDialog title="修改昵称" :dialog-visible="dialogNameVisible" width="30%" @closeDialog="dialogNameVisible=false">
      <el-form ref="form" :model="form" label-width="3vw" style="width: 100%;">
        <el-form-item label="昵称">
          <el-input v-model="userName" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogNameVisible=false">取消</el-button>
        <el-button type="primary" @click="uptadeUserInfo()">确定</el-button>
      </template>
    </NormalDialog>
    <div v-if="preShow" class="pre">
      <Read :pre-mode="true" :book-id-props="preBookId" :pre-publish-mode="prePublishMode" @close="preShow = false" />
    </div>
    <publishPop ref="publish" :info="publishInfo" />
    <reviewHistoryDialog ref="reviewHistoryDialog" />
  </div>
</template>
<script>
import { getDigitalBookReviewList, getPublisher, changeDigitalBookReviewStatus } from '@/api/publishing'
import Empty from '@/components/classPro/Empty/index.vue'
import { mapGetters } from 'vuex'
import Read from '@/views/digitalbooks/read/index.vue'
import publishPop from './components/publishPop.vue'
import { updatePassword } from '@/api/user-api'
import { updateUserInfo } from '@/api/user-api'
import { getPublishToken, getExpertToken } from '@/utils/auth'
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import reviewHistoryDialog from './components/reviewHistoryDialog.vue'
export default {
  components: { Empty, Read, publishPop, NormalDialog, reviewHistoryDialog },
  data() {
    return {
      dialogVisible: false,
      dialogNameVisible: false,
      dialogInfoVisible: false,
      dialogShow: false,
      publishInfo: null,
      userName: '',
      preBookId: '0',
      preShow: false,
      tabIndex: 'PASS',
      imgSrc: '',
      preNode: null,
      prePublishMode: false,
      popImg: '',
      type: 0,
      idotReview: 0,
      idotFiald: 0,
      dataList: [
      ],
      form: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      handleHeader: {
        Authorization: window.location.href.indexOf('publisher') > -1 ? getPublishToken() : getExpertToken()
      },
      handleAccept: 'image/*'
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar'
    ]),
    uploadUrl: function () {
      return `${process.env.VUE_APP_BASE_API}/api/v1/students/avatar`
    }
  },
  mounted() {
    document.title = this.$route.meta.title
    this.getData()
    if (this.$route.query.publisheId) {
      getPublisher({ pubulisherId: this.$route.query.publisheId }).then(
        (res) => {
          this.imgSrc = res.data.coverUrl
        }
      )
    }
    if (window.location.href.indexOf('publisher') > -1) {
      this.type = 1
    } else if (window.location.href.indexOf('expert') > -1) {
      this.type = 0
    }
  },
  methods: {
    toReviewHistory(item) {
      this.$refs.reviewHistoryDialog.open(item)
    },
    /**
     * 在头像上传前进行的验证
     *
     * @param file 上传的文件对象
     * @returns 如果文件大小小于10MB，则返回true，否则返回false
     */
    beforeAvatarUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 10MB!')
        this.uploading = false
      }
      return isLt2M
    },
    /**
     * 更新用户信息
     *
     * @returns 无返回值
     */
    uptadeUserInfo() {
      updateUserInfo({ displayName: this.userName }).then(res => {
        if (res.code === 200) {
          this.$store.dispatch('user/EditName', this.userName)
          this.dialogNameVisible = false
          this.$message.success('修改成功')
        }
      })
    },
    openUserName() {
      this.dialogNameVisible = true
      this.userName = this.name
    },
    handleProgress () {
      this.uploading = true
    },
    handleError (e) {
      this.$message.error(e)
      this.uploading = false
    },
    handleAvatarSuccess (response, file) {
      this.uploading = false
      if (response.code === '200') {
        this.$store.dispatch('user/GetInfo')
        this.$message.success('修改成功')
        this.dialogInfoVisible = false
      } else {
        this.$message.error(response.message)
      }
    },
    getIdot() {
      getDigitalBookReviewList({ reviewStatus: 'UNDER_REVIEW' }).then(res => {
        this.idotReview = res.data ? res.data.length : 0
      })
      getDigitalBookReviewList({ reviewStatus: 'FAILED' }).then(res => {
        this.idotFiald = res.data ? res.data.length : 0
      })
    },
    showImg(item) {
      this.publishInfo = item
      this.$refs.publish.show()
    },
    logout() {
      this.$confirm('是否要退出登录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (window.location.href.indexOf('publisher') > -1) {
          this.$store.dispatch('user/PublishLogout')
          this.$router.push('/publisher/login')
        } else if (window.location.href.indexOf('expert') > -1) {
          this.$store.dispatch('user/ExpertLogout')
          this.$router.push('/expert/login')
        }
        // location.reload()
      })
    },
    toPreView(item) {
      this.preBookId = item.digitalBook.id
      if (item.reviewStatusPublisher === 'PASS' || item.reviewStatusExpert === 'PASS') {
        this.prePublishMode = false
      } else {
        this.prePublishMode = true
      }
      this.preShow = true
    },
    reviewReStart(item) {
      this.$confirm('是否要重新审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeDigitalBookReviewStatus({ digitalBookReviewId: item.id, reviewStatus: 'UNDER_REVIEW' }).then(res => {
          if (res.code === 200) {
            this.$message.success('成功')
            this.getData()
          }
        })
      })
    },
    reviewStart(item) {
      this.$confirm('是否要更换审核状态?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeDigitalBookReviewStatus({ digitalBookReviewId: item.id, reviewStatus: 'UNDER_REVIEW' }).then(res => {
          if (res.code === 200) {
            this.$message.success('更新成功')
            this.getData()
          }
        })
      })
    },
    formatterTitle() {
      if (window.location.href.indexOf('publisher') > -1) {
        return '已出版'
      } else if (window.location.href.indexOf('expert') > -1) {
        return '已通过'
      }
    },
    toReview(item) {
      if (window.location.href.indexOf('publisher') > -1) {
        this.$router.push({
          path: '/publisher/review',
          query: { bookId: item.digitalBook.id, id: item.id }
        })
      } else if (window.location.href.indexOf('expert') > -1) {
        this.$router.push({ path: '/expert/review', query: { bookId: item.digitalBook.id, id: item.id }})
      }
    },
    getData() {
      getDigitalBookReviewList({ reviewStatus: this.tabIndex }).then(res => {
        this.dataList = res.data ? res.data : []
        if (res.code === 200) {
          this.getIdot()
        }
      })
    },
    // 打开弹窗
    openDialog() {
      this.dialogVisible = true
    },
    // 提交表单
    submitForm() {
      // 这里应该添加表单验证和提交逻辑
      if (this.form.newPassword === '' || this.form.confirmPassword === '') {
        this.$message.error('新密码与确认密码不能为空')
        return
      }
      if (this.form.newPassword === this.form.confirmPassword) {
        updatePassword({
          passwordOld: this.form.oldPassword,
          passwordNew: this.form.newPassword
        }).then(res => {
          if (res.code === '200') {
            this.dialogVisible = false
          }
        })
      } else {
        // 密码不一致，给出提示
        this.$message.error('新密码与确认密码不一致')
      }
    },
    changeType(type) {
      this.tabIndex = type
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__label,
.el-input {
  font-size: var(--font-size-M);
  padding: 4px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

::v-deep .el-input__inner {
  height: 30px;
  padding: 5px;
}

::v-deep .el-button {
  padding: 8px;
  font-size: var(--font-size-M);
}

::v-deep.el-dropdown-menu__item {
  transform: scale(0.6);
  padding: 0.1rem;
}

::v-deep .el-dropdown-menu__item {
  line-height: 2rem;
}

.el-dropdown-link {
  font-size: var(--font-size-M);
  color: #000;
}
.pre {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    overflow: auto;
  }
  .row1 {
    width: 100%;
    height: 97px;
    border-radius: 14px;
    .avatar-container {
      position: relative;
      width: 60px;
      height: 60px;
      cursor: pointer;
      margin: 0 auto;
      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }
      .edit-avatar {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 22px;
        height: 22px;
      }
    }
  }
.header {
  width: 100%;
  height: 50px;
  position: relative;

  .icon {
    width: auto;
    height: 30px;
    position: absolute;
    object-fit: cover;
    left: 30px;
    top: 15px;
  }

  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background: #d8d4d4;
    position: absolute;
    top: 15px;
    left: 900px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 12px;
      object-fit: cover;
    }
  }

  .name {
        position: absolute;
        top: 22px;
        left: 930px;
        width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: var(--font-size-M);
        cursor: pointer;
  }

  .setting {
    position: absolute;
    top: 22px;
    left: 1000px;
    font-size: var(--font-size-M);
    cursor: pointer;
  }
}

.content {
  width: 95%;
  height: calc(100vh - 80px);
  margin: 10px auto;
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 1;

  .left {
    width: 15%;
    height: 100%;
    background: #ffffff;
    border-radius: 10px;
    padding: 10px;

    .item {
      width: 100%;
      height: 40px;
      position: relative;
      border-radius: 5px;
      margin-top: 10px;
      .svg_item{
        width: 20px;
        height: 20px;
        position: absolute;
        left: 30px;
        top:10px
      }
      .idot{
        width: 12px;
        height: 12px;
        border-radius: 10px;
        color: #ffff;
        font-size: 8px;
        font-weight: 800;
        text-align: center;
        line-height: 12px;
        background: red;
        position: absolute;
        right: 30px;
        top:13px
      }
      .title {
        width: 45px;
        height: 40px;
        line-height: 40px;
        margin: 0 auto;
        font-weight: 700;
        font-size: var(--font-size-L);
        text-align: center;
        cursor: pointer;
        box-sizing: border-box;
        color:#4F4F4F;
        padding-left: 3px;
        position: relative;
      }

    }
    .active {
        background: #E6F1FF;
        .title{
          color: #2f80ed;
        }
      }
    .item:nth-child(1) {
      margin-top: 20px;
    }
  }

  .right {
    width: 84%;
    height: 100%;
    background: #ffffff;
    border-radius: 10px;
    padding: 20px;
    overflow: auto;

    .book_content {
      width: 100%;
      // height: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }

    .button {
      position: absolute;
      right: 90px;
      bottom: 20px;
      width: 60px;
    }
    .change_button{
      position: absolute;
      right: 90px;
      bottom: 20px;
      width: 80px;
    }
    .review_button{
      position: absolute;
      right: 200px;
      bottom: 12px;
      width: 80px;
      text-decoration: underline;
    }
    .read_button{
      position: absolute;
      right: 20px;
      bottom: 20px;
      width: 60px;
    }
    .book_item {
      width: 49%;
      height: 200px;
      background: #f2f2f2;
      border-radius: 10px;
      position: relative;
      margin-top: 20px;
      img {
        width: 120px;
        height: 150px;
        position: absolute;
        left: 20px;
        top:30px;
        object-fit: cover;
      }

      .title {
        position: absolute;
        left: 170px;
        top: 40px;
        font-size: var(--font-size-L);
      }

      .info {
        position: absolute;
        left: 170px;
        top: 60px;
        font-size: var(--font-size-M);

        .publis_info {
          font-size: var(--font-size-L);
          width: 50px;
          height: 18px;
          line-height: 18px;
          color: #2F80ED;
          cursor: pointer;
          margin-top: 20px;
          border-bottom: 1px solid #2F80ED;
        }
      }
    }
    .book_item:nth-child(1){
      margin-top: 0;
    }
    .book_item:nth-child(2){
      margin-top: 0;
    }
  }
}
</style>
