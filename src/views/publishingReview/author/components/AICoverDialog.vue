<template>
  <NormalDialog
    v-if="dialogShow"
    title="AI生成图片"
    width="520px"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="main_body">
      <el-input v-model="tipStr" placeholder="输入要生成图片的提示词" type="textarea" :rows="3" />
      <div v-loading="loading" class="img-view" :class="{'h_height':type === 'Horizontal','v_height':type === 'Vertical'}">
        <el-image class='img' :class="{'h_height':type === 'Horizontal','h_width':type === 'Horizontal','v_height':type === 'Vertical','v_width':type === 'Vertical'}"  fit="cover" v-if="imgUrl && imgUrl !== ''" :src="imgUrl"/>
      </div>
    </div>
    <template #footer>
      <div class="bottom_btn_view">
        <template v-if="imgUrl && imgUrl !== ''">
          <el-button size="small" :disabled="loading" @click="getImage">重新生成</el-button>
          <el-button type="primary" size="small" @click="handleUse">直接使用</el-button>
        </template>
        <el-button v-else type="primary" size="small" :disabled="loading" @click="getImage">生成图片</el-button>
      </div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
export default {
  components: { NormalDialog },
  data() {
    return {
      dialogShow: false,
      tipStr: '',
      imgUrl: '',
      loading: false,
      type: 'Horizontal'
    }
  },
  methods: {
    close() {
      this.dialogShow = false
    },
    show(type = 'Horizontal') {
      this.dialogShow = true
      this.type = type
    },
    handleUse() {
      this.$emit('use', this.imgUrl)
      this.close()
    },
    async getImage() {
      if (this.tipStr === '') {
        this.$message.warning('请输入提示词')
        return
      }
      this.loading = true
      try {
        const response = await fetch(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aigcImg`, {
          method: 'post',
          headers: {
            'Content-Type': 'application/json;charset=UTF-8'
          },
          body: JSON.stringify({
            prompt: this.tipStr,
            size: this.type === 'Horizontal' ? '1280*720' : '720*1280'
          })
        })
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }
        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        while (result) {
          const { done, value } = await reader.read()
          if (done) {
            result = false
            let img = JSON.parse(res).data.img
            const type = JSON.parse(res).data.type
            if (type !== 'imgUrl') {
              img = await this.uploadImg(img)
            } else {
              img = await this.uploadImg(await this.getBase64Image(img), false)
            }
            if (!img) {
              this.$message.error('图片生成失败，请重试')
            } else {
              this.imgUrl = img
            }
            break
          }
          const chunkText = textDecoder.decode(value)
          res += chunkText
        }
        this.loading = false
      } catch (e) {
        this.$message.error('图片生成失败，请重试')
        this.loading = false
      }
    },
    async uploadImg (base64, type = true) {
      let imgData
      if (type) { imgData = 'data:image/png;base64,' + base64 } else { imgData = base64 }
      const arr = imgData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const suffix = mime.split('/')[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      const file = new File([u8arr], `'test'.${suffix}`, {
        type: mime
      })
      const { data } = await getFileUploadAuthor({
        mediaType: 'IMAGE',
        contentType: '',
        quantity: 1,
        fileName: file.name
      })
      const ossCDN = data[0].ossConfig.ossCDN
      try {
        const formData = new FormData()
        formData.append('success_action_status', '200')
        formData.append('callback', '')
        formData.append('key', data[0].fileName)
        formData.append('policy', data[0].policy)
        formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
        formData.append('signature', data[0].signature)
        formData.append('file', file)
        await axios.post(data[0].ossConfig.host, formData, {
        })
        return `${ossCDN}/${data[0].fileName}`
      } catch (error) {
        console.log(error)
        return false
      }
    },
    getBase64Image (url) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        const canvas = document.createElement('canvas')
        img.crossOrigin = '*'
        img.onload = function () {
          const width = img.width; const height = img.height
          canvas.width = width
          canvas.height = height

          const ctx = canvas.getContext('2d')
          ctx.fillStyle = 'white'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          ctx.drawImage(img, 0, 0, width, height)
          const base64 = canvas.toDataURL()
          resolve(base64)
        }
        img.onerror = function (e) {
          reject(new Error(e))
        }
        img.src = url
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.main_body {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .img-view {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    .img {
      border-radius: 5px;
    }
  }
  .h_width {
    width: 160px;
  }
  .h_height {
    height: 120px;
  }
  .v_width {
    width: 120px;
  }
  .v_height {
    height: 160px;
  }
}
.bottom_btn_view{
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .el-dialog__footer{
  width: 100%;
  color: #fff;
  font-size: 12PX;
  padding: 20PX;
  height: auto !important;
}
::v-deep .el-dialog__body{
  flex:1;
  overflow: auto;
  padding: 5px;
}
::v-deep .dialog-box{
  background: white;
  padding: 20px 20px 0 20px !important;
  position: relative;
}
</style>
