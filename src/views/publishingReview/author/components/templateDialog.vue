<template>
  <NormalDialog
    v-if="dialogShow"
    title="选择云讲义模板"
    width="840px"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="editor-dig" v-loading='loading'>
      <div class="top_view">
        <div class="empty_item" @click='close'>
          <i class="el-icon-plus"></i>
          <span class="empty_title">创建空白讲义</span>
        </div>
        <div class='list_item'  v-if='lastTemplate' @mouseenter="lastTemplate.showBtn = true" @mouseleave="lastTemplate.showBtn = false">
          <div class='item_view'>
            <el-image style='width:100%;height:100%' :src='lastTemplate.background' fit='cover'></el-image>
            <div v-show="lastTemplate.showBtn" class="item_btn_view">
              <div class="use_btn btn" @click='handleUse(lastTemplate)'>立即使用</div>
              <div class="review_btn btn" @click='handleReview(lastTemplate)'>预览</div>
            </div>
          </div>
          <div class="item_title">{{ lastTemplate.title }}</div>
          <div class='item_des'>{{lastTemplate.subtitle}}</div>
        </div>
      </div>
      <div class="bottom_view">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="讲义模板" name="first" />
          <el-tab-pane label="团队模板" name="second"/>
          <el-tab-pane label="我的模板" name="third"/>
        </el-tabs>
        <div class='list_view' v-if='templateList.length > 0'>
          <div class='list_item'  v-for='item in templateList' :key='item.id' @mouseenter="item.showBtn = true" @mouseleave="item.showBtn = false">
            <div class='item_view'>
              <el-image style='width:100%;height:100%' :src='item.background && item.background !=="" ? item.background : DefaultCover' fit='cover'></el-image>
              <div v-show="item.showBtn" class="item_btn_view">
                <div class="use_btn btn" @click='handleUse(item)'>立即使用</div>
                <div class="review_btn btn" @click='handleReview(item)'>预览</div>
              </div>
            </div>
            <div class="item_title">{{ item.title }}</div>
            <div class='item_des'>{{item.subtitle}}</div>
          </div>
        </div>
        <div class='empty_view' v-else>
          暂无数据
        </div>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import DefaultCover from '@/assets/images/default-cover.jpg'
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import { getLectureTemplate } from '@/api/cloudLecture-api'
import { getToken } from 'utils/auth'

export default {
  components: { NormalDialog },
  props: {
    bookId: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dialogShow: false,
      lastTemplate: null,
      activeName: 'first',
      templateList: [],
      token: '',
      loading: false,
      DefaultCover
    }
  },
  mounted() {
    if (localStorage.getItem('templateChangeActive') && localStorage.getItem('templateChangeActive') !== '') {
      this.activeName = localStorage.getItem('templateChangeActive')
      localStorage.removeItem('templateChangeActive')
    }
    if (this.dialogShow) {
      this.handleClick()
    }
  },
  methods: {
    async getList(bookId = 0, type = 'DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT', first = true) {
      try {
        this.loading = true
        const { data } = await getLectureTemplate({
          bookId: bookId,
          type: type
        }, {
          authorization: this.token
        })
        this.templateList = data.map((item) => {
          return {
            ...item,
            showBtn: false
          }
        })
        if (first) {
          if (localStorage.getItem('templateData')) {
            const templateData = JSON.parse(localStorage.getItem('templateData'))
            this.lastTemplate = {
              ...templateData,
              showBtn: false
            }
          } else {
            this.lastTemplate = null
          }
        }
        this.loading = false
      } catch (e) {
        this.templateList = []
        this.loading = false
      }
    },
    close() {
      this.dialogShow = false
    },
    show() {
      this.dialogShow = true
      this.token = getToken()
      this.handleClick()
    },
    handleClick() {
      switch (this.activeName) {
        case 'first':
          this.getList(0, 'DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT', false)
          break
        case 'second':
          this.getList(this.bookId, 'DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT_TEAM', false)
          break
        case 'third':
          this.getList(0, 'DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT_PERSONAL', false)
          break
        default:
          break
      }
    },
    handleUse(data) {
      localStorage.setItem('templateData', JSON.stringify(data))
      this.$emit('handleUse', data)
    },
    handleReview(data) {
      localStorage.setItem('templateChangeActive', this.activeName)
      this.$emit('handleReview', data)
    }
  }
}
</script>

<style scoped lang='scss'>
.editor-dig{
  width: 100%;
  .top_view{
    width: 100%;
    height: 180px;
    display: flex;
    gap: 40px;
  }
  .bottom_view{
    width: 100%;
    max-height: 600px;
    .empty_view{
      width: 100%;
      height: 380px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #ccc;
    }
    .list_view{
      width: 100%;
      height: 380px;
      display: flex;
      flex-wrap: wrap;
      gap: 40px;
      padding: 10px 0;
      overflow-x: auto;
    }
  }
  .item_des{
    width: 100%;
    font-size: 10px;
    color: rgba(79, 79, 79, 1);
    margin-top: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item_title{
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    width: 100%;
    white-space: pre-wrap;
    margin-top: 5px;
  }
  .list_item{
    width: 160px;
  }
  .item_view{
    width: 160px;
    height: 120px;
    border-radius: 9px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    position: relative;
    overflow: hidden;
    .item_btn_view{
      width: 100%;
      //height: 30px;
      position: absolute;
      bottom: 10px;
      display: flex;
      padding: 0 10px;
      justify-content: space-between;
      font-size: 12px;
      gap: 5px;
      .btn{
        height: 100%;
        width: 50%;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        z-index: 9999;
        font-size: 10px;
        &:hover{
          cursor: pointer;
        }
      }
      .use_btn{
        //font-size: var(--font-size-S);
        color: #fff;
        background-color: rgba(47, 128, 237, 1);
      }
      .review_btn{
        //font-size: var(--font-size-S);
        color: rgba(47, 128, 237, 1);
        background-color: #fff;
        border: 1px solid rgba(47, 128, 237, 1);
      }
    }
  }
  .empty_item{
    width: 160px;
    height: 120px;
    border: 1px solid rgba(224, 224, 224, 1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &:hover{
      cursor: pointer;
    }
    .empty_title{
      color: rgba(0, 0, 0, 1);
      font-size: 14px;
      font-weight: 400;
      margin-top: 20px;
    }
  }
}
</style>
