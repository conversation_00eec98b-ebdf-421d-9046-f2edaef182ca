<template>
  <el-drawer
    title="历史记录"
    :visible.sync="drawer"
    size="30%"
    :with-header="false">
    <div class='history_body'>
      <div class='history_header'>
        历史记录
        <el-button
          style='color: #000'
          type="text"
          icon="el-icon-close"
          @click="handleClose" />
      </div>
      <div class='history_content'>
        <div v-if="historyList.length === 0" class="w" style="height: 90%">
          <Empty :msg="'暂无数据'" style="transform: scale(0.6);" />
        </div>
        <div v-for="item in historyList" :key="item.id" class="history_item">
          <p class="time">{{ formatDateTime(item.updatedAt) }}</p>
          <p class="author">{{ item.user.displayName }}</p>
          <el-button type="text" class="button button1" @click="showHistory(item)">查看内容</el-button>
          <el-button type="text" class="button button2" @click="setHistoryContent(item)">恢复</el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getDigitalContentChangeLog } from '@/api/digital-api'
import Empty from '@/components/classPro/Empty/index.vue'

export default {
  props: {
    catalogueId: {
      type: Number,
      default: () => 0
    },
    token: {
      type: String,
      default: () => ''
    }
  },
  components: { Empty },
  data() {
    return {
      drawer: false,
      historyList: [],
      historyContent: '',
    }
  },
  methods: {
    handleClose() {
      this.drawer = false
    },
    handleOpen() {
      this.drawer = true
      this.getHistoryList()
    },
    async getHistoryList() {
      const { data } = await getDigitalContentChangeLog({ catalogueId: this.catalogueId, pageNo: 1, pageSize: 1000 }, {
        authorization: this.token
      })
      this.historyList = data.content
    },
    formatDateTime(dateTimeString) {
      const dateTime = new Date(dateTimeString)
      const year = dateTime.getFullYear()
      const month = String(dateTime.getMonth() + 1).padStart(2, '0')
      const day = String(dateTime.getDate()).padStart(2, '0')
      const hours = String(dateTime.getHours()).padStart(2, '0')
      const minutes = String(dateTime.getMinutes()).padStart(2, '0')
      const seconds = String(dateTime.getSeconds()).padStart(2, '0')

      const formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`

      return formattedDateTime
    },
    showHistory(item) {
      if (item.contentFileUrl && item.contentFileUrl !== '') {
        fetch(item.contentFileUrl)
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok ' + response.statusText)
            }
            return response.text()
          })
          .then(data => {
            this.historyContent = data
            this.$emit('showHistoryDetail', this.historyContent)
          })
          .catch(() => {
          })
      } else {
        this.historyContent = ''
        this.$emit('showHistoryDetail', this.historyContent)
      }

    },
    setHistoryContent(item) {
      this.$confirm('此操作将覆盖当前编辑的内容, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        fetch(item.contentFileUrl)
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok ' + response.statusText)
            }
            return response.text()
          })
          .then(data => {
            this.$emit('setHistoryContent', data)
            this.handleClose()
          })
          .catch(() => {
          })
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.history_body{
  width: 100%;
  height: 100%;
  background-color: #fff;
  .history_header{
    padding: 0 5px;
    width: 100%;
    height: 40px;
    border-bottom: 1px solid  #ccc;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .history_content{
    width: 100%;
    height: calc(100% - 40px);
    padding: 0 5px;
    overflow-y: auto;
    .history_item{
      width: 100%;
      height: 50px;
      background: #fbf9f9;
      margin-top: 10px;
      position: relative;
      .time{
        font-size: 10px;
        height: 12px;
        text-align: left;
        color: #828282;
        padding: 10px;
      }
      .author{
        font-size: 10px;
        height: 12px;
        text-align: left;
        padding-left: 10px;
        position: relative;
        text-indent: 1rem;
      }
      .author::before{
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 3px;
        background: #27AE60;
        position: absolute;
        left: 10px;
        top:3px;
      }
      .button{
        position: absolute;
        font-size: 9px;
      }
      .button1{
        right: 20px;
        bottom:20px;
      }
      .button2{
        right: 20px;
        bottom:-5px;
      }
    }
  }
}

</style>
