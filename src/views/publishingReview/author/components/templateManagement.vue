<template>
  <NormalDialog
    v-if="dialogShow"
    title="模板管理"
    width="840px"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class='editor-dig'>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="我的模板" name="first" />
        <el-tab-pane label="团队模板" name="second"/>
        <el-tab-pane label="模板库" name="third"/>
      </el-tabs>
      <div class='list_view' v-if='templateList.length > 0'>
        <div class='list_item'  v-for='item in templateList' :key='item.id' @mouseenter="item.showBtn = true" @mouseleave="item.showBtn = false">
          <div class='item_view'>
            <el-image style='width:100%;height:100%' :src='item.background && item.background !=="" ? item.background : DefaultCover' fit='cover'/>
            <div v-show="item.showBtn" class="item_btn_view">
              <div class="review_btn btn" @click='handleReview(item)'>预览</div>
            </div>
            <img v-show='item.createBy === userId && item.showBtn' src="@/assets/digitalbooks/read/circle-close.png" class='item_close' @click='deleteTemplate(item)'/>
          </div>
          <div class="item_title">{{ item.title }}</div>
          <div class='item_des'>{{item.subtitle}}</div>
        </div>
      </div>
      <div class='empty_view' v-else>
        暂无数据
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import { getLectureTemplate } from '@/api/cloudLecture-api'
import { getToken } from 'utils/auth'
import { deleteBookCatalogue } from '@/api/digital-api'
import DefaultCover from '@/assets/images/default-cover.jpg'

export default {
  components: { NormalDialog },
  props: {
    bookId: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dialogShow: false,
      loading: false,
      activeName: 'first',
      templateList: [],
      userId: 0,
      DefaultCover
    }
  },
  mounted() {
    if (localStorage.getItem('templateActive') && localStorage.getItem('templateActive') !== '') {
      this.activeName = localStorage.getItem('templateActive')
      localStorage.removeItem('templateActive')
    }
    if (this.dialogShow) {
      this.handleClick()
    }
    this.userId = this.$store.getters.id
  },
  methods: {
    async getList(bookId = 0, type = 'DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT_PERSONAL') {
      try {
        this.loading = true
        const { data } = await getLectureTemplate({
          bookId: bookId,
          type: type
        }, {
          authorization: this.token
        })
        this.templateList = data.map((item) => {
          return {
            ...item,
            showBtn: false
          }
        })
        this.loading = false
      } catch (e) {
        this.templateList = []
        this.loading = false
      }
    },
    close() {
      this.dialogShow = false
    },
    show() {
      this.dialogShow = true
      this.token = getToken()
      this.handleClick()
    },
    handleClick() {
      switch (this.activeName) {
        case 'first':
          this.getList(0, 'DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT_PERSONAL')
          break
        case 'second':
          this.getList(this.bookId, 'DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT_TEAM')
          break
        case 'third':
          this.getList(0, 'DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT')
          break
        default:
          break
      }
    },
    handleReview(data) {
      localStorage.setItem('templateActive', this.activeName)
      this.$emit('handleReview', data)
    },
    deleteTemplate(item) {
      this.$confirm('是否删除该模板？', '提示', {
        type: 'warning'
      }).then(async () => {
        await deleteBookCatalogue({ catalogueId: item.id })
        this.$message.success('删除成功')
        await this.handleClick()
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.editor-dig{
  width: 100%;
  .empty_view{
    width: 100%;
    height: 380px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ccc;
  }
  .list_view{
    width: 100%;
    height: 380px;
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    padding: 10px 0;
    overflow-x: auto;
  }
  .item_des{
    width: 100%;
    font-size: 10px;
    color: rgba(79, 79, 79, 1);
    margin-top: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .item_title{
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    width: 100%;
    white-space: pre-wrap;
    margin-top: 5px;
  }
  .list_item{
    width: 160px;
  }
  .item_view{
    width: 160px;
    height: 120px;
    border-radius: 9px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    position: relative;
    overflow: hidden;
    .item_close{
      position: absolute;
      top: 0;
      right: 0;
      color: rgba(0, 0, 0, 1);
      width: 32px;
      height: 32px;
      &:hover{
        cursor: pointer;
      }
    }
    .item_btn_view{
      width: 100%;
      //height: 30px;
      position: absolute;
      bottom: 10px;
      display: flex;
      padding: 0 10px;
      justify-content: center;
      font-size: 12px;
      gap: 5px;
      .btn{
        height: 100%;
        width: 50%;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        z-index: 9999;
        font-size: 10px;
        &:hover{
          cursor: pointer;
        }
      }
      .use_btn{
        //font-size: var(--font-size-S);
        color: #fff;
        background-color: rgba(47, 128, 237, 1);
      }
      .review_btn{
        //font-size: var(--font-size-S);
        color: rgba(47, 128, 237, 1);
        background-color: #fff;
        border: 1px solid rgba(47, 128, 237, 1);
      }
    }
  }
  .empty_item{
    width: 160px;
    height: 120px;
    border: 1px solid rgba(224, 224, 224, 1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &:hover{
      cursor: pointer;
    }
    .empty_title{
      color: rgba(0, 0, 0, 1);
      font-size: 14px;
      font-weight: 400;
      margin-top: 20px;
    }
  }
}
</style>
