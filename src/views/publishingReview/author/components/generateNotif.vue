<template>
  <NormalDialog
    v-if="dialogShow"
    width="240px"
    :show-close='false'
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    :default-header='false'
    @closeDialog="close"
  >
    <div class="no-conversion">
      <div class='notif-main-body'>
        <template v-if='type === 0 || type === 2'>
          <i class="el-icon-success" style='font-size: 30px;color: #65B85D;margin-bottom: 8px'></i>
          <div>{{type === 0 ? '已提交申请' : '已生成'}}</div>
          <div style='margin-top: 8px'>
            <span v-if='type === 0'>待运营审核确认后生成</span>
            <span>
              请到教材内容
              <span style='text-decoration: underline;color:#2F80ED;cursor: pointer' @click='checkBook'>查看</span>
            </span>
          </div>
          <div style='margin-top: 10px'>{{`${countdown}S`}}</div>
        </template>
        <template v-if='type === 1'>
          <i class="el-icon-loading" style='font-size: 40px;margin-bottom: 8px;margin-top: 20px'></i>
          <span>{{`${percent}%`}}</span>
          <div style='display: flex;align-items: center'>
            <EllipsisAnimation text='教材生成中' style='margin-top: 10px'/>
          </div>
        </template>
      </div>
    </div>
  </NormalDialog>
</template>

<script>
import EllipsisAnimation from '@/components/classPro/EllipsisAnimation'
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import { generateDigitalBook } from '@/api/cloudLecture-api'
export default {
  components: { NormalDialog, EllipsisAnimation },
  props: {
    bookId: {
      type: [String, Number],
      default: 0
    },
    token: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogShow: false,
      type: null, // 0: 已提交 1: 生成中 2：已生成，
      countdown: 5,
      timer: null,

      startValue: 0, // 起始值
      targetValue: 95, // 目标值
      duration: 23000, // 动画持续时间（毫秒）
      percent: 0,
      animationId: null, // 动画ID
      startTime: null // 动画开始时间
    }
  },
  methods: {
    close() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
      }
      this.dialogShow = false
    },
    show(type) {
      this.type = type
      this.countdown = 5
      this.dialogShow = true
      if (type === 0) {
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.timer)
            this.dialogShow = false
            this.$emit('closeDialog')
          }
        }, 1000)
      }
      if (type === 1) {
        this.startAnimation()
        this.handleGenerate()
      }
    },
    async handleGenerate() {
      // 记录开始时间
      const startTime = Date.now()
      try {
        await generateDigitalBook({
          bookId: this.bookId
        }, {
          headers: {
            'Authorization': this.token
          }
        })

        // 计算API调用耗时
        const elapsedTime = Date.now() - startTime
        const minDelay = 25000
        if (elapsedTime < minDelay) {
          await new Promise(resolve => setTimeout(resolve, minDelay - elapsedTime))
        }

        this.type = 2
        this.percent = 100
        this.startValue = 0
        if (this.animationId) {
          cancelAnimationFrame(this.animationId)
          this.animationId = null
          this.startTime = null
        }
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.timer)
            this.dialogShow = false
            this.$emit('closeDialog')
          }
        }, 1000)
      } catch (e) {
        console.log(e)
      }
    },
    // 缓动函数：实现由快到慢的效果
    easeOutQuad(t) {
      return -1 * t * (t - 2)
    },
    // 动画循环函数
    animate(currentTime) {
      // 首次调用记录开始时间
      if (!this.startTime) this.startTime = currentTime
      // 计算已过去的时间（毫秒）
      const elapsedTime = currentTime - this.startTime
      // 计算动画进度（0-1之间）
      const progress = Math.min(elapsedTime / this.duration, 1)
      // 应用缓动函数，使动画由快到慢
      const easedProgress = this.easeOutQuad(progress)
      // 根据进度计算当前应该显示的数值
      this.percent = Math.floor(
        this.startValue + (this.targetValue - this.startValue) * easedProgress
      )
      // 如果动画未完成，继续请求下一帧
      if (progress < 1) {
        this.animationId = requestAnimationFrame(this.animate)
      } else {
        // 动画完成，重置状态
        this.animationId = null
        this.startTime = null
      }
    },
    // 开始动画
    startAnimation() {
      // 如果已有动画在运行，先取消它
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
        this.animationId = null
        this.startTime = null
      }
      // 重置显示值并开始新动画
      this.percent = this.startValue
      this.animationId = requestAnimationFrame(this.animate)
    },
    // 查看数字教材内容
    checkBook() {
      this.$router.push({ path: '/editor', query: { id: this.bookId, token: this.token, path: '/author/lectureNotes' }})
    }
  }
}
</script>

<style scoped lang='scss'>
.no-conversion{
  width: 100%;
  .notif-main-body{
    width: 100%;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
  }
}

::v-deep .el-dialog__header{
  border-bottom: 0 !important;
  padding: 0 !important;
}

</style>
