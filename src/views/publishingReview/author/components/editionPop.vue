<!-- eslint-disable vue/html-indent -->
<template>
    <NormalDialog
      v-if="dialogShow"
      width="1200px"
      title="出版详情"
      :dialog-visible="dialogShow"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog="close">
      <div class="flex flex-col main w">
        <div class="left">
        <div v-if="editionList.length === 0" class="w" style="height: 100%">
          <Empty :msg="'暂未出版'" style="transform: scale(0.7);" />
        </div>
        <div v-else>
            <div v-for="(item,index) in editionList" :key="index" class="left_item" :class="tabindex===index?'active':''" @click="changeData(index)">
            {{ !item.edition?'暂无版号信息':item.edition }}
            </div>
        </div>
    </div>
        <div class="right">
            <p class="title">出版信息</p>
            <el-button type="primary" class="read" :disabled="editionList.length === 0" @click="read">阅读</el-button>
        <div v-if="editionList.length === 0||tabindex==-1" class="w" style="height: 100%">
          <Empty :msg="'暂未出版'" style="transform: scale(0.7);" />
        </div>
        <el-form
          v-else
          label-width="8vw"
          size="mini"
        >
          <el-form-item label="书名：">
            {{ formData.digitalBook.title }}
          </el-form-item>
          <el-form-item label="主编：">
            {{ formData.digitalBook.author?formData.digitalBook.author:'暂无' }}
          </el-form-item>
          <el-form-item label="责任编辑：">
            <span>{{ formData.digitalBook.publishEditor?formData.digitalBook.publishEditor:'暂无' }}</span>
          </el-form-item>
          <el-form-item label="书号（ISBN）：">
            <span>{{ formData.digitalBook.isbn?formData.digitalBook.isbn:'暂无' }}</span>
          </el-form-item>
          <el-form-item label="版次：">
            <span>{{ formData.edition?formData.edition:'暂无' }}</span>
          </el-form-item>
          <el-form-item label="简介：">
            {{ formData.digitalBook.intro?formData.digitalBook.intro:'暂无' }}
          </el-form-item>
        </el-form>
        </div>
      </div>
      <template #footer>
        <div class="left"></div>
    </template>
    </NormalDialog>
  </template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { getDigitalBookReviewListByBook } from '@/api/publishing'
import Empty from '@/components/classPro/Empty/index.vue'
export default {
  components: { NormalDialog, Empty },
  data() {
    return {
      dialogShow: false,
      bookId: 0,
      editionList: [],
      tabindex: 0,
      formData: null
    }
  },
  mounted() {
  },
  methods: {
    read() {
      this.$emit('read', { bookId: this.bookId, editionId: this.formData.id })
      // this.close()
    },
    changeData(index) {
      console.log(this.editionList)
      this.tabindex = index
      this.formData = this.editionList[this.tabindex]
      console.log(this.formData)
    },
    close() {
      this.dialogShow = false
      this.tabindex = 0
      this.formData = null
      this.editionList = []
    },
    show(id) {
      this.bookId = id
      getDigitalBookReviewListByBook({ digitalBookId: id }).then((res) => {
        this.editionList = res.data
        this.formData = res.data[this.tabindex]
      })
      this.dialogShow = true
    }
  }
}
</script>
<style lang="scss" scoped>
//  .el-dialog__wrapper{
//  transform: rotate(0deg)
// }
::v-deep  .el-form-item__content{
    font-size: 12px !important;
    padding-top: 3px;
}
::v-deep .dialog-box{
    padding: 0 !important;
}
::v-deep .el-dialog__footer{
      padding: 0 !important;
      height: 20px;
      position: relative;
      .left{
        width: 20%;
        height: 20px;
        position: absolute;
        left: 0;
        top:0;
        border-right: 1px solid #d9d9d9;
      }
}
::v-deep .el-dialog__body{
    padding: 0 !important;
    // border-radius: 0px 0px 10px 10px;
}
.main{
    width: 100%;
    height: 400px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .left{
        width: 20%;
        height: 100%;
        background: #fff;
        border-right: 1px solid #d9d9d9;
        overflow: auto;
        .left_item{
            width: 80%;
            margin: 0 auto;
            height: 25px;
            text-align: center;
            font-size: 12px;
            color: #000;
            line-height: 25px;
            margin-top: 10px;
            cursor: pointer;
        }
        .active{
            background: #2F80ED;
            color: #fff;
        }
    }
    .right{
        width: 80%;
        height: 100%;
        border-radius: 10px;
        background: #fff;
        position: relative;
        padding-top: 70px;
        padding-left: 10px;
        padding-right: 10px;
        color: #000;
        overflow: auto;
        @include scrollBar;
        .title{
            position: absolute;
            left: 20px;
            top:20px;
            font-size: 14px;
            font-weight: 500;
        }
        .read{
            position: absolute;
            right: 50px;
            height: 25px;
            width: 50px;
            font-size: 12px;
            line-height: 0px;
            top:30px
        }
    }
}
</style>

