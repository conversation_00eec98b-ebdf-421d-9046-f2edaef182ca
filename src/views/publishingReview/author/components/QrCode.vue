<template>
  <NormalDialog
    v-if="dialogShow"
    width="30vw"
    title="邀请主编"
    :dialog-visible="dialogShow"
    :append-to-body="true"
    :is-center="true"
    @closeDialog="dialogShow = false"
  >
    <div class="w flex flex-col items-center">
      <div ref="bill" class="qr_code w flex flex-col items-center">
        <div class="code-share flex items-center">
          {{ name+' '+'邀请你一起编写数字教材《'+ info.title }}》，请扫描二维码接受邀请
        </div>
        <div ref="qrCodeUrl" class="qrcode"></div>
      </div>
      <div class="mt20 flex justify-center">
        <el-button type="text" class="save_qr" @click="saveImage">下载二维码</el-button>
        <el-button
          v-clipboard:copy="inviteText"
          v-clipboard:success="onCopy"
          class="save_qr"
          type="text"
        >复制链接</el-button>
      </div>
      <div class="mt20 flex justify-center">
      </div>
      <div class="invite_tips flex justify-center">
        *复制链接或保存二维码发送给其他主编
      </div>
    </div>
  </NormalDialog>
</template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import { mapGetters } from 'vuex'
export default {
  components: { NormalDialog },
  data() {
    return {
      dialogShow: false,
      info: {}
    }
  },
  computed: {
    inviteUrl() {
      return process.env.VUE_APP_WEB_URL + '#/' + this.info.authorInviteUrl
    },
    ...mapGetters([
      'name'
    ]),
    inviteText() {
      return `${this.name} 邀请你一起编写数字教材《${this.info.title}》点击邀请链接：` + process.env.VUE_APP_WEB_URL + '#/' + this.info.authorInviteUrl
    }
  },
  mounted() {
  },
  methods: {
    /**
       * 复制操作函数
       *
       * @returns 无返回值
       */
    onCopy() {
      this.$message.success('复制成功')
    },
    /**
       * 邀请作者
       *
       * @returns 无返回值
       */
    inviteAuthor() {
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl, {
            width: 200, // 二维码宽度
            height: 200, // 二维码高度
            text: this.inviteUrl, // 浏览器地址url
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.Q
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
        }
      )
    },
    /**
       * 保存二维码
       *
       * @returns 无返回值
       */
    saveImage () {
      const canvasID = this.$refs.bill
      const that = this
      const a = document.createElement('a')
      html2canvas(canvasID).then((canvas) => {
        const dom = document.body.appendChild(canvas)
        dom.style.display = 'none'
        a.style.display = 'none'
        document.body.removeChild(dom)
        const blob = that.dataURLToBlob(dom.toDataURL('image/png'))
        a.setAttribute('href', URL.createObjectURL(blob))
        // 这块是保存图片操作  可以设置保存的图片的信息
        a.setAttribute('download', that.info.title + '编辑邀请' + '.png')
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(blob)
        document.body.removeChild(a)
      })
    },
    /**
       * 将data URL转换为Blob对象
       *
       * @param dataurl data URL字符串
       * @returns 返回转换后的Blob对象
       */
    dataURLToBlob (dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    close() {
      this.dialogShow = false
    },
    show(data = null) {
      if (data) {
        this.info = data
      }
      this.dialogShow = true
      this.inviteAuthor()
    }
  }
}
</script>
  <style lang="scss" scoped>
  .invite_tips{
    color: #EB5757;
    margin-bottom: 20px;
    font-size: var(--font-size-M);
  }
  .qrcode{
    ::v-deep  img{
      width: 123px !important;
      height: 123px !important;
    }
  }
  .code-share-title{
    font-size: var(--font-size-L);
    color: #000;
    font-weight: 600;
    margin-top: 10px;
  }
  .save_qr{
    font-size: var(--font-size-S);
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .code-share{
    font-size: var(--font-size-M);
    color: #000;
    margin-top: 10px;
    width: 70%;
  }
  .qrcode{
    margin-top: 10px;
  }
  ::v-deep .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 150px;
      height: 185px;
      line-height: 185px;
      text-align: center;
    }
    .avatar {
      width: 150px;
      height: 185px;
      display: block;
      object-fit: cover;
    }
    .qr_code{
  padding-bottom: 20px;
  }
  ::v-deep .el-form-item__content {
      font-size: 12px !important;
  }
  ::v-deep .el-form-item{
        margin-bottom: 20px !important;
      }
  ::v-deep .el-dialog__footer{
        display: none;
  }
  ::v-deep .footer_left{
        width: 100px;
        height: 100%;
        border-right: 1px solid #d9d9d9;
  }
  ::v-deep .el-button {
      font-size: 10px;
      padding: 5px;
      padding-left: 10px;
      padding-right: 10px;
      // color:red
  }
  ::v-deep .el-dialog__body{
      padding: 0 !important;
      border-radius: 0px 0px 10px 10px;
  }
  ::v-deep .dialog-box{
      padding: 0 !important;
  }
  .main {
      width: 100%;
      height: 400px;
      height: auo;
      margin: 0 auto;
      position: relative;
      display: flex;
      justify-content: space-between;
      .left{
        width: 100px;
        height: 100%;
        border-right: 1px solid #d9d9d9;
        .left_item{
              width: 80%;
              margin: 0 auto;
              height: 25px;
              text-align: center;
              font-size: var(--font-size-M);
              color: #000;
              line-height: 25px;
              margin-top: 10px;
              cursor: pointer;
          }
          .active{
              background: #2F80ED;
              color: #fff;
          }
      }
      .right{
        width: calc(100% - 100px);
        height: 100%;
        padding-top: 30px;
        padding-bottom: 30px;
        .right_top{
          height: 95%;
          width: 100%;
          overflow: auto;
          @include scrollBar;
          position: relative;
          .title{
            position: absolute;
            left: 20px;
            top:0px;
            font-size: var(--font-size-XL);
          }
          .author_content{
          overflow: auto;
          width: 93%;
          margin: 0 auto;
          height: 280px;
          margin-top: 25px;
          .author_item{
            width: 100%;
            height: 60px;
            border-bottom: 1px solid #F2F2F2;
            position: relative;
            .avatar{
              width: 30px;
              height: 30px;
              border-radius: 15px;
              object-fit: cover;
              position: absolute;
              left: 15px;
              top:15px;
            }
            .author_name{
              color: #000;
              font-size: var(--font-size-M);
              width: 100px;
              white-space: nowrap;
              position: absolute;
              left: 55px;
              top:15px
            }
            .author_type{
              color: #828282;
              font-size: var(--font-size-M);
              width: 100px;
              white-space: nowrap;
              position: absolute;
              left: 255px;
              top:15px
            }
            .delete_btn{
              font-size: var(--font-size-M);
              position: absolute;
              right: 55px;
              top:18px
            }
          }
          @include scrollBar;
          }
        }
        .submit_button{
          margin-top: 10px;
          float: right;
          margin-right: 50px;
        }
      }
      .img_content {
          position: absolute;
          width: 242px;
          height: 391px;
          right: 50px;
          z-index: 10;
          img {
              width: 180px;
              height: 240px;
              object-fit: cover;
          }
      }
      p {
          font-size: 16px;
          font-weight: 700;
          color: #000000;
          display: flex;
          span {
              font-weight: 400;
          }
      }
  }
  </style>
