<template>
  <NormalDialog
    v-if="dialogShow"
    title="生成数字教材"
    width="580px"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="no-conversion">
      <div class='main-body'>
        <div class='left'>
          <i class="el-icon-warning-outline" style='font-size: 30px;color:#FF7F28;'></i>
        </div>
        <div class='right'>
          <div class='right_title'>
            {{hasBook ? '已有数字教材内容，确定重新生成并覆盖：' : '智能云讲义生成数字教材提示：'}}
          </div>
          <div class='right_content'>
            <div>①云讲义确定为最终版本</div>
            <div>②生成过程中时间较长，耐心等待</div>
          </div>
          <div class='right_tip' v-if='hasBook'>
            *提交后会删除原数字教材内容、任务、知识图谱（如有），不可恢复
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="editor-dig">
        <el-button v-if='!hasBook' type="primary" size="small" style='min-width: 100px' @click='handleNotif(1)'>立即生成</el-button>
        <el-button v-else type="primary" size="small" style='min-width: 100px' @click='handleNotif(1)'>立即生成</el-button>
        <el-button size="small" style='min-width: 100px;margin-left: 50px' @click='close'>取消</el-button>
      </div>
    </template>
    <GenerateNotif ref='generateNotifRef' :book-id='bookId' :token='token' @closeDialog='close'/>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import GenerateNotif from '@/views/publishingReview/author/components/generateNotif'
export default {
  components: { NormalDialog, GenerateNotif },
  props: {
    bookId: {
      type: [String, Number],
      default: 0
    },
    token: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogShow: false,
      loading: false,
      hasBook: false
    }
  },
  methods: {
    close() {
      this.dialogShow = false
    },
    show(hasCatalogue = false) {
      this.dialogShow = true
      this.hasBook = hasCatalogue
    },
    handleNotif(type) {
      this.$refs.generateNotifRef.show(type)
    }
  }
}
</script>

<style scoped lang='scss'>
.no-conversion{
  width: 100%;
  .main-body{
    display: flex;
    padding: 20px 50px 50px 50px;
    .left{
      height: 100%;
      width: 30px;
    }
    .right{
      height: 100%;
      width: calc(100% - 30px);
      font-size: 20px;
      .right_title{
        width: 100%;
        height: 30px;
        line-height: 30px;
        font-weight: 600;
        color: #000;
        padding-left: 10px;
      }
      .right_content{
        width: 100%;
        padding-left: 10px;
        color: #000;
        margin-top: 20px;
      }
      .right_tip{
        width: 100%;
        color: #EB5757;
        margin-top: 20px;
      }
    }
  }
}

.editor-dig{
  width: 100%;
  display: flex;
  justify-content: center;
  ::v-deep .el-button--small{
    font-size: 18px;
  }
}

</style>
