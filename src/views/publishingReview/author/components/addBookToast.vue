<!-- eslint-disable vue/html-indent -->
<template>
  <NormalDialog
    v-if="dialogShow"
    width="1200px"
    :title="type === 'edit' ? '教材信息' : '创建教材'"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="main">
        <div class="left">
          <div class="left_item" :class="tabindex===0?'active':''" @click="tabindex=0">
            教材信息
          </div>
          <div v-if="info.id" class="left_item" :class="tabindex===1?'active':''" @click="tabindex=1">
            主编团队
          </div>
          <div v-if="info.id" class="left_item" :class="tabindex===2?'active':''" @click="tabindex=2">
            教材配置
          </div>
          <div v-if="info.id" class="left_item" :class="tabindex===3?'active':''" @click="tabindex=3">出版审核</div>
        </div>
        <div v-if="tabindex===0" class="right">
          <div class="right_top">
            <el-form ref="form" label-width="10vw" size="mini" :model="info">
              <el-form-item
                label="教材名称："
                prop="title"
                :rules="[
                  { required: true, message: '教材名称不能为空'},
                ]"
              >
                <el-input v-model="info.title" placeholder="请输入教材名称" style="width: 600px;" />
              </el-form-item>
              <el-form-item label="主编：">
                <el-input v-model="info.author" placeholder="请输入主编" style="width: 600px;" />
              </el-form-item>
              <el-form-item
                label="封面："
                prop="cover"
                :rules="[
                  { required: true, message: '请上传封面'},
                ]"
              >
                <el-upload
                  class="avatar-uploader"
                  action=""
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                >
                  <img v-if="info.cover" :src="ossUrl+info.cover" :key='imageKey' class="avatar" />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  <div slot="tip" style='display: flex'>
                    <el-button  :disabled='!info.cover || info.cover === ""' type='primary' size='mini' @click='setImage'>图片设置</el-button>
                    <div class='ai_btn' @click='toAIView'>AI生成封面</div>
                  </div>
                </el-upload>

              </el-form-item>
              <el-form-item
                label="教材简介："
                prop="intro"
                :rules="[
                  { max: 2000, message: '内容不能超过2000个字符', trigger: 'blur'},
                ]">
                <el-input v-model="info.intro" type="textarea" :rows="5" placeholder="请输入教材简介" style="width: 600px;" />
              </el-form-item>
            </el-form>
          </div>
          <div>
            <el-button class="submit_button" type="primary" @click="subMit()">确定</el-button>
          </div>
        </div>
        <div v-if="tabindex===1" class="right">
          <div class="right_top">
            <div class="title">主编团队</div>
            <div class="author_content">
              <div v-for="(item,index) in authorList" :key="index" class="author_item">
                <img class="avatar" :src="item.user.avatar?item.user.avatar : require('../../../../assets/publishingReview/default_avator.png')" alt="" />
                <p class="author_name">{{ item.user.displayName }}</p>
                <p class="author_type">{{ item.tag==='admin'?'创作人':'主编' }}</p>
                <el-button v-if="isAdmin&&item.tag!=='admin'" class="delete_btn" type="text" @click="removeAuthor(item.user.id)">删除</el-button>
              </div>
            </div>
          </div>
          <div>
            <el-button v-if="isAdmin" class="submit_button" type="primary" @click="inviteAuthor">邀请主编</el-button>
          </div>
        </div>
        <div v-if="tabindex===2" class="right">
          <div class="right_top">
            <div class="title">教材配置</div>
            <div class="config_item">
              <p>复制配置</p><div>
                <div class="congig_right">  <el-radio v-model="bookConfig.contentCopy" :label="true">支持用户复制</el-radio>
                  <el-radio v-model="bookConfig.contentCopy" :label="false">不支持用户复制</el-radio></div>
                <p class="tips">
                  说明：配置用户是否可以复制教材内容
                </p>
              </div>
            </div>
            <div class="config_item">
              <p>附件下载</p> <div>
                <div class="congig_right">   <el-radio v-model="bookConfig.attachFileDownload" :label="true">支持用户下载</el-radio>
                  <el-radio v-model="bookConfig.attachFileDownload" :label="false">不支持用户下载</el-radio></div>
                <p class="tips">
                  说明：配置用户是否可以下载教材中的附件
                </p>
              </div>
            </div>
<!--            <div class="config_item">-->
<!--              <p>AI伴读</p> <div>-->
<!--                <div class="congig_right">   <el-radio v-model="bookConfig.supportAiMate" :label="true">支持AI伴读</el-radio>-->
<!--                  <el-radio v-model="bookConfig.supportAiMate" :label="false">不支持AI伴读</el-radio></div>-->
<!--                <p class="tips">-->
<!--                  说明：配置用户是否可以使用AI伴读-->
<!--                </p>-->
<!--              </div>-->
<!--            </div>-->
          </div>
          <el-button class="submit_button" type="primary" @click="subMitConfig()">确定</el-button>
        </div>
        <div v-if="tabindex===3" class="right">
          <div class="right_top">
            <div class="title">审核记录</div>
            <div class="review-timeline">
              <div class="timeline-item" :class="{'success': hasReachedStatus('PUBLISHED'), 'fail': getStatusByType('PUBLISHED') && getStatusByType('PUBLISHED').otherData && !getStatusByType('PUBLISHED').otherData.isPass}">
                <div class="status-dot">
                  <i v-if="hasReachedStatus('PUBLISHED')" class="el-icon-check"></i>
                </div>
                <div class="status-content">
                  <div class="status-title">出版</div>
                  <el-popover
                    v-if="getStatusByType('PUBLISHED') && getStatusByType('PUBLISHED').otherData"
                    placement="right"
                    width="300"
                    trigger="click"
                  >
                    <div class="review-detail-content">
                      <div v-for="(reviewer, index) in getStatusByType('PUBLISHED').otherData.reviewUserList" :key="index" class="reviewer-item">
                        <div class="reviewer-role">{{ getRoleText(reviewer.role) }}</div>
                        <div class="reviewer-status" :class="reviewer.reviewStatus.toLowerCase()">
                          {{ getReviewStatusText(reviewer.reviewStatus) }}
                        </div>
                        <div v-if="reviewer.reviewOpinion" class="reviewer-opinion">
                          <div class="opinion-label">审核意见：</div>
                          <div class="opinion-content">{{ reviewer.reviewOpinion }}</div>
                        </div>
                      </div>
                    </div>
                    <el-button slot="reference" type="text" class="detail-btn">查看详情</el-button>
                  </el-popover>
                </div>
              </div>
              <div class="timeline-item" :class="{'success': hasReachedStatus('PUBLISHER_PROOFREAD'), 'fail': getStatusByType('PUBLISHER_PROOFREAD') && getStatusByType('PUBLISHER_PROOFREAD').otherData && !getStatusByType('PUBLISHER_PROOFREAD').otherData.isPass}">
                <div class="status-dot">
                  <i v-if="hasReachedStatus('PUBLISHER_PROOFREAD')" class="el-icon-check"></i>
                </div>
                <div class="status-content">
                  <div class="status-title">校对</div>
                  <el-popover
                    v-if="getStatusByType('PUBLISHER_PROOFREAD') && getStatusByType('PUBLISHER_PROOFREAD').otherData"
                    placement="right"
                    width="300"
                    trigger="click"
                  >
                    <div class="review-detail-content">
                      <div v-for="(reviewer, index) in getStatusByType('PUBLISHER_PROOFREAD').otherData.reviewUserList" :key="index" class="reviewer-item">
                        <div class="reviewer-role">{{ getRoleText(reviewer.role) }}</div>
                        <div class="reviewer-status" :class="reviewer.reviewStatus.toLowerCase()">
                          {{ getReviewStatusText(reviewer.reviewStatus) }}
                        </div>
                        <div v-if="reviewer.reviewOpinion" class="reviewer-opinion">
                          <div class="opinion-label">审核意见：</div>
                          <div class="opinion-content">{{ reviewer.reviewOpinion }}</div>
                        </div>
                      </div>
                    </div>
                    <el-button slot="reference" type="text" class="detail-btn">查看详情</el-button>
                  </el-popover>
                </div>
              </div>
              <div class="timeline-item" :class="{'success': hasReachedStatus('PUBLISHER_REVIEW'), 'fail': getStatusByType('PUBLISHER_REVIEW') && getStatusByType('PUBLISHER_REVIEW').otherData && !getStatusByType('PUBLISHER_REVIEW').otherData.isPass}">
                <div class="status-dot">
                  <i v-if="hasReachedStatus('PUBLISHER_REVIEW')" class="el-icon-check"></i>
                </div>
                <div class="status-content">
                  <div class="status-title">三审</div>
                  <el-popover
                    v-if="getStatusByType('PUBLISHER_REVIEW') && getStatusByType('PUBLISHER_REVIEW').otherData"
                    placement="right"
                    width="300"
                    trigger="click"
                  >
                    <div class="review-detail-content">
                      <div v-for="(reviewer, index) in getStatusByType('PUBLISHER_REVIEW').otherData.reviewUserList" :key="index" class="reviewer-item">
                        <div class="reviewer-role">{{ getRoleText(reviewer.role) }}</div>
                        <div class="reviewer-status" :class="reviewer.reviewStatus.toLowerCase()">
                          {{ getReviewStatusText(reviewer.reviewStatus) }}
                        </div>
                        <div v-if="reviewer.reviewOpinion" class="reviewer-opinion">
                          <div class="opinion-label">审核意见：</div>
                          <div class="opinion-content">{{ reviewer.reviewOpinion }}</div>
                        </div>
                      </div>
                    </div>
                    <el-button slot="reference" type="text" class="detail-btn">查看详情</el-button>
                  </el-popover>
                </div>
              </div>
              <div class="timeline-item" :class="{'success': hasReachedStatus('SUBMIT_REVIEW'), 'fail': getStatusByType('SUBMIT_REVIEW') && getStatusByType('SUBMIT_REVIEW').otherData && !getStatusByType('SUBMIT_REVIEW').otherData.isPass}">
                <div class="status-dot">
                  <i v-if="hasReachedStatus('SUBMIT_REVIEW')" class="el-icon-check"></i>
                </div>
                <div class="status-content">
                  <div class="status-title">提交审核</div>
                  <el-popover
                    v-if="getStatusByType('SUBMIT_REVIEW') && getStatusByType('SUBMIT_REVIEW').otherData"
                    placement="right"
                    width="300"
                    trigger="click"
                  >
                    <div class="review-detail-content">
                      <div v-for="(reviewer, index) in getStatusByType('SUBMIT_REVIEW').otherData.reviewUserList" :key="index" class="reviewer-item">
                        <div class="reviewer-role">{{ getRoleText(reviewer.role) }}</div>
                        <div class="reviewer-status" :class="reviewer.reviewStatus.toLowerCase()">
                          {{ getReviewStatusText(reviewer.reviewStatus) }}
                        </div>
                        <div v-if="reviewer.reviewOpinion" class="reviewer-opinion">
                          <div class="opinion-label">审核意见：</div>
                          <div class="opinion-content">{{ reviewer.reviewOpinion }}</div>
                        </div>
                      </div>
                    </div>
                    <el-button slot="reference" type="text" class="detail-btn">查看详情</el-button>
                  </el-popover>
                </div>
              </div>
              <div class="timeline-item" :class="{'success': hasReachedStatus('NOT_SUBMITTED'), 'fail': getStatusByType('NOT_SUBMITTED') && getStatusByType('NOT_SUBMITTED').otherData && !getStatusByType('NOT_SUBMITTED').otherData.isPass}">
                <div class="status-dot">
                  <i v-if="hasReachedStatus('NOT_SUBMITTED')" class="el-icon-check"></i>
                </div>
                <div class="status-content">
                  <div class="status-title">未提交</div>
                  <el-popover
                    v-if="getStatusByType('NOT_SUBMITTED') && getStatusByType('NOT_SUBMITTED').otherData"
                    placement="right"
                    width="300"
                    trigger="click"
                  >
                    <div class="review-detail-content">
                      <div v-for="(reviewer, index) in getStatusByType('NOT_SUBMITTED').otherData.reviewUserList" :key="index" class="reviewer-item">
                        <div class="reviewer-role">{{ getRoleText(reviewer.role) }}</div>
                        <div class="reviewer-status" :class="reviewer.reviewStatus.toLowerCase()">
                          {{ getReviewStatusText(reviewer.reviewStatus) }}
                        </div>
                        <div v-if="reviewer.reviewOpinion" class="reviewer-opinion">
                          <div class="opinion-label">审核意见：</div>
                          <div class="opinion-content">{{ reviewer.reviewOpinion }}</div>
                        </div>
                      </div>
                    </div>
                    <el-button slot="reference" type="text" class="detail-btn">查看详情</el-button>
                  </el-popover>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <NormalDialog
        v-if="codeShow"
        width="30vw"
        title="邀请主编"
        :dialog-visible="codeShow"
        :append-to-body="true"
        :is-center="true"
        @closeDialog="codeShow = false"
      >
        <div class="w flex flex-col items-center">
          <div ref="bill" class="qr_code w flex flex-col items-center">
            <div class="code-share flex items-center">
              {{ name+' '+'邀请你一起编写数字教材《'+ info.title }}》，请扫描二维码接受邀请
            </div>
            <div ref="qrCodeUrl" class="qrcode"></div>
          </div>
          <div class="mt20 flex justify-center">
            <el-button type="text" class="save_qr" @click="saveImage">下载二维码</el-button>
            <el-button
              v-clipboard:copy="inviteText"
              v-clipboard:success="onCopy"
              class="save_qr"
              type="text"
            >复制链接</el-button>
          </div>
          <div class="mt20 flex justify-center">
          </div>
          <div class="invite_tips flex justify-center">
            *复制链接或保存二维码发送给其他作者
          </div>
        </div>
      </NormalDialog>
    </div>
      <div class="left"></div>
    <AICoverDialog ref='AICoverDialog' @use='userAICover'/>
  </NormalDialog>
</template>
<script>
import AICoverDialog from '@/views/publishingReview/author/components/AICoverDialog'
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { digitalBook } from '@/api/publishing'
import { getDigitalAuthorList, deleteDigitalAuthor, getDigitalBookConfig, digitalBookConfig, getDigitalBookReviewRecord } from '@/api/digital-api'
import { mapGetters } from 'vuex'
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import { addSetImgSizeModal, closeSetImgSize, setData } from '@/views/digitalbooks/editor/components/setImgSize'
export default {
  components: { NormalDialog, AICoverDialog },
  data() {
    return {
      dialogShow: false,
      ossUrl: '',
      type: 'add',
      info: { title: '', cover: '', intro: '' },
      imageKey: 0,
      tabindex: 0,
      isAdmin: false,
      authorList: [],
      codeShow: false,
      bookConfig: null,
      reviewRecord: null,
      currentDetailRecord: null,
      showDetailPopover: false
    }
  },
  computed: {
    ...mapGetters([
      'id',
      'name'
    ]),
    inviteUrl() {
      return process.env.VUE_APP_WEB_URL + '#/' + this.info.authorInviteUrl
    },
    inviteText() {
      return `${this.name} 邀请你一起编写数字教材《${this.info.title}》点击邀请链接：` + process.env.VUE_APP_WEB_URL + '#/' + this.info.authorInviteUrl
    }
  },
  mounted() {
  },
  methods: {
    subMitConfig() {
      digitalBookConfig({
        digitalBookId: this.info.id,
        contentCopy: this.bookConfig.contentCopy,
        attachFileDownload: this.bookConfig.attachFileDownload,
        supportAiMate: true,
        id: this.bookConfig.id
      }).then(res => {
        if (res.code === 200) {
          this.$message.success('修改教材配置成功')
          this.dialogShow = false
        }
      })
    },
    async _getDigitalBookConfig() {
      const { data } = await getDigitalBookConfig({
        digitalBookId: this.info.id
      })
      this.bookConfig = data
    },
    /**
     * 复制操作函数
     *
     * @returns 无返回值
     */
    onCopy() {
      this.$message.success('复制成功')
    },
    /**
     * 邀请作者
     *
     * @returns 无返回值
     */
    inviteAuthor() {
      this.codeShow = true
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl, {
            width: 200, // 二维码宽度
            height: 200, // 二维码高度
            text: this.inviteUrl, // 浏览器地址url
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.Q
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
        }
      )
    },
    /**
     * 从列表中移除作者
     *
     * @param id 作者ID
     * @returns 无返回值
     */
    removeAuthor(id) {
      this.$confirm('确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteDigitalAuthor({
          bookId: this.info.id,
          deleteUserId: id
        }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this._getDigitalAuthorList()
          }
        })
      }).catch(() => {
        console.log('')
      })
    },
    /**
     * 获取数字作者列表
     *
     * @returns 返回一个数字作者列表
     */
    async _getDigitalAuthorList() {
      const { data } = await getDigitalAuthorList({ bookId: this.info.id })
      this.authorList = data
      this.authorList.forEach(item => {
        if (item.user.id === this.id && item.tag === 'admin') {
          this.isAdmin = true
        }
      })
    },
    handleAvatarSuccess() {},
    async beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 10
      const isLt1M = file.size / 1024 < 500
      if (!isLt2M) {
        this.$message.error('上传封面大小不能超过 10MB!')
        return false
      }
      if (!isLt1M) {
        this.$message.warning('上传图片超过500Kb,可能会影响体验，请压缩图片')
      }
      if (file.type.indexOf('image/') === -1) {
        this.$message.error('只能上传图片格式')
        return
      }
      const { data } = await getFileUploadAuthor({
        mediaType: 'IMAGE',
        contentType: '',
        quantity: 1,
        fileName: file.name
      })
      this.ossUrl = data[0].ossConfig.host
      this.progress = true
      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)
      const _this = this
      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })
      if (!isLt1M) {
        setData({
          url: `${this.ossUrl}/${data[0].fileName}`
        })
        addSetImgSizeModal({
          onSubmit (data) {
            _this.ossUrl = ''
            _this.info.cover = `${data.url}`
            closeSetImgSize()
          },
          onCancel() {
            _this.ossUrl = ''
          }
        })
      } else {
        this.info.cover = `/${data[0].fileName}`
      }
      this.imageKey++
      return false
    },
    setImage() {
      const _this = this
      setData({
        url: this.ossUrl + this.info.cover
      })
      addSetImgSizeModal({
        onSubmit (data) {
          _this.ossUrl = ''
          _this.info.cover = `${data.url}`
          closeSetImgSize()
        },
        onCancel() {
          if (_this.info.cover.includes('https://')) {
            _this.ossUrl = ''
          }
        }
      })
    },
    toAIView() {
      this.$refs.AICoverDialog.show('Vertical')
    },
    userAICover(url) {
      this.ossUrl = ''
      this.info.cover = url
      this.imageKey++
    },
    subMit() {
      this.$confirm(`是否要${this.type === 'edit' ? '修改' : '创建'}教材`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(this.info.intro.length)
        this.$refs.form.validate(async valid => {
          let form = {}
          if (this.type === 'edit') {
            form = {
              apiType: 'update',
              id: this.info.id,
              title: this.info.title,
              intro: this.info.intro,
              author: this.info.author
              // cover: this.info.cover
            }
            if (this.imgUrl !== this.info.cover) {
              form.cover = this.info.cover
            }
          } else {
            form = { ...this.info, apiType: 'create' }
          }
          if (valid) {
            const { code } = await digitalBook(form)
            if (code === 200) {
              if (this.type === 'edit') {
                this.$message.success('修改成功,已提交审核或出版的教材需审核通过后更新数据')
              } else {
                this.$message.success('创建成功')
              }
              this.info = { title: '', cover: '', intro: '' }
              this.type = 'add'
              this.$emit('addSuccess')
              this.close()
            }
          } else {
            // this.$message.error('请检查表单填写')
            return false
          }
        })
      })
    },
    /**
     * 保存二维码
     *
     * @returns 无返回值
     */
    saveImage () {
      const canvasID = this.$refs.bill
      const that = this
      const a = document.createElement('a')
      html2canvas(canvasID).then((canvas) => {
        const dom = document.body.appendChild(canvas)
        dom.style.display = 'none'
        a.style.display = 'none'
        document.body.removeChild(dom)
        const blob = that.dataURLToBlob(dom.toDataURL('image/png'))
        a.setAttribute('href', URL.createObjectURL(blob))
        // 这块是保存图片操作  可以设置保存的图片的信息
        a.setAttribute('download', that.info.title + '编辑邀请' + '.png')
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(blob)
        document.body.removeChild(a)
      })
    },
    /**
     * 将data URL转换为Blob对象
     *
     * @param dataurl data URL字符串
     * @returns 返回转换后的Blob对象
     */
    dataURLToBlob (dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    close() {
      this.info = { title: '', cover: '', intro: '' }
      this.ossUrl = ''
      this.dialogShow = false
      this.isAdmin = false
      this.type = 'add'
    },
    async _getDigitalBookReviewRecord() {
      const { data } = await getDigitalBookReviewRecord({
        digitalBookId: this.info.id
      })
      this.reviewRecord = data
    },
    getStatusByType(type) {
      if (!this.reviewRecord || !Array.isArray(this.reviewRecord)) return null
      return this.reviewRecord.find(record => record.recordType === type)
    },
    hasReachedStatus(type) {
      if (!this.reviewRecord || !Array.isArray(this.reviewRecord)) return false
      return this.reviewRecord.some(record => record.recordType === type)
    },
    showDetail(record) {
      if (!record || !record.otherData || !record.otherData.reviewUserList || !record.otherData.reviewUserList.length) return
      this.currentDetailRecord = record
      this.showDetailPopover = true
    },
    getReviewStatusText(status) {
      const statusMap = {
        'UNDER_REVIEW': '审核中',
        'PASS': '通过',
        'FAILED': '不通过'
      }
      return statusMap[status] || status
    },
    getRoleText(role) {
      const roleMap = {
        'REVIEW_1': '一审',
        'REVIEW_2': '二审',
        'REVIEW_3': '三审',
        'PUBLISHER_PROOFREAD': '校对'
      }
      return roleMap[role] || '审核'
    },
    show(data = null) {
      if (data) {
        this.tabindex = 0
        this.info = data
        this.type = 'edit'
        this.imgUrl = data.cover
        this._getDigitalAuthorList()
        this._getDigitalBookConfig()
        this._getDigitalBookReviewRecord()
      }
      this.dialogShow = true
    },
    goToModify() {
      // Implement the logic to go to modify page
    }
  }
}
</script>
<style lang="scss" scoped>
.invite_tips{
  color: #EB5757;
  margin-bottom: 20px;
  font-size: var(--font-size-M);
}
.qrcode{
  ::v-deep  img{
    width: 123px !important;
    height: 123px !important;
  }
}
.code-share-title{
  font-size: var(--font-size-L);
  color: #000;
  font-weight: 600;
  margin-top: 10px;
}
.save_qr{
  font-size: var(--font-size-S);
  margin-top: 10px;
  margin-bottom: 10px;
}
.code-share{
  font-size: var(--font-size-M);
  color: #000;
  margin-top: 10px;
  width: 70%;
}
.qrcode{
  margin-top: 10px;
}
::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 185px;
    line-height: 185px;
    text-align: center;
  }
  .avatar {
    width: 150px;
    height: 185px;
    display: block;
    object-fit: cover;
  }
  .qr_code{
  padding-bottom: 20px;
  }
::v-deep .el-form-item__content {
    font-size: 12px !important;

}
::v-deep .el-form-item{
      margin-bottom: 20px !important;
    }

::v-deep .footer_left{
      width: 100px;
      height: 100%;
      border-right: 1px solid #d9d9d9;
}
::v-deep .el-button {
    font-size: 10px;
    padding: 5px;
    padding-left: 10px;
    padding-right: 10px;
    // color:red
}
::v-deep .el-dialog__footer{
      padding: 0 !important;
      height: 20px;
      position: relative;
      .left{
        width: 100px;
        height: 20px;
        position: absolute;
        left: 0;
        top:0;
        border-right: 1px solid #d9d9d9;
      }
}
::v-deep .el-dialog__body{
    padding: 0 !important;
    // border-radius: 0px 0px 10px 10px;
}
::v-deep .dialog-box{
    padding: 0;
}
.main {
    width: 100%;
    height: 400px;
    height: auto;
    margin: 0 auto;
    position: relative;
    display: flex;
    justify-content: space-between;
    .left{
      width: 100px;
      height: 100%;
      border-right: 1px solid #d9d9d9;
      .left_item{
            width: 80%;
            margin: 0 auto;
            height: 25px;
            text-align: center;
            font-size: var(--font-size-M);
            color: #000;
            line-height: 25px;
            margin-top: 10px;
            cursor: pointer;
        }
        .active{
            background: #2F80ED;
            color: #fff;
        }
    }
    .right{
      width: calc(100% - 100px);
      height: 100%;
      padding-top: 30px;
      padding-bottom: 30px;
      .right_top{
        height: 95%;
        width: 100%;
        overflow: auto;
        @include scrollBar;
        position: relative;
        .config_item{
          width: 280px;
          display: flex;
          justify-content: space-between;
          margin-top: 20px;
          margin-left: 20px;
          ::v-deep .el-radio__inner{
            width: 12px;
            height: 12px;
          }
          ::v-deep .el-radio__label{
            font-size: 10px;
            color: #000;
          }
          .congig_right{
            margin-top: 6px;
          }
          .tips{
              color: #4F4F4F;
            }
          p{
            font-size: 10px;
            font-weight: 400;
          }
        }
        .title{
          position: absolute;
          left: 20px;
          top:0px;
          color: #000;
          font-weight: bold;
          font-size: var(--font-size-XL);
        }
        .author_content{
        overflow: auto;
        width: 93%;
        margin: 0 auto;
        height: 280px;
        margin-top: 25px;
        .author_item{
          width: 100%;
          height: 60px;
          border-bottom: 1px solid #F2F2F2;
          position: relative;
          .avatar{
            width: 30px;
            height: 30px;
            border-radius: 15px;
            object-fit: cover;
            position: absolute;
            left: 15px;
            top:15px;
          }
          .author_name{
            color: #000;
            font-size: var(--font-size-M);
            width: 100px;
            white-space: nowrap;
            position: absolute;
            left: 55px;
            top:15px
          }
          .author_type{
            color: #828282;
            font-size: var(--font-size-M);
            width: 100px;
            white-space: nowrap;
            position: absolute;
            left: 255px;
            top:15px
          }
          .delete_btn{
            font-size: var(--font-size-M);
            position: absolute;
            right: 55px;
            top:18px
          }
        }
        @include scrollBar;
        }
      }
      .submit_button{
        margin-top: 10px;
        float: right;
        margin-right: 50px;
      }
    }
    .img_content {
        position: absolute;
        width: 242px;
        height: 391px;
        right: 50px;
        z-index: 10;

        img {
            width: 180px;
            height: 240px;
            object-fit: cover;
        }
    }

    p {
        font-size: 16px;
        font-weight: 700;
        color: #000000;
        display: flex;

        span {
            font-weight: 400;
        }
    }
}
.review-timeline {
  padding: 30px 40px;
  .timeline-item {
    position: relative;
    padding-left: 25px;
    margin-bottom: 35px;
    display: flex;
    align-items: flex-start;
    &:before {
      content: '';
      position: absolute;
      left: 7px;
      top: 25px;
      bottom: -35px;
      width: 2px;
      background-color: #BDBDBD;
    }
    &:last-child:before {
      display: none;
    }
    .status-dot {
      position: absolute;
      left: 0;
      top: 2px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 3px solid #BDBDBD;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        color: #fff;
        font-size: 12px;
        font-weight: bold;
        margin-top: -1px;
      }
    }
    .status-content {
      padding-left: 15px;
      .status-title {
        font-size: var(--font-size-L);
        color: #333333;
        font-weight: 500;
        line-height: 20px;
      }
      .detail-btn {
        font-size: var(--font-size-S);
        color: #2F80ED;
        margin-top: 4px;
        padding: 0;
      }
    }
    &.success {
      .status-dot {
        border-color: #27AE60;
        background: #27AE60;
      }
      .status-title {
        color: #333333;
      }
      &:before {
        background-color: #27AE60;
      }
    }
    &.fail {
      .status-dot {
        border-color: #EB5757;
        background: #EB5757;
      }
      .status-title {
        color: #333333;
      }
      &:before {
        background-color: #EB5757;
      }
    }
  }
}

.review-detail-content {
  .reviewer-item {
    padding: 12px;
    border-bottom: 1px solid #E0E0E0;
    &:last-child {
      border-bottom: none;
    }
    .reviewer-role {
      font-size: var(--font-size-M);
      font-weight: 500;
      color: #333333;
      margin-bottom: 8px;
    }
    .reviewer-status {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: var(--font-size-S);
      margin-bottom: 8px;
      &.under_review {
        background: #F2C94C;
        color: #fff;
      }
      &.pass {
        background: #27AE60;
        color: #fff;
      }
      &.failed {
        background: #EB5757;
        color: #fff;
      }
    }
    .reviewer-opinion {
      margin-top: 8px;
      font-size: var(--font-size-S);
      .opinion-label {
        color: #4F4F4F;
        margin-bottom: 4px;
      }
      .opinion-content {
        color: #333333;
        line-height: 1.5;
      }
    }
  }
}
.ai_btn{
  padding-right: 10px;
  padding-left: 10px;
  margin-left: 10px;
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 3px 3px 3px 3px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #fff;
  background: linear-gradient(to right, rgba(31, 162, 255, 1), rgba(18, 216, 250, 1), rgba(166, 255, 203, 1));
  &:hover{
    cursor: pointer;
  }
}
</style>
