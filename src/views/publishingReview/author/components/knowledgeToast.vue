<!-- eslint-disable vue/html-indent -->
<template>
    <NormalDialog
      v-if="dialogShow"
      width="100%"
      title="知识图谱"
      :dialog-visible="dialogShow"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog="close">
      <div class="flex flex-col editor-dig w">
        <iframe v-if="dialogShow" class="iframe" :src="url"></iframe>
      </div>
    </NormalDialog>
  </template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },
  props: {
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogShow: false
    }
  },
  mounted() {
  },
  methods: {
    close() {
      this.dialogShow = false
    },
    show() {
      console.log(this.url)
      this.dialogShow = true
    }
  }
}
</script>
<style lang="scss" scoped>
.editor-dig{
    width: 100%;
    height: 90vh;
    padding: 0;
}
::v-deep .el-dialog__body{
    padding: 0 !important;
}
::v-deep .dialog-box{
    padding: 0 !important;
}
::v-deep .el-dialog__footer{
    display: none;
}
::v-deep .el-dialog__body{
    border-radius: 0 0 10px 10px;
}
.iframe{
    width: 100%;
    height: 100%;
    box-sizing: border-box !important;
    overflow: hidden;
    border: none !important;
}
</style>

