<template>
  <div class='no-conversion'>
    <div class="tool-item" :class="{'a-tool-item':active === 1}" @click="showAi">
      <img :src="aiTeacher" />
      AI助教
    </div>
    <div class="tool-item" :class="{'a-tool-item':active === 0}" @click="handleClick(0)">
      <img :src="active === 0 ? Apk : pk" />
      分组PK
    </div>
    <div class="tool-item" :class="{'a-tool-item':isDraw}" @click="handleDraw">
      <img :src="isDraw ? Apen : pen" />
      画笔
    </div>
    <div class="tool-item" :class="{'a-tool-item':active === 3}" @click="handleClick(3)">
      <img :src="active === 3 ? Atime : time" />
      倒计时
    </div>
    <div class="tool-item" :class="{'a-tool-item':active === 4}" @click="handleClick(4)">
      <img :src="active === 4 ? Ascreen : Screen" />
      投屏
    </div>
    <div class="tool-item" :class="{'a-tool-item':active === 2}" @click="handleClick(2)">
      <img :src="active === 2 ? Aquestion : question" />
      抽问
    </div>
    <div class="tool-item" :class="{'a-tool-item':active === 3}" @click="startAnimate">
      <img :src="active === 5 ? Ahecai : hecai" />
      喝彩
    </div>
    <div v-show="quickPkShow" class="ai-tools-pk" @click="handleQuickPk">
      <img src="@/assets/digitalbooks/tools-img/pk-icon.png" />
    </div>
    <div class='ai-tools-items'>
      <Pk v-if="active === 0" ref="pk" :unit-id="unitId" :student-course-id='studentCourseId' @close="active = -1" @quickPkReload="_getUserCatalogue" />
      <screen v-else-if="active === 4" :unit-id="unitId" @close="active = -1" />
      <lotterys v-else-if="active === 2" @close="active = -1" />
      <Time v-else-if="active === 3" @close="active = -1" />
    </div>
    <aiChat ref="chatDrawer" :AIType="'lecture'" :unit-id='unitId'/>
    <div v-if="showAnimate" class="svga-box">
      <div v-if="showSvga" class="shadow-box2"></div>
      <div id="svga-animate"></div>
      <audio ref="audioAnimate" controls="controls" hidden></audio>
    </div>
  </div>
</template>

<script>
import aiTeacher from '@/assets/digitalbooks/tools-img/ai-teacher.png'
import pk from '@/assets/digitalbooks/tools-img/pk.png'
import Apk from '@/assets/digitalbooks/tools-img/pk-active.png'
import pen from '@/assets/digitalbooks/tools-img/pen.png'
import Apen from '@/assets/digitalbooks/tools-img/pen-active.png'
import Ascreen from '@/assets/digitalbooks/tools-img/screen_active.png'
import Screen from '@/assets/digitalbooks/tools-img/screen.png'
import question from '@/assets/digitalbooks/tools-img/question.png'
import Aquestion from '@/assets/digitalbooks/tools-img/question-active.png'
import time from '@/assets/digitalbooks/tools-img/time.png'
import Atime from '@/assets/digitalbooks/tools-img/time-active.png'
import hecai from '@/assets/digitalbooks/tools-img/hecai.png'
import Ahecai from '@/assets/digitalbooks/tools-img/hecai-active.png'

import Pk from '@/views/digitalbooks/attendClass/components/tools/components/pk.vue'
import screen from '@/views/digitalbooks/attendClass/components/tools/components/screeen.vue'
import Lotterys from '@/views/digitalbooks/attendClass/components/tools/components/lotterys.vue'
import aiChat from '@/views/digitalbooks/read/component/aiChat.vue'
import Time from '@/views/digitalbooks/attendClass/components/tools/components/time.vue'

import { getUserCatalogue } from '@/api/digital-api'
import SVGA from 'svgaplayerweb'
import dianzan from '@/assets/audio/dianzan.mp3'
import guzhang from '@/assets/audio/guzhang.mp3'

export default {
  components: {
    Pk,
    screen,
    Lotterys,
    aiChat,
    Time
  },
  props: {
    unitId: {
      type: [String, Number],
      default: 0
    },
    bookId: {
      type: [String, Number],
      default: 0
    },
    studentCourseId: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      active: -1,
      isDraw: false,
      pk,
      Apk,
      pen,
      Apen,
      Ascreen,
      Screen,
      question,
      Aquestion,
      aiTeacher,
      time,
      Atime,
      hecai,
      Ahecai,
      showAnimate: false,
      showSvga: false,
      svgaFiles: [
        { 'name': '点赞', 'url': 'https://static.bingotalk.cn/courses/courseware/63ec9da875703.svga', 'music': dianzan },
        { 'name': '鼓掌', 'url': 'https://static.bingotalk.cn/courses/courseware/63ec9f64e508a.svga', 'music': guzhang }
      ],
      groupNum: 0,
      quickPkShow: false
    }
  },
  methods: {
    showAi() {
      this.$refs.chatDrawer.open()
    },
    handleClick (val) {
      this.active = val
    },
    handleDraw () {
      this.isDraw = !this.isDraw
      this.$emit('handleActive', this.isDraw)
    },
    closeDraw () {
      this.isDraw = false
    },
    async _getUserCatalogue () {
      const { data } = await getUserCatalogue({
        studentCourseId: this.studentCourseId,
        catalogueId: this.unitId
      })
      if (data && data.groupNum) {
        this.groupNum = data.groupNum
        this.quickPkShow = true
      } else {
        this.quickPkShow = false
      }
    },
    handleQuickPk () {
      this.active = 0
      this.$nextTick(() => {
        this.$refs.pk.active = 'pkDetail'
        this.$refs.pk.handelGroupItem(this.groupNum)
      })
    },
    startAnimate () {
      const self = this
      const randomIndex = Math.floor(Math.random() * this.svgaFiles.length)
      const item = this.svgaFiles[randomIndex]
      self.showAnimate = true
      this.$nextTick(() => {
        const audio = this.$refs.audioAnimate
        audio.volume = 0.1
        audio.src = item.music
        var parser = new SVGA.Parser('#svga-animate')
        var player = new SVGA.Player('#svga-animate')
        player.loops = 1
        player.onFinished(() => {
          self.showSvga = false
          self.showAnimate = false
          audio.pause()
          player.clear()
        })
        parser.load(
          item.url,
          function (videoItem) {
            self.showSvga = true
            audio.play()
            player.setContentMode('Fill')
            player.setVideoItem(videoItem)
            player.startAnimation()
          },
          function (error) {
            console.log(error)
          }
        )
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.no-conversion{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  display: flex;
  flex-direction: column;
  gap: 5px;
  .a-tool-item {
    color: #2D9CDB;
  }
  .tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    position: relative;
    margin: 5px;
    img {
      width: 24px;
      height: 24px;
      margin-bottom: 5px;
    }
  }

  .ai-tools-items {
    position: absolute;
    right: 85px;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
  }
  .svga-box{
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba($color: #000000, $alpha: 0.8);

    .shadow-box2 {
      width: 100%;
      height: 100%;
      // background-color: rgba($color: #000000, $alpha: 0.8);
    }

    #svga-animate {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
  .ai-tools-pk {
    position: absolute;
    right: 1px;
    bottom: 460px;
    display: inline-block;
    width: 70px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    img {
      width: 58px;
      height: 46px;
    }
  }
}
</style>
