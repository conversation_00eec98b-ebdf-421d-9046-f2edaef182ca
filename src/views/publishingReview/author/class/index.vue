<template>
  <!-- 课时选择 -->
  <div class="task-class">
    <div class="head-box">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        教材讲义
      </div>
      <!-- <el-button class="button" type="primary" @click="$refs.addClass.show()">预览</el-button> -->
    </div>
    <p class="tips">*课件或者教案文件最大不能超过200MB</p>
    <div class="task-content">
      <div class="task-box">
        <div class="tbody-box">
          <div v-for="(item,index) in list" :key="item.id" class="table-body">
            <div class="tab">{{ index+1 }}</div>
            <div class="title">{{ item.title }}</div>
            <!-- <el-button class="button3" type="primary" plain @click="$refs.classList.show(item)">关联教材</el-button> -->
            <el-dropdown class="button2">
              <el-button class="button4" type="primary" plain>上传课件或教案</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-upload
                    action="''"
                    :before-upload="(file)=>beforeUpload(file,item,'DIGITAL_TEACH_COURSEWARE')"
                    :show-file-list="false"
                    :multiple="true"
                    accept=".ppt,.pptx"
                  >
                    <!-- <div v-if="!progress" class="classpro-btn">上传资源</div> -->
                    <div>上传课件(PPT文件)</div>
                  </el-upload>
                </el-dropdown-item>
                <el-dropdown-item>    <el-upload
                  action="''"
                  :before-upload="(file)=>beforeUpload(file,item,'DIGITAL_TEACH_PLAN')"
                  :show-file-list="false"
                  :multiple="true"
                  accept=".pdf"
                >
                  <!-- <div v-if="!progress" class="classpro-btn">上传资源</div> -->
                  <div>上传教案(PDF文件)</div>
                </el-upload></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button class="button1" type="primary" plain @click="handleEdit(item)">编辑</el-button>
            <!-- <el-button class="button" type="danger" plain @click="deleteTask(item.id)">删除</el-button> -->
            <div v-if="item.coursecommUnitResourceList" class="resource_list">
              <div v-for="(res,index1) in item.coursecommUnitResourceList" :key="index1" class="resource_item">
                <el-tag
                  :type="res.resourceType === 'DIGITAL_TEACH_COURSEWARE' ? 'danger' :'warning'"
                  size="mini"
                >
                  {{ res.resourceType === 'DIGITAL_TEACH_COURSEWARE' ? '课件' :'教案' }}
                </el-tag>
                <span class="resource_name" @click="openResource(res)">{{ res.mediaFile.fileName+'.'+res.mediaFile.expendType }}</span>
                <!-- <i class="el-icon-video-play open" @click="openResource(res)"></i> -->
                <i class="el-icon-error close" @click="deleteResource(res)"></i>
              </div>
            </div>
          </div>
          <Empty v-if="list && list.length === 0" :msg="'暂无数据'" />
        </div>
      </div>
    </div>
    <addClassPop ref="addClass" @addSuccess="_getBookCatalogue" />
    <classList ref="classList" @refresh="_getBookCatalogue" />
    <div v-if="resourceShow" class="pre">
      <openResource ref="openResource" @back="resourceShow=false" />
    </div>
  </div>
</template>

<script>
import { getBookCatalogueByVersionForLesson, deleteBookCatalogue, addResource, deleteResource, dragCatalogue } from '@/api/digital-api.js'
import Empty from '@/components/classPro/Empty/index.vue'
import addClassPop from './components/addClassPop.vue'
import axios from 'axios'
import classList from './components/classList.vue'
import { getFileUploadAuthor } from '@/api/user-api'
import openResource from './components/openPdf.vue'
export default {
  components: { Empty, addClassPop, classList, openResource },
  data () {
    return {
      bookTitle: '',
      bookId: 0,
      studentCourseId: 0,
      visible: false,
      list: [],
      progressShow: false,
      selectTaskInfo: null,
      detailShow: false,
      taskId: '',
      resourceShow: false,
      token: ''
    }
  },
  mounted () {
    this.bookId = this.$route.query && this.$route.query.bookId
    this.token = this.$route.query && 'Bearer ' + this.$route.query.token
    this._getBookCatalogue()
  },
  methods: {
    openResource(item) {
      this.resourceShow = true
      this.$nextTick(() => {
        this.$refs.openResource.show(item)
      })
    },
    deleteResource(item) {
      this.$confirm('确定删除资源吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deleteResource({ coursecommUnitResourceId: item.id }, { authorization: this.token })
        this.$message.success('删除成功')
        this._getBookCatalogue()
      }).catch(() => {

      })
    },
    async beforeUpload (file, item, type) {
      if (type === 'DIGITAL_TEACH_COURSEWARE' && item.coursecommUnitResourceList && item.coursecommUnitResourceList.filter(item => { return item.resourceType === 'DIGITAL_TEACH_COURSEWARE' }).length >= 1) {
        this.$message.warning('课件最多只能上传一个')
        return Promise.reject()
      } else if (type === 'DIGITAL_TEACH_PLAN' && item.coursecommUnitResourceList && item.coursecommUnitResourceList.filter(item => { return item.resourceType === 'DIGITAL_TEACH_PLAN' }).length >= 1) {
        this.$message.warning('教案最多只能上传一个')
        return Promise.reject()
      }
      if (file.size >= 209715200) {
        this.$message.warning('上传文件不能超过200M')
        return Promise.reject()
      }
      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }

      const { data } = await this.getOssSign(mediaType, file.name)
      this.ossUrl = data[0].ossConfig.host
      this.progress = true
      const filename = file.name

      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)

      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })

      await addResource({
        sourceId: item.id,
        resourceType: type
      }, [{
        size: Math.floor(file.size / 1024),
        type: mediaType,
        fileName: filename.substring(0, filename.lastIndexOf('.')),
        url: data[0].fileName,
        expendType: filename.substring(filename.lastIndexOf('.') + 1)
      }], { authorization: this.token }
      )
      this.$message.success('上传成功')
      this._getBookCatalogue()
      return Promise.reject()
    },
    async getOssSign (mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    handleEdit(item) {
      this.$refs.addClass.show({ ...item })
    },
    onChangeDrag(item) {
      console.log(item)
      dragCatalogue({
        catalogueId: item.moved.element.id,
        referCatalogueId: item.moved.newIndex === 0 ? 0 : this.list[item.moved.newIndex - 1 ].id,
        position: 'AFTER'
      }, { authorization: this.token })
    },
    back () {
      if (this.$route.query.token.indexOf('Bearer') === -1) {
        window.close()
        return
      }
      this.$router.push('/author/home')
    },
    async _getBookCatalogue () {
      const { data } = await getBookCatalogueByVersionForLesson({
        bookId: this.bookId
      }, { authorization: this.token })
      this.list = data
    },
    async deleteTask (id) {
      this.$confirm('确定删除课时吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deleteBookCatalogue({ catalogueId: id })
        this.$message.success('删除成功')
        this._getBookCatalogue()
      }).catch(() => {

      })
    },
    handleDetail (item) {
      this.taskId = item.id
      this.detailShow = true
    }
  }
}
</script>

  <style lang="scss" scoped>
  .pre{
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    overflow: auto;
    background: #E9F2FF;
  }
  .task-class {
    width: 100%;
    height: 100%;
    padding: 10px;
    position: relative;
    .tips{
      position: absolute;
      right: 10px;
      top:40px;
      color: red;
      font-size: 10px;
    }
    .head-box {
      height: 40px;
      display: flex;
      width: 100%;
      align-items: center;
      margin-bottom: 10px;
      position: relative;
      .button{
        width: 80px;
        height: 30px;
        font-size: 12px;
        line-height: 0px;
        padding: 0;
        position: absolute;
        font-size: 14px;
        right: 0;
        }
      .share {
        position: absolute;
        right: 40px;
        font-size: 12px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        img {
          width: 20px;
          height: 20px;
        }
      }

      .back {
        width: 20px;
        height: 20px;
        object-fit: cover;
        cursor: pointer;
      }

      .head-title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
        margin-left: 10px;
      }
    }

    .task-content {
      padding: 10px 0;
      height: calc(100% - 40px);
      width: 100%;

      .task-title {
        height: 40px;
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
        display: flex;
        justify-content: space-between;

        .classpro-btn {
          padding: 5px 10px;
          height: 30px;
          margin-top: -5px;
        }
      }

      .task-box {
        border-radius: 5px;
        background: #ffffff;
        width: 100%;
        height: 100%;
        padding: 20px;
        box-sizing: border-box;

        .tbody-box {
          width: 100%;
          height: 100%;
          overflow-y: auto;
          @include scrollBar;
        }
        .table-body:nth-child(1){
            margin-top: 0;
        }
        .table-body {
          height: 144px;
          border: 1px solid #E5E5E5;
          width: 100%;
          display: flex;
          margin-top: 10px;
          background:#FFFFFF;
          border-radius: 5px;
          position: relative;
          .resource_list{
            width: 98%;
            height: 60px;
            background: #F4F4F4;
            border-radius: 5px;
            margin: 70px auto;
            padding: 5px;
            .resource_item{
                .resource_name{
                    margin-left: 20px;
                    display: inline-block;
                    min-width: 120px;
                    font-size: 12px;
                    cursor: pointer;
                }
                .resource_name:hover{
                    color: #2F80ED;
                }
                .close{
                    font-size: 14px;
                    cursor: pointer;
                    margin-left: 10px;
                    color:grey
                }
                .open{
                    font-size: 14px;
                    cursor: pointer;
                    margin-left: 20px;
                    color:#2F80ED;
                }
            }
            .resource_item:nth-child(2){
              margin-top: 9px;
            }
          }
        .tab{
          font-size: 12px;
          font-weight: 600;
          position: absolute;
          left: 20px;
          top:20px
        }
        .title{
        font-size: 12px;
          font-weight: 400;
          position: absolute;
          left: 100px;
          top:20px
        }
      .button{
          width: 100px;
          height: 30px;
          font-size: 12px;
          line-height: 0px;
          padding: 0;
          position: absolute;
          right: 50px;
          top:20px
      }
      .button1{
          width: 100px;
          height: 30px;
          font-size: 12px;
          line-height: 0px;
          padding: 0;
          position: absolute;
          right:30px;
          top:20px
      }
      .button2{
          width: 100px;
          height: 30px;
          font-size: 12px;
          line-height: 0px;
          padding: 0;
          position: absolute;
          right: 140px;
          top:20px
      }
      .button3{
          width: 100px;
          height: 30px;
          font-size: 12px;
          line-height: 0px;
          padding: 0;
          position: absolute;
          right: 380px;
          top:20px
      }
      .button4{
        width: 100px;
          height: 30px;
          font-size: 12px;
          line-height: 0px;
          padding: 0;
      }
        }
      }
    }

    .task-pop {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #E9F2FF;
    }
  }
  </style>
