<!-- eslint-disable vue/html-indent -->
<template>
    <NormalDialog
      v-if="dialogShow"
      width="1200px"
      title="关联章节"
      :dialog-visible="dialogShow"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog="close"
  >
      <div class="flex flex-col editor-dig w">
        <div class="main">
            <el-tree
                ref="tree"
                :data="treeList"
                show-checkbox
                check-strictly
                node-key="id"
                default-expand-all
                :default-checked-keys="checkList"
                :props="treeProps"
/>
          </div>
        </div>
        <template #footer>
      <div class="edu-btn" @click="subMit">确定</div>
    </template>
    </NormalDialog>
  </template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { getBookCatalogueByVersion, editBookCatalogue } from '@/api/digital-api.js'
export default {
  components: { NormalDialog },
  data() {
    return {
      dialogShow: false,
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      bookId: this.$route.query.bookId,
      treeList: [],
      checkList: [],
      CatalogueId: '',
      token: ''
    }
  },
  mounted() {
    this.token = this.$route.query && 'Bearer ' + this.$route.query.token
  },
  methods: {
    async show(item) {
      const { data } = await getBookCatalogueByVersion({ bookId: this.bookId }, { authorization: this.token })
      this.treeList = data
      this.checkList = item.teachCatalogueIds ? item.teachCatalogueIds.split(',') : []
      this.CatalogueId = item.id
      this.dialogShow = true
    },
    close() {
      this.dialogShow = false
    },
    async subMit() {
      const selectedArr = this.$refs.tree.getCheckedNodes().map(item => {
        return item.id
      })
      await editBookCatalogue({ id: this.CatalogueId, teachCatalogueIds: selectedArr.join(',') }, { authorization: this.token })
      this.dialogShow = false
      this.$emit('refresh')
    }
  }
}
</script>
  <style lang="scss" scoped>
  .invite_tips{
    color: #EB5757;
    margin-bottom: 20px;
    font-size: var(--font-size-M);
  }
  .qrcode{
    ::v-deep  img{
      width: 123px !important;
      height: 123px !important;
    }
  }
  .code-share-title{
    font-size: var(--font-size-L);
    color: #000;
    font-weight: 600;
    margin-top: 10px;
  }
  .save_qr{
    font-size: var(--font-size-S);
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .code-share{
    font-size: var(--font-size-M);
    color: #000;
    margin-top: 10px;
    width: 70%;
  }
  .qrcode{
    margin-top: 10px;
  }
  ::v-deep .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 150px;
      height: 185px;
      line-height: 185px;
      text-align: center;
    }
    .avatar {
      width: 150px;
      height: 185px;
      display: block;
      object-fit: cover;
    }
    .qr_code{
    padding-bottom: 20px;
    }
  ::v-deep .el-form-item__content {
      font-size: 12px !important;

  }
  ::v-deep .el-form-item{
        margin-bottom: 20px !important;
      }
  ::v-deep .el-button {
      font-size: 10px;
      padding: 5px;
      padding-left: 10px;
      padding-right: 10px;
      // color:red
  }

  .main {
      width: 100%;
      height: 400px;
      height: auo;
      margin: 0 auto;
      position: relative;
      padding: 10px;
      overflow: auto;
      @include scrollBar;
      justify-content: space-between;

  }
  </style>
