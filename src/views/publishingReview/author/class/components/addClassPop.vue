<!-- eslint-disable vue/html-indent -->
<template>
    <NormalDialog
      v-if="dialogShow"
      width="1200px"
      :title="type === 'add' ? '添加课时' : '编辑课时'"
      :dialog-visible="dialogShow"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog="close"
  >
      <div class="flex flex-col editor-dig w">
        <div class="main">
            <div class="right_top">
              <el-form ref="form" label-width="10vw" size="mini" :model="info">
            <el-form-item
            label="课时名称："
            prop="title"
  >
              <el-input v-model="info.title" disabled placeholder="请输入课时名称" />
            </el-form-item>
            <el-form-item
                label="封面："
                prop="background"
                :rules="[
            { required: true, message: '请上传封面'},
          ]"
  >
              <el-upload
                class="avatar-uploader"
                action=""
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                accept=".png, .jpg, .jpeg"
  >
                <img v-if="info.background" :src="ossUrl+info.background" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            支持png、jpg、jpeg格式图片，宽高比4:3
            </el-form-item>
            <!-- <el-form-item label="课时说明：">
              <el-input v-model="info.subtitle" type="textarea" :rows="4" placeholder="请输入课时说明" />
            </el-form-item> -->
          </el-form>
            </div>
          </div>
        </div>
        <template #footer>
      <div class="edu-btn" @click="subMit">确定</div>
    </template>
    </NormalDialog>
  </template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { getFileUploadAuthor } from '@/api/user-api'
import { editBookCatalogue } from '@/api/digital-api.js'
import axios from 'axios'
export default {
  components: { NormalDialog },
  data() {
    return {
      dialogShow: false,
      ossUrl: '',
      type: 'add',
      info: { title: '', background: '', subtitle: '', type: 'LESSON' },
      tabindex: 0,
      isAdmin: false,
      authorList: [],
      codeShow: false,
      bookId: this.$route.query.bookId,
      token: ''
    }
  },
  mounted() {
    this.token = this.$route.query && 'Bearer ' + this.$route.query.token
  },
  methods: {
    handleAvatarSuccess() {},
    async beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('上传封面大小不能超过 10MB!')
      }
      if (file.type.indexOf('image/') === -1) {
        this.$message.error('只能上传图片格式')
        return
      }
      const { data } = await getFileUploadAuthor({
        mediaType: 'IMAGE',
        contentType: '',
        quantity: 1,
        fileName: file.name
      })
      this.ossUrl = data[0].ossConfig.host
      this.progress = true
      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)
      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })
      this.info.background = '/' + data[0].fileName
      return false
    },
    subMit() {
      this.$confirm(`是否要${this.type === 'edit' ? '修改' : '创建'}封面`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.form.validate(async valid => {
          let form = {}
          if (this.type === 'edit') {
            form = {
              id: this.info.id,
              // title: this.info.title,
              background: this.info.background
              // subtitle: this.info.subtitle
            }
            if (this.imgUrl !== this.info.cover) {
              form.background = this.info.background
            }
          } else {
            form = { ...this.info, bookId: this.bookId }
          }
          if (valid) {
            const { code } = await editBookCatalogue(form, { authorization: this.token })
            if (code === 200) {
              if (this.type === 'edit') {
                this.$message.success('修改成功')
              } else {
                this.$message.success('创建成功')
              }
              this.info = { title: '', cover: '', intro: '' }
              this.type = 'add'
              this.$emit('addSuccess')
              this.close()
            }
          } else {
            // this.$message.error('请检查表单填写')
            return false
          }
        })
      })
    },
    close() {
      this.info = { title: '', background: '', subtitle: '', type: 'LESSON' }
      this.ossUrl = ''
      this.dialogShow = false
      this.isAdmin = false
      this.type = 'add'
    },
    async show(data = null) {
      if (data) {
        this.ossUrl = ''
        this.info = data
        this.type = 'edit'
        this.imgUrl = data.background
      }
      this.dialogShow = true
    }
  }
}
</script>
  <style lang="scss" scoped>
  .invite_tips{
    color: #EB5757;
    margin-bottom: 20px;
    font-size: var(--font-size-M);
  }
  .qrcode{
    ::v-deep  img{
      width: 123px !important;
      height: 123px !important;
    }
  }
  .code-share-title{
    font-size: var(--font-size-L);
    color: #000;
    font-weight: 600;
    margin-top: 10px;
  }
  .save_qr{
    font-size: var(--font-size-S);
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .code-share{
    font-size: var(--font-size-M);
    color: #000;
    margin-top: 10px;
    width: 70%;
  }
  .qrcode{
    margin-top: 10px;
  }
  ::v-deep .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
      border-color: #409EFF;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 200px;
      height: 150px;
      line-height: 150px;
      text-align: center;
    }
    .avatar {
      width: 200px;
      height: 150px;
      display: block;
      object-fit: cover;
    }
    .qr_code{
    padding-bottom: 20px;
    }
  ::v-deep .el-form-item__content {
      font-size: 12px !important;

  }
  ::v-deep .el-form-item{
        margin-bottom: 20px !important;
      }
  ::v-deep .el-button {
      font-size: 10px;
      padding: 5px;
      padding-left: 10px;
      padding-right: 10px;
      // color:red
  }

  .main {
      width: 100%;
      height: 400px;
      height: auo;
      margin: 0 auto;
      position: relative;
      padding: 10px;
      overflow: auto;
      @include scrollBar;
      justify-content: space-between;

  }
  </style>
