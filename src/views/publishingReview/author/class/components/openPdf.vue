<template>
  <!-- 课时选择 -->
  <div class="task-class">
    <div class="head-box">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        课件详情
      </div>
    </div>

    <div class="task-content">
      <div class="task-box">
        <Ppt v-if="type==='ppt'" ref="ppt" />
        <Pdf v-if="type==='pdf'" ref="pdf" />
      </div>
    </div>
  </div></template>

<script>
import Ppt from '../../../../digitalbooks/attendClass/components/backpack/ppt.vue'
import Pdf from '../../../../digitalbooks/attendClass/components/backpack/pdf.vue'
export default {
  components: { Ppt, Pdf },
  data () {
    return {
      type: '',
      resource: null
    }
  },
  mounted () {
  },
  methods: {
    show(item) {
      this.resource = item
      if (item.resourceType === 'DIGITAL_TEACH_COURSEWARE') {
        this.type = 'ppt'
        this.$nextTick(() => {
          this.$refs.ppt._getGenerateToken([item], this.$route.query.token)
        })
      } else if (item.resourceType === 'DIGITAL_TEACH_PLAN') {
        this.type = 'pdf'
        this.$nextTick(() => {
          this.$refs.pdf._loadFile(item.mediaFile.url, `${item.mediaFile.fileName}.${item.mediaFile.expendType}`)
        })
      }
    },
    back() {
      this.$emit('back')
    }
  }
}
</script>
    <style lang="scss" scoped>
    .task-class {
      width: 100%;
      height: 100%;
      padding: 5px;

      .head-box {
        height: 10;
        display: flex;
        width: 100%;
        align-items: center;
        margin-bottom: 10px;
        position: relative;
        .button{
          width: 80px;
          height: 30px;
          font-size: 12px;
          line-height: 0px;
          padding: 0;
          position: absolute;
          font-size: 14px;
          right: 0;
          }
        .share {
          position: absolute;
          right: 40px;
          font-size: 12px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          img {
            width: 20px;
            height: 20px;
          }
        }

        .back {
          width: 20px;
          height: 20px;
          object-fit: cover;
          cursor: pointer;
        }

        .head-title {
          color: #000;
          font-size: var(--font-size-L);
          font-weight: 500;
          margin-left: 10px;
        }
      }

      .task-content {
        padding: 10px 0;
        height: calc(100% - 20px);
        width: 100%;
        .task-title {
          height: 40px;
          color: #000;
          font-size: var(--font-size-L);
          font-weight: 500;
          display: flex;
          justify-content: space-between;

          .classpro-btn {
            padding: 5px 10px;
            height: 30px;
            margin-top: -5px;
          }
        }
        .task-box {
          border-radius: 5px;
          background: #ffffff;
          width: 100%;
          height: 100%;
          padding: 20px;
          box-sizing: border-box;

        }
      }

      .task-pop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #E9F2FF;
      }
    }
    </style>
