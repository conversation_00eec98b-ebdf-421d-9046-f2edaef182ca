<template>
  <div class="editor-dig">
    <div class="read_main">
      <div class="read_menu">
        <el-tree
          v-if="showMenu"
          ref="treeRef"
          :data="menuList"
          :props="treeProps"
          node-key="id"
          :current-node-key="currentKey"
          highlight-current
          default-expand-all
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        >
          <div slot-scope="{ node, data }" class="menu_item">
            <template v-if="data.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CATALOGUE'">
              <div class="item_title">
                <div :title="data.title" class="w article-singer-container">
                  {{ data.title }}
                </div>
              </div>
            </template>
            <template v-else>
              <div class="menu_content">
                <div class="content_view">
                  <div v-if="data.contents && data.contents.length > 0" class="html_view" v-html="data.contents[0].data"></div>
                </div>
              </div>
            </template>
          </div>
        </el-tree>
      </div>
      <div class="read_content">
        <div @click="readFunc" class='read_view' :style='contentStyle'>
          <div v-html='html' class='read_view_content'>

          </div>
        </div>
        <div class='read_option' v-if='type === "class" '>
          <ReadTools ref='tools' :unit-id='catalogueId' :student-course-id='studentCourseId' :book-id='bookId' @handleActive='handleActive'/>
        </div>
      </div>
    </div>
    <div class="read_footer">
      <div class="btn_box">
        <img class="btn" src="@/assets/digitalbooks/ppt-back.svg" @click="handleBack" />
      </div>
      <div class="btn_box">
        <img class="btn" src="@/assets/digitalbooks/ppt-left.svg" @click="handleLeft" />
        <span style="margin-left: 10px;margin-right: 10px">
          {{`${currentIndex}/${contentList.length}`}}
        </span>
        <img class="btn" src="@/assets/digitalbooks/ppt-right.svg" @click="handleRight" />
      </div>
      <div class="btn_box">
        <el-popover
          ref='popover'
          placement="top"
          width="200"
          trigger="click"
          :content="currentNode ? currentNode.subtitle : ''">
          <div class='popover_content'>
            <template v-if=" currentNode && currentNode.subtitle !== ''">
              <div class='popover_title'>
                本页备注：
              </div>
              <div class='popover_text'>
                {{ currentNode.subtitle }}
              </div>
            </template>
            <div class='popover_text' v-else>
              暂无备注!
            </div>
          </div>
        </el-popover>
        <img class="btn" v-popover:popover src="@/assets/digitalbooks/beizhu.svg"/>
        <img class="btn" src="@/assets/digitalbooks/ziyuan.svg" @click="handleResources" />
        <img class="btn" src="@/assets/digitalbooks/jiaocai.svg" @click="handleText" />
        <img v-show="!isFullAllScreen" class="btn" src="@/assets/digitalbooks/full.svg" @click="fullScreen" />
        <img v-show="isFullAllScreen" class="btn" src="@/assets/digitalbooks/unfull.svg" @click="fullScreen" />
      </div>
    </div>
    <Draw v-show="toolActive" ref="draw" :active="toolActive" @closeDraw="toolActive = false; $refs.tools.closeDraw()" />

    <imgGroupPop ref="images" :info="imgListInfo" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <tipsPop ref="tips" :position="tipsPosition" :info="tipsInfo" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <doTest ref="doTest" :test-id="testId" :ids="ids" />
    <analysisTest ref="analysisTest" :test-id="testId" :ids="ids" />
    <doExcelTraing ref="excelRraing" :student-course-id="studentCourseId" />
    <doAiTraing ref="doAiTraing" :student-course-id="studentCourseId" />
  </div>
</template>

<script>
import { getCloudLectureDetail } from '@/api/cloudLecture-api'
import screenfull from 'screenfull'
import ReadTools from '@/views/publishingReview/author/components/readTools'
import Draw from '@/views/digitalbooks/attendClass/components/tools/components/draw'
import imgGroupPop from '@/views/digitalbooks/read/component/imgGroupPop.vue'
import videoCardPop from '@/views/digitalbooks/read/component/videoPop.vue'
import tipsPop from '@/views/digitalbooks/read/component/tipsPop.vue'
import officeView from '@/views/digitalbooks/editor/components/officeView.vue'
import doTest from '@/views/digitalbooks/editor/components/doTest.vue'
import analysisTest from '@/views/digitalbooks/editor/components/analysisTest.vue'
import doExcelTraing from '@/views/digitalbooks/editor/components/doExcelTraing.vue'
import doAiTraing from '@/views/digitalbooks/editor/components/doAiTraing.vue'
import { Notification } from 'element-ui'
import { throttle } from '@/utils/index'
import { saveAs } from 'file-saver'
import { getAuthorToken } from '@/utils/auth'
import { getSqlPlatformToken } from '@/api/training-api'
import { getBook } from '@/api/digital-api'

export default {
  components: { ReadTools, Draw, imgGroupPop, videoCardPop, tipsPop, officeView, doTest, analysisTest, doExcelTraing, doAiTraing },
  data() {
    return {
      catalogueId: this.$route.query ? this.$route.query.catalogueId : '',
      menuList: [],
      contentList: [],
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      currentKey: null,
      showMenu: true,
      showPopover: true,
      html: '',
      htmlId: 0,
      isFullAllScreen: false,
      currentIndex: 1,
      currentNode: null,
      type: this.$route.query && this.$route.query.type ? this.$route.query.type : 'preview',
      toolActive: false,
      actionFn: null,

      imgListInfo: null,
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      },
      tipsPosition: {
        top: 0,
        left: 0
      },
      tipsInfo: {
        keyword: '',
        content: ''
      },
      officeUrl: '',
      token: '',
      testId: '0',
      ids: '',
      openFlag: false,
      studentCourseId: 0,
      bookId: '',
      previousWidth: 0,
      previousHeight: 0,
      scale: 1,
      translateX: 0,
      translateY: 0
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.actionFn)
  },
  mounted() {
    this.bookId = this.$route.query.bookId ?? ''
    if (this.type === 'class') {
      this.getBookInfo()
    } else {
      this.getDetailInfo()
    }
    this.previousWidth = window.innerWidth
    this.previousHeight = window.innerHeight
    this.changeScale()
    this.token = getAuthorToken()
    this.actionFn = this.debounce(this.changeScale, 1000)
    window.addEventListener('resize', this.actionFn)
    screenfull.on('change', () => {
      if (screenfull.isFullscreen) {
        this.isFullAllScreen = true
      } else {
        this.isFullAllScreen = false
      }
    })
  },
  methods: {
    async getBookInfo() {
      const { data } = await getBook({
        bookId: this.bookId,
        scene: 'BOOK_CATALOGUE_OWN'
      }, {
        authorization: this.token
      })
      this.bookInfo = data
      this.studentCourseId = data.studentCourseId
      await this.getDetailInfo()
    },
    async getDetailInfo() {
      const { data } = await getCloudLectureDetail({
        catalogueId: this.catalogueId
      })
      this.menuList = data.childCatalogue ?? []
      this.contentList = []
      for (let i = 0; i < this.menuList.length; i++) {
        const item = this.menuList[i]
        for (let j = 0; j < item.childCatalogue.length; j++) {
          const subItem = item.childCatalogue[j]
          if (subItem.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
            this.contentList.push(subItem)
          }
        }
        if (item.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
          this.contentList.push(item)
        }
      }
      if (localStorage.getItem('contentId')) {
        this.contentList.forEach((item) => {
          if (item.id === Number(localStorage.getItem('contentId'))) {
            this.currentKey = item.id
            this.currentNode = item
            this.html = item.contents[0].data
            this.htmlId = item.contents[0].id
          }
        })
        if (!this.currentKey) {
          this.currentKey = this.contentList[0].id
          this.currentNode = this.contentList[0]
          this.html = this.contentList[0].contents[0].data
          this.htmlId = this.contentList[0].contents[0].id
        }
      } else {
        if (this.contentList.length > 0) {
          this.currentKey = this.contentList[0].id
          this.currentNode = this.contentList[0]
          this.html = this.contentList[0].contents[0].data
          this.htmlId = this.contentList[0].contents[0].id
        }
      }
      this.showMenu = false
      this.$nextTick(() => {
        this.showMenu = true
        window.MathJax.typesetPromise()

        setTimeout(() => {
          const tree = this.$refs.treeRef
          const nodeEl = tree.$el.querySelector(`.is-current`)
          if (nodeEl) {
            nodeEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
          }
        }, 100)
      })
    },

    handleNodeClick(nodeData) {
      if (nodeData.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
        this.currentKey = nodeData.id
        this.currentNode = nodeData
        if (nodeData.contents && nodeData.contents.length > 0) {
          this.html = nodeData.contents[0].data
          this.htmlId = nodeData.contents[0].id
        }
        this.$nextTick(() => {
          window.MathJax.typesetPromise()
        })
      }
    },
    handleBack() {
      this.$router.back()
    },
    handleLeft() {
      if (this.currentIndex > 1) {
        this.currentIndex -= 1
        this.currentNode = this.contentList[this.currentIndex - 1]
        this.currentKey = this.contentList[this.currentIndex - 1].id
        this.html = this.contentList[this.currentIndex - 1].contents[0].data
        this.htmlId = this.contentList[this.currentIndex - 1].contents[0].id
        this.showMenu = false
        this.$nextTick(() => {
          this.showMenu = true
          window.MathJax.typesetPromise()
          setTimeout(() => {
            const tree = this.$refs.treeRef
            const nodeEl = tree.$el.querySelector(`.is-current`)
            if (nodeEl) {
              nodeEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
            }
          }, 100)
        })
      }
    },
    handleRight() {
      if (this.currentIndex < this.contentList.length) {
        this.currentIndex += 1
        this.currentNode = this.contentList[this.currentIndex - 1]
        this.currentKey = this.contentList[this.currentIndex - 1].id
        this.html = this.contentList[this.currentIndex - 1].contents[0].data
        this.htmlId = this.contentList[this.currentIndex - 1].contents[0].id
        this.showMenu = false
        this.$nextTick(() => {
          this.showMenu = true
          window.MathJax.typesetPromise()
          setTimeout(() => {
            const tree = this.$refs.treeRef
            const nodeEl = tree.$el.querySelector(`.is-current`)
            if (nodeEl) {
              nodeEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
            }
          }, 100)
        })
      }
    },
    handleText() {
      this.$message.warning('功能即将上线！敬请期待')
    },
    handleResources() {
      this.$message.warning('功能即将上线！敬请期待')
    },
    fullScreen () {
      if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit()
      } else {
        screenfull.request()
      }
    },
    // 工具条
    handleActive (val) {
      this.toolActive = val
    },

    changeScale() {
      const domHeight = Number(document.querySelector('.read_content').clientHeight)
      const domWidth = Number(document.querySelector('.read_content').clientWidth)
      const readDom = document.querySelector('.read_view')
      const optionDom = document.querySelector('.read_option')

      const readContentDomWidth = Number(document.querySelector('.read_view_content').clientWidth)
      const readContentDomHeight = Number(document.querySelector('.read_view_content').clientHeight)

      const scaleX = domWidth / readDom.clientWidth
      const scaleY = domHeight / readDom.clientHeight
      this.scale = Math.min(scaleX, scaleY)
      // readDom.style.transform = `scale(${this.scale})`
      const scaledWidth = readDom.clientWidth * this.scale
      const scaledHeight = readDom.clientHeight * this.scale
      this.translateX = (domWidth - scaledWidth) / 2
      this.translateY = (domHeight - scaledHeight) / 2
      optionDom.style.top = `${this.translateY + (readContentDomHeight * this.scale) / 2 - Number(optionDom.clientHeight / 2)}px`
      if (domWidth > readContentDomWidth * this.scale) {
        optionDom.style.right = `0`
      } else {
        optionDom.style.left = `${this.translateX + readContentDomWidth * this.scale + 5}px`
      }
    },
    debounce(fn, delay) {
      let timer = null
      return function(...args) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          fn.apply(this, args)
        }, delay)
      }
    },
    readFunc(e) {
      if (e.target.classList.contains('img_card_button')) {
        console.log(1)
        this.imgListInfo = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.images.open()
      }
      if (e.target.classList.contains('video_button')) {
        e.preventDefault()
        this.videoInfo = {
          src: e.target.src,
          poster: e.target.poster,
          text: e.target.parentNode.getElementsByTagName('p')[1].innerText
        }
        this.$refs.videoCard.open()
      }
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        let y = item.getBoundingClientRect().top + 20
        if (window.innerHeight - e.pageY < 195) {
          y = window.innerHeight - 200
        }
        this.tipsPosition = {
          top: y,
          left: item.getBoundingClientRect().left
        }
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.show()
      }
      if (e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          const url = e.target.parentNode.children[1].innerText
          const fileName = e.target.parentNode.children[2].innerText
          const notif = Notification({
            title: fileName,
            dangerouslyUseHTMLString: true,
            message: '',
            duration: 0
          })
          throttle(function () {
            const xhr = new XMLHttpRequest()
            xhr.open('get', url)
            xhr.responseType = 'blob'
            xhr.addEventListener('progress', (e) => {
              const complete = Math.floor(e.loaded / e.total * 100)
              notif.message = complete + '%'
              if (complete >= 100) {
                notif.close()
              }
            })
            xhr.send()
            xhr.onload = function () {
              if (this.status === 200 || this.status === 304) {
                const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
                saveAs(blob, fileName)
              }
            }
          }, 2000)
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.doTest.open()
      }
      if (e.target.classList.contains('analysis_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.analysisTest.open()
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
      if (e.target.classList.contains('to_excel')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.openExcelTraining(id)
      }
      if (e.target.classList.contains('to_aiTrain')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.$refs.doAiTraing.open(id)
      }
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      if (this.getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.images.open()
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    openExcelTraining(id) {
      this.$refs.excelRraing.open(id)
    }
  },
  watch: {
    currentKey(newVal) {
      this.contentList.filter((item, index) => {
        if (newVal === item.id) {
          this.currentIndex = index + 1
        }
      })
    }
  },
  computed: {
    contentStyle() {
      return {
        transform: `translate(${this.translateX}px, ${this.translateY}px) scale(${this.scale})`,
        transformOrigin: 'top left'
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.editor-dig{
  width: 100%;
  height: 100%;
  background-color:#e5f0ff;
  box-sizing: border-box;
  .read_footer{
    width: 100%;
    height: 60px;
    margin-top: 5px;
    background-color: rgba(255, 255, 255, 0.49);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    font-weight: 500;
    font-size: 20px;
    .btn_box{
      display: flex;
      align-items: center;
    }
    .btn{
      width: 45px;
      height: 45px;
      margin: 10px 10px 0 10px;
      cursor: pointer;
    }
  }
  .read_main{
    width: 100%;
    height: calc(100% - 65px);
    display: flex;
    //justify-content: center;
    gap: 10px;
    padding: 20px 20px 0 20px;
    .read_menu{
      width: 230px;
      flex-shrink: 0;
      height: calc(100% + 5px);
      background-color: #fff;
      padding: 10px 5px;
      overflow-y: auto;
      ::v-deep .el-tree-node__content {
        height: auto;
        padding: 5px 0;
      }
      ::v-deep .el-button + .el-button {
        margin-left: 5px;
      }
      ::v-deep .el-button {
        font-size: 14px;
      }
      ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
        padding: 5px;
      }
      .menu_item{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
        .item_title{
          flex: 1;
          overflow: hidden;
          @include scrollBar;
        }
        .item_option{
          flex-shrink: 0;
        }
        .menu_content{
          width: 160px;
          height: 120px;
          display: flex;
          .content_view{
            width: 160px;
            height: 120px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.25);
            padding: 2px;
            box-sizing: border-box;
            white-space: normal;
            .html_view{
              width: 776px;
              //width: 400px;
              height: 580px;
              padding: 5px;
              transform-origin: top left;
              transform: scale(0.2);
              line-height: 1.4;
              overflow: hidden;
              box-sizing: border-box;

            }
          }
        }
      }
    }
    .read_option{
      width: 70px;
      height: auto;
      position: absolute;
      background-color: #fff;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
      border-radius: 10px;
      padding: 10px 0;
    }
    .read_content{
      //background-color: red;
      width: calc(100% - 230px);
      flex-shrink: 0;
      height: calc(100% - 20px);
      display: flex;
      position: relative;
      .read_view{
        width: 851px;
        height: 580px;
        //height: 100%;
        flex-shrink: 0;
        box-sizing: border-box;
        position: absolute;
        top: 0;
        left: 0;
        line-height: 1.4;
        .read_view_content{
          width: 776px;
          height: 100%;
          padding: 15px;
          box-sizing: border-box;
          background-color: #fff;
          box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.25);
          border-radius: 5px;
          overflow: hidden;
          position: absolute;
        }

        ::v-deep img {
          max-width: 100%;
        }
        ::v-deep video {
          width: 100%;
        }
        ::v-deep code {
          white-space: pre-wrap;
          word-break: break-all;
        }
        ::v-deep p,
        ::v-deep li {
          white-space: pre-wrap;
          word-wrap: break-word; /* 保留空格 */
        }
        ::v-deep blockquote {
          border-left: 8px solid #d0e5f2;
          padding: 10px 10px;
          margin: 10px 0;
          background-color: #f1f1f1;
        }
        ::v-deep table {
          border-collapse: collapse;
        }
        ::v-deep pre > code {
          display: block;
          padding: 10px;
        }
        ::v-deep input[type='checkbox'] {
          margin-right: 5px;
        }
        ::v-deep ul {
          display: block;
          list-style-type: disc;
          margin-block-start: 1em;
          margin-block-end: 1em;
          margin-inline-start: 0px;
          margin-inline-end: 0px;
          padding-inline-start: 40px;
        }
        ::v-deep ol {
          display: block;
          list-style-type: decimal;
          margin-block-start: 1em;
          margin-block-end: 1em;
          margin-inline-start: 0px;
          margin-inline-end: 0px;
          padding-inline-start: 40px;
        }
      }
    }
  }
}

.popover_content{
  font-size: var(--font-size-M);
  color: rgba(0, 0, 0, 1);
  .popover_title{
    font-weight: 500;
  }
  .popover_text{
    font-weight: 400;
    white-space: pre-wrap;
  }
}
</style>
