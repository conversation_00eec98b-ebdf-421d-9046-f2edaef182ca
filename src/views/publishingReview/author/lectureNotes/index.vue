<template>
  <div class="main_view">
    <div class="header_view">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        {{`${bookTitle}讲义`}}
      </div>
      <div class="head_btn_view" v-if='type === "author"'>
        <el-dropdown trigger="click">
          <i class="el-icon-more more_btn" style='margin-left: 10px'></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <span @click='generateBook'>生成数字教材</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span @click='handleInvitation'>邀请使用</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span @click='handleManagement'>模板管理</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" size="medium" icon="el-icon-plus" @click="createHours" :disabled='menuTree.length == 0'>创建课时</el-button>
        <!--        <el-button type="success" plain size="medium" style="margin-right: 12px">提交审核</el-button>-->
      </div>
    </div>
    <div class="content_view">
      <div v-show='showMenu' class="menu_view">
        <div class="menu_title">
          <div class="icon1">
            <img src="@/assets/digitalbooks/chapter.svg" />
            目录
          </div>
          <div class="flex items-center">
            <el-tooltip class="item" effect="dark" content="新增" placement="top-start">
              <svg-icon
                v-if="type === 'author'"
                class="svgIcon"
                icon-class="add"
                class-name="add"
                style="margin-right: 10px"
                @click="menuAppend(null, null)"
              />
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="折叠" placement="top-start">
              <i class="el-icon-s-fold svgIcon pointer" @click='showMenu = false'></i>
            </el-tooltip>
          </div>
        </div>
        <div v-loading="menuLoading" class="menu_body" element-loading-background="rgba(255, 255, 255, 0.3)">
          <el-tree
            v-if='refreshMenu'
            ref='treeRef'
            :data="menuTree"
            :props="treeProps"
            node-key="id"
            highlight-current
            default-expand-all
            :draggable='type === "author"'
            :current-node-key='selectMenu.id'
            :expand-on-click-node="false"
            @node-drop="handleDrop"
            @node-click="handleNodeClick"
          >
            <div slot-scope="{ node, data }" class="menu_item">
              <div class="item_title">
                <div :title="data.title" class="w article-singer-container">
                  {{ data.title }}
                </div>
              </div>
              <div class="item_option" v-if='type === "author"'>
                <el-tooltip class="item" effect="dark" content="新增" placement="top-start">
                  <el-button
                    v-if="data.parentId === 0"
                    type="text"
                    size="mini"
                    @click.stop="() => menuAppend(node, data)"
                  >
                    <svg-icon
                      class="svgIcon"
                      icon-class="add"
                      class-name="add"
                    />
                  </el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
                  <el-button
                    type="text"
                    size="mini"
                    @click.stop="() => menuEdit(node, data)"
                  >
                    <svg-icon
                      class="svgIcon"
                      icon-class="edit"
                      class-name="edit"
                    />
                  </el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                  <el-button
                    type="text"
                    size="mini"
                    @click.stop="() => menuRemove(node, data)"
                  >
                    <svg-icon
                      class="svgIcon"
                      icon-class="delete"
                      class-name="delete"
                    />
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </el-tree>
        </div>
      </div>
      <div v-show='!showMenu' class='menu_view_other'>
        <el-tooltip class="item" effect="dark" content="展开" placement="top-start">
          <i class="el-icon-s-unfold pointer" @click='showMenu = true'></i>
        </el-tooltip>
        <span style='margin-top: 5px'>目录</span>
      </div>
      <div class="list_view" :class="{'list_view_show' : showMenu, 'list_view_other' : !showMenu}">
        <draggable v-model="list" filter=".no-drag" chosen-class="" force-fallback="true" group="essay" animation="1000" @change="onChangeDrag">
          <transition-group>
            <div v-for="(item,index) in list" :key="item.id" class="list_item">
              <el-image class="img" :src="item.background && item.background !== '' ? item.background : DefaultCover" fit="cover" />
              <div class="content">
                <div class="title">
                  <span>{{ index + 1 }}</span>
                  <span style="margin-left: 20px">{{ item.title }}</span>
                </div>
                <div class="desc">
                  {{ item.subtitle }}
                </div>
              </div>
              <div class="btn_view">
                <template v-if='type === "author"'>
                  <el-button class='no-drag no-conversion' size='mini' type="primary" plain style="margin-right: 20px" @click.prevent="toEdit(item)">编辑/查看</el-button>
                  <el-dropdown trigger="click">
                    <i class="el-icon-more more_btn no-drag"></i>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item>
                        <span @click="checkHours(item)">课时信息</span>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <span @click="deleteHours(item)">删除课时</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
                <el-button v-if='type === "check"' style='min-width: 100px' type="primary" plain @click="toClass(item)">上课</el-button>
              </div>
            </div>
          </transition-group>
        </draggable>
        <template v-if='type === "check"'>
          <Empty v-if="list && list.length === 0" :msg="'暂无数据'" />
        </template>
        <template>
          <div class='empty' v-if='menuTree && menuTree.length === 0'>
            <el-button type='primary' size='mini' @click="menuAppend(null, null)">创建目录</el-button>
            <span class='tip'>创建课时前，先创建目录</span>
          </div>
          <Empty v-if="list && list.length === 0 && menuTree.length > 0" :msg="'暂无数据'" />
        </template>
      </div>
    </div>
    <createClassHours ref="createClassHours" :book-id="bookId" :parent-id="selectMenu.id" @addSuccess="getMenuList" />
    <invitationDialog ref="invitationDialog" :book-id='bookId' :book-title='bookTitle'/>
    <editChapter
      v-if="editMenuShow"
      :show="editMenuShow"
      :title-obj="{
        addTitle:'新建目录',
        editTitle: '编辑目录'
      }"
      type="DIGITAL_CLOUD_LECTURE_CATALOGUE"
      :append-to-body="true"
      :node-info="currentMenu"
      @close="editMenuShow = false"
      @emitSucess="editDone"
    />
    <templateManagement ref='templateManagementRef' :book-id="bookId"  @handleReview='previewTemplateManagement'/>
    <GenerateDigitalBook ref='generateBookRef' :book-id='bookId' :token='token'/>
  </div>
</template>

<script>
import GenerateDigitalBook from '@/views/publishingReview/author/components/generateDigitalBook'
import templateManagement from '@/views/publishingReview/author/components/templateManagement'
import DefaultCover from '@/assets/images/default-cover.jpg'
import Empty from '@/components/classPro/Empty/index.vue'
import draggable from 'vuedraggable'
import createClassHours from '@/views/publishingReview/author/components/createClassHours'
import invitationDialog from '@/views/publishingReview/author/components/invitationDialog'
import editChapter from '@/views/digitalbooks/editor/components/editChapter'
import { deleteBookCatalogue, dragCatalogue, getBook, getBookCatalogue } from '@/api/digital-api'

export default {
  components: { Empty, draggable, createClassHours, editChapter, invitationDialog, templateManagement, GenerateDigitalBook },
  data() {
    return {
      DefaultCover,
      bookId: 0,
      bookInfo: null,
      bookTitle: '人工智能',
      token: '',
      type: 'author',
      list: [],
      menuLoading: false,
      menuTree: [],
      selectMenu: { id: 0, childCatalogue: [] },
      treeProps: {
        children: 'showCatalogue',
        label: 'title'
      },
      editMenuShow: false,
      currentMenu: null,
      showMenu: true,
      refreshMenu: true
    }
  },
  mounted() {
    this.bookId = this.$route.query && Number(this.$route.query.bookId)
    this.token = this.$route.query && this.$route.query.token
    this.type = this.$route.query.type ?? 'author'
    this.getBookInfo()
    this.getMenuList(true)
    if (localStorage.getItem('showManagement') && localStorage.getItem('showManagement') === 'true') {
      this.handleManagement()
      localStorage.removeItem('showManagement')
    }
  },
  methods: {
    // 生成数字教材
    generateBook() {
      if (this.bookInfo) {
        if (this.bookInfo.firstDraft) {
          if (this.menuTree.length > 0) {
            this.$refs.generateBookRef.show(this.bookInfo.hasCatalogue)
          } else {
            this.$message.warning('请先创建内容再生成数字教材')
          }
        } else {
          this.$message.warning('数字教材内容已提审或已出版，无法由云讲义生成数字教材')
        }
      }
    },
    // 模板管理
    handleManagement() {
      this.$refs.templateManagementRef.show()
    },
    previewTemplateManagement(templateData) {
      localStorage.setItem('showManagement', 'true')
      this.$router.push({
        path: '/author/lectureNotesPreview',
        query: { catalogueId: templateData.id }
      })
    },
    back () {
      // if (this.$route.query.token.indexOf('Bearer') === -1) {
      //   window.close()
      //   return
      // }
      const path = this.$route.query.path
      this.$router.push({ path: path ?? '' })
      // this.$router.back()
    },
    onChangeDrag(item) {
      if (this.type === 'author') {
        dragCatalogue({
          catalogueId: item.moved.element.id,
          referCatalogueId: this.list[item.moved.oldIndex].id,
          position: item.moved.oldIndex < item.moved.newIndex ? 'AFTER' : 'BEFORE'
        }, { authorization: this.token })
      }
    },
    handleInvitation() {
      this.$refs.invitationDialog.show()
    },
    createHours() {
      this.$refs.createClassHours.show()
    },
    checkHours(item) {
      this.$refs.createClassHours.show(item)
    },
    deleteHours(item) {
      this.$confirm('确定删除当前课时吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deleteBookCatalogue({ catalogueId: item.id })
        this.$message.success('删除成功')
        await this.getMenuList()
      })
    },
    toEdit(item) {
      localStorage.setItem('lectureCatalogue', this.selectMenu.id)
      this.$router.push({
        path: '/author/lectureNotesEditor',
        query: {
          catalogueId: item.id,
          bookId: this.bookId,
          token: this.token,
          uuid: this.$route.query.uuid ? this.$route.query.uuid : null
        }
        // query: {
        //   catalogueId: 216,
        //   mode: 'template',
        //   token: this.token
        // }
      })
    },
    toClass(item) {
      localStorage.setItem('lectureCatalogue', this.selectMenu.id)
      this.$router.push({
        path: '/lectureNotesRead',
        query: {
          catalogueId: item.id,
          type: 'class',
          bookId: this.bookId
        }
      })
    },
    async getBookInfo() {
      const { data } = await getBook({
        bookId: this.bookId,
        scene: 'BOOK_CATALOGUE_OWN'
      })
      this.bookInfo = data
      this.bookTitle = data.title
    },
    async getMenuList(first = false) {
      const { data } = await getBookCatalogue({
        bookId: this.bookId,
        type: 'DIGITAL_CLOUD_LECTURE_COURSE'
      }, {
        authorization: this.token
      })
      this.menuTree = []
      this.list = []
      data.forEach((item) => {
        const showCatalogue = item.childCatalogue.filter((children) => {
          return children.type === 'DIGITAL_CLOUD_LECTURE_CATALOGUE'
        })
        this.menuTree.push({
          ...item,
          showCatalogue: showCatalogue
        })
      })
      if (localStorage.getItem('lectureCatalogue')) {
        this.menuTree.forEach((item) => {
          if (item.childCatalogue && item.childCatalogue.length > 0) {
            item.childCatalogue.forEach((subItem) => {
              if (subItem.id === Number(localStorage.getItem('lectureCatalogue'))) {
                this.selectMenu = subItem
              }
            })
          }
          if (item.id === Number(localStorage.getItem('lectureCatalogue'))) {
            this.selectMenu = item
          }
        })
        localStorage.removeItem('lectureCatalogue')
      } else {
        if (this.menuTree && this.menuTree.length > 0) {
          if (first || this.menuTree.length === 1) {
            this.selectMenu = this.menuTree[0]
          } else {
            if (this.selectMenu.parentId === 0) {
              this.menuTree.forEach((item) => {
                if (item.id === this.selectMenu.id) {
                  this.selectMenu = item
                }
              })
            } else {
              this.menuTree.forEach((item) => {
                if (this.selectMenu.parentId === item.id) {
                  item.childCatalogue.forEach((subItem) => {
                    if (subItem.id === this.selectMenu.id) {
                      this.selectMenu = subItem
                    }
                  })
                }
              })
            }
          }
        }
      }
      this.refreshMenu = false
      this.$nextTick(() => {
        this.refreshMenu = true
      })
      this.getCourseList()
    },
    getCourseList() {
      this.list = []
      if (this.selectMenu.childCatalogue) {
        this.list = this.selectMenu.childCatalogue.filter((children) => {
          return children.type === 'DIGITAL_CLOUD_LECTURE_COURSE'
        })
      }
    },
    // 菜单栏
    handleNodeClick(nodeData) {
      this.selectMenu = nodeData
      this.getCourseList()
    },
    async handleDrop (draggingNode, dropNode, dropType, ev) {
      if (dropType === 'inner' && dropNode.level === 2) {
        await this.getMenuList()
        return
      }
      await dragCatalogue({
        catalogueId: draggingNode.data.id,
        referCatalogueId: dropNode.data.id,
        position: dropType.toUpperCase()
      }, {
        authorization: this.token
      })
      await this.getMenuList()
    },
    menuAppend (node, data) {
      // if (this.isPublish) return
      // this.menuTree = data
      if (node) {
        this.currentMenu = { bookId: this.bookId, parentId: data.id, data: null }
      } else {
        this.currentMenu = { bookId: this.bookId, parentId: 0, data: null }
      }
      this.editMenuShow = true
    },
    menuEdit (node, data) {
      this.currentMenu = { bookId: this.bookId, parentId: data.parentId, data }
      this.editMenuShow = true
    },
    async menuRemove (node, data) {
      if (this.selectMenu.id === data.id) {
        this.selectMenu = { id: 0, childCatalogue: [] }
      }
      try {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await deleteBookCatalogue({ catalogueId: data.id })
          this.$message.success('删除成功')
          await this.getMenuList()
        }).catch((error) => {
          console.log(error)
        })
      } catch (error) {
        console.log(error)
      }
    },
    editDone() {
      this.editMenuShow = false
      this.getMenuList()
    }
  }
}
</script>

<style scoped lang='scss'>
.main_view{
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: #F1F7FF;
  .header_view{
    height: 40px;
    display: flex;
    width: 100%;
    align-items: center;
    margin-bottom: 10px;
    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }
    .head-title {
      color: #000;
      font-size: var(--font-size-XXL);
      font-weight: 500;
      margin-left: 10px;
      width: 250px;
    }
    .head_btn_view{
      width:calc(100% - 200px);
      height: 100%;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      padding-right: 20px;
      .invitation_btn{
        font-size: var(--font-size-M);
        color: rgba(47, 128, 237, 1);
        text-decoration: underline;
        margin-right: 30px;
        &:hover{
          cursor: pointer;
        }
      }
    }
  }
  .content_view{
    width: 100%;
    height: calc(100% - 50px);
    background-color: #E9F2FF;
    border-radius: 5px;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    .menu_view_other{
      width: 50px;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      color: #000;
      padding: 5px;
      font-size: var(--font-size-M);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .menu_view{
      width: 200px;
      height: 100%;
      background-color: #fff;
      border-radius: 5px;
      .svgIcon{
        width: 12px;
        height: 12px;
        font-size: 12px;
      }
      .menu_title{
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px;
        .icon1 {
          color: #000;
          font-size: var(--font-size-M);
          display: flex;
          align-items: center;

          img {
            width: 20px;
            height: 20px;
            margin-right: 5px;
          }
        }
        .add-btn {
          color: #2F80ED;
          font-size: var(--font-size-M);
          margin-right: 10px;
          cursor: pointer;
        }
      }
      .menu_body{
        width: 100%;
        height: calc(100% - 40px);
        overflow: auto;
        overflow-x:hidden;
        ::v-deep .el-tree-node__content {
          height: 40px;
        }
        ::v-deep .el-button + .el-button {
          margin-left: 5px;
        }
        ::v-deep .el-button {
          font-size: var(--font-size-M);
        }
        ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
          padding: 5px;
        }
        .menu_item{
          width: calc(100% - 30px);
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: var(--font-size-M);
          padding-right: 8px;
          .item_title{
            flex: 1;
            overflow: hidden;
            @include scrollBar;
          }
          .item_option{
            flex-shrink: 0;
          }
        }
      }
    }
    .list_view_show{
      width: calc(100% - 10px - 200px);
    }
    .list_view_other{
      width: calc(100% - 10px - 50px);
    }
    .list_view{
      height: 100%;
      overflow-y: auto;
      .empty{
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        .tip{
          margin-top: 20px;
          color: rgba(130, 130, 130, 1);
          font-size: var(--font-size-M);
        }
      }
      .list_item{
        width: 100%;
        height: 130px;
        background-color: #fff;
        border-radius: 5px;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        padding-left: 20px;
        color: #000;
        .img{
          width: 120px;
          height: 90px;
          border-radius: 6px;
        }
        .content{
          width:calc(100% - 90px - 180px);
          height: 100%;
          padding: 10px;
          .title{
            height: 30px;
            line-height: 30px;
            width: 100%;
            font-size: var(--font-size-XL);
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .desc{
            height: calc(100% - 40px);
            font-size: var(--font-size-M);
            font-weight: 300;
            color: rgba(51, 51, 51, 1);
            margin-top: 10px;
            white-space: pre-wrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .btn_view{
          width: 180px;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

  }
}
.more_btn{
  color:rgba(47, 128, 237, 1);
  font-size:20px;
  transform: rotate(90deg);
  &:hover{
    cursor: pointer;
  }
}
</style>
