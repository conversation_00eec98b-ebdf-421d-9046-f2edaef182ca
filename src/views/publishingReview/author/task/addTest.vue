<template>
  <NormalDialog
    v-if="dialogShow"
    width="1000px"
    :title="'题目编辑('+title+')'"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div v-if="testType===0">
        <el-form :model="form" label-width="8rem">
          <el-form-item label="题目">
            <el-input v-model="form.question" type="textarea" maxlength="100" show-word-limit style="width: 80%;" placeholder="请输入题目" />
          </el-form-item>
          <el-form-item v-for="(option, index) in form.answerOptionList" :key="index" :label="'选项'+getOptionLabel(index)">
            <el-input
              v-model="option.answer"
              placeholder="请输入选项"
              style="width: 80%;"
              maxlength="50"
              show-word-limit
            />
            <el-checkbox v-model="option.right" style="margin-left: 10px;">答案</el-checkbox>
            <el-button style="margin-left: 10px;" type="danger" icon="el-icon-delete" @click="removeOption(index)" />
          </el-form-item>
          <el-form-item>
            <i class="el-icon-circle-plus addTestItem" @click="addOption"></i>
          </el-form-item>
          <el-form-item label="答案解析">
            <el-input v-model="form.analysis" type="textarea" maxlength="200" show-word-limit style="width: 80%;" placeholder="请输入答案解析" />
          </el-form-item>
          <el-form-item label="分数">
            <el-input v-model.number="form.score" type="number" oninput="value=value.replace(/[^0-9.]/g,'')" style="width: 80%;" placeholder="请输入分数" />
          </el-form-item>
        </el-form>
      </div>
      <div v-if="testType===1">
        <el-form :model="form" label-width="8rem">
          <el-form-item label="题目">
            <el-input v-model="form.question" :rows="5" type="textarea" maxlength="100" show-word-limit style="width: 80%;" placeholder="请输入题目,格式说明：请在题干中使用[]符号来设置填空题的正确答案，正确答案请勿输入逗号 示例：北京奥运会在[2008]年举行" />
          </el-form-item>
          <el-form-item label="答案解析">
            <el-input v-model="form.analysis" type="textarea" maxlength="200" show-word-limit style="width: 80%;" placeholder="请输入答案解析" />
          </el-form-item>
          <el-form-item label="分数">
            <el-input v-model.number="form.score" type="number" oninput="value=value.replace(/[^0-9.]/g,'')" style="width: 80%;" placeholder="请输入分数" />
          </el-form-item>
        </el-form>
      </div>
      <div v-if="testType===2">
        <el-form :model="form" label-width="8rem">
          <el-form-item label="题目">
            <el-input v-model="form.question" type="textarea" maxlength="100" show-word-limit style="width: 80%;" placeholder="请输入题目" />
          </el-form-item>
          <el-form-item label="答案">
            <el-radio-group v-model="form.answerForm">
              <el-radio :label="'1'">正确</el-radio>
              <el-radio :label="'0'">错误</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="答案解析">
            <el-input v-model="form.analysis" type="textarea" maxlength="200" show-word-limit style="width: 80%;" placeholder="请输入答案解析" />
          </el-form-item>
          <el-form-item label="分数">
            <el-input v-model.number="form.score" type="number" oninput="value=value.replace(/[^0-9.]/g,'')" style="width: 80%;" placeholder="请输入分数" />
          </el-form-item>
        </el-form>
      </div>
      <div v-if="testType===3">
        <el-form :model="form" label-width="8rem">
          <el-form-item label="题目">
            <el-input v-model="form.question" type="textarea" maxlength="200" show-word-limit style="width: 80%;" placeholder="请输入题目" />
          </el-form-item>
          <el-form-item label="答案解析">
            <el-input v-model="form.analysis" type="textarea" :rows="5" maxlength="500" show-word-limit style="width: 80%;" placeholder="请输入答案解析" />
          </el-form-item>
          <el-form-item label="分数">
            <el-input v-model.number="form.score" type="number" oninput="value=value.replace(/[^0-9.]/g,'')" style="width: 80%;" placeholder="请输入分数" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { question } from '@/api/test-api'
import router from '../../../../router'
import { debounce } from '@/utils/index'
export default {
  components: { NormalDialog },
  props: {
    testType: {
      type: Number,
      default: 0
    },
    testId: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      dialogShow: false,
      apiType: 'create',
      appendToBody: false,
      questionType: '',
      form: {
      }
    }
  },
  computed: {
    title() {
      let title = ''
      switch (this.testType) {
        case 0 : title = '选择题'
          break
        case 1 : title = '填空题'
          break
        case 2 : title = '判断题'
          break
        case 3 : title = '简答题'
          break
      }
      return title
    }
  },
  mounted () {
  },
  methods: {
    addOption() {
      if (!this.form.answerOptionList) {
        this.form = {
          question: this.form.question,
          answerOptionList: []
        }
      }
      this.form.answerOptionList.push({ answer: '', right: false })
    },
    removeOption(index) {
      this.form.answerOptionList.splice(index, 1)
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    close () {
      this.dialogShow = false
    },
    open (type = false) {
      this.$nextTick(() => {
        if (type) {
          this.apiType = 'update'
          if (this.testType === 1) {
            this.form.question = this.deserializeAndFill(this.form.question, this.form.answer)
          } else if (this.testType === 2) {
            this.$set(this.form, 'answerForm', this.form.answerOptionList[0].right ? '1' : '0')
          }
        } else {
          this.apiType = 'create'
          this.form = {}
          console.log(this.testType)
          if (this.testType === 0) {
            this.addOption()
            this.addOption()
          }
        }
      })
      this.dialogShow = true
    },
    onSubmit: debounce(async function () {
      if (this.testType === 0) {
        setTimeout(() => {
          this._question()
        }, 500)
      } else if (this.testType === 1) {
        const data = this.replaceAndExtract(this.form.question)
        this.form.question = data.replacedString
        this.form.answer = data.extractedContents.toString()
        setTimeout(() => {
          this._question()
        }, 500)
      } else if (this.testType === 2) {
        console.log(this.form)
        this.form.answerOptionList = [{
          answer: '正确',
          id: this.form.answerOptionList && this.form.answerOptionList[0].id,
          right: this.form.answerForm === '1'
        }, {
          answer: '错误',
          id: this.form.answerOptionList && this.form.answerOptionList[1].id,
          right: this.form.answerForm === '0'
        }]
        setTimeout(() => {
          this._question()
        }, 500)
      } else if (this.testType === 3) {
        setTimeout(() => {
          this._question()
        }, 500)
      }
    }, 1000, true),
    replaceAndExtract(input) {
      const regex = /\[(.*?)\]/g
      let match
      const extractedContents = []

      while ((match = regex.exec(input)) !== null) {
        const cleanedContent = match[1].replace(/,/g, '')
        extractedContents.push(cleanedContent)
      }

      const replacedString = input.replace(regex, '[*]')

      return {
        replacedString,
        extractedContents
      }
    },
    deserializeAndFill(input, serializedValues) {
      const valuesArray = serializedValues.split(',')

      const regex = /\[\*\]/g
      let index = 0

      const filledString = input.replace(regex, () => {
        if (index < valuesArray.length) {
          return '[' + valuesArray[index++] + ']'
        }
        return '[*]'
      })

      return filledString
    },
    getQuestionType() {
      let type = ''
      switch (this.testType) {
        case 0 : type = 'CHOICE'
          break
        case 1 : type = 'FILL_IN_THE_BLANK_INPUT'
          break
        case 2 : type = 'SIMPLE_CHOOSE'
          break
        case 3 : type = 'ESSAY_QUESTION'
          break
      }
      return type
    },
    async _question(flag = true) {
      if (this.testId === 0) {
        if (flag) {
          this.$emit('createTest')
        }
        setTimeout(() => {
          this._question(false)
        }, 500)
        return
      }
      question({ apiType: this.apiType, testPaperId: this.testId }, { ...this.form, questionType: this.getQuestionType()
      }, { authorization: 'Bearer ' + router.currentRoute.query.token }).then(res => {
        if (res.code === 200) {
          this.dialogShow = false
          this.form = {}
          this.$emit('onSubmit', res.data.id)
        }
      })
    }
  }
}
</script>

    <style lang="scss" scoped>
    ::v-deep .el-textarea .el-input__count{
      right: -50px;
      bottom: -10px;
    }
    .el-form-item{
      margin-bottom: 5px;
    }
    ::v-deep .el-checkbox__inner::after{
      border-width: 2px;
    }
  ::v-deep .el-checkbox__label{
      padding-left: 3px !important;
  }
    .addTestItem{
      font-size: 20px;
      cursor: pointer;
      color:#409EFF ;
    }
    .option-item {
    display: flex;
    align-items: center;
  }
    ::v-deep .el-button{
      font-size: 12px;
      padding: 5px;
    }

    .editor-dig {
      ::v-deep .el-input__inner {
        transition: none;
      }
      .school-disc {
        margin-top: 10PX;
      }

      .mb10 {
        margin-bottom: 10PX;
      }

      .school-disc2 {
        width: 5PX;
        height: 5PX;
        background: #828282;
        border-radius: 50%;
        margin-right: 5PX;
      }
    }
    </style>
