<template>
  <NormalDialog
    v-if="dialogShow"
     width="80%"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div v-loading="Loading" class="flex flex-col editor-dig w">
      <Pdf ref="pdf" @success="Loading=false" />
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import Pdf from '../../../digitalbooks/attendClass/components/inClass/pdf'
export default {
  components: { NormalDialog, Pdf },
  props: {
    url: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dialogShow: false,
      appendToBody: false,
      title: '帮助说明',
      content: '',
      Loading: true
    }
  },
  mounted () {
  },
  methods: {
    close () {
      this.dialogShow = false
      this.Loading = true
    },
    open (item) {
      this.dialogShow = true
      this.$nextTick(() => {
        this.$refs.pdf._loadFile(`https://easykid.oss-cn-beijing.aliyuncs.com/bingoprd/resource/任务工单功能说明.pdf?token=${Date.now()}`, `任务工单功能说明.pdf`)
      })
    },
    onSubmit () {
      this.close()
    }
  }
}
</script>
    <style lang="scss" scoped>
    .editor-dig {
      ::v-deep .el-input__inner {
        transition: none;
      }
      .school-disc {
        margin-top: 10PX;
      }
      .mb10 {
        margin-bottom: 10PX;
      }
      .school-disc2 {
        width: 5PX;
        height: 5PX;
        background: #828282;
        border-radius: 50%;
        margin-right: 5PX;
      }
    }
    </style>
