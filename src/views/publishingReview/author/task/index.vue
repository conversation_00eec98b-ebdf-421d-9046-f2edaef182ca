<template>
  <!-- 课时选择 -->
  <div class="task-class">
    <div class="head-box">
      <div style="display: flex;">
        <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
        <div class="head-title article-singer-container">
          任务列表
        </div>
      </div>
    </div>
    <el-button type="text" class="text_button" @click="openPdf">功能说明</el-button>
    <div class="task-content">
      <div class="task-box">
        <div class="catalogue-box">
          <div
            v-for="(item, index) in catalogueList"
            :key="index"
            class="card-title"
            :class="CatalogueId === item.id ? 'active' : ''"
            :title="item.title"
            @click="changeData(item.id)"
          >
            {{ item.title }}
          </div>
        </div>
        <div class="tbody-box">
          <div class="title">
            <p class="tips">可拖拽任务调整顺序</p>
            <el-dropdown>
              <div class="classpro-btn">创建任务</div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="$refs.push.show()">标准任务</el-dropdown-item>
                <el-dropdown-item @click.native="$refs.Test.show()">习题任务</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <draggable v-model="list" chosen-class="" filter=".no-drag" force-fallback="true" group="essay" animation="1000" @change="onChangeDrag">
            <transition-group>
              <div v-for="(item,index) in list" :key="item.id" class="table-body">
                <div class="tab">{{ index+1 }}</div>
                <div class="title">{{ item.title }}</div>
                <el-button class="button1 no-drag" type="primary" plain @click="handleDetail(item)">查看</el-button>
                <el-button class="button no-drag" type="danger" plain @click="deleteTask(item)">删除</el-button>
              </div>
            </transition-group>
          </draggable>
          <Empty v-if="list && list.length === 0" :msg="'暂无数据'" />
        </div>
      </div>
    </div>
    <div v-if="detailShow" class="task-pop">
      <taskDetail :task-id="taskId" :mode="'edit'" @edit="toedit" @close="detailShow = false" />
    </div>
    <Push ref="push" :catalogue-id="CatalogueId" @refresh="_getTask" />
    <Test ref="Test" :catalogue-id="CatalogueId" @refresh="_getTask" />
    <showPdf ref="showPdf" />
  </div>
</template>

<script>
import defaultCourse from '@/assets/images/default-cover.jpg'
import { getBook, getDigitalTaskListByCatalogueId, digitalTask, dragDigitalTask, getBookCatalogueByVersion } from '@/api/digital-api.js'
import { formatYYYYMMDDHHmm } from '@/utils/time.js'
import taskDetail from './detail.vue'
import Empty from '@/components/classPro/Empty/index.vue'
import Push from './push.vue'
import draggable from 'vuedraggable'
import Test from './addTestPop.vue'
import showPdf from './showPdf.vue'
export default {
  components: { taskDetail, Empty, Push, draggable, Test, showPdf },
  data () {
    return {
      defaultCourse,
      formatYYYYMMDDHHmm,
      bookTitle: '',
      bookId: 0,
      studentCourseId: 0,
      list: [],
      progressShow: false,
      selectTaskInfo: null,
      detailShow: false,
      taskId: '',
      token: '',
      catalogueList: null,
      CatalogueId: 0
    }
  },
  async mounted () {
    this.bookId = this.$route.query && this.$route.query.bookId
    this.studentCourseId = this.$route.query && this.$route.query.studentCourseId
    this.token = `Bearer ${this.$route.query && this.$route.query.token}`
    this._getBook()
    await this._getBookCatalogueByVersion()
    this._getTask()
  },
  methods: {
    openPdf() {
      this.$refs.showPdf.open()
    },
    changeData(id) {
      this.CatalogueId = id
      this._getTask()
      this.$store.dispatch('app/setCatalogueId', id)
    },
    async  _getBookCatalogueByVersion() {
      const { data } = await getBookCatalogueByVersion({
        bookId: this.bookId,
        type: 'CHAPTER'
        // approvedOnly: false
      }, {
        authorization: this.token
      })
      this.catalogueList = data
      this.CatalogueId = data[0].id
      this.$store.dispatch('app/setCatalogueId', this.CatalogueId )
    },
    toedit(id) {
      this.detailShow = false
      this.$refs.push.show(id)
    },
    onChangeDrag(item) {
      dragDigitalTask({ digitalBookId: this.bookId, digitalHomeworkId: item.moved.element.id, previousDigitalHomeworkId: item.moved.newIndex === 0 ? null : this.list[item.moved.newIndex - 1 ].id }, {
        authorization: this.token
      })
    },
    back () {
      if (this.$route.query.token.indexOf('Bearer') === -1) {
        window.close()
        return
      }
      this.$router.push('/author/home')
    },
    async _getBook () {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN'
        }, { authorization: this.token })
        this.bookTitle = data.title
      }
    },
    async _getTask () {
      const { data } = await getDigitalTaskListByCatalogueId({
        digitalCatalogueId: this.CatalogueId,
        studentCourseId: this.studentCourseId
      }, {
        authorization: this.token
      })
      this.list = data
    },
    async deleteTask (item) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await digitalTask({
          apiType: 'delete',
          id: item.id
        }, {}, {
          authorization: this.token
        })
        await this._getTask()
        this.$message.success('删除成功')
      }).catch(() => {

      })
    },
    handleDetail (item) {
      if (item.sourceId) {
        this.$refs.Test.show(item)
        return
      }
      this.taskId = item.id
      this.detailShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.task-class {
  width: 100%;
  height: 100%;
  padding: 10px;
  .text_button{
    font-size: 10px;
    position: absolute;
    right: 40px;
    top:30px;
    text-decoration: underline;
  }
  .head-box {
    height: 40px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
    justify-content: space-between;
    .share {
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
      margin-left: 10px;
      margin-top: 3px;
    }
  }

  .task-content {
    padding: 10px 0;
    height: calc(100% - 40px);
    width: 100%;
    position: relative;
    .task-title {
      height: 40px;
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
      display: flex;
      justify-content: space-between;
    }

    .task-box {
      border-radius: 10px;
      background: #E1F1FF;
      width: 100%;
      height: 100%;
      padding: 20px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      .title{
        display: flex;
        justify-content: space-between;
        width: 100%;
        .tips{
        font-size: 10px;
        color:red
      }
        .classpro-btn {
        padding: 5px 10px;
        height: 30px;
        font-size: 12px;
        margin-top: -5px;
        border-radius: 5px;
    }
      }
      .catalogue-box{
        width: 20%;
        height: calc(100% );
        overflow-y: auto;
        @include scrollBar;
        background:#F9F9F9;
        border-radius: 10px;
        .card-title {
        width: 90%;
        height: 30px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        text-align: left;
        margin: 0 auto;
        cursor: pointer;
        padding-left: 5px;
        line-height: 30px;
        margin-top: 10px;
      }
      .card-title:hover {
        background: gainsboro;
      }
      .active {
        background: #2F80ED;
        color: #ffffff;
      }
      }
      .table-head {
        background: #E3EFFF;
        height: 40px;
        width: 100%;
        display: flex;
        .thead-box, .thead-box-task {
          width: 25%;
          height: 100%;
          display: flex;
          align-items: center;
          color: #2F80ED;
          font-size: var(--font-size-L);
          padding: 0 5px;
        }

        .thead-box {
          justify-content: center;
        }

        .thead-box-task {
          justify-content: flex-start;
        }
      }

      .tbody-box {
        width: 78%;
        height: calc(100% );
        overflow-y: auto;
        @include scrollBar;
        background:#F9F9F9;
        padding: 30px;
        padding-top: 20px;
        border-radius: 10px;
      }

      .table-body {
        height: 75px;
        width: 100%;
        display: flex;
        margin-top: 10px;
        background:#FFFFFF;
        border-radius: 10px;
        position: relative;
        .title{
            font-size: 14px;
            font-weight: 700;
            height: 75px;
            line-height: 75px;
            padding-left: 20px;
        }
      .tab{
        width: 30px;
        height: 30px;
        border-radius: 15px;
        background: linear-gradient(90deg, #4FACFE 0%, #00F2FE 100%);
        line-height: 30px;
        text-align: center;
        color: #ffffff;
        font-size: 14px;
        font-weight: 600;
        margin-left: 30px;
        margin-top: 22px;
      }
    .button{
        width: 50px;
        height: 30px;
        font-size: 12px;
        line-height: 0px;
        padding: 0;
        position: absolute;
        right: 50px;
        top:20px
    }
    .button1{
        width: 50px;
        height: 30px;
        font-size: 12px;
        line-height: 0px;
        padding: 0;
        position: absolute;
        right: 120px;
        top:20px
    }
      }
    }
  }

  .task-pop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #E9F2FF;
  }
}
</style>
