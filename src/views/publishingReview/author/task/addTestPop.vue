<template>
  <NormalDialog
    v-if="dialogShow"
    width="90%"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col main w">
      <div class="mb10">
        <el-input v-model="testTitle" maxlength="20" class="w" placeholder="请输入任务名称，必填" />
        <el-button class="button" type="primary" @click="selectShow=true">从教材中导入</el-button>
      </div>
      <div class="content1">
        <div class="title">
          <p>一、 选择题</p>
          <el-button type="text" class="button" @click="addQuestion(0)">添加题目</el-button></div>
        <div class="test_content">
          <div v-if="choiceList.length === 0" style="height: calc(50%);" class="w flex justify-center items-center">
            <Empty style="width: 8rem; transform: scale(0.4);" :image="empty3" description="暂无数据" />
          </div>
          <draggable v-model="choiceList" chosen-class="" force-fallback="true" group="choice" animation="1000" @start="onStart" @end="drag=false" @change="onEndchoice">
            <transition-group>
              <div v-for="(item,index) in choiceList" :key="item.id" class="qus_item">
                <div class="item_header">
                  <div class="button_group">
                    <i class="el-icon-edit edit" @click.prevent="editQuestion(item,0)"></i>
                    <i class="el-icon-delete delete" @click.prevent="deleteQuestion(item)"></i>
                  </div>
                </div>
                <div class="item_content">
                  <div>{{ index+1+'.'+ item.question }}<span v-if="item.score">({{ item.score }}分)</span></div>
                  <div v-for="(option,index1) in item.answerOptionList" :key="index1" class="chese_item">
                    <div class="lebel">{{ getOptionLabel(index1) }}</div>
                    {{ option.answer
                    }}</div>
                  <div class="chese_item">答案:{{ getAnswer(item) }}</div>
                  <div class="chese_item">解析：{{ item.analysis }}</div>
                </div>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
      <div class="content1">
        <div class="title">
          <p>二、 填空题</p>
          <el-button type="text" class="button" @click="addQuestion(1)">添加题目</el-button></div>
        <div class="test_content">
          <div v-if="fillList.length === 0" style="height: calc(100%);" class="w flex justify-center items-center">
            <Empty style="width: 8rem; transform: scale(0.4);" :image="empty3" description="暂无数据" />
          </div>
          <draggable v-model="fillList" chosen-class="" force-fallback="true" group="fill" animation="1000" @start="onStart" @end="drag=false" @change="onEndfill">
            <transition-group>
              <div v-for="(item,index) in fillList" :key="item.id" class="qus_item">
                <div class="item_header">
                  <div class="button_group">
                    <i class="el-icon-edit edit" @click.prevent="editQuestion(item,1)"></i>
                    <i class="el-icon-delete delete" @click.prevent="deleteQuestion(item)"></i>
                  </div>
                </div>
                <div class="item_content">
                  <div>{{ index+1+'.'+formatterFill(item.question) }}<span v-if="item.score">({{ item.score }}分)</span></div>
                  <div v-for="(option,index1) in item.answerOptionList" :key="index1" class="chese_item">
                    <div class="lebel">{{ getOptionLabel(index1) }}</div>
                    {{ option.answer
                    }}</div>
                  <div class="chese_item">答案:{{ item.answer }}</div>
                  <div class="chese_item">解析：{{ item.analysis }}</div>
                </div>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
      <div class="content1">
        <div class="title">
          <p>三、 判断题</p>
          <el-button type="text" class="button" @click="addQuestion(2)">添加题目</el-button></div>
        <div class="test_content">
          <div v-if="sopmlieList.length === 0" style="height: calc(100%);" class="w flex justify-center items-center">
            <Empty style="width: 8rem; transform: scale(0.4);" :image="empty3" description="暂无数据" />
          </div>
          <draggable v-model="sopmlieList" chosen-class="" force-fallback="true" group="sopmlie" animation="1000" @start="onStart" @end="drag=false" @change="onEndsopmlie">
            <transition-group>
              <div v-for="(item,index) in sopmlieList" :key="item.id" class="qus_item">
                <div class="item_header">
                  <div class="button_group">
                    <i class="el-icon-edit edit" @click.prevent="editQuestion(item,2)"></i>
                    <i class="el-icon-delete delete" @click.prevent="deleteQuestion(item)"></i>
                  </div>
                </div>
                <div class="item_content">
                  <div>{{ index+1+'.'+ item.question }}<span v-if="item.score">({{ item.score }}分)</span></div>
                  <div v-for="(option,index1) in item.answerOptionList" :key="index1" class="chese_item">
                    <div class="lebel">{{ getOptionLabel(index1) }}</div>
                    {{ option.answer
                    }}</div>
                  <div class="chese_item">答案:{{ getAnswer(item) }}</div>
                  <div class="chese_item">解析：{{ item.analysis }}</div>
                </div>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
      <div class="content1">
        <div class="title">
          <p>四、 简答题</p>
          <el-button type="text" class="button" @click="addQuestion(3)">添加题目</el-button></div>
        <div class="test_content">
          <div v-if="essayList.length === 0" style="height: calc(100%);" class="w flex justify-center items-center">
            <Empty style="width: 8rem; transform: scale(0.4);" :image="empty3" description="暂无数据" />
          </div>
          <draggable v-model="essayList" chosen-class="" force-fallback="true" group="essay" animation="1000" @start="onStart" @end="drag=false" @change="onEndessay">
            <transition-group>
              <div v-for="(item,index) in essayList" :key="item.id" class="qus_item">
                <div class="item_header">
                  <div class="button_group">
                    <i class="el-icon-edit edit" @click.prevent="editQuestion(item,3)"></i>
                    <i class="el-icon-delete delete" @click.prevent="deleteQuestion(item)"></i>
                  </div>
                </div>
                <div class="item_content">
                  <div>{{ index+1+'.'+ item.question }}<span v-if="item.score">({{ item.score }}分)</span></div>
                  <div class="chese_item">解析：{{ item.analysis }}</div>
                </div>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
    <addTest ref="addTest" :test-type="testType" :test-id="testId" @createTest="createTest" @onSubmit="addTestItem" />
    <div v-if="selectShow" class="select-box">
      <div class="select-body">
        <div class="select-title ">
          <div class="fb f26">选择题块</div>
          <div class="flex">
            <i class="el-icon-close pointer" @click="selectShow=false"></i>
          </div>
        </div>
        <div class="select-content">
          <Read :task-mode="true" :pre-mode="true" @clickTest="addTests" />
        </div>
      </div>
    </div>
  </NormalDialog>
</template>
<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import empty3 from '@/assets/images/empty3.png'
import addTest from './addTest.vue'
import { Empty } from 'element-ui'
import draggable from 'vuedraggable'
import { getTestPaperQuestionList, question, dragQuestion, addQuestionToTestpaper } from '@/api/test-api'
import { digitalTask } from '@/api/digital-api.js'
import Read from '@/views/digitalbooks/read/index.vue'
export default {
  components: { NormalDialog, Empty, addTest, draggable, Read },
  props: {
    catalogueId: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      cbs: null,
      empty3,
      dialogShow: false,
      appendToBody: false,
      title: '添加习题任务',
      testTitle: '习题任务',
      testType: 0,
      content: '',
      questionList: [],
      choiceList: [],
      fillList: [],
      sopmlieList: [],
      essayList: [],
      testId: 0,
      taskId: 0,
      ids: '',
      bookId: '',
      token: '',
      selectShow: false
    }
  },
  mounted () {
    // this.dialogShow = true
    this.token = `Bearer ${this.$route.query && this.$route.query.token}`
  },
  methods: {
    addTests(item) {
      let ids = ''
      if (!item.classList.contains('test_card') && !item.parentNode.classList.contains('test_card')) {
        this.$message.warning('请点击互动学习块添加')
        return
      } else if (item.classList.contains('test_card')) {
        ids = item.getAttribute('data-ids')
      } else if (item.parentNode.classList.contains('test_card')) {
        ids = item.parentNode.getAttribute('data-ids')
      }
      this.$confirm(`确认导入题目？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        if (this.testId === 0) {
          await this.createTest()
        }
        addQuestionToTestpaper({ testPaperId: this.testId, questionIds: ids }, { authorization: this.token }, {
          authorization: this.token
        }).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '导入成功!'
            })
            this.selectShow = false
            this.$nextTick(() => {
              this._getTestPaperQuestionList()
            })
          }
        })
      })
    },
    editQuestion(item, index) {
      this.testType = index
      const form = { ...item }
      if (form.score === 0) {
        form.score = ''
      }
      this.$refs.addTest.form = form
      this.$nextTick(() => {
        this.$refs.addTest.open(true)
      })
    },
    deleteQuestion(item) {
      this.$confirm(`确认删除题目？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        question({ testPaperId: this.testId, apiType: 'delete' }, { id: item.id }, {
          authorization: this.token
        }).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.$nextTick(() => {
              this._getTestPaperQuestionList()
            })
          }
        })
      })
    },
    formatterFill(item) {
      return item.replace(/\[\*\]/g, '___')
    },
    getAnswer(item) {
      let idArr = item.answer.split(',')
      idArr = idArr.map((item) => {
        return Number(item)
      })
      const answer = []
      let text = ''
      item.answerOptionList.forEach((choice, index) => {
        if (idArr.indexOf(choice.id) !== -1) {
          answer.push(index)
        }
      })
      answer.forEach((index) => {
        text = text + this.getOptionLabel(index) + ''
      })
      return text
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    onStart () {
      this.drag = true
    },
    onEndsopmlie(item) {
      this.drag = false
      if (item.timeStamp < 1000) {
        return
      }
      dragQuestion({ testPaperId: this.testId, questionId: item.moved.element.id, previousQuestionId: item.moved.newIndex === 0 ? null : this.sopmlieList[item.moved.newIndex - 1 ].id }, {
        authorization: this.token
      })
    },
    onEndessay(item) {
      this.drag = false
      if (item.timeStamp < 1000) {
        return
      }
      dragQuestion({ testPaperId: this.testId, questionId: item.moved.element.id, previousQuestionId: item.movednewIndex === 0 ? null : this.essayList[item.moved.newIndex - 1 ].id }, {
        authorization: this.token
      })
    },
    onEndfill(item) {
      this.drag = false
      if (item.timeStamp < 1000) {
        return
      }
      dragQuestion({ testPaperId: this.testId, questionId: item.moved.element.id, previousQuestionId: item.moved.newIndex === 0 ? null : this.fillList[item.moved.newIndex - 1 ].id }, {
        authorization: this.token
      })
    },
    // 拖拽结束事件
    onEndchoice (item) {
      console.log(item)
      this.drag = false
      if (item.timeStamp < 1000) {
        return
      }
      dragQuestion({ testPaperId: this.testId, questionId: item.moved.element.id, previousQuestionId: item.moved.newIndex === 0 ? null : this.choiceList[item.moved.newIndex - 1 ].id }, {
        authorization: this.token
      })
    },
    addTestItem(item) {
      this._getTestPaperQuestionList()
    },
    addQuestion(index) {
      this.testType = index
      this.$refs.addTest.open()
    },
    close () {
      this.innitData()
      this.$emit('refresh')
      this.dialogShow = false
    },
    async show (item) {
      if (item) {
        this.testId = item.sourceId
        this.taskId = item.id
        this.testTitle = item.title
      }
      this.bookId = this.$route.query && this.$route.query.bookId
      if (this.testId) {
        this._getTestPaperQuestionList()
      }
      this.dialogShow = true
    },
    async createTest() {
      if (!this.testId) {
        const data = await digitalTask({
          apiType: 'create',
          digitalBookId: this.bookId,
          title: this.testTitle ? this.testTitle : '习题任务',
          digitalHomeworkType: 'TESTPAPER',
          digitalCatalogueId: this.catalogueId
        }, {}, { authorization: this.token })
        this.taskId = data.data.id
        this.testId = data.data.sourceId
        this.testTitle = data.data.title
      }
    },
    updateTest() {
      if (Object.prototype.toString.call(this.cbs['updateTest']) === '[object Function]') {
        this.cbs['updateTest'](this.testTitle)
      }
    },
    async onSubmit () {
      if (this.testId === 0) {
        this.$message.warning('请先添加任意试题')
        return
      }
      if (this.testTitle === '') {
        this.$message.warning('请输入任务名称')
        return
      }
      await digitalTask({
        apiType: 'update',
        id: this.taskId,
        digitalBookId: this.bookId,
        title: this.testTitle,
        digitalHomeworkType: 'TESTPAPER'
      }, {}, { authorization: this.token })
      this.$emit('refresh')
      this.innitData()
      this.dialogShow = false
    },
    async  _getTestPaperQuestionList() {
      await getTestPaperQuestionList({ testPaperId: this.testId }, {
        authorization: this.token
      }).then(res => {
        this.questionList = res.data.questionList
        // this.testTitle = res.data.title
        this.choiceList = res.data.questionList.filter((item) => { return item.questionType === 'CHOICE' })
        this.fillList = res.data.questionList.filter((item) => { return item.questionType === 'FILL_IN_THE_BLANK_INPUT' })
        this.sopmlieList = res.data.questionList.filter((item) => { return item.questionType === 'SIMPLE_CHOOSE' })
        this.essayList = res.data.questionList.filter((item) => { return item.questionType === 'ESSAY_QUESTION' })
      })
    },
    onCancel () {
      this.innitData()
    },
    innitData() {
      this.testId = 0
      this.questionList = []
      this.testTitle = '习题任务'
      this.choiceList = []
      this.fillList = []
      this.sopmlieList = []
      this.essayList = []
    }
  }
}
</script>
    <style lang="scss" scoped>
      .select-box {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: rgba($color: #000000, $alpha: .4);
    z-index: 3;
    display: flex;
    align-items: flex-end;

    .select-body {
      width: 100%;
      height: 90%;
      background: #e6f1fc;
      border-radius: 10px 10px 0 0;
      padding: 10px;
    }

    .select-title {
      display: flex;
      justify-content: space-between;
      height: 50px;

      .fb {
        font-weight: 500;
      }
      .f26 {
        font-size: 14px;
      }

      .el-icon-close {
        font-size: 26px;
        margin-top: 2px;
      }

      .classpro-btn {
        height: 30px;
        padding: 5px 10px;
        margin-right: 10px;
      }
    }

    .select-content {
      width: 100%;
      height: calc(100% - 50px);
      overflow-x: auto;
      @include scrollBar;
    }
  }
    .main {
      ::v-deep .el-input__inner {
        transition: none;
      }
      .school-disc {
        margin-top: 10PX;
      }
  .content1{
      width: 100%;
      margin-top: 20px;
      white-space: pre-wrap;
      font-size: 12px;
      .title{
          width: 100%;
          font-size: 14px;
          display: flex;
          padding-left: 20px;
          padding-right: 20px;
          justify-content: space-between;
          p{
              height: 20px;
          }
          .button{
              font-size: 12px;
          }
        }
        .test_content{
          min-height: 100px;
          width: 100%;
          .qus_item{
            width: 100%;
            border: 1px solid #E0E0E0;
            cursor: move;
          font-size: 12px;
            .button_group{
              float: right;
              height: 50px;
              width: 70px;
              line-height: 50px;
              display: flex;
              justify-content: space-between;
              padding: 15px;
              .edit{
                font-size: 14px;
                cursor: pointer;
                color:#409EFF ;
              }
              .delete{
                font-size: 14px;
                cursor: pointer;
                color:red ;
              }
            }
            .item_header{
              background: #F2F2F2;
              width: 100%;
              height: 50px;
            }
            .item_content{
              padding: 20px;
              .chese_item{
                margin-top: 20px;
                line-height: 20px;
                display: flex;
                .lebel{
                  width: 20px;
                  height: 20px;
                  border: 1px solid #E0E0E0;
                  text-align: center;
                  line-height: 20px;
                  font-weight: 600;
                  margin-right: 5px;
                }
              }
            }
          }
          ::v-deep .el-empty__image{
              width: 100px;
          }
          ::v-deep .el-empty__description{
              p{
                  font-size: 18px;
                  margin-top: -20px;
              }
          }
        }
  }
      .mb10 {
        margin-bottom: 20px;
        display: flex;
        .button{
        width: 100px;
        font-size: 10px;
        line-height: 0px;
        padding: 0;
        }
      }

      .school-disc2 {
        width: 5PX;
        height: 5PX;
        background: #828282;
        border-radius: 50%;
        margin-right: 5PX;
      }
    }
    </style>
