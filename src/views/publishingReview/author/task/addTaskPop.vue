<template>
  <NormalDialog
    v-if="dialogShow"
    width="400px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="mb10">
        <el-input v-model="content" class="w" placeholder="请输入字段名" />
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },

  data () {
    return {
      dialogShow: false,
      appendToBody: false,
      title: '添加字段',
      content: ''
    }
  },
  mounted () {
  },
  methods: {
    close () {
      this.dialogShow = false
      this.content = ''
    },
    open () {
      this.dialogShow = true
    },
    onSubmit () {
      this.$emit('success', this.content)
      this.close()
    }

  }
}
</script>

  <style lang="scss" scoped>

  .editor-dig {
    ::v-deep .el-input__inner {
      transition: none;
    }
    .school-disc {
      margin-top: 10PX;
    }

    .mb10 {
      margin-bottom: 10PX;
    }

    .school-disc2 {
      width: 5PX;
      height: 5PX;
      background: #828282;
      border-radius: 50%;
      margin-right: 5PX;
    }
  }
  </style>
