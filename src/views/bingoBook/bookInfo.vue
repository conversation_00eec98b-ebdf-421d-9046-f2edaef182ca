<template>
  <div class="book_content">
    <van-nav-bar
      left-text="返回"
      left-arrow
      @click-left="$router.push(`/bingoBook/home?flag=${$route.query.flag}`)"
    >
      <template #right>
        <img
          class="share"
          src="../../assets/bingoBook/share.png"
          alt=""
          @click="openShare"
        />
      </template>
    </van-nav-bar>
    <div class="content">
      <div class="bookInfo">
        <div v-show="bookInfo && bookInfo.title" class="title">
          {{ bookInfo && bookInfo.title }}
        </div>
        <div v-show="bookInfo && bookInfo.author" class="des">
          主编：{{ bookInfo && bookInfo.author }}
        </div>
        <div v-show="bookInfo && bookInfo.isbn" class="des">
          ISBN：{{ bookInfo && bookInfo.isbn }}
        </div>
        <div v-show="bookInfo && bookInfo.publisher" class="des">
          出版社：{{ bookInfo && bookInfo.publisher }}
        </div>
        <div
          v-if="
            bookInfo && bookInfo.goodsComm && bookInfo.goodsComm.price !== null
          "
          class="des price"
        >
          {{ bookInfo.goodsComm.price }}元
        </div>
        <img class="cover" :src="bookInfo && bookInfo.cover" alt="" />
      </div>
      <div class="bookDescription">
        <div v-if="bookInfo && bookInfo.intro" class="title">简介</div>
        <div v-if="bookInfo && bookInfo.intro" class="description">
          <div v-if="showFullIntro">
            {{ bookInfo.intro }}
            <div v-if="bookInfo.intro.length > 100" class="button_expend" @click="showFullIntro = false">收起</div>
          </div>
          <div v-else>
            {{ bookInfo.intro.substring(0, 100) }}<span>{{ bookInfo.intro.length > 100?'...':'' }}</span>
            <div v-if="bookInfo.intro.length > 100" class="button_expend" @click="showFullIntro = true">展开</div>
          </div>
        </div>
        <div v-if="bookInfo && bookInfo.introVideo" class="title">视频介绍</div>
        <div v-if="bookInfo && bookInfo.introVideo" class="description">
          <video class="video_cover" :src="bookInfo && bookInfo.introVideo" controlsList="nodownload" controls="controls"></video>
        </div>
        <div class="title">目录</div>
        <div v-if="bookTree && bookTree.length !== 0" class="description">
          <ul>
            <tree-node
              v-for="(item, index) in bookTree"
              :key="item.id"
              :index="index"
              :node="item"
              :level="0"
              :can-read="bookInfo && bookInfo.studentCourseId !== 0"
              @toRead="read"
            />
          </ul>
        </div>
        <div v-else class="description">
          <Empty description="暂无数据" />
        </div>
        <div>
        </div>
      </div>
      <div v-if="!bookInfo || !bookInfo.studentCourseId" class="bottom_group">
        <van-button
          class="button"
          type="primary"
          plain
          color="#2F80ED"
          @click="tryRead"
        >试读</van-button>
        <van-button
          class="button"
          color="#2F80ED"
          type="primary"
          @click="showPayPop()"
        >购买/兑换</van-button>
      </div>
      <div v-else class="bottom_group">
        <van-button
          color="#2F80ED"
          type="primary"
          block
          @click="toRead"
        >阅读</van-button>
      </div>
    </div>
    <share-guide ref="shareGuide" />
    <van-popup
      v-model="showPay"
      closeable
      round
      close-icon-position="top-right"
      position="bottom"
      :style="{ height: '50%' }"
    ><div class="payContent">
      <p>{{ payType === 0 ? '立即支付' : '兑换' }}</p>
      <p
        v-if="goodsComm && goodsComm.price !== null && payType === 0"
        class="price"
      >
        {{ goodsComm.price }}元
      </p>
      <van-button
        v-if="payType === 0"
        type="primary"
        color="#2F80ED"
        block
        @click="pay"
      >立即支付</van-button>
      <van-button
        v-if="payType === 0"
        plain
        type="primary"
        color="#2F80ED"
        block
        style="margin-top: 10%"
        @click="payType = 1"
      >兑换码</van-button>
      <el-input
        v-if="payType === 1"
        v-model="code"
        class="code_input"
        style="margin-top: 24%"
        placeholder="请输入兑换码"
      />
      <van-button
        v-if="payType === 1"
        type="primary"
        color="#2F80ED"
        style="margin-top: 20%"
        block
        @click="exchange"
      >立即兑换</van-button>
    </div>
    </van-popup>
  </div>
</template>
<script>
import { getBook, getBookCatalogue } from '@/api/digital-api.js'
import TreeNode from './components/TreeNode.vue'
import { mapGetters } from 'vuex'
import { getWxGzhShareSignature } from '@/api/activity-api.js'
import { initWechatShare } from '@/utils/index.js'
import ShareGuide from '@/components/H5/share-guide.vue'
import { getToken } from '@/utils/auth'
import { createOrders, prepay, Login } from '@/api/community-api.js'
import { exchangeEmpowerCourse } from '@/api/digital-api.js'
import { Dialog } from 'vant'
import { Empty } from 'vant'
export default {
  components: { TreeNode, ShareGuide, Empty },
  data() {
    return {
      bookInfo: null,
      bookTree: null,
      active: '',
      goodsComm: null,
      showPay: false,
      payType: 0,
      payData: null,
      code: this.$route.query.code,
      appId: '',
      isLoginFailed: false,
      fromPath: '',
      studentCourseId: '',
      showFullIntro: false
    }
  },
  computed: {
    ...mapGetters(['id'])
  },
  async activated() {
    this.studentCourseId = this.$route.query.studentCourseId
    await this._getBook()
    this.bookTree = []
    await this._getBookCatalogue()
    await this.share()
    if (sessionStorage.getItem('isActive')) {
      this.showPay = true
      this.pay()
    }
  },
  methods: {
    async handleGetWxCode() {
      if (this.$route.query.code) {
        await this.Login()
      } else {
        await this.goWxCode()
      }
    },
    async goWxCode(state = 1) {
      const baseUrl = window.location.origin
      const url = `${baseUrl}/#/bingoBook/bookInfo?id=${this.bookInfo.id}`

      const { getAvailableWebUrl } = await import('@/utils/domainHelper')
      const webUrl = await getAvailableWebUrl()

      window.location.href = `${webUrl}get-weixin-code.html?appid=${
        this.appId
      }&scope=snsapi_base&state=${state}&redirect_uri=${encodeURIComponent(
        url
      )}`
    },
    async Login() {
      if (this.token) {
        return
      } else if (this.isLoginFailed) {
        this.code = ''
        this.handleGetWxCode()
        return
      }
      try {
        const res = await Login({
          loginType: 'WECHAT_GZH',
          authCode: this.code
        })
        if (res.code === 200) {
          this.token = 'Bearer ' + res.data.access_token
        }
      } catch (err) {
        if (err.code !== 602) {
          this.isLoginFailed = true
          const baseUrl = window.location.origin
          const url = `${baseUrl}/#/bingoBook/bookInfo?id=${this.bookInfo.id}`
          this.code = ''
          window.location.href = url
        }
      }
    },
    async exchange() {
      const data = await exchangeEmpowerCourse({
        exchangeCode: this.code,
        childId: this.id
      })
      if (!data) {
        this.$toast('无效的兑换码！')
      } else {
        this.$toast('兑换成功')
        this.showPay = false
        this._getBook()
      }
    },
    replaceUrl() {
      const path = this.$route.path
      this.$router.replace(
        path + `?id=${this.bookInfo.id}&flag=${this.$route.query.flag}`
      )
      this.code = ''
      this.share()
    },
    async pay() {
      sessionStorage.setItem('isActive', true)
      await this.handleGetWxCode()
      if (!this.$route.query.code) return
      createOrders(
        { goodsId: this.goodsComm.id },
        { authorization: getToken() }
      ).then((res) => {
        this.showGood = false
        if (res.data.orderStatus === 'TRADE_SUCCESS') {
          this.$toast('购买成功')
          this.showPay = false
          this.replaceUrl()
          this._getBook()
          return
        }
        const params = {
          payMethod: 'WECHATPAY_GZH',
          ordersId: res.data.id
        }
        prepay(params, { authorization: getToken() })
          .then((response) => {
            this.replaceUrl()
            if (response.code !== 200) {
              this.$message.error(response.data.message)
            } else {
              if (+response.code === 200) {
                const res = response.data
                this.payData = res
                this.handlePay()
              } else {
                this.$message.error(response.data.message)
              }
            }
          })
          .catch((e) => {
            console.log(e)
          })
      })
    },
    handlePay() {
      const data = this.payData
      if (data) {
        window.WeixinJSBridge.invoke(
          'getBrandWCPayRequest',
          {
            appId: data.appId, // 公众号名称，由商户传入
            timeStamp: data.timeStamp, // 时间戳，自1970年以来的秒数
            nonceStr: data.nonceStr, // 随机串
            package: data.package,
            signType: data.signType, // 微信签名方式：
            paySign: data.paySign // 微信签名
          },
          (res) => {
            sessionStorage.removeItem('isActive')
            if (res.err_msg === 'get_brand_wcpay_request:ok') {
              this.showPay = false
              this.$toast('购买成功')
              this._getBook()
            } else if (res.err_msg === 'get_brand_wcpay_request:cancel') {
              Dialog({ message: '取消支付' })
            } else {
              Dialog({ message: '支付失败' })
            }
          }
        )
      }
    },
    showPayPop() {
      if (getToken()) {
        this.showPay = true
        this.payType = this.goodsComm && this.goodsComm.price !== null ? 0 : 1
      } else {
        this.$toast('请先登录')
        this.$router.push('/bingoBook/login')
      }
    },
    openShare() {
      this.$refs.shareGuide.open()
    },
    async share() {
      const url = location.href.split('#')[0]
      const params = {
        url: url
      }
      const res = await getWxGzhShareSignature(params)
      this.appId = res.data.appId
      // 配置分享的内容
      const shareOptions = {
        title: '《' + this.bookInfo.title + '》',
        desc: this.bookInfo.author ? '主编：' + this.bookInfo.author : '',
        link: location.href,
        imgUrl: this.bookInfo.cover,
        success: () => {
          // 分享成功回调
          this.$refs.shareGuide.close()
        },
        cancel: () => {
          // 分享取消回调
          console.log('分享取消')
        }
      }
      initWechatShare(res, shareOptions)
    },
    tryRead() {
      const id = this.getReadingProgress(this.id, this.bookInfo.id)
      if (id) {
        this.$router.push({
          path: '/bingoBook/bookRead',
          query: {
            bookId: this.bookInfo.id,
            studentCourseId: this.studentCourseId
          }
        })
        this.saveReadingProgress(this.id, this.bookInfo.id, id)
      } else {
        if (this.bookTree.length === 0) {
          this.$router.push({
            path: '/bingoBook/bookRead',
            query: {
              id: 0,
              bookId: this.bookInfo.id,
              studentCourseId: this.studentCourseId
            }
          })
        } else {
          this.$router.push({
            path: '/bingoBook/bookRead',
            query: {
              id: this.bookTree[0].id,
              bookId: this.bookInfo.id,
              studentCourseId: this.studentCourseId
            }
          })
          this.saveReadingProgress(
            this.id,
            this.bookInfo.id,
            this.bookTree[0].id
          )
        }
      }
    },
    toRead() {
      const id = this.getReadingProgress(this.id, this.bookInfo.id)
      if (id) {
        this.$router.push({
          path: '/bingoBook/bookRead',
          query: {
            bookId: this.bookInfo.id,
            studentCourseId: this.studentCourseId
          }
        })
        this.saveReadingProgress(this.id, this.bookInfo.id, id)
      } else {
        if (this.bookTree.length === 0) {
          this.$router.push({
            path: '/bingoBook/bookRead',
            query: {
              id: 0,
              bookId: this.bookInfo.id,
              studentCourseId: this.studentCourseId
            }
          })
        } else {
          this.$router.push({
            path: '/bingoBook/bookRead',
            query: {
              id: this.bookTree[0].id,
              bookId: this.bookInfo.id,
              studentCourseId: this.studentCourseId
            }
          })
          this.saveReadingProgress(
            this.id,
            this.bookInfo.id,
            this.bookTree[0].id
          )
        }
      }
    },
    read(id) {
      this.saveReadingProgress(this.id, this.bookInfo.id, id)
      this.$nextTick(() => {
        this.$router.push({
          path: '/bingoBook/bookRead',
          query: {
            bookId: this.bookInfo.id,
            studentCourseId: this.studentCourseId
          }
        })
      })
    },
    saveReadingProgress(userId, bookId, chapter) {
      // 获取所有用户的阅读进度数据
      const allProgress =
        JSON.parse(localStorage.getItem('readingProgress')) || {}

      // 如果当前用户没有数据，则初始化
      if (!allProgress[userId]) {
        allProgress[userId] = {}
      }

      // 更新当前用户的书籍进度
      allProgress[userId][bookId] = chapter

      // 保存回 localStorage
      localStorage.setItem('readingProgress', JSON.stringify(allProgress))
    },

    // 获取用户的书籍阅读进度
    getReadingProgress(userId, bookId) {
      // 获取所有用户的阅读进度数据
      const allProgress =
        JSON.parse(localStorage.getItem('readingProgress')) || {}

      // 返回指定用户和书籍的当前章节
      return allProgress[userId]?.[bookId] || null
    },
    async _getBook() {
      const { data } = await getBook({
        bookId: this.$route.query.id,
        scene: 'BOOK_CATALOGUE_OWN'
      })
      this.bookInfo = data
      this.goodsComm = data.goodsComm
    },
    async _getBookCatalogue() {
      const { data } = await getBookCatalogue(
        {
          bookId: this.$route.query.id,
          type: 'CHAPTER'
        },
        {
          authorization: this.token
        }
      )
      this.bookTree = data
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .van-nav-bar__left {
  font-size: 14px;
}
::v-deep .van-tabs__wrap {
  height: 32px;
}
::v-deep .van-tab {
  font-size: 14px;
  height: 30px;
  line-height: 30px;
}
::v-deep .van-button__text {
  font-size: 14px;
}
.payContent {
  width: 100%;
  padding: 30px;
  padding-top: 10px;
  p {
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
  }
  .price {
    color: #2f80ed;
    font-size: 30px;
    margin-top: 80px;
  }
  .code_input {
    ::v-deep .el-input__inner {
      height: 50px;
      font-size: 14px;
    }
  }
}
.description {
  padding: 10px;
  margin-top: 10px;
  overflow: auto;
  font-size: 12px;
  line-height: 20px;
  .video_cover{
    width: 100%;
  }
  .button_expend{
    color: #2f80ed;
    width: 100%;
    text-align: right;
  }
}
.tree {
  padding: 10px;
  margin-top: 10px;
  height: 325px;
  overflow: auto;
}
.tree ul {
  list-style-type: none;
  padding-left: 0;
}

.tree li {
  margin: 5px 0;
}

.tree .arrow-icon {
  margin-left: 10px;
  font-size: 16px;
  color: #999;
}
::v-deep .van-tabs__line {
  background: #2f80ed;
}
.book_content {
  width: 100%;
  min-height: 100%;
  background: #f2f2f2;
  overflow: hidden;
  padding-top: 60px;
  padding-bottom: 60px;
}
::v-deep .van-nav-bar {
  background: #ffffff;
  width: 100%;
  height: 46px;
  position: fixed;
  z-index: 2;
  top:0;
  left: 0;
}
::v-deep .van-nav-bar__text {
  color: #000000 !important;
}
::v-deep .van-icon-arrow-left {
  color: #000000 !important;
}
.content {
  padding: 10px;
  margin-bottom: 10px;
  .bookDescription {
    width: 100%;
    height: 100%;
    padding: 10px;
    position: relative;
    margin-top: -20px;
    .title {
      font-size: 14px;
      font-weight: bold;
      margin-top: 10px;
    }
    .description {
      background: #ffffff;
      border-radius: 5px;
    }
  }
  .bottom_group {
    width: 100%;
    height: 80px;
    background: #ffffff;
    position: fixed;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    padding: 20px;
    .button {
      width: 158px;
      height: 50px;
    }
  }
  .bookInfo {
    width: 100%;
    // background: #ffffff;
    border-radius: 5px;
    padding: 10px;
    position: relative;
    min-height: 180px;
    .title {
      font-size: 17px;
      font-weight: 500;
      margin-bottom: 10px;
      width: 62%;
      margin-left: 38%;
    }
    .des {
      font-size: 12px;
      line-height: 30px;
      width: 65%;
      margin-left: 38%;
    }
    .price {
      color: red;
      font-weight: 600;
    }
    .cover {
      width: 108px;
      height: 127px;
      object-fit: cover;
      position: absolute;
      left: 10px;
      bottom: 35px;
    }
  }
}
.share {
  width: 53px;
}
</style>
