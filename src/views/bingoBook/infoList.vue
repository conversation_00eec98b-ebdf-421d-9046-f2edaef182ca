<template>
  <div class="info_main">
    <van-nav-bar left-text="返回" left-arrow @click-left="$router.push('/bingoBook/home')" />
    <div class="top_content">
      <img
        :src="avatar
          ? avatar
          : require('../../assets/publishingReview/default_avator.png')
        "
        class="avatar"
        @click="confirmAvatarChange"
      />
      <van-uploader
        ref="uploader"
        :before-read="beforeRead"
        :after-read="afterRead"
        accept="image/*"
        style="display: none"
      />
    </div>
    <div class="edit">
      <van-cell is-link :value="name" title="姓名" @click="showEditPopup=true" />
      <van-cell is-link :value="mobile" title="手机" @click="passwordPopup=true" />
      <van-cell is-link value="******" title="密码" @click="showPopup=true" />
    </div>
    <div class="login_out" @click="logOut">退出登录</div>
    <van-dialog
      v-model="showPopup"
      title="修改密码"
      show-confirm-button
      confirm-button-text="确认"
      confirm-button-color="#2f80ed"
      show-cancel-button
      @confirm="onConfirm"
    >
      <div class="form">
        <van-field
          v-model="mobile"
          label="手机号"
          placeholder="手机号"
          :maxlength="20"
          disabled
        />
        <van-field
          v-model="verificationCode"
          label="验证码"
          placeholder="请输入验证码"
          type="number"
          maxlength="6"
        >
          <!-- 验证码按钮 -->
          <template #button>
            <van-button
              plain
              size="mini"
              :disabled="isButtonDisabled"
              @click="sendVerificationCodePassWord"
            >
              {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
            </van-button>
          </template>
        </van-field>
        <van-field
          v-model="newPassword"
          type="password"
          label="新密码"
          placeholder="请输入新密码"
          :maxlength="20"
          clearable
        />
        <!-- <van-field
          v-model="confirmPassword"
          type="password"
          label="确认新密码"
          placeholder="请再次输入新密码"
          :maxlength="20"
          clearable
        /> -->
      </div>
    </van-dialog>
    <!-- 修改头像和昵称弹窗 -->
    <van-dialog
      v-model="showEditPopup"
      title="修改昵称"
      show-confirm-button
      confirm-button-text="确认"
      confirm-button-color="#2f80ed"
      show-cancel-button
      @confirm="onConfirm1"
    >
      <div class="form">
        <van-field
          v-model="nickname"
          label="昵称"
          placeholder="请输入新的昵称"
          maxlength="20"
          clearable
        />
      </div>
    </van-dialog>
    <van-dialog
      v-model="passwordPopup"
      title="更换绑定手机号"
      show-confirm-button
      confirm-button-text="确认"
      confirm-button-color="#2f80ed"
      show-cancel-button
      @confirm="confirmChange"
    >
      <div class="form">
        <van-field
          v-model="newPhoneNumber"
          label="新手机号"
          placeholder="请输入新手机号"
          type="tel"
          maxlength="11"
        />
        <van-field
          v-model="verificationCode"
          label="验证码"
          placeholder="请输入验证码"
          type="number"
          maxlength="6"
        >
          <!-- 验证码按钮 -->
          <template #button>
            <van-button
              plain
              size="mini"
              :disabled="isButtonDisabled"
              @click="sendVerificationCode"
            >
              {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
            </van-button>
          </template>
        </van-field>
        <!-- 确认按钮 -->
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { Dialog, Toast } from 'vant'
import { updatePassword, updateUserInfo, updateMobile, verifyCodeForWeb } from '@/api/user-api'
import axios from 'axios'
import { getToken } from '@/utils/auth'
export default {
  data() {
    return {
      showPopup: false,
      showEditPopup: false,
      passwordPopup: false,
      newPassword: '',
      oldPassword: '',
      confirmPassword: '',
      nickname: '',
      newPhoneNumber: '',
      verificationCode: '',
      countdown: 0,
      countdownInterval: null

    }
  },
  computed: {
    ...mapGetters(['name', 'id', 'avatar', 'mobile']),
    isButtonDisabled() {
      return this.countdown > 0
    }
  },
  mounted() {
    this.nickname = this.name
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval)
    }
  },
  methods: {
    sendVerificationCodePassWord() {
      verifyCodeForWeb({ mobile: this.mobile }).then(res => {
      })
      this.$toast.success('验证码已发送')
      // 开启倒计时
      this.countdown = 60
      this.countdownInterval = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.countdownInterval)
        }
      }, 1000)
    },
    sendVerificationCode() {
      if (!this.newPhoneNumber || this.newPhoneNumber.length !== 11) {
        this.$toast.fail('请输入有效的手机号码')
        return
      }

      verifyCodeForWeb({ mobile: this.newPhoneNumber }).then(res => {
      })
      this.$toast.success('验证码已发送')
      // 开启倒计时
      this.countdown = 60
      this.countdownInterval = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.countdownInterval)
        }
      }, 1000)
    },
    confirmChange() {
      if (!this.verificationCode || this.verificationCode.length !== 4) {
        this.$toast.fail('请输入有效的验证码')
        return
      }

      updateMobile({
        mobileOrEmailNew: this.newPhoneNumber,
        code: this.verificationCode
      }).then(res => {
        if (res.code === 200) {
          this.$toast.success('手机号已更换')
          this.passwordPopup = false // 关闭弹窗
          this.$store.dispatch('user/GetInfo')
        }
      })
    },
    // 点击头像触发的确认更换头像对话框
    confirmAvatarChange() {
      Dialog.confirm({
        title: '更换头像',
        message: '是否要更换头像？',
        confirmButtonColor: '#2f80ed'
      })
        .then(() => {
          // 确认更换后，触发文件选择器
          this.$refs.uploader.chooseFile()
        })
        .catch(() => {
          // 取消操作，不执行任何动作
        })
    },

    // 上传前的检查方法
    beforeRead(file) {
      // 检查文件类型，限制为图片类型
      if (!file || !file.type.startsWith('image/')) {
        Toast.fail('请选择图片文件')
        return false
      }
      return true
    },

    // 文件读取后的方法，进行上传
    afterRead(file) {
      Toast.loading({ message: '上传中...', duration: 0 })
      // 使用 FormData 上传图片文件示例
      const formData = new FormData()
      formData.append('file', file.file)
      const headers = {
        'Authorization': getToken()
      }
      axios
        .post(`${process.env.VUE_APP_BASE_API}/api/v1/students/avatar`, formData, { headers }
        )
        .then((response) => {
          Toast.clear()
          Toast.success('头像更换成功')
          this.$store.dispatch('user/GetInfo')
        })
        .catch((_error) => {
          Toast.clear()
          Toast.fail('上传失败')
        })
    },

    // 更新头像 URL（具体实现根据你的应用逻辑）
    onConfirm1() {
      // 校验昵称输入
      if (!this.nickname) {
        this.$toast.fail('请输入昵称')
        return
      }
      this.updateProfile()
    },
    updateProfile() {
      updateUserInfo({ displayName: this.nickname }).then(res => {
        if (res.code === 200) {
          this.$store.dispatch('user/EditName', this.nickname)
          this.showEditPopup = false
          this.$toast.success('修改成功')
        }
      })
    },
    toHelp() {
      this.$router.push('/bingoBook/help')
    },
    toClass() {
      this.$router.push('/bingoBook/myClasslist')
    },
    onConfirm() {
      // 校验输入
      if (!this.newPassword || !this.verificationCode) {
        this.$toast.fail('请输入所有字段')
        return
      }
      if (this.newPassword.length < 6 || this.newPassword.length > 20) {
        this.$toast.fail('密码长度在6-20之间')
        return
      }
      // 调用修改密码的接口
      this.changePassword()
    },
    changePassword() {
      updatePassword({
        mobileOrEmail: this.mobile,
        passwordNew: this.newPassword,
        code: this.verificationCode
      }).then(res => {
        if (res.code === '200') {
          this.$toast.success('密码修改成功！请重新登录')
          this.showPopup = false
          this.oldPassword = ''
          this.newPassword = ''
          this.confirmPassword = ''
          this.verificationCode = ''
          this.$store.dispatch('user/FedLogOut')
          this.$router.push('/bingoBook')
        }
      })
    },
    async logOut() {
      Dialog.confirm({
        message: '确认退出登录吗？',
        confirmButtonColor: '#2F80ED'
      })
        .then(async () => {
          await this.$store.dispatch('user/FedLogOut')
          this.$router.push('/bingoBook')
        })
        .catch(() => {
          // on cancel
        })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .van-cell__right-icon {
  margin-top: 10px;
}
.form{
  ::v-deep .van-cell__title {
  font-size: 14px;
  white-space: nowrap;
}
::v-deep .van-field__control{
  font-size: 12px;
}
}
::v-deep .van-cell__title {
  font-size: 18px;
  padding: 10px;
}

::v-deep .van-cell__value {
  font-size: 18px;
  padding: 10px;
}

.info_main {
  width: 100%;
  height: 100%;
  background: #f2f2f2;
}

.top_content {
  width: 100%;
  height: 153px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  overflow: hidden;

  .avatar {
    display: block;
    overflow: hidden;
    width: 60px;
    height: 60px;
    border-radius: 30px;
    margin: 20px auto;
    object-fit: cover;
  }

  .name {
    width: 100%;
    text-align: center;
    font-size: 12px;
  }

  .button {
    width: 100%;
    text-align: center;
    color: #2f80ed;
  }
}

.edit {
  padding: 10px;
}

::v-deep .van-nav-bar__content {
  background: #f2f2f2;
  border: none;
  height: 46px;
}

::v-deep .van-nav-bar__text {
  color: #000000 !important;
}
::v-deep .van-icon-arrow-left {
  color: #000000 !important;
}

.login_out {
  font-size: 16px;
  font-weight: 500;
  color: #2F80ED;
  width: 100%;
  text-align: center;
  margin-top: 200px;
  text-decoration: underline;
}
</style>
