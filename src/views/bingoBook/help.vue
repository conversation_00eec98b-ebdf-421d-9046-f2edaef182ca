<template>
  <div class="help_content">
    <van-nav-bar
      title="帮助关于"
      left-text="返回"
      left-arrow
      @click-left="$router.push('/bingoBook/home')"
    />
    <div class="content">
      <img class="logo" src="../../assets/bingoBook/logo.png" alt="" />
      <img class="wechat" src="../../assets/bingoBook/wechat.png" alt="" @click="toHlep" />
      <p class="website">官方网站：https://cuiya.cn</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
    }
  },
  methods: {
    toHlep() {
      window.location.href = 'https://work.weixin.qq.com/kfid/kfcd7dd17653a0e225f'
    }
  }
}
</script>

<style scoped lang="scss">
    ::v-deep .van-nav-bar__left{
    font-size: 14px;
    }
    ::v-deep .van-nav-bar__title{
      font-size: 16px;
      height: 20px;
      line-height: 20px;
    }
    .logo{
        width: 174px;
        height: 68px;
        display: block;
        margin: 40px auto;
    }
    .wechat{
        width: 89px;
        height: 38px;
        display: block;
        margin: 0 auto;
        margin-top: 300px;
    }
    .website{
        font-size: 14px;
        width: 100%;
        margin: 0 auto;
        text-align: center;
        margin-top: 30px;
    }
    .help_content{
        width: 100%;
        height: 100%;
        background: #F2F2F2;
        overflow: hidden;
    }
    ::v-deep .van-nav-bar__content{
        background: #F2F2F2;
        height: 46px;
    }
    ::v-deep .van-nav-bar__text{
        color: #000000 !important;
    }
    ::v-deep .van-icon-arrow-left{
        color: #000000 !important;
    }
    .content {
    padding: 16px;
    }
</style>
