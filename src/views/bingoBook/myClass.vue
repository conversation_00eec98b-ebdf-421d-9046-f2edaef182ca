<template>
  <div class="class_content">
    <van-nav-bar left-text="返回" left-arrow @click-left="$router.push('/bingoBook/home')" />
    <div class="content">
      <div v-for="item in classList" :key="item.id" class="class_item">
        <p> {{ item.name }}</p>
        <p>{{ item.school.name }}</p>
        <p class="quit" @click="quitClass(item)">退出班级</p>
      </div>
      <div class="class_item">
        <p class="p1">加入班级</p>
        <p class="p2">输入班级邀请码</p>
        <div class="add">
          <van-icon name="plus" size="30" @click="showPopup = true" />
        </div>
      </div>
    </div>
    <van-popup v-model="showPopup" closeable position="bottom" :style="{ height: '50%' }">
      <div class="popup-content">
        <div class="input-scan-container">
          <van-field v-model="classCode" placeholder="请输入班级邀请码" clearable class="input-field">
            <!-- <template #right-icon>
              <van-icon name="scan" class="scan-icon" @click="scanCode" />
            </template> -->
          </van-field>
        </div>
        <p class="tips">*班级邀请码由老师提供</p>
        <van-button class="join" type="info" block @click="joinClass">加入班级</van-button>
      </div>
    </van-popup>
    <scan v-if="showScan" @close="showScan = false" @ok="scanOk" @err="scanErr" />
  </div>
</template>
<script>
import { joinClass, getUserClassList, quitClass, getClassInfo } from '@/api/digital-api.js'
import { Dialog } from 'vant'
import scan from './components/qrRead.vue'
export default {
  components: { scan },
  data() {
    return {
      classList: [],
      showPopup: false,
      showScan: false,
      classCode: ''
    }
  },
  mounted() {
    this._getUserClassList()
  },
  methods: {
    async quitClass(item) {
      Dialog.confirm({
        message: `确认退出${item.name}?`,
        confirmButtonColor: '#2F80ED'
      }).then(async() => {
        await quitClass({ classUserId: item.userId })
        this.$toast('退出成功')
        this._getUserClassList()
      })
    },
    async _getUserClassList() {
      const { data } = await getUserClassList({
        scene: 'STUDENT_CLASS'
      })
      this.classList = data
    },
    joinClass() {
      if (!this.classCode) {
        this.$toast('请输入邀请码')
        return
      }
      getClassInfo({
        inviteCode: this.classCode
      }).then(res => {
        const name = res.data.name
        Dialog.confirm({
          message: `确认加入${name}?`,
          confirmButtonColor: '#2F80ED'
        })
          .then(async () => {
            await joinClass({
              inviteCode: this.classCode
            })
            this._getUserClassList()
            this.showPopup = false
            this.$toast('加入成功！')
          })
          .catch(() => {
          // on cancel
          })
      })
    },
    scanCode() {
      this.showScan = true
    },
    scanOk (result) {
      this.showScan = false
      this.inputValue = result
    },
    scanErr (error) {
      this.$toast.fail(error)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep van-nav-bar__left {
  font-size: 14px;
}

.class_content {
  width: 100%;
  height: 100%;
  background: #F2F2F2;
  overflow: scroll;

  .class_item {
    width: 361px;
    height: 119px;
    border-radius: 10px;
    background: #ffffff;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 20px;
    padding: 20px;
    position: relative;
    .quit{
      font-size: 14px;
      position: absolute;
      right: 30px;
      color: #409EFF;
      font-weight: bold;
      top:60px
    }
    .add {
      position: absolute;
      right: 20px;
      top: 45px;
    }

    .p1 {
      font-size: 14px;
      font-weight: bold;
    }

    .p2 {
      color: #828282;
      font-size: 12px;
    }
  }
}

::v-deep .van-nav-bar__content {
  background: #F2F2F2;
  border: none;
  height: 46px;
}

::v-deep .van-nav-bar__text {
  color: #000000 !important;
}

::v-deep .van-icon-arrow-left {
  color: #000000 !important;
}

.popup-content {
  padding: 20px;
  margin-top: 30px;
}

.input-field {
  width: 100%;
  font-size: 18px;
  /* 增大字体 */
  border: 1px solid #dcdcdc;
  /* 添加边框 */
  border-radius: 8px;
  /* 边框圆角 */
  padding: 10px;
  /* 内边距调整 */
}

.scan-icon {
  font-size: 24px;
  /* 增大图标大小 */
  color: #409EFF;
  /* 设置图标颜色 */
  margin-right: 8px;
  /* 增加间距 */
}

.join {
  margin-top: 120px;
}

.tips {
  font-size: 12px;
  color: #828282
}
</style>
