<template>
  <div class="task-class">
    <iframe :src="url" frameborder="0"></iframe>
  </div>
</template>

<script>
// import { getToken } from '@/utils/auth'
import { getBook } from '@/api/digital-api.js'
export default {
  components: { },
  data() {
    return {
      bookId: 0,
      studentCourseId: 0,
      url: '',
      bookInfo: null
    }
  },
  watch: {
    async '$route' (to, from) {
      if (to.query.id !== from.query.id) {
        if (this.$route.query && this.$route.query.token) {
          await this.$store.dispatch('user/AppLogin', 'Bearer ' + this.$route.query.token)
        }
        this.bookId = this.$route.query && this.$route.query.id
        await this._getBook()
        this.studentCourseId = this.bookInfo.studentCourseId || 0
        this.url = process.env.VUE_APP_ADMIN_API + `/Home/DigitalBook/graphWapIndex?book_id=${this.bookId}&bought=${this.studentCourseId === 0 ? 0 : 1}`
      }
    }
  },
  async mounted() {
    if (this.$route.query && this.$route.query.token) {
      await this.$store.dispatch('user/AppLogin', 'Bearer ' + this.$route.query.token)
    }
    this.bookId = this.$route.query && this.$route.query.id
    await this._getBook()
    this.studentCourseId = this.bookInfo.studentCourseId || 0
    this.url = process.env.VUE_APP_ADMIN_API + `/Home/DigitalBook/graphWapIndex?book_id=${this.bookId}&bought=${this.studentCourseId === 0 ? 0 : 1}`
    window.addEventListener('message', this.back, false)
  },
  beforeDestroy() {
    window.removeEventListener('message', this.back)
  },
  methods: {
    back() {
      const _this = this
      if (_this.$route.query.from === 'read') {
        _this.$router.replace(`/bingoBook/bookRead?bookId=${_this.$route.query.id}`)
      } else {
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.bingo_download) {
          window.webkit.messageHandlers.bingo_action.postMessage('event_back')
        } else if (window.bingo_action) {
          window.bingo_action.postMessage('event_back')
        } else {
          _this.$router.go(-1)
        }
      }
    },
    async _getBook() {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN'
        })
        this.bookInfo = data
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
    ::v-deep .van-nav-bar__content {
    background: #ffffff;
    height: 46px;
  }
  ::v-deep .van-nav-bar__text {
    color: #000000 !important;
  }
  ::v-deep .van-icon-arrow-left {
    color: #000000 !important;
  }
    .el-loading-parent--relative {
        position: relative;
    }
  ::v-deep .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after{
      border: none;
  }
  .task-class{
    width: 100%;
    height: 100%;
    iframe{
        width: 100%;
        height: 100%;
    }
  }
  </style>
