<template>
  <div class="task-class">
    <van-nav-bar left-text="返回" left-arrow title="任务详情" @click-left="$router.go(-1)">
      <template v-if="type === 1" #right>
        <van-button type="info" class="edot_button" @click="type = 0">编辑</van-button>
      </template></van-nav-bar>
    <div class="task-content">
      <div class="d-content dig-task-box">
        <div class="content-box" @click="readfunc">
          <div v-if="contentType === 'html'" class="editor-content-view" v-html="html"></div>
          <div v-else>
            <div v-for="(item, index) in content" :key="index" class="editor-content-view">
              <p class="title">{{ item.title }}</p>
              <div v-html="item.content"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bouttom">
      <p class="file_list" @click="showFileList = true">查看附件（{{ resourcesList.length }}）</p>
      <van-button v-if="type === 0" class="button" type="info" @click="getFileList">上传任务</van-button>
      <p v-else class="file_list ml20" @click="showUploadFileList = true">已上传：{{ resourcesUserList.length }}个文件</p>
    </div>
    <van-popup v-model="showFileList" closeable round position="bottom" :style="{ height: '50%' }">
      <p class="title">资源列表</p>
      <div v-if="resourcesList.length===0" class="emty">
        <Empty description="暂无数据" />
      </div>
      <div class="file">
        <p
          v-for="(item, index) in resourcesList"
          :key="index"
          :class="activeFileId === item.id ? 'active' : ''"
          @click="toFile(item)"
        >
          {{ item.mediaFile.fileName }}
        </p>
      </div>
    </van-popup>
    <van-popup v-model="showUploadFileList" closeable round position="bottom" :style="{ height: '50%' }">
      <p class="title">文件列表</p>
      <div class="file">
        <p
          v-for="(item, index) in resourcesUserList"
          :key="index"
          :class="activeFileId === item.id ? 'active' : ''"
          @click="toFile(item)"
        >
          {{ item.mediaFile.fileName }}
        </p>
      </div>
    </van-popup>
    <van-popup v-model="showFileUpload" closeable round position="bottom" :style="{ height: '50%' }">
      <p class="title">上传任务
        <span>仅图片或拍照上传，上传文件请用PC客户端</span>
      </p>
      <div class="file1">
        <van-uploader v-model="fileList" multiple :before-read="handleAfterRead" :before-delete="deleteFile" />
      </div>
      <van-button type="info" class="button1" block @click="submitTask">提交</van-button>
    </van-popup>
    <imgPop ref="images" :info="imgListInfo" />
    <tipsPop ref="tips" :info="tipsInfo" />
  </div>
</template>

<script>
import { digitalTask, userDigitalHomework, addResource, getBook, deleteResource } from '@/api/digital-api.js'
// import taskDetail from '../detail.vue'
// import Empty from '@/components/classPro/Empty/index.vue'
import { saveAs } from 'file-saver'
import { mapGetters } from 'vuex'
import { throttle } from '@/utils/index'
import imgPop from './components/imgPop.vue'
import tipsPop from './components/tipsPop.vue'
import { getSqlPlatformToken } from '@/api/training-api'
import { getFileUploadAuthor } from '@/api/user-api'
import { Dialog } from 'vant'
import axios from 'axios'
import { Empty } from 'vant'
export default {
  components: { imgPop, tipsPop, Empty },
  data() {
    return {
      studentCourseId: 0,
      taskId: '',
      currentDigitalHomework: null,
      contentType: 'json',
      content: '',
      html: '',
      imgListInfo: null,
      tipsInfo: null,
      resourcesList: [],
      showFileList: false,
      activeFileId: '',
      showFileUpload: false,
      showUploadFileList: false,
      resourcesUserList: [],
      homeworkId: '',
      type: 0,
      fileList: []
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  async mounted() {
    this.taskId = this.$route.query && this.$route.query.taskId
    this.studentCourseId = this.$route.query && this.$route.query.studentCourseId
    await this._getBook()
    this.getTaskInfo()
    if (this.id) {
      this._userDigitalHomework()
    }
  },
  methods: {
    getFileList() {
      this._userDigitalHomework()
      this.showFileUpload = true
    },
    async deleteFile(data, index) {
      console.log(this.resourcesUserList)
      if (this.resourcesUserList[index.index].id) {
        deleteResource({
          coursecommUnitResourceId: this.resourcesUserList[index.index].id
        })
      }
      this.fileList.splice(index.index, 1)
      this.resourcesUserList.splice(index.index, 1)
      // if (this.resourcesList.length !== 0) {
      //   await this.submitTask()
      // }
      // this._userDigitalHomework()
    },
    async _getBook() {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN'
        })
        this.bookTitle = data.title
        this.bookInfo = data
      }
    },
    async submitTask() {
      if (this.resourcesUserList && this.resourcesUserList.length === 0) {
        this.$toast('请上传文件后提交')
        return
      }
      let data
      if (this.homeworkId) {
        data = await userDigitalHomework({
          apiType: 'update',
          digitalHomeworkId: this.taskId,
          userId: this.id,
          id: this.homeworkId,
          studentCourseId: this.studentCourseId
        })
      } else {
        data = await userDigitalHomework({
          apiType: 'create',
          digitalHomeworkId: this.taskId,
          userId: this.id,
          studentCourseId: this.studentCourseId
        })
        this.homeworkId = data.data.id
      }
      // 处理文件逻辑

      if (this.resourcesUserList && this.resourcesUserList.length) {
        const arr = []

        this.resourcesUserList.map(val => {
          if (!val.mediaFileId) {
            arr.push(val.mediaFile)
          }
        })
        this.fileList = this.resourcesUserList.map((item) => {
          return {
            url: item.mediaFile.url.indexOf('https://') !== -1 ? item.mediaFile.url : item.ossUrl + '/' + item.mediaFile.url
          }
        })
        // 上传
        await addResource({
          sourceId: this.taskId,
          resourceType: 'DIGITAL_HOMEWORK_USER_RESOURCE'
        }, arr)
      }
      this.$toast('提交成功！')
      this.showFileUpload = false
    },
    async getOssSign(mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    async handleAfterRead(file) {
      let mediaType = ''
      if (file.type.indexOf('video/') > -1) {
        mediaType = 'VIDEO'
      } else if (file.type.indexOf('image/') > -1) {
        mediaType = 'IMAGE'
      } else {
        mediaType = 'FILE'
      }

      const { data } = await this.getOssSign(mediaType, file.name)
      const ossCDN = data[0].ossConfig.ossCDN
      this.ossUrl = data[0].ossConfig.host
      this.progress = true
      const filename = file.name

      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)

      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })

      const fileObj = {
        mediaFile: {
          size: Math.floor(file.size / 1024),
          type: mediaType,
          fileName: filename.substring(0, filename.lastIndexOf('.')),
          url: data[0].fileName,
          expendType: filename.substring(filename.lastIndexOf('.') + 1)
        },
        mediaFileId: 0,
        ossUrl: ossCDN
      }

      this.resourcesUserList.push(fileObj)
      this.fileList = this.resourcesUserList.map((item) => {
        return {
          url: item.mediaFile.url.indexOf('https://') !== -1 ? item.mediaFile.url : item.ossUrl + '/' + item.mediaFile.url
        }
      })
      return Promise.reject()
    },
    async _userDigitalHomework() {
      const userId = this.id
      const { data } = await userDigitalHomework({
        userId: userId,
        apiType: 'get',
        digitalHomeworkId: this.taskId,
        studentCourseId: this.studentCourseId
      })
      if (data) {
        // 学生提交过的作业id
        this.homeworkId = data.id
        this.resourcesUserList = data.coursecommUnitResourceList
        this.fileList = this.resourcesUserList.map((item) => {
          return {
            url: item.mediaFile.url.indexOf('https://') !== -1 ? item.mediaFile.url : item.ossUrl + '/' + item.mediaFile.url
          }
        })
        this.type = 1
      } else {
        this.homeworkId = ''
        this.resourcesUserList = []
        this.fileList = []
        this.type = 0
      }
    },
    playVideoInFullscreen(videoUrl) {
      // 使用 Vant 的 Dialog 创建一个弹窗，内部包含视频元素
      Dialog({
        message: `<video id="popup-video" src="${videoUrl}" controls autoplay style="width: 100%;"></video>`,
        className: 'custom-video-dialog',
        closeOnClickOverlay: true, // 点击空白处关闭弹窗
        showConfirmButton: false, // 隐藏确认按钮
        closeOnPopstate: true // 在浏览器返回时关闭弹窗
      }).then(() => {
        // 弹窗关闭时停止视频播放
        const videoElement = document.getElementById('popup-video')
        if (videoElement) {
          videoElement.pause()
          videoElement.src = '' // 清除视频源
        }
      })
    },
    toFile(item) {
      this.activeFileId = item.id
      const url = item.mediaFile.url
      if (this.getFileType('', item.mediaFile.expendType) === 'video') {
        this.playVideoInFullscreen(url)
      } else if (this.getFileType('', item.mediaFile.expendType) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(item.mediaFile.size)) {
          this.$toast('暂不支持大于200M的附件预览')
          return
        }
        this.$router.push({
          path: '/bingoBook/officeView',
          query: { url: url }
        })
      } else if (this.getFileType('', item.mediaFile.expendType) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.images.open()
      } else {
        this.$toast('该类型文件暂不支持预览')
      }
    },
    async getTaskInfo() {
      const { data } = await digitalTask({
        apiType: 'get',
        id: this.taskId
      })
      this.contentType = this.isJSON(data.content) ? 'json' : 'html'
      this.html = data.content
      this.content = this.isJSON(data.content) ? JSON.parse(data.content) : []
      console.log(this.content)
      this.taskTitle = data.title
      this.resourcesList = data.resourcesList
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    },
    isJSON(str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      console.log(url, fileName)
      if (this.getFileType(fileName) === 'video') {
        this.playVideoInFullscreen(url)
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$toast('暂不支持大于200M的附件预览')
          return
        }
        this.$router.push({
          path: '/bingoBook/officeView',
          query: { url: url }
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.images.open()
      } else {
        this.$toast('该类型文件暂不支持预览')
      }
    },
    getFileType(fileName, type) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase() || type
      // 定义不同类型的文件后缀
      const imageExtensions = [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'bmp',
        'svg',
        'webp'
      ]
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv', 'MP4']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = [
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'pdf'
      ]
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    isWeChatBrowser() {
      const ua = navigator.userAgent.toLowerCase()
      return ua.includes('micromessenger')
    },
    isFileSizeGreaterThan200MB(sizeStr) {
      sizeStr = String(sizeStr)
      const sizeUnit = isNaN(sizeStr.slice(-2)) ? sizeStr.slice(-2).toUpperCase() : 'KB'
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    readfunc(e) {
      if (e.target.classList.contains('img_card_button')) {
        this.imgListInfo = JSON.parse(
          e.target.parentNode.getElementsByClassName('info')[0].innerText
        )
        console.log(this.imgListInfo)
        this.$refs.images.open()
      }
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        console.log(item, item.children)
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.open()
      }
      if (e.target.parentNode.classList.contains('file_download')) {
        const item = e.target.parentNode.children[3].children[0]
        this.downloadFun(item)
      }
      if (e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          if (this.isWeChatBrowser()) {
            const url = e.target.parentNode.children[1].innerText
            this.$copyText(url)
            this.$toast({
              message: '链接已复制，请前往默认浏览器打开下载',
              duration: 5000
            })
          } else {
            const url = e.target.parentNode.children[1].innerText
            const fileName = e.target.parentNode.children[2].innerText
            throttle(function () {
              const xhr = new XMLHttpRequest()
              xhr.open('get', url)
              xhr.responseType = 'blob'
              xhr.addEventListener('progress', (e) => { })
              xhr.send()
              xhr.onload = function () {
                if (this.status === 200 || this.status === 304) {
                  const blob = new Blob([this.response], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
                  })
                  saveAs(blob, fileName)
                }
              }
            }, 2000)
          }
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        const testId = e.target.parentNode.getAttribute('data-id')
        const ids = e.target.parentNode.getAttribute('data-ids')
        const studentCourseId = this.studentCourseId || this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId
        this.$router.push({ path: '/bingoBook/test', query: { testId, ids, studentCourseId }})
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.edot_button {
  width: 52px;
  height: 28px;
  font-size: 12px;
  white-space: nowrap;
  border-radius: 5px;
}

.ml20 {
  padding-left: 30px;
}

::v-deep .van-nav-bar__content {
  background: #ffffff;
  height: 46px;
}

::v-deep .van-nav-bar__text {
  color: #000000 !important;
}

::v-deep .van-icon-arrow-left {
  color: #000000 !important;
}

.el-loading-parent--relative {
  position: relative;
}

::v-deep .van-hairline--top-bottom::after,
.van-hairline-unset--top-bottom::after {
  border: none;
}

.task-class {
  width: 100%;
  min-height: 100%;
  background: #EFF6FF;
  padding-bottom: 80px;

  .editor-content-view {
    width: 90%;
    margin: 0 auto;
    margin-top: 20px;
    background: #ffffff;
    border-radius: 5px;
    padding: 10px;

    ::v-deep * {
      max-width: 100%;
    }
  }

  .bouttom {
    width: 100%;
    height: 80px;
    background: #ffffff;
    border-top: 1px solid #E0E0E0;
    position: fixed;
    bottom: 0;
    display: flex;

    .file_list {
      font-size: 14px;
      color: #2F80ED;
      text-decoration: underline;
      margin-left: 30px;
      margin-top: 25px;
    }

    .button {
      width: 142px;
      height: 42px;
      margin-left: 60px;
      margin-top: 10px;
      border-radius: 5px;
    }
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-top: 20px;

    span {
      color: #828282;
      font-size: 10px;
      margin-left: 20px;
    }
  }

  .file {
    margin-top: 0px;
    padding: 10px;
    height: 300px;
    overflow: scroll;

    p {
      width: 100%;
      height: 30px;
      font-size: 14px;
      line-height: 30px;
      font-weight: 500;
      padding-left: 10px;
    }

    .active {
      background: #E6F0FF;
      color: #2F80ED;
    }
  }

  .file1 {
    height: 210px;
    overflow-y: auto;
    border-bottom: 1px solid #E0E0E0;
  }

  .button1 {
    width: 250px;
    height: 42px;
    border-radius: 5px;
    margin: 0 auto;
    margin-top: 10px;
  }

  .file_upload {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
}
</style>
