<template>
  <van-loading v-if="loading" />
  <div v-else class="w h">
    <van-nav-bar
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
    />
    <div v-if="!hasData" class="empty">
      <img class="empty-img" src="@/assets/images/empty.png" alt="暂无数据" />
      <div>暂无数据</div>
    </div>
    <div v-else>
      <div ref="ratioBox" class="radio-box">
        <div id="whiteboard" class="w h"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Loading } from 'vant'
import { generateWebofficeToken, refreshWebofficeToken } from '@/api/aliyun-api'
import { getToken } from '@/utils/auth'
export default {
  data() {
    return {
      currPage: -1,
      total: 0,
      boardClient: null,
      scenes: null,
      sceneUUid: null,
      tokenList: null,
      token: {
        boardToken: '',
        boardUuid: ''
      },
      loading: false,
      hasData: false
    }
  },
  computed: {
    ...mapGetters(['id'])
  },
  mounted() {
    this.$nextTick(() => {
      this._getGenerateToken([this.$route.query.url], getToken() || this.$route.query.token)
    })
  },
  methods: {

    async _getGenerateToken(list, token) {
      if (token) {
        this.accessToken = token
      }
      if (list.length === 0) {
        return false
      }
      this.hasData = true
      const header = this.accessToken === '' ? {} : { authorization: this.accessToken }
      const { data } = await generateWebofficeToken({
        fileUrl: list[0]
        // coursecommUnitResourceId: list[0].id
      }, header)
      this.tokenList = data.body
      this.weboffice({
        AccessToken: data.body.accessToken,
        WebofficeURL: data.body.webofficeURL
      })
    },
    weboffice(tokenInfo) {
      var mount = document.getElementById('whiteboard')
      var demo = window.aliyun.config({
        mount: mount,
        url: tokenInfo.WebofficeURL,
        refreshToken: this.refreshTokenPromise
      })
      demo.setToken({
        token: tokenInfo.AccessToken,
        timeout: 25 * 60 * 1000
      })
    },
    async refreshTokenPromise() {
      const header = this.accessToken === '' ? {} : { authorization: this.accessToken }
      const { data } = await refreshWebofficeToken({
        accessToken: this.tokenList.accessToken,
        refreshToken: this.tokenList.refreshToken
      }, header)
      return {
        token: data.body.accessToken,
        timeout: 10 * 60 * 1000
      }
    }

  },
  components: {
    [Loading.name]: Loading
  }
}
</script>

  <style scoped lang="scss">
     ::v-deep .van-nav-bar__content{
      height: 46px;
      }
      ::v-deep .van-nav-bar__text{
          color: #000000 !important;
      }
      ::v-deep .van-icon-arrow-left{
          color: #000000 !important;
      }
  .radio-box {
    width: 100vw;
    height: 100vh;
  }

  .control-box {
    /* 样式 */
  }

  .empty-img {
    width: 126px;
    height: 126px;
  }
  </style>
