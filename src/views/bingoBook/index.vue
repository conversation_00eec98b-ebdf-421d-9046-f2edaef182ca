<template>
  <div class="main">
    <div class="tab">
      <img class="tab_item" :src="tabIndex===0?require('../../assets/bingoBook/tab1_active.png'):require('../../assets/bingoBook/tab1.png')" alt="" @click="tabIndex=0" />
      <img class="tab_item" :src="tabIndex===1?require('../../assets/bingoBook/tab2_active.png'):require('../../assets/bingoBook/tab2.png')" alt="" @click="tabIndex=1" />
      <img class="tab_item1" :src="tabIndex===2?require('../../assets/bingoBook/tab3_active.png'):require('../../assets/bingoBook/tab3.png')" alt="" @click="tabIndex=2" />
    </div>
    <div class="main_view">
      <MyCourse v-if="tabIndex===0" @setType="tabIndex=1" />
      <courseStore v-if="tabIndex===1" />
      <myInfo v-if="tabIndex===2" />
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import MyCourse from './components/myCourse.vue'
import courseStore from './components/courseStore.vue'
import myInfo from './components/myInfo.vue'
import { getToken } from '@/utils/auth'
export default {
  components: { MyCourse, courseStore, myInfo },
  data () {
    return {
      tabIndex: 1

    }
  },
  computed: {
    ...mapGetters([
      'name',
      'id'
    ])
  },
  watch: {
    'tabIndex'(val) {
      if (!getToken() && this.tabIndex !== 1) {
        this.$router.push('/bingoBook/login')
      }
    }
  },
  activated() {
    if (!getToken()) {
      this.tabIndex = 1
    } else {
      this.$store.dispatch('user/GetInfo')
    }
    if (this.$route.query.flag) {
      const flag = this.$route.query.flag
      this.tabIndex = flag === 'my' ? 0 : 1
    }
  },
  mounted() {
    this.tabIndex = getToken() ? 0 : 1
  },
  methods: {

  }

}
</script>

<style lang="scss" scoped>
.main{
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #ffffff;
  .tab{
    width: 375px;
    height: 87px;
    border-top: 1px solid #E0E0E0;
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    z-index: 999;
    padding: 30px;
    padding-top: 8px;
    justify-content: space-between;
    background: #ffffff;
    .tab_item{
      width: 48px;
      height: 51px;
    }
    .tab_item1{
      width: 34px;
      height: 51px;
    }
  }
  .main_view{
    width: 100%;
    height: 100%;
    padding-bottom: 87px;
    overflow-y:scroll;
  }
}
</style>
