<template>
  <div v-if="show" class="main">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="笔记列表"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <!-- 笔记列表展示 -->
    <van-cell-group>
      <van-cell
        v-for="(note, index) in noteList"
        :key="index"
        :title="note.selection"
        :label="note.notes.slice(0, 30) + '...' "
        is-link
        @click="showPopup(note)"
      />
    </van-cell-group>
    <div v-if="noteList&&noteList.length === 0" class="emty">
      <Empty description="暂无数据" />
    </div>
    <!-- 笔记详情 Popup -->
    <van-popup v-model="showNotePopup" closeable position="bottom" :style="{ height: '60%'}" round>
      <div class="note_main">
        <p class="soyin">引用：{{ currentNote.selection }} <span class="button_soyin" @click="backToView">查看原文</span></p>
        <van-form v-if="isEditMode">
          <el-input v-model="currentNote.notes" placeholder="请输入笔记" label="笔记内容" rows="11" type="textarea" />
        </van-form>
        <p v-else class="soyin1">{{ currentNote.notes }}</p>
        <!-- 保存按钮 -->
        <van-button v-if="isEditMode" class="button" type="info" @click="addNoteDone">保存笔记</van-button>
        <div v-else class="buttonGroup">
          <van-button class="button_edit" @click="isEditMode = true">编辑</van-button>
          <van-button class="button_delete" @click="deleteNote">删除</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { digitalNotes, getDigitalNotesList } from '@/api/digital-api.js'
import { Dialog } from 'vant'
import { Empty } from 'vant'
import { debounce } from '@/utils/index'
export default {
  components: { Empty },
  props: {
    catalogueId: {
      type: Number,
      default: 0
    },
    bookId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      noteList: [
      ],
      highLineData: [],
      showNotePopup: false, // 控制 Popup 显示
      isEditMode: false, // 是否为编辑模式
      currentNote: {}, // 当前查看或编辑的笔记
      show: false
    }
  },
  activated() {
    this.getNotList()
    this.getHighLine()
  },
  methods: {
    backToView() {
      const json = this.highLineData.filter(item => {
        return JSON.parse(item.selection).text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '') === this.currentNote.selection.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
      })[0].selection
      this.$emit('backToView', JSON.parse(json).id)
      this.goBack()
    },
    open() {
      this.show = true
      this.getNotList()
      this.getHighLine()
    },
    async getHighLine () {
      this.highLineList = []
      const { data } = await getDigitalNotesList({
        catalogueId: this.catalogueId,
        digitalNotesType: 'hightline'
      })
      this.highLineData = data
    },
    deleteNote() {
      Dialog.confirm({
        message: `确认删除笔记?`,
        confirmButtonColor: '#2F80ED'
      }).then(() => {
        digitalNotes({
          catalogueId: this.catalogueId,
          apiType: 'delete',
          id: this.currentNote.id
        }).then(() => {
          this.getNotList()
          this.$toast('删除成功！')
          this.showNotePopup = false
        })
        const deItem = this.highLineData.filter(item => {
          return JSON.parse(item.selection).text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '') === this.currentNote.selection.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
        })[0]
        digitalNotes({
          catalogueId: this.catalogueId,
          apiType: 'delete',
          digitalNotesType: 'hightline',
          id: deItem.id
        })
        this.$emit('deleteNote', JSON.parse(deItem.selection).id)
      })
    },
    addNoteDone: debounce(async function () {
      if (this.currentNote.notes === '') {
        this.$toast('笔记内容不能为空！')
        return
      }
      digitalNotes({
        catalogueId: this.catalogueId,
        apiType: 'update',
        digitalNotesType: 'notes',
        id: this.currentNote.id,
        selection: this.currentNote.selection,
        notes: this.currentNote.notes
      }).then(() => {
        this.getNotList()
        this.$toast('保存成功！')
        this.showNotePopup = false
      })
    }, 100),
    async getNotList() {
      const { data } = await getDigitalNotesList({
        catalogueId: this.catalogueId,
        digitalNotesType: 'notes'
      })
      this.noteList = data || []
    },
    goBack() {
      // this.$router.replace(`/bingoBook/bookRead?bookId=${this.$route.query.bookId}`) // 返回上一页
      this.show = false
      this.showNotePopup = false
      this.isEditMode = false
      this.noteList = []
      this.highLineData = []
    },
    showPopup(note) {
      this.currentNote = { ...note } // 显示选中的笔记
      this.showNotePopup = true // 打开 Popup
      this.isEditMode = false // 默认非编辑模式
    },
    closePopup() {
      this.showNotePopup = false // 关闭 Popup
    },
    saveNote() {
      // 在实际场景下，你可能需要通过 API 调用保存到服务器
      console.log('保存笔记', this.currentNote)
      this.showNotePopup = false // 保存后关闭 Popup
    }
  }
}
</script>

  <style scoped lang="scss">
  .main{
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    background: #F2F2F2;
    position: absolute;
    left: 0;
    top:0;
    z-index: 99;
  }
  .note_main{
  width: 100%;
  padding: 20px;
  margin-top: 10px;
  ::v-deep .el-textarea__inner{
    width: 300px;
    margin: 0 auto;
    font-size: 14px;
}
  .soyin{
    font-size: 14px;
    margin-left: 15px;
    font-weight: bold;
    .button_soyin{
      color: #2F80ED;
      margin-left: 10px;
    }
  }
  .soyin1{
    font-size: 14px;
    margin-left: 15px;
    min-height: 250px;
    border: 1px solid #E0E0E0;
    border-radius: 5px;
    padding: 10px;
    width: 95%;
    margin: 0 auto;
  }
  .button{
    width: 300px;
    display: block;
    margin: 20px auto;
    border-radius: 5px;
      border: none;
      background: #2F80ED;
  }
  .buttonGroup{
    width: 300px;
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    margin-top: 20px;
    .button_edit{
      width: 130px;
      border-radius: 5px;
      border: none;
      background: #2F80ED;
      color: #fff;
      font-weight: bold;
    }
    .button_delete{
      width: 130px;
      border-radius: 5px;
      border: 1px solid #2F80ED;
      color: #2F80ED;
      background: #F2F2F2;
      font-weight: bold;
      }
  }
}
  ::v-deep .van-nav-bar__content {
  background: #ffffff;
  height: 46px;
}
::v-deep .van-cell--clickable{
  width: 95%;
  margin: 0 auto;
  border-radius: 5px;
  margin-top: 10px;
}
::v-deep .van-cell-group{
  background: none;
}
::v-deep .van-nav-bar__text {
  color: #000000 !important;
}

::v-deep .van-icon-arrow-left {
  color: #000000 !important;
}
  </style>
