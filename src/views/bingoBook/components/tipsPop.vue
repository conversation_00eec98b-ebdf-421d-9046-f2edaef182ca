<template>
  <van-dialog
    v-model="dialogShow"
    width="90%"
    :center="true"
    :append-to-body="true"
    :close-on-click-overlay="true"
    :show-confirm-button="false"
  >
    <div v-if="info" class="editor-dig">
      <p>引用：{{ info.keyword }}</p>
      <p>解释：{{ info.content }}</p>
    </div>
  </van-dialog>
</template>
<script>
export default {
  props: {
    info: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogShow: false,
      index: 0
    }
  },
  methods: {
    close() {
      this.dialogShow = false
    },
    open() {
      this.dialogShow = true
    },
    onSwipeChange(index) {
      this.index = index
    }
  }
}
</script>

    <style scoped lang="scss">
    .editor-dig {
      overflow: hidden;
      padding: 10px;
      font-size: 12px;
    }
    </style>
