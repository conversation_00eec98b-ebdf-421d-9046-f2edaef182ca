<template>
  <li>
    <div class="item" :style="{ paddingLeft: (level+1) * 20 + 'px' }" :class="node.id===Number(activeId)?'selected':''" @click="read(node.id)">
      <span>{{ node.title }}</span>
      <van-icon
        v-if="node.childCatalogue.length && (canRead || level !== 0 || index < 2)"
        class="arrow-icon"
        @click.stop="toggle"
      >
        <img class="arrow" :src="node.expanded ? require('../../../assets/bingoBook/down.png') : require('../../../assets/bingoBook/up.png')" alt="" />
      </van-icon>
      <van-icon
        v-else-if="level===0&&index>1&&!canRead"
        name="lock"
        class="lock-icon"
      />
    </div>
    <ul v-show="node.expanded" v-if="node.childCatalogue.length">
      <tree-node
        v-for="(child, childIndex) in node.childCatalogue"
        :key="child.id"
        :node="child"
        :level="level + 1"
        :can-read="canRead"
        :index="childIndex"
        :active-id="activeId"
        @toRead="read"
      />
    </ul>
  </li>
</template>

<script>
export default {
  name: 'TreeNode',
  props: {
    node: Object,
    level: Number,
    canRead: Boolean,
    index: Number,
    activeId: String
  },
  mounted() {
    if (this.canRead || this.level !== 0 || this.index < 2) {
      this.$set(this.node, 'expanded', true)
    }
  },
  activated() {
    if (this.canRead || this.level !== 0 || this.index < 2) {
      this.$set(this.node, 'expanded', true)
    }
  },
  methods: {
    toggle() {
      if (this.node.expanded === undefined) {
        this.$set(this.node, 'expanded', true)
      } else {
        this.node.expanded = !this.node.expanded
      }
    },
    read(id) {
      if (this.canRead || this.level !== 0 || this.index < 2) {
        console.log(id)
        this.$emit('toRead', id)
      } else {
        this.$toast('请先购买')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.item {
  width: 100%;
  height: 51px;
  font-size: 14px;
  line-height: 51px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 25px;
  font-weight: 500;
  border-bottom: 1px solid #F2F2F2;
  border-radius: 5px;
  box-sizing: border-box !important;
  .arrow-icon, .lock-icon {
    position: absolute;
    right: 5px;
    top: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #000;
  }
  .arrow{
    width: 24px;
    position: absolute;
    right: 0px;
    top: -8px;
  }

}
.selected{
    display: block;
    background: #D8E9FF;
    color: #2F80ED;
  }
</style>
