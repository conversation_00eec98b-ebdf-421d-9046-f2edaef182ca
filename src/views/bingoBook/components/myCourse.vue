<template>
  <div class="main_my_course">
    <div v-for="item in bookList" :key="item.id" class="book_item" @click.self="toRead(item)">
      <img v-if="item.digitalBook.cover" class="book_cover" :src="item.digitalBook.cover" />
      <img v-else class="book_cover" src="../../../assets/images/default-cover.jpg" />
      <div class="title">{{ item.digitalBook.title }}</div>
      <div v-if="item.userClass" class="intro">班级：{{ item.userClass.name }}</div>
      <van-button class="read_mode" type="info" @click.self="readModeShow=true;activedNode=item">阅读</van-button>
      <van-button class="progress_mode" type="info" @click.self="toProgress(item)">统计</van-button>
    </div>
    <div v-if="bookList.length===0" class="emty">
      <Empty description="暂无数据" />
      <p class="link" @click="toShop">前往教材商城</p>
    </div>
    <van-popup v-model="readModeShow" closeable round position="bottom" :style="{ height: '50%' }">
      <p class="title">选择模式</p>
      <div class="file">
        <p @click="toRead(activedNode)">阅读模式</p>
        <p @click="toGroup(activedNode)">知识图谱</p>
        <p @click="toTask(activedNode)">任务模式</p>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { userDigitalBooks, getBook } from '@/api/digital-api.js'
import { mapGetters } from 'vuex'
import { Empty } from 'vant'
import { getToken } from '@/utils/auth'
export default {
  components: { Empty },
  data () {
    return {
      bookList: [],
      readModeShow: false,
      activedNode: null
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'id'
    ])
  },
  mounted() {
    if (!getToken()) {
      return
    }
    this._userDigitalBooks()
  },
  activated() {
    if (!getToken()) {
      return
    }
    this.readModeShow = false
    this._userDigitalBooks()
  },
  methods: {
    async toProgress(item) {
      const { data } = await getBook({
        bookId: item.digitalBook.id,
        scene: 'BOOK_CATALOGUE_OWN'
      })
      if (data.studentCourseId === 0) {
        this.$toast('需要个人购买或者兑换后才能查看进度')
        return
      }
      this.$router.push({ path: '/bingoBook/progress', query: { id: this.id, bookId: item.digitalBook.id, studentCourseId: item.id }})
    },
    async toTask(item) {
      this.$router.push({ path: '/bingoBook/task', query: { id: item.digitalBook.id, studentCourseId: item.id }})
    },
    async toGroup(item) {
      this.$router.push({ path: '/bingoBook/group', query: { id: item.digitalBook.id, studentCourseId: item.id }})
    },
    toShop() {
      this.$emit('setType')
    },
    toRead(item) {
      this.$router.push({ path: '/bingoBook/bookInfo', query: { id: item.digitalBook.id, flag: 'my', studentCourseId: item.id }})
    },
    async _userDigitalBooks () {
      const obj = {}
      if (this.currUser && +this.currUser.userId !== +this.id) {
        obj.classUserId = +this.currUser.userId
      }
      const { data } = await userDigitalBooks(obj)
      this.bookList = data
    }
  }
}
</script>
  <style lang="scss" scoped>
      .title{
        font-size: 14px;
        font-weight: bold;
        margin-left: 10px;
        margin-top: 10px;
        margin-bottom: 10px;
      }
      .file{
        width: 100%;
        padding-top: 12px;
        p{
          width: 250px;
          height: 50px;
          text-align: center;
          line-height: 50px;
          border: 1px solid #2F80ED;
          color: #2F80ED;
          margin: 0 auto;
          margin-top: 30px;
        }
      }
  ::v-deep .van-empty__description{
    font-size: 14px;
  }
  .main_my_course{
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    background: #ffffff;
    padding: 10px;
    .emty{
      width: 100%;
      padding-top: 50%;
      .link{
        width: 100%;
        text-align: center;
        color: #007bff;
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 30px;
      }
    }
    .book_item{
        width: 362px;
        height: 148px;
        border: 1px solid #F2F2F2;
        padding: 9px, 0px, 9px, 0px;
        border-radius: 5px;
        margin: 10px auto;
        position: relative;
        .read_mode{
          position: absolute;
          width: 46px;
          height: 25px;
          border-radius: 5px;
          font-size: 10px;
          white-space: nowrap;
          bottom: 10px;
          left: 140px;
        }
        .progress_mode{
          position: absolute;
          width: 46px;
          height: 25px;
          border-radius: 5px;
          font-size: 10px;
          white-space: nowrap;
          bottom: 10px;
          left: 190px;
        }
        .book_cover{
            width: 110px;
            height: 130px;
            object-fit: cover;
            position: absolute;
            left: 10px;
            top:8px
        }
        .title{
            width: 200px;
            font-size: 14px;
            font-weight: 600;
            position: absolute;
            left: 130px;
            top:20px;
        }
        .intro{
            font-size: 12px;
            padding: 5px;
            // border: 1px solid #2F80ED;
            border-radius: 4px;
            position: absolute;
            left: 140px;
            overflow: hidden;
            text-overflow: ellipsis;
            background: #EDF5FF;
            color: #2F80ED;
            -webkit-line-clamp: 3;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            top:70px;
        }
    }
  }
  </style>
