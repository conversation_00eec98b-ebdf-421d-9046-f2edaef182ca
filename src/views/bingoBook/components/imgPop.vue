<template>
  <van-dialog
    v-model="dialogShow"
    width="90%"
    :center="true"
    :append-to-body="true"
    :close-on-click-overlay="true"
    :show-confirm-button="false"
  >
    <div class="editor-dig">
      <van-swipe v-if="info" :initial-swipe="index" @change="onSwipeChange">
        <van-swipe-item v-for="(item, itemIndex) in info.content" :key="itemIndex">
          <div class="img">
            <img :src="item.src" alt="" />
          </div>
          <!-- <p class="page">{{ itemIndex + 1 }}/{{ info.content.length }}</p> -->
          <p class="info">{{ item.info||'' }}</p>
        </van-swipe-item>
      </van-swipe>
    </div>
  </van-dialog>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogShow: false,
      index: 0
    }
  },
  methods: {
    close() {
      this.dialogShow = false
      this.index = 0
    },
    open() {
      this.index = 0
      this.dialogShow = true
    },
    onSwipeChange(index) {
      this.index = index
    },
    go(val) {
      if (val === 'right') {
        this.index = (this.index + 1) % this.info.content.length
      } else {
        this.index = (this.index - 1 + this.info.content.length) % this.info.content.length
      }
    }
  }
}
</script>

  <style scoped lang="scss">
  .editor-dig {
    overflow: hidden;
  }

  .img {
    width: 100%;
    height: 350px;
    padding: 10px;
    background: #E4EEFB;
    display: flex;
    align-items: center;

    img {
      max-width: 100%;
      max-height: 300px;
      display: block;
      margin: 0 auto;
    }
  }

  .page {
    width: 100%;
    text-align: center;
    color: black;
    font-size: 16px;
  }

  .info {
    width: 100%;
    padding: 10px;
    text-align: left;
    font-size: 12px;
    white-space: pre-wrap;
  }
  </style>
