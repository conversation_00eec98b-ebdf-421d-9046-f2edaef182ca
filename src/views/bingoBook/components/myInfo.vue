<template>
  <div class="info_main">
    <div class="top_content">
      <img :src="avatar?avatar:require('../../../assets/publishingReview/default_avator.png')" class="avatar" @click="toMyInFo" />
      <p class="name" @click="toMyInFo"> {{ name|| mobile }} ></p>
    </div>
    <div class="edit">
      <van-cell is-link @click="toClass">我的班级</van-cell>
      <van-cell is-link @click="toHelp">帮助关于</van-cell>
    </div>
    <div class="tocLass" @click="toCLass">
      加入班级
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  data() {
    return {
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'id',
      'avatar',
      'mobile'
    ])
  },
  activated() {
    this.$store.dispatch('user/GetInfo')
  },
  methods: {
    toCLass() {
      this.$router.push('/bingoBook/myClasslist')
    },
    toMyInFo() {
      this.$router.push('/bingoBook/myInfoList')
    },
    toHelp() {
      this.$router.push('/bingoBook/help')
    },
    toClass() {
      this.$router.push('/bingoBook/myClasslist')
    }
  }
}
</script>
  <style scoped lang="scss">
  ::v-deep .van-cell__value{
    font-size: 18px;
    padding: 10px;
  }
  ::v-deep .van-cell__right-icon{
    line-height: 41px !important;
  }
  .info_main{
    width: 100%;
    height: 100%;
    background: #F2F2F2;
    position: relative;
    .tocLass{
      position: absolute;
      width: 40px;
      height: 40px;
      box-shadow: 0px 4px 4px 0px #00000040;
      color: #ffffff;
      background: #2f80ed;
      border-radius: 20px;
      text-align: center;
      line-height: 20px;
      font-size: 14px;
      right: 30px;
      bottom: 80px;
    }
  }
  .top_content{
    width: 100%;
    height: 153px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background: #ffffff;
    overflow: hidden;
    .avatar{
      display: block;
      overflow: hidden;
      width: 60px;
      height: 60px;
      border-radius: 30px;
      margin: 20px auto;
      object-fit: cover;
    }
    .name{
      width: 100%;
      text-align: center;
      font-size: 18px;
    }
}
.edit{
  padding: 10px
}
  </style>
