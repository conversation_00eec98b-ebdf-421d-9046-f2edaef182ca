<template>
  <div class="ptsafe pbsafe">
    <div class="scan pt16 pb28">
      <div class="toolbar flex align-center pl28">
        <div class="arrow-left" @click="$emit('close')">
          <img :src="arrowLeft" alt="" />
        </div>
        <div class="title">扫一扫</div>
      </div>
      <qrcode-stream :camera="camera" style="height: 100vh;" @decode="onDecode" @init="onInit">
        <div class="qr-scanner">
          <div class="box">
            <div class="line"></div>
            <div class="angle"></div>
          </div>
        </div>
      </qrcode-stream>
    </div>
  </div>
</template>
<script>
// 下载插件
// cnpm install --save  vue-qrcode-reader
// 引入
import { QrcodeStream } from 'vue-qrcode-reader'
import arrowLeft from '@/assets/digitalbooks/arrow-left.svg'
export default {
  // 注册
  components: { QrcodeStream },
  data () {
    return {
      arrowLeft,
      camera: 'auto',
      result: '', // 扫码结果信息
      error: '' // 错误信息
    }
  },
  methods: {
    // 回调扫描结果
    onDecode (result) {
      if (result !== '') {
        this.scancode = false
        this.$emit('ok', result)
      }
    },
    // 检查是否调用摄像头
    async onInit (promise) {
      try {
        await promise
      } catch (error) {
        // console.log()
        this.$toast(error.name)
        if (error.name === 'NotAllowedError') {
          this.error = 'ERROR: 您需要授予相机访问权限'
        } else if (error.name === 'NotFoundError') {
          this.error = 'ERROR: 这个设备上没有摄像头'
        } else if (error.name === 'NotSupportedError') {
          this.error = 'ERROR: 所需的安全上下文(HTTPS、本地主机)'
        } else if (error.name === 'NotReadableError') {
          this.error = 'ERROR: 相机被占用'
        } else if (error.name === 'OverconstrainedError') {
          this.error = 'ERROR: 安装摄像头不合适'
        } else if (error.name === 'StreamApiNotSupportedError') {
          this.error = 'ERROR: 此浏览器不支持流API'
        } else if (error.name === 'InsecureContextError') {
          this.error = 'ERROR: 仅允许在安全上下文中访问摄像机。使用HTTPS或本地主机，而不是HTTP。'
        } else {
          this.error = 'ERROR:摄像机错误'
        }

        this.$emit('err', this.error)
      }
    }
  }
}
</script>
  <style lang="scss" scoped>
  .scan{
      width: 100vw;
      height: 100vh;
      border-color: #585858;
      position: fixed;
      top: 0;
      left: 0;
      background: black;
      z-index: 9999;
  }

  .toolbar {
      width: 100%;
      height: 48px;
      position: relative;
      margin-bottom: 8px;

      .arrow-left {
          height: 40px;
          width: 40px;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
              height: 25px;
              object-fit: contain;
          }
      }

      .title {
          font-size: 16px;
          line-height: 22px;
          text-align: center;
          color: #FFFFFF;
          position: absolute;
          transform: translate(-50%, 0);
          left: 50%;
      }
  }
  .qrcode-stream-camera{
      width: 100%;
      height: 100%;
      display: block;
      -o-object-fit: cover;
      object-fit: cover;
      position: absolute;
      top: 0%;
      left: 0%;
  }

  .qr-scanner {
      background-image:
      linear-gradient(0deg,
          transparent 24%,
          rgba(32, 255, 77, 0.1) 25%,
          rgba(32, 255, 77, 0.1) 26%,
          transparent 27%,
          transparent 74%,
          rgba(32, 255, 77, 0.1) 75%,
          rgba(32, 255, 77, 0.1) 76%,
          transparent 77%,
          transparent),
      linear-gradient(90deg,
          transparent 24%,
          rgba(32, 255, 77, 0.1) 25%,
          rgba(32, 255, 77, 0.1) 26%,
          transparent 27%,
          transparent 74%,
          rgba(32, 255, 77, 0.1) 75%,
          rgba(32, 255, 77, 0.1) 76%,
          transparent 77%,
          transparent);
      background-size: 3rem 3rem;
      background-position: -1rem -1rem;
      width: 100%;
      /* height: 100%; */
      height: 100vh;
      position: relative;
      /* background-color: #1110;*/

      /* background-color: #111; */
  }

  .qr-scanner .box {
      width: 213px;
      height: 213px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      overflow: hidden;
      border: 0.1rem solid rgba(0, 255, 51, 0.2);
      /* background: url('http://resource.beige.world/imgs/gongconghao.png') no-repeat center center; */
  }

  .qr-scanner .line {
      height: calc(100% - 2px);
      width: 100%;
      background: linear-gradient(180deg, rgba(0, 255, 51, 0) 43%, #00ff33 211%);
      border-bottom: 3px solid #00ff33;
      transform: translateY(-100%);
      animation: radar-beam 2s infinite alternate;
      animation-timing-function: cubic-bezier(0.53, 0, 0.43, 0.99);
      animation-delay: 1.4s;
  }

  .qr-scanner .box:after,
  .qr-scanner .box:before,
  .qr-scanner .angle:after,
  .qr-scanner .angle:before {
      content: '';
      display: block;
      position: absolute;
      width: 3vw;
      height: 3vw;

      border: 0.2rem solid transparent;
  }

  .qr-scanner .box:after,
  .qr-scanner .box:before {
      top: 0;
      border-top-color: #00ff33;
  }

  .qr-scanner .angle:after,
  .qr-scanner .angle:before {
      bottom: 0;
      border-bottom-color: #00ff33;
  }

  .qr-scanner .box:before,
  .qr-scanner .angle:before {
      left: 0;
      border-left-color: #00ff33;
  }

  .qr-scanner .box:after,
  .qr-scanner .angle:after {
      right: 0;
      border-right-color: #00ff33;
  }

  @keyframes radar-beam {
      0% {
      transform: translateY(-100%);
      }

      100% {
      transform: translateY(0);
      }
  }
  </style>
