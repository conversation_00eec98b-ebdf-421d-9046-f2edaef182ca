<template>
  <div>
    <Search v-model="value" placeholder="请输入搜索关键词" show-action @input="filterData">
      <template v-slot:action>
        <div class="share">
          <img src="../../../assets/bingoBook/share.png" alt="" @click="openShare" />
        </div>
      </template>
    </Search>
    <!-- <van-tabs v-model="active">
      <van-tab title="全部" />
      <van-tab title="本科" />
      <van-tab title="高职高专" />
      <van-tab title="其他" />
    </van-tabs> -->
    <div class="content">
      <div v-for="item in bookList" :key="item.id" class="book_item" @click="toBookInfo(item)">
        <img class="cover" :src="item.cover" alt="" />
        <p class="title">{{ item.title }}</p>
        <p class="author">{{ item.author }}</p>
      </div>
    </div>
    <div v-if="bookList.length === 0" class="emty">
      <Empty description="暂无数据" />
    </div>
    <share-guide ref="shareGuide" />
  </div>
</template>
<script>
import { Search } from 'vant'
import { bookList } from '@/api/digital-api.js'
import { Empty } from 'vant'
import { getWxGzhShareSignature } from '@/api/activity-api.js'
import { initWechatShare } from '@/utils/index.js'
import ShareGuide from '@/components/H5/share-guide.vue'
export default {
  components: { Search, Empty, ShareGuide },
  data() {
    return {
      active: 0,
      value: '',
      bookList: [
      ],
      bookListAll: []
    }
  },
  mounted() {
    this.getBookList()
    this.share()
  },
  methods: {
    openShare() {
      this.$refs.shareGuide.open()
    },
    async getBookList() {
      const { data } = await bookList()
      this.bookList = data
      this.bookListAll = data
    },
    filterData() {
      const query = this.value.toLowerCase()
      this.bookList = this.bookListAll.filter(item => {
        return item.title.toLowerCase().includes(query) || item.author.toLowerCase().includes(query)
      })
    },
    toBookInfo(item) {
      this.$router.push({ path: '/bingoBook/bookInfo', query: { id: item.id, flag: 'store' }})
    },
    async share() {
      const url = location.href.split('#')[0]
      const params = {
        'url': url
      }
      const res = await getWxGzhShareSignature(params)
      this.appId = res.data.appId
      const _this = this
      // 配置分享的内容
      const shareOptions = {
        title: '缤果数字教材 BingoBook',
        desc: '新形态AI互动式教材，支持多种阅读模式。',
        link: location.href,
        imgUrl: 'https://static.bingotalk.cn/bingodev/image/2024093019305988.png',
        success: () => {
        // 分享成功回调
          _this.$refs.shareGuide.close()
        },
        cancel: () => {
        // 分享取消回调
          console.log('分享取消')
        }
      }
      initWechatShare(res, shareOptions)
    }
  }

}
</script>
<style scoped lang="scss">
.share {
  padding-top: 10px;
  img {
    width: 53px;
    height: 27px;
  }
}

::v-deep .van-empty__description {
  font-size: 14px;
}

.content {
  width: 100%;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .book_item {
    width: 171px;
    height: 288px;
    padding-top: 10px;
    border-radius: 10px;
    background: #F7F7F7;
    margin-top: 10px;

    .cover {
      width: 158px;
      height: 193px;
      object-fit: cover;
      display: block;
      margin: 0 auto;
    }

    .title {
      font-weight: 700;
      font-size: 14px;
      height: 30px;
      padding-left: 7px;
    }

    .author {
      font-size: 12px;
      color: #828282;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-left: 7px;
    }
  }
}

.emty {
  width: 100%;
  padding-top: 50%;
}

::v-deep .van-tabs__line {
  background: #E0EDFF;
}
</style>
