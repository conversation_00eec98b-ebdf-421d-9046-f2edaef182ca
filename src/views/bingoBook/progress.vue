<template>
  <div class="class_content">
    <van-nav-bar
      left-text="返回"
      title="学习统计"
      left-arrow
      @click-left="back"
    />
    <div class="content">
      <img class="cover" :src="bookInfo && bookInfo.cover" alt="" />
      <p class="title">{{ bookInfo && bookInfo.title }}</p>
      <div class="info">
        <!-- <div class="info_item">
          <p>{{ progress }}%</p>
          <p style="color: #828282">学习进度</p>
        </div> -->
        <div class="info_item">
          <p>{{ progressStatus !== 'GOING' ? '已完成' : '未完成' }}</p>
          <p style="color: #828282">状态</p>
        </div>
      </div>
    </div>
    <div class="content1">
      <div class="progress">
        <p>{{ progress }}%</p>
        <p>{{ testPaperProgress }}</p>
        <p v-if="trainingProgress">{{ trainingProgress }}</p>
      </div>
      <van-tabs v-model="active">
        <van-tab
          title="学习进度"
        ><div class="tree">
          <div v-for="(item, index) in classList" :key="index">
            <p class="title">{{ item.title }}</p>
            <p
              :class="
                item.userDigitalCatalogue.progress === 100 ? 'active' : ''
              "
            >
              <span>{{
                item.userDigitalCatalogue.progress === 100
                  ? '已完成'
                  : '未完成'
              }}</span>
              {{ item.userDigitalCatalogue.progress }}%
            </p>
          </div>
        </div></van-tab>
        <van-tab title="互动答题">
          <div v-if="testData && testData.length !== 0" class="tree">
            <div
              v-for="(item, index) in testData"
              :key="index"
              class="step_item"
            >
              <p class="title">
                <span>{{ index + 1 }}.</span>
                {{ item.title }}
              </p>
              <div class="content_progress">
                <p>
                  答题总数：<span>{{
                    item.userTestpaper
                      ? item.userTestpaper.totalQuantity + '道'
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  答对：<span>{{
                    item.userTestpaper
                      ? item.userTestpaper.rightQuantity + '道'
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  答错：<span>{{
                    item.userTestpaper
                      ? item.userTestpaper.wrongQuantity + '道'
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  总分：<span>{{
                    item.userTestpaper
                      ? item.userTestpaper.totalScore
                        ? item.userTestpaper.totalScore + '分'
                        : '未配置'
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  得分：<span>{{
                    item.userTestpaper
                      ? item.userTestpaper.totalScore
                        ? item.userTestpaper.score + '分'
                        : '-'
                      : '未配置'
                  }}</span>
                </p>
              </div>
              <div
                class="tag"
                :class="
                  item.userTestpaper &&
                    item.userTestpaper.progressStatus !== 'COMING'
                    ? 'done'
                    : 'undone'
                "
              >
                {{
                  item.userTestpaper &&
                    item.userTestpaper.progressStatus !== 'COMING'
                    ? '已完成'
                    : '未完成'
                }}
              </div>
            </div>
          </div>
          <div v-else class="emty">
            <Empty description="暂无数据" />
          </div>
        </van-tab>
        <van-tab v-if="trainingProgress" title="实训">
          <div
            v-if="traningData && traningData.length !== 0"
            class="tree"
          >
            <div
              v-for="(item, index) in traningData"
              :key="index"
              class="step_item"
            >
              <p class="title">
                <span>{{ index + 1 }}.</span>
                {{ item.trainingName }}
              </p>
              <div class="content_progress">
                <p>
                  验证总数：<span>{{
                    item.userTrainingData
                      ? item.userTrainingData.total || '未配置'
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  正确：<span>{{
                    item.userTrainingData
                      ? item.userTrainingData.right === null
                        ? '-'
                        : item.userTrainingData.right
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  错误：<span>{{
                    item.userTrainingData
                      ? item.userTrainingData.wrong === null
                        ? '-'
                        : item.userTrainingData.wrong
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  未完成：<span>{{
                    item.userTrainingData
                      ? item.userTrainingData.unCompleteLabel === null
                        ? '-'
                        : item.userTrainingData.unCompleteLabel
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  实验步骤：<span>{{
                    item.userTrainingData
                      ? item.userTrainingData.userCompleteStep || '0'
                      : '未配置'
                  }}{{ item.userTrainingData ? '/' : ''
                  }}<span>{{
                    item.userTrainingData
                      ? item.userTrainingData.totalStep || '0'
                      : ''
                  }}</span></span>
                </p>
                <p>
                  总分：<span>{{
                    item.userTrainingData
                      ? item.userTrainingData.scoreTotal || '未配置'
                      : '未配置'
                  }}</span>
                </p>
                <p>
                  得分：<span>{{
                    item.userTrainingData
                      ? item.userTrainingData.userScore === null
                        ? '-'
                        : item.userTrainingData.userScore
                      : '未配置'
                  }}</span>
                </p>
              </div>
              <div
                class="tag"
                :class="
                  item.userTrainingData &&
                    item.userTrainingData.progressStatus !== 'GOING'
                    ? 'done'
                    : 'undone'
                "
              >
                {{
                  item.userTrainingData &&
                    item.userTrainingData.progressStatus !== 'GOING'
                    ? '已完成'
                    : '未完成'
                }}
              </div>
            </div>
          </div>
          <div v-else class="emty">
            <Empty description="暂无数据" />
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script>
import { getDigitalBookProgress, getUserDigitalBookProgress } from '@/api/digital-api.js'
import { getBook } from '@/api/digital-api.js'
import { getUserBookTrainingData } from '@/api/training-api.js'
import { getUserBookTestpaperData } from '@/api/test-api.js'
import { mapGetters } from 'vuex'
import { Empty } from 'vant'
export default {
  components: { Empty },
  data() {
    return {
      classList: [],
      bookInfo: null,
      progress: '',
      testData: [],
      traningData: [],
      studentCourseId: this.$route.query.studentCourseId,
      active: 0,
      trainingProgress: '',
      testPaperProgress: ''
    }
  },
  computed: {
    ...mapGetters(['id'])
  },
  watch: {
    async $route(to, from) {
      if (this.$route.query && this.$route.query.token) {
        await this.$store.dispatch(
          'user/AppLogin',
          'Bearer ' + to.query.token
        )
        await this.$store.dispatch('user/GetInfo')
      }
      this.studentCourseId = to.query.studentCourseId
      this.active = 0
      this.innitData()
    }
  },
  async mounted() {
    if (this.$route.query && this.$route.query.token) {
      await this.$store.dispatch(
        'user/AppLogin',
        'Bearer ' + this.$route.query.token
      )
      await this.$store.dispatch('user/GetInfo')
    }
    this.innitData()
  },
  methods: {
    async _getDigitalBookProgress () {
      const { data } = await getDigitalBookProgress({
        studentCourseId: this.studentCourseId
      })
      this.progress = data && data[0].digitalBookProgress
      this.progressStatus = data && data[0].progressStatus
      this.trainingProgress = data && data[0].trainingProgress
      this.testPaperProgress = data && data[0].testPaperProgress
    },
    async _getUserBookTrainingData() {
      const { data } = await getUserBookTrainingData({
        studentCourseId: this.studentCourseId,
        userId: this.id
      })
      this.traningData = data || []
    },
    async _getUserBookTestpaperData() {
      const { data } = await getUserBookTestpaperData({
        bookId: this.bookInfo.id,
        userId: this.id
      })
      this.testData = data || []
    },
    back() {
      if (
        window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.bingo_download
      ) {
        window.webkit.messageHandlers.bingo_action.postMessage('event_back')
      } else if (window.bingo_action) {
        window.bingo_action.postMessage('event_back')
      } else {
        this.$router.push('/bingoBook/home')
      }
    },
    async innitData() {
      // if (window.viewerShow) {
      //   window.viewerShow.destroy()
      //   window.viewerShow = null
      // }
      const { data } = await getUserDigitalBookProgress({
        userId: this.$route.query.id,
        studentCourseId: this.$route.query.studentCourseId
      })
      this.classList = data
      await getBook({ bookId: this.$route.query.bookId }).then((res) => {
        this.bookInfo = res.data
      })
      this.progress = this.classList[0].userDigitalCatalogue.progressTotal
      this._getUserBookTestpaperData()
      this._getUserBookTrainingData()
      this._getDigitalBookProgress()
      // this.progress = parseInt(this.classList.reduce((pre, cur) => {
      //   return pre + cur.userDigitalCatalogue.progress
      // }, 0) / this.classList.length)
    }
  }
}
</script>
<style scoped lang="scss">
.step_item {
  width: 100%;
  border-bottom: 1px solid #e9e9e9;
  position: relative;
  padding-bottom: 30px;
  .title {
    font-size: 12px;
    font-weight: 500;
    width: 85%;
    span{
      margin:0 !important;
    }
  }
  .content_progress {
    width: 100%;
    height: 100%;
    font-size: 10px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    p {
      font-size: 10px;
      white-space: nowrap;
      span {
        color: #2f80ed;
      }
    }
  }
  .tag {
    position: absolute;
    right: 5px;
    top:5px;
    font-size: 12px;
  }
  .done {
    color: #27ae60;
  }
  .undone {
    color: #eb5757;
  }
}
::v-deep .van-tabs__line {
  background: #2f80ed;
}
.tree {
  padding: 10px;
  margin-top: 10px;
  overflow: auto;
}
::v-deep van-nav-bar__left {
  font-size: 14px;
}
.class_content {
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  overflow: scroll;
  .content {
    width: 356px;
    height: 263px;
    background: #ffffff;
    border-radius: 10px;
    margin: 20px auto;
    overflow: hidden;
    margin-top: 60px;
    .cover {
      width: 110px;
      height: 130px;
      object-fit: cover;
      display: block;
      margin: 0 auto;
      margin-top: 20px;
    }
    .title {
      font-size: 16px;
      font-weight: bold;
      width: 100%;
      text-align: center;
    }
    .info {
      width: 100%;
      display: flex;
      justify-content: center;
      font-size: 12px;
      .info_item {
        width: 50%;
        text-align: center;
      }
    }
  }
  .content1 {
    width: 356px;
    background: #ffffff;
    border-radius: 10px;
    margin: 0 auto;
    padding: 10px;
    margin-bottom: 20px;
    .progress{
      display: flex;
      justify-content: space-between;
      p{
        width: 333%;
        text-align: center;
        margin: 0;
        padding: 0;
      }
    }
    p {
      font-size: 12px;
    }
    .title {
      font-weight: bold;
      font-size: 16px;
    }
    .active {
      color: #27ae60;
    }
    span {
      margin-right: 20px;
    }
  }
}
::v-deep .van-nav-bar {
  background: #ffffff;
  border: none;
  height: 46px;
  width: 100%;
  position: fixed;
  top: 0;
}
::v-deep .van-nav-bar__text {
  color: #000000 !important;
}
::v-deep .van-icon-arrow-left {
  color: #000000 !important;
}
::v-deep .van-tabs__wrap{
  position: sticky;
  top: 50px;
  background: #ffffff;
  z-index: 10;
}
</style>
