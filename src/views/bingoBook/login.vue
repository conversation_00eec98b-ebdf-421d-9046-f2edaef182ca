<template>
  <div class="login-page">
    <div class="top">
      <div class="header">
        <img class="logo" src="../../assets/bingoBook/logo.png" alt="" />
        <img class="close" src="../../assets/bingoBook/close.png" alt="" @click="$router.go(-1)" />
        <div class="top-bar">
          <span style="color: #000;">登录/注册</span>
          <span @click="togglePasswordLogin">{{ loginType==='PASSWORD' ? '验证码登录' : '密码登录' }}</span>
        </div>
      </div>
      <van-field v-model="loginForm.mobileOrEmail" placeholder="输入手机号" :error-message="phoneError" />
      <van-field v-if="loginType==='SMS_CODE'" v-model="loginForm.code" placeholder="输入验证码" :error-message="codeError">
        <template #button>
          <van-button size="small" type="primary" class="button" @click="getCode()">{{
            smsDisabled
              ? countdown > 0
                ? countdown + 's后重新获取'
                : '获取验证码'
              : '获取验证码'
          }}</van-button>
        </template>
      </van-field>
      <van-field v-if="loginType==='PASSWORD'" v-model="loginForm.password" type="password" placeholder="输入密码" :error-message="passwordError" />
      <van-button type="primary" block class="login-button" @click="handleSubmit">登录</van-button>
    </div>
    <!-- <img src="../../assets/bingoBook/login_bg.png" alt="Illustration" class="illustration" /> -->
  </div>
</template>
<script>
import { verifyCodeForWeb } from '@/api/user-api'
import { validMobile } from '@/utils/validate'
export default {
  data() {
    return {
      phoneError: '',
      codeError: '',
      passwordError: '',
      smsDisabled: false,
      countdown: 0,
      loginType: 'SMS_CODE',
      loginForm: {
        mobileOrEmail: '',
        password: '',
        loginType: '',
        code: ''
      }
    }
  },
  methods: {
    getCode () {
      if (this.smsDisabled) return
      const mobile = this.loginForm.mobileOrEmail
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this._verifyCodeForWeb()
    },
    _verifyCodeForWeb () {
      verifyCodeForWeb({ mobile: this.loginForm.mobileOrEmail }).then(res => {
        this.countdown = 60
        this.smsDisabled = true
        setTimeout(this.tick, 1000)
      })
    },
    tick () {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    },
    togglePasswordLogin() {
      this.loginType = this.loginType === 'SMS_CODE' ? 'PASSWORD' : 'SMS_CODE'
    },
    validatePhone() {
      const phoneRegex = /^[1]([3-9])[0-9]{9}$/
      if (!this.loginForm.mobileOrEmail) {
        this.phoneError = '手机号不能为空'
        return false
      } else if (!phoneRegex.test(this.loginForm.mobileOrEmail)) {
        this.phoneError = '请输入有效的手机号'
        return false
      }
      this.phoneError = ''
      return true
    },
    validateCode() {
      if (!this.loginForm.code) {
        this.codeError = '验证码不能为空'
        return false
      }
      this.codeError = ''
      return true
    },
    validatePassword() {
      if (!this.loginForm.password) {
        this.passwordError = '密码不能为空'
        return false
      }
      this.passwordError = ''
      return true
    },
    handleSubmit() {
      const isPhoneValid = this.validatePhone()
      const isCodeValid = this.loginType === 'PASSWORD' ? true : this.validateCode()
      const isPasswordValid = this.loginType === 'PASSWORD' ? this.validatePassword() : true
      if (isPhoneValid && isCodeValid && isPasswordValid) {
        this.loginForm.loginType = this.loginType
        this.$store.dispatch('user/Login', this.loginForm).then(() => {
          this.$router.push({ path: '/bingoBook' }).catch(() => { })
        })
      }
    }
  }
}
</script>
  <style scoped lang="scss">
  ::v-deep .van-field{
    border: 1px solid #f1eded;
    font-size: 14px;
    margin-top: 10px;
  }
  ::v-deep .van-cell::after{
    display: none;
  }
  .logo {
    width: 174px;
    height: 68px;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .button {
    background: #2F80ED;
    border-color: #2F80ED;
  }
  .login-page {
    width: 100%;
    height: 100%;
    background: #ffffff;
    text-align: center;
    position: relative;
    .top{
      padding: 16px;

    }
  }
  .header {
    margin-bottom: 20px;
  }
  .close{
    width: 20px;
    position: absolute;
    right: 20px;
    top:20px
  }
  .top-bar {
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    font-size: 14px;
    color: #2F80ED;
    margin-bottom: 10px;
  }
  .login-button {
    margin-top: 20px;
    background: #2F80ED;
    font-size: 16px;
    font-weight: 600;
    border-color: #2F80ED;
  }
  .illustration {
    width: 100%;
    margin-top: 110px;
  }
  </style>
