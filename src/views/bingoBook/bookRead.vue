<template>
  <div v-loading="loading" class="read_content" ref="parent">
    <van-nav-bar
      left-text="返回"
      left-arrow
      :title-class="'nav-title'"
      @click-left="saveProgress"
    >
      <template #title>
        <div class="nav-title" @click="handleTitleClick">{{ title }}</div>
      </template>
      <template #right>
        <img
          class="share"
          src="../../assets/bingoBook/share.png"
          alt=""
          @click="openShare"
        />
      </template>
    </van-nav-bar>
    <div
      v-if="content === ''"
      class="emty"
      @scroll="handleScroll"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <Empty description="暂无数据" />
    </div>
    <div
      id="serchContent"
      ref="readContent"
      class="readContent"
      :style="{
        backgroundColor: backgroundColor.color,
        fontFamily: checkFont(font.defaultFont)
          ? font.defaultFont
          : font.font + '!important',
        lineHeight: size + 5 + 'px' + '!important'
      }"
      @click.capture="readEvent"
      @scroll="handleScroll"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      v-html="content"
    ></div>
    <div class="buttom_bar">
      <div class="mulu" @click="show = true">
        <img src="../../assets/bingoBook/mulu.png" alt="" />
      </div>
      <div class="button_group">
        <img
          src="../../assets/bingoBook/read1.png"
          alt=""
          @click="readModeShow = true"
        />
        <img src="../../assets/bingoBook/read3.png" alt="" @click="toNote" />
        <img
          src="../../assets/bingoBook/read4.png"
          alt=""
          @click="showFont = true"
        />
        <el-popover
          popper-class="pop_search"
          placement="right-end"
          width="380"
          trigger="click"
        >
          <div id="findwindow"></div>

          <div slot="reference" class="trigger_content">
            <img src="../../assets/bingoBook/read2.png" alt="" />
          </div>
        </el-popover>
      </div>
    </div>
    <div
      v-if="showAiBtn"
      ref="child"
      class="ai_btn"
      @click="showAiChat = true"
      :style="{
        left: `${aiBtnPosition.x}px`,
        top: `${aiBtnPosition.y}px`,
       }"
    >
      <img src="../../assets/bingoBook/ai-chat.png" fit="cover" />
    </div>
    <transition name='mobile-fade'>
      <div v-show="showAiChat" class="ai-overlay" @click="showAiChat = false">
      </div>
    </transition>
    <transition name="popup-up">
      <div v-show="showAiChat" class="ai-popup">
        <i class="el-icon-close ai-popup-close" @click.stop="showAiChat = false"></i>
        <div v-if="$route.query.bookId === '131'" class="ai-main">
          <div class="ai-header">
            <img style="height:30px" src="@/assets/digitalbooks/read/aimate.png" fit='cover' />
          </div>
          <div class="ai-content">
            <iframe
              v-if="showAiChat"
              id="scratch-iframe"
              ref="scratchFrame"
              :src="'https://chatpub.com.cn/chat/share?shareId=52c0mrcsmrrrpyl6jx7yymxs'"
              style="border: none"
              width="100%"
              height="100%"
              allowfullscreen
              allow="microphone *; camera *"
              sandbox="allow-same-origin allow-scripts allow-popups allow-modals"
            ></iframe>
          </div>
        </div>
        <AIChat v-else :source-str="'h5'"/>
      </div>
    </transition>
    <van-popup
      v-model="show"
      closeable
      round
      close-icon-position="top-right"
      position="bottom"
      :style="{ height: '70%' }"
    >
      <p class="title">目录</p>
      <div class="mulu_content">
        <ul>
          <tree-node
            v-for="(item, index) in bookTree"
            :key="item.id"
            :index="index"
            :node="item"
            :level="0"
            :can-read="bookInfo && bookInfo.studentCourseId !== 0"
            :active-id="String(catalogueId)"
            @toRead="toRead"
          />
        </ul>
      </div>
    </van-popup>
    <van-popup
      v-model="showPreview"
      closeable
      round
      close-icon-position="top-right"
      position="bottom"
      :style="{ height: '50%' }"
    >
      <div ref='scrollContainer' class="preview-content" v-html="previewHtml">

      </div>
    </van-popup>
    <imgPop ref="images" :info="imgListInfo" />
    <tipsPop ref="tips" :info="tipsInfo" />
    <share-guide ref="shareGuide" />
    <div
      v-if="showToolbar"
      :style="{
        top: `${toolbarPosition.top}px`,
        left: `${toolbarPosition.left}px`
      }"
      style="
        position: absolute;
        display: flex;
        z-index: 9999;
        box-shadow: 0px 4px 4px 0px #00000040;
      "
      class="toolbar"
    >
      <button class="item" @click="interpretation">解读</button>
      <button class="item" @click="highlightText">高亮</button>
      <button class="item" @click="addNote">笔记</button>
      <button class="item" @click="copyText">复制</button>
    </div>
    <van-popup
      v-model="showNotePopup"
      closeable
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="note_main">
        <p class="soyin">引用：{{ selectionText }}</p>
        <el-input
          v-model="noteValue"
          placeholder="请输入笔记"
          label="笔记内容"
          rows="11"
          type="textarea"
        />
        <!-- 保存按钮 -->
        <van-button
          class="button"
          type="info"
          @click="addNoteDone"
        >保存笔记</van-button>
      </div>
    </van-popup>
    <van-popup
      v-model="readModeShow"
      closeable
      round
      position="bottom"
      :style="{ height: '44.5%' }"
    >
      <div class="file">
        <div class="mode-option" @click="readModeShow = false">
          <img
            src="../../assets/bingoBook/mode1.png"
            alt="阅读模式图标"
            class="mode-icon"
          />
          <p>阅读模式</p>
        </div>
        <div class="mode-option" @click="toTask(bookInfo)">
          <img
            src="../../assets/bingoBook/mode2.png"
            alt="任务模式图标"
            class="mode-icon"
          />
          <p>任务模式</p>
        </div>
        <div class="mode-option" @click="toGroup(bookInfo)">
          <img
            src="../../assets/bingoBook/mode3.png"
            alt="知识图谱图标"
            class="mode-icon"
          />
          <p>知识图谱</p>
        </div>
      </div>
    </van-popup>
    <van-popup
      v-model="showFont"
      closeable
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="title">字体</div>
      <div class="pop_card">
        <p>正文字号</p>
        <p class="size">
          {{
            fontTitleList.filter((item) => {
              return item.value === size
            })[0].title
          }}
        </p>
        <div class="block">
          <span>小</span>
          <van-slider
            v-model="size"
            :min="12"
            :max="24"
            :step="3"
            bar-height="6px"
          />
          <span>大</span>
        </div>

        <!-- 正文字体 -->
        <div class="block">
          <div>正文字体</div>
          <div class="value" @click="showFontPicker = true">
            {{ font.title }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </div>
          <van-popup
            v-model="showFontPicker"
            position="bottom"
            :style="{ zIndex: 3000 }"
          >
            <van-picker
              show-toolbar
              :columns="fontFamily.map((item) => item.title)"
              @confirm="onFontConfirm"
              @cancel="showFontPicker = false"
            />
          </van-popup>
        </div>

        <!-- 阅读背景 -->
        <div class="block">
          <div>背景</div>
        </div>
        <div class="color-options">
          <div
            v-for="color in colorList"
            :key="color.title"
            class="color-option"
            @click="onBgConfirm(color.title)"
          >
            <div
              :class="color.title === backgroundColor.title ? 'active' : ''"
              :style="{ backgroundColor: color.color }"
            ></div>
            <p>{{ color.title }}</p>
          </div>
        </div>
      </div>
    </van-popup>
    <note ref="note" :catalogue-id="catalogueId" :book-id="bookInfo&&bookInfo.id" @backToView="backToView" @deleteNote="deleteNote" />
    <van-dialog
      v-model="showImg"
      :center="true"
      :append-to-body="true"
      :close-on-click-overlay="true"
      :show-confirm-button="false"
      class="show_img_dialog"
    >
      <img class="show_img" :src="imgSrc" />
    </van-dialog>
  </div>
</template>

<script>
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import mk from 'markdown-it-katex'
import {
  getContent,
  getBookCatalogue,
  getBook,
  updateReadProgress,
  digitalNotes,
  getDigitalNotesList,
  getDigitalBookConfig
} from '@/api/digital-api.js'
import { Empty, Dialog } from 'vant'
import AIChat from '@/components/classPro/AIChat'
import TreeNode from './components/TreeNode.vue'
import imgPop from './components/imgPop.vue'
import { mapGetters } from 'vuex'
import tipsPop from './components/tipsPop.vue'
// import { getWXConfig } from '@/api/community-api.js'
import { getWxGzhShareSignature } from '@/api/activity-api.js'
import { initWechatShare } from '@/utils/index.js'
import ShareGuide from '@/components/H5/share-guide.vue'
import { saveAs } from 'file-saver'
import { throttle } from '@/utils/index'
// import { getSqlPlatformToken } from '@/api/training-api'
import Prism from 'prismjs'
import 'prismjs/themes/prism-tomorrow.min.css'
import { debounce } from '@/utils/index'
import Highlighter from 'web-highlighter'
import initFindWin from 'find5'
import note from './note.vue'
import { getConfigObj } from '@/api/config'
export default {
  components: { Empty, TreeNode, imgPop, tipsPop, ShareGuide, note, AIChat },
  data() {
    return {
      md: new MarkdownIt({
        html: true,
        breaks: false,
        linkify: true,
        typographer: true,
        tables: true,
        highlight: function (str, lang) {
          if (!lang) lang = 'plaintext'
          if (hljs.getLanguage(lang)) {
            try {
              return '<pre class="hljs"><div class="code-header">' +
                '<span class="code-lang">' + lang + '</span>' +
                '<button class="copy-btn" onclick="copyCode(this)">复制</button>' +
                '</div><code class="' + lang + '">' +
                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                '</code></pre>'
            } catch (__) {
              return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
            }
          }
          return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
        }
      }).use(mk, {
        throwOnError: false,
        trust: true,
        macros: {
          '\\sum': '\\sum\\limits',
          '\\f': '\\frac'
        },
        delimiters: [
          { left: '$$', right: '$$', display: true },
          { left: '\\[', right: '\\]', display: true },
          { left: '$', right: '$', display: false },
          { left: '\\(', right: '\\)', display: false }
        ]
      }),
      showAiBtn: false,
      isDragging: false,
      aiBtnPosition: {
        x: 0,
        y: 0
      },
      startPos: { x: 0, y: 0 },
      parentRect: null,
      childRect: null,
      title: '',
      content: '',
      contentCopy: '',
      showFontPicker: false,
      showBgPicker: false,
      showAiChat: false,
      show: false,
      bookTree: null,
      bookInfo: null,
      catalogueId: 0,
      imgListInfo: null,
      tipsInfo: null,
      isAtBottom: false,
      isAtTop: false,
      startY: 0,
      hasExecuted: false,
      loading: true,
      openFlag: false,
      highlighter: null,
      showToolbar: false,
      showPreview: false,
      previewHtml: '',
      toolbarPosition: { top: 0, left: 0 },
      selection: null,
      highLineRenderList: [],
      highLineData: [],
      highLineList: [],
      noteList: [],
      selectionText: '',
      showNotePopup: false,
      readModeShow: false,
      showImg: false,
      noteValue: '',
      noteId: 0,
      noteType: 'create',
      showFont: false,
      flag: false,
      studentCourseId: this.$route.query.studentCourseId,
      bookConfig: null,
      viewer: null,
      imgSrc: '',
      backgroundColor: {
        title: '默认',
        color: ''
      },
      marks: {
        12: '超小',
        15: '小',
        18: '正常',
        21: '大',
        24: '超大'
      },
      font: {
        title: '默认',
        font: '',
        defaultFont: ''
      },
      fontTitleList: [
        {
          title: '超小',
          value: 12
        },
        {
          title: '小',
          value: 15
        },
        {
          title: '正常',
          value: 18
        },
        {
          title: '大',
          value: 21
        },
        {
          title: '超大',
          value: 24
        }
      ],
      size: 18,
      colorList: [
        {
          title: '默认',
          color: ''
        },
        {
          title: '浅灰色',
          color: '#F5F5F5'
        },
        {
          title: '银白色',
          color: '#E8E8E8'
        },
        {
          title: '象牙白',
          color: '#F2F1F1'
        },
        {
          title: '浅米色',
          color: '#FBF8F1'
        },
        {
          title: '乳白色',
          color: '#FEFDF9'
        },
        {
          title: '浅绿色',
          color: '#F0FFF0'
        },
        {
          title: '牛奶白',
          color: '#FCFBF9 '
        },
        {
          title: '玉米色',
          color: '#FFF8DC'
        },
        {
          title: '浅蓝灰',
          color: '#F1F8F9'
        },
        {
          title: '天蓝色',
          color: '#F2F8FC '
        },
        {
          title: '粉蓝色',
          color: '#E0EAF4'
        }
      ],
      fontFamily: [
        {
          title: '默认',
          font: ''
        },
        {
          title: '宋体',
          font: 'songti',
          defaultFont: 'SimSun'
        },
        {
          title: '黑体',
          font: 'heiti',
          defaultFont: 'SimHei'
        },
        {
          title: '仿宋',
          font: 'fangsong',
          defaultFont: 'FangSong'
        },
        {
          title: '楷体',
          font: 'kaiti',
          defaultFont: 'KaiTi'
        },
        {
          title: '微软雅黑',
          font: 'yahei',
          defaultFont: 'Microsoft Yahei'
        },
        {
          title: '思源宋体',
          font: 'siyuansongti',
          defaultFont: 'Source Han Serif SC'
        },
        {
          title: '思源黑体',
          font: 'siyuanheiti',
          defaultFont: 'Source Han Sans CN'
        }
      ],
      clickCount: 0,
      clickTimer: null
    }
  },
  computed: {
    ...mapGetters(['id']),
    aiUrl () {
      return `${process.env.VUE_APP_WEB_URL}#/AIChat?bookId=${this.$route.query.bookId}&source=h5`
    }
  },
  watch: {
    size(val) {
      localStorage.setItem('readSetfontSize', val)
      this.setSize()
    },
    async $route(to, from) {
      // if (window.viewerShow) {
      //   window.viewerShow.destroy()
      //   window.viewerShow = null
      // }
      if (this.$refs.note) {
        this.$refs.note.goBack()
      }
      if (this.$route.query && this.$route.query.token) {
        await this.$store.dispatch('user/AppLogin', 'Bearer ' + to.query.token)
        await this.$store.dispatch('user/GetInfo')
      }
      if (
        to.query.catalogueId !== from.query.catalogueId ||
        to.query.bookId !== from.query.bookId
      ) {
        if (this.$route.query && this.$route.query.token) {
          await this.$store.dispatch(
            'user/AppLogin',
            'Bearer ' + this.$route.query.token
          )
          await this.$store.dispatch('user/GetInfo')
        }
        this.bookTree = []
        this.catalogueId = 0
        if (Number(this.$route.query.catalogueId)) {
          await this._getContent(Number(this.$route.query.catalogueId))
          this.$nextTick(() => {
            this.saveReadingProgress(
              this.id,
              this.$route.query.bookId,
              Number(this.$route.query.catalogueId)
            )
          })
          await this._getBookCatalogue()
        } else if (this.getReadingProgress(this.id, this.$route.query.bookId)) {
          await this._getContent(
            this.getReadingProgress(this.id, this.$route.query.bookId)
          )
          await this._getBookCatalogue()
        } else {
          await this._getBookCatalogue()
          await this._getContent(this.bookTree[0] && this.bookTree[0].id)
          this.$nextTick(() => {
            this.saveReadingProgress(
              this.id,
              this.$route.query.bookId,
              this.bookTree[0] && this.bookTree[0].id
            )
          })
        }
        await this._getBook()
        await this._getDigitalBookConfig()
        this.getNoteData()
        initFindWin(false, 'serchContent')
        this.$nextTick(() => {
          this.share()
        })
        this.init()
        window['setBingoToken'] = async (val) => {
          await this.$store.dispatch('user/AppLogin', 'Bearer ' + val)
          await this.$store.dispatch('user/GetInfo')
          location.reload()
          this.initHighLine()
          this.bookTree = []
          await this._getBookCatalogue()
          const id =
            Number(this.$route.query.catalogueId) ||
            this.getReadingProgress(this.id, this.$route.query.bookId) ||
            this.bookTree[0].id
          await this._getContent(id)
          await this._getBook()
          this.getNoteData()
          initFindWin(false, 'serchContent')
          this.init()
        }
      }
    }
  },
  async mounted() {
    // await this.$store.dispatch('user/LogOut')
    if (this.$route.query && this.$route.query.token) {
      await this.$store.dispatch(
        'user/AppLogin',
        'Bearer ' + this.$route.query.token
      )
      await this.$store.dispatch('user/GetInfo')
    }
    this.initHighLine()
    this.bookTree = []
    if (Number(this.$route.query.catalogueId)) {
      await this._getContent(Number(this.$route.query.catalogueId))
      this.$nextTick(() => {
        this.saveReadingProgress(
          this.id,
          this.$route.query.bookId,
          Number(this.$route.query.catalogueId)
        )
      })
      await this._getBookCatalogue()
    } else if (this.getReadingProgress(this.id, this.$route.query.bookId)) {
      await this._getContent(
        this.getReadingProgress(this.id, this.$route.query.bookId)
      )
      await this._getBookCatalogue()
    } else {
      await this._getBookCatalogue()
      await this._getContent(this.bookTree[0] && this.bookTree[0].id)
      this.$nextTick(() => {
        this.saveReadingProgress(
          this.id,
          this.$route.query.bookId,
          this.bookTree[0] && this.bookTree[0].id
        )
      })
    }
    await this._getBook()
    await this._getDigitalBookConfig()
    this.getNoteData()
    initFindWin(false, 'serchContent')
    this.$nextTick(() => {
      this.share()
    })
    this.init()
    window['setBingoToken'] = async (val) => {
      await this.$store.dispatch('user/AppLogin', 'Bearer ' + val)
      await this.$store.dispatch('user/GetInfo')
      location.reload()
      this.initHighLine()
      this.bookTree = []
      await this._getBookCatalogue()
      const id =
        Number(this.$route.query.catalogueId) ||
        this.getReadingProgress(this.id, this.$route.query.bookId) ||
        this.bookTree[0].id
      await this._getContent(id)
      await this._getBook()
      this.getNoteData()
      initFindWin(false, 'serchContent')
      // this.$nextTick(() => {
      //   this.share()
      // })
      this.init()
    }
    // window.addEventListener('setBingoToken', (val) => {
    //   this.$toast(val)
    // })
    // 获取父容器尺寸
    const parentRect = this.$refs.parent.getBoundingClientRect()
    // 计算右下角位置
    this.aiBtnPosition.x = parentRect.width - 53 - 10 // 10px margin
    this.aiBtnPosition.y = parentRect.height - 53 - 70 // 70px margin

    const phoneName = this.getPhoneManufacturer()
    if (phoneName !== 'apple') {
      this.getConfigObj()
    } else {
      this.showAiBtn = true
    }
    document.addEventListener('mousemove', this.drag)
    document.addEventListener('mouseup', this.stopDrag)
    this.$nextTick(() => {
      this.parentRect = this.$refs.parent.getBoundingClientRect()
      this.childRect = this.$refs.child.getBoundingClientRect()
    })
  },
  beforeDestroy() {
    document.removeEventListener('selectionchange', this.handleTextSelection)
    document.removeEventListener('click', this.setEvent)
    if (!this.bookConfig.contentCopy) {
      window.removeEventListener('copy', this.enableCopy)
    }
  },
  methods: {
    async getConfigObj() {
      const param = {
        keyNo: 'DIGITAL_BOOK_APP_STORE_STATUS'
      }
      const { data } = await getConfigObj(param)
      if (data && data.keyValue) {
        let show = true
        const configObj = JSON.parse(data.keyValue)
        Object.values(configObj).forEach((value) => {
          if (value.status === 'UNDER_REVIEW') {
            show = false
          }
        })
        this.showAiBtn = show
        // if (configObj && configObj[phoneName] && configObj[phoneName].status && configObj[phoneName].status === 'UNDER_REVIEW') {
        //   this.showAiBtn = false
        // } else {
        //   this.showAiBtn = true
        // }
      }
    },
    getPhoneManufacturer() {
      const userAgent = navigator.userAgent.toLowerCase()
      console.log(navigator.userAgent)
      // 识别各手机厂商的特征字符串
      if (userAgent.includes('huawei')) {
        return 'huawei'
      } else if (userAgent.includes('honor')) {
        return 'honor'
      } else if (userAgent.includes('xiaomi') || userAgent.includes('redmi')) {
        return 'xiaomi'
      } else if (userAgent.includes('oppo')) {
        return 'oppo'
      } else if (userAgent.includes('vivo') || userAgent.includes('iqoo')) {
        return 'vivo'
      } else if (userAgent.includes('samsung')) {
        return 'samsung'
      } else if (userAgent.includes('meizu')) {
        return 'meizu'
      } else if (userAgent.includes('oneplus')) {
        return 'oneplus'
      } else if (userAgent.includes('google')) {
        return 'google'
      } else if (userAgent.includes('iphone')) {
        return 'apple'
      } else {
        return '未知'
      }
    },
    async deleteNote(id) {
      this.highlighter.remove(id)
    },
    backToView(id) {
      const element = document.querySelector(`[data-highlight-id="${id}"]`)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      } else {
        console.log(`Element with data-highlight-id="${id}" not found.`)
      }
    },
    enableCopy(event) {
      this.$toast('为保护作者版权，当前教材暂不支持复制')
      event.preventDefault()
    },
    enableDonload() {
      const download = document.getElementsByClassName('download_button')
      for (let i = 0; i < download.length; i++) {
        download[i].style.display = 'none'
      }
    },
    async _getDigitalBookConfig() {
      const { data } = await getDigitalBookConfig(
        {
          digitalBookId: this.bookInfo.id
        },
        {
          authorization: this.token
        }
      )
      this.bookConfig = data
      if (this.bookConfig && !this.bookConfig.attachFileDownload) {
        this.enableDonload()
      }
      if (!this.bookConfig.contentCopy) {
        window.addEventListener('copy', this.enableCopy)
      }
    },
    saveProgress() {
      this.saveReadingProgressId(
        this.id,
        this.catalogueId,
        this.getScrollPercentage(this.$refs.readContent)
      )
      if (
        window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.bingo_download
      ) {
        window.webkit.messageHandlers.bingo_action.postMessage('event_back')
      } else if (window.bingo_action) {
        window.bingo_action.postMessage('event_back')
      } else {
        this.$router.go(-1)
      }
    },
    getReadingProgressId(userId, chapterId) {
      // 获取所有用户的阅读进度数据
      const allProgress =
        JSON.parse(localStorage.getItem('readingProgressId')) || {}

      // 返回指定用户和章节的进度
      return allProgress[userId]?.[chapterId] || null
    },

    saveReadingProgressId(userId, chapterId, progress) {
      // 获取所有用户的阅读进度数据
      const allProgress =
        JSON.parse(localStorage.getItem('readingProgressId')) || {}

      // 如果当前用户没有数据，则初始化
      if (!allProgress[userId]) {
        allProgress[userId] = {}
      }

      // 更新当前用户的章节进度
      allProgress[userId][chapterId] = progress

      // 保存回 localStorage
      localStorage.setItem('readingProgressId', JSON.stringify(allProgress))
    },

    getScrollPercentage(container) {
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight - container.clientHeight
      return (scrollTop / scrollHeight) * 100 // 返回百分比
    },

    async setSize() {
      await this.getNoteData()
      await this.getHighLine()
      if (this.size === 18) {
        this.content = ''
        this.$nextTick(() => {
          this.content = this.contentCopy
          this.$nextTick(() => {
            window.MathJax.typesetPromise()
            Prism.highlightAll()
            this.bindVideo()
            this.setIframe()
            this.getHighLine()
            if (this.bookConfig && !this.bookConfig.attachFileDownload) {
              this.enableDonload()
            }
          })
        })
        return
      }
      const nodesObj = this.$refs.readContent.getElementsByTagName('*')
      const nodes = Object.values(nodesObj)
      nodes.forEach((node) => {
        if (node.style.fontSize) {
          node.style.fontSize = this.size + 'px'
        }
      })
    },
    init() {
      const fontFamily = localStorage.getItem('readSetfontFamily')
      const BgColor = localStorage.getItem('readSetBgColor')
      const fontSize = localStorage.getItem('readSetfontSize')
      this.font = JSON.parse(fontFamily) || this.font
      this.backgroundColor = JSON.parse(BgColor) || this.backgroundColor
      this.size = Number(fontSize) || this.size
    },
    checkFont(f) {
      if (typeof f !== 'string') {
        return false
      }
      var h = 'Arial'
      if (f.toLowerCase() === h.toLowerCase()) {
        return true
      }
      var e = 'a'
      var d = 100
      var a = 100
      var i = 100
      var c = document.createElement('canvas')
      var b = c.getContext('2d')
      c.width = a
      c.height = i
      b.textAlign = 'center'
      b.fillStyle = 'black'
      b.textBaseline = 'middle'
      var g = function (j) {
        b.clearRect(0, 0, a, i)
        b.font = d + 'px ' + j + ', ' + h
        b.fillText(e, a / 2, i / 2)
        var k = b.getImageData(0, 0, a, i).data
        return [].slice.call(k).filter(function (l) {
          return l !== 0
        })
      }
      return g(h).join('') !== g(f).join('')
    },
    onFontConfirm(item) {
      this.font = this.fontFamily.filter((item1) => {
        return item1.title === item
      })[0]
      this.showFontPicker = false
      localStorage.setItem('readSetfontFamily', JSON.stringify(this.font))
    },
    onBgConfirm(item) {
      this.backgroundColor = this.colorList.filter((item1) => {
        return item1.title === item
      })[0]
      this.showBgPicker = false
      localStorage.setItem(
        'readSetBgColor',
        JSON.stringify(this.backgroundColor)
      )
    },
    toTask(item) {
      this.$router.push({
        path: '/bingoBook/task',
        query: { id: item.id, studentCourseId: this.studentCourseId }
      })
    },
    toGroup(item) {
      this.$router.push({
        path: '/bingoBook/group',
        query: {
          id: item.id,
          studentCourseId: this.studentCourseId,
          from: 'read'
        }
      })
    },
    toNote() {
      console.log('打开笔记')
      this.$refs.note.open()
      // this.$router.push({
      //   path: '/bingoBook/note',
      //   query: { id: this.catalogueId, bookId: this.bookInfo.id }
      // })
    },
    async getNoteData() {
      this.noteList = []
      const { data } = await getDigitalNotesList({
        catalogueId: this.catalogueId,
        digitalNotesType: 'notes'
      })
      this.noteList = data || []
    },
    addNoteDone: debounce(
      function () {
        if (!this.noteValue) {
          this.$toast('请输入笔记内容')
          return
        }
        digitalNotes({
          catalogueId: this.catalogueId,
          apiType: this.noteType,
          digitalNotesType: 'notes',
          id: this.noteId ? this.noteId : null,
          selection: this.selectionText,
          notes: this.noteValue
        }).then(() => {
          this.getNoteData()
          this.$toast('保存成功！')
          this.showNotePopup = false
          if (this.noteType === 'create') {
            this.highlighter.fromRange(this.selection)
          }
        })
      },
      500,
      true
    ),
    initHighLine() {
      if (this.highlighter) {
        this.highlighter.destroy()
        document.removeEventListener('selectionchange', this.handleTextSelection)
        document.removeEventListener('click', this.setEvent)
      }
      this.highlighter = new Highlighter({
        $root: this.$refs.readContent, // 可以指定目标容器元素
        style: {
          className: 'highlighted-text' // 设置高亮样式的类名
        }
      })
      this.highlighter
        .on(Highlighter.event.CREATE, ({ sources }) => {
          if (this.highLineRenderList.includes(sources[0].id)) return
          this.highLineRenderList.push(sources[0].id)
          sources.forEach((s) => {
            const position = this.getPosition(this.highlighter.getDoms(s.id)[0])
            this.createTag(position.top, position.left, s.id)
          })
          const res = this.highLineData.filter((item) => {
            return JSON.parse(item.selection).id === sources[0].id
          })
          if (res.length === 0) {
            this.addNoteData(JSON.stringify(sources[0]))
            this.highLineList.push(sources[0])
          }
          //
        })
        .on(Highlighter.event.CLICK, ({ id }, _, e) => {
          const text = this.highLineList.filter((item) => {
            return item.id === id
          })[0].text
          let flag = false
          const cleanText = text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
          for (let i = 0; i < this.noteList.length; i++) {
            if (cleanText === this.noteList[i].selection.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')) {
              flag = true
            }
          }
          const elements = document.getElementsByClassName('my-remove-tip')
          for (let i = 0; i < elements.length; i++) {
            if (elements[i].dataset.id === id) {
              if (flag) {
                elements[i].innerHTML = '查看笔记'
              }
              elements[i].style.display === 'block'
                ? (elements[i].style.display = 'none')
                : (elements[i].style.display = 'block')
            }
          }
        })
      // 监听鼠标松开事件并进行高亮
      document.addEventListener('selectionchange', this.handleTextSelection)
      document.addEventListener('contextmenu', function (e) {
        const selection = window.getSelection()
        if (selection && selection.toString().length > 0) {
          e.preventDefault()
        }
      })
      document.addEventListener('click', this.setEvent)
    },
    async getHighLine() {
      this.highLineList = []
      const buttons = document.getElementsByClassName('my-remove-tip')
      for (var i = 0; i < buttons.length; i++) {
        buttons[i].parentNode.removeChild(buttons[i])
      }
      const { data } = await getDigitalNotesList({
        catalogueId: this.catalogueId,
        digitalNotesType: 'hightline'
      })
      this.highLineData = data || []
      this.highLineData.forEach((item) => {
        this.highLineList.push(JSON.parse(item.selection))
      })
      this.highLineRenderList = []
      this.highLineList.forEach((item) => {
        if (item.id) {
          this.highlighter.fromStore(
            item.startMeta,
            item.endMeta,
            item.text,
            item.id
          )
        }
      })
    },
    setEvent(e) {
      const $ele = e.target
      // delete highlight
      if (
        $ele.classList.contains('my-remove-tip') &&
        $ele.innerHTML.includes('删除高亮')
      ) {
        const id = $ele.dataset.id
        this.highlighter.removeClass('highlight-wrap-hover', id)
        this.highlighter.remove(id)
        const deId = this.highLineData.filter((item) => {
          return JSON.parse(item.selection).id === id
        })[0].id
        digitalNotes({
          catalogueId: this.catalogueId,
          apiType: 'delete',
          digitalNotesType: 'hightline',
          id: deId
        })
        $ele.parentNode.removeChild($ele)
      } else if (
        $ele.classList.contains('my-remove-tip') &&
        $ele.innerHTML.includes('查看笔记')
      ) {
        let text = ''
        this.highLineData.forEach((item) => {
          const id = $ele.dataset.id
          if (JSON.parse(item.selection).id === id) {
            text = JSON.parse(item.selection).text.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
          }
        })
        this.noteList.forEach((item, index) => {
          if (item.selection.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '') === text) {
            this.showNotePopup = true
            this.selectionText = item.selection
            this.noteValue = item.notes
            this.noteId = item.id
            this.noteType = 'update'
            $ele.style.display = 'none'
          }
        })
      } else if (!$ele.classList.contains('highlighted-text')) {
        const elements = document.getElementsByClassName('my-remove-tip')
        for (let i = 0; i < elements.length; i++) {
          // console.log(typeof (elements[i].style.display))
          elements[i].style.display = 'none'
        }
      }
    },
    addNoteData(data) {
      digitalNotes({
        catalogueId: this.catalogueId,
        apiType: 'create',
        digitalNotesType: 'hightline',
        selection: data
      }).then(async () => {
        const { data } = await getDigitalNotesList({
          catalogueId: this.catalogueId,
          digitalNotesType: 'hightline'
        })
        console.log(data)
        this.highLineData = data
      })
    },
    createTag(top, left, id) {
      const $span = document.createElement('span')

      // 获取窗口的宽度和高度
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      // 假设标签的宽度和高度
      const tagWidth = 100 // 假设标签宽度为100px
      const tagHeight = 30 // 假设标签高度为30px

      // 初步计算位置
      let adjustedLeft = left - 20
      let adjustedTop = top - 25

      // 确保标签不超出窗口右侧
      if (adjustedLeft + tagWidth > windowWidth) {
        adjustedLeft = windowWidth - tagWidth - 10 // 保持10px边距
      }

      // 确保标签不超出窗口底部
      if (adjustedTop + tagHeight > windowHeight) {
        adjustedTop = top - tagHeight - 10 // 保持10px边距
      }

      // 确保标签不超出窗口左侧
      if (adjustedLeft < 0) {
        adjustedLeft = 10 // 保持10px边距
      }

      // 确保标签不超出窗口顶部
      if (adjustedTop < 0) {
        adjustedTop = 10 // 保持10px边距
      }

      // 设置标签位置
      $span.style.left = `${adjustedLeft}px`
      $span.style.top = `${adjustedTop}px`

      // 设置其他属性
      $span.dataset['id'] = id
      $span.textContent = '删除高亮'
      $span.classList.add('my-remove-tip')

      // 添加到页面中
      this.$refs.readContent.appendChild($span)
    },
    getPosition($node) {
      const offset = {
        top: 0,
        left: 0
      }
      while ($node && $node.id !== 'serchContent') {
        offset.top += $node.offsetTop
        offset.left += $node.offsetLeft
        $node = $node.offsetParent
      }

      return offset
    },
    handleTextSelection(event) {
      const selection = window.getSelection()
      if (selection && selection.toString().length > 0) {
        this.selection = selection.getRangeAt(0)
        this.selectionText = selection.toString()
        console.log(selection)
        // 获取选区坐标
        event.preventDefault()
        const range = selection.getRangeAt(0)
        const rect = range.getBoundingClientRect()

        // 更新浮动框位置
        // 获取窗口的宽度和高度
        const windowWidth = window.innerWidth
        const windowHeight = window.innerHeight

        // 原始的计算位置
        let top = rect.top + rect.height + window.scrollY + 50
        let left = rect.left + window.scrollX

        // 确保工具栏不超出窗口底部
        const toolbarHeight = 50 // 假设工具栏高度为 50
        if (top + toolbarHeight > windowHeight) {
          top = windowHeight - toolbarHeight - 10 // 调整到窗口底部，并留一点边距
        }

        // 确保工具栏不超出窗口右侧
        const toolbarWidth = 100 // 假设工具栏宽度为 200
        if (left + toolbarWidth > windowWidth) {
          left = windowWidth - toolbarWidth - 10 // 调整到窗口右侧，并留一点边距
        }

        // 更新工具栏位置
        this.toolbarPosition = {
          top: top,
          left: left
        }

        // 显示浮动框
        this.showToolbar = true
      } else {
        this.showToolbar = false
      }
    },
    highlightText() {
      const selection = this.selection
      if (!selection.isCollapsed) {
        this.highlighter.fromRange(selection)
      }
      this.showToolbar = false
    },
    addNote() {
      // if (!selection.isCollapsed) {
      //   this.highlighter.fromRange(selection)
      // }
      this.noteId = 0
      this.noteType = 'create'
      this.noteValue = ''
      this.showToolbar = false
      this.showNotePopup = true
    },
    interpretation() {
      this.showToolbar = false
      this.getPreview()
    },
    copyText() {
      if (!this.bookConfig.contentCopy) {
        this.$toast('为保护作者版权，当前教材暂不支持复制')
        return
      }
      const selection = this.selectionText
      const textArea = document.createElement('textarea')
      textArea.value = selection
      document.body.appendChild(textArea)
      textArea.select()
      textArea.setSelectionRange(0, 99999) // 对于移动端
      document.execCommand('copy')
      document.body.removeChild(textArea)
      this.$toast('已复制！')
    },
    toRead(id) {
      this.updateProgress()
      this.read(id)
    },
    updateProgress() {
      const element = this.$refs.readContent
      const scrollTop = element.scrollTop
      const scrollHeight = element.scrollHeight
      const clientHeight = element.clientHeight

      if (scrollHeight <= clientHeight) {
        this.sendProgress(100, this.catalogueId)
      } else {
        const scrollPercentage =
          (scrollTop / (scrollHeight - clientHeight)) * 100
        this.sendProgress(Math.round(scrollPercentage), this.catalogueId)
      }
    },
    sendProgress: debounce(async function (val, id) {
      if (this.id === '') {
        return
      }
      await updateReadProgress({
        studentCourseId:
          this.studentCourseId ||
          this.bookInfo.classstudentCourseId ||
          this.bookInfo.studentCourseId,
        catalogueId: id,
        progress: val
      })
    }, 500),
    findNextNode(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i + 1] || null
        }
      }

      return null
    },
    findPreviousNode(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i - 1] || null
        }
      }

      return null
    },
    handleScroll() {
      const container = this.$refs.readContent
      const tolerance = 5 // 容差值

      this.isAtBottom =
        container.scrollTop + container.clientHeight >=
          container.scrollHeight - tolerance ||
        container.scrollHeight <= container.clientHeight

      this.isAtTop = container.scrollTop <= tolerance
    },

    handleTouchStart(event) {
      this.startY = event.touches[0].clientY
      this.touchMoved = false // 用于判断是否移动
    },

    handleTouchMove(event) {
      this.touchMoved = true // 标记为已经移动
      const container = this.$refs.readContent
      // 判断是否到底部
      this.isAtBottom =
        container.scrollTop + container.clientHeight >=
        container.scrollHeight - 5
      const distance = this.startY - event.changedTouches[0].clientY
      // 检查是否向上滚动
      this.isAtTop = container.scrollTop <= 5
      if (this.isAtTop && this.flag) {
        event.preventDefault()
      }

      if (this.isAtBottom && distance > 10) {
        const nextNode = this.findNextNode(this.bookTree, this.catalogueId)
        if (nextNode) {
          this.$toast('即将进入下一章')
        } else {
          this.$toast('已到最后一章')
        }
      } else if (this.isAtTop && distance < -10) {
        const prevNode = this.findPreviousNode(this.bookTree, this.catalogueId)
        if (prevNode) {
          this.$toast('即将进入上一章')
        } else {
          this.$toast('已到第一章')
        }
        this.flag = true
      }
    },

    handleTouchEnd(event) {
      if (!this.touchMoved) return // 如果没有移动，则不执行
      const distance = this.startY - event.changedTouches[0].clientY // 计算手指移动的距离

      // 如果到达底部并且向下滚动超过50像素，则翻页
      if (this.isAtBottom && distance > 50) {
        this.hasExecuted = true // 设置标志，防止重复执行
        const nextNode = this.findNextNode(this.bookTree, this.catalogueId)
        this.sendProgress(100, this.catalogueId)
        if (nextNode) {
          this.loading = true
          this.$toast('已切换到下一章节')
          this.read(nextNode.id)
        }
      }

      // 如果到达顶部并且向上滚动超过50像素，则翻页
      if (this.isAtTop && distance < -50) {
        this.hasExecuted = true // 设置标志，防止重复执行
        const prevNode = this.findPreviousNode(this.bookTree, this.catalogueId)
        if (prevNode) {
          this.loading = true
          this.read(prevNode.id)
          this.$toast('已切换到上一章节')
        }
      }
      this.flag = false
      this.hasExecuted = false // 重置标志，允许下一次触摸事件执行
    },
    bindVideo() {
      this.replaceVideoControls()
      const videos = document.querySelectorAll('video')
      videos.forEach((video) => {
        video.setAttribute('controlslist', 'nodownload noplaybackrate')
        video.setAttribute('disablePictureInPicture', true)
        video.addEventListener('play', () => {
          console.log('Video playing') // 调试信息

          // 进入全屏模式
          if (video.requestFullscreen) {
            video.requestFullscreen().catch((err) => console.log(err))
          } else if (video.webkitRequestFullscreen) {
            // Safari
            video.webkitRequestFullscreen()
          } else if (video.msRequestFullscreen) {
            // IE/Edge
            video.msRequestFullscreen()
          }
        })

        // 监听全屏变化事件
        document.addEventListener('fullscreenchange', () => {
          if (!document.fullscreenElement) {
            video.pause()
          }
        })
      })

      // 新增table样式
      const tables = document.querySelectorAll('table')
      tables.forEach((table) => {
        const wrapper = document.createElement('div') // 创建一个新的div作为父元素
        wrapper.style.overflowX = 'auto' // 设置样式
        table.parentNode.insertBefore(wrapper, table) // 在table前插入wrapper
        wrapper.appendChild(table) // 将table移入wrapper中
      })
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'bmp',
        'svg',
        'webp'
      ]
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = [
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'pdf'
      ]
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    playVideoInFullscreen(videoUrl) {
      // 使用 Vant 的 Dialog 创建一个弹窗，内部包含视频元素
      Dialog({
        message: `<video id="popup-video" src="${videoUrl}" controls controlslist="nodownload" autoplay style="width: 100%;"></video>`,
        className: 'custom-video-dialog',
        closeOnClickOverlay: true, // 点击空白处关闭弹窗
        showConfirmButton: false, // 隐藏确认按钮
        closeOnPopstate: true // 在浏览器返回时关闭弹窗
      }).then(() => {
        // 弹窗关闭时停止视频播放
        const videoElement = document.getElementById('popup-video')
        if (videoElement) {
          videoElement.pause()
          videoElement.src = '' // 清除视频源
        }
      })
    },
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      console.log(url, fileName)
      if (this.getFileType(fileName) === 'video') {
        this.playVideoInFullscreen(url)
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.$router.push({
          path: '/bingoBook/officeView',
          query: { url: url }
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.images.open()
      } else {
        this.$toast('该类型文件暂不支持预览')
      }
    },
    setIframe() {
      var contentContainer = document.getElementsByClassName('readContent')[0]
      var iframes = contentContainer.querySelectorAll('iframe')
      iframes.forEach(function (iframe) {
        iframe.style.height =
          (window.innerWidth / 793) * iframe.getAttribute('height') + 'px'
      })
    },
    openShare() {
      if (
        window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.bingo_download
      ) {
        window.webkit.messageHandlers.bingo_action.postMessage('event_share')
      } else if (window.bingo_action) {
        window.bingo_action.postMessage('event_share')
      } else {
        this.$refs.shareGuide.open()
      }
    },
    async share() {
      const url = location.href.split('#')[0]
      const params = {
        url: url
      }
      const linkUrl = url + `#/bingoBook/bookInfo?id=${this.bookInfo.id}`
      const res = await getWxGzhShareSignature(params)
      // 配置分享的内容
      const shareOptions = {
        title: '《' + this.bookInfo.title + '》',
        desc: this.bookInfo.author ? '作者：' + this.bookInfo.author : '',
        link: linkUrl,
        imgUrl: this.bookInfo.cover,
        success: () => {
          // 分享成功回调
          this.$refs.shareGuide.close()
        },
        cancel: () => {
          // 分享取消回调
          console.log('分享取消')
        }
      }
      initWechatShare(res, shareOptions)
    },
    readEvent(e) {
      if (e.target.classList.contains('img_card_button')) {
        this.imgListInfo = JSON.parse(
          e.target.parentNode.getElementsByClassName('info')[0].innerText
        )
        console.log(this.imgListInfo)
        this.$refs.images.open()
      }
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        console.log(item, item.children)
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.open()
      }
      if (e.target.parentNode.classList.contains('file_download')) {
        const item = e.target.parentNode.children[3].children[0]
        this.downloadFun(item)
      }
      if (e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          if (
            window.webkit &&
            window.webkit.messageHandlers &&
            window.webkit.messageHandlers.bingo_download
          ) {
            window.webkit.messageHandlers.bingo_download.postMessage(
              JSON.stringify({
                fileName: e.target.parentNode.children[2].innerText,
                filePath: e.target.parentNode.children[1].innerText
              })
            )
            return
          } else if (window.bingo_download) {
            window.bingo_download.postMessage(
              JSON.stringify({
                fileName: e.target.parentNode.children[2].innerText,
                filePath: e.target.parentNode.children[1].innerText
              })
            )
            return
          }
          if (this.isWeChatBrowser()) {
            const url = e.target.parentNode.children[1].innerText
            this.$copyText(url)
            this.$toast({
              message: '链接已复制，请前往默认浏览器打开下载',
              duration: 5000
            })
          } else {
            const url = e.target.parentNode.children[1].innerText
            const fileName = e.target.parentNode.children[2].innerText
            throttle(function () {
              const xhr = new XMLHttpRequest()
              xhr.open('get', url)
              xhr.responseType = 'blob'
              xhr.addEventListener('progress', (e) => {})
              xhr.send()
              xhr.onload = function () {
                if (this.status === 200 || this.status === 304) {
                  const blob = new Blob([this.response], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
                  })
                  saveAs(blob, fileName)
                }
              }
            }, 2000)
          }
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        const testId = e.target.parentNode.getAttribute('data-id')
        const ids = e.target.parentNode.getAttribute('data-ids')
        const studentCourseId =
          this.studentCourseId ||
          this.bookInfo.classstudentCourseId ||
          this.bookInfo.studentCourseId ||
          0
        this.$router.push({
          path: '/bingoBook/test',
          query: { testId, ids, studentCourseId }
        })
      }
      if (e.target.classList.contains('to_training')) {
        this.$toast('为体验完整功能，请在电脑客户端操作')
      }
      if (e.target.classList.contains('to_excel')) {
        this.$toast('为体验完整功能，请在电脑客户端操作')
      }
      if (e.target.classList.contains('to_aiTrain')) {
        this.$toast('为体验完整功能，请在电脑客户端操作')
      }
      if (e.target.tagName === 'IMG') {
        const img = e.target
        const parent = img.parentElement
        const grandparent = parent ? parent.parentElement : null
        const parentHasClass =
          parent && parent.classList.contains('mceNonEditable')
        const grandparentHasClass =
          grandparent && grandparent.classList.contains('mceNonEditable')
        if (parentHasClass || grandparentHasClass) {
          return
        }
        this.imgSrc = e.target.src
        this.showImg = true
        // window.viewerShow = this.$viewerApi({
        //   images: [e.target.src],
        //   options: {
        //     toolbar: false,
        //     navbar: false,
        //     title: false,
        //     transition: false,
        //     movable: false,
        //     zoomable: false,
        //     hidden: function() {
        //       window.viewerShow = null
        //       document.getElementById('serchContent').style.height = '100%'
        //     }
        //   }
        // })
      }
    },
    isWeChatBrowser() {
      const ua = navigator.userAgent.toLowerCase()
      return ua.includes('micromessenger')
    },
    read(id) {
      this.saveReadingProgressId(
        this.id,
        this.catalogueId,
        this.getScrollPercentage(this.$refs.readContent)
      )
      if (
        this.bookInfo.studentCourseId === 0 &&
        this.bookTree[2] &&
        id === this.bookTree[2].id
      ) {
        this.$toast('试读结束，请先购买！')
        this.loading = false
        return
      }
      if (id === this.getReadingProgress(this.id, this.$route.query.bookId)) {
        this.saveReadingProgress(this.id, this.$route.query.bookId, id)
        this._getContent(id)
      } else {
        this.saveReadingProgress(this.id, this.$route.query.bookId, id)
        this._getContent(id)
        this.show = false
      }
    },
    getReadingProgress(userId, bookId) {
      // 获取所有用户的阅读进度数据
      const allProgress =
        JSON.parse(localStorage.getItem('readingProgress')) || {}

      // 返回指定用户和书籍的当前章节
      return typeof allProgress[userId]?.[bookId] === 'number'
        ? allProgress[userId]?.[bookId]
        : null
    },
    saveReadingProgress(userId, bookId, chapter) {
      // 获取所有用户的阅读进度数据
      const allProgress =
        JSON.parse(localStorage.getItem('readingProgress')) || {}

      // 如果当前用户没有数据，则初始化
      if (!allProgress[userId]) {
        allProgress[userId] = {}
      }

      // 更新当前用户的书籍进度
      allProgress[userId][bookId] = chapter

      // 保存回 localStorage
      localStorage.setItem('readingProgress', JSON.stringify(allProgress))
    },
    findNodeByIdInForest(forest, targetId) {
      for (const tree of forest) {
        const result = this.findNodeById(tree, targetId)
        if (result) {
          return result
        }
      }
      return null
    },
    findNodeById(tree, targetId) {
      // 如果当前节点的 id 匹配，返回该节点
      if (tree.id === targetId) {
        return tree
      }

      // 如果有子节点，递归遍历子节点
      if (tree.childCatalogue) {
        for (const child of tree.childCatalogue) {
          const result = this.findNodeById(child, targetId)
          if (result) {
            return result
          }
        }
      }

      // 如果没有找到，返回 null
      return null
    },
    async _getContent(id) {
      if (!id) {
        this.loading = false
        this.$toast('本书暂无内容')
        return
      }
      const { data } = await getContent({
        catalogueId: id
      })
      this.contentCopy = data
        ? JSON.parse(
          JSON.stringify(data[0].data + ' <div class="tips">本页已到底</div>')
        )
        : ''
      this.content = data
        ? data[0].data + ' <div class="tips">本页已到底</div>'
        : ''
      this.catalogueId = id
      if (this.bookTree.length !== 0) {
        this.title = this.findNodeByIdInForest(
          this.bookTree,
          Number(this.catalogueId)
        ).title
      }
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
        Prism.highlightAll()
        this.bindVideo()
        this.setIframe()
        this.setSize()
        this.loading = false
        this.$refs.readContent.scrollTop = 0
        const progress = this.getReadingProgressId(this.id, this.catalogueId)
        const container = this.$refs.readContent
        if (progress !== null) {
          this.$toast('已定位到上次阅读位置')
          const scrollHeight = container.scrollHeight - container.clientHeight
          const scrollTop = (progress / 100) * scrollHeight // 根据百分比计算 scrollTop
          container.scrollTop = scrollTop // 设置容器的 scrollTop，实现回显
        }
        if (this.bookConfig && !this.bookConfig.attachFileDownload) {
          this.enableDonload()
        }
      })
    },
    async _getBookCatalogue() {
      const { data } = await getBookCatalogue(
        {
          bookId: this.$route.query.bookId,
          type: 'CHAPTER'
        },
        {
          authorization: this.token
        }
      )
      this.bookTree = data
      if (this.bookTree.length !== 0 && this.catalogueId) {
        this.title = this.findNodeByIdInForest(
          this.bookTree,
          Number(this.catalogueId)
        ).title
      }
    },
    async _getBook() {
      console.log('111', this.$route.query.bookId)
      const { data } = await getBook({
        bookId: this.$route.query.bookId,
        scene: 'BOOK_CATALOGUE_OWN'
      })
      this.bookInfo = data
    },
    replaceVideoControls() {
      const videos = document.querySelectorAll('.video_button')
      videos.forEach(video => {
        // 隐藏原生控件
        video.controls = false

        // 创建播放器容器
        const playerContainer = document.createElement('div')
        playerContainer.style.position = 'relative'
        playerContainer.style.width = `${video.clientWidth}px`
        playerContainer.style.height = `${video.clientHeight}px`
        playerContainer.style.display = 'flex'
        playerContainer.style.justifyContent = 'center'
        playerContainer.style.alignItems = 'center'

        // 创建播放按钮
        const playButton = document.createElement('img')
        playButton.style.width = '20%'
        playButton.src = require('@/assets/images/play.png')
        playButton.style.position = 'absolute'
        playButton.style.zIndex = '10'
        playButton.addEventListener('click', () => {
          if (video.paused) {
            video.play()
          } else {
            video.pause()
          }
        })

        // 将视频从原父节点中移除
        const originalParent = video.parentNode
        originalParent.removeChild(video)
        video.style.width = '100%'
        // 将视频和播放按钮添加到播放器容器
        playerContainer.appendChild(video)
        playerContainer.appendChild(playButton)

        // 将播放器容器插入到原视频位置
        originalParent.appendChild(playerContainer)
      })
    },
    handleTitleClick() {
      console.log('触发五连击事件')
      this.clickCount++
      // 清除之前的定时器
      clearTimeout(this.clickTimer)
      // 设置新的定时器，1.5秒后重置点击次数
      this.clickTimer = setTimeout(() => {
        this.clickCount = 0
      }, 1500)

      // 当点击次数达到5次时触发
      if (this.clickCount === 5) {
        this.clickCount = 0
        clearTimeout(this.clickTimer)
        console.log('触发五连击事件')
        new window.VConsole()
      }
    },
    startDrag(e) {
      this.isDragging = true
      this.startPos = {
        x: e.clientX - this.aiBtnPosition.x,
        y: e.clientY - this.aiBtnPosition.y
      }
    },
    drag(e) {
      if (!this.isDragging) return

      // 计算新位置
      let newX = e.clientX - this.startPos.x
      let newY = e.clientY - this.startPos.y

      // 确保不超出父元素左边界
      newX = Math.max(newX, 0)
      // 确保不超出父元素右边界
      newX = Math.min(newX, this.parentRect.width - this.childRect.width)

      // 确保不超出父元素上边界
      newY = Math.max(newY, 0)
      // 确保不超出父元素下边界
      newY = Math.min(newY, this.parentRect.height - this.childRect.height)

      this.aiBtnPosition = {
        x: newX,
        y: newY
      }
    },
    stopDrag() {
      this.isDragging = false
    },
    async getPreview() {
      try {
        this.isWorking = true
        this.previewHtml = '<p>思考中...</p>'
        this.showPreview = true
        const message = `结合上下文解读："${window.getSelection().toString()}"这段话在知识库中的含义，言简意赅，字数不超过300，不做任何推荐，不返回其他内容`
        const url = `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${message}&dataId=${this.$route.query.bookId}&requestId=''&dataType=DIGITAL_BOOK`
        const response = await fetch(url)
        if (!response.ok) throw new Error('Network response was not ok')
        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        while (result) {
          const { done, value } = await reader.read()
          if (done) {
            result = false
            this.isWorking = false
            this.scrollToTop()
            break
          }
          const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
          const arr = chunkText.split('-down-')
          arr.forEach(item => {
            if (this.isJSON(item)) {
              if (JSON.parse(item).data.result) {
                res += JSON.parse(item).data.result
              }
            }
          })
          this.previewHtml = this.renderMarkdown(res)
          this.scrollToBottom()
        }
      } catch (e) {
        this.isWorking = false
        console.log(e)
      }
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    renderMarkdown (text) {
      text = text.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
      let html = this.md.render(text)
      html = html.replace(/>\s+</g, '><')
      html = html.replace(/<\/td>\s+<td>/g, '</td><td>')
      html = html.replace(/<\/th>\s+<th>/g, '</th><th>')
      html = html.replace(/<\/tr>\s+<tr>/g, '</tr><tr>')
      return html
    },
    // 滚动到最底部的方法
    scrollToBottom() {
      // 使用 $nextTick 确保 DOM 已更新
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer
        if (container) {
          // 设置滚动条位置到最底部
          container.scrollTop = container.scrollHeight
        }
      })
    },
    // 滚动到最顶部的方法
    scrollToTop() {
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer
        if (container) {
          // 设置滚动条位置到最顶部
          container.scrollTop = 0
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
@font-face {
  font-family: fangsong;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918054519.woff2')
    format('woff2');
}

@font-face {
  font-family: kaiti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918253536.woff')
    format('woff');
}

@font-face {
  font-family: heiti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024030517123260.TTF')
    format('opentype');
}

@font-face {
  font-family: yahei;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918324516.woff')
    format('woff');
}

@font-face {
  font-family: pingfang;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918353783.woff')
    format('woff');
}

@font-face {
  font-family: siyuanheiti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918540864.otf')
    format('opentype');
}

@font-face {
  font-family: siyuansongti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022918583626.otf')
    format('opentype');
}

@font-face {
  font-family: songti;
  src: url('https://static.bingotalk.cn/bingodev/file/2024022919221599.woff')
    format('woff');
}

.show_img_dialog{
  border-radius: 0;
  width: 95%;
  overflow: auto;
  max-height: 90%;
  margin: 0 auto;
  display: flex;
  margin-top: 20px;
  background: none;
}
.show_img {
  width: 100%;
}
.toolbar {
  z-index: 9999;
}
::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.4);
}
.pop_card {
  padding: 20px;
  .block {
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    text-align: center;
  }
  .size {
    text-align: center;
  }
  .color-options {
    width: 100%;
    height: 50px;
    display: flex;
    overflow-x: auto;
    justify-content: space-between;
    margin-top: 10px;
  }
  .color-options::-webkit-scrollbar {
    display: none;
  }
  .color-option {
    width: 60px;
    height: 27px;
    display: block;
    margin-left: 5px;
    div {
      width: 60px;
      height: 27px;
      border-radius: 13px;
      border: 2px solid transparent;
    }
    .active {
      border-color: #49c0f8;
    }
  }
  .color-option p {
    width: 60px;
    font-size: 8px;
    text-align: center;
    white-space: nowrap;
  }
}

.title {
  font-size: 14px;
  font-weight: bold;
  margin-left: 10px;
  margin-top: 20px;
}

.file {
  width: 100%;
  height: 90%;
  padding-top: 12px;
  margin-top: 15px;
  .mode-option {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 35.3%;
    border-bottom: 1px solid #e0e0e0;
    font-weight: bold;
  }

  .mode-icon {
    width: 50px; // 根据需要调整大小
    height: 50px; // 根据需要调整大小
    margin-right: 10px;
  }
}

.note_main {
  width: 100%;
  padding: 20px;
  margin-top: 10px;
  ::v-deep .el-textarea__inner {
    width: 300px;
    margin: 0 auto;
    font-size: 14px;
  }
  .soyin {
    font-size: 14px;
    margin-left: 15px;
    font-weight: bold;
  }

  .button {
    width: 300px;
    display: block;
    margin: 20px auto;
    background: #49c0f8;
    border: none;
    border-radius: 5px;
  }
}

::v-deep .my-remove-tip {
  box-sizing: border-box;
  position: absolute;
  border: 1px solid #fff;
  border-radius: 3px;
  height: 2rem;
  width: 6rem;
  color: #fff;
  display: none;
  background: #444;
  text-align: center;
  font-size: 0.8rem;
  cursor: pointer;
  line-height: 2rem;
  overflow: visible;
}

.item {
  padding: 10px;
  background: #ffffff;
  cursor: pointer;
  border: none;
}

::v-deep .highlighted-text {
  background-color: yellow;
  /* 自定义颜色 */
  // padding: 2px;
}

::v-deep .van-nav-bar__title {
  font-size: 14px;
  height: 20px;
  line-height: 20px;
}

::v-deep .van-nav-bar__left {
  font-size: 14px;
}

.logo {
  width: 174px;
  height: 68px;
  display: block;
  margin: 40px auto;
}
::v-deep .van-slider {
  width: 70%;
}
.read_content {
  width: 100%;
  height: 100%;
  background: #ffffff;
  overflow: hidden;

  .readContent {
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px;
    padding-bottom: 80px;
    overflow-y: scroll;
    overflow-x: hidden;
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
    position: relative;
    /* 启用惯性滚动 */
    ::v-deep * {
      max-width: 100%;
    }
    ::v-deep .video_button {
      height: 100px !important;
      border-radius: 0;
    }
    // ::v-deep .video_button::-webkit-media-controls-play-button {
    //   transform: scale(0.5) !important;
    // }
    ::v-deep pre > code {
      display: block;
      font-size: 12px;
      white-space: pre-wrap;
      word-break: break-all;
    }
    ::v-deep th {
      border: 1px solid #bbb;
    }
    ::v-deep td {
      border: 1px solid #bbb;
    }
    ::v-deep table {
      border-collapse: collapse;
    }
  }

  ::v-deep .tips {
    width: 100%;
    text-align: center;
    font-size: 12px;
    margin-top: 20px;
    color: #e0e0e0;
  }

  .emty {
    width: 100%;
    height: 100%;
  }

  .share {
    width: 53px;
  }
  .ai_btn {
    width: 53px;
    height: 53px;
    position: fixed;
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 50px;
      height: 50px;
    }
  }

  .buttom_bar {
    width: 100%;
    height: 60px;
    padding: 10px;
    border-top: 1px solid #e0e0e0;
    background: #ffffff;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    .button_group {
      width: 210px;
      position: absolute;
      left: 150px;
      top: 10px;
      display: flex;
      justify-content: space-between;

      img {
        width: 30px;
        height: 42px;
      }
    }

    .mulu {
      display: flex;
      img {
        width: 79px;
      }
    }
  }

  .title {
    padding: 10px;
    font-size: 16px;
    font-weight: 700;
    height: 20px;
  }

  .mulu_content {
    padding: 10px;
    height: 80%;
    overflow: auto;
  }
  .preview-content{
    width: 100%;
    height: 100%;
    padding: 20px 10px 10px 10px;
    overflow-y: auto;
    ::v-deep table {
      border-collapse: collapse;
    }
    ::v-deep th {
      border: 1px solid #bbb;
    }
    ::v-deep td {
      border: 1px solid #bbb;
    }
  }

  .ai-overlay{
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2003;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,.7);
  }
  .ai-popup{
    height: 90%;
    z-index: 2004;
    bottom: 0;
    left: 0;
    width: 100%;
    position: fixed;
    max-height: 100%;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 16px 16px 0 0;
    .ai-popup-close{
      position: absolute;
      top: 20px;
      right: 20px;
      font-size: 16px;
      z-index: 2005;
    }
    .ai-main{
      width: 100%;
      height: 100%;
      background: white;
      overflow: hidden;
      padding-bottom: 50px;
      .ai-header{
        width: 100%;
        height: 60px;
        border-bottom: 1px solid #E0E0E0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
        background-color: #cbe2ff;
      }
      .ai-content{
        width: 100%;
        height: calc(100% - 40px);
        box-sizing: border-box;
      }
    }
  }
}

/* 移动端优化的淡入动画 */
.mobile-fade-enter-active, .mobile-fade-leave-active {
  transition: opacity 0.3s ease;
  will-change: opacity; /* 提升移动端性能 */
}
.mobile-fade-enter, .mobile-fade-leave-to {
  opacity: 0;
}
/* 内容上升动画 */
.popup-up-enter-active, .popup-up-leave-active {
  transition: transform 0.3s ease-out;
}
.popup-up-enter, .popup-up-leave-to {
  transform: translateY(100%);
}

::v-deep .van-nav-bar__content {
  background: #ffffff;
  height: 46px;
}

::v-deep .van-nav-bar__text {
  color: #000000 !important;
}

::v-deep .van-icon-arrow-left {
  color: #000000 !important;
}

.content {
  padding: 16px;
}

::v-deep .nav-title {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent; // 移除移动端点击高亮
}

</style>
