<template>
  <div class="main">
    <!-- <div class="header">《{{ title }}》</div> -->
    <div class="content">
      <img :src="imgSrc" alt="" />
    </div>
    <div class="footer">
      <div class="price">￥{{ price }}</div>
      <van-button v-if="!payed" class="submit_button" color="linear-gradient(90deg, #48C6EF 0%, #6F86D6 100%)" @click="showWxLogin">立即报名</van-button>
      <van-button v-if="payed" class="submit_button" color="grey">已报名</van-button>
    </div>
    <van-popup v-model="show" round position="bottom" :style="{ height: '60%' }">
      <div class="toast_header">
        <van-icon class="close" name="cross" @click="show=false" />
      </div>
      <van-form ref="form" class="form" @submit="onSubmit">
        <van-field
          v-model="name"
          class="form_item"
          required
          name="孩子姓名"
          label="孩子姓名"
          placeholder="请输入孩子姓名"
          :rules="[{ required: true,message: '请输入孩子姓名', trigger: 'onSubmit' }]"
        />
        <van-field
          readonly
          clickable
          name="calendar"
          class="form_item"
          :value="age"
          label="出生日期"
          placeholder="点击选择日期"
          @click="showCalendar = true"
        />
        <van-popup v-model="showCalendar"  position="bottom">
          <van-datetime-picker
            v-model="currentDate"
            title="出生日期"
            type="date"
            :min-date="minDate"
            @confirm="onConDatefirm"
            @cancel="showCalendar = false"
          />
        </van-popup>
        <!-- <van-calendar v-model="showCalendar" :min-date="new Date('2000-01-01')" @confirm="onConDatefirm" /> -->
        <van-field
          v-model="sex"
          clickable
          readonly
          class="form_item"
          name="孩子性别"
          label="孩子性别"
          placeholder="孩子性别"
          @click="showPicker = true"
        />
        <van-popup v-model="showPicker" round position="bottom">
          <van-picker
            show-toolbar
            :columns="['男','女']"
            @cancel="showPicker = false"
            @confirm="onConfirm"
          />
        </van-popup>
        <van-field
          v-model="phone"
          class="form_item"
          type="tel"
          required
          label="手机号"
          placeholder="请输入手机号"
          :disabled="!canSetMobile"
          :rules="[{
            required: true,
            trigger: 'onSubmit',
            message: '请输入手机号',
          }, {
            // 自定义校验规则
            validator: value => {
              return /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/
                .test(value)
            },
            message: '请输入正确格式的手机号码',
            trigger: 'onSubmit'
          }]"
        />
        <van-field
          v-if="!token"
          v-model="sms"
          class="form_item"
          clearable
          required
          label="短信验证码"
          placeholder="请输入短信验证码"
          :rules="[{ required: true,message: '请输入短信验证码', trigger: 'onSubmit' }]"
        >
          <template #button>
            <van-button size="small" text @click="getCode">  {{
              smsDisabled
                ? countdown > 0
                  ? countdown + 's后重新获取'
                  : '获取验证码'
                : '获取验证码'
            }}</van-button>
          </template>
        </van-field>
        <div style="margin: 16px;">
          <van-button color="linear-gradient(90deg, #48C6EF 0%, #6F86D6 100%)" block native-type="submit">立即报名</van-button>
        </div>
      </van-form>
    </van-popup>
    <van-dialog v-model="showGood" class="payToast" title="微信支付" :show-confirm-button="false">
      <van-icon class="close" name="cross" @click="showGood=false" />
      <p class="title">《{{ title }}》</p>
      <p class="price1">￥{{ price }}</p>
      <div class="pay" @click="buy">立即支付</div>
      <div style="height: 30px;"></div>
    </van-dialog>
    <van-dialog v-model="showPayed" title="支付成功!" confirm-button-color="#000" />
  </div>
</template>
<script>
import { getGoodsComm, getWXConfig, Login, parentBindChildNoSchool, bindUser, createOrders, prepay, getUserInfo } from '@/api/community-api.js'
import { validMobile } from '@/utils/validate'
import { verifyCodeForWeb } from '@/api/user-api'
import { Dialog } from 'vant'
export default {
  data() {
    return {
      minDate: new Date('1900-01-01'),
      currentDate: new Date('2024-01-01'),
      title: '',
      price: '',
      imgSrc: '',
      show: false,
      showPicker: false,
      showCalendar: false,
      name: '',
      age: '',
      sex: '',
      sms: '',
      phone: '',
      smsDisabled: false,
      countdown: 0,
      goodId: this.$route.query.id,
      appid: '',
      code: this.$route.query.code,
      token: '',
      showGood: false,
      finished: false,
      payed: false,
      showPayed: false,
      payData: null,
      canSetMobile: true,
      isLoginFailed: false
    }
  },
  mounted() {
    if (this.code) {
      this.showWxLogin()
    }
    this._getGoodsComm()
    this._getWXConfig()
  },
  methods: {
    buy() {
      createOrders({ goodsId: this.goodId }, { authorization: this.token }).then(res => {
        this.showGood = false
        if (res.data.orderStatus === 'TRADE_SUCCESS') {
          this.showPayed = true
          this.payed = true
          return
        }
        const params = {
          payMethod: 'WECHATPAY_GZH',
          ordersId: res.data.id
        }
        prepay(params, { authorization: this.token })
          .then(response => {
            if (response.code !== 200) {
              this.$message.error(response.data.message)
            } else {
              if (+response.code === 200) {
                const res = response.data
                this.payData = res
                this.handlePay()
              } else {
                this.$message.error(response.data.message)
              }
            }
          })
          .catch(e => {
            console.log(e)
          })
      })
    },
    handlePay() {
      const data = this.payData
      if (data) {
        window.WeixinJSBridge.invoke(
          'getBrandWCPayRequest', {
            appId: data.appId, // 公众号名称，由商户传入
            timeStamp: data.timeStamp, // 时间戳，自1970年以来的秒数
            nonceStr: data.nonceStr, // 随机串
            package: data.package,
            signType: data.signType, // 微信签名方式：
            paySign: data.paySign // 微信签名
          },
          (res) => {
            if (res.err_msg === 'get_brand_wcpay_request:ok') {
              this.showPayed = true
              this.payed = true
            } else if (res.err_msg === 'get_brand_wcpay_request:cancel') {
              Dialog({ message: '取消支付' })
            } else {
              Dialog({ message: '支付失败' })
            }
          }
        )
      }
    },
    getCode () {
      if (this.smsDisabled) return
      const mobile = this.phone
      if (mobile === '') {
        this.$message.error('手机号不能为空')
        return false
      } else if (!validMobile(mobile)) {
        this.$message.error('手机号不正确')
        return false
      }
      this._verifyCodeForWeb()
    },
    _verifyCodeForWeb () {
      verifyCodeForWeb({ mobile: this.phone }).then(res => {
        this.countdown = 60
        this.smsDisabled = true
        setTimeout(this.tick, 1000)
      })
    },
    tick () {
      if (this.countdown === 0) {
        this.smsDisabled = false
      } else {
        this.countdown--
        setTimeout(this.tick, 1000)
      }
    },
    async onSubmit() {
      if (!this.token) {
        const { data } = await bindUser({
          mobileOrEmail: this.phone,
          smsCode: this.sms,
          userType: 'STUDENT',
          loginType: 'WECHAT_GZH',
          authCode: this.code
        }, {
          authorization: this.token
        }
        )
        this.token = 'Bearer ' + data.access_token
      }
      parentBindChildNoSchool({ childName: this.name, birthDay: this.age, gender: this.sex ? this.sex === '男' ? 'MALE' : 'FEMALE' : '' }, {
        authorization: this.token
      }).then(() => {
        this.show = false
        this.showGood = true
        this.finished = true
      })
    },
    async Login() {
      if (this.token) {
        this.show = true
        return
      } else if (this.isLoginFailed) {
        this.code = ''
        this.handleGetWxCode()
        return
      }
      Login({ loginType: 'WECHAT_GZH', authCode: this.code }).then(res => {
        if (res.code === 200) {
          this.token = 'Bearer ' + res.data.access_token
          getUserInfo({}, { authorization: this.token }).then(res => {
            this.canSetMobile = false
            this.phone = res.data.mobile ? res.data.mobile : ''
            // this.name = res.data.displayName ? res.data.displayName : ''
            // this.age = res.data.birthday ? this.timestampToTime(res.data.birthday) : ''
            // this.sex = res.data.gender ? res.data.gender === 'MALE' ? '男' : '女' : ''
            this.show = true
          })
        }
      }).catch((err) => {
        if (err.code === 602) {
          this.show = true
          this.isLoginFailed = true
        } else {
          const baseUrl = window.location.origin
          const url = `${baseUrl}/#/community?id=${this.goodId}`
          this.code = ''
          window.location.href = url
        }
      })
    },
    timestampToTime(timestamp) {
      var date = new Date(timestamp)
      var Y = date.getFullYear() + '-'
      var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
      var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate())
      return Y + M + D
    },
    showWxLogin() {
      this.handleGetWxCode()
    },
    handleGetWxCode () {
      if (this.finished) {
        this.showGood = true
      } else if (this.code) {
        this.Login()
      } else {
        this.goWxCode()
      }
    },
    goWxCode(state = 1) {
      const baseUrl = window.location.origin
      const url = `${baseUrl}/#/community?id=${this.goodId}`
      window.location.href = `${process.env.VUE_APP_WEB_URL}get-weixin-code.html?appid=${this.appid}&scope=snsapi_userinfo&state=${state}&redirect_uri=${encodeURIComponent(url)}`
    },
    _getWXConfig() {
      const params = {
        url: decodeURIComponent(window.location.href.split('#')[0])
      }
      getWXConfig(params).then((res) => {
        this.appid = res.data.appId
      })
    },
    changeTitleClick(val) {
      document.setTitle = function (t) {
        document.title = t
        var i = document.createElement('iframe')
        i.src = ''
        i.style.display = ''
        i.onload = function () {
          setTimeout(function () {
            i.remove()
          }, 9)
        }
        document.body.appendChild(i)
      }
      setTimeout(function () {
        document.setTitle(val)
      }, 1)
    },
    async _getGoodsComm() {
      const { data } = await getGoodsComm({ goodsId: this.$route.query.id })
      this.title = data.title
      this.price = data.price
      this.imgSrc = data.discImg
      this.changeTitleClick('《' + this.title + '》')
    },
    getQueryString (name) {
      const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
      const result = window.location.search.substring(1).match(reg)
      if (result != null) {
        return decodeURIComponent(result[2])
      }
      return null
    },
    onConfirm(value) {
      this.sex = value
      this.showPicker = false
    },
    onConDatefirm (date) {
      this.age = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
      this.showCalendar = false
    }
  }
}
</script>
<style lang="scss" scoped>
.payToast{
  border-radius: 0;
  .close{
    position: absolute;
    right: 10px;
    top:10px;
    font-size: 20px;
  }
}
::v-deep .van-field__control::-webkit-input-placeholder {
  color: #9b9da9 !important;
}
.title, .price1{
  width: 100%;
  text-align: center;
  font-size: 22px;
  font-weight: 600;
}
.price1{
  font-size: 40px;
  font-weight: 700;
  padding-right: 20px;
}
.pay{
  width: 147px;
  height: 48px;
  text-align: center;
  border-radius: 5px;
  color:#fff;
  margin: 0 auto;
  line-height: 48px;
  background: linear-gradient(90deg, #48C6EF 0%, #6F86D6 100%);

}
.form{
    margin-top: 20px;
}
.form_item{
    margin-top: 15px;
}
.toast_header{
    width: 100%;
    padding: 15px;
    .close{
        float: right;
        font-size: 15px;
    }
}
.main{
    width: 100%;
    height: 100%;
    overflow-y: auto;
    .header{
        width: 100%;
        height: 70px;
        background:#ffffff;
        font-weight: 500;
        font-size: 20px;
        text-align: center;
        line-height: 70px;
        position: fixed;
        top:0;
        left: 0;
    }
    .content{
        width: 100%;
        margin-bottom: 70px;
        img{
          width: 100%;
          height: auto;
        }
    }
    .footer{
        width: 100%;
        height: 70px;
        background:#ffffff;
        position: fixed;
        left:0;
        bottom:0;
        .price{
            font-size: 22px;
            font-weight: 600;
            position: absolute;
            left: 40px;
            top:23px
        }
        .submit_button{
            width: 127px;
            height: 40px;
            font-size: 18px;
            font-weight: 600;
            position: absolute;
            right: 20px;
            top:15px
        }
    }
};
</style>
