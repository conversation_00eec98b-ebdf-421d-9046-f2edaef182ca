import { login, logout, getInfo, refreshToken } from '@/api/user-api'
import { pLogin, bindUser } from '@/api/partent-api'
import { getToken, getExpertToken, getPublishToken, removeExpertToken, removePublishToken, setToken, setExpertToken, setPublishToken, getPartentToken, setPartentToken, removePartentToken, setExpiresIn, removeToken, removeChildToken, removeChildName, removeChildId, removeAdminToken, setAuthorToken, getAuthorToken, removeAuthorToken } from '@/utils/auth'
import { getUserRelationListWithSameMainUser } from '../../api/user-api'
import { dataCenterLogin, dataCenterLogout, getDataCenterInfo } from '@/api/datacenter-api.js'
import { setAdminToken } from '@/utils/auth'

const state = {
  token: getToken(),
  partentToken: getPartentToken(),
  expertToken: getExpertToken(),
  publishToken: getPublishToken(),
  authorToken: getAuthorToken(),
  name: '',
  avatar: '',
  school: '',
  mobile: '',
  showCourseCenter: '',
  id: '',
  roles: [],
  permissions: [],
  userRelations: [],
  guideProgress: 0,
  currRole: localStorage.getItem('currRole') || 'PERSON',
  reviewRole: '',
  adminInfo: {
    role: ''
  }
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_EXPERT_TOKEN: (state, expertToken) => {
    state.expertToken = expertToken
  },
  SET_PUBLISH_TOKEN: (state, publishToken) => {
    state.publishToken = publishToken
  },
  SET_AUTHOR_TOKEN: (state, authorToken) => {
    state.authorToken = authorToken
  },
  SET_PARTENT_TOKEN: (state, token) => {
    state.partentToken = token
  },
  SET_EXPIRES_IN: (state, time) => {
    state.expires_in = time
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
  SET_SCHOOL: (state, school) => {
    state.school = school
  },
  SET_MOBILE: (state, mobile) => {
    state.mobile = mobile
  },
  SET_SHOWCOURSECENTER: (state, showCourseCenter) => {
    state.showCourseCenter = showCourseCenter
  },
  SET_ID: (state, id) => {
    state.id = id
  },
  SET_USERRELATIONS: (state, userRelations) => {
    state.userRelations = userRelations
  },
  SET_CHILD_ID: (state, childId) => {
    state.childId = childId
  },
  SET_CHILD_NAME: (state, childName) => {
    state.childName = childName
  },
  SET_GUIDE_PROGRESS: (state, guideProgress) => {
    state.guideProgress = guideProgress
  },
  SET_ADMIN_INFO: (state, user) => {
    state.adminInfo = user
  },
  SET_CURR_ROLE: (state, currRole) => {
    state.currRole = currRole
  },
  SET_REVIEW_ROLE: (state, reviewRole) => {
    state.reviewRole = reviewRole
  }
}
const actions = {
  // 登录
  Login ({ commit }, userInfo) {
    const username = userInfo.mobileOrEmail.trim()
    const password = userInfo.password
    const loginType = userInfo.loginType
    const code = userInfo.code
    return new Promise((resolve, reject) => {
      login(username, password, loginType, code).then(res => {
        const data = res.data
        commit('SET_TOKEN', 'Bearer ' + data.access_token)
        setToken('Bearer ' + data.access_token)
        setExpiresIn(data.expires_in)
        commit('SET_EXPIRES_IN', data.expires_in)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 专家登录
  ExpertLogin ({ commit }, userInfo) {
    const username = userInfo.mobileOrEmail.trim()
    const password = userInfo.password
    const loginType = userInfo.loginType
    const code = userInfo.code
    return new Promise((resolve, reject) => {
      login(username, password, loginType, code).then(res => {
        const data = res.data
        commit('SET_EXPERT_TOKEN', 'Bearer ' + data.access_token)
        setExpertToken('Bearer ' + data.access_token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 出版社登录
  PublishLogin ({ commit }, userInfo) {
    const username = userInfo.mobileOrEmail.trim()
    const password = userInfo.password
    const loginType = userInfo.loginType
    const code = userInfo.code
    return new Promise((resolve, reject) => {
      login(username, password, loginType, code).then(res => {
        const data = res.data
        commit('SET_PUBLISH_TOKEN', 'Bearer ' + data.access_token)
        setPublishToken('Bearer ' + data.access_token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 登录
  DataCenterLogin ({ commit }, userInfo) {
    const formData = new FormData()
    formData.append('from', 'web')
    formData.append('mobile', userInfo.mobile)
    formData.append('yzm', userInfo.yzm)
    return new Promise((resolve, reject) => {
      dataCenterLogin(formData).then(response => {
        if (+response.code === 200) {
          commit('SET_TOKEN', response.data.token)
          setAdminToken(response.data.token)
        }
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 后台token登录
  DataCenterTokenLogin ({ commit }, token) {
    return new Promise(resolve => {
      commit('SET_TOKEN', token)
      setAdminToken(token)
      resolve()
    })
  },
  // APP登录
  AppLogin({ commit }, token) {
    return new Promise(resolve => {
      commit('SET_TOKEN', token)
      setToken(token)
      resolve()
    })
  },
  // 家长端app传入token登录
  partentLoginApp({ commit }, token) {
    return new Promise(resolve => {
      commit('SET_PARTENT_TOKEN', 'Bearer ' + token)
      setPartentToken('Bearer ' + token)
      resolve()
    })
  },
  // 家长登录
  partentLogin ({ commit }, userInfo) {
    const username = userInfo.mobileOrEmail.trim()
    const password = userInfo.password
    const loginType = userInfo.loginType
    const code = userInfo.code
    return new Promise((resolve, reject) => {
      pLogin(username, password, loginType, code).then(res => {
        const data = res.data
        commit('SET_PARTENT_TOKEN', 'Bearer ' + data.access_token)
        setPartentToken('Bearer ' + data.access_token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 家长登录
  partentBindLogin ({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      bindUser(userInfo).then(res => {
        const data = res.data
        commit('SET_PARTENT_TOKEN', 'Bearer ' + data.access_token)
        setPartentToken('Bearer ' + data.access_token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  AuthorLogin({ commit }, userInfo) {
    const username = userInfo.mobileOrEmail.trim()
    const password = userInfo.password
    const loginType = userInfo.loginType
    const code = userInfo.code
    return new Promise((resolve, reject) => {
      login(username, password, loginType, code).then(res => {
        const data = res.data
        commit('SET_AUTHOR_TOKEN', 'Bearer ' + data.access_token)
        setAuthorToken('Bearer ' + data.access_token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 获取用户信息
  GetInfo ({ commit, state }, userType = 'ASSISTANT') {
    return new Promise((resolve, reject) => {
      const params = { 'userType': userType }
      getInfo(params).then(res => {
        const user = res.data
        if (!user) resolve(null)
        // const avatar = user.avatar === '' ? require('@/assets/images/profile.png') : user.avatar
        if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
          // commit('SET_ROLES', res.roles)
          // commit('SET_PERMISSIONS', res.permissions)
        } else {
          commit('SET_ROLES', ['ROLE_DEFAULT'])
        }
        commit('SET_NAME', user.displayName)
        commit('SET_AVATAR', user.avatar)
        commit('SET_SCHOOL', user.school)
        commit('SET_MOBILE', user.mobile)
        commit('SET_SHOWCOURSECENTER', user.showCourseCenter)
        commit('SET_ID', user.id)
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 获取统计平台用户信息
  GetDataCenterInfo ({ commit, state }) {
    return new Promise((resolve, reject) => {
      getDataCenterInfo().then(res => {
        const user = res.data
        // const avatar = user.avatar === '' ? require('@/assets/images/profile.png') : user.avatar
        if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
          // commit('SET_ROLES', res.roles)
          // commit('SET_PERMISSIONS', res.permissions)
        } else {
          commit('SET_ROLES', ['ROLE_DEFAULT'])
        }
        commit('SET_ADMIN_INFO', user)
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 获取用户相关联账号列表
  GetUserRelation ({ commit, state }) {
    return new Promise((resolve, reject) => {
      const params = { 'parentUserId': state.id, 'relationListType': 'WITH_MAIN' }
      // const params = { 'parentUserId': state.id }
      getUserRelationListWithSameMainUser(params).then(res => {
        const list = res.data
        if (list !== null) {
          const userList = []
          for (var item of list) {
            const json = { 'toUserId': item.toUserId, 'toUserDisplayName': item.toUser.displayName, 'isRelation': true }
            userList.push(json)
          }
          // if (userList.filter(item => item.toUserId === state.id).length === 0) {
          //   userList.unshift(
          //     { 'toUserId': state.id, 'toUserDisplayName': state.name, 'isRelation': false }
          //   )
          // }
          commit('SET_USERRELATIONS', userList)
        } else {
          const userList = []
          userList.unshift(
            { 'toUserId': state.id, 'toUserDisplayName': state.name, 'isRelation': false }
          )
          commit('SET_USERRELATIONS', userList)
        }
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 刷新token
  RefreshToken ({ commit, state }) {
    return new Promise((resolve, reject) => {
      refreshToken(state.token).then(res => {
        setExpiresIn(res.data)
        commit('SET_EXPIRES_IN', res.data)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 退出系统
  LogOut ({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        removeToken()
        removePartentToken()
        removeChildToken()
        removeChildId()
        removeChildName()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 专家退出
  ExpertLogout({ commit }) {
    return new Promise(resolve => {
      commit('SET_EXPERT_TOKEN', '')
      removeExpertToken()
      resolve()
    })
  },
  // 出版社退出
  PublishLogout({ commit }) {
    return new Promise((resolve) => {
      commit('SET_PUBLISH_TOKEN', '')
      removePublishToken()
      resolve()
    })
  },
  AuthorLogout({ commit }) {
    return new Promise((resolve) => {
      commit('SET_AUTHOR_TOKEN', '')
      removeAuthorToken()
      resolve()
    })
  },
  DataCenterLogout ({ commit, state }) {
    return new Promise((resolve, reject) => {
      dataCenterLogout().then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        removeToken()
        removePartentToken()
        removeChildToken()
        removeChildId()
        removeChildName()
        removeAdminToken()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 前端 登出
  FedLogOut ({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_PERMISSIONS', [])
      commit('SET_ID', '')
      removeToken()
      removePartentToken()
      removeChildToken()
      removeChildId()
      removeChildName()
      removeAdminToken()
      resolve()
    })
  },

  // 修改昵称
  EditName ({ commit, state }, NewName) {
    commit('SET_NAME', NewName)
  },
  SetCurrRole ({ commit, state }, currRole) {
    commit('SET_CURR_ROLE', currRole)
  },
  SetReviewRole ({ commit, state }, reviewRole) {
    commit('SET_REVIEW_ROLE', reviewRole)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
