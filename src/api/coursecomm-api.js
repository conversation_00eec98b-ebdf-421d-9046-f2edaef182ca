import request from '@/utils/request'

export function getCourseCommList (params) {
  return request({
    url: '/api/v1/coursecomm/coursecommList',
    method: 'get',
    params
  })
}

export function getCoursecommUnitList (params) {
  return request({
    url: '/api/v1/coursecomm/coursecommUnitList',
    method: 'get',
    params
  })
}

export function getCoursecommUnitDetail (params) {
  return request({
    url: '/api/v1/coursecomm/coursecommUnitDetail',
    method: 'get',
    params
  })
}

export function getCoursecommInfo (params) {
  return request({
    url: '/api/v1/coursecomm/coursecommInfo',
    method: 'get',
    params
  })
}
