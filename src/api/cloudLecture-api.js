import request from '@/utils/request'
export function getCloudLectureDetail (param) {
  return request({
    url: '/api/v1/digital/vt/getCloudLectureDetail',
    method: 'get',
    params: param
  })
}

export function getInvitationList (param) {
  return request({
    url: '/api/v1/goods/vt/getEmpowerExchangeCodeList',
    method: 'get',
    params: param
  })
}

export function getImage (param) {
  return request({
    url: '/api/v2/comm/vt/aigcImg',
    method: 'get',
    params: param
  })
}

export function updateEmpowerExchangeCode (param) {
  return request({
    url: '/api/v1/goods/updateEmpowerExchangeCode',
    method: 'post',
    data: param
  })
}

export function usedCloudLectureTemplate (param) {
  return request({
    url: '/api/v1/digital/usedCloudLectureTemplate',
    method: 'get',
    params: param
  })
}

// 设为模板
export function addCloudLectureTemplate (param) {
  return request({
    url: '/api/v1/digital/addCloudLectureTemplate',
    method: 'post',
    params: param
  })
}

// 获取模板列表
export function getLectureTemplate (param) {
  return request({
    url: '/api/v1/digital/getDigitalCatalogueList',
    method: 'get',
    params: param
  })
}

// 生成数字教材
export function generateDigitalBook (param) {
  return request({
    url: '/api/v1/digital/generateDigitalBook',
    method: 'post',
    params: param
  })
}
