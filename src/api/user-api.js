import request from '@/utils/request'
import { getToken, getChildToken } from '@/utils/auth'

export function getInfo (params) {
  return request({
    url: '/api/v2/user/getUserInfo',
    method: 'get',
    params
  })
}

export function getInfoIfream (params, token) {
  return request({
    url: '/api/v2/user/getUserInfo',
    method: 'get',
    headers: {
      'authorization': token
    },
    params
  })
}

//  查询老师详情内容
export function getVtUserInfo (params) {
  return request({
    url: '/api/v2/user/vt/getUserInfo',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function getFileUploadAuthor (params) {
  return request({
    url: '/api/v2/comm/vt/getFileUploadAuthor',
    method: 'get',
    params
  })
}

export function getUserExtra (params) {
  return request({
    url: '/api/v2/user/vt/getUserExtra',
    method: 'get',
    params
  })
}

export function updatePassword (params) {
  return request({
    url: '/api/v2/user/updatePassword',
    method: 'post',
    params
  })
}

export function forgetPassword (params) {
  return request({
    url: '/api/v2/user/vt/updatePassword',
    method: 'post',
    params
  })
}

export function logout (data) {
  return request({
    url: '/api/v1/classRoom/vt/reportClassRoomAction',
    method: 'post',
    params: data
  })
}

export function refreshToken (data) {
  return request({
    url: '/api/v1/classRoom/vt/reportClassRoomAction',
    method: 'post',
    params: data
  })
}

export function verifyCode (data) {
  return request({
    url: '/api/v1/students/verifyCode',
    method: 'post',
    data
  })
}

export function getVerifyCodeImage (data) {
  return request({
    url: '/api/v1/systemManager/getVerifyCodeImage',
    method: 'post',
    params: data
  })
}

export function verifyCodeForWeb (data) {
  return request({
    url: '/api/v1/students/verifyCodeForWeb',
    method: 'post',
    params: data
  })
}

export function slideToGetSmsCode (data) {
  var formData = new FormData()

  formData.append('smsCodeType', data.smsCodeType)
  formData.append('mobile', data.mobile)
  formData.append('sig', data.sig)
  formData.append('sessionId', data.sessionId)
  formData.append('token', data.token)
  formData.append('scene', data.scene)
  return request({
    url: '/api/v1/systemManager/vt/slideToGetSmsCode',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: formData
  })
}

export function register (data) {
  return request({
    url: '/api/v2/user/vt/register',
    method: 'post',
    params: data
  })
}

export function login (mobileOrEamil, password, loginType, code) {
  let param = {}
  if (loginType === 'PASSWORD') {
    param = {
      mobileOrEamil: mobileOrEamil,
      password: password,
      userType: 'STUDENT',
      loginType: loginType
    }
  } else {
    param = {
      mobileOrEamil: mobileOrEamil,
      userType: 'STUDENT',
      loginType: loginType,
      authCode: code
    }
  }
  return request({
    url: '/api/v2/user/vt/login',
    method: 'get',
    params: param
  })
}

export function updateUserInfo (params) {
  return request({
    url: '/api/v2/user/updateUserInfo',
    method: 'post',
    params: params
  })
}

export function updateMobile (params) {
  return request({
    url: '/api/v2/user/updateMobileOrEmail',
    method: 'post',
    params: params
  })
}

// 获取有关联关系的所有账号，比如助教获取下面所有的子账号，而子账号也可以获取同一个助教下面的所有账号
export function getUserRelationListWithSameMainUser (params) {
  return request({
    url: '/api/v2/user/getUserRelationListWithSameMainUser',
    method: 'get',
    params: params
  })
}

// 账号切换,获取新账号的授权token
export function switchAccount (params) {
  return request({
    url: '/api/v2/user/switchAccount',
    method: 'post',
    params
  })
}

// 助教修改班级名
export function changeChildName (params) {
  return request({
    url: '/api/v2/user/changeChildName',
    method: 'post',
    params
  })
}

// 获取助教带班信息
export function getAssistantInfo (params) {
  return request({
    url: '/api/v2/user/getAssistantInfo',
    method: 'get',
    params
  })
}

// 关注教师
export function followTeacher (data) {
  return request({
    url: '/api/v1/students/followTeacher',
    method: 'post',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    data: data
  })
}

// 取消关注教师
export function cancelFollowedTeacher (data) {
  return request({
    url: '/api/v1/students/cancelFollowedTeacher',
    method: 'post',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    data: data
  })
}
export function castingShareFile (data, params) {
  return request({
    url: '/api/v2/comm/vt/castingShareFile',
    method: 'post',
    data,
    params
  })
}
