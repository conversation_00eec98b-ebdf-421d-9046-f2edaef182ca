import request from '@/utils/request'
import { getToken } from '@/utils/auth'

export function getCourseLessonPlanList (params) {
  return request({
    url: '/api/v2/course/getSchoolCourseList',
    method: 'get',
    headers: {
      'authorization': getToken()
    },
    params
  })
}

export function getUserClassList (params, token) {
  return request({
    url: '/api/v2/user/getUserClassList',
    method: 'get',
    headers: {
      'authorization': token || getToken()
    },
    params
  })
}

export function assistantAddClass (params, token) {
  return request({
    url: '/api/v2/user/assistantAddClass',
    method: 'post',
    headers: {
      'authorization': token || getToken()
    },
    params
  })
}

export function assistantBindSchool (params, token) {
  return request({
    url: '/api/v2/user/assistantBindSchool',
    method: 'post',
    headers: {
      'authorization': token || getToken()
    },
    params
  })
}

export function getStudentCourseList (params, token) {
  return request({
    url: '/api/v2/lesson/getStudentCourseList',
    method: 'get',
    headers: {
      'authorization': 'Bearer ' + token
    },
    params
  })
}

export function getUserInfo (params, token) {
  return request({
    url: '/api/v2/user/getUserInfo',
    method: 'get',
    headers: {
      'authorization': 'Bearer ' + token
    },
    params
  })
}

export function changeChildName (params, token) {
  return request({
    url: '/api/v2/user/changeChildName',
    method: 'post',
    headers: {
      'authorization': token || getToken()
    },
    params
  })
}

export function getSchoolCourseList (params, token) {
  return request({
    url: '/api/v2/course/getSchoolCourseList',
    method: 'get',
    headers: {
      'authorization': token || getToken()
    },
    params
  })
}

export function addCourseToClass (params, token) {
  return request({
    url: '/api/v2/course/addCourseToClass',
    method: 'post',
    headers: {
      'authorization': token || getToken()
    },
    params
  })
}

export function updateStudentCoursePlan (params) {
  return request({
    url: '/api/v2/course/updateStudentCoursePlan',
    method: 'post',
    headers: {
      'authorization': getToken()
    },
    params
  })
}

export function removeStudentCourse (params) {
  return request({
    url: '/api/v2/course/removeStudentCourse',
    method: 'post',
    headers: {
      'authorization': getToken()
    },
    params
  })
}

export function assistantDeleteClass (params, token) {
  return request({
    url: '/api/v2/user/assistantDeleteClass',
    method: 'post',
    headers: {
      'authorization': token || getToken()
    },
    params
  })
}

export function updateClassIntro (params, token) {
  return request({
    url: '/api/v2/user/updateClassIntro',
    method: 'post',
    headers: {
      'authorization': token || getToken()
    },
    params
  })
}

export function getSubjectConfig (params) {
  return request({
    url: '/api/v2/comm/vt/getSubjectConfig',
    method: 'get',
    params
  })
}

export function userClassBindSchool (params) {
  return request({
    url: '/api/v2/user/userClassBindSchool',
    method: 'post',
    params
  })
}
