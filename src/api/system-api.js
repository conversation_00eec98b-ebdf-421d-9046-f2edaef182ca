import request from '@/utils/request'

export function getNoticeList (params) {
  return request({
    url: '/api/v2/notice/vt/getNoticeList',
    method: 'get',
    params
  })
}

export function getNoticeInfo (params) {
  return request({
    url: '/api/v2/notice/vt/getNoticeInfo',
    method: 'get',
    params
  })
}

export function getWelcomeMessage (params) {
  return request({
    url: '/api/v2/aigc/getWelcomeMessage',
    method: 'get',
    params
  })
}

export function getStaticDataByBook (params) {
  return request({
    url: '/api/v1/digital/vt/getStaticDataByBook',
    method: 'get',
    params
  })
}
