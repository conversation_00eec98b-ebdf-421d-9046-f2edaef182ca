import request from '@/utils/request'
import { getToken, getChildToken } from '@/utils/auth'

export function getGenerateToken (params) {
  return request({
    url: '/api/v1/classRoom/vt/generateToken',
    method: 'get',
    params
  })
}

export function reportClassRoomAction (data) {
  return request({
    url: '/api/v1/classRoom/vt/reportClassRoomAction',
    method: 'post',
    params: data
  })
}

export function getDocumentInfo (documentId) {
  return request({
    url: '/api/v1/classRoom/vt/getDocumentScene',
    method: 'get',
    params: { documentId }
  })
}

export function logEquipmentStatu (params) {
  return request({
    url: '/api/v2/comm/logEquipmentStatus',
    method: 'post',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}
