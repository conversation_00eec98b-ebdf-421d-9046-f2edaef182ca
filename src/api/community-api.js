import request from '@/utils/request'
export function getGoodsComm (params) {
  return request({
    url: '/api/v2/goodscomm/vt/getGoodsComm',
    method: 'get',
    params
  })
}
export function getWXConfig(params) {
  return request({
    url: '/api/v2/comm/vt/getWXConfig',
    method: 'get',
    params
  })
}
export function Login(params) {
  return request({
    url: '/api/v2/user/vt/login',
    method: 'get',
    params: params
  })
}
export function parentBindChildNoSchool(params, headers = {}) {
  return request({
    url: '/api/v2/user/parentBindChildNoSchool',
    method: 'post',
    params: params,
    headers
  })
}
export function bindUser (params, headers = {}) {
  return request({
    url: '/api/v2/user/vt/bindUser',
    method: 'get',
    headers,
    params
  })
}
export function createOrders (params, headers = {}) {
  return request({
    url: '/api/v2/goodscomm/createOrdersComm',
    method: 'get',
    params,
    headers
  })
}
export function getUserInfo (params, headers = {}) {
  return request({
    url: '/api/v2/user/getUserInfo',
    method: 'get',
    params,
    headers
  })
}
export function prepay (params, headers = {}) {
  return request({
    url: '/api/v2/goodscomm/prepay',
    method: 'post',
    params,
    headers
  })
}
export function getOrders (params) {
  return request({
    url: '/api/v2/goodscomm/getOrders',
    method: 'get',
    params
  })
}
