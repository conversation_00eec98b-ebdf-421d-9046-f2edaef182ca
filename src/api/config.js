import request from '@/utils/request'

export const ERR_OK = '200'
export const VIDEO_URL = 'https://sv.static.bingotalk.cn/video/'
export const TIME_ZONE = 'HST,EST,ROC,GMT+0,GMT-0,MST'.split(',')
export const isElectronWeb = navigator.userAgent.indexOf('Electron') !== -1

export function getLastAppVersion (params) {
  return request({
    url: '/api/v2/comm/vt/getLastAppVersion',
    method: 'get',
    params
  })
}

export function getConfig (params) {
  return request({
    url: '/api/v1/dictionary/getConfig',
    method: 'get',
    params
  })
}

export function getConfigObj (params) {
  return request({
    url: '/api/v1/dictionary/dictionary',
    method: 'get',
    params
  })
}
