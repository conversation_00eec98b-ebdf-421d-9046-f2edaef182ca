import request from '@/utils/request'

export function getActivityList (params) {
  return request({
    url: '/api/v2/activity/vt/getActivityList',
    method: 'get',
    params
  })
}

export function getActivityInfo (params) {
  return request({
    url: '/api/v2/activity/vt/getActivityInfo',
    method: 'get',
    params
  })
}

export function submitActivityWorks (data) {
  return request({
    url: '/api/v2/activity/submitActivityWorks',
    method: 'post',
    data
  })
}

export function getActivitySchoolList (params) {
  return request({
    url: '/api/v2/activity/vt/getActivitySchoolList',
    method: 'get',
    params
  })
}

export function getUserActivityWorks (params, token) {
  return request({
    url: '/api/v2/activity/vt/getUserActivityWorks',
    method: 'get',
    headers: {
      tempToken: token
    },
    params
  })
}

export function cancelActivityWorks (params, token) {
  return request({
    url: '/api/v2/activity/vt/cancelActivityWorks',
    method: 'post',
    headers: {
      tempToken: token
    },
    params
  })
}

export function getActivityWorkReviewList (params) {
  return request({
    url: '/api/v2/activity/vt/getActivityWorkReviewList',
    method: 'get',
    params
  })
}

export function markActivityWorks (params) {
  return request({
    url: '/api/v2/activity/vt/markActivityWorks',
    method: 'post',
    params
  })
}

export function getBannerList (params) {
  return request({
    url: '/api/v2/comm/vt/getBannerList',
    method: 'get',
    params
  })
}

export function getActivityWorkList (params) {
  return request({
    url: '/api/v2/activity/vt/getActivityWorkList',
    method: 'get',
    params
  })
}
export function registeredActivityList (params) {
  return request({
    url: '/api/v2/activity/registeredActivityList',
    method: 'get',
    params
  })
}
export function getUserActivityWorkRank (params) {
  return request({
    url: '/api/v2/activity/getUserActivityWorkRank',
    method: 'get',
    params
  })
}
export function getActivityWorkInfo (params) {
  return request({
    url: '/api/v2/activity/vt/getActivityWorkInfo',
    method: 'get',
    params
  })
}
export function praiseActivityWorks (params) {
  return request({
    url: '/api/v2/activity/praiseActivityWorks',
    method: 'post',
    params
  })
}

export function getInformationList (params) {
  return request({
    url: '/api/v2/comm/vt/getInformationList',
    method: 'get',
    params
  })
}
export function registerActivity (params) {
  return request({
    url: '/api/v2/activity/registerActivity',
    method: 'post',
    params
  })
}

export function getWxGzhShareSignature (params) {
  return request({
    url: '/api/v2/comm/vt/getWxGzhShareSignature',
    method: 'get',
    params
  })
}
export function getRecommend (params) {
  return request({
    url: '/api/v2/comm/vt/getRecommend',
    method: 'get',
    params
  })
}

