import request from '@/utils/request'
export function login (param) {
  return request({
    url: '/api/v2/user/vt/login',
    method: 'get',
    params: param
  })
}
export function getPublisher (params) {
  return request({
    url: '/api/v1/digital/review/vt/getPublisher',
    method: 'get',
    params
  })
}
export function getDigitalBookReviewList (params) {
  return request({
    url: '/api/v1/digital/review/getDigitalBookReviewList',
    method: 'get',
    params
  })
}
export function submitDigitalBug (data) {
  return request({
    url: '/api/v1/digital/review/submitDigitalBug',
    method: 'post',
    data
  })
}
export function getDigitalBugList (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/getDigitalBugList',
    method: 'get',
    params,
    headers
  })
}
export function getDigitalBookReview (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/getDigitalBookReview',
    method: 'get',
    params,
    headers
  })
}
export function updateDigitalBug (data, headers = {}) {
  return request({
    url: '/api/v1/digital/review/updateDigitalBug',
    method: 'post',
    data,
    headers
  })
}

export function grantPublish (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/grantPublish',
    method: 'post',
    params,
    headers
  })
}
export function changeDigitalBugStatus (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/changeDigitalBugStatus',
    method: 'post',
    params,
    headers
  })
}
export function changeDigitalBookReviewStatus (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/changeDigitalBookReviewStatus',
    method: 'post',
    params,
    headers
  })
}

export function submitDigitalBugComment (data, headers = {}) {
  return request({
    url: '/api/v1/digital/review/submitDigitalBugComment',
    method: 'post',
    data,
    headers
  })
}
export function getDigitalBugCommentList (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/getDigitalBugCommentList',
    method: 'get',
    params,
    headers
  })
}
export function getDigitalBookDataAuthor (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/getDigitalBookDataAuthor',
    method: 'get',
    params,
    headers
  })
}
export function digitalBook (params, headers = {}) {
  return request({
    url: '/api/v1/digital/digitalBook',
    method: 'post',
    data: params,
    params: {
      apiType: params.apiType,
    },
    headers
  })
}

export function submitDigitalBookReview (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/submitDigitalBookReview',
    method: 'post',
    params,
    headers
  })
}

export function getDigitalBookReviewListByBook (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/vt/getDigitalBookReviewListByBook',
    method: 'get',
    params,
    headers
  })
}
export function acceptDigitalAuthorInvite (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/acceptDigitalAuthorInvite',
    method: 'get',
    params,
    headers
  })
}
export function getDigitalBookReviewUserByBook (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/getDigitalBookReviewUserByBook',
    method: 'get',
    params,
    headers
  })
}
