import request from '@/utils/request'
import { getAdminToken } from '@/utils/auth'

export function dataCenterLogin (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/user/login',
    method: 'post',
    data: formData,
    urlType: 'admin'
  })
}
export function getYzm (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/user/getYzm',
    method: 'post',
    data: formData,
    urlType: 'admin'
  })
}

export function dataCenterLogout (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/user/logout',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getDataCenterInfo (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/user/info',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getAreaByPid (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/getAreaByPid',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getPublisher (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/getPublisher',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getIssuer (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/getIssuer',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getSchool (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/getSchool',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function baseData (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/baseData',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function useData (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/useData',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function chartBar (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/chartBar',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function chartLine (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/chartLine',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getDistrictList (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/getDistrictList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getSchoolList (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/getSchoolList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getPackageList (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/index/getPackageList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function packageBaseData (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/package/baseData',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function packageUseData (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/package/useData',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}

export function getAiCourseList (formData) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/aicenter/package/getAiCourseList',
    method: 'post',
    headers: {
      'Cms-Token': getAdminToken()
    },
    data: formData,
    urlType: 'admin'
  })
}
