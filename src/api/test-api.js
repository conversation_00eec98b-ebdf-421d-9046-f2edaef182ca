import request from '@/utils/request'

export function createTest (params, headers) {
  return request({
    url: '/api/v2/question/testPaper',
    method: 'post',
    params,
    headers
  })
}
export function getTestPaperQuestionList (params, headers) {
  return request({
    url: '/api/v2/question/vt/getTestPaperQuestionList',
    method: 'post',
    params,
    headers
  })
}
export function question (params, data, headers) {
  return request({
    url: '/api/v2/question/question',
    method: 'post',
    params,
    data,
    headers
  })
}
export function dragQuestion (params, headers) {
  return request({
    url: '/api/v2/question/dragQuestion',
    method: 'post',
    params,
    headers
  })
}
export function answerQuestion (params, headers) {
  return request({
    url: '/api/v2/question/answerQuestion',
    method: 'post',
    params,
    headers
  })
}
export function getUserAnswer (params, headers) {
  return request({
    url: '/api/v2/question/vt/getUserAnswer',
    method: 'post',
    params,
    headers
  })
}
export function redoTestPaper (params, headers) {
  return request({
    url: '/api/v2/question/redoTestPaper',
    method: 'post',
    params,
    headers
  })
}
export function submiteTestpaper (params, headers) {
  return request({
    url: '/api/v2/question/submiteTestpaper',
    method: 'post',
    params,
    headers
  })
}
export function addQuestionToTestpaper (params, headers) {
  return request({
    url: '/api/v2/question/addQuestionToTestpaper',
    method: 'post',
    params,
    headers
  })
}
export function generateQuestionByAigc (params, headers) {
  return request({
    url: '/api/v2/question/vt/generateQuestionByAigc',
    method: 'get',
    params,
    headers
  })
}
export function getUserBookTestpaperData (params, headers) {
  return request({
    url: '/api/v2/question/getUserBookTestpaperData',
    method: 'get',
    params,
    headers
  })
}

export function getCacheS () {
  return request({
    url: '/test/getCacheS?cacheName=jzjxuser',
    method: 'get'
  })
}
