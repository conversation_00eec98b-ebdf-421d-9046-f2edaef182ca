import request from '@/utils/request'
import { getToken, getChildToken } from '@/utils/auth'

export function getStuCourseList (params, needChildToken = true) {
  return request({
    url: '/api/v2/lesson/getStudentCourseList',
    method: 'get',
    headers: {
      'authorization': needChildToken ? getChildToken() || getToken() : getToken()
    },
    params
  })
}

export function getLessonListAllType (params) {
  return request({
    url: '/api/v2/lesson/getLessonListAllType',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function getLessonInfo (params) {
  return request({
    url: '/api/v2/lesson/getLessonInfo',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function applyLesson (params) {
  return request({
    url: '/api/v2/lesson/userApplyLesson',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function getLessonPrepare (params) {
  return request({
    url: '/api/v2/lesson/vt/getLessonPrepare',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

// 获取用户补课课程列表
export function getUserPlanLessonList () {
  return request({
    url: '/api/v2/lesson/getUserPlanLessonList',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    }
  })
}

export function getLessonScheduleList (params) {
  return request({
    url: '/api/v2/lesson/getLessonScheduleList',
    method: 'get',
    params
  })
}
